package com.simnectz.soiplus.core.system.validation;

import com.simnectz.soiplus.core.system.annotation.validation.ValidationEnum;
import com.simnectz.soiplus.core.system.enums.EnumValidate;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

/**
 * Enum type validator
 */
public class EnumValidation implements ConstraintValidator<ValidationEnum, String> {

    private Class<? extends EnumValidate> enumClass;

    @Override
    public void initialize(ValidationEnum constraintAnnotation) {
        enumClass = constraintAnnotation.enumClass();
    }

    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        if (value == null) {
            return true;
        }

        EnumValidate[] enumValues = enumClass.getEnumConstants();
        for (EnumValidate enumValue : enumValues) {
            if (enumValue.getValue().equals(value)) {
                return true;
            }
        }

        return false;
    }

}
