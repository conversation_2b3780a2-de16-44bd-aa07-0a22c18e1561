package com.simnectz.soiplus.core.tc.resource;

import com.simnectz.soiplus.core.system.domain.vo.Response;
import com.simnectz.soiplus.core.tc.domain.dto.UpdateTcDto;
import com.simnectz.soiplus.core.tc.domain.vo.TcVo;
import com.simnectz.soiplus.core.tc.service.TcService;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("v1/system/tc")
@RequiredArgsConstructor
public class TcResource {

    private final TcService tcService;

    @GetMapping("get")
    public ResponseEntity<Response<TcVo>> getTc() {
        return ResponseEntity.ok(tcService.getTc());
    }

    @PutMapping("update")
    public ResponseEntity<Response<?>> updateTc(@RequestBody @Validated UpdateTcDto updateTcDto) {
        return ResponseEntity.ok(tcService.updateTc(updateTcDto));
    }

}
