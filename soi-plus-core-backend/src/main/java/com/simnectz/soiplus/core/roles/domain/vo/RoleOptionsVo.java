package com.simnectz.soiplus.core.roles.domain.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * Role option information response entity
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class RoleOptionsVo implements Serializable {

    private static final long serialVersionUID = 4927317625056482928L;

    private String id;
    private String roleName;

}
