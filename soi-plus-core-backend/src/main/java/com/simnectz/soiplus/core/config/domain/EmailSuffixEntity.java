package com.simnectz.soiplus.core.config.domain;

import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import lombok.*;

import java.io.Serializable;

@Data
@With
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
@Table(value = "sysconf_email_suffix")
public class EmailSuffixEntity implements Serializable {

    private static final long serialVersionUID = -210938750909829102L;

    @Id(keyType = KeyType.Auto)
    private Integer id;

    private String suffix;
}
