package com.simnectz.soiplus.core.system.domain.entity;

import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.core.keygen.KeyGenerators;
import lombok.Data;

import java.io.Serializable;

/**
 * Common audit fields of the data table
 * Required field
 *  1. id: primary_key(flexId)
 * Audit fields
 *  1. createdTime: Record creation time
 *  2. lastModifiedTime: Last update time of the record
 *  3. createdBy: Record creator id
 *  4. lastModifiedBy: Record the id of the last modifier
 */
@Data
public class BasicEntity implements Serializable {

    private static final long serialVersionUID = 4249725425602162688L;

    @Id(keyType = KeyType.Generator, value = KeyGenerators.uuid)
    private String id;

    /**
     * 创建时间
     */
    private String createTime;
    /**
     * 最后的更新时间
     */
    private String lastUpdateTime;
    /**
     * 创建人
     */
    private String creator;
    /**
     * 修后修改的人
     */
    private String lastUpdateCreator;

}
