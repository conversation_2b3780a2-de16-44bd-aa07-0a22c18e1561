package com.simnectz.soiplus.core.system.config.security;

import cn.hutool.extra.servlet.ServletUtil;
import cn.hutool.http.ContentType;
import cn.hutool.json.JSONConfig;
import cn.hutool.json.JSONUtil;
import com.simnectz.soiplus.core.system.constant.ResponseConstant;
import com.simnectz.soiplus.core.system.domain.vo.Response;
import com.simnectz.soiplus.core.system.utils.MessageUtils;
import org.springframework.security.access.AccessDeniedException;
import org.springframework.security.web.access.AccessDeniedHandler;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.nio.charset.StandardCharsets;

/**
 * Access denied handler
 */
@Component
public class RestAccessDeniedHandler implements AccessDeniedHandler {

    @Override
    public void handle(HttpServletRequest request, HttpServletResponse response, AccessDeniedException accessDeniedException) {
        String result = JSONUtil.toJsonStr(
                Response.failed(
                        ResponseConstant.Code.FORBIDDEN,
                        MessageUtils.message(ResponseConstant.Message.FORBIDDEN)
                ),
                JSONConfig.create().setIgnoreNullValue(false)
        );
        ServletUtil.write(response, result, ContentType.build(ContentType.JSON, StandardCharsets.UTF_8));
    }

}
