package com.simnectz.soiplus.core.system.utils;

import cn.hutool.core.util.ObjectUtil;
import com.simnectz.authorize.system.domain.bo.User;
import com.simnectz.soiplus.core.system.constant.SystemConstant;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;

/**
 * Security Utils
 */
public class SecurityUtils {

    /**
     * Obtain the current login user id
     * @return User id
     */
    public static String getUserId() {
        return isAnonymousUser() ? null : getLoginUser().getId();
    }

    /**
     * Obtain the current login user information
     * @return User Information
     */
    public static User getLoginUser() {
        return (User) getAuthentication().getPrincipal();
    }

    /**
     * Determine if it is an anonymous user
     */
    public static boolean isAnonymousUser() {
        Authentication authentication = getAuthentication();

        if (ObjectUtil.isNull(authentication)) {
            return true;
        }

        return SystemConstant.ANONYMOUS_USER.equals(authentication.getPrincipal());
    }

    /**
     * Obtain authentication information
     * @return authentication information
     */
    public static Authentication getAuthentication() {
        return SecurityContextHolder.getContext().getAuthentication();
    }

}
