package com.simnectz.soiplus.core.system.validation;

import cn.hutool.core.util.StrUtil;
import com.simnectz.soiplus.core.system.annotation.validation.ValidationCardId;
import com.simnectz.soiplus.core.system.utils.ValidatorUtils;
import com.simnectz.soiplus.core.users.domian.dto.Card;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;


/**
 * Card ID validator
 */
public class CardIdValidation implements ConstraintValidator<ValidationCardId, Card> {

    @Override
    public void initialize(ValidationCardId constraintAnnotation) {
        ConstraintValidator.super.initialize(constraintAnnotation);
    }

    @Override
    public boolean isValid(Card userInfo, ConstraintValidatorContext constraintValidatorContext) {
        String cardType = userInfo.getCardType();
        String cardId = userInfo.getCardId();

        if (StrUtil.isBlank(cardType) || StrUtil.isBlank(cardId)) {
            return true;
        }

        return ValidatorUtils.validateCardId(cardType, cardId);
    }

}
