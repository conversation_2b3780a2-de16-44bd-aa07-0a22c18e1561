package com.simnectz.soiplus.core.tc.domain.entity;

import com.mybatisflex.annotation.Table;
import com.simnectz.soiplus.core.system.domain.entity.BasicEntity;
import lombok.*;

import java.io.Serializable;

@Data
@With
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(value = "system_tc")
@EqualsAndHashCode(callSuper = true)
public class TcEntity extends BasicEntity implements Serializable {

    private static final long serialVersionUID = -4575018507193557227L;

    private String content;

}
