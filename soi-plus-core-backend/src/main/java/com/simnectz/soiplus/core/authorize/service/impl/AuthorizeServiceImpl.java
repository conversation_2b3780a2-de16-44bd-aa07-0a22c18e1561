package com.simnectz.soiplus.core.authorize.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.simnectz.authorize.system.domain.bo.User;
import com.simnectz.soiplus.core.authorize.domain.bo.ResetPasswordPayloadBo;
import com.simnectz.soiplus.core.authorize.domain.dto.*;
import com.simnectz.soiplus.core.authorize.domain.vo.TokenVo;
import com.simnectz.soiplus.core.authorize.service.AuthorizeService;
import com.simnectz.soiplus.core.system.async.AsyncFactory;
import com.simnectz.soiplus.core.system.async.AsyncManager;
import com.simnectz.soiplus.core.system.config.SystemProperties;
import com.simnectz.soiplus.core.system.constant.ResponseConstant;
import com.simnectz.soiplus.core.system.constant.SystemConstant;
import com.simnectz.soiplus.core.system.domain.bo.TokenPayload;
import com.simnectz.soiplus.core.system.domain.vo.Response;
import com.simnectz.soiplus.core.system.exception.SystemException;
import com.simnectz.soiplus.core.system.utils.*;
import com.simnectz.soiplus.core.users.domian.dto.CreateUserDto;
import com.simnectz.soiplus.core.users.domian.entity.UserEntity;
import com.simnectz.soiplus.core.users.service.UserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.authentication.*;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import static com.simnectz.soiplus.core.users.domian.entity.table.UserTableDef.user;


/**
 * Authorize service implements
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class AuthorizeServiceImpl implements AuthorizeService {

    private final AuthenticationManager authenticationManager;
    private final UserService userService;
    private final SystemProperties systemProperties;
    private final RedisTemplate<String, User> redisTemplate;
    private final RedisTemplate<String, String> captchaRedisTemplate;
    private final PasswordEncoder passwordEncoder;

    /**
     * User register
     *
     * @param userRegisterDto User register information
     */
    @Override
    public Response<?> register(UserRegisterDto userRegisterDto) {
        CreateUserDto createUserDto = BeanUtil.copyProperties(userRegisterDto, CreateUserDto.class);

        // Create user
        userService.createUser(createUserDto);

        String email = createUserDto.getEmail();
        String username = createUserDto.getUsername();

        sendActiveEmail(username, email);

        return Response.success();
    }

    /**
     * User login
     *
     * @param userLoginDto User information
     */
    @Override
    public Response<?> login(UserLoginDto userLoginDto) {
        // Validate captcha
        this.validateCaptcha(userLoginDto.getCaptchaKey(), userLoginDto.getCaptcha(), userLoginDto.getEmail());

        UsernamePasswordAuthenticationToken authRequest =
                new UsernamePasswordAuthenticationToken(userLoginDto.getEmail(), userLoginDto.getPassword());
        Authentication authenticate;
        try {
            // Perform authentication logic
            authenticate = authenticationManager.authenticate(authRequest);
        } catch (BadCredentialsException exception) {
            SecurityContextHolder.clearContext();

            // login record
            AsyncManager.getInstance().executor(AsyncFactory.loginRecord(userLoginDto.getEmail(), SystemConstant.LOGIN_STATUS_FAILED, "username or password error"));

            throw new SystemException(ResponseConstant.Code.BAD_REQUEST, exception.getMessage());
        } catch (

                DisabledException |
                LockedException |
                AccountExpiredException |
                CredentialsExpiredException exception
        ) {
            SecurityContextHolder.clearContext();

            throw new SystemException(ResponseConstant.Code.BAD_REQUEST, exception.getMessage());
        }

        User loginUser = (User) authenticate.getPrincipal();

        if (Integer.valueOf(1).equals(loginUser.getBlackList())) {
            AsyncManager.getInstance().executor(AsyncFactory.loginRecord(userLoginDto.getEmail(), SystemConstant.LOGIN_STATUS_FAILED, "user has been blacklisted"));

            SecurityContextHolder.clearContext();

            throw new SystemException(
                    ResponseConstant.Code.USER_HAS_BEEN_BLACKLISTED,
                    MessageUtils.message(ResponseConstant.Message.USER_HAS_BEEN_BLACKLISTED)
            );
        }

        if (Integer.valueOf(0).equals(loginUser.getStatus())) {
            AsyncManager.getInstance().executor(AsyncFactory.loginRecord(userLoginDto.getEmail(), SystemConstant.LOGIN_STATUS_FAILED, "user not activated"));

            SecurityContextHolder.clearContext();

            throw new SystemException(
                    ResponseConstant.Code.USER_NOT_ACTIVATED,
                    MessageUtils.message(ResponseConstant.Message.USER_NOT_ACTIVATED)
            );
        }

        String userId = loginUser.getId();

        // Save user information to redis
        redisTemplate.opsForValue().set(
                // key: user id
                SystemConstant.REDIS_PREFIX_LOGIN + userId,
                // user information
                loginUser,
                // expire time: refresh token expire time
                systemProperties.getAuthorize().getRefreshTokenExpireTime(),
                // unit: minutes
                TimeUnit.MINUTES
        );

        // Create token
        TokenVo tokenInfo = new TokenVo();
        TokenPayload payload = new TokenPayload(userId);

        tokenInfo.setAccessToken(JwtUtils.createAccessToken(payload));
        tokenInfo.setRefreshToken(JwtUtils.createRefreshToken(payload));
        tokenInfo.setAutoLoginKey(EncryptionUtils.encode(userId));

        Integer loginCount = userService.queryChain()
                .select(user.login_count)
                .where(user.id.eq(userId))
                .objAs(Integer.class);

        // Update login time and count
        userService.updateChain()
                .set(user.login_count, loginCount == null ? 1 : loginCount + 1)
                .set(user.login_time, DateUtil.date())
                .where(user.id.eq(userId))
                .update();

        AsyncManager.getInstance().executor(AsyncFactory.loginRecord(userLoginDto.getEmail(), SystemConstant.LOGIN_STATUS_SUCCESS, "login success"));

        return Response.success(tokenInfo);
    }

    @Override
    public void activeUser(String email, String code, HttpServletResponse response) {
        String validateCode = EncryptionUtils.encode(email);

        if (StrUtil.isBlank(email) || StrUtil.isBlank(code) || !validateCode.equals(code)) {
            throw new SystemException(
                    ResponseConstant.Code.BAD_REQUEST,
                    MessageUtils.message(ResponseConstant.Message.USER_ACTIVATION_FAILED)
            );
        }

        String userId = userService.queryChain()
                .select(user.id)
                .where(user.email.eq(email))
                .objAs(String.class);

        if (StrUtil.isBlank(userId)) {
            throw new SystemException(
                    ResponseConstant.Code.NOT_FOUND,
                    MessageUtils.message(ResponseConstant.Message.USER_NOT_FOUND)
            );
        }

        // Update user active status
        userService.updateChain()
                .set(user.status, 1)
                .where(user.id.eq(userId))
                .update();

        try {
            response.sendRedirect(systemProperties.getFrontendUrl() + "/authorize/login?email=" + email);
        } catch (Exception exception) {
            log.error("active user failed: ", exception);
            throw new SystemException(
                    ResponseConstant.Code.BAD_REQUEST,
                    MessageUtils.message(ResponseConstant.Message.USER_ACTIVATION_FAILED)
            );
        }
    }

    /**
     * Resend active email
     */
    @Override
    public Response<?> resendActiveEmail(String email) {
        UserEntity userEntity = userService.queryChain()
                .select(user.black_list, user.status, user.username)
                .where(user.email.eq(email))
                .one();

        if (ObjUtil.isNull(userEntity)) {
            throw new SystemException(
                    ResponseConstant.Code.NOT_FOUND,
                    MessageUtils.message(ResponseConstant.Message.USER_NOT_FOUND)
            );
        }

        if (Integer.valueOf(1).equals(userEntity.getStatus())) {
            throw new SystemException(
                    ResponseConstant.Code.USER_ACTIVATED,
                    MessageUtils.message(ResponseConstant.Message.USER_ACTIVATED)
            );
        }

        if (Integer.valueOf(1).equals(userEntity.getBlackList())) {
            throw new SystemException(
                    ResponseConstant.Code.BAD_REQUEST,
                    MessageUtils.message(ResponseConstant.Message.USER_HAS_BEEN_BLACKLISTED)
            );
        }

        sendActiveEmail(userEntity.getUsername(), email);

        return Response.success();
    }

    /**
     * Forgot password
     */
    @Override
    public Response<?> forgotPassword(ForgotPasswordDto forgotPasswordDto) {
        String email = forgotPasswordDto.getEmail();
        String newPassword = forgotPasswordDto.getNewPassword();
        UserEntity userEntity = userService.queryChain()
                .select(user.black_list, user.status, user.username)
                .where(user.email.eq(email))
                .one();

        if (ObjUtil.isNull(userEntity)) {
            throw new SystemException(
                    ResponseConstant.Code.NOT_FOUND,
                    MessageUtils.message(ResponseConstant.Message.USER_NOT_FOUND)
            );
        }

        if (Integer.valueOf(0).equals(userEntity.getStatus())) {
            throw new SystemException(
                    ResponseConstant.Code.USER_NOT_ACTIVATED,
                    MessageUtils.message(ResponseConstant.Message.USER_NOT_ACTIVATED)
            );
        }

        if (Integer.valueOf(1).equals(userEntity.getBlackList())) {
            throw new SystemException(
                    ResponseConstant.Code.USER_HAS_BEEN_BLACKLISTED,
                    MessageUtils.message(ResponseConstant.Message.USER_HAS_BEEN_BLACKLISTED)
            );
        }

        sendResetPasswordEmail(email, newPassword);

        return Response.success();
    }

    /**
     * Reset password
     */
    @Override
    public void resetPassword(String code, HttpServletResponse response) {
        try {
            String codeJson = EncryptionUtils.decode(code);
            ResetPasswordPayloadBo decrypt = JSON.parseObject(codeJson, ResetPasswordPayloadBo.class);

            String email = decrypt.getEmail();
            String newPassword = decrypt.getNewPassword();

            long data = decrypt.getDate().getTime();
            long currentTime = System.currentTimeMillis();
            if (currentTime > data + 1000 * 60 * 5) {
                response.sendRedirect(systemProperties.getFrontendUrl() + "/authorize/link-expired");
                return;
            }

            // Reset password
            userService.updateChain()
                    .set(user.password, passwordEncoder.encode(newPassword))
                    .where(user.email.eq(email))
                    .update();

            response.sendRedirect(systemProperties.getFrontendUrl() + "/authorize/login");
        } catch (Exception exception) {
            log.error("Reset password failed: ", exception);
            throw new SystemException(
                    ResponseConstant.Code.BAD_REQUEST,
                    MessageUtils.message(ResponseConstant.Message.RESET_PASSWORD_FAILED)
            );
        }

    }

    /**
     * Refresh token
     *
     * @param refreshTokenDTO Refresh token info
     * @return Token information
     */
    @Override
    public Response<TokenVo> refreshToken(RefreshTokenDto refreshTokenDTO) {
        // Parse and verify access token
        TokenPayload payloadAccessToken = JwtUtils
                .parseAccessToken(refreshTokenDTO.getAccessToken(), Boolean.FALSE)
                .orElseThrow(() -> new SystemException(
                        ResponseConstant.Code.BAD_REQUEST,
                        MessageUtils.message(ResponseConstant.Message.TOKEN_REFRESH_FAILED)
                ));

        // Parse and verify refresh token
        TokenPayload payloadRefreshToken = JwtUtils
                .parseRefreshToken(refreshTokenDTO.getRefreshToken(), Boolean.TRUE)
                .orElseThrow(() -> new SystemException(
                        ResponseConstant.Code.BAD_REQUEST,
                        MessageUtils.message(ResponseConstant.Message.TOKEN_REFRESH_FAILED)
                ));

        // Update user expiration time in redis
        redisTemplate
                .expire(
                        // key: user id
                        SystemConstant.REDIS_PREFIX_LOGIN + payloadAccessToken.getUserId(),
                        // expire time: refresh token expire time
                        systemProperties.getAuthorize().getAccessTokenExpireTime(),
                        // unit: minutes
                        TimeUnit.MINUTES
                );

        // Create new access token and refresh token
        TokenVo tokenInfo = new TokenVo();
        tokenInfo.setAccessToken(JwtUtils.createAccessToken(payloadAccessToken));
        tokenInfo.setRefreshToken(JwtUtils.createRefreshToken(payloadRefreshToken));
        return Response.success(tokenInfo);
    }

    /**
     * Auto login
     *
     * @param autoLoginDto Auto login key
     */
    @Override
    public Response<?> autoLogin(AutoLoginDto autoLoginDto) {
        // user id
        String userId = EncryptionUtils.decode(autoLoginDto.getLoginKey());

        User loginUser = redisTemplate.opsForValue().get(SystemConstant.REDIS_PREFIX_LOGIN + userId);

        if (ObjectUtil.isNull(loginUser) || StrUtil.isBlank(userId)) {
            throw new SystemException(
                    ResponseConstant.Code.UNAUTHORIZED,
                    MessageUtils.message(ResponseConstant.Message.UNAUTHORIZED)
            );
        }

        TokenPayload payload = new TokenPayload(userId);

        // Create new access token and refresh token
        TokenVo tokenInfo = new TokenVo();
        tokenInfo.setAccessToken(JwtUtils.createAccessToken(payload));
        tokenInfo.setRefreshToken(JwtUtils.createRefreshToken(payload));
        return Response.success(tokenInfo);
    }

    /**
     * Get captcha
     *
     * @param captchaKey Front end random generation
     */
    @Override
    public void getCaptcha(String captchaKey, HttpServletResponse response) {
        String captcha = ValidateCodeUtils.getCaptcha(5);
        captchaRedisTemplate.opsForValue().set(
                // key: captcha key
                SystemConstant.REDIS_PREFIX_CAPTCHA + captchaKey,
                // captcha
                captcha,
                // expire time: five minutes
                systemProperties.getAuthorize().getCaptchaExpireTime(),
                // unit: minutes
                TimeUnit.MINUTES
        );
        response.setContentType("image/jpeg");
        try {
            ValidateCodeUtils.generateCaptchaImage(response.getOutputStream(), captcha);
        } catch (Exception exception) {
            log.error("generate captcha failed: ", exception);

            throw new SystemException(
                    ResponseConstant.Code.SERVER_ERROR,
                    MessageUtils.message(ResponseConstant.Message.GET_CAPTCHA_FAILED)
            );
        }

    }

    private void validateCaptcha(String captchaKey, String captcha, String email) {
        String captchaCacheKey = SystemConstant.REDIS_PREFIX_CAPTCHA + captchaKey;

        String cacheCaptcha = captchaRedisTemplate.opsForValue().get(captchaCacheKey);
        if (StrUtil.isBlank(cacheCaptcha)) {
            AsyncManager.getInstance().executor(AsyncFactory.loginRecord(email, SystemConstant.LOGIN_STATUS_FAILED, "captcha has expired"));
            throw new SystemException(
                    ResponseConstant.Code.BAD_REQUEST,
                    MessageUtils.message(ResponseConstant.Message.CAPTCHA_HAS_EXPIRED)
            );
        }

        if (!cacheCaptcha.equalsIgnoreCase(captcha)) {
            AsyncManager.getInstance().executor(AsyncFactory.loginRecord(email, SystemConstant.LOGIN_STATUS_FAILED, "captcha error"));
            throw new SystemException(
                    ResponseConstant.Code.BAD_REQUEST,
                    MessageUtils.message(ResponseConstant.Message.CAPTCHA_ERROR)
            );
        }

        redisTemplate.delete(captchaCacheKey);
    }

    private void sendActiveEmail(String username, String email) {
        Map<String, Object> model = new HashMap<>();
        model.put("username", username);

        try {
            String url = String.format(
                    "%s/v1/authorize/active-user?email=%s&code=%s",
                    systemProperties.getBackendUrl(),
                    email,
                    URLEncoder.encode(EncryptionUtils.encode(email), "UTF-8")
            );
            model.put("url", url);
        } catch (Exception exception) {
            log.error("register user failed: ", exception);

            throw new SystemException(
                    ResponseConstant.Code.SERVER_ERROR,
                    MessageUtils.message(ResponseConstant.Message.SEND_EMAIL_FAILED)
            );
        }

        new Thread(() -> {
            MailUtils.sendTemplateMail(
                    email,
                    "Registration Activation - Simnectz Digital Finance Learning And Innovation Platform",
                    "active-user.ftlh",
                    model
            );
        }).start();
    }

    private void sendResetPasswordEmail(String email, String newPassword) {
        Map<String, Object> model = new HashMap<>();

        try {
            ResetPasswordPayloadBo encrypt = new ResetPasswordPayloadBo();
            encrypt.setEmail(email);
            encrypt.setNewPassword(newPassword);
            encrypt.setDate(new Date());

            String url = String.format(
                    "%s/v1/authorize/reset-password?code=%s",
                    systemProperties.getBackendUrl(),
                    URLEncoder.encode(EncryptionUtils.encode(JSON.toJSONString(encrypt)), "UTF-8")
            );
            model.put("url", url);
        } catch (Exception exception) {
            log.error("send email failed: ", exception);

            throw new SystemException(
                    ResponseConstant.Code.SERVER_ERROR,
                    MessageUtils.message(ResponseConstant.Message.SEND_EMAIL_FAILED)
            );
        }

        new Thread(() -> {
            MailUtils.sendTemplateMail(
                    email,
                    "Reset Password Confirm In 5 minutes - Simnectz Digital Finance Learning And Innovation Platform",
                    "reset-password.ftlh",
                    model
            );
        }).start();
    }

}
