package com.simnectz.soiplus.core.organizations.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.If;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import com.simnectz.soiplus.core.organizations.domain.bo.ValidationOrganizationBo;
import com.simnectz.soiplus.core.organizations.domain.dto.CreateOrganizationDto;
import com.simnectz.soiplus.core.organizations.domain.dto.UpdateOrganizationDto;
import com.simnectz.soiplus.core.organizations.domain.entity.OrganizationEntity;
import com.simnectz.soiplus.core.organizations.domain.vo.OrganizationDetailsVo;
import com.simnectz.soiplus.core.organizations.domain.vo.OrganizationOptionsVo;
import com.simnectz.soiplus.core.organizations.domain.vo.OrganizationVo;
import com.simnectz.soiplus.core.organizations.mapper.OrganizationMapper;
import com.simnectz.soiplus.core.organizations.service.OrganizationService;
import com.simnectz.soiplus.core.system.constant.ResponseConstant;
import com.simnectz.soiplus.core.system.constant.SystemConstant;
import com.simnectz.soiplus.core.system.domain.vo.Paginate;
import com.simnectz.soiplus.core.system.domain.vo.Response;
import com.simnectz.soiplus.core.system.exception.SystemException;
import com.simnectz.soiplus.core.system.utils.CodeUtils;
import com.simnectz.soiplus.core.system.utils.MessageUtils;
import com.simnectz.soiplus.core.users.service.UserService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;

import static com.mybatisflex.core.query.QueryMethods.distinct;
import static com.simnectz.soiplus.core.organizations.domain.entity.table.OrganizationTableDef.organization;
import static com.simnectz.soiplus.core.users.domian.entity.table.UserTableDef.user;

@Service
@RequiredArgsConstructor
public class OrganizationServiceImpl extends ServiceImpl<OrganizationMapper, OrganizationEntity> implements OrganizationService {

    private final UserService userService;

    /**
     * Validation organization name is unique
     *
     * @param organizationId   Organization id
     * @param organizationName Organization name
     * @return true: repeat, false: not repeat
     */
    @Override
    @Transactional(readOnly = true)
    public Response<Boolean> validationOrganizationNameUnique(String organizationId, String organizationName) {
        Boolean exists = this.exists(this.queryChain()
                .select()
                .from(organization)
                .where(organization.id.ne(organizationId, If::notNull))
                .and(organization.organization_name.eq(organizationName))
        );

        return Response.success(exists);
    }

    /**
     * Enquiry organization details
     *
     * @param organizationId Organization id
     * @return Organization details
     */
    @Override
    @Transactional(readOnly = true)
    public Response<OrganizationDetailsVo> enquiryOrganizationDetails(String organizationId) {
        // Enquiry organization details
        OrganizationDetailsVo organizationDetails = mapper.selectOneByQueryAs(
                this.queryChain()
                        .select(
                                organization.id,
                                organization.organization_id,
                                organization.organization_abbreviation_code,
                                organization.organization_name,
                                organization.organization_code,
                                organization.organization_entity_address,
                                organization.organization_nature,
                                organization.organization_tax_code,
                                organization.contact_person,
                                organization.contact_phone_number,
                                organization.contact_email
                        )
                        .from(organization)
                        .where(organization.id.eq(organizationId)),
                OrganizationDetailsVo.class
        );

        return Response.success(organizationDetails);
    }

    /**
     * Create organization
     *
     * @param createOrganizationDto Organization information
     */
    @Override
    @Transactional
    public Response<?> createOrganization(CreateOrganizationDto createOrganizationDto) {
        // 1. Validation organization information
        ValidationOrganizationBo validationOrganizationBo = BeanUtil.copyProperties(createOrganizationDto, ValidationOrganizationBo.class);
        this.validationOrganizationInfo(validationOrganizationBo);

        // 2. Create organization
        OrganizationEntity organizationEntity = BeanUtil.copyProperties(createOrganizationDto, OrganizationEntity.class);
        organizationEntity.setOrganizationId(CodeUtils.getBusinessCode());
        this.save(organizationEntity);

        return Response.success();
    }

    /**
     * Paginate enquiry organizations
     *
     * @param keywords    Organization name keywords
     * @param currentPage Current page
     * @param pageSize    Paginate size
     * @param orderBy     Order field
     * @param isAsc       true: ASC, false: DESC
     * @param startTime   Timestamp of the end time
     * @param endTime     Timestamp of the start time
     * @return List of eligible organizations
     */
    @Override
    @Transactional(readOnly = true)
    public Response<Paginate<OrganizationVo>> paginateEnquiryOrganizations(
            String keywords,
            Integer currentPage,
            Integer pageSize,
            String orderBy,
            Boolean isAsc,
            Date startTime,
            Date endTime
    ) {
        Page<OrganizationVo> organizationPage = mapper.paginateAs(
                currentPage,
                pageSize,
                this.queryChain()
                        .select(
                                organization.id,
                                organization.organization_id,
                                organization.organization_name,
                                organization.organization_abbreviation_code,
                                organization.organization_nature,
                                organization.create_time
                        )
                        .from(organization)
                        .where(organization.organization_name.like(keywords, If::hasText))
                        .and(organization.create_time.gt(startTime, If::notNull))
                        .and(organization.create_time.lt(endTime, If::notNull))
                        .orderBy(StrUtil.toUnderlineCase(orderBy), StrUtil.isNotBlank(orderBy) ? isAsc : null),
                OrganizationVo.class
        );

        return Response.success(new Paginate<>(organizationPage.getTotalRow(), organizationPage.getRecords()));
    }

    /**
     * Batch delete organizations
     *
     * @param organizationIds Organization ids
     */
    @Override
    @Transactional
    public Response<?> batchDeleteOrganizations(Set<String> organizationIds) {
        // Filter unused organization ids
        Set<String> usedOrganizationIds = new HashSet<>(userService.queryChain()
                .select(distinct(user.organization_id))
                .from(user)
                .where(user.organization_id.in(organizationIds))
                .listAs(String.class)
        );

        if (usedOrganizationIds.size() > SystemConstant.ZERO) {
            // Enquiry used organization name
            List<String> usedOrganizationName = this.queryChain()
                    .select(organization.organization_name)
                    .from(organization)
                    .where(organization.organization_id.in(usedOrganizationIds))
                    .listAs(String.class);

            HashMap<String, String> keywords = new HashMap<>();
            keywords.put("{organizationNames}", StrUtil.join(",", usedOrganizationName));

            throw new SystemException(
                    ResponseConstant.Code.BAD_REQUEST,
                    MessageUtils.replacePlaceholders(
                            ResponseConstant.Message.UNABLE_DELETE_ORGANIZATION,
                            keywords
                    )
            );
        }

        // Delete organizations by organization ids
        this.remove(organization.organization_id.in(organizationIds));

        return Response.success();
    }

    /**
     * Update organization
     *
     * @param updateOrganizationDto Organization information
     */
    @Override
    @Transactional
    public Response<?> updateOrganization(UpdateOrganizationDto updateOrganizationDto) {
        // 1. Validation organization information
        ValidationOrganizationBo validationOrganizationBo = BeanUtil.copyProperties(updateOrganizationDto, ValidationOrganizationBo.class);
        this.validationOrganizationInfo(validationOrganizationBo);

        // 2. Update organization
        this.updateChain()
                .set(organization.organization_abbreviation_code, updateOrganizationDto.getOrganizationAbbreviationCode())
                .set(organization.organization_name, updateOrganizationDto.getOrganizationName())
                .set(organization.organization_code, updateOrganizationDto.getOrganizationCode())
                .set(organization.organization_entity_address, updateOrganizationDto.getOrganizationEntityAddress())
                .set(organization.organization_nature, updateOrganizationDto.getOrganizationNature())
                .set(organization.organization_tax_code, updateOrganizationDto.getOrganizationTaxCode())
                .set(organization.contact_person, updateOrganizationDto.getContactPerson())
                .set(organization.contact_phone_number, updateOrganizationDto.getContactPhoneNumber())
                .set(organization.contact_email, updateOrganizationDto.getContactEmail())
                .where(organization.id.eq(updateOrganizationDto.getId()))
                .update();

        return Response.success();
    }

    /**
     * Enquiry organization options
     *
     * @return Organization options
     */
    @Override
    @Transactional(readOnly = true)
    public Response<List<OrganizationOptionsVo>> enquiryOrganizationOptions() {
        List<OrganizationOptionsVo> organizationOptions = this.queryChain()
                .select(organization.organization_id, organization.organization_name)
                .from(organization)
                .listAs(OrganizationOptionsVo.class);
        return Response.success(organizationOptions);
    }

    /**
     * Validation organization information
     *
     * @param validationOrganizationBo Organization information that needs to be verified
     *                               1. If organization id is not null, validation organization is exists
     *                               2. Validation organization name is unique
     */
    @Transactional(readOnly = true)
    public void validationOrganizationInfo(ValidationOrganizationBo validationOrganizationBo) {
        // If organization id is not null, validation organization is exists
        if (ObjUtil.isNotEmpty(validationOrganizationBo.getId())) {
            Boolean exists = this.validationOrganizationExists(validationOrganizationBo.getId());
            if (!exists) {
                throw new SystemException(
                        ResponseConstant.Code.NOT_FOUND,
                        MessageUtils.message(ResponseConstant.Message.ORGANIZATION_NOT_FOUND)
                );
            }
        }

        // Validation organization name is unique
        Response<Boolean> isOrganizationNameUniqueResult = this.validationOrganizationNameUnique(
                validationOrganizationBo.getId(), validationOrganizationBo.getOrganizationName());

        if (isOrganizationNameUniqueResult.getData()) {
            throw new SystemException(
                    ResponseConstant.Code.BAD_REQUEST,
                    MessageUtils.message(ResponseConstant.Message.ORGANIZATION_NAME_ALREADY_EXISTS)
            );
        }
    }

    /**
     * Validation organization is exists
     *
     * @param organizationId Organization id
     * @return true: exists, false: not exists
     */
    @Transactional(readOnly = true)
    public Boolean validationOrganizationExists(String organizationId) {
        return this.exists(this.queryChain()
                .select()
                .from(organization)
                .where(organization.id.eq(organizationId))
        );
    }

}
