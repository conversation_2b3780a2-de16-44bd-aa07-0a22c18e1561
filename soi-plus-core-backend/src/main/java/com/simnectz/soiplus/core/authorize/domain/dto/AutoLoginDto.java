package com.simnectz.soiplus.core.authorize.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class AutoLoginDto implements Serializable {

    private static final long serialVersionUID = -6058463501419386194L;

    private String loginKey;

}
