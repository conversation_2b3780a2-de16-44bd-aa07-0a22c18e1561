package com.simnectz.soiplus.core.log.login.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class LoginLogVo implements Serializable {

    private static final long serialVersionUID = -8071332034071086600L;

    private String id;
    private String email;
    private String ipAddr;
    private String loginLocation;
    private String browser;
    private String os;
    private Integer status;
    private String message;
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date loginTime;

}
