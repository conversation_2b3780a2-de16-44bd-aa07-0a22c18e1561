package com.simnectz.soiplus.core.system.utils;

import cn.hutool.extra.spring.SpringUtil;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;

import java.util.Map;
import java.util.Set;

/**
 * Ii8n Utils
 */
public class MessageUtils {

    private static final MessageSource messageSource = SpringUtil.getBean(MessageSource.class);

    public static String message(String code) {
        return messageSource.getMessage(code, null, LocaleContextHolder.getLocale());
    }

    public static String message(String code, String... args) {
        return messageSource.getMessage(code, args, LocaleContextHolder.getLocale());
    }

    /**
     *
     * @param message Message key
     * @param placeholders Placeholders, key: placeholder, value: value
     * @return message
     */
    public static String replacePlaceholders(String message, Map<String, String> placeholders) {
        String messageTemplate = MessageUtils.message(message);
        Set<String> keys = placeholders.keySet();
        for (String key : keys) {
            messageTemplate = messageTemplate.replace(key, placeholders.get(key));
        }
        return messageTemplate;
    }

}
