package com.simnectz.soiplus.core.system.constant;

import java.util.HashSet;
import java.util.Set;

public class SystemConstant {

    /**
     * Anonymous user
     */
    public static final String ANONYMOUS_USER = "anonymousUser";

    /**
     * Datetime format
     */
    public static final String DATE_TIME_FORMAT = "yyyy-MM-dd HH:mm:ss";

    /**
     * Date format
     */
    public static final String DATE_FORMAT = "yyyy-MM-dd";

    /**
     * Time format
     */
    public static final String TIME_FORMAT = "HH:mm:ss";

    /**
     * Default role
     */
    public static final Set<String> DEFAULT_ROLES = new HashSet<String>(){{
        this.add("Student");
        this.add("Examinee");
    }};

    /**
     * Constant 'zero'
     */
    public static final Integer ZERO = 0;

    /**
     * Constant 'ONE'
     */
    public static final Integer ONE = 1;

    /**
     * Constant 'TWO'
     */
    public static final Integer TWO = 2;

    /**
     * Constant 'THREE'
     */
    public static final Integer THREE = 3;

    /**
     * Default current page
     */
    public static final Integer DEFAULT_CURRENT_PAGE = 1;

    /**
     * Default page size
     */
    public static final Integer DEFAULT_PAGE_SIZE = 20;

    /**
     * Constant 'empty string'
     */
    public static final String EMPTY_STRING = "";

    /**
     * Constant 'zero string'
     */
    public static final String ZERO_STRING = "0";

    /**
     * Redis login user information prefix
     */
    public static final String REDIS_PREFIX_LOGIN = "LOGIN:";

    /**
     * Redis captcha prefix
     */
    public static final String REDIS_PREFIX_CAPTCHA = "CAPTCHA:";

    /**
     * Token payload 'id'
     */
    public static final String TOKEN_PAYLOAD_ID = "id";

    /**
     * Role authority prefix
     */
    public static final String ROLE_AUTHORITY_PREFIX = "ROLE_";

    /**
     * String '#'
     */
    public static final String WELL = "#";

    /**
     * Root permission id
     */
    public static final String ROOT_PERMISSION_ID = "0";

    /**
     * Root permission name
     */
    public static final String ROOT_PERMISSION_NAME = "Root";

    /**
     * Root permission tree path
     */
    public static final String ROOT_PERMISSION_TREE_PATH = "#01#";

    /**
     * system config
     */
    public static final String CONFIG_PASSWORD = "PASSWORD";
    public static final String CONFIG_USER = "USER";

    public static final Integer LOGIN_STATUS_SUCCESS = 1;
    public static final Integer LOGIN_STATUS_FAILED = -1;

    private SystemConstant() {
    }

}
