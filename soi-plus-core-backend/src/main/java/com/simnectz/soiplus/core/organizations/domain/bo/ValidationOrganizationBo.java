package com.simnectz.soiplus.core.organizations.domain.bo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class ValidationOrganizationBo implements Serializable {

    private static final long serialVersionUID = -5412863439928257172L;

    private String id;
    private String organizationName;

}
