package com.simnectz.soiplus.core.roles.domain.dto;

import com.simnectz.soiplus.core.system.domain.dto.PaginateEnquiry;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Set;

/**
 * Paginate enquiry roles request parameters
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class EnquiryRoleDto extends PaginateEnquiry implements Serializable {

    private static final long serialVersionUID = -7181585090697781299L;

    private String keywords;
    private Set<Long> roleIds;

}
