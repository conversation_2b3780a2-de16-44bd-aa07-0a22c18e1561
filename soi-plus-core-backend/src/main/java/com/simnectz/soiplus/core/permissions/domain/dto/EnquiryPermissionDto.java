package com.simnectz.soiplus.core.permissions.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class EnquiryPermissionDto implements Serializable {

    private static final long serialVersionUID = 1881485223503272766L;

    private String keywords;

}
