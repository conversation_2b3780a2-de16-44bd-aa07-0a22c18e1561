package com.simnectz.soiplus.core.system.validation;

import cn.hutool.core.util.StrUtil;
import com.simnectz.soiplus.core.system.annotation.validation.ValidationUsername;
import com.simnectz.soiplus.core.system.utils.ValidatorUtils;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

/**
 * Username validator
 */
public class UsernameValidation implements ConstraintValidator<ValidationUsername, String> {


    @Override
    public void initialize(ValidationUsername constraintAnnotation) {
        ConstraintValidator.super.initialize(constraintAnnotation);
    }

    @Override
    public boolean isValid(String username, ConstraintValidatorContext constraintValidatorContext) {
        if (StrUtil.isBlank(username)) {
            return true;
        }

        return ValidatorUtils.validateUsername(username);
    }

}
