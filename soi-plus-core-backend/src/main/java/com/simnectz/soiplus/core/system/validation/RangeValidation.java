package com.simnectz.soiplus.core.system.validation;

import com.simnectz.soiplus.core.system.annotation.validation.ValidationRange;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

public class RangeValidation implements ConstraintValidator<ValidationRange, Integer> {

    private Integer min;
    private Integer max;

    @Override
    public void initialize(ValidationRange constraintAnnotation) {
        min = constraintAnnotation.min();
        max = constraintAnnotation.max();
    }

    @Override
    public boolean isValid(Integer value, ConstraintValidatorContext context) {
        return value >= min && value <= max;
    }

}
