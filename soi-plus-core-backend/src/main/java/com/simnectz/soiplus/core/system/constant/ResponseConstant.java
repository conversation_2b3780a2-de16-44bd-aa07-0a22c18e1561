package com.simnectz.soiplus.core.system.constant;

public class ResponseConstant {

    public static class Code {
        public static final Integer OPERATION_SUCCESS = 20000;
        public static final Integer BAD_REQUEST = 40000;
        public static final Integer UNAUTHORIZED = 40100;
        public static final Integer FORBIDDEN = 40300;
        public static final Integer NOT_FOUND = 40400;
        public static final Integer METHOD_NOT_ALLOWED = 40500;
        public static final Integer SERVER_ERROR = 50000;
        public static final Integer USER_NOT_ACTIVATED = 40001;
        public static final Integer USER_HAS_BEEN_BLACKLISTED = 40002;
        public static final Integer USER_ACTIVATED = 40003;
    }

    public static class Message {
        public static final String OPERATION_SUCCESS = "OPERATION_SUCCESS";
        public static final String UNAUTHORIZED = "UNAUTHORIZED";
        public static final String FORBIDDEN = "FORBIDDEN";
        public static final String METHOD_NOT_ALLOWED = "METHOD_NOT_ALLOWED";
        public static final String SERVER_ERROR = "SERVER_ERROR";
        public static final String DEFAULT_ROLE_NOT_FOUND = "DEFAULT_ROLE_NOT_FOUND";
        public static final String EMAIL_ALREADY_REGISTER = "EMAIL_ALREADY_REGISTER";
        public static final String TOKEN_REFRESH_FAILED = "TOKEN_REFRESH_FAILED";
        public static final String INVALID_ROLE_INFORMATION = "INVALID_ROLE_INFORMATION";
        public static final String USER_NOT_FOUND = "USER_NOT_FOUND";
        public static final String ROLE_NOT_FOUND = "ROLE_NOT_FOUND";
        public static final String ROLE_NAME_ALREADY_EXISTS = "ROLE_NAME_ALREADY_EXISTS";
        public static final String UNABLE_DELETE_ROLE = "UNABLE_DELETE_ROLE";
        public static final String USER_NOT_ACTIVATED = "USER_NOT_ACTIVATED";
        public static final String CAPTCHA_HAS_EXPIRED = "CAPTCHA_HAS_EXPIRED";
        public static final String CAPTCHA_ERROR = "CAPTCHA_ERROR";
        public static final String USER_ACTIVATION_FAILED = "USER_ACTIVATION_FAILED";
        public static final String GET_CAPTCHA_FAILED = "GET_CAPTCHA_FAILED";
        public static final String USER_ACTIVATED = "USER_ACTIVATED";
        public static final String USER_HAS_BEEN_BLACKLISTED = "USER_HAS_BEEN_BLACKLISTED";
        public static final String SEND_EMAIL_FAILED = "SEND_EMAIL_FAILED";
        public static final String RESET_PASSWORD_FAILED = "RESET_PASSWORD_FAILED";
        public static final String INVALID_PERMISSION_INFORMATION = "INVALID_PERMISSION_INFORMATION";
        public static final String PERMISSION_NOT_FOUND = "PERMISSION_NOT_FOUND";
        public static final String PERMISSION_NAME_ALREADY_EXISTS = "PERMISSION_NAME_ALREADY_EXISTS";
        public static final String UNABLE_DELETE_PERMISSION = "UNABLE_DELETE_PERMISSION";
        public static final String PARENT_PERMISSION_NOT_FOUND = "PARENT_PERMISSION_NOT_FOUND";
        public static final String ORGANIZATION_NOT_FOUND = "ORGANIZATION_NOT_FOUND";
        public static final String ORGANIZATION_NAME_ALREADY_EXISTS = "ORGANIZATION_NAME_ALREADY_EXISTS";
        public static final String UNABLE_DELETE_ORGANIZATION = "UNABLE_DELETE_ORGANIZATION";
        public static final String IMPORT_USER_FAILED = "IMPORT_USER_FAILED";
        public static final String EMAIL_DUPLICATED = "EMAIL_DUPLICATED";
        public static final String INVALID_USERNAMES = "INVALID_USERNAMES";
        public static final String INVALID_EMAILS = "INVALID_EMAILS";
        public static final String INVALID_CARD_IDS = "INVALID_CARD_IDS";
        public static final String INVALID_PHONE_NUMBERS = "INVALID_PHONE_NUMBERS";
        public static final String INVALID_SEX = "INVALID_SEX";
        public static final String INVALID_CARD_TYPE = "INVALID_CARD_TYPE";
        public static final String INVALID_USER_LEVEL = "INVALID_USER_LEVEL";
        public static final String LEVEL_IS_REQUIRED = "LEVEL_IS_REQUIRED";
        public static final String PARAM_IS_NULL = "PARAM_IS_NULL";
        public static final String MAIL_SUFFIX_NOT_EMPTY = "MAIL_SUFFIX_NOT_EMPTY";
        public static final String NAME_ALREADY_EXISTS = "NAME_ALREADY_EXISTS";
        public static final String PASSWORD_UPDATE_FAILED = "PASSWORD_UPDATE_FAILED";
        public static final String INVALID_ACCOUNT_RETENTION_PERIOD = "INVALID_ACCOUNT_RETENTION_PERIOD";
        public static final String TC_NOT_FOUND = "TC_NOT_FOUND";
    }

    private ResponseConstant() {
    }

}
