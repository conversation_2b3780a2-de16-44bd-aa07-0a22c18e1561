package com.simnectz.soiplus.core.config.resource;

import com.simnectz.soiplus.core.config.domain.SystemConfEntity;
import com.simnectz.soiplus.core.config.service.SystemConfigService;
import com.simnectz.soiplus.core.system.domain.vo.Response;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;

import java.util.List;

/**
 * 系统参数配置模块
 */
@RequestMapping("v1/practical/system-config")
@RequiredArgsConstructor
public class SystemConfigResource {

    private final SystemConfigService systemConfigService;

    /**
     * 获取所有邮箱后缀
     * @return
     */
    @GetMapping("/getEmailSuffix")
    public ResponseEntity<Response<?>> getEmailSuffix() {
        return ResponseEntity.ok(systemConfigService.selectEmailSuffix());
    }

    /**
     * 新增邮箱后缀规则
     * @param suffixList
     * @return
     */
    @PostMapping("/addEmailRule")
    public ResponseEntity<Response<?>> addEmaiRule(@RequestBody String[] suffixList) {
        return ResponseEntity.ok(systemConfigService.addEmailSuffix(suffixList));
    }

    /**
     * 查询系统配置
     * @return
     */
    @GetMapping("/find")
    public ResponseEntity<Response<?>> queryConfig() {
        return ResponseEntity.ok(systemConfigService.queryConfig());
    }

    /**
     * 新增更新系统配置
     * @param systemConf
     * @return
     */
    @PostMapping("/save")
    public ResponseEntity<Response<?>> saveConfig(@RequestBody SystemConfEntity systemConf) {
        return ResponseEntity.ok(systemConfigService.saveConfig(systemConf));
    }

    /**
     * 删除系统配置
     * @param ids
     * @return
     */
    @PostMapping("/del")
    public ResponseEntity<Response<?>> deleteConfig(@RequestBody List<Integer> ids) {
        return ResponseEntity.ok(systemConfigService.deleteConfig(ids));
    }
}
