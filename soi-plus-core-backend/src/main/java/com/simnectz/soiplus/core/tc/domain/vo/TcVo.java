package com.simnectz.soiplus.core.tc.domain.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class TcVo implements Serializable {

    private static final long serialVersionUID = -1602595608000716165L;

    private String id;
    private String content;

}
