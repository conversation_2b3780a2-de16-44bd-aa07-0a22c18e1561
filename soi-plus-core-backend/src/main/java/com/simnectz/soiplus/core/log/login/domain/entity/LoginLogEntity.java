package com.simnectz.soiplus.core.log.login.domain.entity;

import com.mybatisflex.annotation.Table;
import com.simnectz.soiplus.core.system.domain.entity.BasicEntity;
import lombok.*;

import java.io.Serializable;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Table(value = "system_login_log")
@EqualsAndHashCode(callSuper = true)
public class LoginLogEntity extends BasicEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 用户账号
     */
    private String email;

    /**
     * 登录IP地址
     */
    private String ipAddr;

    /**
     * 登录地点
     */
    private String loginLocation;

    /**
     * 浏览器类型
     */
    private String browser;

    /**
     * 操作系统
     */
    private String os;

    /**
     * 登录状态 -1: 失败 1：成功
     */
    private Integer status;

    /**
     * 提示消息
     */
    private String message;

    /**
     * 访问时间
     */
    private String loginTime;

}
