package com.simnectz.soiplus.core.organizations.domain.dto;

import com.simnectz.soiplus.core.system.annotation.validation.ValidationEmail;
import com.simnectz.soiplus.core.system.annotation.validation.ValidationPhoneNumber;
import com.simnectz.soiplus.core.system.annotation.validation.ValidationUsername;
import com.simnectz.soiplus.core.system.constant.FieldLength;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class UpdateOrganizationDto implements Serializable {

    private static final long serialVersionUID = -3808876873187083229L;

    private String id;
    @NotBlank(message = "{ORGANIZATION_NAME_IS_REQUIRED}")
    @Length(max = FieldLength.ORGANIZATION_NAME_MAX, message = "{INVALID_ORGANIZATION_NAME_LENGTH}")
    private String organizationName;

    @NotBlank(message = "{ORGANIZATION_ABBREVIATION_CODE_IS_REQUIRED}")
    @Length(max = FieldLength.ORGANIZATION_ABBREVIATION_CODE_MAX, message = "{INVALID_ORGANIZATION_ABBREVIATION_CODE_LENGTH}")
    private String organizationAbbreviationCode;

    @NotBlank(message = "{CONTACT_PERSON_IS_REQUIRED}")
    @Length(min = FieldLength.CONTACT_PERSON_MIN, max = FieldLength.CONTACT_PERSON_MAX, message = "{INVALID_CONTACT_PERSON_LENGTH}")
    @ValidationUsername(message = "{INVALID_CONTACT_PERSON_FORMAT}")
    private String contactPerson;

    @NotBlank(message = "{CONTACT_PHONE_NUMBER_IS_REQUIRED}")
    @ValidationPhoneNumber
    private String contactPhoneNumber;

    @NotBlank(message = "{CONTACT_EMAIL_IS_REQUIRED}")
    @ValidationEmail
    private String contactEmail;

    private String organizationCode;

    private String organizationEntityAddress;

    private String organizationNature;

    private String organizationTaxCode;

}
