package com.simnectz.soiplus.core.system.constant;

public class RegexpConstant {

    /**
     * 用户昵称正则表达式
     */
    public static final String NICKNAME_REGEXP = "^[\\s\\S]*$";
    /**
     * 用户姓名正则表达式
     */
    public static final String USERNAME_REGEXP = "^[\\s\\S]*$";
    /**
     * 电子邮箱正则表达式
     */
    public static final String EMAIL_REGEXP = "^[\\w.-]+@([a-zA-Z0-9-]+\\.)+[a-zA-Z]{2,}$";
    /**
     * 登录密码正则表达式
     */
    public static final String PASSWORD_REGEXP = "^.*(?=.)(?=.*\\d)(?=.*[a-z])(?=.*[A-Z]).*$";
    /**
     * 中国大陆手机号码正则表达式
     */
    public static final String CHINESE_MAINLAND_PHONE_NUMBER_REGEXP = "^1[3456789]\\d{9}$";
    /**
     * 香港手机号码正则表达式
     */
    public static final String HONG_KONG_PHONE_NUMBER_REGEXP = "^[6|9]\\d{7}$";
    /**
     * 台湾手机号码正则表达式
     */
    public static final String TAIWAN_PHONE_NUMBER_REGEXP = "^[0][9]\\d{8}$";
    /**
     * 澳门手机号码正则表达式
     */
    public static final String MACAO_PHONE_NUMBER_REGEXP = "^[6]\\d{7}$";

    private RegexpConstant() {
    }

}
