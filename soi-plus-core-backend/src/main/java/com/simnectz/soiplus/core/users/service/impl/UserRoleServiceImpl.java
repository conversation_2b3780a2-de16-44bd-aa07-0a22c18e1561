package com.simnectz.soiplus.core.users.service.impl;

import com.mybatisflex.spring.service.impl.ServiceImpl;
import com.simnectz.soiplus.core.users.domian.entity.UserRoleEntity;
import com.simnectz.soiplus.core.users.mapper.UserRoleMapper;
import com.simnectz.soiplus.core.users.service.UserRoleService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * User role service implements
 */
@Service
@RequiredArgsConstructor
public class UserRoleServiceImpl extends ServiceImpl<UserRoleMapper, UserRoleEntity> implements UserRoleService {
}
