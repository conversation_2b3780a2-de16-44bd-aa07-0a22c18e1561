package com.simnectz.soiplus.core.tc.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import com.simnectz.soiplus.core.system.constant.ResponseConstant;
import com.simnectz.soiplus.core.system.domain.vo.Response;
import com.simnectz.soiplus.core.system.exception.SystemException;
import com.simnectz.soiplus.core.system.utils.MessageUtils;
import com.simnectz.soiplus.core.tc.domain.dto.UpdateTcDto;
import com.simnectz.soiplus.core.tc.domain.entity.TcEntity;
import com.simnectz.soiplus.core.tc.domain.vo.TcVo;
import com.simnectz.soiplus.core.tc.mapper.TcMapper;
import com.simnectz.soiplus.core.tc.service.TcService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

import static com.simnectz.soiplus.core.tc.domain.entity.table.TcTableDef.tc;

@Service
@RequiredArgsConstructor
public class TcServiceImpl extends ServiceImpl<TcMapper, TcEntity> implements TcService {

    @Override
    public Response<?> updateTc(UpdateTcDto updateTcDto) {
        this.updateChain()
                .set(tc.content, updateTcDto.getContent())
                .where(tc.id.eq(updateTcDto.getId()))
                .update();
        return Response.success();
    }

    @Override
    public Response<TcVo> getTc() {
        List<TcEntity> tc = this.list();
        if (CollUtil.isEmpty(tc)) {
            throw new SystemException(
                    ResponseConstant.Code.NOT_FOUND,
                    MessageUtils.message(ResponseConstant.Message.TC_NOT_FOUND)
            );
        }

        TcVo tcVo = BeanUtil.copyProperties(tc.get(0), TcVo.class);
        return Response.success(tcVo);
    }

}
