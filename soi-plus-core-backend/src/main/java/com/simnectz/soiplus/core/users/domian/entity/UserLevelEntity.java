package com.simnectz.soiplus.core.users.domian.entity;

import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import lombok.*;

@Data
@With
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(value = "system_user_level")
public class UserLevelEntity {

    private static final long serialVersionUID = 5792416597542396818L;

    @Id(keyType = KeyType.Auto)
    private Integer id;
    private String levelEn;
    private String levelZh;

}
