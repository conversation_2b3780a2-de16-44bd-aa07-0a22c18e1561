package com.simnectz.soiplus.core.users.domian.bo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * User information that needs to be verified
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class ValidationUserBo implements Serializable {

    private static final long serialVersionUID = -2110849513766727461L;

    private String id;
    private String email;

}
