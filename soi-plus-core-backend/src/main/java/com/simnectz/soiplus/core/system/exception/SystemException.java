package com.simnectz.soiplus.core.system.exception;

import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * Global exception
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class SystemException extends RuntimeException {

    private static final long serialVersionUID = 6110499517189303427L;

    private Integer code;

    public SystemException(Integer code, String message) {
        super(message);
        this.code = code;
    }

}
