package com.simnectz.soiplus.core.system.config.serialize;

import cn.hutool.core.date.DateUtil;
import com.fasterxml.jackson.core.JsonGenerator;
import com.fasterxml.jackson.databind.SerializerProvider;
import com.fasterxml.jackson.databind.ser.std.StdSerializer;

import java.io.IOException;

public class LongToTimeSerializer extends StdSerializer<Long> {

    private static final long serialVersionUID = 9034102310925725634L;

    public LongToTimeSerializer() {
        super(Long.class);
    }

    @Override
    public void serialize(Long value, JsonGenerator generator, SerializerProvider provider) throws IOException {
        generator.writeString(DateUtil.formatTime(DateUtil.date()));
    }

}
