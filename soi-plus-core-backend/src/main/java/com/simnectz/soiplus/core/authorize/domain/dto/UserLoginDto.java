package com.simnectz.soiplus.core.authorize.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class UserLoginDto implements Serializable {

    private static final long serialVersionUID = 6101001382116487076L;

    @NotBlank(message = "{EMAIL_IS_REQUIRED}")
    private String email;
    @NotBlank(message = "{PASSWORD_IS_REQUIRED}")
    private String password;
     @NotBlank(message = "{CAPTCHA_KEY_IS_REQUIRED}")
    private String captchaKey;
     @NotBlank(message = "{CAPTCHA_IS_REQUIRED}")
    private String captcha;

}
