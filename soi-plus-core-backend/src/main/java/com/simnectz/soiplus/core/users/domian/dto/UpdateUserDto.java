package com.simnectz.soiplus.core.users.domian.dto;

import com.simnectz.soiplus.core.system.annotation.validation.*;
import com.simnectz.soiplus.core.system.constant.FieldLength;
import com.simnectz.soiplus.core.system.enums.CardType;
import com.simnectz.soiplus.core.system.enums.Gender;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.Length;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.io.Serializable;
import java.util.Date;
import java.util.Set;

/**
 * Update user request parameters
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
@ValidationCardId
public class UpdateUserDto implements Serializable, Card {

    private static final long serialVersionUID = -5394544996915191041L;

    @NotBlank(message = "{USER_ID_IS_REQUIRED}")
    private String id;
    /**
     * 昵称
     */
    @NotBlank(message = "{NICKNAME_IS_REQUIRED}")
    @ValidationNickname
    @Length(min = FieldLength.NICKNAME_MIN, max = FieldLength.NICKNAME_MAX, message = "{INVALID_NICKNAME_LENGTH}")
    private String nickname;
    /**
     * 用户姓名
     */
    @NotBlank(message = "{USERNAME_IS_REQUIRED}")
    @ValidationUsername
    @Length(min = FieldLength.USERNAME_MIN, max = FieldLength.USERNAME_MAX, message = "{USERNAME_NICKNAME_LENGTH}")
    private String username;
    /**
     * 用户邮箱
     */
    @NotBlank(message = "{EMAIL_IS_REQUIRED}")
    @ValidationEmail
    @Length(max = FieldLength.EMAIL_MAX, message = "{INVALID_EMAIL_LENGTH}")
    private String email;
    /**
     * 用户照片
     */
    private String image;
    /**
     * 证件类型
     * 1: 中国大陆身份证(15/18位)
     * 2: 香港身份证(10位)
     * 3: 台湾身份证(10位)
     * 4: 澳门身份证(10位)
     */
    @NotBlank(message = "{CARD_TYPE_IS_REQUIRED}")
    @ValidationEnum(enumClass = CardType.class, message = "{UNSUPPORTED_CARD_TYPE}")
    private String cardType;
    /**
     * 证件号码
     */
    @NotBlank(message = "{CARD_ID_IS_REQUIRED}")
    private String cardId;
    /**
     * 出生年份
     */
    private Date birthday;
    /**
     * 性别：1：男，2：女
     */
    @ValidationEnum(enumClass = Gender.class, message = "{INVALID_GENDER}")
    private String sex;
    /**
     * 手机号码
     */
    @NotBlank(message = "{PHONE_NUMBER_IS_REQUIRED}")
    @ValidationPhoneNumber
    private String phoneNumber;
    /**
     * 用户级别
     */
    private String level;
    /**
     * 毕业学校
     */
    private String graduateSchool;
    /**
     * 所属组织的名称
     */
    private String organizationId;
    /**
     * 工号/学号
     */
    private String studentOrStaffNumber;
    /**
     * 部门
     */
    private String department;
    /**
     * 职务
     */
    private String jobTitle;
    /**
     * 工作年限
     */
    private Integer experienceYear;
    /**
     * 角色ids
     */
    @NotEmpty(message = "{ROLE_IS_REQUIRED}")
    private Set<String> roleIds;

}
