package com.simnectz.soiplus.core.authorize.domain.dto;

import com.simnectz.soiplus.core.system.annotation.validation.ValidationEmail;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class ResendActiveEmailDto implements Serializable {

    private static final long serialVersionUID = 4627867143623889959L;

    @NotBlank(message = "{EMAIL_IS_REQUIRED}")
    @ValidationEmail
    private String email;

}
