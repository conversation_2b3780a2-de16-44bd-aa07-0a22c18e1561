package com.simnectz.soiplus.core.users.domian.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.springframework.web.multipart.MultipartFile;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class ImportUsersDto implements Serializable {

    private static final long serialVersionUID = -1178769815226483280L;

    private String organizationId;
    private MultipartFile file;

}
