package com.simnectz.soiplus.core.log.login.service.impl;

import cn.hutool.core.util.StrUtil;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.If;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import com.simnectz.soiplus.core.log.login.domain.entity.LoginLogEntity;
import com.simnectz.soiplus.core.log.login.domain.vo.LoginLogVo;
import com.simnectz.soiplus.core.log.login.mapper.LoginLogMapper;
import com.simnectz.soiplus.core.log.login.service.LoginLogService;
import com.simnectz.soiplus.core.system.domain.vo.Paginate;
import com.simnectz.soiplus.core.system.domain.vo.Response;
import org.springframework.stereotype.Service;

import java.util.Date;

import static com.simnectz.soiplus.core.log.login.domain.entity.table.LoginLogTableDef.login_log;

@Service
public class LoginLogServiceImpl extends ServiceImpl<LoginLogMapper, LoginLogEntity> implements LoginLogService {

    @Override
    public void saveLoginLog(LoginLogEntity loginLogEntity) {
        this.save(loginLogEntity);
    }

    @Override
    public Response<Paginate<LoginLogVo>> paginateEnquiryLoginLogs(
            String email,
            String ipAddr,
            Integer status,
            Integer currentPage,
            Integer pageSize,
            String orderBy,
            Boolean isAsc,
            Date startTime,
            Date endTime
    ) {
        Page<LoginLogVo> loginLogPage = mapper.paginateAs(
                currentPage,
                pageSize,
                this.queryChain()
                        .select(
                                login_log.id,
                                login_log.email,
                                login_log.ip_addr,
                                login_log.login_location,
                                login_log.browser,
                                login_log.os,
                                login_log.status,
                                login_log.message,
                                login_log.login_time
                        )
                        .from(login_log)
                        .where(login_log.email.eq(email, If::hasText))
                        .where(login_log.ip_addr.eq(ipAddr, If::hasText))
                        .where(login_log.status.eq(status, If::notNull))
                        .and(login_log.create_time.gt(startTime, If::notNull))
                        .and(login_log.create_time.lt(endTime, If::notNull))
                        .orderBy(StrUtil.toUnderlineCase(orderBy), StrUtil.isNotBlank(orderBy) ? isAsc : null),
                LoginLogVo.class
        );

        return Response.success(new Paginate<>(loginLogPage.getTotalRow(), loginLogPage.getRecords()));
    }

}
