package com.simnectz.soiplus.core.system.utils;

import com.simnectz.soiplus.core.system.constant.SystemConstant;
import com.simnectz.soiplus.core.system.domain.bo.Tree;

import java.util.*;

public class TreeUtils {

    public static  <T extends Tree<T>> List<T> listToTree(Collection<T> source) {
        if (source.size() < 2) {
            return new ArrayList<>(source);
        }
        List<T> result = new ArrayList<>();
        Map<Object, T> sourceMap = new LinkedHashMap<>();
        for (T t : source) {
            if (t != null) {
                Object nodeId = t.getId();
                sourceMap.put(nodeId, t);
            }
        }
        for (T t : source) {
            String parentId = t.getParentId();
            T node = null;
            if (!parentId.equals(SystemConstant.ROOT_PERMISSION_ID)) {
                node = sourceMap.get(parentId);
            }
            if (node == null) {
                result.add(t);
                result.sort(Comparator.comparingInt(Tree::getSortNumber));
            } else {
                List<T> children = node.getChildren();
                children.add(t);
                children.sort(Comparator.comparingInt(Tree::getSortNumber));
            }
        }
        return result;
    }

}
