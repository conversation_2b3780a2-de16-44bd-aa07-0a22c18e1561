package com.simnectz.soiplus.core.system.domain.vo;

import com.simnectz.soiplus.core.system.constant.ResponseConstant;
import com.simnectz.soiplus.core.system.utils.MessageUtils;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * Common response entity
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class Response<T> implements Serializable {

    private static final long serialVersionUID = -7951756605318869047L;

    private Integer code;
    private String message;
    private T data;
    private Long timestamp;

    public static <T> Response<T> success() {
        return success(MessageUtils.message(ResponseConstant.Message.OPERATION_SUCCESS));
    }

    public static <T> Response<T> success(String message) {
        return instance(
                ResponseConstant.Code.OPERATION_SUCCESS,
                message,
                null
        );
    }

    public static <T> Response<T> success(T data) {
        return instance(
                ResponseConstant.Code.OPERATION_SUCCESS,
                MessageUtils.message(ResponseConstant.Message.OPERATION_SUCCESS),
                data
        );
    }
    public static <T> Response<T> success(String message, T data) {
        return instance(
                ResponseConstant.Code.OPERATION_SUCCESS,
                message,
                data
        );
    }

    public static <T> Response<T> failed(Integer code, String message) {
        return instance(code, message, null);
    }

    private static <T> Response<T> instance(Integer code, String message, T data) {
        return new Response<>(code, message, data, System.currentTimeMillis());
    }

}
