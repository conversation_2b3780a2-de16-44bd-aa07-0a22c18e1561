package com.simnectz.soiplus.core.users.service;


import com.mybatisflex.core.service.IService;
import com.simnectz.soiplus.core.system.domain.vo.Paginate;
import com.simnectz.soiplus.core.system.domain.vo.Response;
import com.simnectz.soiplus.core.users.domian.dto.CreateUserDto;
import com.simnectz.soiplus.core.users.domian.dto.ImportUsersDto;
import com.simnectz.soiplus.core.users.domian.dto.UpdateBlacklistDto;
import com.simnectz.soiplus.core.users.domian.dto.UpdateUserDto;
import com.simnectz.soiplus.core.users.domian.entity.UserEntity;
import com.simnectz.soiplus.core.users.domian.vo.MeVo;
import com.simnectz.soiplus.core.users.domian.vo.UserDetailsVo;
import com.simnectz.soiplus.core.users.domian.vo.UserVo;

import java.util.Date;
import java.util.Set;

/**
 * User service
 */
public interface UserService extends IService<UserEntity> {

    /**
     * Validation email is unique
     *
     * @param userId User id
     * @param email  Email
     * @return true: repeat, false: not repeat
     */
    Response<Boolean> validationEmailUnique(String userId, String email);

    /**
     * Create user
     *
     * @param createUserDto User information
     */
    Response<?> createUser(CreateUserDto createUserDto);

    /**
     * Enquiry the current logged-in user information
     *
     * @return User information
     */
    Response<MeVo> enquiryLoginUserInfo();

    /**
     * Enquiry user details
     *
     * @param userId User id
     * @return User details
     */
    Response<UserDetailsVo> enquiryUserDetails(String userId);

    /**
     * Batch delete user(s)
     *
     * @param userIds User ids
     */
    Response<?> batchDeleteUsers(Set<String> userIds);

    /**
     * Paginate enquiry users
     *
     * @param keywords    Email or username keywords
     * @param roleIds     Roles ids
     * @param status      Active status
     * @param blackList   Blacklist
     * @param currentPage Current page
     * @param pageSize    Paginate size
     * @param orderBy     Order field
     * @param isAsc       true: ASC, false: DESC
     * @param startTime   Timestamp of the end time
     * @param endTime     Timestamp of the start time
     * @return List of eligible users
     */
    Response<Paginate<UserVo>> paginateEnquiryUsers(
            String keywords,
            Set<String> roleIds,
            Integer status,
            Integer blackList,
            Integer currentPage,
            Integer pageSize,
            String orderBy,
            Boolean isAsc,
            Date startTime,
            Date endTime
    );

    /**
     * Update user
     *
     * @param updateUserDto User information
     */
    Response<?> updateUser(UpdateUserDto updateUserDto);

    /**
     * Update blacklist status
     * @param updateBlacklistDto blacklist status
     */
    Response<?> updateBlacklist(UpdateBlacklistDto updateBlacklistDto);

    /**
     * Batch import users
     * @param importUsersDto org id & users information file
     */
    Response<?> batchImportUsers(ImportUsersDto importUsersDto);

}
