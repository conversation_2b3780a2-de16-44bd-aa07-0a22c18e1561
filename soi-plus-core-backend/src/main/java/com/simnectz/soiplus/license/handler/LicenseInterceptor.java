package com.simnectz.soiplus.license.handler;

import com.alibaba.fastjson2.JSON;
import com.simnectz.soiplus.core.system.domain.vo.Response;
import com.simnectz.soiplus.core.system.utils.ServletUtils;
import com.simnectz.soiplus.license.constant.LicenseConstant;
import com.simnectz.soiplus.license.verify.LicenseVerify;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

@Component
public class LicenseInterceptor implements HandlerInterceptor {
	@Override
	public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
		boolean verify = LicenseVerify.verify();
		if (verify) {
			return true;
		}
		String result = JSON.toJSONString(Response.failed(LicenseConstant.Code.INVALID_SERVER_AUTHORIZATION, LicenseConstant.Message.INVALID_SERVER_HARDWARE_INFORMATION));
		ServletUtils.renderString(response, result);
		return false;
	}
}
