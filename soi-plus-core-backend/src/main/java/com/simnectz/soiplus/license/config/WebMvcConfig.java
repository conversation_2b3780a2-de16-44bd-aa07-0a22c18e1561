package com.simnectz.soiplus.license.config;

import com.simnectz.soiplus.license.handler.LicenseInterceptor;
import com.simnectz.soiplus.license.properties.LicenseProperties;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurationSupport;

@Configuration
@RequiredArgsConstructor
public class WebMvcConfig extends WebMvcConfigurationSupport {
	private final LicenseInterceptor licenseInterceptor;
	private final LicenseProperties licenseProperties;

	@Override
	protected void addInterceptors(InterceptorRegistry registry) {
		if (licenseProperties.isEnabled()) {
			registry.addInterceptor(licenseInterceptor)
					.addPathPatterns("/**");
		}
	}
}
