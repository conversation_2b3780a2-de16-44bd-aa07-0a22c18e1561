package com.simnectz.soiplus.license.licenser;

import com.simnectz.soiplus.license.verify.LicenseVerify;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationListener;
import org.springframework.context.event.ContextRefreshedEvent;
import org.springframework.stereotype.Component;

@Component
@ConditionalOnProperty(prefix = "license", name = "enabled", havingValue = "true")
public class LicenseCheckListener implements ApplicationListener<ContextRefreshedEvent> {
	@Override
	public void onApplicationEvent(ContextRefreshedEvent event) {
		ApplicationContext context = event.getApplicationContext().getParent();
		if (context == null) {
			LicenseVerify.verify();
		}
	}
}
