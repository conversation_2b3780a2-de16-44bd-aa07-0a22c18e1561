package com.simnectz.authorize.system.domain.bo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.springframework.security.core.GrantedAuthority;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class Authority implements Serializable, GrantedAuthority {

    private static final long serialVersionUID = 2657280046725117881L;

    private String authority;

}
