OPERATION_SUCCESS=Operation Successfully
UNAUTHORIZED=Unauthorized
FORBIDDEN=Forbidden
METHOD_NOT_ALLOWED=Request method '{method}' not supported
SERVER_ERROR=Server Error
DEFAULT_ROLE_NOT_FOUND=Default role not found
EMAIL_ALREADY_REGISTER=This email has already been registered
TOKEN_REFRESH_FAILED=Token refresh failed
USER_NOT_FOUND=User not found
ROLE_NOT_FOUND=Role not found
INVALID_ROLE_INFORMATION=Invalid role information
ROLE_NAME_ALREADY_EXISTS=The role name already exists
UNABLE_DELETE_ROLE=Delete failed, the following roles are already in use: {roleNames}
PASSWORD_IS_REQUIRED=Password is required
CAPTCHA_HAS_EXPIRED=Captcha has expired
CAPTCHA_ERROR=Captcha error
CAPTCHA_KEY_IS_REQUIRED=Captcha key is required
CAPTCHA_IS_REQUIRED=Captcha is required
USERNAME_IS_REQUIRED=Username is required
USER_NOT_ACTIVATED=Your registered email has not been activated. Please resend the email to activate it
USER_ROLE_IS_REQUIRED=User role(s) is required
EMAIL_IS_REQUIRED=Email is required
NICKNAME_IS_REQUIRED=Nickname is required
CONFIRM_PASSWORD_IS_REQUIRED=Confirm password is required
INVALID_EMAIL_FORMAT=Invalid email format
INVALID_NICKNAME_FORMAT=Invalid nickname format
INVALID_USERNAME_FORMAT=Invalid username format
INVALID_PASSWORD_FORMAT=Passwords must contain at least uppercase letters, lowercase letters, and numbers
INVALID_EMAIL_LENGTH=Invalid email length, maximum: {max}
INVALID_NICKNAME_LENGTH=Nickname must have between {min} and {max} characters
INVALID_PASSWORD_LENGTH=Password must have between {min} and {max} characters
PASSWORD_INCONSISTENCY=Entered passwords differ
INVALID_GENDER=Invalid gender
USER_ID_IS_REQUIRED=User id is required
ACCESS_TOKEN_IS_REQUIRED=Access token is required
REFRESH_TOKEN_IS_REQUIRED=Refresh token is required
INVALID_REMARK_LENGTH=Invalid remark length, maximum: {max}
ROLE_NAME_IS_REQUIRED=Role name is required
INVALID_ROLE_NAME_LENGTH=Invalid role name length, maximum: {max}
ROLE_ID_IS_REQUIRED=Role id is required
UNSUPPORTED_CARD_TYPE=Unsupported card type
INVALID_CARD_ID_FORMAT = Invalid card number format
INVALID_PHONE_NUMBER_FORMAT = Invalid phone number format
USER_ACTIVATION_FAILED=User activation failed
GET_CAPTCHA_FAILED=Get captcha failed
SEND_EMAIL_FAILED=Send email failed
USER_HAS_BEEN_BLACKLISTED=Operation failed! You have been blacklisted
USER_ACTIVATED=User activated
PHONE_NUMBER_IS_REQUIRED=Phone number is required
CARD_TYPE_IS_REQUIRED=Card type is required
CARD_ID_IS_REQUIRED=Card number is required
NEW_PASSWORD_IS_REQUIRED=New password is required
RESET_PASSWORD_FAILED=Reset password failed
BLACKLIST_STATUS_IS_REQUIRED=Blacklist status is required
INVALID_BLACKLIST_STATUS=Invalid blacklist status
ROLE_IS_REQUIRED=Role is required
PERMISSION_IS_REQUIRED=Permission is required
INVALID_PERMISSION_INFORMATION=Invalid permission information
PERMISSION_ID_IS_REQUIRED=Permission id is required
PARENT_ID_IS_REQUIRED=Parent node is required
PERMISSION_NAME_IS_REQUIRED=Permission name is required
INVALID_PERMISSION_NAME_LENGTH=Invalid permission name length, maximum: {max}
ACCREDIT_IS_REQUIRED=Accredit is required
INVALID_ACCREDIT_LENGTH=Invalid accredit length, maximum: {max}
SORT_NUMBER_IS_REQUIRED=Sort number is required
INVALID_SORT_NUMBER_SIZE=Invalid sort number, sort number can only be in {min} between {max}
PERMISSION_NOT_FOUND=Permission not found
PERMISSION_NAME_ALREADY_EXISTS=The permission name already exists
UNABLE_DELETE_PERMISSION=Delete failed, the following permissions are already in use: {permissionNames}
ORGANIZATION_NOT_FOUND=Organization not found
ORGANIZATION_NAME_ALREADY_EXISTS=The organization name already exists
UNABLE_DELETE_ORGANIZATION=Delete failed, there are users under the following organizations: {organizationNames}
ORGANIZATION_NAME_IS_REQUIRED=Organization name is required
INVALID_ORGANIZATION_NAME_LENGTH=Invalid organization name length, maximum: {max}
ORGANIZATION_ABBREVIATION_CODE_IS_REQUIRED=Organization abbreviation code is required
INVALID_ORGANIZATION_ABBREVIATION_CODE_LENGTH=Invalid organization abbreviation code length, maximum: {max}
CONTACT_PERSON_IS_REQUIRED=Organization contact person is required
INVALID_CONTACT_PERSON_LENGTH=Organization contact person must have between {min} and {max} characters
INVALID_CONTACT_PERSON_FORMAT=Invalid contact person format
CONTACT_PHONE_NUMBER_IS_REQUIRED=Organization contact phone number is required
CONTACT_EMAIL_IS_REQUIRED=Organization contact email is required
USERNAME_NICKNAME_LENGTH=Username must have between {min} and {max} characters
IMPORT_USER_FAILED=Users import failed
EMAIL_DUPLICATED=Users import failed, email duplicated: {emails}
INVALID_USERNAMES=Invalid username: {usernames}
INVALID_EMAILS=Invalid email: {emails}
INVALID_CARD_IDS=Invalid card id: {cardIds}
INVALID_PHONE_NUMBERS=Invalid phone number: {phoneNumbers}
INVALID_SEX=Invalid sex: {sex}
INVALID_CARD_TYPE=Invalid card type: {cardType}
INVALID_USER_LEVEL=Invalid user level: {userLevel}
LEVEL_IS_REQUIRED=Level column is required
PARAM_IS_NULL=Have parameters are null
MAIL_SUFFIX_NOT_EMPTY=Please provide at least one mail suffix
NAME_ALREADY_EXISTS=The name already existed
PASSWORD_UPDATE_FAILED=The password can be updated in a maximum of 9 months
INVALID_ACCOUNT_RETENTION_PERIOD=The account retention period is 99 months at most
TC_ID_IS_REQUIRED=T&C id is required
TC_CONTENT_IS_REQUIRED=T&C content is required
TC_NOT_FOUND=T&C not found
# License
INVALID_SERVER_HARDWARE_INFORMATION=Abnormal acquisition of server hardware information
INVALID_AUTHORIZATION_TIME=Abnormal authorization time
INVALID_CURRENT_TIME=Current time is not within the authorized range
INVALID_CURRENT_IP=The current server IP is not within the authorized range
INVALID_CURRENT_MAC_ADDRESS=The current server Mac address is not within the authorized range
INVALID_CURRENT_MOTHERBOARD_SERIAL_NUMBER=The current server motherboard serial number is not within the authorized range
INVALID_CURRENT_CPU_SERIAL_NUMBER=The current server CPU serial number is not within the authorized range
