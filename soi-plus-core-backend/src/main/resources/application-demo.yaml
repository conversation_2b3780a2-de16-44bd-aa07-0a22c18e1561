server:
  port: 8200
#  ssl:
#    key-store: classpath:ssl/uat/demo.simnectzplatform.com.pfx
#    key-store-password: hfcdogxh
#    keyStoreType: PKCS12

spring:
  redis:
    password:
    database: 0
    port: 6379
    host: 127.0.0.1
  mail:
    host: smtp.exmail.qq.com
    protocol: smtp
    port: 465
    username: <EMAIL>
    password: Guangzhou2019
    properties:
      mail:
        smtp:
          socketFactory:
            port: 465
          ssl:
            enable: true
            socketFactory:
              fallback: false
              class: com.sun.mail.util.MailSSLSocketFactory
          auth: true
          starttls:
            enable: true
            required: true
  freemarker:
    cache: true

mybatis-flex:
  type-aliases-package: com.simnectz.authorize.**.entity
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  datasource:
    default:
      url: jdbc:mysql://**************:3306/soi_plus_platform?characterEncoding=utf8&serverTimezone=UTC
      username: chinasoft
      password: Aa123456

system:
  backend-url: https://simnectzplatform.com/soi-plus-core
  frontend-url: https://simnectzplatform.com
  authorize:
    header: 'Authorization'
    prefix: 'Bearer '
    access-token-secret-key: a83f0e5e-55f5-c3f2-2bf4-c9eb442d2cec
    access-token-expire-time: 1440 # 1 day
    refresh-token-secret-key: 87a2e6c6-a656-9180-8799-73a133fa4609
    refresh-token-expire-time: 10080 # 7 days
    password-encode-secret-key: 8b70e850-7420-58b1-7a4d-ec1399a992ad
    auto-login-secret-key: DQuvQXoRihnZlLruayD8Gg==
    captcha-expire-time: 5
  email:
    from: ${spring.mail.username}
  template-path: templates
