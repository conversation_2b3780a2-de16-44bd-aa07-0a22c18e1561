server:
  port: 8200

spring:
  redis:
    password: root
    database: 0
    port: 6379
    host: localhost
  mail:
    host: smtp.exmail.qq.com
    protocol: smtp
    port: 465
    username: <EMAIL>
    password: Guangzhou2019
    properties:
      mail:
        smtp:
          socketFactory:
            port: 465
          ssl:
            enable: true
            socketFactory:
              fallback: false
              class: com.sun.mail.util.MailSSLSocketFactory
          auth: true
          starttls:
            enable: true
            required: true
  freemarker:
    cache: true

mybatis-flex:
  type-aliases-package: com.simnectz.authorize.**.entity
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  datasource:
    default:
      url: jdbc:mysql://**************:3306/soi_plus_platform?characterEncoding=utf8&serverTimezone=UTC
      username: root
      password: SimnectzRDS@2020

system:
  backend-url: http://127.0.0.1:8200
  frontend-url: http://127.0.0.1:8848
  authorize:
    header: 'Authorization'
    prefix: 'Bearer '
    access-token-secret-key: a83f0e5e-55f5-c3f2-2bf4-c9eb442d2cec
    access-token-expire-time: 1440 # 1 day
    refresh-token-secret-key: 87a2e6c6-a656-9180-8799-73a133fa4609
    refresh-token-expire-time: 10080 # 7 days
    password-encode-secret-key: 8b70e850-7420-58b1-7a4d-ec1399a992ad
    auto-login-secret-key: DQuvQXoRihnZlLruayD8Gg==
    captcha-expire-time: 5
  email:
    from: ${spring.mail.username}
  template-path: templates
  login-log-enable: false
license:
  enabled: false # Whether to enable license verification
  issued-time: 2024-01-01 00:00:00 # Authorization start time(yyyy-MM-dd HH:mm:ss)
  expiry-time: 2024-05-27 17:30:00 # Authorization end time(yyyy-MM-dd HH:mm:ss)
  ip-address: # Authorization server ip address list (Leave it blank to not verify IP)
  mac-address: # Authorization server mac address list
    - fa:16:3e:3a:fd:46
    - 02:42:78:2e:24:07
    - 1a:b4:a2:e9:dc:7a
    - 02:4e:b0:4e:7f:52
  cpu-serial: 54 06 05 00 FF FB 8B 0F # Authorization server cpu serial
  main-board-serial: 2cba6298-d1c0-4ff8-872f-372720d5f404 # Authorization server main board serial
  rule: lenient # strict or lenient
