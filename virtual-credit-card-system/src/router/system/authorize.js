import AuthorizeLayout from '@/layouts/authorize/index.vue'

export default [
  {
    path: '/authorize',
    component: AuthorizeLayout,
    hidden: true,
    children: [
      {
        path: 'login',
        name: 'authorize-login',
        meta: { title: 'login' },
        component: () => import('@/views/authorize/login/index')
      }
    ]
  },

  {
    path: '/authorize/apply',
    component: AuthorizeLayout,
    hidden: true,
    children: [
      {
        path: '/',
        name: 'authorize-apply',
        meta: { title: 'creditCardApply' },
        component: () => import('@/views/authorize/apply/index')
      }
    ]
  }
]
