<template>
  <div v-loading="loading" class="business-container">
    <el-card>
      <!-- 搜索表单 -->
      <el-form
        ref="searchForm"
        :model="searchForm"
        size="small"
        label-width="220px"
        label-position="left"
        :show-message="false"
      >
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item :label="$t('vcs.creditCardTransactionDetails.transactionDate')" prop="dateRange">
              <el-date-picker
                v-model="searchForm.dateRange"
                type="daterange"
                :range-separator="$t('vcs.common.to')"
                value-format="timestamp"
                :start-placeholder="$t('vcs.common.startDate')"
                :end-placeholder="$t('vcs.common.endDate')"
                :default-time="['00:00:00', '23:59:59']"
                unlink-panels
                class="search-form-item"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <!-- 操作按钮 -->
            <el-button type="primary" size="small" style="margin-bottom: 18px" icon="el-icon-search" @click="getItems">
              {{ $t('vcs.common.search') }}
            </el-button>
            <el-button icon="el-icon-refresh" size="small" @click="resetForm('searchForm')">
              {{ $t('vcs.common.reset') }}
            </el-button>
          </el-col>
        </el-row>

      </el-form>

      <!-- 创建和批量删除按钮 -->
      <div class="data-action-button-group">
        <div />

        <!-- 刷新按钮 -->
        <el-button icon="el-icon-refresh" size="small" circle @click="getItems" />
      </div>

      <!-- 表格 -->
      <el-card shadow="never">
        <el-table v-loading="tableLoading" :data="items" stripe>
          <el-table-column type="index" label="#" align="center" />
          <el-table-column
            :label="$t('vcs.creditCardTransactionDetails.creditCardNumber')"
            prop="creditCardNumber"
            align="center"
            width="160"
          />
          <el-table-column
            :label="$t('vcs.creditCardTransactionDetails.dealNumber')"
            prop="dealNumber"
            align="center"
            width="160"
          />
          <el-table-column
            :label="$t('vcs.creditCardTransactionDetails.transactionDate')"
            prop="transactionDate"
            align="center"
            width="150"
          >
            <template slot-scope="scope">
              {{ moment(Number(scope.row.transactionDate)).format('YYYY-MM-DD HH:mm:ss') }}
            </template>
          </el-table-column>
          <el-table-column
            :label="$t('vcs.creditCardTransactionDetails.merchantNumber')"
            prop="merchantNumber"
            align="center"
            width="150"
          />
          <el-table-column
            :label="$t('vcs.creditCardTransactionDetails.transactionAmount')"
            prop="transactionAmount"
            align="center"
            width="160"
          />
          <el-table-column
            :label="$t('vcs.creditCardTransactionDetails.transactionCcy')"
            prop="transactionCcy"
            align="center"
            width="150"
          />
          <el-table-column
            :label="$t('vcs.creditCardTransactionDetails.bookingAmount')"
            prop="bookingAmount"
            align="center"
            width="150"
          />
          <el-table-column
            :label="$t('vcs.creditCardTransactionDetails.bookingCcy')"
            prop="bookingCcy"
            align="center"
            width="150"
          />
          <el-table-column
            :label="$t('vcs.creditCardTransactionDetails.authorizationNumber')"
            prop="authorizationNumber"
            align="center"
            width="170"
          />
          <el-table-column
            :label="$t('vcs.creditCardTransactionDetails.transactionType')"
            prop="transactionType"
            align="center"
            width="150"
          >
            <template slot-scope="scope">
              {{ $t(getLabel("TRANSACTION_TYPE", scope.row.transactionType)) }}
            </template>
          </el-table-column>
          <el-table-column :label="$t('vcs.common.operate')" align="center" width="200" fixed="right">
            <template slot-scope="{ row }">
              <el-button
                type="text"
                size="mini"
                text
                icon="el-icon-view"
                @click="viewDialog(row)"
              >
                {{ $t('vcs.common.view') }}
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>

      <div class="table-footer">
        <el-pagination
          :current-page.sync="searchForm.currentPage"
          :page-sizes="[20, 50, 100, 500, 1000]"
          :page-size.sync="searchForm.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="getItems()"
          @current-change="getItems()"
        />
      </div>
    </el-card>
    <!-- 查看的对话框 -->
    <el-dialog
      :title="$t('vcs.creditCardTransactionDetails.viewDetails')"
      :visible.sync="viewDialogVisible"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      width="60%"
      top="50px"
    >
      <descriptions :view-row="viewRow" />
    </el-dialog>
  </div>
</template>

<script>
import moment from 'moment'
import { getLabel } from '@/data'
import Descriptions from '@/components/Descriptions/Descriptions.vue'
import { getCreditCardTransactionDetailsPage } from '@/api/business/credit-card'
import { formConfig } from '@/views/system/creditCardTransactionDetails/form-config'

export default {
  name: 'CreditCardTransactionDetails',
  components: { Descriptions },
  props: {
    customerNumber: {
      type: String,
      require: true,
      default: ''
    }
  },
  data() {
    return {
      moment,
      getLabel,
      loading: false,
      tableLoading: false,
      viewDialogVisible: false,
      formConfig: formConfig,
      searchForm: {
        currentPage: 1,
        pageSize: 20,
        dateRange: [],
        creditCardNumber: this.customerNumber
      },
      viewRow: [],
      items: [],
      total: 0
    }
  },
  mounted() {
    this.getItems()
  },
  methods: {
    getItems() {
      this.tableLoading = true

      const requestData = { ...this.searchForm }

      if (requestData.dateRange) {
        requestData.createDateFrom = this.searchForm.dateRange[0]
        requestData.createDateTo = this.searchForm.dateRange[1]
      } delete requestData.dateRange
      getCreditCardTransactionDetailsPage(requestData)
        .then((res) => {
          const { total, items } = res.data

          this.items = items
          this.total = total
        })
        .finally(() => {
          this.tableLoading = false
        })
    },
    viewDialog(row) {
      const description = { ...row }
      this.viewRow = this.formConfig
        .map(item => {
          let value = description[item.prop]
          if (item.format) {
            return { key: this.$t(item.label), value: moment(Number(value)).format(item.format.replace('dd', 'DD').replace('yyyy', 'YYYY')) }
          } else {
            if (item.options) {
              const option = item.options.filter(option => option.value === value)
              if (option && option.length) {
                value = this.$t(option[0].label)
              }
            }
            return { key: this.$t(item.label), value: value }
          }
        })
      this.viewDialogVisible = true
    },
    resetForm(formName) {
      this.$refs[formName].resetFields()
    }
  }
}
</script>
