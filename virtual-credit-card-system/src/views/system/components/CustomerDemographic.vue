<template>
  <el-descriptions class="descriptions" :column="3" border>
    <el-descriptions-item>
      <template slot="label">{{ $t('vcs.creditCardApplication.firstName') }}</template>
      {{ accountOpenApplication.firstName }}
    </el-descriptions-item>
    <el-descriptions-item>
      <template slot="label">{{ $t('vcs.creditCardApplication.lastName') }}</template>
      {{ accountOpenApplication.lastName }}
    </el-descriptions-item>
    <el-descriptions-item>
      <template slot="label">{{ $t('vcs.creditCardApplication.chineseName') }}</template>
      {{ accountOpenApplication.chineseName }}
    </el-descriptions-item>
    <el-descriptions-item>
      <template slot="label">{{ $t('vcs.creditCardApplication.gender') }}</template>
      {{ $t(getLabel("GENDER", accountOpenApplication.gender)) }}
    </el-descriptions-item>
    <el-descriptions-item>
      <template slot="label">{{ $t('vcs.creditCardApplication.customerId') }}</template>
      {{ accountOpenApplication.customerId }}
    </el-descriptions-item>
    <el-descriptions-item>
      <template slot="label">{{ $t('vcs.creditCardApplication.customerIdType') }}</template>
      {{ $t(getLabel("CUSTOMER_ID_TYPE", accountOpenApplication.customerIdType)) }}
    </el-descriptions-item>
    <el-descriptions-item>
      <template slot="label">{{ $t('vcs.creditCardApplication.issueCountry') }}</template>
      {{ $t(getLabel("COUNTRY", accountOpenApplication.issueCountry)) }}
    </el-descriptions-item>
    <el-descriptions-item>
      <template slot="label">{{ $t('vcs.creditCardApplication.nationality') }}</template>
      {{ $t(getLabel("COUNTRY", accountOpenApplication.nationality)) }}
    </el-descriptions-item>
    <el-descriptions-item>
      <template slot="label">{{ $t('vcs.creditCardApplication.dateOfBirth') }}</template>
      {{ moment(Number(accountOpenApplication.dateOfBirth)).format('YYYY-MM-DD') }}
    </el-descriptions-item>
    <el-descriptions-item :span="2">
      <template slot="label">{{ $t('vcs.creditCardApplication.address') }}</template>
      {{ accountOpenApplication.address }}
    </el-descriptions-item>
    <el-descriptions-item>
      <template slot="label">{{ $t('vcs.creditCardApplication.phoneNumber') }}</template>
      {{ accountOpenApplication.phoneNumber }}
    </el-descriptions-item>
    <el-descriptions-item>
      <template slot="label">{{ $t('vcs.creditCardApplication.permanentResidentStatus') }}</template>
      {{ $t(getLabel("PERMANENT_RESIDENT_STATUS", accountOpenApplication.permanentResidentStatus)) }}
    </el-descriptions-item>
    <el-descriptions-item>
      <template slot="label">{{ $t('vcs.creditCardApplication.maritalStatus') }}</template>
      {{ $t(getLabel("MARITAL_STATUS", accountOpenApplication.maritalStatus)) }}
    </el-descriptions-item>
    <!--    <el-descriptions-item>-->
    <!--      <template slot="label">{{ $t('vcs.creditCardApplication.yearsOfResidence') }}</template>-->
    <!--      {{ accountOpenApplication.yearsOfResidence }}-->
    <!--    </el-descriptions-item>-->
    <el-descriptions-item>
      <template slot="label">{{ $t('vcs.creditCardApplication.familyIncome') }}</template>
      {{ accountOpenApplication.familyIncome }}
    </el-descriptions-item>
    <el-descriptions-item>
      <template slot="label">{{ $t('vcs.creditCardApplication.industry') }}</template>
      {{ accountOpenApplication.industry }}
    </el-descriptions-item>
    <el-descriptions-item>
      <template slot="label">{{ $t('vcs.creditCardApplication.position') }}</template>
      {{ accountOpenApplication.position }}
    </el-descriptions-item>
    <el-descriptions-item>
      <template slot="label">{{ $t('vcs.creditCardApplication.personalIncome') }}</template>
      {{ accountOpenApplication.personalIncome }}
    </el-descriptions-item>
    <el-descriptions-item>
      <template slot="label">{{ $t('vcs.creditCardApplication.companyName') }}</template>
      {{ accountOpenApplication.companyName }}
    </el-descriptions-item>
    <el-descriptions-item>
      <template slot="label">{{ $t('vcs.creditCardApplication.noOfCreditCardsCarry') }}</template>
      {{ accountOpenApplication.noOfCreditCardsCarry }}
    </el-descriptions-item>
    <el-descriptions-item v-for="(item, index) in cardHoldingList" :key="index">
      <template slot="label">{{ $t(item.label, {index: item.index}) }}</template>
      {{ item.value }}
    </el-descriptions-item>
    <el-descriptions-item>
      <template slot="label">{{ $t('vcs.creditCardApplication.noOfYearsHaveFirstCreditCard') }}</template>
      {{ accountOpenApplication.noOfYearsHaveFirstCreditCard }}
    </el-descriptions-item>
    <el-descriptions-item>
      <template slot="label">{{ $t('vcs.creditCardApplication.pastDueDebts') }}</template>
      {{ accountOpenApplication.pastDueDebts }}
    </el-descriptions-item>
    <el-descriptions-item>
      <template slot="label">{{ $t('vcs.creditCardApplication.repaymentRatioToIncome') }}</template>
      {{ accountOpenApplication.repaymentRatioToIncome }}
    </el-descriptions-item>
    <el-descriptions-item>
      <template slot="label">{{ $t('vcs.creditCardApplication.bankruptcyHistory') }}</template>
      {{ $t(getLabel("BANKRUPTCY_HISTORY", accountOpenApplication.bankruptcyHistory)) }}
    </el-descriptions-item>
    <el-descriptions-item>
      <template slot="label">{{ $t('vcs.creditCardApplication.outstandingLoansAmount') }}</template>
      {{ accountOpenApplication.outstandingLoansAmount }}
    </el-descriptions-item>
    <el-descriptions-item>
      <template slot="label">{{ $t('vcs.creditCardApplication.customerIdPicture') }}</template>
      <el-image
        style="width: 100px; height: 100px"
        :src="accountOpenApplication.customerIdPicturePath"
        :preview-src-list="[accountOpenApplication.customerIdPicturePath]"
      />
    </el-descriptions-item>
    <el-descriptions-item>
      <template slot="label">{{ $t('vcs.creditCardApplication.addressProofPicture') }}</template>
      <el-image
        style="width: 100px; height: 100px"
        :src="accountOpenApplication.addressProofPicturePath"
        :preview-src-list="[accountOpenApplication.addressProofPicturePath]"
      />
    </el-descriptions-item>
  </el-descriptions>
</template>
<script>
import { getLabel } from '@/data'
import moment from 'moment'

export default {
  name: 'CustomerDemographic',
  props: {
    accountOpenApplication: {
      type: Object,
      require: true,
      default: () => {
      }
    }
  },
  data() {
    return {
      moment,
      getLabel
    }
  },
  computed: {
    cardHoldingList() {
      const cardHoldingList = []
      if (this.accountOpenApplication.cardHoldingList) {
        this.accountOpenApplication.cardHoldingList.forEach((cardHolding, index) => {
          cardHoldingList.push({
            label: `vcs.authorize.creditCardNumber`,
            index: index + 1,
            value: cardHolding.creditCardNumber
          })
          cardHoldingList.push({
            label: `vcs.authorize.limitIndex`,
            index: index + 1,
            value: cardHolding.creditCardLimit
          })
          cardHoldingList.push({
            label: `vcs.authorize.expiryDate`,
            index: index + 1,
            value: moment(Number(cardHolding.creditCardExpiryDate)).format('YYYY-MM-DD')
          })
        })
      }
      return cardHoldingList
    }
  }
}

</script>
