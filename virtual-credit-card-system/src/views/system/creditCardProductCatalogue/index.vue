<template>
  <div v-loading="loading" class="business-container">
    <customize-card :title="$t('vcs.router.catalogue')" :show-back-btn="false">
      <!-- 搜索表单 -->
      <el-form
        ref="searchForm"
        :model="searchForm"
        size="small"
        label-width="130px"
        label-position="left"
        :show-message="false"
      >
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item :label="$t('vcs.creditCardProductCatalogue.catalogueCode')" prop="catalogueCode">
              <el-input
                v-model="searchForm.catalogueCode"
                :placeholder="$t('vcs.creditCardProductCatalogue.catalogueCodePlaceholder')"
                clearable
                class="search-form-item"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="$t('vcs.creditCardProductCatalogue.catalogueName')" prop="catalogueName">
              <el-input
                v-model="searchForm.catalogueName"
                :placeholder="$t('vcs.creditCardProductCatalogue.catalogueNamePlaceholder')"
                clearable
                class="search-form-item"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="$t('vcs.common.createDate')" prop="dateRange">
              <el-date-picker
                v-model="searchForm.dateRange"
                type="daterange"
                :range-separator="$t('vcs.common.to')"
                value-format="timestamp"
                :start-placeholder="$t('vcs.common.startDate')"
                :end-placeholder="$t('vcs.common.endDate')"
                :default-time="['00:00:00', '23:59:59']"
                unlink-panels
                class="search-form-item"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <!-- 操作按钮 -->
            <el-button type="primary" size="small" style="margin-bottom: 18px" icon="el-icon-search" @click="getItems">
              {{ $t('vcs.common.search') }}
            </el-button>
            <el-button icon="el-icon-refresh" size="small" @click="resetForm('searchForm')">{{
              $t('vcs.common.reset')
            }}
            </el-button>
          </el-col>
        </el-row>

      </el-form>

      <!-- 创建和批量删除按钮 -->
      <div class="data-action-button-group">
        <div>
          <el-button size="small" icon="el-icon-plus" type="primary" plain @click="openCreateDialog">
            {{ $t('vcs.common.create') }}
          </el-button>
          <el-button size="small" icon="el-icon-delete" type="danger" plain @click="deleteItem">
            {{ $t('vcs.common.delete') }}
          </el-button>
        </div>

        <!-- 刷新按钮 -->
        <el-button icon="el-icon-refresh" size="small" circle @click="getItems" />
      </div>

      <!-- 表格 -->
      <el-card shadow="never">
        <el-table v-loading="tableLoading" :data="items" stripe @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="55" />
          <el-table-column type="index" label="#" align="center" />
          <el-table-column
            :label="$t('vcs.creditCardProductCatalogue.catalogueCode')"
            prop="catalogueCode"
            align="center"
          />
          <el-table-column
            :label="$t('vcs.creditCardProductCatalogue.catalogueName')"
            prop="catalogueName"
            align="center"
          />
          <el-table-column
            :label="$t('vcs.creditCardProductCatalogue.createDate')"
            prop="createDate"
            align="center"
          >
            <template slot-scope="{ row }">
              {{ moment(row.createDate).format('YYYY-MM-DD HH:mm:ss') }}
            </template>
          </el-table-column>
          <el-table-column
            :label="$t('vcs.creditCardProductCatalogue.remarks')"
            prop="remarks"
            align="center"
          />
          <el-table-column :label="$t('vcs.common.operate')" align="center" width="200" fixed="right">
            <template slot-scope="{ row }">
              <el-button
                type="text"
                size="mini"
                text
                icon="el-icon-view"
                @click="viewDialog(row)"
              >
                {{ $t('vcs.common.view') }}
              </el-button>
              <el-button
                type="text"
                size="mini"
                text
                icon="el-icon-edit"
                @click="openUpdateDialog(row)"
              >
                {{ $t('vcs.common.edit') }}
              </el-button>
              <el-button
                type="text"
                size="mini"
                text
                icon="el-icon-delete"
                @click="deleteItem([row.id])"
              >
                {{ $t('vcs.common.delete') }}
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>

      <div class="table-footer">
        <el-pagination
          :current-page.sync="searchForm.currentPage"
          :page-sizes="[20, 50, 100, 500, 1000]"
          :page-size.sync="searchForm.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="getItems()"
          @current-change="getItems()"
        />
      </div>
    </customize-card>
    <!-- 查看的对话框 -->
    <el-dialog
      :title="$t('vcs.creditCardProductCatalogue.viewDetails')"
      :visible.sync="viewDialogVisible"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      width="60%"
      top="50px"
    >
      <descriptions :view-row="viewRow" />
    </el-dialog>

    <el-dialog
      :title="isUpdate ? $t('vcs.creditCardProductCatalogue.editDetails') : $t('vcs.creditCardProductCatalogue.createDetails')"
      :visible.sync="dataFormDialogVisible"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      width="60%"
      top="10vh"
      destroy-on-close
    >
      <customize-form
        ref="customizeForm"
        form-ref="dataForm"
        size="small"
        :model="formData"
        :form-item-config="formConfig"
        label-width="220px"
        label-position="left"
        :is-update="isUpdate"
      />

      <span slot="footer" class="dialog-footer">
        <el-button size="small" @click="dataFormDialogVisible = false"> {{ $t('vcs.common.cancel') }}</el-button>
        <el-button
          v-if="!isUpdate"
          type="primary"
          size="small"
          :loading="confirmButtonLoading"
          @click="handlerCreateData()"
        >{{ $t('vcs.common.confirm') }}</el-button>
        <el-button
          v-if="isUpdate"
          type="primary"
          size="small"
          :loading="confirmButtonLoading"
          @click="handlerUpdateData()"
        >{{ $t('vcs.common.update') }}</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import moment from 'moment'
import Descriptions from '@/components/Descriptions/Descriptions.vue'
import CustomizeForm from '@/components/CustomizeForm/CustomizeForm.vue'
import {
  createCreditCardProductCatalogue,
  deleteCreditCardProductCatalogue,
  getCreditCardProductCataloguePage,
  updateCreditCardProductCatalogue
} from '@/api/system/creditCardProductCatalogue'
import { formConfig } from '@/views/system/creditCardProductCatalogue/form-config'
import CustomizeCard from '@/components/CustomizeCard/index.vue'

export default {
  name: 'CreditCardProductCatalogue',
  components: { CustomizeCard, CustomizeForm, Descriptions },
  data() {
    return {
      moment,
      loading: false,
      tableLoading: false,
      viewDialogVisible: false,
      confirmButtonLoading: false,
      dataFormDialogVisible: false,
      isUpdate: false,
      formConfig: formConfig,
      searchForm: {
        currentPage: 1,
        pageSize: 20,
        dateRange: [],
        catalogueCode: '',
        catalogueName: ''
      },
      viewRow: [],
      formData: {},
      items: [],
      total: 0,
      multipleSelection: []
    }
  },
  mounted() {
    this.getItems()
  },
  methods: {
    getItems() {
      this.tableLoading = true

      const requestData = { ...this.searchForm }

      if (requestData.dateRange) {
        requestData.createDateFrom = this.searchForm.dateRange[0]
        requestData.createDateTo = this.searchForm.dateRange[1]
      } delete requestData.dateRange
      getCreditCardProductCataloguePage(requestData)
        .then((res) => {
          const { total, items } = res.data

          this.items = items
          this.total = total
        })
        .finally(() => {
          this.tableLoading = false
        })
    },
    handlerCreateData() {
      this.$refs.customizeForm.validate((valid, data) => {
        if (valid) {
          const formData = data
          this.confirmButtonLoading = true
          createCreditCardProductCatalogue(formData)
            .then((res) => {
              const { msg } = res
              this.$message.success(msg)
              this.getItems()
              this.dataFormDialogVisible = false
            })
            .finally(() => {
              this.confirmButtonLoading = false
            })
        }
      })
    },
    handlerUpdateData() {
      this.$refs.customizeForm.validate((valid, data) => {
        if (valid) {
          const formData = data
          this.confirmButtonLoading = true
          updateCreditCardProductCatalogue(formData)
            .then((res) => {
              const { msg } = res
              this.$message.success(msg)
              this.getItems()
              this.dataFormDialogVisible = false
            })
            .finally(() => {
              this.confirmButtonLoading = false
            })
        }
      })
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    viewDialog(row) {
      const description = { ...row }
      this.viewRow = this.formConfig.map(item => {
        let value = description[item.prop]
        if (item.format) {
          return { key: this.$t(item.label), value: moment(Number(value)).format(item.format.replace('dd', 'DD').replace('yyyy', 'YYYY')) }
        } else {
          if (item.options) {
            const option = item.options.filter(option => option.value === value)
            if (option && option.length) {
              value = this.$t(option[0].label)
            }
          }
          return { key: this.$t(item.label), value: value }
        }
      })
      this.viewDialogVisible = true
    },
    openUpdateDialog(row) {
      this.formData = { ...row }
      this.isUpdate = true
      this.dataFormDialogVisible = true
    },
    openCreateDialog() {
      this.isUpdate = false
      this.formData = {}
      if (this.$refs.customizeForm) {
        this.$refs.customizeForm.resetForm()
      }
      this.dataFormDialogVisible = true
    },
    deleteItem(ids) {
      let deletedIds = ids

      if (!deletedIds || !deletedIds.length) {
        deletedIds = this.multipleSelection.map((item) => item.id)
      }

      if (!deletedIds || !deletedIds.length) {
        this.$message.error(this.$t('vcs.common.deleteSelectTipMessage'))
        return
      }

      this.$confirm(this.$t('vcs.common.deleteTipMessage'), this.$t('vcs.common.tip'), {
        confirmButtonText: this.$t('vcs.common.confirm'),
        cancelButtonText: this.$t('vcs.common.cancel'),
        type: 'warning'
      }).then(() => {
        this.tableLoading = true
        deleteCreditCardProductCatalogue({ ids: deletedIds })
          .then((res) => {
            const { msg } = res
            this.searchForm.currentPage = 1
            this.$message.success(msg)
            this.getItems()
            this.dataFormDialogVisible = false
          })
          .finally(() => {
            this.tableLoading = false
          })
      })
    },
    resetForm(formName) {
      this.$refs[formName].resetFields()
    }
  }
}
</script>
