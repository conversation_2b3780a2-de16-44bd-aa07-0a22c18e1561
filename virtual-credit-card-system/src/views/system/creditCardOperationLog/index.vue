<template>
  <div v-loading="loading" class="business-container">
    <el-card>
      <!-- 搜索表单 -->
      <el-form
        ref="searchForm"
        :model="searchForm"
        size="small"
        label-width="220px"
        label-position="left"
        :show-message="false"
      >
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item :label="$t('vcs.creditCardOperationLog.customerNumber')" prop="customerNumber">
              <el-input
                v-model="searchForm.customerNumber"
                :placeholder="$t('vcs.creditCardOperationLog.customerNumberPlaceholder')"
                clearable
                class="search-form-item"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="$t('vcs.creditCardOperationLog.title')" prop="title">
              <el-input
                v-model="searchForm.title"
                :placeholder="$t('vcs.creditCardOperationLog.titlePlaceholder')"
                clearable
                class="search-form-item"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="$t('vcs.creditCardOperationLog.type')" prop="type">
              <el-select
                v-model="searchForm.type"
                :placeholder="$t('vcs.creditCardOperationLog.typePlaceholder')"
                clearable
                class="search-form-item"
              >
                <el-option v-for="item in operationLogTypeOptions" :key="item.value" :label="$t(item.label)" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="$t('vcs.creditCardOperationLog.method')" prop="method">
              <el-select
                v-model="searchForm.method"
                :placeholder="$t('vcs.creditCardOperationLog.methodPlaceholder')"
                clearable
                class="search-form-item"
              >
                <el-option v-for="item in operationLogMethodOptions" :key="item.value" :label="$t(item.label)" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="$t('vcs.creditCardOperationLog.status')" prop="status">
              <el-select
                v-model="searchForm.status"
                :placeholder="$t('vcs.creditCardOperationLog.statusPlaceholder')"
                clearable
                class="search-form-item"
              >
                <el-option v-for="item in operationLogStatusOptions" :key="item.value" :label="$t(item.label)" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="$t('vcs.creditCardOperationLog.ip')" prop="ip">
              <el-input
                v-model="searchForm.ip"
                :placeholder="$t('vcs.creditCardOperationLog.ipPlaceholder')"
                clearable
                class="search-form-item"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="$t('vcs.creditCardOperationLog.requestUrl')" prop="requestUrl">
              <el-input
                v-model="searchForm.requestUrl"
                :placeholder="$t('vcs.creditCardOperationLog.requestUrlPlaceholder')"
                clearable
                class="search-form-item"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="$t('vcs.common.createDate')" prop="dateRange">
              <el-date-picker
                v-model="searchForm.dateRange"
                type="daterange"
                :range-separator="$t('vcs.common.to')"
                value-format="timestamp"
                :start-placeholder="$t('vcs.common.startDate')"
                :end-placeholder="$t('vcs.common.endDate')"
                :default-time="['00:00:00', '23:59:59']"
                unlink-panels
                class="search-form-item"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <!-- 操作按钮 -->
            <el-button type="primary" size="small" style="margin-bottom: 18px" icon="el-icon-search" @click="getItems">
              {{ $t('vcs.common.search') }}
            </el-button>
            <el-button icon="el-icon-refresh" size="small" @click="resetForm('searchForm')">{{
              $t('vcs.common.reset')
            }}
            </el-button>
          </el-col>
        </el-row>

      </el-form>

      <!-- 创建和批量删除按钮 -->
      <div class="data-action-button-group">
        <div />

        <!-- 刷新按钮 -->
        <el-button icon="el-icon-refresh" size="small" circle @click="getItems" />
      </div>

      <!-- 表格 -->
      <el-card shadow="never">
        <el-table v-loading="tableLoading" :data="items" stripe>
          <el-table-column type="index" label="#" align="center" />
          <el-table-column
            :label="$t('vcs.creditCardOperationLog.createDate')"
            prop="createDate"
            align="center"
            width="150"
          >
            <template slot-scope="{ row }">
              {{ moment(row.createDate).format('YYYY-MM-DD HH:mm:ss') }}
            </template>
          </el-table-column>
          <el-table-column
            :label="$t('vcs.creditCardOperationLog.customerNumber')"
            prop="customerNumber"
            align="center"
            width="150"
          />
          <el-table-column :label="$t('vcs.creditCardOperationLog.title')" prop="title" align="center" width="200" show-overflow-tooltip />
          <el-table-column
            :label="$t('vcs.creditCardOperationLog.requestUrl')"
            prop="requestUrl"
            align="center"
            width="200"
            show-overflow-tooltip
          />
          <el-table-column :label="$t('vcs.creditCardOperationLog.type')" prop="type" align="center" width="150">
            <template slot-scope="scope">
              {{ $t(getLabel("OPERATION_LOG_TYPE", scope.row.type)) }}
            </template>
          </el-table-column>
          <el-table-column :label="$t('vcs.creditCardOperationLog.method')" prop="method" align="center" width="150">
            <template slot-scope="scope">
              {{ $t(getLabel("OPERATION_LOG_METHOD", scope.row.method)) }}
            </template>
          </el-table-column>
          <el-table-column :label="$t('vcs.creditCardOperationLog.status')" prop="status" align="center" width="150">
            <template slot-scope="scope">
              {{ $t(getLabel("OPERATION_LOG_STATUS", scope.row.status)) }}
            </template>
          </el-table-column>
          <el-table-column :label="$t('vcs.creditCardOperationLog.ip')" prop="ip" align="center" width="150" />
          <el-table-column
            :label="$t('vcs.creditCardOperationLog.duration')"
            prop="duration"
            align="center"
            width="150"
          />
          <el-table-column
            :label="$t('vcs.creditCardOperationLog.userAgent')"
            prop="userAgent"
            align="center"
            width="150"
          />
          <el-table-column :label="$t('vcs.common.operate')" align="center" width="200" fixed="right">
            <template slot-scope="{ row }">
              <el-button
                type="text"
                size="mini"
                text
                icon="el-icon-view"
                @click="viewDialog(row)"
              >
                {{ $t('vcs.common.view') }}
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>

      <div class="table-footer">
        <el-pagination
          :current-page.sync="searchForm.currentPage"
          :page-sizes="[20, 50, 100, 500, 1000]"
          :page-size.sync="searchForm.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="getItems()"
          @current-change="getItems()"
        />
      </div>
    </el-card>
    <!-- 查看的对话框 -->
    <el-dialog
      :title="$t('vcs.creditCardOperationLog.viewDetails')"
      :visible.sync="viewDialogVisible"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      width="60%"
      top="50px"
    >
      <el-descriptions class="descriptions" :column="2" border>
        <el-descriptions-item>
          <template slot="label">{{ $t('vcs.creditCardOperationLog.customerNumber') }}</template>
          {{ operationLog.customerNumber }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">{{ $t('vcs.creditCardOperationLog.createDate') }}</template>
          {{ moment(operationLog.createDate).format('YYYY-MM-DD HH:mm:ss') }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">{{ $t('vcs.creditCardOperationLog.title') }}</template>
          {{ operationLog.title }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">{{ $t('vcs.creditCardOperationLog.type') }}</template>
          {{ $t(getLabel("OPERATION_LOG_TYPE", operationLog.type)) }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">{{ $t('vcs.creditCardOperationLog.method') }}</template>
          {{ $t(getLabel("OPERATION_LOG_METHOD", operationLog.method)) }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">{{ $t('vcs.creditCardOperationLog.status') }}</template>
          {{ $t(getLabel("OPERATION_LOG_STATUS", operationLog.status)) }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">{{ $t('vcs.creditCardOperationLog.ip') }}</template>
          {{ operationLog.ip }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">{{ $t('vcs.creditCardOperationLog.duration') }}</template>
          {{ operationLog.duration }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">{{ $t('vcs.creditCardOperationLog.userAgent') }}</template>
          {{ operationLog.userAgent }}
        </el-descriptions-item>
        <el-descriptions-item>
          <template slot="label">{{ $t('vcs.creditCardOperationLog.requestUrl') }}</template>
          {{ operationLog.requestUrl }}
        </el-descriptions-item>
        <el-descriptions-item :span="2">
          <template slot="label">{{ $t('vcs.creditCardOperationLog.params') }}</template>
          <pre>{{ operationLog.params && JSON.stringify(JSON.parse(operationLog.params), null, 2) }}</pre>
        </el-descriptions-item>
        <el-descriptions-item :span="2">
          <template slot="label">{{ $t('vcs.creditCardOperationLog.result') }}</template>
          <pre>{{ operationLog.result && JSON.stringify(JSON.parse(operationLog.result), null, 2) }}</pre>
        </el-descriptions-item>
        <el-descriptions-item :span="2">
          <template slot="label">{{ $t('vcs.creditCardOperationLog.exception') }}</template>
          {{ operationLog.exception }}
        </el-descriptions-item>
      </el-descriptions>
    </el-dialog>
  </div>
</template>

<script>
import moment from 'moment'
import { getCreditCardOperationLogPage } from '@/api/system/creditCardOperationLog'
import { formConfig } from '@/views/system/creditCardOperationLog/form-config'
import { getLabel, operationLogMethodOptions, operationLogStatusOptions, operationLogTypeOptions } from '@/data'

export default {
  name: 'CreditCardOperationLog',
  data() {
    return {
      moment,
      getLabel,
      operationLogTypeOptions,
      operationLogMethodOptions,
      operationLogStatusOptions,
      loading: false,
      tableLoading: false,
      viewDialogVisible: false,
      formConfig: formConfig,
      searchForm: {
        currentPage: 1,
        pageSize: 20,
        dateRange: [],
        customerNumber: '',
        title: '',
        type: '',
        method: '',
        status: '',
        ip: '',
        requestUrl: ''
      },
      viewRow: [],
      items: [],
      total: 0,
      operationLog: {}
    }
  },
  mounted() {
    this.getItems()
  },
  methods: {
    getItems() {
      this.tableLoading = true

      const requestData = { ...this.searchForm }

      if (this.searchForm.dateRange) {
        requestData.createDateForm = this.searchForm.dateRange[0]
        requestData.createDateTo = this.searchForm.dateRange[1]
      }
      getCreditCardOperationLogPage(requestData)
        .then((res) => {
          const { total, items } = res.data

          this.items = items
          this.total = total
        })
        .finally(() => {
          this.tableLoading = false
        })
    },
    viewDialog(row) {
      this.operationLog = { ...row }
      this.viewDialogVisible = true
    },
    resetForm(formName) {
      this.$refs[formName].resetFields()
    }
  }
}
</script>
