<template>
  <div v-loading="loading" class="business-container">
    <customize-card :title="$t('vcs.router.redemptionHistory')" :show-back-btn="false">
      <!-- 搜索表单 -->
      <el-form
        ref="searchForm"
        :model="searchForm"
        size="small"
        label-width="160px"
        label-position="left"
        :show-message="false"
      >
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item :label="$t('vcs.creditCardRewardPointTransaction.creditCardNumber')" prop="creditCardNumber">
              <el-input
                v-model="searchForm.creditCardNumber"
                :placeholder="$t('vcs.creditCardRewardPointTransaction.creditCardNumberPlaceholder')"
                clearable
                class="search-form-item"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="$t('vcs.creditCardRewardPointTransaction.transactionDate')" prop="dateRange">
              <el-date-picker
                v-model="searchForm.dateRange"
                type="daterange"
                :range-separator="$t('vcs.common.to')"
                value-format="timestamp"
                :start-placeholder="$t('vcs.creditCardRewardPointTransaction.transactionDateStart')"
                :end-placeholder="$t('vcs.creditCardRewardPointTransaction.transactionDateEnd')"
                :default-time="['00:00:00', '23:59:59']"
                unlink-panels
                class="search-form-item"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <!-- 操作按钮 -->
            <el-button type="primary" size="small" style="margin-bottom: 18px" icon="el-icon-search" @click="getItems">
              {{ $t('vcs.common.search') }}
            </el-button>
            <el-button icon="el-icon-refresh" size="small" @click="resetForm('searchForm')">{{
              $t('vcs.common.reset')
            }}
            </el-button>
          </el-col>
        </el-row>

      </el-form>

      <!-- 创建和批量删除按钮 -->
      <div class="data-action-button-group">
        <div />

        <!-- 刷新按钮 -->
        <el-button icon="el-icon-refresh" size="small" circle @click="getItems" />
      </div>

      <!-- 表格 -->
      <el-card shadow="never">
        <el-table v-loading="tableLoading" :data="items" stripe>
          <el-table-column type="index" label="#" align="center" />
          <el-table-column
            :label="$t('vcs.creditCardRewardPointTransaction.creditCardNumber')"
            prop="creditCardNumber"
            align="center"
          />
          <el-table-column
            :label="$t('vcs.creditCardRewardPointTransaction.transactionOption')"
            prop="transactionOption"
            align="center"
          >
            <template slot-scope="{ row }">
              {{ $t(getLabel("TRANSACTION_OPTION", row.transactionOption)) }}
            </template>
          </el-table-column>
          <el-table-column
            :label="$t('vcs.creditCardRewardPointTransaction.rewardPointsForEachUnit')"
            align="center"
            width="130"
          >
            <template slot-scope="{ row }">
              {{ row.transactionDetails.reduce((acc, item) => acc + item.rewardPointsForEachUnit * item.redemptionCount, 0) }}
            </template>
          </el-table-column>
          <el-table-column
            :label="$t('vcs.creditCardRewardPointTransaction.totalAdditionalCashRequired')"
            prop="totalAdditionalCashRequired"
            align="center"
          />
          <el-table-column
            :label="$t('vcs.creditCardRewardPointTransaction.debitAccountNumberForCashSupplement')"
            prop="debitAccountNumberForCashSupplement"
            align="center"
            width="330"
          />
          <el-table-column
            :label="$t('vcs.creditCardRewardPointTransaction.transactionDate')"
            prop="transactionDate"
            align="center"
          >
            <template slot-scope="{ row }">
              {{ moment(row.transactionDate).format('YYYY-MM-DD HH:mm:ss') }}
            </template>
          </el-table-column>
          <el-table-column :label="$t('vcs.common.operate')" align="center" width="200" fixed="right">
            <template slot-scope="{ row }">
              <el-button
                type="text"
                size="mini"
                text
                icon="el-icon-view"
                @click="viewDialog(row)"
              >
                {{ $t('vcs.common.view') }}
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>

      <div class="table-footer">
        <el-pagination
          :current-page.sync="searchForm.currentPage"
          :page-sizes="[20, 50, 100, 500, 1000]"
          :page-size.sync="searchForm.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="getItems()"
          @current-change="getItems()"
        />
      </div>
    </customize-card>

    <!-- 查看的对话框 -->
    <el-dialog
      :title="$t('vcs.creditCardRewardPointTransaction.viewDetails')"
      :visible.sync="viewDialogVisible"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      width="60%"
      top="50px"
    >
      <el-card shadow="never">
        <el-table v-loading="viewDialogTableLoading" :data="transactionDetail" stripe>
          <el-table-column type="index" label="#" align="center" />
          <el-table-column
            :label="$t('vcs.creditCardRewardPointTransaction.productName')"
            prop="productName"
            align="center"
          />
          <el-table-column
            :label="$t('vcs.creditCardRewardPointTransaction.productCatalogueName')"
            prop="productCatalogueName"
            align="center"
          />
          <el-table-column
            :label="$t('vcs.creditCardRewardPointTransaction.rewardPointsForEachUnit')"
            prop="rewardPointsForEachUnit"
            align="center"
          />
          <el-table-column
            :label="$t('vcs.creditCardRewardPointTransaction.redemptionCount')"
            prop="redemptionCount"
            align="center"
          />
          <el-table-column
            :label="$t('vcs.creditCardRewardPointTransaction.additionalCashRequired')"
            prop="additionalCashRequired"
            align="center"
            width="240"
          />
        </el-table>
      </el-card>
    </el-dialog>

  </div>
</template>

<script>
import moment from 'moment'
import { getRedemptionHistoryDetails } from '@/api/system/creditCardRewardPointTransaction'
import { formConfig } from '@/views/points/redemption-history/form-config'
import CustomizeCard from '@/components/CustomizeCard/index.vue'
import { getLabel } from '@/data'

export default {
  name: 'CreditCardRewardPointTransaction',
  components: { CustomizeCard },
  data() {
    return {
      moment,
      loading: false,
      tableLoading: false,
      viewDialogVisible: false,
      viewDialogTableLoading: false,
      formConfig: formConfig,
      getLabel,
      searchForm: {
        currentPage: 1,
        pageSize: 20,
        dateRange: [],
        creditCardNumber: ''
      },
      items: [],
      total: 0,
      creditCardList: [],
      transactionDetail: []
    }
  },
  methods: {
    getItems() {
      this.tableLoading = true

      const requestData = { ...this.searchForm }

      if (requestData.dateRange) {
        requestData.createDateFrom = this.searchForm.dateRange[0]
        requestData.createDateTo = this.searchForm.dateRange[1]
      }
      delete requestData.dateRange
      getRedemptionHistoryDetails(requestData)
        .then((res) => {
          const { total, items } = res.data

          this.items = items
          this.total = total
        })
        .finally(() => {
          this.tableLoading = false
        })
    },
    viewDialog(row) {
      this.transactionDetail = row.transactionDetails
      this.viewDialogVisible = true
    },
    resetForm(formName) {
      this.$refs[formName].resetFields()
    }
  }
}
</script>
