<template>
  <div v-loading.fullscreen.lock="loading" class="workflow-manage">
    <customize-card :title="$t('vcs.router.workflow')">
      <div v-show="pageList.AcknowledgeTC.show" style="width:60%;margin:0 auto;">
        <h3 style="text-align:center;margin-bottom: 30px;">{{ $t("vcs.workflow.AcknowledgeTC") }}</h3>
        <el-alert style="margin-bottom:30px;text-align: left;" :title="$t('vcs.workflow.AcknowledgeTCMsg')" type="success" />
        <div class="privacy-policy" v-html="$t('vcs.workflow.privacyPolicyContent')" />
        <div class="text-center" style="text-align: center;margin-bottom: 20px">
          <el-checkbox v-model="form2.acknowledge">{{ $t('vcs.workflow.endUserPrivacyPolicy') }}</el-checkbox>
        </div>
        <div slot="footer" class="dialog-footer" style="text-align: center">
          <el-button
            v-if="pageList.AcknowledgeTC.notFirstNode"
            style="margin-right: 10px"
            @click="prev('Acknowledge T&C')"
          >{{ $t("vcs.common.prev") }}
          </el-button>
          <span v-if="pageList.AcknowledgeTC.completed">
            <el-button @click="viewReponse('Acknowledge T&C')">{{ $t("vcs.workflow.viewReponse") }}</el-button>
            <el-button
              v-if="pageList.AcknowledgeTC.notLastNode"
              type="primary"
              @click="next('Acknowledge T&C')"
            >{{ $t("vcs.common.next") }}</el-button>
          </span>
          <span v-else-if="pageList.AcknowledgeTC.running" style="margin-left: 10px">
            <el-button type="primary" @click="submit2">{{ $t("vcs.common.next") }}</el-button>
          </span>
        </div>
      </div>

      <div v-show="pageList.CaptureSignature.show" style="width:500px;margin:0 auto;text-align:center;">
        <h3 style="text-align:center;margin-bottom: 30px;">{{ $t("vcs.workflow.CaptureSignature") }}</h3>
        <el-alert
          style="margin-bottom:30px;text-align: left;"
          :title="$t('vcs.workflow.CaptureSignatureMsg')"
          type="success"
        />
        <el-form ref="form4" :model="form4" :rules="formrules4" label-width="100px">
          <!-- <el-form-item :label='$t("vcs.workflow.customerID")' prop='customerId'>
              <el-input v-model="form4.customerId" :maxlength="35"/>
            </el-form-item> -->
          <el-form-item :label="$t('vcs.workflow.Signature')" prop="file">
            <el-upload
              ref="upload"
              class="upload-demo"
              action="/test"
              accept=".bmp,.jpg,.jpeg,.png"
              :auto-upload="false"
              :show-file-list="true"
              :drag="true"
              :limit="1"
              :on-change="exportData4"
              :file-list="fileList4"
            >
              <i class="el-icon-upload" />
              <div class="el-upload__text">
                <em>{{ $t("vcs.common.clickUpload") }}</em>
              </div>
              <div slot="tip" class="el-upload__tip">{{ $t("vcs.workflow.fileerror") }}</div>
            </el-upload>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button
            v-if="pageList.CaptureSignature.notFirstNode"
            style="margin-right: 10px"
            @click="prev('Capture Signature')"
          >{{ $t("vcs.common.prev") }}
          </el-button>
          <span v-if="pageList.CaptureSignature.completed">
            <el-button @click="viewReponse('Capture Signature')">{{ $t("vcs.workflow.viewReponse") }}</el-button>
            <el-button
              v-if="pageList.CaptureSignature.notLastNode"
              type="primary"
              @click="next('Capture Signature')"
            >{{ $t("vcs.common.next") }}</el-button>
          </span>
          <span v-else-if="pageList.CaptureSignature.running">
            <el-button type="primary" @click="submit4">{{ $t("vcs.common.next") }}</el-button>
          </span>
        </div>
      </div>

      <div v-show="pageList.CheckBlacklist.show" style="width:500px;margin:0 auto;text-align:center;">
        <h3 style="text-align:center;margin-bottom: 30px;">{{ $t("vcs.workflow.CheckingBlacklist") }}</h3>
        <el-alert
          style="margin-bottom:30px;text-align: left;"
          :title="$t('vcs.workflow.CheckBlacklistMsg')"
          type="success"
        />
        <!-- <el-form :model="form5" :rules="formrules5" ref="form5" label-width="150px">
            <el-form-item :label='$t("vcs.workflow.customerID")' prop='customerId'>
              <el-input v-model="form5.customerId" :maxlength="35"/>
            </el-form-item>
          </el-form> -->
        <div style="margin:50px 0;text-algin:center">{{ $t("vcs.workflow.CheckingBlacklistTip") }}</div>
        <div slot="footer" class="dialog-footer">
          <el-button
            v-if="pageList.CheckBlacklist.notFirstNode"
            style="margin-right: 10px"
            @click="prev('Check Blacklist')"
          >{{ $t("vcs.common.prev") }}
          </el-button>
          <span v-if="pageList.CheckBlacklist.completed">
            <el-button @click="viewReponse('Check Blacklist')">{{ $t("vcs.workflow.viewReponse") }}</el-button>
            <el-button
              v-if="pageList.CheckBlacklist.notLastNode"
              type="primary"
              @click="next('Check Blacklist')"
            >{{ $t("vcs.common.next") }}</el-button>
          </span>
          <span v-else-if="pageList.CheckBlacklist.running">
            <!-- <el-button type="primary" @click="submit5">{{$t("vcs.common.next")}}</el-button> -->
          </span>
        </div>
      </div>

      <div v-show="pageList.CheckSanction.show" style="width:500px;margin:0 auto;text-align:center;">
        <h3 style="text-align:center;margin-bottom: 30px;">{{ $t("vcs.workflow.CheckingSanction") }}</h3>
        <el-alert style="margin-bottom:30px;text-align: left;" :title="$t('vcs.workflow.CheckSanctionMsg')" type="success" />
        <!-- <el-form :model="form6" :rules="formrules6" ref="form6" label-width="150px">
            <el-form-item :label='$t("vcs.workflow.customerID")' prop='customerId'>
              <el-input v-model="form6.customerId" :maxlength="35"/>
            </el-form-item>
          </el-form> -->
        <div style="margin:50px 0;text-algin:center">{{ $t("vcs.workflow.CheckingSanctionTip") }}</div>
        <div slot="footer" class="dialog-footer">
          <span v-if="pageList.CheckSanction.completed">
            <el-button @click="viewReponse('Check Sanction')">{{ $t("vcs.workflow.viewReponse") }}</el-button>
            <el-button
              v-if="pageList.CheckSanction.notLastNode"
              type="primary"
              @click="next('Check Sanction')"
            >{{ $t("vcs.common.next") }}</el-button>
          </span>
          <span v-else-if="pageList.CheckSanction.running">
            <!-- <el-button type="primary" @click="submit6">{{$t("vcs.common.next")}}</el-button> -->
          </span>
        </div>
      </div>

      <div v-show="pageList.PEP.show" style="width:500px;margin:0 auto;text-align:center;">
        <h3 style="text-align:center;margin-bottom: 30px;">{{ $t("vcs.workflow.PEP") }}</h3>
        <el-alert style="margin-bottom:30px;text-align: left;" :title="$t('vcs.workflow.PEPMsg')" type="success" />
        <div style="margin:50px 0;text-algin:center">{{ $t("vcs.workflow.PEPTip") }}</div>
        <div slot="footer" class="dialog-footer">
          <el-button v-if="pageList.PEP.notFirstNode" style="margin-right: 10px" @click="prev('PEP')">
            {{ $t("vcs.common.prev") }}
          </el-button>
          <span v-if="pageList.PEP.completed">
            <el-button @click="viewReponse('PEP')">{{ $t("vcs.workflow.viewReponse") }}</el-button>
            <el-button v-if="pageList.PEP.notLastNode" type="primary" @click="next('PEP')">{{
              $t("vcs.common.next")
            }}</el-button>
          </span>
          <span v-else-if="pageList.PEP.running">
            <!-- <el-button type="primary" @click="submitPep">{{$t("vcs.common.next")}}</el-button> -->
          </span>
        </div>
      </div>

      <div v-show="pageList.CRS.show" style="width:1000px;margin:0 auto;text-align:center;">
        <h3 style="text-align:center;margin-bottom: 30px;">{{ $t("vcs.workflow.CRS") }}</h3>
        <el-alert style="margin-bottom:30px;text-align: left;" :title="$t('vcs.workflow.CRSMsg')" type="success" />
        <customize-form
          ref="customizeCRSForm"
          form-ref="dataForm"
          size="small"
          :model="form7"
          :form-item-config="crsFormConfig"
          label-width="220px"
          label-position="left"
          is-update
        />
        <div slot="footer" class="dialog-footer" style="margin-top: 10px">
          <el-button v-if="pageList.CRS.notFirstNode" style="margin-right: 10px" @click="prev('CRS')">
            {{ $t("vcs.common.prev") }}
          </el-button>
          <span v-if="pageList.CRS.completed">
            <el-button @click="viewReponse('CRS')">{{ $t("vcs.workflow.viewReponse") }}</el-button>
            <el-button v-if="pageList.CRS.notLastNode" type="primary" @click="next('CRS')">{{
              $t("vcs.common.next")
            }}</el-button>
          </span>
          <span v-else-if="pageList.CRS.running" style="margin-left: 10px">
            <el-button type="primary" @click="submit7">{{ $t("vcs.common.next") }}</el-button>
          </span>
        </div>
      </div>

      <div v-show="pageList.FATCA.show" style="width:1000px;margin:0 auto;text-align:center;">
        <h3 style="text-align:center;margin-bottom: 30px;">{{ $t("vcs.workflow.FATCA") }}</h3>
        <el-alert style="margin-bottom:30px;text-align: left;" :title="$t('vcs.workflow.FATCAMsg')" type="success" />
        <customize-form
          ref="customizeFATCAForm"
          form-ref="dataForm"
          size="small"
          :model="form8"
          :form-item-config="fatcaFormConfig"
          label-width="240px"
          label-position="left"
          is-update
        />
        <div slot="footer" class="dialog-footer" style="margin-top: 10px">
          <el-button v-if="pageList.FATCA.notFirstNode" style="margin-right: 10px" @click="prev('FATCA')">
            {{ $t("vcs.common.prev") }}
          </el-button>
          <span v-if="pageList.FATCA.completed">
            <el-button @click="viewReponse('FATCA')">{{ $t("vcs.workflow.viewReponse") }}</el-button>
            <el-button v-if="pageList.FATCA.notLastNode" type="primary" @click="next('FATCA')">{{
              $t("vcs.common.next")
            }}</el-button>
          </span>
          <span v-else-if="pageList.FATCA.running">
            <el-button type="primary" @click="submit8">{{ $t("vcs.common.next") }}</el-button>
          </span>
        </div>
      </div>

      <div v-show="pageList.userCreation.show" style="width:500px;margin:0 auto;text-align:center;">
        <h3 style="text-align:center;margin-bottom: 30px;">{{ $t("vcs.workflow.userCreation") }}</h3>
        <el-alert style="margin-bottom:30px;text-align: left;" :title="$t('vcs.workflow.SetPinMsg')" type="success" />
        <el-form ref="form9" :model="form9" :rules="formrules9" label-width="150px">
          <el-form-item :label="$t('vcs.workflow.developerID')" prop="developerID">
            <el-input v-model="form9.developerID" :maxlength="50" />
          </el-form-item>
          <el-form-item :label="$t('vcs.workflow.developername')" prop="developername">
            <el-input v-model="form9.developername" :maxlength="25" />
          </el-form-item>
          <el-form-item :label="$t('vcs.workflow.email')" prop="email">
            <el-input v-model="form9.email" :maxlength="50" />
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button v-if="pageList.userCreation.notFirstNode" style="margin-right: 10px" @click="prev('Set Pin')">
            {{ $t("vcs.common.prev") }}
          </el-button>
          <span v-if="pageList.userCreation.completed">
            <el-button @click="viewReponse('Set Pin')">{{ $t("vcs.workflow.viewReponse") }}</el-button>
            <el-button
              v-if="pageList.userCreation.notLastNode"
              type="primary"
              @click="next('Set Pin')"
            >{{ $t("vcs.common.next") }}</el-button>
          </span>
          <span v-else-if="pageList.userCreation.running">
            <el-button type="primary" @click="submit9">{{ $t("vcs.common.next") }}</el-button>
          </span>
        </div>
      </div>

      <div v-show="pageList.SetPin.show" style="width:500px;margin:0 auto;text-align:center;">
        <h3 style="text-align:center;margin-bottom: 30px;">{{ $t("vcs.workflow.SetPin") }}</h3>
        <el-alert style="margin-bottom:30px;text-align: left;" :title="$t('vcs.workflow.userCreationMsg')" type="success" />
        <el-form ref="form10" :model="form10" :rules="formrules10" label-width="150px">
          <!-- <el-form-item :label='$t("vcs.workflow.developerID")' prop='developerID'>
              <el-input v-model="form10.developerID" :maxlength="50"/>
            </el-form-item> -->
          <el-form-item :label="$t('vcs.workflow.customerNumber')" prop="customerNumber">
            <el-input v-model="form10.customerNumber" :maxlength="25" disabled />
          </el-form-item>
          <el-form-item :label="$t('vcs.workflow.loginName')" prop="loginName">
            <el-input v-model="form10.loginName" :maxlength="50" />
          </el-form-item>
          <el-form-item :label="$t('vcs.workflow.loginPwd')" prop="loginPwd">
            <el-input v-model="form10.loginPwd" :maxlength="50" />
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button v-if="pageList.SetPin.notFirstNode" style="margin-right: 10px" @click="prev('Set Pin')">
            {{ $t("vcs.common.prev") }}
          </el-button>
          <span v-if="pageList.SetPin.completed">
            <el-button @click="viewReponse('SetPin')">{{ $t("vcs.workflow.viewReponse") }}</el-button>
            <el-button
              v-if="pageList.SetPin.notLastNode"
              type="primary"
              @click="next('SetPin')"
            >{{ $t("vcs.common.next") }}</el-button>
          </span>
          <span v-else-if="pageList.SetPin.running">
            <el-button type="primary" @click="submit10">{{ $t("vcs.common.next") }}</el-button>
          </span>
        </div>
      </div>

      <div v-show="pageList.VerifyAddress.show" style="width:500px;margin:0 auto;text-align:center;">
        <h3 style="text-align:center;margin-bottom: 30px;">{{ $t("vcs.workflow.VerifyAddress") }}</h3>
        <el-alert style="margin-bottom:30px;text-align: left;" :title="$t('vcs.workflow.VerifyAddressMsg')" type="success" />
        <el-form ref="form11" :model="form11" :rules="formrules11" label-width="150px">
          <el-form-item :label="$t('vcs.workflow.proofOfAddress')">
            <el-image
              style="width: 100px; height: 100px"
              :src="proofOfAddress[0]"
              :preview-src-list="proofOfAddress"
            />
          </el-form-item>
          <el-form-item :label="$t('vcs.workflow.addressReview')" prop="addressReview">
            <el-select v-model="form11.addressReview" style="width:100%">
              <el-option :label="$t('vcs.common.yes')" value="yes" />
              <el-option :label="$t('vcs.common.no')" value="no" />
            </el-select>
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button
            v-if="pageList.VerifyAddress.notFirstNode"
            style="margin-right: 10px"
            @click="prev('Verify Address')"
          >{{ $t("vcs.common.prev") }}
          </el-button>
          <span v-if="pageList.VerifyAddress.completed">
            <el-button @click="viewReponse('Verify Address')">{{ $t("vcs.workflow.viewReponse") }}</el-button>
            <el-button
              v-if="pageList.VerifyAddress.notLastNode"
              type="primary"
              @click="next('Verify Address')"
            >{{ $t("vcs.common.next") }}</el-button>
          </span>
          <span v-else-if="pageList.VerifyAddress.running">
            <el-button type="primary" @click="submit11">{{ $t("vcs.common.next") }}</el-button>
          </span>
        </div>
      </div>

      <div v-show="pageList.VerifyBiometrics.show" style="width:500px;margin:0 auto;text-align:center;">
        <h3 style="text-align:center;margin-bottom: 30px;">{{ $t("vcs.workflow.VerifyBiometrics") }}</h3>
        <el-alert
          style="margin-bottom:30px;text-align: left;"
          :title="$t('vcs.workflow.VerifyBiometricsMsg')"
          type="success"
        />
        <el-form ref="form12" :model="form12" :rules="formrules12" label-width="100px">
          <!-- <el-form-item :label='$t("vcs.workflow.customerID")' prop='customerId'>
              <el-input v-model="form12.customerId" :maxlength="35"/>
            </el-form-item> -->
          <el-form-item :label="$t('vcs.workflow.Biometrics')" prop="file">
            <el-image
              style="width: 100px; height: 100px"
              :src="form12.serverPath"
              :preview-src-list="[form12.serverPath]"
            />
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button
            v-if="pageList.VerifyBiometrics.notFirstNode"
            style="margin-right: 10px"
            @click="prev('Verify Biometrics')"
          >{{ $t("vcs.common.prev") }}
          </el-button>
          <span v-if="pageList.VerifyBiometrics.completed">
            <el-button @click="viewReponse('Verify Biometrics')">{{ $t("vcs.workflow.viewReponse") }}</el-button>
            <el-button
              v-if="pageList.VerifyBiometrics.notLastNode"
              type="primary"
              @click="next('Verify Biometrics')"
            >{{ $t("vcs.common.next") }}</el-button>
          </span>
          <span v-else-if="pageList.VerifyBiometrics.running" style="margin-left: 10px">
            <el-button type="primary" @click="submit12">{{ $t("vcs.common.next") }}</el-button>
          </span>
        </div>
      </div>

      <div v-show="pageList.CreditLimitAllocation.show">
        <h3 style="text-align:center;margin-bottom: 30px;">{{ $t("vcs.workflow.CreditLimitAllocation") }}</h3>
        <el-alert
          style="margin-bottom:30px;text-align: left;"
          :title="$t('vcs.workflow.CreditLimitAllocationMsg')"
          type="success"
        />
        <div style="margin-bottom: 10px">
          <el-tabs v-model="limitAllocation.activeName">
            <el-tab-pane
              :label="$t('vcs.creditCardApplication.limitAllocationDialog.customerDemographic')"
              name="customerDemographic"
            >
              <customerDemographic :account-open-application="accountOpenApplication" />
            </el-tab-pane>
            <el-tab-pane
              :label="$t('vcs.creditCardApplication.limitAllocationDialog.customerPortfolio')"
              name="customerPortfolio"
            >{{
              $t('vcs.creditCardApplication.limitAllocationDialog.customerPortfolio')
            }}
            </el-tab-pane>
            <el-tab-pane
              :label="$t('vcs.creditCardApplication.limitAllocationDialog.customerCreditInformation')"
              name="customerCreditInformation"
            >
              <customize-form
                ref="customizeForm"
                form-ref="dataForm"
                size="small"
                :model="customerInfoData"
                :form-item-config="informationFormConfig"
                label-width="240px"
                label-position="left"
                class="apply-form"
                style="margin-bottom: 10px"
              />
            </el-tab-pane>
          </el-tabs>
        </div>

        <div slot="footer" class="dialog-footer" style="text-align: center">
          <el-button
            v-if="pageList.CreditLimitAllocation.notFirstNode"
            style="margin-right: 10px"
            @click="prev('Credit Limit Allocation')"
          >{{ $t("vcs.common.prev") }}
          </el-button>
          <span v-if="pageList.CreditLimitAllocation.completed">
            <!--            <el-button @click="viewReponse('Credit Limit Allocation')">{{ $t("vcs.workflow.viewReponse") }}</el-button>-->
            <el-button
              v-if="pageList.CreditLimitAllocation.notLastNode"
              type="primary"
              @click="next('Credit Limit Allocation')"
            >{{ $t("vcs.common.next") }}</el-button>
          </span>
          <span v-else-if="pageList.CreditLimitAllocation.running">
            <el-button type="primary" @click="submitCreditLimitAllocation">{{ $t("vcs.common.next") }}</el-button>
          </span>
        </div>
      </div>
      <div v-show="pageList.AccountOpening.show" style="width:500px;margin:0 auto;text-align:center;">
        <h3 style="text-align:center;margin-bottom: 30px;">{{ $t("vcs.workflow.AccountOpening") }}</h3>
        <el-alert style="margin-bottom:30px;text-align: left;" :title="$t('vcs.workflow.AccountOpeningMsg')" type="success" />
        <el-form ref="form1" :model="form1" :rules="formrules1" label-width="210px">
          <el-form-item :label="$t('vcs.workflow.accountType')" prop="accountType">
            <el-select v-model="form1.accountType" style="width:100%">
              <el-option v-for="item in debitAccountTypeOptions" :key="item.value" :label="$t(item.label)" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item :label="$t('vcs.workflow.currencyCode')" prop="currencyCode">
            <el-select v-model="form1.currencyCode" style="width:100%">
              <el-option v-for="item in ccyOptions" :key="item" :value="item" />
            </el-select>
          </el-form-item>
          <el-form-item :label="$t('vcs.workflow.customerNumber')" prop="customerNumber">
            <el-input v-model="form1.customerNumber" :maxlength="25" disabled />
          </el-form-item>
          <el-form-item v-if="relAccountNumberShow" :label="$t('vcs.workflow.relaccountnumber')" prop="relaccountnumber">
            <el-input v-model="form1.relaccountnumber" :maxlength="34" />
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button v-if="pageList.AccountOpening.notFirstNode" style="margin-right: 10px" @click="prev('Account Opening')">{{ $t("vcs.common.prev") }}</el-button>
          <span v-if="pageList.AccountOpening.completed">
            <el-button @click="viewReponse('Account Opening')">{{ $t("vcs.workflow.viewReponse") }}</el-button>
            <el-button v-if="pageList.AccountOpening.notLastNode" type="primary" @click="next('Account Opening')">{{ $t("vcs.common.next") }}</el-button>
          </span>
          <span v-else-if="pageList.AccountOpening.running">
            <el-button type="primary" @click="submit1">{{ $t("vcs.common.next") }}</el-button>
          </span>
        </div>
      </div>
    </customize-card>
    <el-dialog
      append-to-body
      :visible.sync="responseDialog"
      :title="currentNode"
      width="600px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <pre>{{ response }}</pre>
      <div slot="footer" class="dialog-footer">
        <el-button @click="responseDialog = false">{{ $t("vcs.common.close") }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import CustomizeForm from '@/components/CustomizeForm/CustomizeForm.vue'
import { crsFormConfig, fatcaFormConfig } from '@/views/system/workflow/form-config'
import { debitAccountTypeOptions, handlerCommonData } from '@/data'
import CustomizeCard from '@/components/CustomizeCard/index.vue'
import { getCreditCardApplicationDetails, kycProcess } from '@/api/system/creditCardApplication'
import { executeFlow, executeUploadFlow, getFlowSort, getResponse } from '@/api/workflow/workflow'
import CustomerDemographic from '@/views/system/components/CustomerDemographic.vue'
import { initialLimit } from '@/api/system/creditCardCalculation'
import { formConfig as informationFormConfig } from '@/views/system/components/information-form-config'

const developerID = new Date().getTime()
export default {
  components: { CustomerDemographic, CustomizeCard, CustomizeForm },
  data() {
    const hiddenCRSFields = ['otherLanguageName', 'department', 'role', 'position', 'industry']
    const showCRSConfig = crsFormConfig.filter(item => !hiddenCRSFields.includes(item.prop))
    const hiddenFATCAFields = ['otherLanguageName', 'department', 'role', 'position', 'industry']
    const showFATCAConfig = fatcaFormConfig.filter(item => !hiddenFATCAFields.includes(item.prop))
    return {
      debitAccountTypeOptions,
      accountOpenApplication: {},
      crsFormConfig: showCRSConfig,
      fatcaFormConfig: showFATCAConfig,
      loading: false,
      userId: localStorage.getItem('DeveloperId'),
      customerId: '',
      customerNumber: '',
      mailingAddress: '',
      nationality: '',
      issueCountry: '',
      customerIDType: '',
      pageList: {
        AccountOpening: {
          show: false,
          notFirstNode: true,
          completed: false,
          running: false
        },
        AcknowledgeTC: {
          show: false,
          notFirstNode: true,
          completed: false,
          running: false
        },
        CaptureSignature: {
          show: false,
          notFirstNode: true,
          completed: false,
          running: false
        },
        CheckBlacklist: {
          show: false,
          notFirstNode: true,
          completed: false,
          running: false
        },
        CheckSanction: {
          show: false,
          notFirstNode: true,
          completed: false,
          running: false
        },
        PEP: {
          show: false,
          notFirstNode: true,
          completed: false,
          running: false
        },
        CRS: {
          show: false,
          notFirstNode: true,
          completed: false,
          running: false
        },
        FATCA: {
          show: false,
          notFirstNode: true,
          completed: false,
          running: false
        },
        userCreation: {
          show: false,
          notFirstNode: true,
          completed: false,
          running: false
        },
        SetPin: {
          show: false,
          notFirstNode: true,
          completed: false,
          running: false
        },
        VerifyAddress: {
          show: false,
          notFirstNode: true,
          completed: false,
          running: false
        },
        VerifyBiometrics: {
          show: false,
          notFirstNode: true,
          completed: false,
          running: false
        },
        CreditLimitAllocation: {
          show: false,
          notFirstNode: true,
          completed: false,
          running: false
        }
      },
      currentNode: '',
      tableData: [],
      responseTableData: [],
      responseDialog: false,
      response: {},
      requestData1: {
        'processInstanceId': this.$route.params.processInstanceId,
        'group_name': this.$route.params.groupName,
        'api_header': [],
        'api_param': [],
        'api_formdata': [],
        'api_json': ''
      },
      form1: {
        accountType: '001',
        branchCode: '',
        currencyCode: 'HKD',
        customerNumber: this.customerNumber,
        relaccountnumber: ''
      },
      formrules1: {
        customerNumber: [{ required: true, message: this.$t('vcs.workflow.customerNumbererror'), trigger: 'blur' }],
        relaccountnumber: [{ required: true, message: this.$t('vcs.workflow.relAccountNumberError'), trigger: 'blur' }]
      },
      requestData2: {
        'processInstanceId': this.$route.params.processInstanceId,
        'group_name': this.$route.params.groupName,
        'api_header': [],
        'api_param': [],
        'api_formdata': [],
        'api_json': ''
      },
      form2: {
        customerId: this.customerId,
        acknowledge: false
      },
      formrules2: {
        customerId: [{ required: true, message: this.$t('vcs.workflow.customerIDerror'), trigger: 'blur' }]
      },
      requestData4: {
        'processInstanceId': this.$route.params.processInstanceId,
        'group_name': this.$route.params.groupName,
        'api_header': [],
        'api_param': [],
        'api_formdata': [],
        'api_json': ''
      },
      form4: {
        customerId: this.customerId,
        file: null
      },
      formrules4: {
        customerId: [{ required: true, message: this.$t('vcs.workflow.customerIDerror'), trigger: 'blur' }],
        file: [{ required: true, message: this.$t('vcs.workflow.fileerror'), trigger: 'change' }]
      },
      fileList4: [],
      requestData5: {
        'processInstanceId': this.$route.params.processInstanceId,
        'group_name': this.$route.params.groupName,
        'api_header': [],
        'api_param': [],
        'api_formdata': [],
        'api_json': ''
      },
      form5: {
        customerId: this.customerId,
        issueCountry: this.issueCountry
      },
      formrules5: {
        customerId: [{ required: true, message: this.$t('vcs.workflow.customerIDerror'), trigger: 'blur' }]
      },
      requestData6: {
        'processInstanceId': this.$route.params.processInstanceId,
        'group_name': this.$route.params.groupName,
        'api_header': [],
        'api_param': [],
        'api_formdata': [],
        'api_json': ''
      },
      formPep: {
        customerId: this.customerId,
        issueCountry: this.issueCountry,
        customerIDType: this.customerIDType
      },
      requestDataPep: {
        'processInstanceId': this.$route.params.processInstanceId,
        'group_name': this.$route.params.groupName,
        'api_header': [],
        'api_param': [],
        'api_formdata': [],
        'api_json': ''
      },
      form6: {
        customerId: this.customerId,
        issueCountry: this.issueCountry,
        customerIDType: this.customerIDType,
        nationality: this.nationality
      },
      formrules6: {
        customerId: [{ required: true, message: this.$t('vcs.workflow.customerIDerror'), trigger: 'blur' }]
      },
      requestData7: {
        'processInstanceId': this.$route.params.processInstanceId,
        'group_name': this.$route.params.groupName,
        'api_header': [],
        'api_param': [],
        'api_formdata': [],
        'api_json': ''
      },
      form7: {
        customerId: '',
        issueCountry: '',
        firstName: '',
        lastName: '',
        customerIdType: '',
        dateOfBirth: '',
        gender: '',
        nationality: '',
        foreignCountryTaxNumber: '',
        validityPeriod: null,
        report: '',
        remark: ''
      },
      formrules7: {
        customerId: [{ required: true, message: this.$t('vcs.workflow.customerIDerror'), trigger: 'blur' }],
        accountNumber: [{ required: true, message: this.$t('vcs.workflow.accountNumbererror'), trigger: 'blur' }]
      },
      requestData8: {
        'processInstanceId': this.$route.params.processInstanceId,
        'group_name': this.$route.params.groupName,
        'api_header': [],
        'api_param': [],
        'api_formdata': [],
        'api_json': ''
      },
      form8: {
        customerId: '',
        issueCountry: '',
        firstName: '',
        lastName: '',
        customerIdType: '',
        dateOfBirth: '',
        gender: '',
        nationality: '',
        taxpayerIdentificationNumber: '',
        validityPeriod: null,
        report: '',
        remark: ''
      },
      formrules8: {
        customerId: [{ required: true, message: this.$t('vcs.workflow.customerIDerror'), trigger: 'blur' }],
        accountNumber: [{ required: true, message: this.$t('vcs.workflow.accountNumbererror'), trigger: 'blur' }]
      },
      requestData9: {
        'processInstanceId': this.$route.params.processInstanceId,
        'group_name': this.$route.params.groupName,
        'api_header': [],
        'api_param': [],
        'api_formdata': [],
        'api_json': ''
      },
      form9: {
        developerID: developerID,
        developername: '',
        email: ''
      },
      formrules9: {
        developerID: [{ required: true, message: this.$t('vcs.workflow.developerIDerror'), trigger: 'blur' }],
        developername: [{ required: true, message: this.$t('vcs.workflow.developernameerrorerror'), trigger: 'blur' }]
      },
      requestData10: {
        'processInstanceId': this.$route.params.processInstanceId,
        'group_name': this.$route.params.groupName,
        'api_header': [],
        'api_param': [],
        'api_formdata': [],
        'api_json': ''
      },
      form10: {
        // developerID: developerID,
        loginName: '',
        loginPwd: '',
        customerNumber: ''
      },
      formrules10: {
        // developerID:[{ required: true, message: this.$t("vcs.workflow.developerIDerror"), trigger: 'blur' }],
        loginName: [{ required: true, message: this.$t('vcs.workflow.loginNameerror'), trigger: 'blur' }],
        loginPwd: [{ required: true, message: this.$t('vcs.workflow.loginPwderror'), trigger: 'blur' }],
        customerNumber: [{ required: true, message: this.$t('vcs.workflow.customerNumbererror'), trigger: 'blur' }]
      },
      requestData11: {
        'processInstanceId': this.$route.params.processInstanceId,
        'group_name': this.$route.params.groupName,
        'api_header': [],
        'api_param': [],
        'api_formdata': [],
        'api_json': ''
      },
      form11: {
        customerId: this.customerId,
        address: this.mailingAddress,
        addressReview: ''
      },
      formrules11: {
        customerId: [{ required: true, message: this.$t('vcs.workflow.customerIDerror'), trigger: 'blur' }],
        address: [{ required: true, message: this.$t('vcs.workflow.residentialaddresserror'), trigger: 'blur' }],
        addressReview: [{ required: true, message: this.$t('vcs.workflow.addressReviewError'), trigger: 'blur' }]
      },
      requestData12: {
        'processInstanceId': this.$route.params.processInstanceId,
        'group_name': this.$route.params.groupName,
        'api_header': [],
        'api_param': [],
        'api_formdata': [],
        'api_json': ''
      },
      form12: {
        customerId: this.customerId,
        serverPath: ''
      },
      formrules12: {
        customerId: [{ required: true, message: this.$t('vcs.workflow.customerIDerror'), trigger: 'blur' }]
      },
      fileList12: [],
      latestResponse: {},
      latestNode: '',
      isFinished: true,
      content: '',
      ccyOptions: ['HKD'],
      proofOfAddress: [],
      relAccountNumberShow: false,
      limitAllocation: {
        activeName: 'customerDemographic'
      },
      customerInfoData: {
        id: '',
        customerId: '',
        customerIdType: '',
        issueCountry: '',
        creditCardNumber: '',
        personalIncome: '',
        familyIncome: '',
        suggestedLimit: '',
        totalAsset: '',
        totalLiability: '',
        nextLimitChangeReview: '',
        remarks: ''
      },
      informationFormConfig: informationFormConfig,
      requestDataLimitAllocation: {
        'processInstanceId': this.$route.params.processInstanceId,
        'group_name': this.$route.params.groupName,
        'api_header': [],
        'api_param': [],
        'api_formdata': [],
        'api_json': ''
      },
      kycProcessRequestData: {
        applicationId: null,
        processInstanceId: '',
        processInstanceStatus: 'Done'
      }
    }
  },
  async created() {
    this.loading = true
    const { data } = await getCreditCardApplicationDetails(this.$route.params.applicationId)

    this.accountOpenApplication = data
    this.nationality = data.nationality
    this.customerId = data.customerId
    this.customerIDType = data.customerIdType
    this.issueCountry = data.issueCountry

    this.proofOfAddress.push(data.addressProofPicturePath)

    const { data: suggestedLimit } = await initialLimit({
      personalIncome: this.accountOpenApplication.personalIncome,
      customerId: this.accountOpenApplication.customerId,
      customerIdType: this.accountOpenApplication.customerIdType,
      issueCountry: this.accountOpenApplication.issueCountry
    })

    this.customerInfoData.id = this.accountOpenApplication.id
    this.customerInfoData.customerId = this.accountOpenApplication.customerId
    this.customerInfoData.customerIdType = this.accountOpenApplication.customerIdType
    this.customerInfoData.issueCountry = this.accountOpenApplication.issueCountry
    this.customerInfoData.creditCardNumber = this.accountOpenApplication.creditCardNumber
    this.customerInfoData.personalIncome = this.accountOpenApplication.personalIncome
    this.customerInfoData.familyIncome = this.accountOpenApplication.familyIncome
    this.customerInfoData.suggestedLimit = suggestedLimit
    this.customerInfoData.totalAsset = 0
    this.customerInfoData.totalLiability = 0
    this.customerInfoData.nextLimitChangeReview = ''

    this.kycProcessRequestData = {
      applicationId: this.accountOpenApplication.id,
      processInstanceId: this.$route.params.processInstanceId,
      processInstanceStatus: 'Done'
    }

    this.getWorkflowDetail()
  },
  methods: {
    submit1: function() {
      const _this = this
      _this.$refs['form1'].validate(valid => {
        if (valid) {
          _this.loading = true
          _this.form1.customerNumber = _this.customerNumber
          _this.requestData1.api_json = _this.form1
          _this.requestData1.nationality = _this.nationality
          executeFlow(_this.requestData1)
            .then(() => {
              _this.loading = false
              _this.getWorkflowDetail()
            })
            .catch(error => {
              _this.loading = false
              const res = error.data.response
              _this.$alert('<pre style="white-space: pre-wrap">' + JSON.stringify(res, null, 2) + '</pre>', _this.$t('vcs.common.error'), { dangerouslyUseHTMLString: true })
            })
        }
      })
    },
    submit12: function() {
      const _this = this
      _this.$refs['form12'].validate(valid => {
        if (valid) {
          _this.loading = true

          _this.form12.customerId = _this.customerId
          _this.form12.customerIDType = _this.customerIDType
          _this.form12.serverPath = _this.accountOpenApplication.customerIdPicturePath

          _this.requestData12.api_json = _this.form12
          _this.requestData12.nationality = _this.nationality

          executeFlow(_this.requestData12)
            .then(() => {
              _this.loading = false
              _this.getWorkflowDetail()
            })
            .catch(error => {
              _this.loading = false
              let res = error.data.response
              if (error.data.response.data.customerMasterInfoEntity) {
                res = {
                  ...error.data.response,
                  data: { ...error.data.response.data, ...error.data.response.data.customerMasterInfoEntity }
                }
                delete res.data.customerMasterInfoEntity
              }
              _this.$alert('<pre style="white-space: pre-wrap">' + JSON.stringify(res, null, 2) + '</pre>', _this.$t('vcs.common.tip'), { dangerouslyUseHTMLString: true })
            })
        }
      })
    },
    submit11: function() {
      const _this = this
      _this.$refs['form11'].validate(valid => {
        if (valid) {
          _this.loading = true
          _this.form11.customerId = _this.customerId
          _this.form11.customerIDType = _this.customerIDType
          _this.form11.address = _this.mailingAddress
          _this.requestData11.api_json = _this.form11
          _this.requestData11.nationality = _this.nationality
          executeFlow(_this.requestData11)
            .then(() => {
              _this.loading = false
              _this.getWorkflowDetail()
            })
            .catch(error => {
              _this.loading = false
              const res = error.data.response
              res.tip = _this.$t('vcs.workflow.invalidAddress')
              _this.$alert('<pre style="white-space: pre-wrap">' + JSON.stringify(res, null, 2) + '</pre>', _this.$t('vcs.common.tip'), { dangerouslyUseHTMLString: true })
            })
        }
      })
    },
    submit10: function() {
      const _this = this
      _this.$refs['form10'].validate(valid => {
        if (valid) {
          _this.loading = true
          _this.form10.customerNumber = _this.customerNumber
          _this.requestData10.api_json = _this.form10
          _this.requestData10.nationality = _this.nationality
          executeFlow(_this.requestData10)
            .then(response => {
              _this.loading = false
              _this.$alert('<p>' + _this.$t('vcs.workflow.createSuccess') + '</p>' + '<p>Username:' + response.data.response.data.loginName + '</p><p>Username:' + response.data.response.data.loginPwd + '</p>', _this.$t('vcs.common.tip'), { dangerouslyUseHTMLString: true })
              _this.getWorkflowDetail()
              // TODO 用户已存在：展示登录名，用户新增成功：展示登录名密码。
              kycProcess(this.kycProcessRequestData)
                .then(res => {
                  console.log(res)
                })
            })
            .catch(error => {
              _this.loading = false
              const res = error.data.response
              _this.$alert('<pre style="white-space: pre-wrap">' + JSON.stringify(res, null, 2) + '</pre>', _this.$t('vcs.common.tip'), { dangerouslyUseHTMLString: true })
            })
        }
      })
    },
    submit9: function() {
      const _this = this
      _this.$refs['form9'].validate(valid => {
        if (valid) {
          _this.loading = true
          _this.form9.customerIDType = _this.customerIDType
          _this.requestData9.api_json = _this.form9
          _this.requestData9.nationality = _this.nationality
          executeFlow(_this.requestData9)
            .then(() => {
              _this.loading = false
              _this.getWorkflowDetail()
            })
            .catch(error => {
              _this.loading = false
              let res = error.data.response
              if (error.data.response.data.customerMasterInfoEntity) {
                res = {
                  ...error.data.response,
                  data: { ...error.data.response.data, ...error.data.response.data.customerMasterInfoEntity }
                }
                delete res.data.customerMasterInfoEntity
              }
              _this.$alert('<pre style="white-space: pre-wrap">' + JSON.stringify(res, null, 2) + '</pre>', _this.$t('vcs.common.tip'), { dangerouslyUseHTMLString: true })
            })
        }
      })
    },
    submit8: function() {
      const _this = this
      _this.$refs.customizeFATCAForm.validate((valid, data) => {
        if (valid) {
          _this.loading = true
          _this.requestData8.api_json = handlerCommonData(data)
          _this.requestData8.nationality = _this.nationality
          executeFlow(_this.requestData8)
            .then(() => {
              _this.loading = false
              _this.getWorkflowDetail()
            })
            .catch(error => {
              _this.loading = false
              const res = error.data.response
              _this.$alert('<pre style="white-space: pre-wrap">' + JSON.stringify(res, null, 2) + '</pre>', _this.$t('vcs.common.tip'), { dangerouslyUseHTMLString: true })
            })
        }
      })
    },
    submit7: function() {
      const _this = this
      _this.$refs.customizeCRSForm.validate((valid, data) => {
        if (valid) {
          _this.loading = true
          _this.requestData7.api_json = handlerCommonData(data)
          _this.requestData7.nationality = _this.nationality
          executeFlow(_this.requestData7)
            .then(() => {
              _this.loading = false
              _this.getWorkflowDetail()
            })
            .catch(error => {
              _this.loading = false
              const res = error.data.response
              _this.$alert('<pre style="white-space: pre-wrap">' + JSON.stringify(res, null, 2) + '</pre>', _this.$t('vcs.common.tip'), { dangerouslyUseHTMLString: true })
            })
        }
      })
    },
    submit6: function() {
      const _this = this
      // _this.$refs['form6'].validate(valid => {
      //   if (valid) {
      _this.loading = true
      _this.form6.customerId = _this.customerId
      _this.form6.customerIDType = _this.customerIDType
      _this.form6.issueCountry = _this.issueCountry
      _this.form6.nationality = _this.nationality
      _this.requestData6.api_json = _this.form6
      _this.requestData6.nationality = _this.nationality

      executeFlow(_this.requestData6)
        .then(() => {
          _this.loading = false
          _this.getWorkflowDetail()
        })
        .catch(error => {
          _this.loading = false
          let res = error.data.response
          if (error.data.response.data.customerMasterInfoEntity) {
            res = {
              tip: 'The customer is in the Sanction list.', ...error.data.response,
              data: { ...error.data.response.data, ...error.data.response.data.customerMasterInfoEntity }
            }
            delete res.data.customerMasterInfoEntity
          } else {
            res = { tip: 'The customer\'s nationality is in the Sanction list.', ...error.data.response }
          }
          _this.$alert('<pre style="white-space: pre-wrap">' + JSON.stringify(res, null, 2) + '</pre>', _this.$t('vcs.common.tip'), { dangerouslyUseHTMLString: true })
        })
      //   }
      // })
    },
    submitPep: function() {
      const _this = this
      // _this.$refs['formPep'].validate(valid => {
      //   if (valid) {
      _this.loading = true
      _this.formPep.customerId = _this.customerId
      _this.formPep.customerIDType = _this.customerIDType
      _this.formPep.issueCountry = _this.issueCountry
      _this.requestDataPep.api_json = _this.formPep
      _this.requestDataPep.nationality = _this.nationality
      executeFlow(_this.requestDataPep)
        .then(() => {
          _this.loading = false
          _this.getWorkflowDetail()
        })
        .catch(error => {
          _this.loading = false
          let res = error.data.response
          if (error.data.response.data.customerMasterInfoEntity) {
            res = {
              tip: 'The customer is in the PEP list.', ...error.data.response,
              data: { ...error.data.response.data, ...error.data.response.data.customerMasterInfoEntity }
            }
            delete res.data.customerMasterInfoEntity
          }
          _this.$alert('<pre style="white-space: pre-wrap">' + JSON.stringify(res, null, 2) + '</pre>', _this.$t('vcs.common.tip'), { dangerouslyUseHTMLString: true })
        })
      //   }
      // })
    },
    submit5: function() {
      const _this = this
      // _this.$refs['form5'].validate(valid => {
      //   if (valid) {
      _this.loading = true
      _this.form5.customerId = _this.customerId
      _this.form5.customerIDType = _this.customerIDType
      _this.form5.issueCountry = _this.issueCountry
      _this.requestData5.api_json = _this.form5
      _this.requestData5.nationality = _this.nationality
      executeFlow(_this.requestData5)
        .then(() => {
          _this.loading = false
          _this.getWorkflowDetail()
        })
        .catch(error => {
          _this.loading = false
          let res = error.data.response
          if (error.data.response.data.customerMasterInfoEntity) {
            res = {
              tip: 'The customer is in the Blacklist.', ...error.data.response,
              data: { ...error.data.response.data, ...error.data.response.data.customerMasterInfoEntity }
            }
            delete res.data.customerMasterInfoEntity
          }
          _this.$alert('<pre style="white-space: pre-wrap">' + JSON.stringify(res, null, 2) + '</pre>', _this.$t('vcs.common.tip'), { dangerouslyUseHTMLString: true })
        })
      //   }
      // })
    },
    exportData4(file, fileList) {
      const vue = this
      if (!file) {
        return
      }

      const isLt2M = file.size / 1024 / 1024 < 1
      if (!isLt2M) {
        this.$message.error(vue.$t('vcs.workflow.fileerror'))
        vue.fileList4 = []
        return isLt2M
      } else {
        vue.form4.file = file.raw
        vue.fileList4 = fileList.slice(-1) // 取最后一个元素
      }
    },
    submit4: function() {
      const _this = this
      debugger
      _this.$refs['form4'].validate(valid => {
        if (valid) {
          _this.loading = true
          const request = new FormData()
          request.append('file', _this.form4.file)
          request.append('api_formdata', JSON.stringify({
            customerId: _this.customerId,
            customerIDType: _this.customerIDType
          }))
          request.append('processInstanceId', _this.$route.params.processInstanceId)
          request.append('group_name', 'eKYC')
          request.append('nationality', _this.nationality)
          executeUploadFlow(request)
            .then(() => {
              _this.loading = false
              _this.getWorkflowDetail()
            })
            .catch(error => {
              _this.loading = false
              let res = error.data.response
              if (error.data.response.data.customerMasterInfoEntity) {
                res = {
                  ...error.data.response,
                  data: { ...error.data.response.data, ...error.data.response.data.customerMasterInfoEntity }
                }
                delete res.data.customerMasterInfoEntity
              }
              _this.$alert('<pre style="white-space: pre-wrap">' + JSON.stringify(res, null, 2) + '</pre>', _this.$t('vcs.common.tip'), { dangerouslyUseHTMLString: true })
            })
        }
      })
    },
    submit2: function() {
      const _this = this
      if (!_this.form2.acknowledge) {
        _this.$alert(_this.$t('vcs.workflow.acknowledgeerror'), _this.$t('vcs.common.tip'))
        return
      }
      _this.loading = true
      const request = {
        customerId: _this.customerId,
        customerIDType: _this.customerIDType,
        acknowledge: _this.form2.acknowledge ? 'Y' : 'N'
      }
      _this.form2.customerId = _this.customerId
      // _this.form2.customerId = _this.customerId
      _this.requestData2.api_json = request
      _this.requestData2.nationality = _this.nationality
      executeFlow(_this.requestData2)
        .then(() => {
          _this.loading = false
          _this.getWorkflowDetail()
        })
        .catch(error => {
          _this.loading = false
          let res = error.data.response
          if (error.data.response.data.customerMasterInfoEntity) {
            res = {
              ...error.data.response,
              data: { ...error.data.response.data, ...error.data.response.data.customerMasterInfoEntity }
            }
            delete res.data.customerMasterInfoEntity
          }
          _this.$alert('<pre style="white-space: pre-wrap">' + JSON.stringify(res, null, 2) + '</pre>', _this.$t('vcs.common.tip'), { dangerouslyUseHTMLString: true })
        })
    },
    getWorkflowDetail: function() {
      const _this = this
      _this.loading = true
      getFlowSort({
        processInstanceId: _this.$route.params.processInstanceId,
        nationality: _this.nationality
      })
        .then(response => {
          _this.loading = false
          _this.getReponse()
          const nodeList = [{ name: 'Capture Demographic', state: 'completed' }]
          _this.tableData = nodeList.concat(response.data.nodeSort)
        })
        .catch(error => {
          _this.loading = false
          _this.$message(error.message, 'error')
        })
    },
    getReponse() {
      const _this = this
      _this.loading = true
      getResponse(_this.$route.params.processInstanceId)
        .then(response => {
          _this.loading = false
          _this.responseTableData = response.data

          _this.responseTableData.forEach(item => {
            if (item.api_name === 'Capture Demographic') {
              _this.customerId = this.accountOpenApplication.customerId
              _this.mailingAddress = '714000'
              _this.customerIDType = this.accountOpenApplication.customerIdType
              _this.issueCountry = this.accountOpenApplication.issueCountry
              _this.nationality = this.accountOpenApplication.nationality

              _this.form7.customerId = this.accountOpenApplication.customerId
              _this.form7.issueCountry = this.accountOpenApplication.issueCountry
              _this.form7.firstName = this.accountOpenApplication.firstName
              _this.form7.lastName = this.accountOpenApplication.lastName
              _this.form7.customerIdType = this.accountOpenApplication.customerIdType
              _this.form7.dateOfBirth = this.accountOpenApplication.dateOfBirth
              _this.form7.gender = this.accountOpenApplication.gender
              _this.form7.nationality = this.accountOpenApplication.nationality

              _this.form8.customerId = this.accountOpenApplication.customerId
              _this.form8.issueCountry = this.accountOpenApplication.issueCountry
              _this.form8.firstName = this.accountOpenApplication.firstName
              _this.form8.lastName = this.accountOpenApplication.lastName
              _this.form8.customerIdType = this.accountOpenApplication.customerIdType
              _this.form8.dateOfBirth = this.accountOpenApplication.dateOfBirth
              _this.form8.gender = this.accountOpenApplication.gender
              _this.form8.nationality = this.accountOpenApplication.nationality
            }
            if (item.api_name === 'Check Blacklist') {
              const request = JSON.parse(item.api_request)
              _this.form5 = request
            }
            if (item.api_name === 'Check Sanction') {
              const request = JSON.parse(item.api_request)
              _this.form6 = request
            }
            if (item.api_name === 'Verify Address') {
              const request = JSON.parse(item.api_request)
              _this.form11 = request
            }
            if (item.api_name === 'Verify Biometrics') {
              const request = JSON.parse(item.api_request)
              _this.form12 = request
            }
            if (item.api_name === 'FATCA') {
              const request = JSON.parse(item.api_request)
              _this.form8 = {
                customerId: request.customerMasterInfoEntity.customerId,
                issueCountry: request.customerMasterInfoEntity.issueCountry,
                firstName: request.customerMasterInfoEntity.firstName,
                lastName: request.customerMasterInfoEntity.lastName,
                customerIdType: request.customerMasterInfoEntity.customerIdType,
                dateOfBirth: request.customerMasterInfoEntity.dateOfBirth,
                gender: request.customerMasterInfoEntity.gender,
                nationality: request.customerMasterInfoEntity.nationality,
                taxpayerIdentificationNumber: request.taxpayerIdentificationNumber,
                validityPeriod: request.validityPeriod,
                report: request.report,
                remark: request.remark
              }
            }
            if (item.api_name === 'CRS') {
              const request = JSON.parse(item.api_request)
              this.form7 = {
                customerId: request.customerMasterInfoEntity.customerId,
                issueCountry: request.customerMasterInfoEntity.issueCountry,
                firstName: request.customerMasterInfoEntity.firstName,
                lastName: request.customerMasterInfoEntity.lastName,
                customerIdType: request.customerMasterInfoEntity.customerIdType,
                dateOfBirth: request.customerMasterInfoEntity.dateOfBirth,
                gender: request.customerMasterInfoEntity.gender,
                nationality: request.customerMasterInfoEntity.nationality,
                foreignCountryTaxNumber: request.foreignCountryTaxNumber,
                validityPeriod: request.validityPeriod,
                report: request.report,
                remark: request.remark
              }
            }
            if (item.api_name === 'Credit Limit Allocation') {
              const request = JSON.parse(item.api_request)
              const responseData = JSON.parse(item.api_response)
              if (responseData.code === '200') {
                _this.customerNumber = responseData.data.customernumber
                _this.form1.branchCode = responseData.data.branchcode
              }
              _this.customerInfoData.suggestedLimit = request.suggestedLimit
              _this.customerInfoData.nextLimitChangeReview = request.nextLimitChangeReview
              _this.customerInfoData.remarks = request.remarks
            }
            if (item.api_name === 'Acknowledge T&C') {
              const request = JSON.parse(item.api_request)
              _this.form2 = request
              _this.form2.acknowledge = _this.form2.acknowledge === 'Y'
            }
            if (item.api_name === 'Capture Signature') {
              const request = JSON.parse(item.api_request)
              _this.form4 = request
            }
            if (item.api_name === 'Set Pin') {
              const request = JSON.parse(item.api_request)
              _this.form10 = request
            }
            if (item.api_name === 'userCreation') {
              const request = JSON.parse(item.api_request)
              _this.form9 = request
              // _this.form10.developerID = request.developerID
            }
            _this.latestResponse = JSON.parse(item.api_response)
            _this.latestNode = item.api_name
          })
          _this.isFinished = true
          _this.tableData.forEach(item => {
            if (item.state === 'running') {
              _this.isFinished = false
            }
          })
          if (_this.isFinished) {
            // 运行结束
            // _this.$alert(`<p>${_this.$t("vcs.workflow.finishTip")}</p>`,_this.$t("vcs.workflow.complete"),{
            //   dangerouslyUseHTMLString: true,
            //   callback: action => {
            //     // vue.back()
            //   }
            // })
          }

          _this.initNodeStatus(_this.tableData)
        })
        .catch(error => {
          _this.loading = false
          _this.$message(error.message, 'error')
        })
    },
    viewReponse(node) {
      if (this.responseTableData.length <= 0) {
        this.$alert('Response not found')
        return
      }
      this.responseTableData.forEach(item => {
        if (this.currentNode === item.api_name) {
          this.response = JSON.stringify(JSON.parse(item.api_response), null, 2)
          this.responseDialog = true
        }
      })
    },
    initNodeStatus(list) {
      const vue = this
      for (var key in this.pageList) {
        this.pageList[key].show = false
        this.pageList[key].notLastNode = false
        this.pageList[key].notLastNode = false
        this.pageList[key].running = false
        this.pageList[key].unexecuted = false
        this.pageList[key].completed = false
      }
      list.forEach((item, index) => {
        switch (item.name) {
          case 'Check Blacklist': {
            if (index === 0) {
              vue.pageList.CheckBlacklist.notFirstNode = false
              vue.pageList.CheckBlacklist.notLastNode = true
            } else if ((index + 1) === list.length) {
              vue.pageList.CheckBlacklist.notFirstNode = true
              vue.pageList.CheckBlacklist.notLastNode = false
            } else {
              vue.pageList.CheckBlacklist.notFirstNode = true
              vue.pageList.CheckBlacklist.notLastNode = true
            }
            if (item.state === 'running') {
              vue.currentNode = item.name
              vue.pageList.CheckBlacklist.show = true
              vue.pageList.CheckBlacklist.running = true
              setTimeout(function() {
                vue.submit5()
              }, 1000)
            } else if (item.state === 'unexecuted') {
              vue.pageList.CheckBlacklist.unexecuted = true
            } else if (item.state === 'completed') {
              vue.pageList.CheckBlacklist.completed = true
            }
            break
          }
          case 'Check Sanction': {
            if (index === 0) {
              vue.pageList.CheckSanction.notFirstNode = false
              vue.pageList.CheckSanction.notLastNode = true
            } else if ((index + 1) === list.length) {
              vue.pageList.CheckSanction.notFirstNode = true
              vue.pageList.CheckSanction.notLastNode = false
            } else {
              vue.pageList.CheckSanction.notFirstNode = true
              vue.pageList.CheckSanction.notLastNode = true
            }
            if (item.state === 'running') {
              vue.currentNode = item.name
              vue.pageList.CheckSanction.show = true
              vue.pageList.CheckSanction.running = true
              setTimeout(function() {
                vue.submit6()
              }, 1000)
            } else if (item.state === 'unexecuted') {
              vue.pageList.CheckSanction.unexecuted = true
            } else if (item.state === 'completed') {
              vue.pageList.CheckSanction.completed = true
            }
            break
          }
          case 'PEP': {
            if (index === 0) {
              vue.pageList.PEP.notFirstNode = false
              vue.pageList.PEP.notLastNode = true
            } else if ((index + 1) === list.length) {
              vue.pageList.PEP.notFirstNode = true
              vue.pageList.PEP.notLastNode = false
            } else {
              vue.pageList.PEP.notFirstNode = true
              vue.pageList.PEP.notLastNode = true
            }
            if (item.state === 'running') {
              vue.currentNode = item.name
              vue.pageList.PEP.show = true
              vue.pageList.PEP.running = true
              setTimeout(function() {
                vue.submitPep()
              }, 1000)
            } else if (item.state === 'unexecuted') {
              vue.pageList.PEP.unexecuted = true
            } else if (item.state === 'completed') {
              vue.pageList.PEP.completed = true
            }
            break
          }
          case 'Verify Address': {
            if (index === 0) {
              vue.pageList.VerifyAddress.notFirstNode = false
              vue.pageList.VerifyAddress.notLastNode = true
            } else if ((index + 1) === list.length) {
              vue.pageList.VerifyAddress.notFirstNode = true
              vue.pageList.VerifyAddress.notLastNode = false
            } else {
              vue.pageList.VerifyAddress.notFirstNode = true
              vue.pageList.VerifyAddress.notLastNode = true
            }
            if (item.state === 'running') {
              vue.currentNode = item.name
              vue.pageList.VerifyAddress.show = true
              vue.pageList.VerifyAddress.running = true
            } else if (item.state === 'unexecuted') {
              vue.pageList.VerifyAddress.unexecuted = true
            } else if (item.state === 'completed') {
              vue.pageList.VerifyAddress.completed = true
            }
            break
          }
          case 'Verify Biometrics': {
            if (index === 0) {
              vue.pageList.VerifyBiometrics.notFirstNode = false
              vue.pageList.VerifyBiometrics.notLastNode = true
            } else if ((index + 1) === list.length) {
              vue.pageList.VerifyBiometrics.notFirstNode = true
              vue.pageList.VerifyBiometrics.notLastNode = false
            } else {
              vue.pageList.VerifyBiometrics.notFirstNode = true
              vue.pageList.VerifyBiometrics.notLastNode = true
            }
            if (item.state === 'running') {
              vue.currentNode = item.name
              vue.pageList.VerifyBiometrics.show = true
              vue.pageList.VerifyBiometrics.running = true
              setTimeout(function() {
                vue.submit12()
              }, 1000)
            } else if (item.state === 'unexecuted') {
              vue.pageList.VerifyBiometrics.unexecuted = true
            } else if (item.state === 'completed') {
              vue.pageList.VerifyBiometrics.completed = true
            }
            break
          }
          case 'FATCA': {
            if (index === 0) {
              vue.pageList.FATCA.notFirstNode = false
              vue.pageList.FATCA.notLastNode = true
            } else if ((index + 1) === list.length) {
              vue.pageList.FATCA.notFirstNode = true
              vue.pageList.FATCA.notLastNode = false
            } else {
              vue.pageList.FATCA.notFirstNode = true
              vue.pageList.FATCA.notLastNode = true
            }
            if (item.state === 'running') {
              vue.currentNode = item.name
              vue.pageList.FATCA.show = true
              vue.pageList.FATCA.running = true
            } else if (item.state === 'unexecuted') {
              vue.pageList.FATCA.unexecuted = true
            } else if (item.state === 'completed') {
              vue.pageList.FATCA.completed = true
            }
            break
          }
          case 'CRS': {
            if (index === 0) {
              vue.pageList.CRS.notFirstNode = false
              vue.pageList.CRS.notLastNode = true
            } else if ((index + 1) === list.length) {
              vue.pageList.CRS.notFirstNode = true
              vue.pageList.CRS.notLastNode = false
            } else {
              vue.pageList.CRS.notFirstNode = true
              vue.pageList.CRS.notLastNode = true
            }
            if (item.state === 'running') {
              vue.currentNode = item.name
              vue.pageList.CRS.show = true
              vue.pageList.CRS.running = true
            } else if (item.state === 'unexecuted') {
              vue.pageList.CRS.unexecuted = true
            } else if (item.state === 'completed') {
              vue.pageList.CRS.completed = true
            }
            break
          }
          case 'Credit Limit Allocation': {
            if (index === 0) {
              vue.pageList.CreditLimitAllocation.notFirstNode = false
              vue.pageList.CreditLimitAllocation.notLastNode = true
            } else if ((index + 1) === list.length) {
              vue.pageList.CreditLimitAllocation.notFirstNode = true
              vue.pageList.CreditLimitAllocation.notLastNode = false
            } else {
              vue.pageList.CreditLimitAllocation.notFirstNode = true
              vue.pageList.CreditLimitAllocation.notLastNode = true
            }
            if (item.state === 'running') {
              vue.currentNode = item.name
              vue.pageList.CreditLimitAllocation.show = true
              vue.pageList.CreditLimitAllocation.running = true
            } else if (item.state === 'unexecuted') {
              vue.pageList.CreditLimitAllocation.unexecuted = true
            } else if (item.state === 'completed') {
              vue.pageList.CreditLimitAllocation.completed = true
            }
            break
          }
          case 'Account Opening': {
            vue.form1.customerNumber = vue.customerNumber
            if (index === 0) {
              vue.pageList.AccountOpening.notFirstNode = false
              vue.pageList.AccountOpening.notLastNode = true
            } else if ((index + 1) === list.length) {
              vue.pageList.AccountOpening.notFirstNode = true
              vue.pageList.AccountOpening.notLastNode = false
            } else {
              vue.pageList.AccountOpening.notFirstNode = true
              vue.pageList.AccountOpening.notLastNode = true
            }
            if (item.state === 'running') {
              vue.currentNode = item.name
              vue.pageList.AccountOpening.show = true
              vue.pageList.AccountOpening.running = true
            } else if (item.state === 'unexecuted') {
              vue.pageList.AccountOpening.unexecuted = true
            } else if (item.state === 'completed') {
              vue.pageList.AccountOpening.completed = true
            }
            break
          }
          case 'Acknowledge T&C': {
            if (index === 0) {
              vue.pageList.AcknowledgeTC.notFirstNode = false
              vue.pageList.AcknowledgeTC.notLastNode = true
            } else if ((index + 1) === list.length) {
              vue.pageList.AcknowledgeTC.notFirstNode = true
              vue.pageList.AcknowledgeTC.notLastNode = false
            } else {
              vue.pageList.AcknowledgeTC.notFirstNode = true
              vue.pageList.AcknowledgeTC.notLastNode = true
            }
            if (item.state === 'running') {
              vue.currentNode = item.name
              vue.pageList.AcknowledgeTC.show = true
              vue.pageList.AcknowledgeTC.running = true
            } else if (item.state === 'unexecuted') {
              vue.pageList.AcknowledgeTC.unexecuted = true
            } else if (item.state === 'completed') {
              vue.pageList.AcknowledgeTC.completed = true
            }
            break
          }
          case 'Capture Signature': {
            if (index === 0) {
              vue.pageList.CaptureSignature.notFirstNode = false
              vue.pageList.CaptureSignature.notLastNode = true
            } else if ((index + 1) === list.length) {
              vue.pageList.CaptureSignature.notFirstNode = true
              vue.pageList.CaptureSignature.notLastNode = false
            } else {
              vue.pageList.CaptureSignature.notFirstNode = true
              vue.pageList.CaptureSignature.notLastNode = true
            }
            if (item.state === 'running') {
              vue.currentNode = item.name
              vue.pageList.CaptureSignature.show = true
              vue.pageList.CaptureSignature.running = true
            } else if (item.state === 'unexecuted') {
              vue.pageList.CaptureSignature.unexecuted = true
            } else if (item.state === 'completed') {
              vue.pageList.CaptureSignature.completed = true
            }
            break
          }
          case 'Set Pin': {
            vue.form10.customerNumber = vue.customerNumber
            if (index === 0) {
              vue.pageList.SetPin.notFirstNode = false
              vue.pageList.SetPin.notLastNode = true
            } else if ((index + 1) === list.length) {
              vue.pageList.SetPin.notFirstNode = true
              vue.pageList.SetPin.notLastNode = false
            } else {
              vue.pageList.SetPin.notFirstNode = true
              vue.pageList.SetPin.notLastNode = true
            }
            if (item.state === 'running') {
              vue.currentNode = item.name
              vue.pageList.SetPin.show = true
              vue.pageList.SetPin.running = true
            } else if (item.state === 'unexecuted') {
              vue.pageList.SetPin.unexecuted = true
            } else if (item.state === 'completed') {
              vue.pageList.SetPin.completed = true
            }
            break
          }
          case 'userCreation': {
            if (index === 0) {
              vue.pageList.userCreation.notFirstNode = false
              vue.pageList.userCreation.notLastNode = true
            } else if ((index + 1) === list.length) {
              vue.pageList.userCreation.notFirstNode = true
              vue.pageList.userCreation.notLastNode = false
            } else {
              vue.pageList.userCreation.notFirstNode = true
              vue.pageList.userCreation.notLastNode = true
            }
            if (item.state === 'running') {
              vue.currentNode = item.name
              vue.pageList.userCreation.show = true
              vue.pageList.userCreation.running = true
            } else if (item.state === 'unexecuted') {
              vue.pageList.userCreation.unexecuted = true
            } else if (item.state === 'completed') {
              vue.pageList.userCreation.completed = true
            }
            break
          }
        }
      })
      if (vue.latestNode && vue.isFinished) {
        vue.currentNode = vue.latestNode
        vue.showNode(vue.latestNode)
      }
    },
    showNode(name) {
      for (var key in this.pageList) {
        this.pageList[key].show = false
      }
      this.currentNode = name
      switch (name) {
        case 'Check Blacklist': {
          this.pageList.CheckBlacklist.show = true
          break
        }
        case 'Check Sanction': {
          this.pageList.CheckSanction.show = true
          break
        }
        case 'PEP': {
          this.pageList.PEP.show = true
          break
        }
        case 'Verify Address': {
          this.pageList.VerifyAddress.show = true
          break
        }
        case 'Verify Biometrics': {
          this.pageList.VerifyBiometrics.show = true
          break
        }
        case 'FATCA': {
          this.pageList.FATCA.show = true
          break
        }
        case 'CRS': {
          this.pageList.CRS.show = true
          break
        }
        case 'Credit Limit Allocation': {
          this.pageList.CreditLimitAllocation.show = true
          break
        }
        case 'Account Opening': {
          this.pageList.AccountOpening.show = true
          break
        }
        case 'Acknowledge T&C': {
          this.pageList.AcknowledgeTC.show = true
          break
        }
        case 'Capture Signature': {
          this.pageList.CaptureSignature.show = true
          break
        }
        case 'Set Pin': {
          this.pageList.SetPin.show = true
          break
        }
        case 'userCreation': {
          this.pageList.userCreation.show = true
          break
        }
      }
    },
    prev(name) {
      for (let i = 0; i < this.tableData.length; i++) {
        if (this.tableData[i].name === name) {
          for (var key in this.pageList) {
            this.pageList[key].show = false
          }
          this.showNode(this.tableData[i - 1].name)
        }
      }
    },
    next(name) {
      let stopFlag = false
      for (let i = 0; i < this.tableData.length; i++) {
        if (!stopFlag) {
          if (this.tableData[i].name === this.latestNode && this.isFinished) {
            stopFlag = true
            this.$alert(this.$t('vcs.workflow.hasbeenlatestnode'))
            break
          }
          if (this.tableData[i].name === name) {
            for (var key in this.pageList) {
              this.pageList[key].show = false
            }
            this.showNode(this.tableData[i + 1].name)
            break
          }
        }
      }
    },
    back() {
      this.$emit('showPage', 'workflowhistory')
    },
    submitCreditLimitAllocation() {
      const _this = this
      _this.$refs.customizeForm.validate((valid, data) => {
        if (valid) {
          _this.loading = true
          _this.requestDataLimitAllocation.api_json = {
            id: data.id,
            nextLimitChangeReview: data.nextLimitChangeReview,
            suggestedLimit: data.suggestedLimit,
            limitAllocationType: 'KYC',
            remarks: data.remarks
          }
          _this.requestDataLimitAllocation.nationality = _this.nationality
          executeFlow(_this.requestDataLimitAllocation)
            .then(response => {
              _this.loading = false
              _this.customerNumber = response.data.response.data.customernumber
              _this.form10.customerNumber = response.data.response.data.customernumber
              _this.getWorkflowDetail()
            })
            .catch(error => {
              _this.loading = false
              const res = error.data.response
              _this.$alert('<pre style="white-space: pre-wrap">' + JSON.stringify(res, null, 2) + '</pre>', this.$t('vcs.common.tip'), { dangerouslyUseHTMLString: true })
            })
        } else {
          _this.limitAllocation.activeName = 'customerCreditInformation'
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.workflow-manage {
  .operating {
    margin-bottom: 5px;
  }

  .search-input {
    width: 240px;
  }

  .params-table {
    width: 100%;
  }

  .addParamForm, .editParamForm {
    width: 400px;
    margin: 0 auto;
  }

  .privacy-policy {
    padding: 0 100px;
    margin: 30px auto;
    font-size: 14px;
    line-height: 1.5;
  }
}
</style>

<style lang="scss">
.workflow-manage {
  .el-dialog__body {
    padding-bottom: 0;
  }

  .el-step__title {
    line-height: 20px;
    margin-bottom: 15px;
    height: 40px;
  }

  .el-step__description {
    font-size: 16px;
  }

  .el-card__header .clearfix {
    white-space: nowrap;
  }

  input[type=color], input[type=date], input[type=datetime-local], input[type=datetime], input[type=email], input[type=month], input[type=number], input[type=password], input[type=search], input[type=tel], input[type=text], input[type=time], input[type=url], input[type=week], select, textarea {
    margin-bottom: 0;
  }
}
</style>
