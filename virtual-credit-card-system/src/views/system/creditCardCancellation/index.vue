<template>
  <div v-loading="loading" class="business-container">
    <customize-card :title="$t('vcs.router.cancellationManagement')" :show-back-btn="false">
      <!-- 搜索表单 -->
      <el-form
        ref="searchForm"
        :model="searchForm"
        size="small"
        label-width="160px"
        label-position="left"
        :show-message="false"
      >
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item :label="$t('vcs.creditCardMaintenance.creditCardNumber')" prop="creditCardNumber">
              <el-input
                v-model="searchForm.creditCardNumber"
                :placeholder="$t('vcs.creditCardMaintenance.creditCardNumberPlaceholder')"
                clearable
                class="search-form-item"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="$t('vcs.creditCardApplication.customerId')" prop="customerId">
              <el-input
                v-model="searchForm.customerId"
                :placeholder="$t('vcs.creditCardApplication.customerIdPlaceholder')"
                clearable
                class="search-form-item"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="$t('vcs.creditCardMaintenance.applicationStatus')" prop="applicationStatus">
              <el-select
                v-model="searchForm.applicationStatus"
                :placeholder="$t('vcs.creditCardMaintenance.applicationStatusPlaceholder')"
                clearable
                class="search-form-item"
              >
                <el-option v-for="item in applicationStatusOptions" :key="item.value" :label="$t(item.label)" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="$t('vcs.common.createDate')" prop="dateRange">
              <el-date-picker
                v-model="searchForm.dateRange"
                type="daterange"
                :range-separator="$t('vcs.common.to')"
                value-format="timestamp"
                :start-placeholder="$t('vcs.common.startDate')"
                :end-placeholder="$t('vcs.common.endDate')"
                :default-time="['00:00:00', '23:59:59']"
                unlink-panels
                class="search-form-item"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <!-- 操作按钮 -->
            <el-button type="primary" size="small" style="margin-bottom: 18px" icon="el-icon-search" @click="getItems">
              {{ $t('vcs.common.search') }}
            </el-button>
            <el-button icon="el-icon-refresh" size="small" @click="resetForm('searchForm')">
              {{ $t('vcs.common.reset') }}
            </el-button>
          </el-col>
        </el-row>

      </el-form>

      <!-- 创建和批量删除按钮 -->
      <div class="data-action-button-group">
        <div />

        <!-- 刷新按钮 -->
        <el-button icon="el-icon-refresh" size="small" circle @click="getItems" />
      </div>

      <!-- 表格 -->
      <el-card shadow="never">
        <el-table v-loading="tableLoading" :data="items" stripe>
          <el-table-column type="index" label="#" align="center" />
          <el-table-column
            :label="$t('vcs.creditCardMaintenance.creditCardNumber')"
            prop="creditCardNumber"
            align="center"
          />
          <el-table-column
            :label="$t('vcs.creditCardMaintenance.issueCountry')"
            prop="issueCountry"
            align="center"
          >
            <template slot-scope="scope">
              {{ $t(getLabel("COUNTRY", scope.row.issueCountry)) }}
            </template>
          </el-table-column>
          <el-table-column
            :label="$t('vcs.creditCardMaintenance.customerIdType')"
            prop="customerIdType"
            align="center"
          >
            <template slot-scope="scope">
              {{ $t(getLabel("CUSTOMER_ID_TYPE", scope.row.customerIdType)) }}
            </template>
          </el-table-column>
          <el-table-column
            :label="$t('vcs.creditCardMaintenance.customerId')"
            prop="customerId"
            align="center"
          />
          <el-table-column
            :label="$t('vcs.creditCardMaintenance.customerName')"
            prop="customerName"
            align="center"
          />
          <el-table-column :label="$t('vcs.creditCardMaintenance.cancellationReason')" prop="reason" align="center" show-overflow-tooltip />
          <el-table-column :label="$t('vcs.creditCardMaintenance.remarks')" prop="remarks" align="center" show-overflow-tooltip />
          <el-table-column
            :label="$t('vcs.creditCardMaintenance.applicationStatus')"
            prop="applicationStatus"
            align="center"
          >
            <template slot-scope="scope">
              {{ $t(getLabel("APPLICATION_STATUS", scope.row.applicationStatus)) }}
            </template>
          </el-table-column>
          <el-table-column
            :label="$t('vcs.creditCardMaintenance.createDate')"
            prop="createDate"
            align="center"
          >
            <template slot-scope="{ row }">
              {{ moment(row.createDate).format('YYYY-MM-DD HH:mm:ss') }}
            </template>
          </el-table-column>
          <el-table-column :label="$t('vcs.common.operate')" align="center" width="200" fixed="right">
            <template slot-scope="{ row }">
              <el-button
                type="text"
                size="mini"
                text
                icon="el-icon-view"
                @click="viewDialog(row)"
              >
                {{ $t('vcs.common.view') }}
              </el-button>
              <el-button
                type="text"
                size="mini"
                text
                icon="el-icon-search"
                :disabled="row.applicationStatus !== 'P'"
                @click="reviewDialog(row)"
              >
                {{ $t('vcs.common.review') }}
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>

      <div class="table-footer">
        <el-pagination
          :current-page.sync="searchForm.currentPage"
          :page-sizes="[20, 50, 100, 500, 1000]"
          :page-size.sync="searchForm.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="getItems()"
          @current-change="getItems()"
        />
      </div>
      <!-- 查看的对话框 -->
      <el-dialog
        :title="$t('vcs.creditCardMaintenance.viewCancellationDetails')"
        :visible.sync="viewDialogVisible"
        :close-on-press-escape="false"
        :close-on-click-modal="false"
        width="60%"
      >
        <descriptions :view-row="viewRow" />
      </el-dialog>

      <review
        :visible.sync="reviewDialogVisible"
        :options="cancelStatusOptions"
        label-width="120px"
        :confirm-review-button-loading.sync="confirmReviewButtonLoading"
        :title="$t('vcs.creditCardMaintenance.cancellationReview')"
        :review-result-label="$t('vcs.creditCardMaintenance.cancelStatus')"
        :review-result-placeholder="$t('vcs.creditCardMaintenance.cancelStatusPlaceholder')"
        @handler-validated="handlerReview($event)"
      />

      <el-dialog
        :title="isUpdate ? $t('vcs.creditCardMaintenance.editCancellationDetails') : $t('vcs.creditCardMaintenance.createCancellationDetails')"
        :visible.sync="dataFormDialogVisible"
        :close-on-press-escape="false"
        :close-on-click-modal="false"
        width="60%"
        top="10vh"
        destroy-on-close
      >
        <customize-form
          ref="customizeForm"
          form-ref="dataForm"
          size="small"
          :model="formData"
          :form-item-config="formConfig"
          label-width="220px"
          label-position="left"
        />

        <span slot="footer" class="dialog-footer">
          <el-button size="small" @click="dataFormDialogVisible = false"> {{ $t('vcs.common.cancel') }}</el-button>
          <el-button
            v-if="!isUpdate"
            type="primary"
            size="small"
            :loading="confirmButtonLoading"
            @click="handlerCreateData()"
          >{{ $t('vcs.common.confirm') }}</el-button>
          <el-button
            v-if="isUpdate"
            type="primary"
            size="small"
            :loading="confirmButtonLoading"
            @click="handlerUpdateData()"
          >{{ $t('vcs.common.update') }}</el-button>
        </span>
      </el-dialog>
    </customize-card>
  </div>
</template>

<script>
import Descriptions from '@/components/Descriptions/Descriptions.vue'
import CustomizeForm from '@/components/CustomizeForm/CustomizeForm.vue'
import {
  createCreditCardMaintenance,
  getCreditCardCancellationPage,
  getCreditCardCancellationReview,
  updateCreditCardMaintenance
} from '@/api/system/creditCardMaintenance'
import { formConfig } from '@/views/system/creditCardCancellation/form-config'
import { applicationStatusOptions, cancelStatusOptions, countryOptions, customerIdTypeOptions, getLabel } from '@/data'
import moment from 'moment'
import Review from '@/views/system/components/Review.vue'
import CustomizeCard from '@/components/CustomizeCard/index.vue'

export default {
  name: 'CreditCardMaintenance',
  components: { CustomizeCard, Review, CustomizeForm, Descriptions },
  data() {
    return {
      loading: false,
      tableLoading: false,
      viewDialogVisible: false,
      confirmButtonLoading: false,
      reviewDialogVisible: false,
      confirmReviewButtonLoading: false,
      moment,
      dataFormDialogVisible: false,
      isUpdate: false,
      formConfig: formConfig,
      cancelStatusOptions,
      customerIdTypeOptions,
      applicationStatusOptions,
      countryOptions,
      searchForm: {
        currentPage: 1,
        pageSize: 20,
        dateRange: [],
        creditCardNumber: '',
        customerId: '',
        applicationStatus: ''
      },
      viewRow: [],
      formData: {},
      items: [],
      total: 0,
      reviewId: 0
    }
  },
  mounted() {
    this.getItems()
  },
  methods: {
    getLabel,
    handlerReview(row) {
      getCreditCardCancellationReview({
        creditCardMaintenanceId: this.reviewId,
        cancelStatus: row.reviewResult,
        comment: row.comment
      })
        .then((res) => {
          const { msg } = res
          this.$message.success(msg)
          this.getItems()
          this.reviewDialogVisible = false
        })
        .finally(() => {
          this.confirmReviewButtonLoading = false
        })
    },
    getItems() {
      this.tableLoading = true

      const requestData = { ...this.searchForm }

      if (requestData.dateRange) {
        requestData.createDateFrom = this.searchForm.dateRange[0]
        requestData.createDateTo = this.searchForm.dateRange[1]
      } delete requestData.dateRange
      getCreditCardCancellationPage(requestData)
        .then((res) => {
          const { total, items } = res.data

          this.items = items
          this.total = total
        })
        .finally(() => {
          this.tableLoading = false
        })
    },
    handlerCreateData() {
      this.$refs.customizeForm.validate((valid, data) => {
        if (valid) {
          const formData = data
          this.confirmButtonLoading = true
          createCreditCardMaintenance(formData)
            .then((res) => {
              const { msg } = res
              this.$message.success(msg)
              this.getItems()
              this.dataFormDialogVisible = false
            })
            .finally(() => {
              this.confirmButtonLoading = false
            })
        }
      })
    },
    handlerUpdateData() {
      this.$refs.customizeForm.validate((valid, data) => {
        if (valid) {
          const formData = data
          this.confirmButtonLoading = true
          updateCreditCardMaintenance(formData)
            .then((res) => {
              const { msg } = res
              this.$message.success(msg)
              this.getItems()
              this.dataFormDialogVisible = false
            })
            .finally(() => {
              this.confirmButtonLoading = false
            })
        }
      })
    },
    viewDialog(row) {
      const description = { ...row }
      this.viewRow = this.formConfig
        .filter(item => {
          if (row.applicationStatus !== 'P') {
            return true
          }
          return item.prop !== 'approvalStaffNumber' && item.prop !== 'approvalDate' && item.prop !== 'approvalComment'
        })
        .map(item => {
          let value = description[item.prop]
          if (item.format) {
            return { key: this.$t(item.label), value: moment(Number(value)).format(item.format.replace('dd', 'DD').replace('yyyy', 'YYYY')) }
          } else {
            if (item.options) {
              const option = item.options.filter(option => option.value === value)
              if (option && option.length) {
                value = this.$t(option[0].label)
              }
            }
            return { key: this.$t(item.label), value: value }
          }
        })
      this.viewDialogVisible = true
    },
    reviewDialog(row) {
      this.reviewId = row.id
      this.reviewDialogVisible = true
    },
    resetForm(formName) {
      this.$refs[formName].resetFields()
    }
  }
}
</script>

<style lang="scss" scoped>
.business-container {
  .form-item {
    width: 100%;
  }
}
</style>
