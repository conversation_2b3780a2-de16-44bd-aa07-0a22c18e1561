<template>
  <div v-loading="loading" class="business-container">
    <customize-card :title="$t('vcs.router.applicationManagement')" :show-back-btn="false">
      <!-- 搜索表单 -->
      <el-form
        ref="searchForm"
        :model="searchForm"
        size="small"
        label-width="150px"
        label-position="left"
        :show-message="false"
      >
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item :label="$t('vcs.creditCardApplication.customerId')" prop="customerId">
              <el-input
                v-model="searchForm.customerId"
                :placeholder="$t('vcs.creditCardApplication.customerIdPlaceholder')"
                clearable
                class="search-form-item"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="$t('vcs.creditCardApplication.customerIdType')" prop="customerIdType">
              <el-select
                v-model="searchForm.customerIdType"
                :placeholder="$t('vcs.creditCardApplication.customerIdTypePlaceholder')"
                clearable
                class="search-form-item"
              >
                <el-option
                  v-for="item in customerIdTypeOptions"
                  :key="item.value"
                  :label="$t(item.label)"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="$t('vcs.creditCardApplication.issueCountry')" prop="issueCountry">
              <el-select
                v-model="searchForm.issueCountry"
                :placeholder="$t('vcs.creditCardApplication.issueCountryPlaceholder')"
                clearable
                class="search-form-item"
              >
                <el-option
                  v-for="item in countryOptions"
                  :key="item.value"
                  :label="$t(item.label)"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item :label="$t('vcs.creditCardApplication.applicationStatus')" prop="applicationStatus">
              <el-select
                v-model="searchForm.applicationStatus"
                :placeholder="$t('vcs.creditCardApplication.applicationStatusPlaceholder')"
                clearable
                class="search-form-item"
              >
                <el-option
                  v-for="item in applicationStatusOptions"
                  :key="item.value"
                  :label="$t(item.label)"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <!-- 操作按钮 -->
            <el-button type="primary" size="small" style="margin-bottom: 18px" icon="el-icon-search" @click="getItems">
              {{ $t('vcs.common.search') }}
            </el-button>
            <el-button icon="el-icon-refresh" size="small" @click="resetForm('searchForm')">{{
              $t('vcs.common.reset')
            }}
            </el-button>
          </el-col>
        </el-row>
      </el-form>

      <!-- 创建和批量删除按钮 -->
      <div class="data-action-button-group">
        <div />

        <!-- 刷新按钮 -->
        <el-button icon="el-icon-refresh" size="small" circle @click="getItems" />
      </div>

      <!-- 表格 -->
      <el-card shadow="never">
        <el-table v-loading="tableLoading" :data="items" stripe>
          <el-table-column type="index" label="#" align="center" />
          <el-table-column
            :label="$t('vcs.creditCardApplication.customerName')"
            prop="firstName"
            align="center"
            width="150"
          >
            <template slot-scope="scope">
              {{ `${scope.row.firstName} ${scope.row.lastName}` }}
            </template>
          </el-table-column>
          <el-table-column
            :label="$t('vcs.creditCardApplication.chineseName')"
            prop="chineseName"
            align="center"
            width="150"
          />
          <el-table-column :label="$t('vcs.creditCardApplication.gender')" prop="gender" align="center" width="70">
            <template slot-scope="scope">
              {{ $t(getLabel("GENDER", scope.row.gender)) }}
            </template>
          </el-table-column>
          <el-table-column
            :label="$t('vcs.creditCardApplication.customerId')"
            prop="customerId"
            align="center"
            width="180"
          />
          <el-table-column
            :label="$t('vcs.creditCardApplication.customerIdType')"
            prop="customerIdType"
            align="center"
            width="150"
          >
            <template slot-scope="scope">
              {{ $t(getLabel("CUSTOMER_ID_TYPE", scope.row.customerIdType)) }}
            </template>
          </el-table-column>
          <el-table-column
            :label="$t('vcs.creditCardApplication.issueCountry')"
            prop="issueCountry"
            align="center"
            width="150"
          >
            <template slot-scope="scope">
              {{ $t(getLabel("COUNTRY", scope.row.issueCountry)) }}
            </template>
          </el-table-column>
          <el-table-column
            :label="$t('vcs.creditCardApplication.nationality')"
            prop="nationality"
            align="center"
            width="150"
          >
            <template slot-scope="scope">
              {{ $t(getLabel("COUNTRY", scope.row.nationality)) }}
            </template>
          </el-table-column>
          <el-table-column
            :label="$t('vcs.creditCardApplication.applicationStatus')"
            prop="applicationStatus"
            align="center"
            width="150"
          >
            <template slot-scope="scope">
              {{ $t(getLabel("APPLICATION_STATUS", scope.row.applicationStatus)) }}
            </template>
          </el-table-column>
          <el-table-column
            :label="$t('vcs.creditCardApplication.processInstanceStatus')"
            prop="processInstanceStatus"
            align="center"
            width="170"
          >
            <template slot-scope="scope">
              {{ $t(getLabel("PROCESS_INSTANCE_STATUS", scope.row.processInstanceStatus)) }}
            </template>
          </el-table-column>
          <el-table-column
            :label="$t('vcs.creditCardApplication.applicationDate')"
            prop="applicationDate"
            align="center"
            width="150"
          >
            <template slot-scope="scope">
              {{ moment(Number(scope.row.applicationDate)).format('YYYY-MM-DD HH:mm:ss') }}
            </template>
          </el-table-column>
          <el-table-column :label="$t('vcs.common.operate')" align="center" width="200" fixed="right">
            <template slot-scope="{ row }">
              <el-button
                v-if="hasPermission('business:creditcard:apply-management:kyc')"
                :disabled="row.processInstanceStatus ==='Done' && row.processInstanceId === ''"
                type="text"
                size="mini"
                text
                @click="openKycDialog(row)"
              >
                {{ $t('vcs.common.kyc') }}
              </el-button>
              <el-button
                v-if="hasPermission('business:creditcard:apply-management:kyc') && false"
                type="text"
                size="mini"
                text
                :disabled="row.limitAllocationStatus ==='Y' || row.processInstanceId !== ''"
                @click="openLimitAllocationDialog(row)"
              >
                {{ $t('vcs.common.limitAllocation') }}
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>

      <div class="table-footer">
        <el-pagination
          :current-page.sync="searchForm.currentPage"
          :page-sizes="[20, 50, 100, 500, 1000]"
          :page-size.sync="searchForm.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="getItems()"
          @current-change="getItems()"
        />
      </div>

      <el-dialog
        :title="$t('vcs.creditCardApplication.limitAllocationDialog.title')"
        :visible.sync="limitAllocationDialog.visible"
        :close-on-press-escape="false"
        :close-on-click-modal="false"
        width="80%"
        top="10vh"
      >
        <el-tabs v-model="limitAllocationDialog.activeName" @tab-click="handlerLimitAllocationTagsClick">
          <el-tab-pane
            :label="$t('vcs.creditCardApplication.limitAllocationDialog.customerDemographic')"
            name="customerDemographic"
          >
            <customerDemographic :account-open-application="limitAllocationDialog.accountOpenApplication" />
          </el-tab-pane>
          <el-tab-pane
            :label="$t('vcs.creditCardApplication.limitAllocationDialog.customerPortfolio')"
            name="customerPortfolio"
          >{{
            $t('vcs.creditCardApplication.limitAllocationDialog.customerPortfolio')
          }}
          </el-tab-pane>
          <el-tab-pane
            :label="$t('vcs.creditCardApplication.limitAllocationDialog.customerCreditInformation')"
            name="customerCreditInformation"
          >
            <customerCreditInformation
              :customer-info="limitAllocationDialog.customerInfoData"
              :total-asset="limitAllocationDialog.totalAsset"
              :total-liability="limitAllocationDialog.totalLiability"
              :suggested-limit="limitAllocationDialog.suggestedLimit"
              :next-limit-change-review="limitAllocationDialog.nextLimitChangeReview"
              @handler-confirm="handlerLimitAllocationConfirm"
              @handler-cancel="handlerLimitAllocationClosed"
            />
          </el-tab-pane>
        </el-tabs>
      </el-dialog>
    </customize-card>

    <el-dialog
      :title="$t('vcs.creditCardApplication.selectWorkflowDialog.title')"
      :visible.sync="selectWorkflowDialog.visible"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      width="40%"
      top="10vh"
    >
      <el-table
        :data="selectWorkflowDialog.items"
        style="width: 100%"
        stripe
        highlight-current-row
        :header-cell-style="{'background': '#BEEEDE', 'color': '#333'}"
      >
        <el-table-column type="index" label="No." width="50" />
        <el-table-column prop="name" :label="$t('vcs.creditCardApplication.selectWorkflowDialog.workflowName')" min-width="150" />
        <el-table-column prop="group_name" :label="$t('vcs.creditCardApplication.selectWorkflowDialog.workflowDesc')" min-width="150" />
        <el-table-column :label="$t('vcs.common.operate')" width="150">
          <template slot-scope="scope">
            <el-button type="text" size="small" @click="startWorkflowProcess(scope.row)">{{ $t("vcs.common.confirm") }}</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<script>
import moment from 'moment'
import {
  createCreditCardApplication,
  getCreditCardApplicationPage,
  kycProcess,
  updateCreditCardApplication
} from '@/api/system/creditCardApplication'
import { formConfig } from '@/views/system/creditCardApplication/form-config'
import { applicationStatusOptions, countryOptions, customerIdTypeOptions, getLabel } from '@/data'
import CustomerDemographic from '@/views/system/components/CustomerDemographic.vue'
import CustomerCreditInformation from '@/views/system/components/CustomerCreditInformation.vue'
import { initialLimit, openLimitAllocation } from '@/api/system/creditCardCalculation'
import CustomizeCard from '@/components/CustomizeCard/index.vue'
import { mapGetters } from 'vuex'
import { getWorkflow, startProcess } from '@/api/workflow/workflow'
import { reference } from '@/api/system/creditCardCustomer'
import { hasPermission } from '@/utils/auth'

export default {
  name: 'CreditCardApplication',
  components: { CustomizeCard, CustomerDemographic, CustomerCreditInformation },
  data() {
    return {
      loading: false,
      tableLoading: false,
      viewDialogVisible: false,
      confirmButtonLoading: false,
      dataFormDialogVisible: false,
      isUpdate: false,
      formConfig: formConfig,
      customerIdTypeOptions,
      countryOptions,
      applicationStatusOptions,
      moment,
      getLabel,
      searchForm: {
        currentPage: 1,
        pageSize: 20,
        customerId: '',
        customerIdType: '',
        issueCountry: '',
        applicationStatus: ''
      },
      viewRow: [],
      formData: {},
      items: [],
      total: 0,
      limitAllocationDialog: {
        visible: false,
        activeName: 'customerDemographic',
        accountOpenApplication: {},
        customerInfoData: {},
        totalAsset: 0,
        totalLiability: 0,
        suggestedLimit: 0,
        nextLimitChangeReview: ''
      },
      selectWorkflowDialog: {
        visible: false,
        items: [],
        accountOpenApplication: {}
      }
    }
  },
  computed: {
    ...mapGetters([
      'userDetails'
    ])
  },
  mounted() {
    this.getItems()
  },
  methods: {
    hasPermission,
    getItems() {
      this.tableLoading = true

      const requestData = { ...this.searchForm }

      if (requestData.dateRange) {
        requestData.createDateFrom = this.searchForm.dateRange[0]
        requestData.createDateTo = this.searchForm.dateRange[1]
      }
      delete requestData.dateRange
      getCreditCardApplicationPage(requestData)
        .then((res) => {
          const { total, items } = res.data

          this.items = items
          this.total = total
        })
        .finally(() => {
          this.tableLoading = false
        })
    },
    handlerCreateData() {
      this.$refs.customizeForm.validate((valid, data) => {
        if (valid) {
          const formData = data
          this.confirmButtonLoading = true
          createCreditCardApplication(formData)
            .then((res) => {
              const { msg } = res
              this.$message.success(msg)
              this.getItems()
              this.dataFormDialogVisible = false
            })
            .finally(() => {
              this.confirmButtonLoading = false
            })
        }
      })
    },
    handlerUpdateData() {
      this.$refs.customizeForm.validate((valid, data) => {
        if (valid) {
          const formData = data
          this.confirmButtonLoading = true
          updateCreditCardApplication(formData)
            .then((res) => {
              const { msg } = res
              this.$message.success(msg)
              this.getItems()
              this.dataFormDialogVisible = false
            })
            .finally(() => {
              this.confirmButtonLoading = false
            })
        }
      })
    },
    resetForm(formName) {
      this.$refs[formName].resetFields()
    },
    openKycDialog(row) {
      const applicationId = row.id
      const processInstanceId = row.processInstanceId
      const groupName = 'vcsKYC'
      if (processInstanceId) {
        this.$router.push(`/apply-management/workflow/${processInstanceId}/${groupName}/${applicationId}`)
      } else {
        this.selectWorkflowDialog.accountOpenApplication = { ...row }
        this.getWorkflowList()
        this.selectWorkflowDialog.visible = true
      }
    },
    openLimitAllocationDialog(row) {
      const calculationInitialLimit = {
        personalIncome: row.personalIncome,
        customerId: row.customerId,
        customerIdType: row.customerIdType,
        issueCountry: row.issueCountry
      }
      initialLimit(calculationInitialLimit)
        .then(res => {
          const { data } = res
          this.limitAllocationDialog.suggestedLimit = data
        })
        .finally(() => {
          this.limitAllocationDialog.accountOpenApplication = { ...row }
          this.limitAllocationDialog.customerInfoData = {
            id: row.id,
            customerId: row.customerId,
            customerIdType: row.customerIdType,
            issueCountry: row.issueCountry,
            creditCardNumber: row.creditCardNumber,
            personalIncome: row.personalIncome,
            familyIncome: row.familyIncome
          }
          this.limitAllocationDialog.visible = true
        })
    },
    handlerLimitAllocationTagsClick(tab, event) {
      console.log(tab, event)
    },
    handlerLimitAllocationClosed() {
      this.limitAllocationDialog.visible = false
    },
    handlerLimitAllocationConfirm(formData) {
      openLimitAllocation(formData)
        .then((res) => {
          const { msg } = res
          this.$message.success(msg)
          this.getItems()
          this.limitAllocationDialog.visible = false
        })
    },
    getWorkflowList() {
      getWorkflow()
        .then(res => {
          this.selectWorkflowDialog.items = res.data.filter(item => item.group_name === 'vcsKYC')
        })
    },
    startWorkflowProcess(row) {
      const requestData = {
        processDefinitionId: row.process_definition_id,
        staffNumber: this.userDetails.loginName,
        customerId: this.selectWorkflowDialog.accountOpenApplication.customerId,
        api_request: JSON.stringify(this.selectWorkflowDialog.accountOpenApplication),
        api_response: JSON.stringify({ 'code': '200', 'msg': 'Operation Successed', 'data': null }),
        api_name: 'Capture Demographic',
        group: row.group_name
      }
      startProcess(requestData)
        .then(res => {
          const { data: processInstanceId } = res
          const { group_name: groupName } = row
          const { id: applicationId } = this.selectWorkflowDialog.accountOpenApplication
          reference({
            applicationId: applicationId
          })
            .then(() => {
              kycProcess({
                applicationId: applicationId,
                processInstanceId: processInstanceId,
                processInstanceStatus: 'Doing'
              })
                .then(() => {
                  this.$router.push(`/apply-management/workflow/${processInstanceId}/${groupName}/${applicationId}`)
                })
            })
        })
    }
  }
}
</script>
