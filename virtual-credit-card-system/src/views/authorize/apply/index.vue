<template>
  <div class="apply-container">
    <div class="header">
      <img class="logo" :src="logo" alt="logo" style="" @click="officialWebsite()">
      <h1>{{ $t('vcs.common.platformName') }}</h1>
    </div>

    <customize-form
      ref="customizeForm"
      form-ref="dataForm"
      size="small"
      :model="formData"
      :form-item-config="formConfig"
      label-width="255px"
      label-position="left"
      class="apply-form"
      @update="validateCustomerIdByCustomerType($event)"
    >
      <div class="form-header">
        <div class="title-container">
          <h3 class="title">{{ $t('vcs.router.creditCardApply') }}</h3>
        </div>

        <div class="language-select">
          <language />
        </div>
      </div>

      <template #credit-cards>
        <el-button type="primary" size="mini" @click="addHoldCreditCard()">{{ $t('vcs.common.add') }}</el-button>
        <div
          v-for="(creditInfo, index) in formData.creditInfoDtoList"
          :key="index"
          style="display: flex; align-items: center"
        >
          <el-form ref="creditCardInfo" :model="creditInfo" label-position="left" size="small" style="flex: 1">
            <el-row :gutter="10" style="margin-left: 0">
              <el-col :span="8" style="padding-left: 0">
                <el-form-item
                  :label="$t('vcs.authorize.creditCardNumber')"
                  prop="creditCardNumber"
                  :rules="[{ required: true, message: $t('vcs.authorize.creditCardNumberIsRequired') }]"
                >
                  <el-input
                    v-model="creditInfo.creditCardNumber"
                    :placeholder="$t('vcs.authorize.creditCardNumberPlaceholder')"
                    class="form-item"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item
                  :label="$t('vcs.authorize.limit')"
                  prop="creditCardLimit"
                  :rules="[{ required: true, message: $t('vcs.authorize.limitIsRequired') }]"
                >
                  <el-input-number
                    v-model="creditInfo.creditCardLimit"
                    :min="0"
                    :max="9999999999999.99"
                    :controls="false"
                    :step="0.01"
                    step-strictly
                    :placeholder="$t('vcs.authorize.limitPlaceholder')"
                    class="form-item"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item
                  :label="$t('vcs.authorize.expiryDate')"
                  prop="creditCardExpiryDate"
                  :rules="[{ required: true, message: $t('vcs.authorize.expiryDateIsRequired') }]"
                >
                  <el-date-picker
                    v-model="creditInfo.creditCardExpiryDate"
                    type="date"
                    format="yyyy-MM-dd"
                    value-format="timestamp"
                    :placeholder="$t('vcs.authorize.expiryDatePlaceholder')"
                    class="form-item"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
          <el-button
            v-if="formData.noOfYearsHaveFirstCreditCard <= 0 || formData.creditInfoDtoList.length > 1"
            size="small"
            style="margin-left: 10px; margin-top: 13px; width: 60px;"
            @click="deleteHoldCreditCard(index)"
          >{{ $t('vcs.common.delete') }}
          </el-button>
        </div>
      </template>

      <template #customer-id-picture>
        <el-upload
          class="avatar-uploader"
          action="/"
          :show-file-list="false"
          :auto-upload="false"
          accept="image/*"
          :on-change="handleCustomerIdPictureChange"
        >
          <img v-if="customerIdPictureUrl" :src="customerIdPictureUrl" class="avatar" alt="customer-id">
          <i v-else class="el-icon-plus avatar-uploader-icon" />
        </el-upload>
      </template>

      <template #address-proof-picture>
        <el-upload
          class="avatar-uploader"
          action="/"
          :show-file-list="false"
          :auto-upload="false"
          accept="image/*"
          :on-change="handleAddressProofPictureChange"
        >
          <img v-if="addressProofPictureUrl" :src="addressProofPictureUrl" class="avatar" alt="customer-id">
          <i v-else class="el-icon-plus avatar-uploader-icon" />
        </el-upload>
      </template>
    </customize-form>

    <div style="text-align: right; padding: 20px 50px 100px">
      <el-button class="apply-button" size="small" @click.native.prevent="goLink('/authorize/login')">
        {{ $t('vcs.authorize.backLogin') }}
      </el-button>
      <el-button size="small" class="apply-button" @click.native.prevent="reset">
        {{ $t('vcs.common.reset') }}
      </el-button>
      <el-button
        :loading="confirmButtonLoading"
        type="primary"
        size="small"
        class="apply-button"
        @click.native.prevent="handleValidate"
      >
        {{ $t('vcs.authorize.apply') }}
      </el-button>
    </div>

  </div>
</template>

<script>
import Language from '@/components/Language/index.vue'
import CustomizeForm from '@/components/CustomizeForm/CustomizeForm.vue'
import { formConfig } from '@/views/authorize/apply/form-config'
import { creditCardOpenApplication } from '@/api/business/credit-card'
import { uploadImage } from '@/api/business/file'
import { cardIdValidator } from '@/utils/validate'
import { getMessage } from '@/utils/i18n'

export default {
  name: 'CreditCardApply',
  components: { CustomizeForm, Language },
  data() {
    return {
      loading: false,
      logo: require(`@/assets/images/logo-${process.env.VUE_APP_TEMPORARY_PERMISSION}.png`),
      formConfig,
      formData: {
        'noOfCreditCardsCarry': 0,
        'creditInfoDtoList': [],
        // 'yearsOfResidence': 0,
        'familyIncome': 0,
        'personalIncome': 0,
        'noOfYearsHaveFirstCreditCard': 0,
        'outstandingLoansAmount': 0,
        'repaymentRatioToIncome': 0,
        'firstName': '',
        'lastName': '',
        'chineseName': '',
        'gender': '',
        'customerIdType': '',
        'customerId': '',
        'issueCountry': '',
        'dateOfBirth': null,
        'nationality': '',
        'phoneNumber': '',
        'address': '',
        // 'mailingAddress': '',
        'maritalStatus': '',
        'permanentResidentStatus': '',
        'industry': '',
        'position': '',
        'companyName': '',
        'bankruptcyHistory': '',
        'pastDueDebts': '',
        'customerIdPicture': '',
        'addressProofPicture': '',
        'remarks': ''
      },
      applyRules: {},
      confirmButtonLoading: false,
      customerIdPictureUrl: '',
      addressProofPictureUrl: ''
    }
  },
  methods: {
    deleteHoldCreditCard(index) {
      this.formData.creditInfoDtoList.splice(index, 1)
      this.formData.noOfCreditCardsCarry = this.formData.creditInfoDtoList.length
    },
    addHoldCreditCard() {
      if (this.formData.noOfCreditCardsCarry >= 20) {
        this.$message.error(this.$t('vcs.authorize.addHoldCreditCardErrorMessage'))
        return
      }
      this.formData.creditInfoDtoList.push({
        creditCardNumber: '',
        creditCardExpiryDate: '',
        creditCardLimit: undefined
      })

      this.formData.noOfCreditCardsCarry = this.formData.creditInfoDtoList.length
    },
    async handleCustomerIdPictureChange(file) {
      const formData = new FormData()
      formData.append('file', file.raw)
      const { data: url } = await uploadImage(formData)

      this.customerIdPictureUrl = url
      this.formData.customerIdPicture = url
    },
    async handleAddressProofPictureChange(file) {
      const formData = new FormData()
      formData.append('file', file.raw)
      const { data: url } = await uploadImage(formData)

      this.addressProofPictureUrl = url
      this.formData.addressProofPicture = url
    },
    handleValidate() {
      this.$refs.customizeForm.validate((valid, data) => {
        if (valid) {
          const fromRefs = this.$refs.creditCardInfo
          if (fromRefs && fromRefs.length) {
            Promise.all(fromRefs.map(item => item.validate())).then(() => {
              this.handleApply(data)
            })
          } else {
            this.handleApply(data)
          }
        }
      })
    },
    handleApply(data) {
      this.confirmButtonLoading = true
      creditCardOpenApplication(data)
        .then((res) => {
          this.reset()
          const { msg } = res
          this.$message.success(msg)
        })
        .finally(() => {
          this.confirmButtonLoading = false
        })
    },
    officialWebsite() {
    },
    goLink(path) {
      this.$router.push({ path: path })
    },
    reset() {
      this.formData.creditInfoDtoList = []
      this.customerIdPictureUrl = ''
      this.addressProofPictureUrl = ''
      this.$refs.customizeForm.reset()
    },
    validateCustomerIdByCustomerType({ prop, value }) {
      if (prop === 'customerIdType') {
        const index = this.formConfig.findIndex(item => item.prop === 'customerId')
        if (index > -1) {
          this.$set(this.formConfig[index], 'rules', [
            { required: true, message: 'vcs.creditCardApplication.customerIdPlaceholder' },
            {
              validator: (rule, value, callback) => {
                const cardType = this.formData.customerIdType
                const issueCountry = this.formData.issueCountry
                if (!cardIdValidator(cardType, issueCountry, value)) {
                  if (issueCountry === 'US' && cardType === 'P') {
                    callback(new Error(getMessage('vcs.creditCardApplication.invalidUsPassportFormat')))
                  } else {
                    callback(new Error(getMessage('vcs.creditCardApplication.invalidCardIdFormat')))
                  }
                } else {
                  callback()
                }
              }
            }
          ])
        }
      }
      if (prop === 'noOfYearsHaveFirstCreditCard') {
        if (value > 0) {
          this.addHoldCreditCard()
        } else {
          this.formData.creditInfoDtoList = []
        }
      }
    }
  }
}
</script>
<style lang="scss">
.apply-form {
  .avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }

  .avatar-uploader .el-upload:hover {
    border-color: #409EFF;
  }

  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 248px;
    height: 140px;
    line-height: 140px;
    text-align: center;
  }

  .avatar {
    width: 248px;
    height: 140px;
    display: block;
  }
}

</style>
<style lang="scss" scoped>
$light_gray: #333;
$width: 1100px;

.apply-container {
  width: $width;
  margin: 0 auto;

  .header {
    padding-top: 50px;
    width: $width;
    margin: 0 auto 20px;
    display: flex;
    align-items: center;

    .logo {
      width: 200px;
      height: auto;
    }

    img {
      cursor: pointer;
    }

    h1 {
      display: inline-block;
      font-size: 32px;
      font-weight: 400;
    }
  }

  .apply-form {
    .form-header {
      padding: 10px;
    }

    padding: 0 40px;

    .title-container {
      position: relative;
      width: 500px;
      margin: 0 auto;

      .title {
        font-size: 26px;
        color: $light_gray;
        margin: 0 auto 10px auto;
        text-align: center;
        font-weight: bold;

        &::before,
        &::after {
          position: absolute;
          top: 46%;
          content: "";
          display: block;
          width: 100px;
          height: 1px;
          background: #333;
        }

        &::before {
          left: 0;
        }

        &::after {
          right: 0;
        }
      }
    }

    .language-select {
      margin-bottom: 10px;
      text-align: right;
    }

    .form-item {
      width: 100%;
    }
  }
}
</style>
