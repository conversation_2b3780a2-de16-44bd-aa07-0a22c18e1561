<template>
  <div v-loading="loading" class="business-container">
    <customize-card :title="$t('vcs.router.creditCardStatement')">
      <template #header-actions>
        <el-button type="primary" size="small" :disabled="creditCardDetails.creditCardStatus !== 'A'" @click="goToRepaymentPage">
          {{ $t('vcs.common.repayment') }}
        </el-button>
      </template>
      <el-card>
        <!-- 搜索表单 -->
        <el-form
          ref="searchForm"
          :model="searchForm"
          size="small"
          label-width="120px"
          label-position="left"
          :show-message="false"
        >
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item :label="$t('vcs.creditCardStatement.statementDate')" prop="dateRange">
                <el-date-picker
                  v-model="searchForm.dateRange"
                  type="monthrange"
                  :range-separator="$t('vcs.common.to')"
                  value-format="timestamp"
                  :start-placeholder="$t('vcs.common.startDate')"
                  :end-placeholder="$t('vcs.common.endDate')"
                  :default-time="['00:00:00', '23:59:59']"
                  unlink-panels
                  class="search-form-item"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <!-- 操作按钮 -->
              <el-button type="primary" size="small" style="margin-bottom: 18px" icon="el-icon-search" @click="getItems">
                {{ $t('vcs.common.search') }}
              </el-button>
              <el-button icon="el-icon-refresh" size="small" @click="resetForm('searchForm')">
                {{ $t('vcs.common.reset') }}
              </el-button>
            </el-col>
          </el-row>

        </el-form>

        <!-- 创建和批量删除按钮 -->
        <div class="data-action-button-group">
          <div />

          <!-- 刷新按钮 -->
          <el-button icon="el-icon-refresh" size="small" circle @click="getItems" />
        </div>

        <!-- 表格 -->
        <el-card shadow="never">
          <el-table v-loading="tableLoading" :data="items" stripe>
            <el-table-column type="index" label="#" align="center" />
            <el-table-column
              :label="$t('vcs.creditCardStatement.statementDate')"
              prop="statementDate"
              align="center"
            >
              <template slot-scope="scope">
                {{ moment(Number(scope.row.statementDate)).format('YYYY-MM-DD') }}
              </template>
            </el-table-column>
            <el-table-column
              :label="$t('vcs.creditCardStatement.statementBalance')"
              prop="statementBalance"
              align="center"
            >
              <template slot-scope="scope">
                {{ `${scope.row.currency} ${Number(scope.row.statementBalance).toFixed(2)}` }}
              </template>
            </el-table-column>
            <el-table-column
              :label="$t('vcs.creditCardStatement.repaymentDueDate')"
              prop="repaymentDueDate"
              align="center"
            >
              <template slot-scope="scope">
                {{ moment(Number(scope.row.repaymentDueDate)).format('YYYY-MM-DD') }}
              </template>
            </el-table-column>
            <el-table-column
              :label="$t('vcs.creditCardStatement.minimumPayment')"
              prop="minimumPayment"
              align="center"
            >
              <template slot-scope="scope">
                {{ `${scope.row.currency} ${Number(scope.row.minimumPayment).toFixed(2)}` }}
              </template>
            </el-table-column>
            <el-table-column
              :label="$t('vcs.creditCardStatement.overdueInterest')"
              prop="overdueInterest"
              align="center"
            >
              <template slot-scope="scope">
                {{ `${scope.row.currency} ${Number(scope.row.overdueInterest).toFixed(2)}` }}
              </template>
            </el-table-column>
            <el-table-column :label="$t('vcs.creditCardStatement.points')" prop="points" align="center" width="150" />
            <el-table-column :label="$t('vcs.common.operate')" align="center" fixed="right">
              <template slot-scope="{ row }">
                <el-button
                  type="text"
                  size="mini"
                  text
                  icon="el-icon-view"
                  @click="viewDialog(row)"
                >
                  {{ $t('vcs.common.view') }}
                </el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-card>

        <div class="table-footer">
          <el-pagination
            :current-page.sync="searchForm.currentPage"
            :page-sizes="[20, 50, 100, 500, 1000]"
            :page-size.sync="searchForm.pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="total"
            @size-change="getItems()"
            @current-change="getItems()"
          />
        </div>
      </el-card>
    </customize-card>
  </div>
</template>

<script>

import CustomizeCard from '@/components/CustomizeCard/index.vue'
import { formConfig } from '@/views/system/creditCardStatement/form-config'
import { getCreditCardMasterDetails, getCreditCardStatementPage } from '@/api/business/credit-card'
import moment from 'moment'
import { mapGetters } from 'vuex'

export default {
  name: 'CreditCardStatement',
  components: { CustomizeCard },
  data() {
    return {
      loading: false,
      tableLoading: false,
      viewDialogVisible: false,
      confirmButtonLoading: false,
      dataFormDialogVisible: false,
      isUpdate: false,
      moment,
      formConfig: formConfig,
      searchForm: {
        currentPage: 1,
        pageSize: 20,
        dateRange: [],
        creditCardNumber: ''
      },
      viewRow: [],
      formData: {},
      items: [],
      total: 0,
      creditCardDetails: {}
    }
  },
  computed: {
    ...mapGetters([
      'language'
    ])
  },
  mounted() {
    this.searchForm.creditCardNumber = this.$route.params.creditCardNumber
    this.getCreditCardMaster()
    this.getItems()
  },
  methods: {
    getCreditCardMaster() {
      this.loading = true
      getCreditCardMasterDetails({ creditCardNumber: this.$route.params.creditCardNumber })
        .then((res) => {
          this.creditCardDetails = res.data
        })
        .finally(() => {
          this.loading = false
        })
    },
    getItems() {
      this.tableLoading = true

      const requestData = { ...this.searchForm }

      if (requestData.dateRange) {
        requestData.createDateFrom = this.searchForm.dateRange[0]
        requestData.createDateTo = this.searchForm.dateRange[1]
      }
      delete requestData.dateRange
      getCreditCardStatementPage(requestData)
        .then((res) => {
          const { total, items } = res.data

          this.items = items
          this.total = total
        })
        .finally(() => {
          this.tableLoading = false
        })
    },
    viewDialog(row) {
      window.open(row.statementStorePath.replace('{language}', this.language), '_black')
    },
    resetForm(formName) {
      this.$refs[formName].resetFields()
    },
    goToRepaymentPage() {
      this.$router.push(`/credit-card/repayment/${this.$route.params.creditCardNumber}`)
    }
  }
}
</script>
