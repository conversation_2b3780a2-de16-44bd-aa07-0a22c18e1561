import CustomizeSelect from '@/components/CustomizeForm/CustomizeSelect.vue'
import { countryOptions, customerIdTypeOptions, lossReportingReasonOptions, reportLossToPoliceOptions } from '@/data'

export const formConfig = [
  {
    'label': 'vcs.creditCardMaintenance.creditCardNumber',
    'placeholder': 'vcs.creditCardMaintenance.creditCardNumberPlaceholder',
    'prop': 'creditCardNumber',
    'component': 'el-input',
    'span': 12,
    'width': '100%',
    'clearable': true,
    'maxlength': 70,
    'disabled': true,
    'rules': [
      { required: true, message: 'vcs.creditCardMaintenance.creditCardNumberPlaceholder' }
    ]
  },
  {
    'label': 'vcs.creditCardApplication.customerId',
    'placeholder': 'vcs.creditCardApplication.customerIdPlaceholder',
    'prop': 'customerId',
    'component': 'el-input',
    'span': 12,
    'width': '100%',
    'clearable': true,
    'maxlength': 35,
    'disabled': true,
    'rules': [
      { required: true, message: 'vcs.creditCardApplication.customerIdPlaceholder' }
    ],
    'hasButton': true
  },
  {
    'label': 'vcs.creditCardApplication.customerIdType',
    'placeholder': 'vcs.creditCardApplication.customerIdTypePlaceholder',
    'prop': 'customerIdType',
    'component': CustomizeSelect,
    'span': 12,
    'width': '100%',
    'clearable': true,
    'disabled': true,
    'options': customerIdTypeOptions,
    'rules': [
      { required: true, message: 'vcs.creditCardApplication.customerIdTypePlaceholder' }
    ]
  },
  {
    'label': 'vcs.creditCardApplication.issueCountry',
    'placeholder': 'vcs.creditCardApplication.issueCountryPlaceholder',
    'prop': 'issueCountry',
    'component': CustomizeSelect,
    'span': 12,
    'width': '100%',
    'clearable': true,
    'disabled': true,
    'options': countryOptions,
    'rules': [
      { required: true, message: 'vcs.creditCardApplication.issueCountryPlaceholder' }
    ]
  },
  {
    'label': 'vcs.creditCardMaintenance.lossReportingReason',
    'placeholder': 'vcs.creditCardMaintenance.lossReportingReasonPlaceholder',
    'prop': 'lossReportingReason',
    'component': CustomizeSelect,
    'span': 12,
    'width': '100%',
    'clearable': true,
    'options': lossReportingReasonOptions,
    'rules': [
      { required: true, message: 'vcs.creditCardMaintenance.lossReportingReasonPlaceholder' }
    ]
  },
  {
    'label': 'vcs.creditCardMaintenance.reportLossDate',
    'placeholder': 'vcs.creditCardMaintenance.reportLossDatePlaceholder',
    'prop': 'reportLossDate',
    'component': 'el-date-picker',
    'type': 'datetime',
    'span': 12,
    'value-format': 'timestamp',
    'format': 'yyyy-MM-DD HH:mm:ss',
    'width': '100%',
    'clearable': true,
    'maxlength': 70,
    'rules': [
      { required: true, message: 'vcs.creditCardMaintenance.reportLossDatePlaceholder' }
    ]
  },
  {
    'label': 'vcs.creditCardMaintenance.reportLossToPolice',
    'placeholder': 'vcs.creditCardMaintenance.reportLossToPolicePlaceholder',
    'prop': 'reportLossToPolice',
    'component': CustomizeSelect,
    'span': 12,
    'width': '100%',
    'clearable': true,
    'maxlength': 70,
    'options': reportLossToPoliceOptions,
    'rules': [
      { required: true, message: 'vcs.creditCardMaintenance.reportLossToPolicePlaceholder' }
    ]
  },
  {
    'label': 'vcs.creditCardMaintenance.policeFileNo',
    'placeholder': 'vcs.creditCardMaintenance.policeFileNoPlaceholder',
    'prop': 'policeFileNo',
    'component': 'el-input',
    'span': 12,
    'width': '100%',
    'clearable': true,
    'maxlength': 70,
    'hidden': true,
    'rules': [
      { required: true, message: 'vcs.creditCardMaintenance.policeFileNoPlaceholder' }
    ]
  },
  {
    'label': 'vcs.creditCardMaintenance.remarks',
    'placeholder': 'vcs.creditCardMaintenance.remarksPlaceholder',
    'prop': 'remarks',
    'component': 'el-input',
    'type': 'textarea',
    'span': 24,
    'width': '100%',
    'clearable': true,
    'maxlength': 70
  }
]
