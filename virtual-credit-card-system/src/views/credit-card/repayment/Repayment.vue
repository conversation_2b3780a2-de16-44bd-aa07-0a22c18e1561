<template>
  <div v-loading="loading" class="business-container">
    <customize-card :title="$t('vcs.router.creditCardRepayment')">
      <customize-form
        ref="customizeForm"
        form-ref="dataForm"
        size="small"
        :model="formData"
        :form-item-config="formConfig"
        label-width="260px"
        label-position="left"
        class="repayment-form"
      />
      <div style="text-align: right; padding: 10px">
        <el-button size="small" @click="handlerCancel"> {{ $t('vcs.common.cancel') }}</el-button>
        <el-button type="primary" size="small" :loading="confirmButtonLoading" @click="handlerConfirm">
          {{ $t('vcs.common.confirm') }}
        </el-button>
      </div>
    </customize-card>
  </div>
</template>

<script>
import CustomizeCard from '@/components/CustomizeCard/index.vue'
import CustomizeForm from '@/components/CustomizeForm/CustomizeForm.vue'
import { formConfig } from '@/views/credit-card/repayment/form-config'
import { mapGetters } from 'vuex'
import { enquiryPendingStatement, repayment } from '@/api/business/credit-card'

export default {
  name: 'CreditCardRepayment',
  components: { CustomizeForm, CustomizeCard },
  data() {
    return {
      loading: false,
      confirmButtonLoading: false,
      formData: {
        statementAmount: 0,
        repaidAmount: 0,
        outstandingAmount: 0,
        minimumRepaymentAmount: 0,
        creditCardNumber: '',
        debitAccountNumber: '',
        repaymentAmount: 0,
        remarks: ''
      },
      formConfig: formConfig
    }
  },
  computed: {
    ...mapGetters([
      'userDetails'
    ])
  },
  created() {
    this.formData.creditCardNumber = this.$route.params.creditCardNumber

    this.enquiryPendingStatement()
  },
  methods: {
    enquiryPendingStatement() {
      this.loading = true
      enquiryPendingStatement({ creditCardNumber: this.formData.creditCardNumber })
        .then(res => {
          const { data } = res
          this.formData.statementAmount = data.statementAmount
          this.formData.repaidAmount = data.repaidAmount
          this.formData.outstandingAmount = data.outstandingAmount
          this.formData.minimumRepaymentAmount = data.minimumRepaymentAmount
        })
        .finally(() => {
          this.loading = false
        })
    },
    handlerConfirm() {
      this.$refs.customizeForm.validate((valid, data) => {
        if (valid) {
          this.confirmButtonLoading = true
          repayment(data)
            .then(res => {
              const { msg } = res
              this.$message.success(msg)
              this.$router.go(-1)
            })
            .finally(() => {
              this.confirmButtonLoading = false
            })
        }
      })
    },
    handlerCancel() {
      this.$router.go(-1)
    }
  }
}
</script>
