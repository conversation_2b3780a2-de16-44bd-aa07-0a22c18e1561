import CustomizeSelectGroup from '@/components/CustomizeForm/CustomizeSelectGroup.vue'
import store from '@/store'

export const formConfig = [
  {
    'label': 'vcs.creditCardMaintenance.creditCardNumber',
    'placeholder': 'vcs.creditCardMaintenance.creditCardNumberPlaceholder',
    'prop': 'creditCardNumber',
    'component': 'el-input',
    'span': 12,
    'width': '100%',
    'clearable': true,
    'maxlength': 70,
    'disabled': true,
    'rules': [
      { required: true, message: 'vcs.creditCardMaintenance.creditCardNumberPlaceholder' }
    ]
  },
  {
    'label': 'vcs.creditCardMaintenance.statementAmount',
    'placeholder': 'vcs.creditCardMaintenance.statementAmountPlaceholder',
    'prop': 'statementAmount',
    'component': 'el-input-number',
    'span': 12,
    'width': '100%',
    'min': 0,
    'max': *************.99,
    'controls': false,
    'step': 0.01,
    'step-strictly': true,
    'precision': 2,
    'maxlength': 70,
    'disabled': true,
    'rules': [
      { required: true, message: 'vcs.creditCardMaintenance.statementAmountPlaceholder' }
    ]
  },
  {
    'label': 'vcs.creditCardMaintenance.repaidAmount',
    'placeholder': 'vcs.creditCardMaintenance.repaidAmountPlaceholder',
    'prop': 'repaidAmount',
    'component': 'el-input-number',
    'span': 12,
    'width': '100%',
    'min': 0,
    'max': *************.99,
    'controls': false,
    'step': 0.01,
    'step-strictly': true,
    'precision': 2,
    'maxlength': 70,
    'disabled': true,
    'rules': [
      { required: true, message: 'vcs.creditCardMaintenance.repaidAmountPlaceholder' }
    ]
  },
  {
    'label': 'vcs.creditCardMaintenance.outstandingAmount',
    'placeholder': 'vcs.creditCardMaintenance.outstandingAmountPlaceholder',
    'prop': 'outstandingAmount',
    'component': 'el-input-number',
    'span': 12,
    'width': '100%',
    'min': 0,
    'max': *************.99,
    'controls': false,
    'step': 0.01,
    'step-strictly': true,
    'precision': 2,
    'maxlength': 70,
    'disabled': true,
    'rules': [
      { required: true, message: 'vcs.creditCardMaintenance.outstandingAmountPlaceholder' }
    ]
  },
  {
    'label': 'vcs.creditCardMaintenance.minimumRepaymentAmount',
    'placeholder': 'vcs.creditCardMaintenance.minimumRepaymentAmountPlaceholder',
    'prop': 'minimumRepaymentAmount',
    'component': 'el-input-number',
    'span': 12,
    'width': '100%',
    'min': 0,
    'max': *************.99,
    'controls': false,
    'step': 0.01,
    'step-strictly': true,
    'precision': 2,
    'maxlength': 70,
    'disabled': true,
    'rules': [
      { required: true, message: 'vcs.creditCardMaintenance.minimumRepaymentAmountPlaceholder' }
    ]
  },
  {
    'label': 'vcs.creditCardStatement.creditCardRepayment.debitAccountNumber',
    'placeholder': 'vcs.creditCardStatement.creditCardRepayment.debitAccountNumberPlaceholder',
    'prop': 'debitAccountNumber',
    'component': CustomizeSelectGroup,
    'span': 12,
    'width': '100%',
    'clearable': true,
    'options': store.getters.debitAccounts,
    'rules': [
      { required: true, message: 'vcs.creditCardStatement.creditCardRepayment.debitAccountNumberPlaceholder' }
    ]
  },
  {
    'label': 'vcs.creditCardStatement.creditCardRepayment.repaymentAmount',
    'placeholder': 'vcs.creditCardStatement.creditCardRepayment.repaymentAmountPlaceholder',
    'prop': 'repaymentAmount',
    'component': 'el-input-number',
    'span': 24,
    'min': 0,
    'max': *************.99,
    'controls': false,
    'step': 0.01,
    'step-strictly': true,
    'precision': 2,
    'width': '100%',
    'clearable': true,
    'rules': [
      { required: true, message: 'vcs.creditCardRepayment.repaymentAmountPlaceholder' }
    ]
  },
  {
    'label': 'vcs.creditCardStatement.creditCardRepayment.remarks',
    'placeholder': 'vcs.creditCardStatement.creditCardRepayment.remarksPlaceholder',
    'prop': 'remarks',
    'component': 'el-input',
    'type': 'textarea',
    'span': 24,
    'width': '100%',
    'clearable': true,
    'maxlength': 255
  }
]
