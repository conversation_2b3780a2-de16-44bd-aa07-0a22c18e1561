export const genderOptions = [
  { label: 'vcs.common.male', value: 'M' },
  { label: 'vcs.common.female', value: 'F' }
]

export const countryOptions = [
  { label: 'vcs.common.HK', value: 'HK' },
  { label: 'vcs.common.CN', value: 'CN' },
  { label: 'vcs.common.MY', value: 'MY' },
  { label: 'vcs.common.JP', value: 'JP' },
  { label: 'vcs.common.FR', value: 'FR' },
  { label: 'vcs.common.IN', value: 'IN' },
  { label: 'vcs.common.DE', value: 'DE' },
  { label: 'vcs.common.PH', value: 'PH' },
  { label: 'vcs.common.GB', value: 'GB' },
  { label: 'vcs.common.SG', value: 'SG' },
  { label: 'vcs.common.AU', value: 'AU' },
  { label: 'vcs.common.US', value: 'US' }
]

export const maritalStatusOptions = [
  { label: 'vcs.common.single', value: 'S' },
  { label: 'vcs.common.married', value: 'M' },
  { label: 'vcs.common.divorce', value: 'D' },
  { label: 'vcs.common.widowed', value: 'W' }
]

export const permanentResidentStatusOptions = [
  { label: 'vcs.common.yes', value: 'Y' },
  { label: 'vcs.common.no', value: 'N' }
]

export const reportLossToPoliceOptions = [
  { label: 'vcs.common.yes', value: 'Y' },
  { label: 'vcs.common.no', value: 'N' }
]

export const cancelStatusOptions = [
  { label: 'vcs.common.yes', value: 'Y' },
  { label: 'vcs.common.no', value: 'N' }
]

export const issueReplacementCardOptions = [
  { label: 'vcs.common.yes', value: 'Y' },
  { label: 'vcs.common.no', value: 'N' }
]

export const bankruptcyHistoryOptions = [
  { label: 'vcs.common.yes', value: 'Y' },
  { label: 'vcs.common.no', value: 'N' }
]

export const pastDueDebtsOptions = [
  { label: 'vcs.common.notHave', value: '0' },
  { label: 'vcs.common.once', value: '1' },
  { label: 'vcs.common.twice', value: '2' },
  { label: 'vcs.common.threeOrMoreTimes', value: '3' }
]

export const customerIdTypeOptions = [
  { label: 'vcs.common.idCard', value: 'I' },
  { label: 'vcs.common.passport', value: 'P' },
  { label: 'vcs.common.others', value: 'X' }
]

export const nextLimitChangeReviewOptions = [
  { label: 'vcs.common.twelveMonthsLater', value: '12' },
  { label: 'vcs.common.eighteenMonthsLater', value: '18' },
  { label: 'vcs.common.twentyFourMonthsLater', value: '24' }
]

export const applicationStatusOptions = [
  { label: 'vcs.common.pending', value: 'P' },
  { label: 'vcs.common.approved', value: 'A' },
  { label: 'vcs.common.rejected', value: 'R' }
]

export const limitAdjustmentStatusOptions = [
  { label: 'vcs.common.pending', value: 'P' },
  { label: 'vcs.common.approved', value: 'A' },
  { label: 'vcs.common.rejected', value: 'R' }
]

export const adjustmentTypeOptions = [
  { label: 'vcs.common.limitIncrease', value: 'I' },
  { label: 'vcs.common.limitDecrease', value: 'D' }
]

export const creditCardTypeOptions = [
  { label: 'vcs.common.visa', value: 'V' },
  { label: 'vcs.common.master', value: 'M' }
]

export const creditCardStatusOptions = [
  { label: 'vcs.common.pendingActivation', value: 'P' },
  { label: 'vcs.common.active', value: 'A' },
  { label: 'vcs.common.cancelled', value: 'C' },
  { label: 'vcs.common.lostReported', value: 'L' },
  { label: 'vcs.common.hold', value: 'H' },
  { label: 'vcs.common.supervisorApprovalCancel', value: 'S-C' },
  { label: 'vcs.common.supervisorApprovalLossReport', value: 'S-L' },
  { label: 'vcs.common.supervisorApprovalSInitLimit', value: 'S-IL' },
  { label: 'vcs.common.disabled', value: 'D' }
]

export const transactionTypeOptions = [
  { label: 'vcs.common.repayment', value: 1 },
  { label: 'vcs.common.post', value: 0 }
]

export const lossReportingReasonOptions = [
  { label: 'vcs.common.breakInTheCar', value: 'Car theft' },
  { label: 'vcs.common.houseBreakIn', value: 'House theft' },
  { label: 'vcs.common.walletStolen', value: 'Wallet stolen' },
  { label: 'vcs.common.accidentallyDrop', value: 'Accidentally drop' },
  { label: 'vcs.common.others', value: 'Others' }
]

export const cancellationReasonOptions = [
  { label: 'vcs.common.tooManyCreditCards', value: 'Too Many Credit Cards' },
  { label: 'vcs.common.movingOutOfCountry', value: 'Moving Out Of Country' },
  { label: 'vcs.common.highAnnualFee', value: 'High Annual Fee' },
  { label: 'vcs.common.rewardsNotAttractive', value: 'Rewards Not Attractive' },
  { label: 'vcs.common.disSatisfiedService', value: 'Dis Satisfied Service' },
  { label: 'vcs.common.serviceFeeTooHigh', value: 'Service Fee Too High' },
  { label: 'vcs.common.others', value: 'Others' }
]

export const booleanOptions = [
  { label: 'vcs.common.yes', value: 'Y' },
  { label: 'vcs.common.no', value: 'N' }
]

export const statusOptions = [
  { label: 'vcs.common.active', value: 'Active' },
  { label: 'vcs.common.cancel', value: 'Cancel' }
]

export const creditCardTransactionTypeOptions = [
  { label: 'vcs.common.posting', value: 0 },
  { label: 'vcs.common.repayment', value: 1 }
]

export const billingCycleOptions = [
  { label: 'vcs.common.monthly', value: 'M' },
  { label: 'vcs.common.biWeekly', value: 'B' }
]

export const accommodationOptions = [
  { label: 'vcs.common.selfOwned', value: 'S' },
  { label: 'vcs.common.privateProperty', value: 'P' },
  { label: 'vcs.common.homeOwnershipScheme', value: 'H' },
  { label: 'vcs.common.friendsOrRelatives', value: 'F' },
  { label: 'vcs.common.publicHousing', value: 'U' },
  { label: 'vcs.common.rented', value: 'R' },
  { label: 'vcs.common.quarters', value: 'Q' }
]

export const sensitiveStatusOptions = [
  { label: 'vcs.common.yes', value: 'Y' },
  { label: 'vcs.common.no', value: 'N' }
]

export const educationOptions = [
  { label: 'vcs.common.university', value: 'U' },
  { label: 'vcs.common.master', value: 'M' },
  { label: 'vcs.common.secondary', value: 'S' },
  { label: 'vcs.common.postSecondary', value: 'P' },
  { label: 'vcs.common.others', value: 'X' },
  { label: 'vcs.common.primaryOrJunior', value: 'J' }
]

export const debitAccountTypeOptions = [
  { label: 'vcs.common.savingAccount', value: '001' },
  { label: 'vcs.common.currentAccount', value: '002' }
]

export const processInstanceStatusOptions = [
  { label: 'vcs.common.kycInstanceStatusPending', value: 'Pending' },
  { label: 'vcs.common.kycInstanceStatusDoing', value: 'Doing' },
  { label: 'vcs.common.kycInstanceStatusDone', value: 'Done' }
]

export const operationLogStatusOptions = [
  { label: 'vcs.common.successStatus', value: 'SUCCESS' },
  { label: 'vcs.common.failStatus', value: 'FAIL' }
]

export const operationLogMethodOptions = [
  { label: 'vcs.common.postMethod', value: 'POST' },
  { label: 'vcs.common.getMethod', value: 'GET' },
  { label: 'vcs.common.deleteMethod', value: 'DELETE' },
  { label: 'vcs.common.putMethod', value: 'PUT' }
]

export const operationLogTypeOptions = [
  { label: 'vcs.common.insertOperate', value: 'INSERT' },
  { label: 'vcs.common.deleteOperate', value: 'DELETE' },
  { label: 'vcs.common.updateOperate', value: 'UPDATE' },
  { label: 'vcs.common.selectOperate', value: 'SELECT' },
  { label: 'vcs.common.exportOperate', value: 'EXPORT' },
  { label: 'vcs.common.importOperate', value: 'IMPORT' },
  { label: 'vcs.common.otherOperate', value: 'OTHER' }
]

export const creditProductTypeOptions = [
  { label: 'vcs.common.creditCard', value: 'C' }
]

export const creditLimitSecuredStatusOptions = [
  { label: 'vcs.common.secured', value: 'S' },
  { label: 'vcs.common.clean', value: 'C' }
]

export const transactionOptionOptions = [
  { label: 'vcs.common.cancelRedemption', value: '0001' },
  { label: 'vcs.common.redemption', value: '0002' }
]

export function getLabel(type, value) {
  let temp
  switch (type.toUpperCase()) {
    case 'CUSTOMER_ID_TYPE':
      temp = customerIdTypeOptions.filter(item => item.value === value)
      break
    case 'COUNTRY':
      temp = countryOptions.filter(item => item.value === value)
      break
    case 'GENDER':
      temp = genderOptions.filter(item => item.value === value)
      break
    case 'APPLICATION_STATUS':
      temp = applicationStatusOptions.filter(item => item.value === value)
      break
    case 'REPORT_LOSS_TO_POLICE':
      temp = reportLossToPoliceOptions.filter(item => item.value === value)
      break
    case 'BANKRUPTCY_HISTORY':
      temp = bankruptcyHistoryOptions.filter(item => item.value === value)
      break
    case 'MARITAL_STATUS':
      temp = maritalStatusOptions.filter(item => item.value === value)
      break
    case 'PERMANENT_RESIDENT_STATUS':
      temp = permanentResidentStatusOptions.filter(item => item.value === value)
      break
    case 'LIMIT_ADJUSTMENT_STATUS':
      temp = limitAdjustmentStatusOptions.filter(item => item.value === value)
      break
    case 'ADJUSTMENT_TYPE':
      temp = adjustmentTypeOptions.filter(item => item.value === value)
      break
    case 'CREDIT_CARD_TYPE':
      temp = creditCardTypeOptions.filter(item => item.value === value)
      break
    case 'CREDIT_CARD_STATUS':
      temp = creditCardStatusOptions.filter(item => item.value === value)
      break
    case 'TRANSACTION_TYPE':
      temp = transactionTypeOptions.filter(item => item.value === value)
      break
    case 'LOSS_REPORTING_REASON':
      temp = lossReportingReasonOptions.filter(item => item.value === value)
      break
    case 'CANCELLATION_REASON':
      temp = cancellationReasonOptions.filter(item => item.value === value)
      break
    case 'BILLING_CYCLE':
      temp = billingCycleOptions.filter(item => item.value === value)
      break
    case 'SENSITIVE_STATUS':
      temp = sensitiveStatusOptions.filter(item => item.value === value)
      break
    case 'ACCOMMODATION':
      temp = accommodationOptions.filter(item => item.value === value)
      break
    case 'EDUCATION':
      temp = educationOptions.filter(item => item.value === value)
      break
    case 'DEBIT_ACCOUNT_TYPE':
      temp = debitAccountTypeOptions.filter(item => item.value === value)
      break
    case 'PROCESS_INSTANCE_STATUS':
      temp = processInstanceStatusOptions.filter(item => item.value === value)
      break
    case 'OPERATION_LOG_STATUS':
      temp = operationLogStatusOptions.filter(item => item.value === value)
      break
    case 'OPERATION_LOG_METHOD':
      temp = operationLogMethodOptions.filter(item => item.value === value)
      break
    case 'OPERATION_LOG_TYPE':
      temp = operationLogTypeOptions.filter(item => item.value === value)
      break
    case 'CREDIT_PRODUCT_TYPE':
      temp = creditProductTypeOptions.filter(item => item.value === value)
      break
    case 'CREDIT_LIMIT_SECURED_STATUS':
      temp = creditLimitSecuredStatusOptions.filter(item => item.value === value)
      break
    case 'TRANSACTION_OPTION':
      temp = transactionOptionOptions.filter(item => item.value === value)
      break
    default:
      temp = []
      break
  }
  if (temp && temp.length) {
    return temp[0].label
  }
  return ''
}

export function handlerCommonData(data) {
  const formData = JSON.parse(JSON.stringify(data))
  formData.customerMasterInfoEntity = {}
  const commonField = [
    'accountNumber',
    'customerId',
    'customerIdType',
    'customerNumber',
    'dateOfBirth',
    'department',
    'firstName',
    'gender',
    'industry',
    'issueCountry',
    'lastName',
    'nationality',
    'otherLanguageName',
    'position',
    'role'
  ]
  commonField.forEach(item => {
    formData.customerMasterInfoEntity[item] = formData[item]
    delete formData[item]
  })
  return formData
}
