import { get, post } from '@/api/utils/http'

const baseUrl = process.env.VUE_APP_WORKFLOW_API + '/workflow'

export function getWorkflow() {
  return get(baseUrl + '/getWorkflow')
}

export function startProcess(data) {
  return post(baseUrl + '/startProcess', data)
}

export function getResponse(processInstanceId) {
  return get(baseUrl + `/getResponse?processInstanceId=${processInstanceId}`)
}
export function getFlowSort(params) {
  return get(baseUrl + '/getflowSort', params)
}

export function executeUploadFlow(data) {
  return post(baseUrl + '/executeUploadFlow', data)
}

export function executeFlow(data) {
  return post(baseUrl + '/executeFlow', data)
}
