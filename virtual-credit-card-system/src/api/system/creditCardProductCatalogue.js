import request from '@/utils/request'

const baseUrl = '/creditcard-experience/credit-card'

export function getCreditCardProductCatalogueList(data) {
  return request({
    url: `${baseUrl}/reward-point/product-catalogue/list`,
    method: 'post',
    data
  })
}

export function getCreditCardProductCataloguePage(data) {
  return request({
    url: `${baseUrl}/reward-point/product-catalogue/page`,
    method: 'post',
    data
  })
}

export function deleteCreditCardProductCatalogue(data) {
  return request({
    url: `${baseUrl}/reward-point/product-catalogue/delete`,
    method: 'post',
    data
  })
}

export function createCreditCardProductCatalogue(data) {
  return request({
    url: `${baseUrl}/reward-point/product-catalogue/maintenance`,
    method: 'post',
    data
  })
}

export function updateCreditCardProductCatalogue(data) {
  return request({
    url: `${baseUrl}/reward-point/product-catalogue/update`,
    method: 'post',
    data
  })
}

