import request from '@/utils/request'

const baseUrl = '/creditcard-experience/credit-card'

export function getCreditCardTransactionDetailsList() {
  return request({
    url: `${baseUrl}/creditCardTransactionDetails/list`,
    method: 'post'
  })
}

export function deleteCreditCardTransactionDetails(id) {
  return request({
    url: `${baseUrl}/creditCardTransactionDetails/delete`,
    method: 'post',
    data: { id }
  })
}

export function createCreditCardTransactionDetails(data) {
  return request({
    url: `${baseUrl}/creditCardTransactionDetails/create`,
    method: 'post',
    data
  })
}

export function updateCreditCardTransactionDetails(data) {
  return request({
    url: `${baseUrl}/creditCardTransactionDetails/update`,
    method: 'post',
    data
  })
}

