import request from '@/utils/request'

const baseUrl = '/creditcard-experience/credit-card/maintenance'

export function getCreditCardCancellationPage(data) {
  return request({
    url: `${baseUrl}/cancellation-page`,
    method: 'post',
    data
  })
}

export function getCreditCardLossReportingPage(data) {
  return request({
    url: `${baseUrl}/loss-reporting-page`,
    method: 'post',
    data
  })
}

export function getCreditCardCancellationReview(data) {
  return request({
    url: `${baseUrl}/cancellation-review`,
    method: 'post',
    data
  })
}

export function getCreditCardLossReportingReview(data) {
  return request({
    url: `${baseUrl}/loss-reporting-review`,
    method: 'post',
    data
  })
}

export function createCreditCardMaintenance(data) {
  return request({
    url: `${baseUrl}/create`,
    method: 'post',
    data
  })
}

export function updateCreditCardMaintenance(data) {
  return request({
    url: `${baseUrl}/update`,
    method: 'post',
    data
  })
}

