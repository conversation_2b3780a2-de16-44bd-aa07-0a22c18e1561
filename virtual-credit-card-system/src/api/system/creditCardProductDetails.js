import request from '@/utils/request'

const baseUrl = '/creditcard-experience/credit-card'

export function getCreditCardProductDetailsList() {
  return request({
    url: `${baseUrl}/creditCardProductDetails/list`,
    method: 'post'
  })
}

export function getCreditCardProductDetailsPage(data) {
  return request({
    url: `${baseUrl}/reward-point/product/page`,
    method: 'post',
    data
  })
}

export function deleteCreditCardProductDetails(data) {
  return request({
    url: `${baseUrl}/reward-point/product/delete`,
    method: 'post',
    data
  })
}

export function createCreditCardProductDetails(data) {
  return request({
    url: `${baseUrl}/reward-point/product/maintenance`,
    method: 'post',
    data
  })
}

export function updateCreditCardProductDetails(data) {
  return request({
    url: `${baseUrl}/reward-point/product/update`,
    method: 'post',
    data
  })
}

