import request from '@/utils/request'
import { stringify } from 'qs'

export function get(url, params, config) {
  return request.get(url, {
    params,
    ...config,
    paramsSerializer: (params) => {
      return stringify(params, { indices: false, allowDots: true })
    }
  })
}

export function post(url, data, config) {
  return request.post(url, data, config)
}

export function put(url, data, config) {
  return request.put(url, data, config)
}

export function del(url, params, config) {
  return request.delete(url, {
    params,
    ...config,
    paramsSerializer: (params) => {
      return stringify(params, { indices: false, allowDots: true })
    }
  })
}
