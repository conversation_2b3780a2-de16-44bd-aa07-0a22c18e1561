<template>
  <el-form
    :ref="formRef"
    class="freeForm"
    :model="model"
    v-bind="$attrs"
    :disabled="isView"
  >
    <slot />
    <el-row :gutter="20" style="margin-right: 0; margin-left: 0; padding-left: 0; padding-right: 0">
      <!-- 显示hidden为false的表单项 -->
      <div
        v-for="(item, index) in formItemConfig"
        :key="index"
      >
        <el-col
          v-if="!item.hidden"
          :span="item.span"
          :style="{height: item['slot-name'] ? auto : '50px'}"
        >
          <div class="customize-form-item">
            <!-- 处理标题 -->
            <p v-if="item.title">{{ item.title }}</p>
            <el-form-item
              v-else
              :label="$t(item.label)"
              :prop="item.prop"
              style="width: 100%"
              :rules="(item.rules || []).map(rule => ({ ...rule, message: rule.message ? $t(rule.message) : undefined }))"
              :class="{'form-item-has-action': item.actionButton }"
            >
              <div v-if="item['slot-name']">
                <slot :name="item['slot-name']" />
              </div>
              <!-- 动态渲染组件 -->
              <component
                :is="isComponentName(item)"
                v-else
                v-model="model[item.prop]"
                class="form-item-content-has-action"
                :placeholder="$t(item.placeholder)"
                v-bind="{ ...item, disabled: item.disabled || (isUpdate && item.updateDisabled) }"
                :style="{ width: item.width }"
                @change="handleChange(item, $event)"
              >
                <template v-if="item.append" #append>
                  {{ item.append }}
                </template>
              </component>
              <el-button
                v-if="item.actionButton"
                class="action-btn"
                v-bind="item.actionButton"
                @click="handlerActionButtonClick(item, $event)"
              >
                {{ $t(item.actionButton.text) }}
              </el-button>
            </el-form-item>
          </div>
        </el-col>
      </div>

    </el-row>
  </el-form>
</template>

<script>
import { auto } from 'html-webpack-plugin/lib/chunksorter'

/**
 * @desc 表单组件
 * @param {Object} formRef - el-form 的 ref 名称
 * @param {Object} model - 表单数据模型
 * @param {Object} formItemConfig - el-form-item 配置项
 * @param {Object} rules - el-form-item 验证规则
 */
export default {
  props: {
    // 表单引用名称
    formRef: {
      type: String,
      default: 'formRef'
    },
    // 表单数据模型
    model: {
      type: Object,
      default: () => ({})
    },
    // 表单项配置
    formItemConfig: {
      type: Array,
      default: () => []
    },
    modelCode: {
      type: String,
      default: ''
    },
    isView: {
      type: Boolean,
      default: false
    },
    isUpdate: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    /**
     * 根据组件类型获取需要渲染的组件名称
     */
    isComponentName() {
      return (item) => {
        if (item.component === 'el-select') {
          return 'CustomizeSelect'
        } else {
          return item.component || 'el-input'
        }
      }
    },
    /**
     * 根据表单项配置获取占位符
     */
    placeholder() {
      return (item) => {
        if (item.placeholder) return item.placeholder
        const arr = ['el-input', 'el-input-number']
        return !item.component || arr.includes(item.component)
          ? `Please enter ${(item.label || '').toLocaleLowerCase()}`
          : `Please select ${(item.label || '').toLocaleLowerCase()}`
      }
    }
  },
  methods: {
    auto,
    /**
     * 验证表单并执行回调函数
     * @param {Function} cb - 表单验证通过后的回调函数
     * @returns {boolean} - 表单验证结果
     */
    validate(cb) {
      this.$refs[this.formRef].validate((valid) => {
        cb(valid, this.model)
        if (valid) {
          // 如果表单验证通过，执行提交操作
        } else {
          // 如果表单验证失败，处理失败情况
          return false
        }
      })
    },
    reset() {
      this.$refs[this.formRef].resetFields()
    },
    // change型式的回调
    handleChange(item, e) {
      this.$emit('update', { prop: item.prop, value: e })
    },
    handlerActionButtonClick(item, e) {
      this.$emit('click-button', { prop: item.prop, value: e })
    },
    // 通过ref属性重置表单
    resetForm() {
      this.$refs[this.formRef].resetFields()
    }
  }
}
</script>

<style lang="scss">
.form-item-has-action .el-form-item__content {
  display: flex;
  .form-item-content-has-action {
    flex: 1;
  }
}
</style>
