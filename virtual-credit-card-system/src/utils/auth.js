import Cookies from 'js-cookie'
import store from '@/store'

const vcsLoginPk = 'vcs-login-pk'

export function getLoginPk() {
  return Cookies.get(vcsLoginPk)
}

export function setLoginPk(loginPk) {
  return Cookies.set(vcsLoginPk, loginPk)
}

export function removeLoginPk() {
  Cookies.remove(vcsLoginPk)
}

export function hasPermission(permission) {
  return store.getters.permissions.includes(permission)
}
