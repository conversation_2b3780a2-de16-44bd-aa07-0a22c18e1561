// translate router.meta.title, be used in breadcrumb sidebar tagsview
import i18n from '@/lang'
export function generateTitle(title) {
  const hasKey = i18n.te('vcs.router.' + title)

  if (hasKey) {
    // $t :this method from vue-i18n, inject in @/lang/index.js
    return i18n.t('vcs.router.' + title)
  }
  return title
}

export function getMessage(key) {
  const hasKey = i18n.te(key)
  if (hasKey) {
    return i18n.t(key)
  }
  return key
}
