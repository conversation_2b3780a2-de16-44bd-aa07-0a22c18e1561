{"vcs": {"common": {"platformName": "Virtual Credit Card System", "oops": "OOPS!", "pageNotFound": "The page was not found and cannot be accessed...", "404Tip": "Please check if the URL you entered is correct, or click the button below to return to the homepage.", "backHome": "Back Home", "confirm": "Confirm", "cancel": "Cancel", "female": "Female", "male": "Male", "copyright": "Empowered by SIMNECTZ®", "createDate": "Create Date", "to": "To", "startDate": "Start Date", "endDate": "End Date", "create": "Create", "search": "Search", "reset": "Reset", "operate": "Operate", "view": "View", "edit": "Edit", "delete": "Delete", "deleteSelectTipMessage": "Please select the record to delete", "update": "Update", "deleteTipMessage": "This operation will permanently delete the record, do you want to continue?", "tip": "Tip", "HK": "Hong Kong", "CN": "China", "MY": "Malaysia", "JP": "Japan", "FR": "France", "IN": "India", "DE": "Germany", "PH": "Philippines", "GB": "United Kingdom", "SG": "Singapore", "AU": "Australia", "US": "United States", "single": "Single", "married": "Married", "divorce": "Divorce", "widowed": "Widowed", "yes": "Yes", "no": "No", "notHave": "Not Have", "once": "Once", "twice": "Twice", "threeOrMoreTimes": "Three or More Times", "idCard": "ID Card", "passport": "Passport", "others": "Others", "add": "Add", "twelveMonthsLater": "After 12 months", "eighteenMonthsLater": "After 18 months", "twentyFourMonthsLater": "After 24 months", "pending": "Pending", "approved": "Approved", "rejected": "Rejected", "limitAllocation": "Limit Allocation", "limitIncrease": "Limit Increase", "limitDecrease": "Limit Decrease", "kyc": "KYC", "review": "Review", "visa": "Visa", "master": "Master", "back": "Back", "currency": "<PERSON><PERSON><PERSON><PERSON>", "pendingActivation": "Pending Activation", "active": "Active", "loss": "Loss", "hold": "Hold", "disabled": "Disabled", "supervisorApproval": "Supervisor Approval", "paymentPassword": "Payment password", "paymentPasswordPlaceholder": "Please enter six digits as the payment password", "retypePaymentPassword": "Confirm payment password", "retypePaymentPasswordPlaceholder": "Please retype six digits as the payment password", "paymentPasswordRule": "Please enter your six digit password", "repayment": "Repayment", "lossReport": "Loss Report", "cancellation": "Cancellation", "statement": "Statement", "limitAdjustment": "LimitAdjustment", "post": "Post", "breakInTheCar": "Car Theft", "houseBreakIn": "House theft", "walletStolen": "<PERSON><PERSON>", "accidentallyDrop": "Accidentally Drop", "tooManyCreditCards": "Too Many Credit Cards", "movingOutOfCountry": "Moving Out Of Country", "highAnnualFee": "High Annual Fee", "rewardsNotAttractive": "Rewards Not Attractive", "disSatisfiedService": "Dis Satisfied Service", "serviceFeeTooHigh": "Service Fee Too High", "savingAccount": "Saving Account", "currentAccount": "Current Account", "redemption": "Redemption", "creationDate": "Creation Date", "updateDate": "Update Date", "clickUpload": "Click Upload", "next": "Next", "prev": "Prev", "close": "Close", "posting": "Posting", "monthly": "Monthly", "biWeekly": "Bi-Weekly", "selfOwned": "Self Owned", "privateProperty": "Private Property", "homeOwnershipScheme": "Home Ownership Scheme", "friendsOrRelatives": "Friends / Relatives", "publicHousing": "Public Housing", "rented": "Rented", "quarters": "Quarters", "university": "University", "secondary": "Secondary", "postSecondary": "Post Secondary", "primaryOrJunior": "Primary / Junior", "supervisorApprovalCancel": "Cancelling", "supervisorApprovalLossReport": "Loss Reporting", "supervisorApprovalSInitLimit": "Initial limit approval", "cancelled": "Cancelled", "lostReported": "Loss Reported", "username": "Username", "role": "Rule", "doing": "Doing", "done": "Done", "successStatus": "Success", "failStatus": "Fail", "postMethod": "Post", "getMethod": "Get", "putMethod": "Put", "deleteMethod": "Delete", "insertOperate": "Insert", "deleteOperate": "Delete", "updateOperate": "Update", "selectOperate": "Select", "exportOperate": "Export", "importOperate": "Import", "otherOperate": "Other", "creditCard": "Credit Card", "clean": "Clean", "secured": "Secured", "kycInstanceStatusPending": "Pending", "kycInstanceStatusDoing": "Doing", "kycInstanceStatusDone": "Done", "cancelRedemption": "Cancel Redemption", "submit": "Submit"}, "router": {"404": "404", "login": "<PERSON><PERSON>", "creditCardApply": "Credit Card Apply", "dashboard": "Dashboard", "applicationManagement": "Credit Card Apply Management", "limitAdjustmentManagement": "Limit Adjustment Management", "cancellationManagement": "Cancellation Management", "lossReportingManagement": "Loss Reporting Management", "creditCardManagement": "Credit Card Management", "creditCard": "Credit Card", "creditCardOverview": "Credit Card Overview", "creditCardDetails": "Credit Card Details", "creditCardActivation": "Credit Card Activation", "creditCancellation": "Credit Cancellation", "creditCardLimitAdjustment": "Credit Card Limit Adjustment", "creditCardLossReporting": "Credit Card Loss Reporting", "creditCardRepayment": "Credit Card Repayment", "creditCardStatement": "Credit Card Statement", "points": "Reward Points", "workflow": "Workflow", "product": "Product Management", "catalogue": "Catalogue Management", "redemption": "Redemption", "redemptionHistory": "Redemption History", "simulator": "Simulator", "overdueInterest": "Overdue Interest", "minimumPayment": "Minimum Payment", "customerInformation": "Customer Information Enquiry", "creditEnquiry": "Credit Information", "portfolioEnquiry": "Portfolio Information", "demographicsEnquiry": "Demographics Information", "creditCardEnquiry": "Credit Card Enquiry", "statementEnquiry": "Statement Enquiry", "redemptionHistoryEnquiry": "Redemption History Enquiry", "operationLog": "Operation Log"}, "authorize": {"login": "<PERSON><PERSON>", "logout": "Logout", "apply": "Apply", "backLogin": "<PERSON> Login", "customerNumber": "Customer number", "customerNumberPlaceholder": "Please enter customer number", "usernamePlaceholder": "Please enter login name", "passwordPlaceholder": "Please enter login password", "customerNumberIsRequired": "Customer number is required", "usernameIsRequired": "Username is required", "passwordIsRequired": "Login password is required", "creditCardNumber": "Credit Card Number", "limit": "Limit(HKD)", "limitIndex": "Limit{index}(HKD)", "expiryDate": "Expiry Date", "creditCardNumberPlaceholder": "Please enter credit card number", "limitPlaceholder": "Please enter limit", "expiryDatePlaceholder": "Please select expiry date", "creditCardNumberIsRequired": "Credit card number is required", "limitIsRequired": "Limit is required", "expiryDateIsRequired": "Expiry date is required", "addHoldCreditCardErrorMessage": "The maximum number of credit cards that can be added to an existing one is 20"}}}