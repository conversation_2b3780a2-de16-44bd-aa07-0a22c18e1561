{"addRole": "添加角色", "editRole": "编辑角色", "staffNumberIsRequired": "员工编号不能为空", "departmentIsRequired": "部门不能为空", "roleIsRequired": "角色不能为空", "usernameIsRequired": "用户名不能为空", "emailIsRequired": "电子邮箱不能为空", "pwdIsRequired": "密码不能为空", "emailAddress": "电子邮箱", "username": "用户名", "CustomerNumber": "客户编号", "industryNum": "收款人行业编号", "Payeeindustry": "收款人行业", "UpdateTime": "更新时间", "Cteatime": "创建时间", "proCode": "产品代码", "Currencytype": "货币类型", "Minimum": "贷款最低金额", "Maximum": "贷款最大金额", "Lowinterest": "低利率", "Highinteres": "高利率", "Currencyname": "货币名称", "Currencyabbreviation": "货币简称", "Decimalplaces": "小数位", "BnakBuy": "银行买入价", "BankSell": "银行卖出价", "FixedAmount": "金额范围", "Deposit": "存款期限", "depositrate": "定存利率", "selectDataType": "选择流程类型", "eKYC": "创建eKYC", "insurance": "创建保险理赔", "country": "国家", "Sensitive": "敏感权限", "depositLimit": "存款限额", "withdrawalLimit": "取款限额", "transferLimit": "转账限额", "paymentLimit": "支付限额", "fxLimit": "外汇限额", "termDepositLimit": "定存限额", "stockLimit": "股票限额", "bondLimit": "债券限额", "fundLimit": "基金限额", "describe": "描述", "applicant": "申请人", "date": "日期", "OperationType": "操作类型", "type": "类型", "ApplicationNo": "申请编号", "submitTime": "提交时间", "Status": "状态", "Application": "申请列表", "SourcedataBase": "源数据库", "parameterT": "系统参数表", "Fixeddeposit": "定存利率表", "Foreign": "外汇利率表", "Holiday": "节假日配置表", "loanamount": "个人贷款金额配置表", "Payee": "支付收款人信息表", "batch": "定时跑批表", "parameterN": "参数", "value": "值", "remarks": "备注", "PleaseObj": "请输入操作对象", "Pleasedesc": "请输入权限描述", "Pleasepermiss": "请输入权限接口", "Pleaseinterface": "请填写接口额度审核字段", "opearObject": "操作对象", "description": "权限描述", "interface": "权限接口", "approvalfield": "接口额度审核字段", "parameter": "系统参数配置", "permissionconfig": "员工权限配置", "pleaseSelect": "请选择", "Retailquota": "零售额度", "Industrialcial": "工商额度", "Employeeconfig": "员工额度配置", "departmentC": "员工部门和角色配置", "query": "查询", "Emplonumber": "员工编号", "EmplonumberMsg": "请选择员工编号", "department": "部门", "departments": "部门", "rolequota": "员工角色和额度配置", "role": "角色", "Retail": "零售额度", "Industria": "工商额度", "UpdateT": "更新时间", "Configuration": "配置时间", "operation": "操作", "operate": "系统运维", "jurisdiction": "权限", "Employee": "员工列表", "Workflow": "工作流程", "WorkflowNodeManage": "节点管理", "Add": "新增", "Edit": "编辑", "Delete": "删除", "Cancel": "取消", "Confirm": "确认", "AddTip": "你确定要添加吗？", "DeleteTip": "你确定要删除吗？", "UpdateTip": "你确定要更新吗？", "CanNotEmptyTip": "该字段不能为空", "OperatingSuccess": "操作成功", "Tip": "提示", "Operate": "操作", "Name": "名称", "Group": "分组（中文）", "GroupEn": "分组（英文）", "ApiUrl": "API地址", "ApiMethod": "API方法", "ApiHeader": "api_header", "ApiParams": "api_params", "CreateDate": "创建日期", "EnterNameTip": "请输入节点名称", "EnterGroupTip": "请输入节点分组", "EnterApiUrlTip": "请输入 api_url", "EnterApiMethodTip": "请输入 api_method", "EnterApiHeaderTip": "请输入 api_header", "EnterApiParamsTip": "请输入 api_params", "AddFlow": "新增节点", "EditFlow": "编辑节点", "WorkflowManage": "规则管理", "Json": "Json", "LastUpdateDate": "更新时间", "Save": "保存", "Update": "更新", "AddWorkflow": "添加规则", "UpdateWorkflow": "更新规则", "createworkflow": "创建工作流", "PublicWorkflow": "公共工作流", "CustomWorkflow": "自定义工作流", "desc": "分组", "kyc": "KYC - 开户流程", "ProcessGroup": "流程组", "Process": "步骤", "nodelist": "开户流程节点列表", "nodeInsurancelist": "保险理赔流程节点列表", "rules": "开户流程节点规则", "tip1": "提示：使用公共节点创建您的Workflow。", "tip2": "<p> <span>请按照以下说明开始创建Workflow：</span> </p> <p> <span>1.Workflow创建工具</span> </p> <p> <span>&nbsp; &nbsp; 点击<a target=\"_blank\" href=\"https://demo.bpmn.io/\"><b>Workflow创建工具</b></a>跳转到在线编辑工具。</span> </p> <p> <span>2.下载模板</span> </p> <p> <span>&nbsp; &nbsp; 在上一页“工作流程”的公共工作流中点击下载按钮，得到公共工作流模板点击下载。</span> </p> <p> <span>3.使用workflow模板</span> </p> <p> <span>&nbsp; &nbsp; 在打开的工具页面中点击open，选择上一步下载得到的EKYC.bpmn文件。</span> </p> <p> <span>4.开始创建</span> </p> <p> <span>&nbsp; &nbsp; 在workflow模板的基础上开始创建您的workflow。</span> </p> <p> <span>5.保存工作流</span> </p> <p> <span>&nbsp; &nbsp; 在工具页面左下角点击“下载”图标，保存您创建的工作流文件，然后点击右侧的“上传文件”上传您保存的工作流文件。</span> </p> <p> <br/> </p> <p> <span>Workflow创建工具的详细操作手册，请<a target=\"_blank\" href=\"https://simnectzplatform.com/platform/api/portal/developers/wk/download\"><b>点击下载</b></a>。</span> </p> <p> <span>使用公共Node创建workflow，需要遵守Node之间的顺序规则，请参考“<a target=\"_blank\" href=\"/tooling/nodeRules?type=KYC\"})\"><b>公共Node规则</b></a>”。</span> </p> <p> <br/> </p>", "insurancetip1": "提示：使用公共节点创建您的Workflow。", "insurancetip2": "<p> <span>请按照以下说明开始创建Workflow：</span> </p> <p> <span>1.Workflow创建工具</span> </p> <p> <span>&nbsp; &nbsp; 点击<a target=\"_blank\" href=\"https://demo.bpmn.io/\"><b>Workflow创建工具</b></a>跳转到在线编辑工具。</span> </p> <p> <span>2.下载模板</span> </p> <p> <span>&nbsp; &nbsp; 在上一页“工作流程”的公共工作流中点击下载按钮，得到公共工作流模板点击下载。</span> </p> <p> <span>3.使用workflow模板</span> </p> <p> <span>&nbsp; &nbsp; 在打开的工具页面中点击open，选择上一步下载得到的insurance_claim.bpmn文件。</span> </p> <p> <span>4.开始创建</span> </p> <p> <span>&nbsp; &nbsp; 在workflow模板的基础上开始创建您的workflow。</span> </p> <p> <span>5.保存工作流</span> </p> <p> <span>&nbsp; &nbsp; 在工具页面左下角点击“下载”图标，保存您创建的工作流文件，然后点击右侧的“上传文件”上传您保存的工作流文件。</span> </p> <p> <br/> </p> <p> <span>Workflow创建工具的详细操作手册，请<a target=\"_blank\" href=\"https://simnectzplatform.com/platform/api/portal/developers/wk/download\"><b>点击下载</b></a>。</span> </p> <p> <span>使用公共Node创建workflow，需要遵守Node之间的顺序规则，请参考“<a target=\"_blank\" href=\"/tooling/nodeRules?type=insurance\"})\"><b>公共Node规则</b></a>”。</span> </p> <p> <br/> </p>", "class": "类型", "file": "文件", "selectworkflow": "选择工作流", "workflowStatus": "工作流运行状态", "accommodation": "住宅情况", "branchcode": "分行代码", "chinesename": "中文名", "clearingcode": "银行代码", "companyaddress": "公司地址", "companyphonenumber": "公司电话", "countrycode": "国家代码", "customerID": "个人ID", "customerIDType": "证件类型", "dateOfBirth": "出生日期", "education": "教育程度", "emailaddress": "电子邮箱", "employercompanyname": "公司名称", "firstname": "名", "gender": "性别", "issueCountry": "签发国家", "proofOfAddress": "地址证明", "proofOfAddressError": "请上传地址证明", "lastname": "姓", "mailingAddress": "邮政地址", "maritalstatus": "婚姻状况", "mobilePhoneNumber": "移动电话", "monthlysalary": "月收入", "nationality": "国籍", "occupation": "职业", "permanentresidencestatus": "永久居留身份", "position": "职位", "residencephonenumber": "住宅电话", "residentialaddress": "住宅地址", "wechatid": "微信号", "yearsofresidence": "居住年限", "yearsofservices": "工作年限", "accountType": "账户类型", "currencyCode": "货币代码", "customerNumber": "用户编号", "relaccountnumber": "关联账户", "acknowledge": "确认条款", "accountNumber": "用户其他银行账户", "accountNumbererror": "请填写您的其他银行账户", "fileerror": "请上传文件,文件格式：bmp,jpg,jpeg,png,大小在1MB以内", "customerIDerror": "客户在其国家/地区的唯一标识。最大长度：35", "dateOfBirtherror": "客户的生日", "firstnameerror": "最大长度：70", "lastnameerror": "最大长度：70", "gendererror": "性别不能为空", "issueCountryerror": "客户ID的颁发国家。最大长度：20", "mailingAddresserror": "您的邮件可以发送到的地址。最大长度：1000", "mobilePhoneNumbererror": "客户的手机号码。最大长度：34", "residentialaddresserror": "客户居住地址。最大长度：1000", "accountTypeerror": "账户类型指账户是否为储蓄账户、活期账户、股票交易账户等。可能值：001（储蓄账户）；002（活期账户）；003（外币账户）；100（定期存款账户）；300（股票交易账户）；400（贵金属账户）；500（共同基金账户）；600（抵押贷款）最大长度：3", "currencyCodeerror": "为账户设置的货币。请根据账户类型设置正确的币种类型。目前，储蓄/活期/定期存款/贷款/基金/股票/贵金属账户目前只支持港币；外汇账户支持以下11种货币：港币、人民币、美元、澳元、欧元、瑞士法郎、加元、英镑、日元、新西兰元、新加坡元。最大长度：3", "customerNumbererror": "在银行系统中创建新客户记录时生成的唯一ID。最大长度：25", "relAccountNumberError": "请输入关联账户", "developerIDerror": "在SIMNECTZ API平台中注册的开发者ID。最大长度：50", "loginNameerror": "为登录账户设置的登录名。最大长度：50", "loginPwderror": "为登录账户设置的登录密码。最大长度：50", "developernameerrorerror": "开发者姓名。最大长度：140", "nationalityError": "客户的国籍。最大长度：20", "monthlySalaryError": "客户的月收入。最小值：0", "countryCodeError": "国家代码不能为空", "clearingCodeError": "银行代码不能为空", "definition_key": "编号", "status": "状态", "createcustomer": "创建客户", "view": "查看结果", "resume": "恢复", "viewReponse": "查看数据", "run": "运行", "uploadtip": "只上传*.bpmn文件，文件大小不能超过2MB。", "flowNameerror": "请填写工作流名称", "deleteTip": "确定删除吗？", "developerID": "用户ID", "developername": "用户姓名", "email": "电子邮件", "loginName": "登录账号", "loginPwd": "登录密码", "CustomerProfile": "填写用户信息", "finishTip": "工作流已结束：", "createSuccess": "开户成功!", "complete": "工作流结束", "hasbeenlatestnode": "已经是最后一个节点了", "Biometrics": "人脸识别", "Signature": "签名", "CheckingBlacklist": "黑名单检测", "CheckingBlacklistTip": "正在检测用户是否被列入黑名单，请等待...", "CheckingSanction": "制裁名单检测", "CheckingSanctionTip": "正在检测用户是否被列入制裁名单，请等待...", "PEPTip": "正在检测用户是否被列入政治公共人物名单，请等待...", "VerifyAddress": "住址检测", "VerifyAddressTip": "正在检测用户住址是否真实有效，请等待...", "VerifyBiometrics": "验证生物特征", "AccountOpening": "开户", "AcknowledgeTC": "确认用户条款", "CaptureSignature": "提交签名", "CRS": "共同申报准则检测", "PEP": "政治公共人物", "FATCA": "海外账户税收合规检测", "userCreation": "设置账户", "SetPin": "登记账户信息", "AccountOpeningMsg": "根据指定的账户类型创建新账户。", "AcknowledgeTCMsg": "当用户做一些操作时，首先需要同意协议。", "CaptureDemographicMsg": "所有银行服务都以客户为基础。在开户或使用其他服务之前，您需要创建新客户记录，请提交您的客户信息。", "CaptureSignatureMsg": "通过这个界面，可以上传签名照片并绑定到相应的客户。", "CheckBlacklistMsg": "如果用户信息在黑名单上，则无法执行后续操作。", "CheckSanctionMsg": "检查客户是否被列入制裁名单。", "CRSMsg": "检查用户信息是否提交到CRS。填写客户的任何其他银行卡账户进行检测。", "PEPMsg": "检查客户是否被列入政治公共人物名单。", "FATCAMsg": "检查用户信息是否提交给FATCA。填写客户的任何其他银行卡账户进行检测。", "SetPinMsg": "在客户进行交易之前创建银行系统的登录账户。", "userCreationMsg": "登记网银账户的昵称。", "VerifyAddressMsg": "验证用户住址是否真实有效。", "VerifyBiometricsMsg": "人脸识别。提交包含客户脸部信息的照片。", "acknowledgeerror": "请勾选同意最终用户隐私政策。", "currentStatus": "工作流状态：", "addressReview": "地址审核", "addressReviewError": "请选择地址审核", "invalidAddress": "地址审核不通过", "preview": "预览", "download": "下载", "CreditLimitAllocation": "信用额度分配", "CreditLimitAllocationMsg": "为客户账户分配信用额度", "privacyPolicyContent": "<p>尊敬的客户：</p><p>&nbsp;</p><p>感谢您选择本银行作为您的金融服务提供商。为了保障您的资金安全、维护双方权益，并遵循相关法律法规要求，特此向您明确个人银行开户的相关声明条款。请您在仔细阅读并充分理解以下内容后，进行开户操作或签署相关文件。</p><p>&nbsp;</p><p>一、客户身份确认与信息真实性</p><p>&nbsp;</p><p>本人声明：本人自愿向本银行申请开立个人银行账户，并承诺所提供的所有个人信息（包括但不限于姓名、性别、出生日期、国籍、联系方式、职业、住址等）均真实、准确、完整、有效，无虚假、误导或遗漏之处。</p><p>身份验证：本人同意并授权本银行通过合法途径（包括但不限于公安部门、征信机构等）核实本人身份信息的真实性，并接受银行为完成开户流程所进行的必要身份验证措施。</p><p><br></p><p>二、账户使用与管理</p><p>&nbsp;</p><p>合法合规使用：本人承诺将严格按照国家法律法规、监管要求及本银行的相关规定使用账户，不进行任何违法、违规或损害银行及其他客户利益的活动，包括但不限于洗钱、恐怖融资、逃税、欺诈等行为。</p><p>账户安全：本人将妥善保管账户密码、电子银行登录信息、交易验证码等敏感信息，不泄露给任何第三方，并承担因个人保管不善导致的账户资金损失风险。</p><p>及时通知：如本人联系方式、住址等个人信息发生变更，或发现账户存在异常交易、被盗用等风险情况，将立即通知本银行，并配合银行采取相应措施。</p><p><br></p><p>三、费用与利率</p><p>&nbsp;</p><p>费用说明：本人已了解并同意按照本银行公布的收费标准支付账户管理费、交易手续费等相关费用。具体费用标准以银行最新公告为准。</p><p>利率政策：本银行账户的存款利率及计息方式遵循中国人民银行及本银行的相关规定执行，具体以银行公告为准。</p><p><br></p><p>四、隐私保护</p><p>&nbsp;</p><p>本银行承诺将严格遵守《中华人民共和国个人信息保护法》等法律法规，对客户的个人信息采取严格的安全保密措施，未经客户同意，不向任何第三方披露客户的个人信息，但法律法规另有规定或经客户授权的情形除外。</p><p>&nbsp;</p><p>五、协议变更与终止</p><p>&nbsp;</p><p>协议变更：本银行有权根据法律法规、监管要求及业务需要调整本声明条款。如有变更，银行将通过官方网站、营业网点等渠道公告，客户应定期查阅并遵守最新条款。</p><p>账户终止：如客户需注销账户，应提前向本银行提出书面申请，并按照银行规定办理相关手续。账户注销后，本银行将停止提供相应服务，但保留根据法律法规要求处理账户信息的权利。</p><p><br></p><p>六、争议解决</p><p>&nbsp;</p><p>双方因执行本声明条款发生的争议，应首先通过友好协商解决；协商不成时，可提交至本银行所在地人民法院诉讼解决。</p><p>&nbsp;</p><p>七、其他</p><p>&nbsp;</p><p>本声明条款自客户签字确认之日起生效，并与本银行其他相关规定共同构成双方之间的完整协议。</p>", "endUserPrivacyPolicy": "同意最终用户隐私政策"}