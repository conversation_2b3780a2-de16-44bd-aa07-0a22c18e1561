const context = require.context('@/lang/locales/business', true, /\.json$/)

function getBusinessLocales() {
  const locales = { en: {}, cht: {}, cn: {}}

  context.keys().forEach(key => {
    const langPath = key.replace('./', '').replace('.json', '')
    const [business, lang] = langPath.split('/')

    if (locales[lang]) {
      if (!locales[lang][business]) {
        locales[lang][business] = {}
      }

      locales[lang][business] = {
        ...locales[lang][business],
        ...context(key)
      }
    }
  })

  return locales
}

export const { en: businessEnLocales, cht: businessChtLocales, cn: businessZhLocales } = getBusinessLocales()
