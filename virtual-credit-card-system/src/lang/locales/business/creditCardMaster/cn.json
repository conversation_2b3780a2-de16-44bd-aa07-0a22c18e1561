{"viewDetails": "查看信用卡账户表", "editDetails": "编辑信用卡账户表", "createDetails": "创建信用卡账户表", "countryCode": "国家代码", "countryCodePlaceholder": "请输入国家代码", "clearingCode": "银行代码", "clearingCodePlaceholder": "请输入银行代码", "branchCode": "分行代码", "branchCodePlaceholder": "请输入分行代码", "sandBoxId": "沙箱ID", "sandBoxIdPlaceholder": "请输入沙箱ID", "creditCardNumber": "信用卡号", "creditCardNumberPlaceholder": "请输入信用卡号", "holderName": "持有人", "holderNamePlaceholder": "请输入持有人", "creditCardType": "信用卡类型", "creditCardTypePlaceholder": "请输入信用卡类型", "issuanceDate": "颁发日期", "issuanceDatePlaceholder": "请输入颁发日期", "expiryDate": "到期日", "expiryDatePlaceholder": "请输入到期日", "verificationCode": "校验码", "verificationCodePlaceholder": "请输入校验码", "creditCardStatus": "信用卡状态", "creditCardStatusPlaceholder": "请输入信用卡状态", "reportLossDate": "挂失日期", "reportLossDatePlaceholder": "请输入挂失日期", "reportCancelDate": "注销日期", "reportCancelDatePlaceholder": "请输入注销日期", "creditCardLimitApprovedDate": "信用卡额度批准日期", "creditCardLimitApprovedDatePlaceholder": "请输入信用卡额度批准日期", "nextLimitReviewDate": "下一次额度审核日期", "nextLimitReviewDatePlaceholder": "请输入下一次额度审核日期", "limitApprovalStatus": "额度审批状态", "limitApprovalStatusPlaceholder": "请输入额度审批状态", "approvedLimit": "授权额度", "approvedLimitPlaceholder": "请输入授权额度", "cashAdvanceLimit": "提现额度", "cashAdvanceLimitPlaceholder": "请输入提现额度", "usedLimit": "已用额度", "usedLimitPlaceholder": "请输入已用额度", "availableLimit": "可用额度", "availableLimitPlaceholder": "请输入可用额度", "associatedCusNum": "关联客户编号", "associatedCusNumPlaceholder": "请输入关联客户编号", "repaymentCycle": "还款周期", "repaymentCyclePlaceholder": "请输入还款周期", "rewardPoint": "累计有效积分", "rewardPointPlaceholder": "请输入累计有效积分", "createDate": "信用卡开户时间", "createDatePlaceholder": "请输入信用卡开户时间", "remarks": "备注", "remarksPlaceholder": "请输入备注", "activationTipMessage": "当前信用卡未激活,是否激活信用卡?", "customerNumber": "客户编号", "customerNumberPlaceholder": "请输入客户编号", "mobilePhoneNumber": "手机号码", "customerId": "证件号", "customerIdType": "客户证件类型", "issueCountry": "签发国家", "dateOfBirth": "出生日期", "chineseName": "中文姓名", "gender": "性别", "nationality": "国籍", "permanentResidenceStatus": "永久居住状态", "maritalStatus": "婚姻状况", "education": "教育程度", "residentialAddress": "居住地址", "mailingAddress": "邮件地址", "residencePhoneNumber": "居住电话号码", "wechatId": "微信ID", "accommodation": "住宿", "yearsOfResidence": "居住年限", "occupation": "职业", "employerCompanyName": "雇主公司名称", "position": "职位", "companyAddress": "公司地址", "companyPhoneNumber": "公司电话号码", "yearsOfServices": "服务年限", "monthlySalary": "月薪(HKD)", "emailAddress": "电子邮件地址", "firstName": "名字", "lastName": "姓氏", "lastUpdatedDate": "上次更新日期", "sensitiveStatus": "敏感状态", "createTime": "创建时间", "aggregateCreditLimitCurrency": "货币类型", "aggregateCreditLimit": "总信用额度", "aggregateCreditLimitUtilization": "总已使用信用额度", "accountNumber": "账号", "creditCurrency": "货币类型", "creditLimit": "信用额度", "creditLimitApprovalDate": "信用额度批准日期", "creditProductType": "信用产品类型", "creditLimitUtilization": "已使用额度", "creditLimitSecuredStatus": "信用额度担保状态", "depositAsset": "存款投资金额", "termDepositAsset": "定期存款市值", "stockAsset": "股票市值", "fundAsset": "基金市值", "fexAsset": "外汇市值", "bondAsset": "债券市值", "creditCardLiability": "信用卡欠款", "loanLiability": "贷款负债", "wealthPortfolioCurrency": "货币类型"}