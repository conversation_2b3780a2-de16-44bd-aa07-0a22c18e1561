{"customer": {"firstName": "First Name", "lastName": "Last Name", "customerId": "Customer ID", "customerIdType": "Customer ID Type", "issueCountry": "Issue Country", "dateOfBirth": "Date of Birth", "otherLanguageName": "Other Language Name", "gender": "Gender", "nationality": "Nationality", "department": "Department", "role": "Role", "industry": "Industry", "position": "Position", "customerNumber": "Customer Number", "accountNumber": "Account Number", "firstNamePlaceholder": "Please enter first name", "lastNamePlaceholder": "Please enter last name", "customerIdPlaceholder": "Please enter customer ID", "customerIdTypePlaceholder": "Please select customer ID type", "issueCountryPlaceholder": "Please select Issue Country", "dateOfBirthPlaceholder": "Please select date of birth", "otherLanguageNamePlaceholder": "Please enter other language name", "genderPlaceholder": "Please select gender", "nationalityPlaceholder": "Please select nationality", "departmentPlaceholder": "Please enter department", "rolePlaceholder": "Please enter role", "industryPlaceholder": "Please enter industry", "positionPlaceholder": "Please enter position", "customerNumberPlaceholder": "Please enter customer number", "accountNumberPlaceholder": "Please enter account number"}, "facta": {"editDetails": "Edit FACTA", "createDetails": "Add FACTA", "viewDetails": "View FACTA Details", "validityPeriod": "Validity Period", "taxpayerIdentificationNumber": "Taxpayer Identification Number", "name": "Name", "report": "Report", "factaStatus": "FACTA Status", "remark": "Remark", "validityPeriodPlaceholder": "Please select validity period", "taxpayerIdentificationNumberPlaceholder": "Please enter taxpayer identification number", "factaStatusPlaceholder": "Please select FACTA status", "reportPlaceholder": "Please select report", "remarkPlaceholder": "Please enter remark"}, "crs": {"editDetails": "Edit CRS", "createDetails": "Add CRS", "viewDetails": "View CRS Details", "validityPeriod": "Validity Period", "foreignCountryTaxNumber": "Foreign Country Tax Number", "name": "Name", "crsStatus": "CRS Status", "report": "Report", "remark": "Remark", "validityPeriodPlaceholder": "Please select validity period", "foreignCountryTaxNumberPlaceholder": "Please enter foreign country tax number", "crsStatusPlaceholder": "Please select CRS status", "reportPlaceholder": "Please select report", "remarkPlaceholder": "Please enter remark"}}