{"viewDetails": "View Credit Card Statement", "editDetails": "Edit Credit Card Statement", "createDetails": "Create Credit Card Statement", "creditCardNumber": "Credit Card Number", "creditCardNumberPlaceholder": "Please enter credit card number", "statementDate": "Statement Date", "statementDatePlaceholder": "Please enter statement date", "statementBalance": "Statement Balance", "statementBalancePlaceholder": "Please enter statement balance", "repaymentDueDate": "Repayment Due Date", "repaymentDueDatePlaceholder": "Please enter repayment due date", "minimumPayment": "Minimum Payment", "minimumPaymentPlaceholder": "Please enter minimum payment", "repaymentAmount": "Repayment Amount", "repaymentAmountPlaceholder": "Please enter repayment amount", "overdueInterest": "Overdue Interest", "overdueInterestPlaceholder": "Please enter overdue interest", "statementStorePath": "Statement Store Path", "statementStorePathPlaceholder": "Please enter statement store path", "points": "Points", "pointsPlaceholder": "Please enter points", "pointExpireDate": "Point Expire Date", "pointExpireDatePlaceholder": "Please enter point expire date", "createDate": "Create Date", "createDatePlaceholder": "Please enter create date", "statementStatus": "Statement Status", "statementStatusPlaceholder": "Please enter statement status", "interestRate": "Interest Rate", "interestRatePlaceholder": "Please enter interest rate", "overdueSurcharge": "Overdue Surcharge", "overdueSurchargePlaceholder": "Please enter overdue surcharge", "creditCardRepayment": {"debitAccountNumber": "Debit Account Number", "repaymentAmount": "Repayment Amount(HKD)", "remarks": "Remarks", "debitAccountNumberPlaceholder": "Please select debit account number", "repaymentAmountPlaceholder": "Please enter repayment Amount", "remarksPlaceholder": "Please enter remarks"}, "simulator": {"statementAmount": "Statement Amount(HKD)", "statementAmountPlaceholder": "Please enter statement amount", "minimumRepaymentPct": "Minimum Repayment Pct(%)", "minimumRepaymentPctPlaceholder": "Please enter minimum repayment pct(0-100)", "baseMinimumRepayment": "Base Minimum Repayment(HKD)", "baseMinimumRepaymentPlaceholder": "Please enter base minimum repayment", "minimumRepaymentAmountOfTheStatement": "Minimum Repayment Amount of The Statement(HKD)", "statementDate": "Statement Date", "statementDatePlaceholder": "Please select statement date", "billingCycle": "Billing Cycle", "billingCyclePlaceholder": "Please select billing cycle", "dueDate": "Due Date", "dueDatePlaceholder": "Please select due date", "minimumRepaymentAPR": "Minimum Repayment APR(%)", "minimumRepaymentAPRPlaceholder": "Please enter minimum repayment APR", "transaction": "Transaction", "transactionType": "Transaction Type", "transactionTypePlaceholder": "Please select transaction type", "transactionAmount": "Transaction Amount(HKD)", "transactionAmountPlaceholder": "Please enter transaction amount", "transactionDate": "Transaction Date", "transactionDatePlaceholder": "Please select transaction Date", "overdueInterest": "Overdue Interest(HKD)", "today": "Today's Date", "todayPlaceholder": "Please select data", "totalOverdueInterest": "Total Overdue Interest(HKD)", "totalOverdueInterestPlaceholder": "Please enter total overdue interest", "lateCharge": "Late Charge(HKD)", "lateChargePlaceholder": "Please enter late charge", "nullRepaymentAPR": "Null Repayment APR(%)", "nullRepaymentAPRPlaceholder": "Please enter null repayment APR"}}