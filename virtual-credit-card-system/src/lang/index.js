import Vue from 'vue'
import VueI18n from 'vue-i18n'
import Cookies from 'js-cookie'
import elementEnLocale from 'element-ui/lib/locale/lang/en'
import elementZhLocale from 'element-ui/lib/locale/lang/zh-CN'
import elementChtLocale from 'element-ui/lib/locale/lang/zh-TW'
import zhSOILocale from '@/lang/locales/zh.json'
import enSOILocale from '@/lang/locales/en.json'
import chtSOILocale from '@/lang/locales/cht.json'
import { businessEnLocales, businessChtLocales, businessZhLocales } from '@/lang/locales/business'

Vue.use(VueI18n)

const messages = {
  en: {
    ...elementEnLocale,
    ... { vcs: { ...enSOILocale.vcs, ...businessEnLocales }}
  },
  zh: {
    ...elementZhLocale,
    ... { vcs: { ...zhSOILocale.vcs, ...businessZhLocales }}
  },
  cht: {
    ...chtSOILocale,
    ...elementChtLocale,
    ... { vcs: { ...chtSOILocale.vcs, ...businessChtLocales }}
  }
}

const i18n = new VueI18n({
  locale: Cookies.get('language') || 'zh', // set locale
  messages // set locale messages
})

export default i18n
