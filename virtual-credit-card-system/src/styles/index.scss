@import './variables.scss';
@import './mixin.scss';
@import './transition.scss';
@import './element-ui.scss';
@import './sidebar.scss';
@import './element-variables.scss';

body {
  height: 100%;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Arial, sans-serif;
}

label {
  font-weight: 700;
}

html {
  height: 100%;
  box-sizing: border-box;
}

#app {
  height: 100%;
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

a:focus,
a:active {
  outline: none;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

div:focus {
  outline: none;
}

.clearfix {
  &:after {
    visibility: hidden;
    display: block;
    font-size: 0;
    content: " ";
    clear: both;
    height: 0;
  }
}

// main-container global css
.app-container {
  padding: 20px;
}

.table-footer {
  text-align: right;
  margin-top: 10px;
}

.text-primary {
  color: $--color-primary;
}

.text-danger {
  color: $--color-danger;
}

a {
  text-decoration: none;
  color: #5f9ea0;
}

.business-container {
  .search-form-item {
    width: 100%;
  }
  .data-action-button-group {
    margin-bottom: 10px;
    display: flex;
    justify-content: space-between;
  }
  .bottom-actions-button-group {
    text-align: right;
  }
}
