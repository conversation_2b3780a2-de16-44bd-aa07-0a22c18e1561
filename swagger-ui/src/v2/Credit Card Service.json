{"swagger": "2.0", "info": {"description": "APIs here are all about credit card service. Before calling these APIs, please make sure you’ve learnt the business logic between these APIs.", "version": "1.0", "title": "Credit Card Service", "contact": {"name": "<EMAIL>"}}, "schemes": ["https"], "host": "simnectz.hsu.edu.hk/lbsgateway/creditcard-experience", "basePath": "/", "tags": [{"name": "Merchant", "description": "Merchant information and its product/transaction enquiry."}, {"name": "Repayment", "description": "Outstanding credit card bill enquiry  & repayment."}, {"name": "Account Information & Maintenance", "description": "Credit card processing includes application/cancellation and limit ."}, {"name": "Rewards", "description": "Credit card reward point system includes reward point redemption, history and point enquiry."}, {"name": "Transaction", "description": "Transaction processing in credit card & transaction details enquiry."}], "paths": {"/creditcard/creditCardRepayment": {"post": {"tags": ["Repayment"], "summary": "This API is designed to make a credit card repayment.", "description": "Before calling this API, you may need to call /creditcard/outstandingPayment to check your outstanding payment details.", "operationId": "creditCardRepaymentUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.<br>maxLength: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "Message ID in 128 bit random UUID format generated uniquely for every request by F/E side.<br>maxLength: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "client_id generated during consumer onboarding. <br>maxLength: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "repaymentModel", "description": "repaymentModel", "required": true, "schema": {"$ref": "#/definitions/RepaymentModel"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ResultUtil"}}}}}, "/creditcard/creditLimitDetails": {"post": {"tags": ["Account Information & Maintenance"], "summary": "This API is designed to retrieve a credit card limit details.", "description": "Before doing a transaction, you may want to call this API and check your credit card limit details to make sure the available limit is enough.", "operationId": "retCreditLimitDetailsUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.<br>maxLength: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "Message ID in 128 bit random UUID format generated uniquely for every request by F/E side.<br>maxLength: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "client_id generated during consumer onboarding. <br>maxLength: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "creditCardModel", "description": "creditCardModel", "required": true, "schema": {"$ref": "#/definitions/CreditCardModel"}}], "responses": {"200": {"description": "Query completed successfully.(Returned By Get)", "schema": {"$ref": "#/definitions/ResultUtil«LimitsRetrieveModel»"}}, "201": {"description": "Normal execution. The request has succeeded. (Returned By Post)", "schema": {"$ref": "#/definitions/ResultUtil«LimitsRetrieveModel»"}}, "403": {"description": "Token has incorrect scope or a security policy was violated. Action: Please check whether you’re using the right token with the legal authorized user account."}, "404": {"description": "The requested deposit account does not exist.Action: Please make sure the account number and account type you’re inputting are correct."}, "500": {"description": "Something went wrong on the API gateway or micro-service. Action: check your network and try again later."}}}}, "/creditcard/limitDecrease": {"post": {"tags": ["Account Information & Maintenance"], "summary": "This API is designed to decrease a credit card limit.", "description": "For example, the customer's credit card limit is now 20000 HKD. If you want to reduce it to 15000 HKD, please directly call the API to complete the business operation. If you need to increase the quota, please call the/creditcard/limitIncrease API.", "operationId": "limitDecreaseUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.<br>maxLength: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "Message ID in 128 bit random UUID format generated uniquely for every request by F/E side.<br>maxLength: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "client_id generated during consumer onboarding. <br>maxLength: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "limitsChangeInputModel", "description": "limitsChangeInputModel", "required": true, "schema": {"$ref": "#/definitions/LimitsChangeInputModel"}}], "responses": {"200": {"description": "Query completed successfully.(Returned By Get)", "schema": {"$ref": "#/definitions/ResultUtil"}}, "201": {"description": "Normal execution. The request has succeeded. (Returned By Post)", "schema": {"$ref": "#/definitions/ResultUtil"}}, "403": {"description": "Token has incorrect scope or a security policy was violated. Action: Please check whether you’re using the right token with the legal authorized user account."}, "404": {"description": "The requested deposit account does not exist.Action: Please make sure the account number and account type you’re inputting are correct."}, "500": {"description": "Something went wrong on the API gateway or micro-service. Action: check your network and try again later."}}}}, "/creditcard/limitIncrease": {"post": {"tags": ["Account Information & Maintenance"], "summary": "This API is designed to increase a credit card limit.", "description": "For example, the customer's credit card limit is now 20000 HKD. If you want to increase it to 25000 HKD, please call the API to complete the business operation. If you need to reduce the quota, please call the/creditcard/limitDecrease API.", "operationId": "limitIncreaseUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.<br>maxLength: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "Message ID in 128 bit random UUID format generated uniquely for every request by F/E side.<br>maxLength: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "client_id generated during consumer onboarding. <br>maxLength: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "limitsChangeInputModel", "description": "limitsChangeInputModel", "required": true, "schema": {"$ref": "#/definitions/IncreaseLimitsChangeInputModel"}}], "responses": {"200": {"description": "Query completed successfully.(Returned By Get)", "schema": {"$ref": "#/definitions/ResultUtil"}}, "201": {"description": "Normal execution. The request has succeeded. (Returned By Post)", "schema": {"$ref": "#/definitions/ResultUtil"}}, "403": {"description": "Token has incorrect scope or a security policy was violated. Action: Please check whether you’re using the right token with the legal authorized user account."}, "404": {"description": "The requested deposit account does not exist.Action: Please make sure the account number and account type you’re inputting are correct."}, "500": {"description": "Something went wrong on the API gateway or micro-service. Action: check your network and try again later."}}}}, "/creditcard/lossReporting": {"post": {"tags": ["Account Information & Maintenance"], "summary": "This API is designed to report a credit card loss.", "description": " After reporting the loss by calling this  API, the account can no longer be traded.", "operationId": "lossReportingUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.<br>maxLength: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "Message ID in 128 bit random UUID format generated uniquely for every request by F/E side.<br>maxLength: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "client_id generated during consumer onboarding. <br>maxLength: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "creditCardModel", "description": "creditCardModel", "required": true, "schema": {"$ref": "#/definitions/CreditCardModel"}}], "responses": {"200": {"description": "Query completed successfully.(Returned By Get)", "schema": {"$ref": "#/definitions/ResultUtil"}}, "201": {"description": "Normal execution. The request has succeeded. (Returned By Post)", "schema": {"$ref": "#/definitions/ResultUtil"}}, "403": {"description": "Token has incorrect scope or a security policy was violated. Action: Please check whether you’re using the right token with the legal authorized user account."}, "404": {"description": "The requested deposit account does not exist.Action: Please make sure the account number and account type you’re inputting are correct."}, "500": {"description": "Something went wrong on the API gateway or micro-service. Action: check your network and try again later."}}}}, "/creditcard/outstandingPayment": {"post": {"tags": ["Repayment"], "summary": "This API is designed to get a credit card outstanding payment details.", "description": "Before calling /creditcard/creditCardRepayment API, you may need to call this API to check your outstanding payment details.", "operationId": "outstandingPaymentUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.<br>maxLength: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "Message ID in 128 bit random UUID format generated uniquely for every request by F/E side.<br>maxLength: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "client_id generated during consumer onboarding. <br>maxLength: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "creditCardModel", "description": "creditCardModel", "required": true, "schema": {"$ref": "#/definitions/CreditCardModel"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ResultUtil«PaymentDetailsModel»"}}}}}, "/creditcard/transactionDetails": {"post": {"tags": ["Transaction"], "summary": "The API is designed to retrieve a credit card transaction details.", "description": "After making any transaction, you may want to call this API to check your credit card transaction details.", "operationId": "transactionDetailsUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.<br>maxLength: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "Message ID in 128 bit random UUID format generated uniquely for every request by F/E side.<br>maxLength: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "client_id generated during consumer onboarding. <br>maxLength: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "reTransactionDetailsModel", "description": "reTransactionDetailsModel", "required": true, "schema": {"$ref": "#/definitions/ReTransactionDetailsModel"}}], "responses": {"200": {"description": "Query completed successfully.(Returned By Get)", "schema": {"$ref": "#/definitions/ResultUtil«List«TransactionDetailsModel»»"}}, "201": {"description": "Normal execution. The request has succeeded. (Returned By Post)", "schema": {"$ref": "#/definitions/ResultUtil«List«TransactionDetailsModel»»"}}, "403": {"description": "Token has incorrect scope or a security policy was violated. Action: Please check whether you’re using the right token with the legal authorized user account."}, "404": {"description": "The requested deposit account does not exist.Action: Please make sure the account number and account type you’re inputting are correct."}, "500": {"description": "Something went wrong on the API gateway or micro-service. Action: check your network and try again later."}}}}, "/creditcard/transactionPosting": {"post": {"tags": ["Transaction"], "summary": "This API is designed to simulate credit card posting.", "description": "This API is used to simulate credit card transactions.", "operationId": "transactionPostingUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.<br>maxLength: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "Message ID in 128 bit random UUID format generated uniquely for every request by F/E side.<br>maxLength: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "client_id generated during consumer onboarding. <br>maxLength: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "postTransDeInputModel", "description": "postTransDeInputModel", "required": true, "schema": {"$ref": "#/definitions/PostTransDeInputModel"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ResultUtil"}}}}}, "/merchant/merchantEnquiry/{merchantNumber}": {"get": {"tags": ["Merchant"], "summary": "This API is designed to retrieve a credit card merchant details.", "description": "Before making a transaction posting, you may want to check the merchant information by calling this API.", "operationId": "merchantEnquiryUsingGET", "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.<br>maxLength: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "Message ID in 128 bit random UUID format generated uniquely for every request by F/E side.<br>maxLength: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "client_id generated during consumer onboarding. <br>maxLength: 50</br>", "required": true, "type": "string", "default": "devin"}, {"name": "merchantNumber", "in": "path", "description": "This parameter is required in the GET method eg:HK0001001000009.<br>maxLength: 35</br>", "required": true, "type": "string"}], "responses": {"200": {"description": "Query completed successfully.(Returned By Get)", "schema": {"$ref": "#/definitions/ResultUtil«ReMerchantReferenceModel»"}}, "201": {"description": "Normal execution. The request has succeeded. (Returned By Post)", "schema": {"$ref": "#/definitions/ResultUtil«ReMerchantReferenceModel»"}}, "403": {"description": "Token has incorrect scope or a security policy was violated. Action: Please check whether you’re using the right token with the legal authorized user account."}, "404": {"description": "The requested deposit account does not exist.Action: Please make sure the account number and account type you’re inputting are correct."}, "500": {"description": "Something went wrong on the API gateway or micro-service. Action: check your network and try again later."}}}}, "/merchant/productEnquiry": {"post": {"tags": ["Merchant"], "summary": "This API is designed to retrieve merchant products.", "description": "You may need to call this AP to check available merchant products before calling /point/redemption API.", "operationId": "productEnquiryUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.<br>maxLength: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "Message ID in 128 bit random UUID format generated uniquely for every request by F/E side.<br>maxLength: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "client_id generated during consumer onboarding. <br>maxLength: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "searchPointProductModel", "description": "searchPointProductModel", "required": true, "schema": {"$ref": "#/definitions/SearchPointProductModel"}}], "responses": {"200": {"description": "Query completed successfully.(Returned By Get)", "schema": {"$ref": "#/definitions/ResultUtil«List«ProductModel»»"}}, "201": {"description": "Normal execution. The request has succeeded. (Returned By Post)", "schema": {"$ref": "#/definitions/ResultUtil«List«ProductModel»»"}}, "403": {"description": "Token has incorrect scope or a security policy was violated. Action: Please check whether you’re using the right token with the legal authorized user account."}, "404": {"description": "The requested deposit account does not exist.Action: Please make sure the account number and account type you’re inputting are correct."}, "500": {"description": "Something went wrong on the API gateway or micro-service. Action: check your network and try again later."}}}}, "/merchant/transactions": {"post": {"tags": ["Merchant"], "summary": "This API is designed for credit card merchants to check their transaction details.", "description": "This API is used to query the results by entering the merchant number, start and end time. However, if the customer has not transacted with the merchant yet, you may need to call/creditcard/transactionPosting to simulate the transaction process and generate transaction records.", "operationId": "transactionsUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.<br>maxLength: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "Message ID in 128 bit random UUID format generated uniquely for every request by F/E side.<br>maxLength: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "client_id generated during consumer onboarding. <br>maxLength: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "queryHistoryModel", "description": "queryHistoryModel", "required": true, "schema": {"$ref": "#/definitions/QueryHistoryModel"}}], "responses": {"200": {"description": "Query completed successfully.(Returned By Get)", "schema": {"$ref": "#/definitions/ResultUtil«ReHistoryModel»"}}, "201": {"description": "Normal execution. The request has succeeded. (Returned By Post)", "schema": {"$ref": "#/definitions/ResultUtil«ReHistoryModel»"}}, "403": {"description": "Token has incorrect scope or a security policy was violated. Action: Please check whether you’re using the right token with the legal authorized user account."}, "404": {"description": "The requested deposit account does not exist.Action: Please make sure the account number and account type you’re inputting are correct."}, "500": {"description": "Something went wrong on the API gateway or micro-service. Action: check your network and try again later."}}}}, "/point/redemption": {"post": {"tags": ["Rewards"], "summary": "This API allows credit card users to redeem credit card reward points.", "description": "Before calling this API, you may want to call /point/totalPoint to check the total available reward points in your credit card and /merchant/productEnquiry to check available merchant products.", "operationId": "redemptionUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.<br>maxLength: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "Message ID in 128 bit random UUID format generated uniquely for every request by F/E side.<br>maxLength: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "client_id generated during consumer onboarding. <br>maxLength: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "pointRedemptionModel", "description": "pointRedemptionModel", "required": true, "schema": {"$ref": "#/definitions/PointRedemptionModel"}}], "responses": {"200": {"description": "Query completed successfully.(Returned By Get)", "schema": {"$ref": "#/definitions/ResultUtil«TotalConsumptionModel»"}}, "201": {"description": "Normal execution. The request has succeeded. (Returned By Post)", "schema": {"$ref": "#/definitions/ResultUtil«TotalConsumptionModel»"}}, "400": {"description": "Situation: Request has malformed, missing or non-compliant JSON body, URL parameters or header fields.Action: Please go back and refer to the method Model details to recheck the message and make sure your input is correct."}, "401": {"description": "Situation: Authorization header missing or invalid token. Action: Please make sure the token, key or relevant parameters are correct."}, "403": {"description": "Situation: Token has incorrect scope or a security policy was violated. Action: Please check whether you’re using the right token with the legal authorized user account."}, "404": {"description": "Situation: The requested deposit account does not exist.Action: Please make sure the account number and account type you’re inputting are correct."}, "405": {"description": "Situation: A request method is not supported for the requested deposit account. Action: Please make sure that you’re using a POST method to get the requested deposit account details."}, "408": {"description": "Situation: The server timed out waiting for the request. Action: Try again later."}, "500": {"description": "Situation: Something went wrong on the API gateway or micro-service. Action: check your network and try again later."}, "503": {"description": "Situation: Service version deprecation. Action: contact API platform support to fix the service issue."}}}}, "/point/redemptionHistory": {"post": {"tags": ["Rewards"], "summary": "This API is designed to retrieve a credit card reward point redemption records.", "description": "After calling /point/redemption API, you may want to call this API to check the redemption records.", "operationId": "redemptionHistoryUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.<br>maxLength: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "Message ID in 128 bit random UUID format generated uniquely for every request by F/E side.<br>maxLength: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "client_id generated during consumer onboarding. <br>maxLength: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "checkCreditcardPointModel", "description": "checkCreditcardPointModel", "required": true, "schema": {"$ref": "#/definitions/CheckCreditcardPointModel"}}], "responses": {"200": {"description": "Query completed successfully.(Returned By Get)", "schema": {"$ref": "#/definitions/ResultUtil«List«PointRedemptionHistoryModel»»"}}, "201": {"description": "Normal execution. The request has succeeded. (Returned By Post)", "schema": {"$ref": "#/definitions/ResultUtil«List«PointRedemptionHistoryModel»»"}}, "403": {"description": "Token has incorrect scope or a security policy was violated. Action: Please check whether you’re using the right token with the legal authorized user account."}, "404": {"description": "The requested deposit account does not exist.Action: Please make sure the account number and account type you’re inputting are correct."}, "500": {"description": "Something went wrong on the API gateway or micro-service. Action: check your network and try again later."}}}}, "/point/totalPoint": {"post": {"tags": ["Rewards"], "summary": "This API is designed to retrieve a creditcard total points.", "description": "This API is used when the customer wants to query the total credit card points, or before exchanging the points for products. Every time a customer makes a transaction, new points will be generated. To simulate a credit card transaction, please call/creditcard/transactionPosting", "operationId": "totalPointUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.<br>maxLength: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "Message ID in 128 bit random UUID format generated uniquely for every request by F/E side.<br>maxLength: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "client_id generated during consumer onboarding. <br>maxLength: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "checkCreditcardPointModel", "description": "checkCreditcardPointModel", "required": true, "schema": {"$ref": "#/definitions/CreditcardPointModel"}}], "responses": {"200": {"description": "Query completed successfully.(Returned By Get)", "schema": {"$ref": "#/definitions/ResultUtil«TotalPointModel»"}}, "201": {"description": "Normal execution. The request has succeeded. (Returned By Post)", "schema": {"$ref": "#/definitions/ResultUtil«TotalPointModel»"}}, "403": {"description": "Token has incorrect scope or a security policy was violated. Action: Please check whether you’re using the right token with the legal authorized user account."}, "404": {"description": "The requested deposit account does not exist.Action: Please make sure the account number and account type you’re inputting are correct."}, "500": {"description": "Something went wrong on the API gateway or micro-service. Action: check your network and try again later."}}}}}, "definitions": {"CheckCreditcardPointModel": {"type": "object", "required": ["creditcardnumber", "index", "items"], "properties": {"creditcardnumber": {"type": "string", "example": ****************, "description": "The number of Credit Card.<br> maxLength:16 </br>", "allowEmptyValue": false}, "index": {"type": "number", "example": 0.0, "description": "an indicator to identify where your respone result will start to show.<br> maxLength:4 </br>", "allowEmptyValue": false}, "items": {"type": "number", "example": 3.0, "description": "the number of items you'd like to get in the response result.<br> maxLength:4 </br>", "allowEmptyValue": false}}, "title": "CheckCreditcardPointModel"}, "CreditCardModel": {"type": "object", "required": ["creditcardnumber"], "properties": {"creditcardnumber": {"type": "string", "example": 5000010000000032, "description": "The number of Credit Card.<br> maxLength:16 </br>", "allowEmptyValue": false}}, "title": "CreditCardModel"}, "CreditCardOpenningModel": {"type": "object", "required": ["approvedlimit", "cashadvancelimit", "creditcardtype", "expirydate", "issuancedate", "repaymentcycle", "verificationcode"], "properties": {"approvedlimit": {"type": "number", "example": 10000.0, "description": "The approved limit of a credit card.<br>maxLength: 18</br>", "allowEmptyValue": false}, "cashadvancelimit": {"type": "number", "example": 5000.0, "description": "The cashadvance limit of a credit card.<br>maxLength: 18</br>", "allowEmptyValue": false}, "creditcardtype": {"type": "string", "example": "V", "description": "The Type of Credit Card.<br>maxLength: 4</br>", "allowEmptyValue": false}, "expirydate": {"type": "number", "example": *************.0, "description": "The expirydate of a credit card.<br>maxLength: 20</br>", "allowEmptyValue": false}, "issuancedate": {"type": "number", "example": ************.0, "description": "The issuance date of a credit card.<br>maxLength: 20</br>", "allowEmptyValue": false}, "repaymentaccountnum": {"type": "string", "example": "***********************", "description": "The debit account number which is used to make repayments. <br>maxLength: 34</br>", "allowEmptyValue": false}, "repaymentcycle": {"type": "string", "example": "M", "description": "The repayment cycle of the credit card. \nPossible value: M-Monthly.<br>maxLength: 5</br>", "allowEmptyValue": false}, "verificationcode": {"type": "string", "example": "001", "description": "The verification code of a credit card.<br>maxLength: 3</br>", "allowEmptyValue": false}}, "title": "CreditCardOpenningModel"}, "CreditcardPointModel": {"type": "object", "required": ["creditcardnumber"], "properties": {"creditcardnumber": {"type": "string", "example": ****************, "description": "The number of Credit Card.<br> maxLength:16 </br>", "allowEmptyValue": false}}, "title": "CreditcardPointModel"}, "CustomerCurrencyAccountModel": {"type": "object", "properties": {"account": {"$ref": "#/definitions/CreditCardOpenningModel"}, "customer": {"$ref": "#/definitions/CustomerMasterModel"}}, "title": "CustomerCurrencyAccountModel"}, "CustomerMasterModel": {"type": "object", "required": ["chinesename", "customerid", "emailaddress", "firstname", "issuecountry", "lastname", "mailingaddress", "mobilephonenumber"], "properties": {"accommodation": {"type": "string", "example": "S", "description": "The ownership of the provided residential address. If it is set S, that means the accommodation is self owned.Possible values:S-Self Owned, P-Private property, H-Home ownership scheme,F-Friends/Relatives, U-Public Housing,R-Rented, Q-Quarters.<br>maxLength: 10</br>", "allowEmptyValue": false}, "chinesename": {"type": "string", "example": "王研", "description": "The Chinese name of the customer shown in his/her ID card.<br>maxLength: 50</br>", "allowEmptyValue": false}, "companyaddress": {"type": "string", "example": "228 Pok Fu Lam Road,Aberdeen,Hong Kong", "description": "The physical address of the company where the customer works.<br>maxLength: 490</br>", "allowEmptyValue": false}, "companyphonenumber": {"type": "string", "example": 24827781, "description": "The phone number of the company where the customer works.<br>maxLength: 34</br>", "allowEmptyValue": false}, "customerid": {"type": "string", "example": "U735535(9)", "description": "The unique identification of a customer in his/her country.<br>maxLength: 50</br>", "allowEmptyValue": false}, "dateofbirth": {"type": "number", "example": 178163911000.0, "description": "The birthday of the customer.<br>maxLength: 20</br>", "allowEmptyValue": false}, "education": {"type": "string", "example": "U", "description": "The education level of this customer. When it is set U, that means the customer education level is university.Possible values: U-University, P-Post Secondary, S-Secondary, J-Primary/Junior,O-Others.<br>maxLength: 10</br>", "allowEmptyValue": false}, "emailaddress": {"type": "string", "example": "<EMAIL>", "description": "Email Address.<br>maxLength: 490</br>", "allowEmptyValue": false}, "employercompanyname": {"type": "string", "example": "Happy Realty", "description": "The name of the company for whom the customer works.<br>maxLength: 50</br>", "allowEmptyValue": false}, "firstname": {"type": "string", "example": "Yan", "description": "First Name.<br>maxLength: 70</br>", "allowEmptyValue": false}, "gender": {"type": "string", "example": "M", "description": "The gender of the customer. When it is set M, that means the customer is a Male.Possible values: M-Male, F-Female.", "allowEmptyValue": false}, "issuecountry": {"type": "string", "example": "China", "description": "The issue country of the customer’s ID. If it is set China, that means China is the issuing country for the customer’s ID.<br>maxLength: 50</br>", "allowEmptyValue": false}, "lastname": {"type": "string", "example": "<PERSON>", "description": "Last Name.<br>maxLength: 70</br>", "allowEmptyValue": false}, "mailingaddress": {"type": "string", "example": "551 Austin Road,Tsim Sha Tsui,Kowloon", "description": "The mailingaddress is an address to which your mail can be sent.<br>maxLength: 490</br>", "allowEmptyValue": false}, "maritalstatus": {"type": "string", "example": "S", "description": "Marital status describes a person's relationship with a significant person.When it is set S, that means the customer is single.Possible values: S-Single, M-Married, D-Divorced.<br>maxLength: 10</br>", "allowEmptyValue": false}, "mobilephonenumber": {"type": "string", "example": 64657884, "description": "Customer’s mobile phone number.<br>maxLength: 11</br>", "allowEmptyValue": false}, "monthlysalary": {"type": "number", "example": 19000.0, "description": "The salary paid by his/her employer company every month.<br>maxLength: 10</br>", "allowEmptyValue": false}, "nationality": {"type": "string", "example": "China", "description": "Nationality is a legal relationship between an individual person and a state.\tWhen it is set China, that means the customer is a Chinese. Possible values: China, Japan, German, France.<br>maxLength: 50</br>", "allowEmptyValue": false}, "occupation": {"type": "string", "example": "Software", "description": "The job or profession of the customer.<br>maxLength: 50</br>", "allowEmptyValue": false}, "permanentresidencestatus": {"type": "string", "example": "Y", "description": "Whether the customer is a Hong Kong permanent resident or not.When it is set Y, it means yes. Possible values:Y-Yes, N-No.", "allowEmptyValue": false}, "position": {"type": "string", "example": "Senior Manager", "description": "The job title of this customer in his/her company. Possible value:CEO, Senior Manager, Consultant, Business Analyst, Chairman, staff, Engineer, Manager, Director.<br>maxLength: 50</br>", "allowEmptyValue": false}, "residencephonenumber": {"type": "string", "example": 34828869, "description": "The phone number of the customer’s residential address in HongKong.<br>maxLength: 34</br>", "allowEmptyValue": false}, "residentialaddress": {"type": "string", "example": "779 Yi Chun Street, Sai Kung, New Territory", "description": "The Hong Kong address where the customer lives.<br>maxLength: 490</br>", "allowEmptyValue": false}, "wechatid": {"type": "string", "example": "W3456754", "description": "The customer’s WeChat ID.<br>maxLength: 50</br>", "allowEmptyValue": false}, "yearsofresidence": {"type": "integer", "format": "int32", "example": 3, "description": "The year of the customer living in Hong Kong.<br>maxLength: 10</br>", "allowEmptyValue": false}, "yearsofservices": {"type": "integer", "format": "int32", "example": 2, "description": "The year of the customer working in this company.<br>maxLength: 50</br>", "allowEmptyValue": false}}, "title": "CustomerMasterModel"}, "IncreaseLimitsChangeInputModel": {"type": "object", "required": ["changeAmount", "creditcardnumber"], "properties": {"changeAmount": {"type": "number", "example": 5000.0, "description": "The amount to be increased for a credit card approved limit. <br> maxLength:18 </br>", "allowEmptyValue": false}, "creditcardnumber": {"type": "string", "example": ****************, "description": "Customer credit card number. <br> maxLength:16 </br>", "allowEmptyValue": false}}, "title": "IncreaseLimitsChangeInputModel"}, "LimitsChangeInputModel": {"type": "object", "required": ["changeAmount", "creditcardnumber"], "properties": {"changeAmount": {"type": "number", "example": 5000.0, "description": "The amount to be decreased for a credit card approved limit. <br> maxLength:18 </br>", "allowEmptyValue": false}, "creditcardnumber": {"type": "string", "example": ****************, "description": "Customer credit card number. <br> maxLength:16 </br>", "allowEmptyValue": false}}, "title": "LimitsChangeInputModel"}, "LimitsRetrieveModel": {"type": "object", "properties": {"approvedLimit": {"type": "string", "example": 10000, "description": "The approved limit of a credit card.", "allowEmptyValue": false}, "availableLimit": {"type": "string", "example": 10000, "description": "The available limit of a credit card.", "allowEmptyValue": false}, "cashAdvanceLimit": {"type": "string", "example": 5000, "description": "The cashadvance limit of a credit card.", "allowEmptyValue": false}, "ccyCode": {"type": "string", "example": "HKD", "description": "The currency code of a credit card.", "allowEmptyValue": false}, "usedLimit": {"type": "string", "example": 5000, "description": "The used limit of a credit card.", "allowEmptyValue": false}}, "title": "LimitsRetrieveModel"}, "PaymentDetailsModel": {"type": "object", "properties": {"minimumpayment": {"type": "number", "example": 1000.0, "description": "Minimum repayment amount.<br> maxLength:18</br>", "allowEmptyValue": false}, "repaymentamount": {"type": "number", "example": 1000.0, "description": "Repayment amount.<br> maxLength:18</br>", "allowEmptyValue": false}, "repaymentduedate": {"type": "number", "example": 1561540143000.0, "description": "The due date of repayment.<br>maxLength: 20</br>", "allowEmptyValue": false}, "statementdate": {"type": "number", "example": 1561540143000.0, "description": "The statement date of creditcard.<br>maxLength: 20</br>", "allowEmptyValue": false}}, "title": "PaymentDetailsModel"}, "PointRedemptionHistoryModel": {"type": "object", "properties": {"amount": {"type": "number", "example": 1000.0, "description": "Quantity of exchangeable products.<br> maxLength:18</br>", "allowEmptyValue": false}, "creditcardnumber": {"type": "string", "example": ****************, "description": "Customer credit card number. <br> maxLength:16 </br>", "allowEmptyValue": false}, "id": {"type": "integer", "format": "int32", "example": 1, "description": "Unique id. <br> maxLength: 20 </br>", "allowEmptyValue": false}, "merchantname": {"type": "string", "example": "Man Kiu College", "description": "The name of merchant.<br> maxLength:140 </br>", "allowEmptyValue": false}, "points": {"type": "number", "example": 1000.0, "description": "Generate or charge points.<br> maxLength:18</br>", "allowEmptyValue": false}, "productcode": {"type": "string", "example": "00001", "description": "product code <br> maxLength:10 </br>", "allowEmptyValue": false}, "productname": {"type": "string", "example": "Lovers' suits", "description": "The product name.<br> maxLength:140 </br>", "allowEmptyValue": false}, "transactionoption": {"type": "string", "example": "0001", "description": "Type of points transaction.<br> maxLength:5 </br>", "allowEmptyValue": false}, "transactiontime": {"type": "number", "example": 1561540143000.0, "description": "The transaction time of Credit Card.<br> maxLength:20 </br>", "allowEmptyValue": false}}, "title": "PointRedemptionHistoryModel"}, "PointRedemptionModel": {"type": "object", "required": ["amount", "creditcardnumber", "productcode"], "properties": {"amount": {"type": "number", "example": 2.0, "description": "The amount of product <br> maxLength:13 </br>", "allowEmptyValue": false}, "creditcardnumber": {"type": "string", "example": ****************, "description": "The creditcard number <br> maxLength:16 </br>", "allowEmptyValue": false}, "productcode": {"type": "string", "example": "00001", "description": "product code <br> maxLength:10 </br>", "allowEmptyValue": false}}, "title": "PointRedemptionModel"}, "PostTransDeInputModel": {"type": "object", "required": ["creditcardnumber", "merchantnumber", "transactionamount", "transactionccy", "transactiontime"], "properties": {"creditcardnumber": {"type": "string", "example": ****************, "description": "The number of Credit Card.<br> maxLength:16 </br>", "allowEmptyValue": false}, "merchantnumber": {"type": "string", "example": "HK0001001000009", "description": "Credit card merchant number.<br> maxLength:35 </br>", "allowEmptyValue": false}, "transactionamount": {"type": "number", "example": 100.0, "description": "Customer credit card transaction amount.<br> maxLength:18 </br>", "allowEmptyValue": false}, "transactionccy": {"type": "string", "example": "USD", "description": "Transaction currency code.<br> maxLength:3 </br>", "allowEmptyValue": false}, "transactiontime": {"type": "number", "example": 1561540143000.0, "description": "The transaction time of Credit Card.<br> maxLength:20 </br>", "allowEmptyValue": false}}, "title": "PostTransDeInputModel"}, "ProductModel": {"type": "object", "properties": {"category": {"type": "string", "example": "Clothing", "description": "Credit card merchant category. <br>Possilble Values : Clothing, Dining, Beauty, Jewelry, Education</br> <br> maxLength:50 </br>", "allowEmptyValue": false}, "merchantname": {"type": "string", "example": "Man Kiu College", "description": "The name of merchant.<br> maxLength:140 </br>", "allowEmptyValue": false}, "merchantnumber": {"type": "string", "example": "HK0001001000009", "description": "The number of merchant<br>maxLength: 50</br>", "allowEmptyValue": false}, "point": {"type": "number", "example": 1000.0, "description": "Redeemable points.<br> maxLength:18 </br>", "allowEmptyValue": false}, "productcode": {"type": "string", "example": "00001", "description": "product code <br> maxLength:10 </br>", "allowEmptyValue": false}, "productinventory": {"type": "number", "example": 100.0, "description": "The inventory of product.<br> maxLength:18 </br>", "allowEmptyValue": false}, "productname": {"type": "string", "example": "Lovers' suits", "description": "product name.<br> maxLength:140 </br>", "allowEmptyValue": false}}, "title": "ProductModel"}, "QueryHistoryModel": {"type": "object", "required": ["endDate", "index", "items", "merchantNumber", "startDate"], "properties": {"endDate": {"type": "number", "example": 1562774399000.0, "description": "The end date of a credit card merchant transaction details.<br> maxLength:20 </br>", "allowEmptyValue": false}, "index": {"type": "number", "example": 0.0, "description": "an indicator to identify where your respone result will start to show.<br> maxLength:4 </br>", "allowEmptyValue": false}, "items": {"type": "number", "example": 3.0, "description": "the number of items you'd like to get in the response result.<br> maxLength:4 </br>", "allowEmptyValue": false}, "merchantNumber": {"type": "string", "example": "HK0001001000009", "description": "The number of merchant<br>maxLength: 50</br>", "allowEmptyValue": false}, "startDate": {"type": "number", "example": 1556269743000.0, "description": "The start date of a credit card merchant transaction details <br> maxLength:20 </br>", "allowEmptyValue": false}}, "title": "QueryHistoryModel"}, "ReHistoryModel": {"type": "object", "properties": {"merchant": {"$ref": "#/definitions/ReMerchantReferenceModel"}, "trandetail": {"type": "array", "items": {"$ref": "#/definitions/ReTranDetailModel"}}}, "title": "ReHistoryModel"}, "ReMerchantReferenceModel": {"type": "object", "properties": {"businesslicenseid": {"type": "string", "example": "5e645df8157642c9bcb1811a453bf5a8", "description": "The business license id of merchant.<br>maxLength: 50</br>", "allowEmptyValue": false}, "displayname": {"type": "string", "example": "Toddle Kids Ltd.", "description": "The displayname of merchant.<br>maxLength: 140</br>", "allowEmptyValue": false}, "lastupdateddate": {"type": "number", "example": 1420073798000.0, "description": "The lastupdated date of merchant data<br>maxLength: 20</br>", "allowEmptyValue": false}, "merchantaddress": {"type": "string", "example": "Flat 2, Front Block, 12/F.", "description": "The address of merchant.<br>maxLength: 490</br>", "allowEmptyValue": false}, "merchantname": {"type": "string", "example": "Man Kiu College", "description": "The name of merchant.<br>maxLength: 140</br>", "allowEmptyValue": false}, "merchantnumber": {"type": "string", "example": "HK0001001000009", "description": "The number of merchant.<br>maxLength: 35</br>", "allowEmptyValue": false}, "outstandingbalance": {"type": "number", "example": 0.0, "description": "The outstanding balance of merchant.<br>maxLength: 18</br>", "allowEmptyValue": false}, "paymentterm": {"type": "string", "example": "W", "description": "The payment period.<br>maxLength: 5</br>", "allowEmptyValue": false}, "phone": {"type": "string", "example": "2771 0870", "description": "Merchant’s phone number.<br>maxLength: 34</br>", "allowEmptyValue": false}}, "title": "ReMerchantReferenceModel"}, "ReTranDetailModel": {"type": "object", "properties": {"bookingamount": {"type": "number", "example": 100.0, "description": "Customer credit card booking amount.<br> maxLength:18 </br>", "allowEmptyValue": false}, "bookingccy": {"type": "string", "example": "USD", "description": "Booking currency type.<br> maxLength:3 </br>", "allowEmptyValue": false}, "creditcardnumber": {"type": "string", "example": ****************, "description": "Customer credit card number. <br> maxLength: 16 </br>", "allowEmptyValue": false}, "creditcardtype": {"type": "string", "example": "V", "description": "The Type of Credit Card.<br>maxLength: 4</br>", "allowEmptyValue": false}, "dealnumber": {"type": "string", "example": 20150128000003406, "description": "The dealnumber of Credit Card Transaction log.<br>maxLength: 20</br>", "allowEmptyValue": false}, "transactionamount": {"type": "number", "example": 100.0, "description": "Customer credit card transaction amount.<br> maxLength:18 </br>", "allowEmptyValue": false}, "transactionccy": {"type": "string", "example": "USD", "description": "Transaction currency type.<br> maxLength:3 </br>", "allowEmptyValue": false}, "transactiontime": {"type": "number", "example": 1561540143000.0, "description": "The transaction time of Credit Card.<br> maxLength:20 </br>", "allowEmptyValue": false}}, "title": "ReTranDetailModel"}, "ReTransactionDetailsModel": {"type": "object", "required": ["creditcardnumber", "index", "items", "transFromDate", "transToDate"], "properties": {"creditcardnumber": {"type": "string", "example": ****************, "description": "The number of Credit Card.<br> maxLength:16 </br>", "allowEmptyValue": false}, "index": {"type": "number", "example": 0.0, "description": "an indicator to identify where your respone result will start to show.<br> maxLength:4 </br>", "allowEmptyValue": false}, "items": {"type": "number", "example": 3.0, "description": "the number of items you'd like to get in the response result.<br> maxLength:4 </br>", "allowEmptyValue": false}, "transFromDate": {"type": "number", "example": *************.0, "description": "The start date of the transactions.<br> maxLength:20 </br>", "allowEmptyValue": false}, "transToDate": {"type": "number", "example": *************.0, "description": "The end date of the transactions.<br> maxLength:20 </br>", "allowEmptyValue": false}}, "title": "ReTransactionDetailsModel"}, "RepaymentModel": {"type": "object", "required": ["creditcardnumber", "debitaccountnumber", "repaymentAmount"], "properties": {"creditcardnumber": {"type": "string", "example": ****************, "description": "The number of Credit Card.<br> maxLength:16 </br>", "allowEmptyValue": false}, "debitaccountnumber": {"type": "string", "example": "***********************", "description": "Debit account number that is used to make the repayment.<br> maxLength:23 </br>", "allowEmptyValue": false}, "repaymentAmount": {"type": "number", "example": 1000.0, "description": "Repayment amount.<br> maxLength:18</br>", "allowEmptyValue": false}}, "title": "RepaymentModel"}, "ResultUtil": {"type": "object", "properties": {"code": {"type": "string"}, "data": {"type": "object"}, "msg": {"type": "string"}}, "title": "ResultUtil"}, "ResultUtil«CreditCardModel»": {"type": "object", "properties": {"code": {"type": "string"}, "data": {"$ref": "#/definitions/CreditCardModel"}, "msg": {"type": "string"}}, "title": "ResultUtil«CreditCardModel»"}, "ResultUtil«LimitsRetrieveModel»": {"type": "object", "properties": {"code": {"type": "string"}, "data": {"$ref": "#/definitions/LimitsRetrieveModel"}, "msg": {"type": "string"}}, "title": "ResultUtil«LimitsRetrieveModel»"}, "ResultUtil«List«PointRedemptionHistoryModel»»": {"type": "object", "properties": {"code": {"type": "string"}, "data": {"type": "array", "items": {"$ref": "#/definitions/PointRedemptionHistoryModel"}}, "msg": {"type": "string"}}, "title": "ResultUtil«List«PointRedemptionHistoryModel»»"}, "ResultUtil«List«ProductModel»»": {"type": "object", "properties": {"code": {"type": "string"}, "data": {"type": "array", "items": {"$ref": "#/definitions/ProductModel"}}, "msg": {"type": "string"}}, "title": "ResultUtil«List«ProductModel»»"}, "ResultUtil«List«TransactionDetailsModel»»": {"type": "object", "properties": {"code": {"type": "string"}, "data": {"type": "array", "items": {"$ref": "#/definitions/TransactionDetailsModel"}}, "msg": {"type": "string"}}, "title": "ResultUtil«List«TransactionDetailsModel»»"}, "ResultUtil«PaymentDetailsModel»": {"type": "object", "properties": {"code": {"type": "string"}, "data": {"$ref": "#/definitions/PaymentDetailsModel"}, "msg": {"type": "string"}}, "title": "ResultUtil«PaymentDetailsModel»"}, "ResultUtil«ReHistoryModel»": {"type": "object", "properties": {"code": {"type": "string"}, "data": {"$ref": "#/definitions/ReHistoryModel"}, "msg": {"type": "string"}}, "title": "ResultUtil«ReHistoryModel»"}, "ResultUtil«ReMerchantReferenceModel»": {"type": "object", "properties": {"code": {"type": "string"}, "data": {"$ref": "#/definitions/ReMerchantReferenceModel"}, "msg": {"type": "string"}}, "title": "ResultUtil«ReMerchantReferenceModel»"}, "ResultUtil«TotalConsumptionModel»": {"type": "object", "properties": {"code": {"type": "string"}, "data": {"$ref": "#/definitions/TotalConsumptionModel"}, "msg": {"type": "string"}}, "title": "ResultUtil«TotalConsumptionModel»"}, "ResultUtil«TotalPointModel»": {"type": "object", "properties": {"code": {"type": "string"}, "data": {"$ref": "#/definitions/TotalPointModel"}, "msg": {"type": "string"}}, "title": "ResultUtil«TotalPointModel»"}, "SearchPointProductModel": {"type": "object", "required": ["category"], "properties": {"category": {"type": "string", "example": "Clothing", "description": "Credit card merchant category. <br>Possilble Values : Clothing, Dining, Beauty, Jewelry, Education</br> <br> maxLength:35 </br>", "allowEmptyValue": false}}, "title": "SearchPointProductModel"}, "TotalConsumptionModel": {"type": "object", "properties": {"totalConsumption": {"type": "string", "example": 200, "description": "The total consumption of Credit Card.<br>maxLength: 50</br>", "allowEmptyValue": false}}, "title": "TotalConsumptionModel"}, "TotalPointModel": {"type": "object", "required": ["totalPoint"], "properties": {"totalPoint": {"type": "number", "example": 200.0, "description": "The total point of Credit Card.<br>maxLength: 20</br>", "allowEmptyValue": false}}, "title": "TotalPointModel"}, "TransactionDetailsModel": {"type": "object", "properties": {"bookingamount": {"type": "number", "example": 100.0, "description": "Customer credit card booking amount.<br> maxLength:18 </br>", "allowEmptyValue": false}, "bookingccy": {"type": "string", "example": "USD", "description": "Booking currency code.<br> maxLength:3 </br>", "allowEmptyValue": false}, "dealnumber": {"type": "string", "example": 20150128000003406, "description": "The dealnumber of Credit Card Transaction log.<br> maxLength:20 </br>", "allowEmptyValue": false}, "merchantname": {"type": "string", "example": "Man Kiu College", "description": "The name of merchant.<br> maxLength:140 </br>", "allowEmptyValue": false}, "merchantnumber": {"type": "string", "example": "HK0001001000009", "description": "The number of merchant<br>maxLength: 35</br>", "allowEmptyValue": false}, "transactionamount": {"type": "number", "example": 100.0, "description": "Customer credit card transaction amount.<br> maxLength:18 </br>", "allowEmptyValue": false}, "transactionccy": {"type": "string", "example": "USD", "description": "Transaction currency code.<br> maxLength:3 </br>", "allowEmptyValue": false}, "transactiontime": {"type": "number", "example": 1561540143000.0, "description": "The transaction time of Credit Card.<br> maxLength:20 </br>", "allowEmptyValue": false}}, "title": "TransactionDetailsModel"}}}