{"swagger": "2.0", "info": {"description": "APIs here are all about foreign exchange service. Before calling these APIs, please make sure you’ve learnt the business logic between these APIs.", "version": "1.0", "title": "Foreign Exchange Service", "contact": {"name": "<EMAIL>"}}, "schemes": ["https"], "host": "simnectz.hsu.edu.hk/lbsgateway/foreign-experience", "basePath": "/", "tags": [{"name": "Market Information", "description": "Retrieve and display latest exchange rate information."}, {"name": "Transaction", "description": "Transaction processing in customer’s FEX account."}], "paths": {"/foreignExchange/exchangerateinformation": {"post": {"tags": ["Market Information"], "summary": "This api is designed to retrieve currency rate information.", "description": " Before buying/selling foreign currency, you may need to call this API to query the market exchange rate information of the current currency.", "operationId": "exchangerateinformationUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.<br>maxLength: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "Message ID in 128 bit random UUID format generated uniquely for every request by F/E side.<br>maxLength: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "client_id generated during consumer onboarding.<br>maxLength: 50</br> ", "required": true, "type": "string", "default": "devin"}], "responses": {"200": {"description": "Query completed successfully.(Returned By Get)", "schema": {"$ref": "#/definitions/ResultUtil"}}, "201": {"description": "Normal execution. The request has succeeded. (Returned By Post)", "schema": {"$ref": "#/definitions/ResultUtil"}}, "403": {"description": "Token has incorrect scope or a security policy was violated. Action: Please check whether you’re using the right token with the legal authorized user account."}, "404": {"description": "The requested deposit account does not exist.Action: Please make sure the account number and account type you’re inputting are correct."}, "500": {"description": "Something went wrong on the API gateway or micro-service. Action: check your network and try again later."}}}}, "/foreignExchange/foreignExchange": {"post": {"tags": ["Transaction"], "summary": "This api is designed for currency exchange.", "description": "With this API, customers can make currency exchange between HKD and USD,CNY,AUD,CAD,CHF,EUR,GBP,JPY,NZD,SGD.", "operationId": "currencyExchangeUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.<br>maxLength: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "Message ID in 128 bit random UUID format generated uniquely for every request by F/E side.<br>maxLength: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "client_id generated during consumer onboarding.<br>maxLength: 50</br> ", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "exchangeModel", "description": "exchangeModel", "required": true, "schema": {"$ref": "#/definitions/ExchangeModel"}}], "responses": {"200": {"description": "Query completed successfully.(Returned By Get)", "schema": {"$ref": "#/definitions/ResultUtil"}}, "201": {"description": "Normal execution. The request has succeeded. (Returned By Post)", "schema": {"$ref": "#/definitions/ResultUtil"}}, "403": {"description": "Token has incorrect scope or a security policy was violated. Action: Please check whether you’re using the right token with the legal authorized user account."}, "404": {"description": "The requested deposit account does not exist.Action: Please make sure the account number and account type you’re inputting are correct."}, "500": {"description": "Something went wrong on the API gateway or micro-service. Action: check your network and try again later."}}}}}, "definitions": {"ExchangeModel": {"type": "object", "required": ["actionCode", "ccycode", "debitaccountnumber", "exchangeAmount", "fexAccountNumber"], "properties": {"actionCode": {"type": "string", "example": "Buy", "description": "Customer buy/sell foreign currency via banks. Possible values: Buy, Sell.<br>maxLength: 4</br>", "allowEmptyValue": false}, "ccycode": {"type": "string", "example": "USD", "description": "The currency type that you'd like to exchange. Possible values: HKD,USD,CNY,AUD,CAD,CHF,EUR,GBP,JPY,NZD,SGD.<br>maxLength: 3</br>", "allowEmptyValue": false}, "debitaccountnumber": {"type": "string", "example": "***********************", "description": "A saving or current account number that is associated with the foreign currency exchange account.<br>maxLength: 34", "allowEmptyValue": false}, "exchangeAmount": {"type": "number", "example": 100.0, "description": "Transaction amount in this foreign exchange；<br>5 decimals,maxLength: 13", "allowEmptyValue": false}, "fexAccountNumber": {"type": "string", "example": "***********************", "description": "Customer foreign currency exchange account number.<br>maxLength: 34", "allowEmptyValue": false}}, "title": "ExchangeModel", "description": "Input Params Model"}, "ResultUtil": {"type": "object", "properties": {"code": {"type": "string"}, "data": {"type": "object"}, "msg": {"type": "string"}}, "title": "ResultUtil"}}}