{"swagger": "2.0", "info": {"description": "APIs here are all about bond trading service. Before calling these APIs, please make sure you’ve learnt the business logic between these APIs.", "version": "1.0", "title": "Bond Experience", "contact": {"name": "<EMAIL>"}}, "host": "simnectz.hsu.edu.hk/lbsgateway/bond-experience", "schemes": ["https"], "basePath": "/", "tags": [{"name": "Market Information", "description": "Retrieve and display latest Bond information."}, {"name": "Order & Simulation", "description": "Bond subscription application & simulation."}, {"name": "Account Information", "description": "Bond account holding information & settlement."}], "paths": {"/Bond/bondHoldingEnquiry": {"post": {"tags": ["Account Information"], "summary": "This API is designed to retrieve a bond account holding information.", "description": "After you buy/sell bonds successfully, you may want to check your latest bond holding details by calling this API.", "operationId": "bondHoldingEnquiryUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.<br>maxLength: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "Message ID in 128 bit random UUID format generated uniquely for every request by F/E side.<br>maxLength: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "client_id generated during consumer onboarding. <br>maxLength: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "bondHoldingEnquiryModel", "description": "bondHoldingEnquiryModel", "required": true, "schema": {"$ref": "#/definitions/BondHoldingEnquiryModel"}}], "responses": {"200": {"description": "Query completed successfully.(Returned By Get)", "schema": {"$ref": "#/definitions/ResultUtil«BondHoldingInfoModel»"}}, "201": {"description": "Normal execution. The request has succeeded. (Returned By Post)", "schema": {"$ref": "#/definitions/ResultUtil«BondHoldingInfoModel»"}}, "403": {"description": "Token has incorrect scope or a security policy was violated. Action: Please check whether you’re using the right token with the legal authorized user account."}, "404": {"description": "The requested deposit account does not exist.Action: Please make sure the account number and account type you’re inputting are correct."}, "500": {"description": "Something went wrong on the API gateway or micro-service. Action: check your network and try again later."}}}}, "/Bond/bondList": {"post": {"tags": ["Market Information"], "summary": "This API is designed to retrieve all the bonds.", "description": "Before calling /Bond/order/subscriptionOrderPlacing API to buy a bond product, you may want to check the bonds in the market.", "operationId": "bondListUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.<br>maxLength: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "Message ID in 128 bit random UUID format generated uniquely for every request by F/E side.<br>maxLength: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "client_id generated during consumer onboarding. <br>maxLength: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "queryBondListModel", "description": "queryBondListModel", "required": true, "schema": {"$ref": "#/definitions/QueryBondListModel"}}], "responses": {"200": {"description": "Query completed successfully.(Returned By Get)", "schema": {"$ref": "#/definitions/ResultUtil«List«BondInfomationModel»»"}}, "201": {"description": "Normal execution. The request has succeeded. (Returned By Post)", "schema": {"$ref": "#/definitions/ResultUtil«List«BondInfomationModel»»"}}, "403": {"description": "Token has incorrect scope or a security policy was violated. Action: Please check whether you’re using the right token with the legal authorized user account."}, "404": {"description": "The requested deposit account does not exist.Action: Please make sure the account number and account type you’re inputting are correct."}, "500": {"description": "Something went wrong on the API gateway or micro-service. Action: check your network and try again later."}}}}, "/Bond/bondQuotation": {"post": {"tags": ["Market Information"], "summary": "This API is designed to retrieve a bond quotation details.", "description": "This API is designed to retrieve a bond product market information before doing any transaction.", "operationId": "bondQuotationUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.<br>maxLength: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "Message ID in 128 bit random UUID format generated uniquely for every request by F/E side.<br>maxLength: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "client_id generated during consumer onboarding. <br>maxLength: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "bondCodeModel", "description": "bondCodeModel", "required": true, "schema": {"$ref": "#/definitions/BondCodeModel"}}], "responses": {"200": {"description": "Query completed successfully.(Returned By Get)", "schema": {"$ref": "#/definitions/ResultUtil«BondInfomationModel»"}}, "201": {"description": "Normal execution. The request has succeeded. (Returned By Post)", "schema": {"$ref": "#/definitions/ResultUtil«BondInfomationModel»"}}, "403": {"description": "Token has incorrect scope or a security policy was violated. Action: Please check whether you’re using the right token with the legal authorized user account."}, "404": {"description": "The requested deposit account does not exist.Action: Please make sure the account number and account type you’re inputting are correct."}, "500": {"description": "Something went wrong on the API gateway or micro-service. Action: check your network and try again later."}}}}, "/Bond/order/bondDetailRetrievalById": {"post": {"tags": ["Order & Simulation"], "summary": "This API is designed to retrieve order details by Id.", "description": "Once an order is placed successfully by calling /Bond/order/subscriptionOrderPlacing or /Bond/order/redemptionOrderPlacing API , you can retrieve the order details via this API. This API will return all order details within a specific time range based on your search condition.", "operationId": "orderDetailRetrievalByIdUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.<br>maxLength: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "Message ID in 128 bit random UUID format generated uniquely for every request by F/E side.<br>maxLength: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "client_id generated during consumer onboarding. <br>maxLength: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "idmodel", "description": "idmodel", "required": true, "schema": {"$ref": "#/definitions/IDModel"}}], "responses": {"200": {"description": "Query completed successfully.(Returned By Get)", "schema": {"$ref": "#/definitions/ResultUtil«BondOrderInfoModel»"}}, "201": {"description": "Normal execution. The request has succeeded. (Returned By Post)", "schema": {"$ref": "#/definitions/ResultUtil«BondOrderInfoModel»"}}, "403": {"description": "Token has incorrect scope or a security policy was violated. Action: Please check whether you’re using the right token with the legal authorized user account."}, "404": {"description": "The requested deposit account does not exist.Action: Please make sure the account number and account type you’re inputting are correct."}, "500": {"description": "Something went wrong on the API gateway or micro-service. Action: check your network and try again later."}}}}, "/Bond/order/cancellation": {"post": {"tags": ["Order & Simulation"], "summary": "This API is designed to cancel a bond order.", "description": "Before calling this API, you may need to place a bond order by calling /Bond/order/subscriptionOrderPlacing to buy or /Bond/order/redemptionOrderPlacing to sell bond. To cancel an order, please also make sure the order is still in pending status, which means the transaction is still in pending status.", "operationId": "cancellationUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.<br>maxLength: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "Message ID in 128 bit random UUID format generated uniquely for every request by F/E side.<br>maxLength: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "client_id generated during consumer onboarding. <br>maxLength: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "idmodel", "description": "idmodel", "required": true, "schema": {"$ref": "#/definitions/IDModel"}}], "responses": {"200": {"description": "Query completed successfully.(Returned By Get)", "schema": {"$ref": "#/definitions/ResultUtil"}}, "201": {"description": "Normal execution. The request has succeeded. (Returned By Post)", "schema": {"$ref": "#/definitions/ResultUtil"}}, "403": {"description": "Token has incorrect scope or a security policy was violated. Action: Please check whether you’re using the right token with the legal authorized user account."}, "404": {"description": "The requested deposit account does not exist.Action: Please make sure the account number and account type you’re inputting are correct."}, "500": {"description": "Something went wrong on the API gateway or micro-service. Action: check your network and try again later."}}}}, "/Bond/order/orderChange": {"post": {"tags": ["Order & Simulation"], "summary": "This API is designed to change a bond order.", "description": "Once an order is placed successfully, but status hasn’t been updated yet and the transaction is not really made , you can update the order information,such as investment Amount by calling this API.", "operationId": "orderInfoUpdateUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.<br>maxLength: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "Message ID in 128 bit random UUID format generated uniquely for every request by F/E side.<br>maxLength: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "client_id generated during consumer onboarding. <br>maxLength: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "bondUpdateOrderRequestModel", "description": "bondUpdateOrderRequestModel", "required": true, "schema": {"$ref": "#/definitions/BondUpdateOrderRequestModel"}}], "responses": {"200": {"description": "Query completed successfully.(Returned By Get)", "schema": {"$ref": "#/definitions/ResultUtil"}}, "201": {"description": "Normal execution. The request has succeeded. (Returned By Post)", "schema": {"$ref": "#/definitions/ResultUtil"}}, "403": {"description": "Token has incorrect scope or a security policy was violated. Action: Please check whether you’re using the right token with the legal authorized user account."}, "404": {"description": "The requested deposit account does not exist.Action: Please make sure the account number and account type you’re inputting are correct."}, "500": {"description": "Something went wrong on the API gateway or micro-service. Action: check your network and try again later."}}}}, "/Bond/order/orderRetrieval": {"post": {"tags": ["Order & Simulation"], "summary": "This API is designed to retrieve all the bond order records.", "description": "This API is designed to allow users to kick off the bond execution simulation through the query on corresponding Bond order ID.", "operationId": "orderRetrievalUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.<br>maxLength: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "Message ID in 128 bit random UUID format generated uniquely for every request by F/E side.<br>maxLength: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "client_id generated during consumer onboarding. <br>maxLength: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "bondOrderRequestModel", "description": "bondOrderRequestModel", "required": true, "schema": {"$ref": "#/definitions/BondOrderRequestModel"}}], "responses": {"200": {"description": "Query completed successfully.(Returned By Get)", "schema": {"$ref": "#/definitions/ResultUtil«List«BondOrderInfoModel»»"}}, "201": {"description": "Normal execution. The request has succeeded. (Returned By Post)", "schema": {"$ref": "#/definitions/ResultUtil«List«BondOrderInfoModel»»"}}, "403": {"description": "Token has incorrect scope or a security policy was violated. Action: Please check whether you’re using the right token with the legal authorized user account."}, "404": {"description": "The requested deposit account does not exist.Action: Please make sure the account number and account type you’re inputting are correct."}, "500": {"description": "Something went wrong on the API gateway or micro-service. Action: check your network and try again later."}}}}, "/Bond/order/redemptionOrderPlacing": {"post": {"tags": ["Order & Simulation"], "summary": "This API is designed to place a bond redemption order.", "description": "After buying a bond product, if you want to sell the bond product, please call this API.", "operationId": "redemptionOrderPlacingUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.<br>maxLength: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "Message ID in 128 bit random UUID format generated uniquely for every request by F/E side.<br>maxLength: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "client_id generated during consumer onboarding. <br>maxLength: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "bondRedemptionTradingModel", "description": "bondRedemptionTradingModel", "required": true, "schema": {"$ref": "#/definitions/BondRedemptionTradingModel"}}], "responses": {"200": {"description": "Query completed successfully.(Returned By Get)", "schema": {"$ref": "#/definitions/ResultUtil"}}, "201": {"description": "Normal execution. The request has succeeded. (Returned By Post)", "schema": {"$ref": "#/definitions/ResultUtil"}}, "403": {"description": "Token has incorrect scope or a security policy was violated. Action: Please check whether you’re using the right token with the legal authorized user account."}, "404": {"description": "The requested deposit account does not exist.Action: Please make sure the account number and account type you’re inputting are correct."}, "500": {"description": "Something went wrong on the API gateway or micro-service. Action: check your network and try again later."}}}}, "/Bond/order/simulatorUpdate": {"post": {"tags": ["Order & Simulation"], "summary": "This API is designed to update bond order simulator.", "description": "After you place an order via /Bond/order/subscriptionOrderPlacing API or /Bond/order/redemptionOrderPlacing API , you need to call /Bond/order/orderRetrieval API to retrieve the order id, then you’re able to simulate to approve/reject this order with the order id via this API.", "operationId": "orderStatusUpdateUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.<br>maxLength: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "Message ID in 128 bit random UUID format generated uniquely for every request by F/E side.<br>maxLength: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "client_id generated during consumer onboarding. <br>maxLength: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "bondUpdateOrderStatusModel", "description": "bondUpdateOrderStatusModel", "required": true, "schema": {"$ref": "#/definitions/BondUpdateOrderStatusModel"}}], "responses": {"200": {"description": "Query completed successfully.(Returned By Get)", "schema": {"$ref": "#/definitions/ResultUtil"}}, "201": {"description": "Normal execution. The request has succeeded. (Returned By Post)", "schema": {"$ref": "#/definitions/ResultUtil"}}, "403": {"description": "Token has incorrect scope or a security policy was violated. Action: Please check whether you’re using the right token with the legal authorized user account."}, "404": {"description": "The requested deposit account does not exist.Action: Please make sure the account number and account type you’re inputting are correct."}, "500": {"description": "Something went wrong on the API gateway or micro-service. Action: check your network and try again later."}}}}, "/Bond/order/subscriptionOrderPlacing": {"post": {"tags": ["Order & Simulation"], "summary": "This API is designed to place a bond subscription order.", "description": "After checking a bond market details by calling /Bond/bondQuotation or/Bond/bondList , we can call this API to place a bond subscription order.", "operationId": "subscriptionOrderPlacingUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.<br>maxLength: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "Message ID in 128 bit random UUID format generated uniquely for every request by F/E side.<br>maxLength: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "client_id generated during consumer onboarding. <br>maxLength: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "bondSubscriptionTradingModel", "description": "bondSubscriptionTradingModel", "required": true, "schema": {"$ref": "#/definitions/BondSubscriptionTradingModel"}}], "responses": {"200": {"description": "Query completed successfully.(Returned By Get)", "schema": {"$ref": "#/definitions/ResultUtil"}}, "201": {"description": "Normal execution. The request has succeeded. (Returned By Post)", "schema": {"$ref": "#/definitions/ResultUtil"}}, "403": {"description": "Token has incorrect scope or a security policy was violated. Action: Please check whether you’re using the right token with the legal authorized user account."}, "404": {"description": "The requested deposit account does not exist.Action: Please make sure the account number and account type you’re inputting are correct."}, "500": {"description": "Something went wrong on the API gateway or micro-service. Action: check your network and try again later."}}}}, "/Bond/settlementAccountUpdate": {"post": {"tags": ["Account Information"], "summary": "This API is designed to support custmomer to update their settlement account. ", "description": "When you want to update a new settlement account for this bond account, please call this API.", "operationId": "settlementAccountUpdateUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.<br>maxLength: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "Message ID in 128 bit random UUID format generated uniquely for every request by F/E side.<br>maxLength: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "client_id generated during consumer onboarding. <br>maxLength: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "updateSettlementAccountModel", "description": "updateSettlementAccountModel", "required": true, "schema": {"$ref": "#/definitions/UpdateSettlementAccountModel"}}], "responses": {"200": {"description": "Query completed successfully.(Returned By Get)", "schema": {"$ref": "#/definitions/ResultUtil"}}, "201": {"description": "Normal execution. The request has succeeded. (Returned By Post)", "schema": {"$ref": "#/definitions/ResultUtil"}}, "403": {"description": "Token has incorrect scope or a security policy was violated. Action: Please check whether you’re using the right token with the legal authorized user account."}, "404": {"description": "The requested deposit account does not exist.Action: Please make sure the account number and account type you’re inputting are correct."}, "500": {"description": "Something went wrong on the API gateway or micro-service. Action: check your network and try again later."}}}}}, "definitions": {"BondCodeModel": {"type": "object", "required": ["bondCode"], "properties": {"bondCode": {"type": "string", "example": "K941", "description": "If you just want to check a specific bond market information with a bond code, you can just call this API dierctly. <br/> maxLength: 10", "allowEmptyValue": false}}, "title": "BondCodeModel"}, "BondHoldingEnquiryModel": {"type": "object", "required": ["stkaccountnumber"], "properties": {"stkaccountnumber": {"type": "string", "example": "***********************", "description": "Stock Trading Account Number <br/> maxLength: 34", "allowEmptyValue": false}}, "title": "BondHoldingEnquiryModel"}, "BondHoldingInfoModel": {"type": "object", "properties": {"bondCode": {"type": "string", "description": "bondCode.<br>maxLength: 20</br>", "allowEmptyValue": false}, "contractNumber": {"type": "string", "description": "contract Number.<br>maxLength: 9</br>", "allowEmptyValue": false}, "couponRate": {"type": "string", "example": "0.500%", "description": "Coupon Rate.<br>maxLength: 20</br>", "allowEmptyValue": false}, "currency": {"type": "string", "description": "currency.<br>maxLength: 20</br>", "allowEmptyValue": false}, "faceValue": {"type": "number", "example": 100000.0, "description": "Face Value.<br>maxLength: 20</br>", "allowEmptyValue": false}, "investmentAmount": {"type": "number", "description": "investment Amount.<br>maxLength: 20</br>", "allowEmptyValue": false}, "maturity": {"type": "number", "example": *************.0, "description": "Maturity.<br>maxLength: 20</br>", "allowEmptyValue": false}, "stkaccountnumber": {"type": "string", "example": "***********************", "description": "A stock account number of the customer.<br/> maxLength: 34", "allowEmptyValue": false}, "subscriptionTime": {"type": "number", "description": "Subscription Time.<br>maxLength: 20</br>", "allowEmptyValue": false}, "tradingOption": {"type": "string", "description": "Trading Option.<br>maxLength: 20</br>", "allowEmptyValue": false}, "transactionDesc": {"type": "string", "description": "Transaction Desc.<br>maxLength: 34</br>", "allowEmptyValue": false}}, "title": "BondHoldingInfoModel"}, "BondInfomationModel": {"type": "object", "properties": {"bondCode": {"type": "string", "example": "K941", "description": "Bond code.<br>maxLength: 10</br>", "allowEmptyValue": false}, "couponRate": {"type": "string", "example": "0.500%", "description": "Coupon Rate.<br>maxLength: 20</br>", "allowEmptyValue": false}, "creditRating": {"type": "string", "example": "N/A / N/A / AAAu", "description": "Credit Rating.<br>maxLength: 140</br>", "allowEmptyValue": false}, "faceValue": {"type": "number", "example": 100000.0, "description": "Face Value.<br>maxLength: 20</br>", "allowEmptyValue": false}, "fixedRate": {"type": "string", "example": "2.510%", "description": "Fixed Rate.<br>maxLength: 20</br>", "allowEmptyValue": false}, "id": {"type": "integer", "format": "int32", "example": 1, "description": "Unique id.<br>maxLength: 20</br>", "allowEmptyValue": false}, "issuer": {"type": "string", "example": "Commonwealth of Australia", "description": "Bond issuer.<br>maxLength: 140</br>", "allowEmptyValue": false}, "listingDate": {"type": "number", "example": 1652284800000.0, "description": "Listing Date.<br>maxLength: 20</br>", "allowEmptyValue": false}, "maturity": {"type": "number", "example": *************.0, "description": "Maturity.<br>maxLength: 20</br>", "allowEmptyValue": false}, "productRiskLevel": {"type": "string", "example": 2, "description": "ProductRiskLevel.<br>maxLength: 10</br>", "allowEmptyValue": false}, "tenor": {"type": "number", "example": 4.36, "description": "Tenor.<br>maxLength: 20</br>", "allowEmptyValue": false}}, "title": "BondInfomationModel"}, "BondOrderInfoModel": {"type": "object", "properties": {"accountNumber": {"type": "string", "description": "Stock account number.<br>maxLength: 34</br>", "allowEmptyValue": false}, "bondCode": {"type": "string", "description": "Bond code.<br>maxLength: 20</br>", "allowEmptyValue": false}, "currency": {"type": "string", "description": "Currency.<br>maxLength: 20</br>", "allowEmptyValue": false}, "lastUpdateDate": {"type": "number", "description": "Last update date.<br>maxLength: 20</br>", "allowEmptyValue": false}, "operationReasons": {"type": "string", "description": "Operation reasons.<br>maxLength: 140</br>", "allowEmptyValue": false}, "operationTime": {"type": "number", "description": "Operation Time.<br>maxLength: 20</br>", "allowEmptyValue": false}, "requestTime": {"type": "number", "description": "Request Time.<br>maxLength: 20</br>", "allowEmptyValue": false}, "status": {"type": "string", "description": "Status.<br>maxLength: 1</br>", "allowEmptyValue": false}, "statusCode": {"type": "string", "description": "Status code.<br>maxLength: 1</br>", "allowEmptyValue": false}, "tradingOption": {"type": "string", "description": "Trading Option.<br>maxLength: 20</br>", "allowEmptyValue": false}, "transactionAmount": {"type": "number", "description": "Transaction Amount.<br>maxLength: 20</br>", "allowEmptyValue": false}}, "title": "BondOrderInfoModel"}, "BondOrderRequestModel": {"type": "object", "required": ["enddate", "fromdate", "index", "items", "stkaccountnumber"], "properties": {"enddate": {"type": "number", "example": *************.0, "description": "The end date when you want to retrieve the order records.<br/>maxLength: 20", "allowEmptyValue": false}, "fromdate": {"type": "number", "example": *************.0, "description": "The start date when you want to retrieve the order records.<br/>maxLength: 20", "allowEmptyValue": false}, "index": {"type": "number", "example": 0.0, "description": "an indicator to identify where your respone result will start to show.<br/>maxLength: 4", "allowEmptyValue": false}, "items": {"type": "number", "example": 3.0, "description": "the number of items you'd like to get in the response result.<br/>maxLength: 4", "allowEmptyValue": false}, "stkaccountnumber": {"type": "string", "example": "***********************", "description": "A stock account number of the customer.<br/> maxLength: 34", "allowEmptyValue": false}, "stockcode": {"type": "string", "example": "K941", "description": "A unique code to identify a bond. <br/> maxLength: 10", "allowEmptyValue": true}}, "title": "BondOrderRequestModel"}, "BondRedemptionTradingModel": {"type": "object", "required": ["contractNumber", "stkaccountnumber"], "properties": {"contractNumber": {"type": "string", "example": "*********", "description": "Bond ContractNumber. <br/> maxLength: 20", "allowEmptyValue": false}, "stkaccountnumber": {"type": "string", "example": "***********************", "description": "Stock Trading Account Number <br/> maxLength: 34", "allowEmptyValue": false}}, "title": "BondRedemptionTradingModel"}, "BondSubscriptionTradingModel": {"type": "object", "required": ["bondCode", "currency", "stkaccountnumber"], "properties": {"bondCode": {"type": "string", "example": "K941", "description": "Bond code. <br/> maxLength: 10", "allowEmptyValue": false}, "currency": {"type": "string", "example": "HKD", "description": "What kind of currency will be used to buy bonds. <br/> maxLength: 20", "allowEmptyValue": false}, "investmentAmount": {"type": "string", "example": 10000, "description": "The price set for the bond trading when investment .<br/> maxLength: 20", "allowEmptyValue": true}, "stkaccountnumber": {"type": "string", "example": "***********************", "description": "Stock Trading Account Number <br/> maxLength: 34", "allowEmptyValue": false}, "subscriptionTime": {"type": "number", "example": *************.0, "description": "A simulated order and buy time when a bond order is placed.<br/>maxLength: 20", "allowEmptyValue": true}}, "title": "BondSubscriptionTradingModel"}, "BondUpdateOrderRequestModel": {"type": "object", "required": ["orderId"], "properties": {"investmentAmount": {"type": "string", "example": 10000, "description": "The price set for the bond trading when investment .<br/> maxLength: 20", "allowEmptyValue": true}, "orderId": {"type": "string", "example": 2, "description": "A unique id generated when you place a stock order.<br/> maxValue: **********<br>maxLength: 20</br>", "allowEmptyValue": false}}, "title": "BondUpdateOrderRequestModel"}, "BondUpdateOrderStatusModel": {"type": "object", "required": ["orderId", "status"], "properties": {"operationreasons": {"type": "string", "description": "The reason why you approve or reject the request. When you reject the request, this field is required. Otherwise, it's optional. <br/> maxLength: 140", "allowEmptyValue": true}, "orderId": {"type": "string", "example": 2, "description": "A unique orderid generated when you place a stock order.<br/> maxValue: **********<br>maxLength: 20</br>", "allowEmptyValue": false}, "status": {"type": "string", "example": 1, "description": "The status of the stock order. <br/> Possible values: 1 - approve, 2 - reject  <br/> maxLength: 20", "allowEmptyValue": false}}, "title": "BondUpdateOrderStatusModel"}, "IDModel": {"type": "object", "required": ["orderId"], "properties": {"orderId": {"type": "string", "example": 2, "description": "A unique id generated when you place a bond order.<br/> maxValue: **********<br>maxLength: 20</br>", "allowEmptyValue": false}}, "title": "IDModel"}, "QueryBondListModel": {"type": "object", "required": ["index", "items"], "properties": {"index": {"type": "number", "example": 0.0, "description": "an indicator to identify where your respone result will start to show.<br/>maxLength: 4", "allowEmptyValue": false}, "items": {"type": "number", "example": 3.0, "description": "the number of items you'd like to get in the response result.<br/>maxLength: 4", "allowEmptyValue": false}}, "title": "QueryBondListModel"}, "ResultUtil": {"type": "object", "properties": {"code": {"type": "string"}, "data": {"type": "object"}, "msg": {"type": "string"}}, "title": "ResultUtil"}, "ResultUtil«BondHoldingInfoModel»": {"type": "object", "properties": {"code": {"type": "string"}, "data": {"$ref": "#/definitions/BondHoldingInfoModel"}, "msg": {"type": "string"}}, "title": "ResultUtil«BondHoldingInfoModel»"}, "ResultUtil«BondInfomationModel»": {"type": "object", "properties": {"code": {"type": "string"}, "data": {"$ref": "#/definitions/BondInfomationModel"}, "msg": {"type": "string"}}, "title": "ResultUtil«BondInfomationModel»"}, "ResultUtil«BondOrderInfoModel»": {"type": "object", "properties": {"code": {"type": "string"}, "data": {"$ref": "#/definitions/BondOrderInfoModel"}, "msg": {"type": "string"}}, "title": "ResultUtil«BondOrderInfoModel»"}, "ResultUtil«List«BondInfomationModel»»": {"type": "object", "properties": {"code": {"type": "string"}, "data": {"type": "array", "items": {"$ref": "#/definitions/BondInfomationModel"}}, "msg": {"type": "string"}}, "title": "ResultUtil«List«BondInfomationModel»»"}, "ResultUtil«List«BondOrderInfoModel»»": {"type": "object", "properties": {"code": {"type": "string"}, "data": {"type": "array", "items": {"$ref": "#/definitions/BondOrderInfoModel"}}, "msg": {"type": "string"}}, "title": "ResultUtil«List«BondOrderInfoModel»»"}, "UpdateSettlementAccountModel": {"type": "object", "required": ["newsettleaccountnumber", "stkaccountnumber"], "properties": {"newsettleaccountnumber": {"type": "string", "example": "***********************", "description": "The new settlement account, to which you’d like to change the current one. <br/> maxLength: 34", "allowEmptyValue": false}, "stkaccountnumber": {"type": "string", "example": "***********************", "description": "A stock account number. <br/> maxLength: 34", "allowEmptyValue": false}}, "title": "UpdateSettlementAccountModel"}}}