{"swagger": "2.0", "info": {"description": "APIs here are all about e-wallet service. Before calling these APIs, please make sure you’ve learnt the business logic between these APIs.", "version": "1.0", "title": "E-Wallet Experience Layer", "contact": {"name": "<EMAIL>"}}, "schemes": ["https"], "host": "simnectz.hsu.edu.hk/lbsgateway/third-ewallet-experience", "basePath": "/", "tags": [{"name": "E-Wallet Account Information", "description": "System services APIs supports the authentication and authorization processing."}, {"name": "My wallet’s bank cards", "description": "My wallet related bank card services includes bank login, card binding, balance enquiry, password."}, {"name": "Transaction", "description": "e-Wallet transaction includes merchant information,payment and transaction log enquiry."}], "paths": {"/e-wallet/bankCard/bind": {"post": {"tags": ["My wallet’s bank cards"], "summary": "This API is designed to bind a bank account to the e-Wallet account.", "description": "When you opened an e-wallet account, you may want to call this API to bind a bank account to the e-wallet account.", "operationId": "bindCardUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.<br>maxLength: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzI1NiJ9.********************************************************************.R5UztJFjrbVmTcd9VcVXTxbqFyIKnuadTCvabLTrMpY"}, {"in": "body", "name": "vo", "description": "vo", "required": true, "schema": {"$ref": "#/definitions/BindCardVo"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/Res"}}}}}, "/e-wallet/bankCard/unbind": {"post": {"tags": ["My wallet’s bank cards"], "summary": "This API is designed to unbind a bank account from the e-Wallet account.", "description": "Before calling this API to unbind a bank account from the e-wallet account, please make sure a bank account has been bounded into the e-wallet account by calling /e-wallet/bankCard/bind.", "operationId": "unbindUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.<br>maxLength: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzI1NiJ9.********************************************************************.R5UztJFjrbVmTcd9VcVXTxbqFyIKnuadTCvabLTrMpY"}, {"in": "body", "name": "vo", "description": "vo", "required": true, "schema": {"$ref": "#/definitions/BindCardVo"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/Res"}}}}}, "/e-wallet/merchantInfo/all": {"get": {"tags": ["Transaction"], "summary": "This API is designed to retrieve  information of all the merchants that are registered in the e-wallet system.", "description": "Before consuming with the e-wallet account, you may want to call this API to check all the merchants and then make transactions with them.", "operationId": "allUsingGET", "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.<br>maxLength: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzI1NiJ9.********************************************************************.R5UztJFjrbVmTcd9VcVXTxbqFyIKnuadTCvabLTrMpY"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/Res«List«MerchantInfoEntity»»"}}}}}, "/e-wallet/merchantInfo/qrCode": {"post": {"tags": ["Transaction"], "summary": "Get the merchant's payment QR code.", "description": "Get the merchant's payment QR code.", "operationId": "qrCodeUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.<br>maxLength: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzI1NiJ9.********************************************************************.R5UztJFjrbVmTcd9VcVXTxbqFyIKnuadTCvabLTrMpY"}, {"in": "body", "name": "vo", "description": "vo", "required": true, "schema": {"$ref": "#/definitions/MerchantVo"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/Res«object»"}}}}}, "/e-wallet/pay/ByBankCard": {"post": {"tags": ["Transaction"], "summary": "This API is designed to pay with the bounded bank card.", "description": "When consuming with the e-wallet account, you may want to call this API to make a payment.", "operationId": "cardPayUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.<br>maxLength: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzI1NiJ9.********************************************************************.R5UztJFjrbVmTcd9VcVXTxbqFyIKnuadTCvabLTrMpY"}, {"in": "body", "name": "vo", "description": "vo", "required": true, "schema": {"$ref": "#/definitions/CardPayVo"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/Res"}}}}}, "/e-wallet/pay/ByWalletBalance": {"post": {"tags": ["Transaction"], "summary": "This API is designed to pay with the e-wallet account balance.", "description": "When consuming with the e-wallet account, you may want to call this API to make a payment.", "operationId": "balancePayUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.<br>maxLength: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzI1NiJ9.********************************************************************.R5UztJFjrbVmTcd9VcVXTxbqFyIKnuadTCvabLTrMpY"}, {"in": "body", "name": "vo", "description": "vo", "required": true, "schema": {"$ref": "#/definitions/BalancePayVo"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/Res"}}}}}, "/e-wallet/pay/ChangePaymentPwd": {"post": {"tags": ["My wallet’s bank cards"], "summary": "This API is designed to change the e-wallet account payment password.", "description": "When you want to reset the e-wallet account payment password, please call this API.", "operationId": "changePwdUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.<br>maxLength: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzI1NiJ9.********************************************************************.R5UztJFjrbVmTcd9VcVXTxbqFyIKnuadTCvabLTrMpY"}, {"in": "body", "name": "vo", "description": "vo", "required": true, "schema": {"$ref": "#/definitions/ChangePwdVo"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/Res"}}}}}, "/e-wallet/pay/cardBalance": {"post": {"tags": ["My wallet’s bank cards"], "summary": "This API is designed to retrieve the balance of the bounded bank card.", "description": "Before calling this API, please make sure you've bounded a bank card to the e-wallet account by calling /e-wallet/bankCard/bind and call  /e-wallet/pay/recharge API to top up the e-wallet account .", "operationId": "cardBalanceUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.<br>maxLength: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzI1NiJ9.********************************************************************.R5UztJFjrbVmTcd9VcVXTxbqFyIKnuadTCvabLTrMpY"}, {"in": "body", "name": "cardBalanceVo", "description": "cardBalanceVo", "required": true, "schema": {"$ref": "#/definitions/CardBalanceVo"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/Res«BalanceVo»"}}}}}, "/e-wallet/pay/eWalletBalance": {"get": {"tags": ["My wallet’s bank cards"], "summary": "This API is designed to retrieve the e-wallet account balance. ", "description": "Before calling this API, please make sure you've logged into the e-wallet account by calling /e-wallet/sys/login API.", "operationId": "eWalletBalanceUsingGET", "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.<br>maxLength: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzI1NiJ9.********************************************************************.R5UztJFjrbVmTcd9VcVXTxbqFyIKnuadTCvabLTrMpY"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/Res«BalanceVo»"}}}}}, "/e-wallet/pay/recharge": {"post": {"tags": ["My wallet’s bank cards"], "summary": "This API is designed to top up the e-wallet account balance.", "description": "Before calling this API, please make sure you've bounded a bank card to the e-wallet account by calling /e-wallet/bankCard/bind.", "operationId": "rechargeUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.<br>maxLength: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzI1NiJ9.********************************************************************.R5UztJFjrbVmTcd9VcVXTxbqFyIKnuadTCvabLTrMpY"}, {"in": "body", "name": "vo", "description": "vo", "required": true, "schema": {"$ref": "#/definitions/RechargeVo"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/Res"}}}}}, "/e-wallet/pay/withdraw": {"post": {"tags": ["My wallet’s bank cards"], "summary": "This API is designed to withdraw from the e-wallet balance to the bounded bank cards.", "description": "When you want to move the money from e-wallet account to your bounded bank account, you may want to call this API.", "operationId": "withdrawUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.<br>maxLength: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzI1NiJ9.********************************************************************.R5UztJFjrbVmTcd9VcVXTxbqFyIKnuadTCvabLTrMpY"}, {"in": "body", "name": "vo", "description": "vo", "required": true, "schema": {"$ref": "#/definitions/WithdrawVo"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/Res"}}}}}, "/e-wallet/sys/login": {"post": {"tags": ["E-Wallet Account Information"], "summary": "This API is designed to log into the e-wallet system.", "description": "Before doing any operation in the e-wallet system, you always need to login first by calling this API.", "operationId": "loginUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.<br>maxLength: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzI1NiJ9.********************************************************************.R5UztJFjrbVmTcd9VcVXTxbqFyIKnuadTCvabLTrMpY"}, {"in": "body", "name": "vo", "description": "vo", "required": true, "schema": {"$ref": "#/definitions/LoginUserVo"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/Res"}}}}}, "/e-wallet/sys/loginuserenquiry": {"post": {"tags": ["E-Wallet Account Information"], "summary": "This API is designed to retrieve the current login user details.", "description": "Before calling this API,please make sure you've logged ito the system by calling /e-wallet/sys/login.", "operationId": "loginUserUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.<br>maxLength: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzI1NiJ9.********************************************************************.R5UztJFjrbVmTcd9VcVXTxbqFyIKnuadTCvabLTrMpY"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/Res«SysUser»"}}}}}, "/e-wallet/sys/register": {"post": {"tags": ["E-Wallet Account Information"], "summary": "This API is designed to register an e-wallet user.", "description": "If you want to use the e-wallet system, you need to call this API to create a e-wallet account.", "operationId": "registerUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.<br>maxLength: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzI1NiJ9.********************************************************************.R5UztJFjrbVmTcd9VcVXTxbqFyIKnuadTCvabLTrMpY"}, {"in": "body", "name": "vo", "description": "vo", "required": true, "schema": {"$ref": "#/definitions/RegisterUserVo"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/Res"}}}}}, "/e-wallet/sys/updateloginpassword": {"post": {"tags": ["E-Wallet Account Information"], "summary": "This API is designed to update the e-wallet login password.", "description": "When you need to reset the e-wallet account login password, please call this API.", "operationId": "updatePwdUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.<br>maxLength: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzI1NiJ9.********************************************************************.R5UztJFjrbVmTcd9VcVXTxbqFyIKnuadTCvabLTrMpY"}, {"in": "body", "name": "vo", "description": "vo", "required": true, "schema": {"$ref": "#/definitions/UpdatePwdVo"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/Res"}}}}}, "/e-wallet/transaction/logs": {"post": {"tags": ["Transaction"], "summary": "This API is designed to  retrieve all the transactions of the e-wallet account.", "description": "When you want to retrieve transactions history  of this e-wallet account, please call this API.", "operationId": "logsUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.<br>maxLength: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzI1NiJ9.********************************************************************.R5UztJFjrbVmTcd9VcVXTxbqFyIKnuadTCvabLTrMpY"}, {"in": "body", "name": "vo", "description": "vo", "required": true, "schema": {"$ref": "#/definitions/TransactionLogVo"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/Res«List«WalletTransactionLog»»"}}}}}, "/e-wallet/vbs/check": {"post": {"tags": ["My wallet’s bank cards"], "summary": "This API is designed to login to the bank system when calling open API of the bank.", "description": "To develop some functions in the e-wallet system, like top up e-wallet balance from a bank account, we may need to call open APIs of the bank. In this situation, we need to call /e-wallet/vbs/login to login to the bank system first.  ", "operationId": "checkUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.<br>maxLength: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzI1NiJ9.********************************************************************.R5UztJFjrbVmTcd9VcVXTxbqFyIKnuadTCvabLTrMpY"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/Res"}}}}}, "/e-wallet/vbs/login": {"post": {"tags": ["My wallet’s bank cards"], "summary": "This API is designed to check  whether the bank user is authorized by the bank when calling open APIs of the bank to access the user data. After authorized successfully, the developer is able to call the open bank  APIs.", "description": "After calling /e-wallet/vbs/login API, the bank user record,which carries a unique token of this user, is saved in the e-wallet system.  /e-wallet/vbs/check API will check the user token in the e-wallet system. If authorized successfully, the developer can call open bank APIs, like /e-wallet/bankCard/bind to bind a bank account into the e-wallet account.", "operationId": "loginUsingPOST_1", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.<br>maxLength: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzI1NiJ9.********************************************************************.R5UztJFjrbVmTcd9VcVXTxbqFyIKnuadTCvabLTrMpY"}, {"in": "body", "name": "vo", "description": "vo", "required": true, "schema": {"$ref": "#/definitions/VbsLoginVo"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/Res"}}}}}}, "definitions": {"BalancePayVo": {"type": "object", "required": ["amount", "merchantNumber", "payPassword"], "properties": {"amount": {"type": "number", "example": 100, "description": "The payment amount in this transaction.<br>maxLength: 18</br>", "allowEmptyValue": false}, "merchantNumber": {"type": "string", "example": "******************", "description": "A unique number to identify a merchant in the ewallet system.<br>maxLength: 64</br>", "allowEmptyValue": false}, "payPassword": {"type": "string", "example": 123456, "description": "Payment password of the ewallet account.<br>maxLength: 64</br>", "allowEmptyValue": false}}, "title": "BalancePayVo"}, "BalanceVo": {"type": "object", "properties": {"balance": {"type": "number", "description": "E-wallet or bank card available balance.<br>maxLength: 18</br>", "allowEmptyValue": false}, "currency": {"type": "string", "description": "Currency code.<br>maxLength: 3</br>", "allowEmptyValue": false}, "lastUpdateTime": {"type": "string", "format": "date-time", "description": "Last update time.<br>maxLength: 20</br>", "allowEmptyValue": false}}, "title": "BalanceVo"}, "BindCardVo": {"type": "object", "required": ["accountNumber", "accountType", "customerNumber"], "properties": {"accountNumber": {"type": "string", "example": "***********************", "description": "The number of a bank account. Supported account types: Saving Account, Current Account and Credit Card Account.<br>maxLength: 34</br>", "allowEmptyValue": false}, "accountType": {"type": "string", "example": "SAVI", "description": "The type of bank account that the customer has bound.<br>Savings account ending in SAVI-001<br>Current account ending in CURR-002<br>CRED - Credit card account.<br>maxLength: 4</br>", "allowEmptyValue": false}, "customerNumber": {"type": "string", "example": "************", "description": "Bank customer number. After the bank customer is successfully created, the system will generate a unique number for that customer.<br>maxLength: 25</br>", "allowEmptyValue": false}}, "title": "BindCardVo"}, "CardBalanceVo": {"type": "object", "required": ["accountNumber"], "properties": {"accountNumber": {"type": "string", "example": "***********************", "description": "The number of a bank account. Supported account types: Saving Account, Current Account and Credit Card Account.<br>maxLength: 34</br>", "allowEmptyValue": false}}, "title": "CardBalanceVo"}, "CardPayVo": {"type": "object", "required": ["accountNumber", "accountType", "merchantNumber", "payPassword", "transactionAmount", "transactionCcy"], "properties": {"accountNumber": {"type": "string", "example": "***********************", "description": "The number of a bank account. Supported account types: Saving Account, Current Account and Credit Card Account.<br>maxLength: 34</br>", "allowEmptyValue": false}, "accountType": {"type": "string", "example": "SAVI", "description": "The type of bank account that the customer has bound.<br>Savings account ending in SAVI-001<br>Current account ending in CURR-002<br>CRED - Credit card account.<br>maxLength: 4</br>", "allowEmptyValue": false}, "merchantNumber": {"type": "string", "example": "******************", "description": "A unique number to identify a merchant in the ewallet system.<br>maxLength: 64</br>", "allowEmptyValue": false}, "payPassword": {"type": "string", "example": 123456, "description": "Payment password of the ewallet account.<br>maxLength: 64</br>", "allowEmptyValue": false}, "transactionAmount": {"type": "number", "example": 100, "description": "The payment amount in this transaction.<br>maxLength: 18</br>", "allowEmptyValue": false}, "transactionCcy": {"type": "string", "example": "HKD", "description": "Transaction currency code.Possible value: HKD.<br>maxLength: 3</br>", "allowEmptyValue": false}}, "title": "CardPayVo"}, "ChangePwdVo": {"type": "object", "required": ["newPwd", "oldPwd"], "properties": {"newPwd": {"type": "string", "example": 888888, "description": "New payment password set by the ewallet user.<br>maxLength: 64</br>", "allowEmptyValue": false}, "oldPwd": {"type": "string", "example": 123456, "description": "Old payment password of the ewallet user.<br>maxLength: 64</br>", "allowEmptyValue": false}}, "title": "ChangePwdVo"}, "LoginUserVo": {"type": "object", "required": ["password", "username"], "properties": {"password": {"type": "string", "example": ********, "description": "Login password for ewallet system.<br>maxLength: 64</br>", "allowEmptyValue": false}, "username": {"type": "string", "example": "<PERSON><PERSON><PERSON>", "description": "Login username for ewallet system.<br>maxLength: 64</br>", "allowEmptyValue": false}}, "title": "LoginUserVo"}, "MerchantInfoEntity": {"type": "object", "properties": {"branchcode": {"type": "string", "description": "Branch code.<br>maxLength: 20</br>", "allowEmptyValue": false}, "businesslicenseid": {"type": "string", "description": "Business license id.<br>maxLength: 50</br>", "allowEmptyValue": false}, "categoryname": {"type": "string", "description": "Merchant category name.<br>maxLength: 150</br>", "allowEmptyValue": false}, "clearingcode": {"type": "string", "description": "Clearing code.<br>maxLength: 20</br>", "allowEmptyValue": false}, "countrycode": {"type": "string", "description": "Country code.<br>maxLength: 2</br>", "allowEmptyValue": false}, "displayname": {"type": "string", "description": "Display name.<br>maxLength: 140</br>", "allowEmptyValue": false}, "id": {"type": "integer", "format": "int32", "description": "Merchant information uniquely identifies.<br>maxLength: 20</br>", "allowEmptyValue": false}, "lastupdateddate": {"type": "number", "description": "Last updated date.<br>maxLength: 20</br>", "allowEmptyValue": false}, "merchantaccountnumber": {"type": "string", "description": "Merchant account number.<br>maxLength: 34</br>", "allowEmptyValue": false}, "merchantaddress": {"type": "string", "description": "Merchant address.<br>maxLength: 490</br>", "allowEmptyValue": false}, "merchantname": {"type": "string", "description": "merchant name.<br>maxLength: 140</br>", "allowEmptyValue": false}, "merchantnumber": {"type": "string", "description": "Merchant number.<br>maxLength: 35</br>", "allowEmptyValue": false}, "outstandingbalance": {"type": "number", "description": "Outstanding balance.<br>maxLength: 18</br>", "allowEmptyValue": false}, "phone": {"type": "string", "description": "Phone number.<br>maxLength: 34</br>", "allowEmptyValue": false}}, "title": "MerchantInfoEntity"}, "MerchantVo": {"type": "object", "required": ["merchantNumber"], "properties": {"merchantNumber": {"type": "string", "example": "******************", "description": "A unique number to identify a merchant in the ewallet system.<br>maxLength: 34</br>", "allowEmptyValue": false}}, "title": "MerchantVo"}, "RechargeVo": {"type": "object", "required": ["accountNumber", "amount", "ccy", "payPassword"], "properties": {"accountNumber": {"type": "string", "example": "***********************", "description": "The number of a bank account. Supported account types: Saving Account, Current Account and Credit Card Account.<br>maxLength: 34</br>", "allowEmptyValue": false}, "amount": {"type": "number", "format": "double", "example": 100, "description": "Transaction amount.<br>maxLength: 18</br>", "allowEmptyValue": false}, "ccy": {"type": "string", "example": "HKD", "description": "Transaction currency code.Possible value: HKD.<br>maxLength: 3</br>", "allowEmptyValue": false}, "payPassword": {"type": "string", "example": 123456, "description": "Payment password of the ewallet account.<br>maxLength: 64</br>", "allowEmptyValue": false}}, "title": "RechargeVo"}, "RegisterUserVo": {"type": "object", "required": ["birthday", "chineseName", "customerId", "firstName", "lastName", "password", "payPassword", "phone", "username"], "properties": {"birthday": {"type": "string", "format": "date-time", "example": "1997-11-06", "description": "User's birthday.<br>maxLength: 20</br>", "allowEmptyValue": false}, "chineseName": {"type": "string", "example": "王妍", "description": "User's Chinese name.<br>maxLength: 64</br>", "allowEmptyValue": false}, "customerId": {"type": "string", "example": "U735535(9)", "description": "User ID number.<br>maxLength: 64</br>", "allowEmptyValue": false}, "firstName": {"type": "string", "example": "<PERSON>", "description": "User name.<br>maxLength: 64</br>", "allowEmptyValue": false}, "lastName": {"type": "string", "example": "Yan", "description": "User surname.<br>maxLength: 64</br>", "allowEmptyValue": false}, "password": {"type": "string", "example": ********, "description": "Login password for ewallet system.<br>maxLength: 64</br>", "allowEmptyValue": false}, "payPassword": {"type": "string", "example": 123456, "description": "Payment password of the ewallet account.<br>maxLength: 64</br>", "allowEmptyValue": false}, "phone": {"type": "string", "example": ***********, "description": "User phone number.<br>maxLength: 16</br>", "allowEmptyValue": false}, "username": {"type": "string", "example": "<PERSON><PERSON><PERSON>", "description": "Login username for ewallet system.<br>maxLength: 64</br>", "allowEmptyValue": false}}, "title": "RegisterUserVo"}, "Res": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "object"}, "msg": {"type": "string"}}, "title": "Res"}, "Res«BalanceVo»": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/definitions/BalanceVo"}, "msg": {"type": "string"}}, "title": "Res«BalanceVo»"}, "Res«List«MerchantInfoEntity»»": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/definitions/MerchantInfoEntity"}}, "msg": {"type": "string"}}, "title": "Res«List«MerchantInfoEntity»»"}, "Res«List«WalletTransactionLog»»": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "array", "items": {"$ref": "#/definitions/WalletTransactionLog"}}, "msg": {"type": "string"}}, "title": "Res«List«WalletTransactionLog»»"}, "Res«SysUser»": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"$ref": "#/definitions/SysUser"}, "msg": {"type": "string"}}, "title": "Res«SysUser»"}, "Res«object»": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32"}, "data": {"type": "object"}, "msg": {"type": "string"}}, "title": "Res«object»"}, "SysUser": {"type": "object", "properties": {"birthday": {"type": "string", "format": "date-time", "description": "User birthday.<br>maxLength: 20</br>", "allowEmptyValue": false}, "chineseName": {"type": "string", "description": "User chinese name.<br>maxLength: 64</br>", "allowEmptyValue": false}, "createTime": {"type": "string", "format": "date-time", "description": "User create time.<br>maxLength: 20</br>", "allowEmptyValue": false}, "customerId": {"type": "string", "description": "Customer id.<br>maxLength: 64</br>", "allowEmptyValue": false}, "firstName": {"type": "string", "description": "First name.<br>maxLength: 64</br>", "allowEmptyValue": false}, "lastName": {"type": "string", "description": "Last name.<br>maxLength: 64</br>", "allowEmptyValue": false}, "phone": {"type": "string", "description": "User phone number.<br>maxLength: 16</br>", "allowEmptyValue": false}, "updateTime": {"type": "string", "format": "date-time", "description": "User update time.<br>maxLength: 20</br>", "allowEmptyValue": false}, "userId": {"type": "string", "description": "User uniquely identifies.<br>maxLength: 11</br>", "allowEmptyValue": false}}, "title": "SysUser"}, "TransactionLogVo": {"type": "object", "required": ["begin", "end", "type"], "properties": {"begin": {"type": "integer", "format": "int64", "example": 1717171200000, "description": "Transaction start timestamp.<br>maxLength: 20</br>", "allowEmptyValue": false}, "end": {"type": "integer", "format": "int64", "example": 1719763199000, "description": "Transaction end timestamp.<br>maxLength: 20</br>", "allowEmptyValue": false}, "type": {"type": "string", "example": "BALANCE_PAY", "description": "Transaction type, supported type: BALANCE_PAY; CARD_PAY<br>maxLength: 64</br>", "allowEmptyValue": false}}, "title": "TransactionLogVo"}, "UpdatePwdVo": {"type": "object", "required": ["newPwd", "oldPwd"], "properties": {"newPwd": {"type": "string", "example": ********, "description": "New login password set by the ewallet user.<br>maxLength: 64</br>", "allowEmptyValue": false}, "oldPwd": {"type": "string", "example": ********, "description": "Old login password of the ewallet user.<br>maxLength: 64</br>", "allowEmptyValue": false}}, "title": "UpdatePwdVo"}, "VbsLoginVo": {"type": "object", "required": ["loginname", "loginpwd"], "properties": {"loginname": {"type": "string", "example": "U735535(9)", "description": "Login account of VBS(Virtual Banking System) system.<br>maxLength: 64</br>", "allowEmptyValue": false}, "loginpwd": {"type": "string", "example": 123456, "description": "Login password of VBS(Virtual Banking System) system.<br>maxLength: 64</br>", "allowEmptyValue": false}}, "title": "VbsLoginVo"}, "WalletTransactionLog": {"type": "object", "properties": {"accountNumber": {"type": "string"}, "amount": {"type": "number"}, "ccy": {"type": "string"}, "eWalletID": {"type": "string"}, "tranSeq": {"type": "string"}, "tranTime": {"type": "number"}, "tranType": {"type": "string"}}, "title": "WalletTransactionLog"}, "WithdrawVo": {"type": "object", "required": ["accountNumber", "amount", "ccy", "payPassword"], "properties": {"accountNumber": {"type": "string", "example": "***********************", "description": "The number of a bank account. Supported account types: Saving Account, Current Account and Credit Card Account.<br>maxLength: 34</br>", "allowEmptyValue": false}, "amount": {"type": "number", "example": 100, "description": "Transaction amount.<br>maxLength: 18</br>", "allowEmptyValue": false}, "ccy": {"type": "string", "example": "HKD", "description": "Transaction currency code.Possible value: HKD.<br>maxLength: 3</br>", "allowEmptyValue": false}, "payPassword": {"type": "string", "example": 123456, "description": "Payment password of the ewallet account.<br>maxLength: 64</br>", "allowEmptyValue": false}}, "title": "WithdrawVo"}}}