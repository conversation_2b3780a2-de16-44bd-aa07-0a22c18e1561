{"swagger": "2.0", "info": {"description": "APIs here are all about deposit service. Before calling these APIs, please make sure you’ve learnt the business logic between these APIs.", "version": "1.0", "title": "Deposit Service", "contact": {"name": "<EMAIL>"}}, "schemes": ["https"], "host": "simnectz.hsu.edu.hk/lbsgateway/deposit-experience", "basePath": "/", "tags": [{"name": "Transaction", "description": "Term deposit contract processing, includes application, drawdown, renewal and transaction enquiry."}, {"name": "Market Information", "description": "Retrieve and display latest Term Deposit interest rate."}], "paths": {"/termDeposit/AccountTermDeposit": {"post": {"tags": ["Transaction"], "summary": "This API is designed to retrieve all the term deposits in this account number.", "description": "A customer can make multiple term deposits in one term deposit account. This API helps you retrieve all the term deposits in this term deposit account. Before  calling this API, you may need to call /termDeposit/application API to make successful term deposits.", "operationId": "termDepositUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.<br>maxLength: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "Message ID in 128 bit random UUID format generated uniquely for every request by F/E side.<br>maxLength: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "client_id generated during consumer onboarding.<br>maxLength: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "accountTermDepositEnquiry", "description": "accountTermDepositEnquiry", "required": true, "schema": {"$ref": "#/definitions/AccountTermDepositEnquiry"}}], "responses": {"200": {"description": "Query completed successfully.(Returned By Get)", "schema": {"$ref": "#/definitions/TermDepositDetailVo"}}, "201": {"description": "Normal execution. The request has succeeded. (Returned By Post)", "schema": {"$ref": "#/definitions/ResultUtil"}}, "403": {"description": "Token has incorrect scope or a security policy was violated. Action: Please check whether you’re using the right token with the legal authorized user account."}, "404": {"description": "The requested deposit account does not exist.Action: Please make sure the account number and account type you’re inputting are correct."}, "500": {"description": "Something went wrong on the API gateway or micro-service. Action: check your network and try again later."}}}}, "/termDeposit/allTermDeposit": {"post": {"tags": ["Transaction"], "summary": "This API is designed to retrieve all the term deposits of this customer.", "description": "A customer can make multiple term deposits in multiple term deposit accounts. This API helps you retrieve all the term deposits made in all the term deposit accounts of this customer. Before  calling this API, you may need to call /termDeposit/application API to make successful term deposits.", "operationId": "allTermDepositUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.<br>maxLength: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "Message ID in 128 bit random UUID format generated uniquely for every request by F/E side.<br>maxLength: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "client_id generated during consumer onboarding.<br>maxLength: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "customerTermDepositEnquiryModel", "description": "customerTermDepositEnquiryModel", "required": true, "schema": {"$ref": "#/definitions/CustomerTermDepositEnquiryModel"}}], "responses": {"200": {"description": "Query completed successfully.(Returned By Get)", "schema": {"$ref": "#/definitions/ResultUtil«List«TermDepositDetailVo»»"}}, "201": {"description": "Normal execution. The request has succeeded. (Returned By Post)", "schema": {"$ref": "#/definitions/ResultUtil«List«TermDepositDetailVo»»"}}, "403": {"description": "Token has incorrect scope or a security policy was violated. Action: Please check whether you’re using the right token with the legal authorized user account."}, "404": {"description": "The requested deposit account does not exist.Action: Please make sure the account number and account type you’re inputting are correct."}, "500": {"description": "Something went wrong on the API gateway or micro-service. Action: check your network and try again later."}}}}, "/termDeposit/application": {"post": {"tags": ["Transaction"], "summary": "This API is designed to apply for a term deposit.", "description": "Before calling any term deposit related API, you may need to call this API to apply for a term deposit with your term deposit account. If you don't have a term deposit account, please call /deposit/account/accountCreation API to open one.", "operationId": "applicationUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.<br>maxLength: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "Message ID in 128 bit random UUID format generated uniquely for every request by F/E side.<br>maxLength: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "client_id generated during consumer onboarding.<br>maxLength: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "termDepositMasterModel", "description": "termDepositMasterModel", "required": true, "schema": {"$ref": "#/definitions/TermDepositMasterModel"}}], "responses": {"200": {"description": "Query completed successfully.(Returned By Get)", "schema": {"$ref": "#/definitions/TdNumberVo"}}, "201": {"description": "Normal execution. The request has succeeded. (Returned By Post)", "schema": {"$ref": "#/definitions/ResultUtil"}}, "403": {"description": "Token has incorrect scope or a security policy was violated. Action: Please check whether you’re using the right token with the legal authorized user account."}, "404": {"description": "The requested deposit account does not exist.Action: Please make sure the account number and account type you’re inputting are correct."}, "500": {"description": "Something went wrong on the API gateway or micro-service. Action: check your network and try again later."}}}}, "/termDeposit/drawDown": {"post": {"tags": ["Transaction"], "summary": "This API is designed to make a term deposit drawdown. ", "description": "When your term deposit is mature, you can all this API to make a term deposit drawdown. So please make sure you've made a successful term deposit by calling /termDeposit/application API firstly.", "operationId": "drawDownUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.<br>maxLength: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "Message ID in 128 bit random UUID format generated uniquely for every request by F/E side.<br>maxLength: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "client_id generated during consumer onboarding.<br>maxLength: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "termDepositDrawDownModel", "description": "termDepositDrawDownModel", "required": true, "schema": {"$ref": "#/definitions/TermDepositDrawDownModel"}}], "responses": {"200": {"description": "Query completed successfully.(Returned By Get)", "schema": {"$ref": "#/definitions/ResultUtil"}}, "201": {"description": "Normal execution. The request has succeeded. (Returned By Post)", "schema": {"$ref": "#/definitions/ResultUtil"}}, "403": {"description": "Token has incorrect scope or a security policy was violated. Action: Please check whether you’re using the right token with the legal authorized user account."}, "404": {"description": "The requested deposit account does not exist.Action: Please make sure the account number and account type you’re inputting are correct."}, "500": {"description": "Something went wrong on the API gateway or micro-service. Action: check your network and try again later."}}}}, "/termDeposit/enquiry": {"post": {"tags": ["Transaction"], "summary": "This API is designed to get a term deposit details.", "description": "Before calling this API, please make sure you've made a successful term deposit by calling /termDeposit/application API.", "operationId": "enquiryUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.<br>maxLength: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "Message ID in 128 bit random UUID format generated uniquely for every request by F/E side.<br>maxLength: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "client_id generated during consumer onboarding.<br>maxLength: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "termDepositEnquiryModel", "description": "termDepositEnquiryModel", "required": true, "schema": {"$ref": "#/definitions/TermDepositEnquiryModel"}}], "responses": {"200": {"description": "Query completed successfully.(Returned By Get)", "schema": {"$ref": "#/definitions/TermDepositDetailVo"}}, "201": {"description": "Normal execution. The request has succeeded. (Returned By Post)", "schema": {"$ref": "#/definitions/TermDepositDetailVo"}}, "403": {"description": "Token has incorrect scope or a security policy was violated. Action: Please check whether you’re using the right token with the legal authorized user account."}, "404": {"description": "The requested deposit account does not exist.Action: Please make sure the account number and account type you’re inputting are correct."}, "500": {"description": "Something went wrong on the API gateway or micro-service. Action: check your network and try again later."}}}}, "/termDeposit/renewal": {"post": {"tags": ["Transaction"], "summary": "This API is designed to renew a term deposit.", "description": "Before calling this API, please make sure you've had an existing term deposit that is matured.  If not, please call /termDeposit/application API firstly.", "operationId": "renewalUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.<br>maxLength: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "Message ID in 128 bit random UUID format generated uniquely for every request by F/E side.<br>maxLength: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "client_id generated during consumer onboarding.<br>maxLength: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "termDepositRenewalModel", "description": "termDepositRenewalModel", "required": true, "schema": {"$ref": "#/definitions/TermDepositRenewalModel"}}], "responses": {"200": {"description": "Query completed successfully.(Returned By Get)", "schema": {"$ref": "#/definitions/ResultUtil"}}, "201": {"description": "Normal execution. The request has succeeded. (Returned By Post)", "schema": {"$ref": "#/definitions/ResultUtil"}}, "403": {"description": "Token has incorrect scope or a security policy was violated. Action: Please check whether you’re using the right token with the legal authorized user account."}, "404": {"description": "The requested deposit account does not exist.Action: Please make sure the account number and account type you’re inputting are correct."}, "500": {"description": "Something went wrong on the API gateway or micro-service. Action: check your network and try again later."}}}}, "/termDeposit/reterivalTermDepositRate": {"post": {"tags": ["Market Information"], "summary": "This API is designed to retrieve term deposit rate information.", "description": "Before making a term deposit application, you may want to retrieve term deposit interest rate information.", "operationId": "reterivalTermDepositRateUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.<br>maxLength: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "Message ID in 128 bit random UUID format generated uniquely for every request by F/E side.<br>maxLength: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "client_id generated during consumer onboarding.<br>maxLength: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "queryTdRateModel", "description": "queryTdRateModel", "required": true, "schema": {"$ref": "#/definitions/QueryTdRateModel"}}], "responses": {"200": {"description": "Query completed successfully.(Returned By Get)", "schema": {"$ref": "#/definitions/TermDepositRateVo"}}, "201": {"description": "Normal execution. The request has succeeded. (Returned By Post)", "schema": {"$ref": "#/definitions/ResultUtil"}}, "403": {"description": "Token has incorrect scope or a security policy was violated. Action: Please check whether you’re using the right token with the legal authorized user account."}, "404": {"description": "The requested deposit account does not exist.Action: Please make sure the account number and account type you’re inputting are correct."}, "500": {"description": "Something went wrong on the API gateway or micro-service. Action: ch eck your network and try again later."}}}}}, "definitions": {"AccountTermDepositEnquiry": {"type": "object", "required": ["accountNumber", "index", "items"], "properties": {"accountNumber": {"type": "string", "example": "***********************", "description": "A term deposit account number.maxLength is 34", "allowEmptyValue": false}, "index": {"type": "number", "example": 0.0, "description": " an indicator to identify where your respone result will start to show.<br>maxLength: 20</br>", "allowEmptyValue": false}, "items": {"type": "number", "example": 10.0, "description": " the number of items you'd like to get in the response result.<br>maxLength: 20</br>", "allowEmptyValue": false}}, "title": "AccountTermDepositEnquiry"}, "AcknowledgeModel": {"type": "object", "required": ["acknowledge", "customerIDType", "customerId"], "properties": {"acknowledge": {"type": "string", "example": "Y", "description": "Is there a unified agreement.<br>maxLength: 1</br>", "allowEmptyValue": false}, "customerIDType": {"type": "string", "example": "I", "description": "Customer ID Type.I:ID Card,P:Passport <br>maxLength: 1</br>", "allowEmptyValue": false}, "customerId": {"type": "string", "example": "U735535(9)", "description": "The unique identification of a customer in his/her country.<br></br>maxLength is 35", "allowEmptyValue": false}}, "title": "AcknowledgeModel"}, "AddAccountModel": {"type": "object", "required": ["accountType", "currencyCode", "customerNumber"], "properties": {"accountType": {"type": "string", "example": "001", "description": " Account type means whether the account is a saving, current, stock trading  account and so on.  Possible values:<br></br>001 (saving account);002 (current account);003 (foreign currency account);100 (term deposit account);<br></br>300 (stock trading account);400 (precious metal account);500 (mutual fund account);600 (Mortgage Loan)<br></br>maxLength: 3", "allowEmptyValue": false}, "currencyCode": {"type": "string", "example": "HKD", "description": " Currency set for the account. Please set correct currency type based on the account type.    Currently, <br></br>for Saving/Current/Term Deposit/Loan/Fund/Stock/Precious Metal Account, <br></br>only HKD is supported at this moment; <br></br>for foreign exchange account, <br></br>the following 11 currency is supported:  HKD,CNY,USD,AUD,EUR,CHF,CAD,GBP,JPY,NZD,SGD.</br>maxLength: 3", "allowEmptyValue": false}, "customerNumber": {"type": "string", "example": "************", "description": "The unique ID generated when we create a new customer record  in the banking system.<br></br>maxLength: 25", "allowEmptyValue": false}, "relaccountnumber": {"type": "string", "example": "***********************", "description": " the Saving/Current account that is associated with a Term Deposit/Loan/Fund/Stock Precious Metal Account. <br></br>For Saving or Current account creation, please keep relaccountnumber field empty. <br></br>For other types account creation, this field is required. <br></br>maxLength: 34", "allowEmptyValue": true}}, "title": "AddAccountModel"}, "AddCustomerModel": {"type": "object", "required": ["customerID", "customerIDType", "dateOfBirth", "firstname", "issueCountry", "lastname", "mailingAddress", "mobilePhoneNumber"], "properties": {"accommodation": {"type": "string", "example": "S", "description": "The ownership of the provided residential address. If it is set S, that means the accommodation is self owned.<br></br>Possible values:S-Self Owned, P-Private property, <br></br>H-Home ownership scheme,F-Friends/Relatives, <br></br>U-Public Housing,R-Rented, Q-Quarters.<br><br>maxLength: 255</br>", "allowEmptyValue": false}, "branchcode": {"type": "string", "example": "001", "description": "A branch code is a unique identifying code for a given branch of a bank.<br><br>maxLength: 20</br>", "allowEmptyValue": false}, "chinesename": {"type": "string", "example": "王研", "description": "The Chinese name of the customer shown in his/her ID card.<br><br>maxLength: 140</br>", "allowEmptyValue": false}, "clearingcode": {"type": "string", "example": "0001", "description": "clearingcode <br>maxLength: 20</br>", "allowEmptyValue": false}, "companyaddress": {"type": "string", "example": "228 Pok Fu Lam Road,Aberdeen,Hong Kong", "description": "The physical address of the company where the customer works.<br><br>maxLength: 1000</br>", "allowEmptyValue": false}, "companyphonenumber": {"type": "string", "example": ********, "description": "The phone number of the company where the customer works.<br><br>maxLength: 34</br>", "allowEmptyValue": false}, "countrycode": {"type": "string", "example": "HK", "description": "Country codes are short alphabetic or numeric geographical codes developed to represent countries and dependent areas.<br><br>maxLength: 2</br>", "allowEmptyValue": false}, "customerID": {"type": "string", "example": "U735535(9)", "description": "The unique identification of a customer in his/her country.<br><br>maxLength: 35</br>", "allowEmptyValue": false}, "customerIDType": {"type": "string", "example": "I", "description": "CustomerID Type(I/P)", "allowEmptyValue": false}, "dateOfBirth": {"type": "string", "example": ************, "description": "The birthday of the customer.<br><br>maxLength: 20</br>", "allowEmptyValue": false}, "education": {"type": "string", "example": "U", "description": "The education level of this customer. When it is set U, that means the customer education level is university.<br></br>Possible values: U-University, P-Post Secondary, <br></br>S-Secondary, J-Primary/Junior,O-Others.<br><br>maxLength: 1</br>", "allowEmptyValue": false}, "emailaddress": {"type": "string", "example": "<EMAIL>", "description": "Email Address maxLength is 320", "allowEmptyValue": false}, "employercompanyname": {"type": "string", "example": "Happy Realty", "description": "The name of the company for whom the customer works.<br><br>maxLength: 140</br>", "allowEmptyValue": false}, "firstname": {"type": "string", "example": "Yan", "description": "First Name <br>maxLength: 70</br>", "allowEmptyValue": false}, "gender": {"type": "string", "example": "M", "description": "The gender of the customer. When it is set M, that means the customer is a Male.Possible values: M-Male, F-Female.<br></br>maxLength is 1<br>maxLength: 1</br>", "allowEmptyValue": false}, "issueCountry": {"type": "string", "example": "China", "description": "The issue country of the customer’s ID. If it is set China, that means China is the issuing country for the customer’s ID.<br><br>maxLength: 20</br>", "allowEmptyValue": false}, "lastname": {"type": "string", "example": "<PERSON>", "description": "Last Name <br>maxLength: 70</br>", "allowEmptyValue": false}, "mailingAddress": {"type": "string", "example": "551 Austin Road,Tsim Sha Tsui,Kowloon", "description": "The mailingaddress is an address to which your mail can be sent.<br><br>maxLength: 1000</br>", "allowEmptyValue": false}, "maritalstatus": {"type": "string", "example": "S", "description": "Marital status describes a person's relationship with a significant person.When it is set S, <br></br>that means the customer is single.Possible values: S-Single, M-Married, D-Divorced.<br><br>maxLength: 1</br>", "allowEmptyValue": false}, "mobilePhoneNumber": {"type": "string", "example": 64657884, "description": "Customer’s mobile phone number.<br>maxLength: 34</br>", "allowEmptyValue": false}, "monthlysalary": {"type": "string", "example": 19000.0, "description": "The salary paid by his/her employer company every month.<br><br>maxLength: 18</br>", "allowEmptyValue": false}, "nationality": {"type": "string", "example": "China", "description": "Nationality is a legal relationship between an individual person and a state.\tWhen it is set China, <br></br>that means the customer is a Chinese. Possible values: China, Japan, German, France.<br></br>maxLength is 20<br>maxLength: 20</br>", "allowEmptyValue": false}, "occupation": {"type": "string", "example": "Software", "description": "The job or profession of the customer.<br>maxLength: 1000</br>", "allowEmptyValue": false}, "permanentresidencestatus": {"type": "string", "example": "Y", "description": "Whether the customer is a Hong Kong permanent resident or not.When it is set Y, it means yes. Possible values:Y-Yes, N-No.<br><br>maxLength: 1</br>", "allowEmptyValue": false}, "position": {"type": "string", "example": "Senior Manager", "description": "The job title of this customer in his/her company. Possible value:CEO, Senior Manager, Consultant, <br></br>Business Analyst, Chairman, staff, Engineer, Manager, Director.<br><br>maxLength: 500</br>", "allowEmptyValue": false}, "residencephonenumber": {"type": "string", "example": 34828869, "description": "The phone number of the customer’s residential address in HongKong.<br><br>maxLength: 34</br>", "allowEmptyValue": false}, "residentialaddress": {"type": "string", "example": "779 Yi Chun Street, Sai Kung, New Territory", "description": "The Hong Kong address where the customer lives.<br><br>maxLength: 1000</br>", "allowEmptyValue": false}, "wechatid": {"type": "string", "example": "W3456754", "description": "The customer’s WeChat ID <br>maxLength: 30</br>", "allowEmptyValue": false}, "yearsofresidence": {"type": "integer", "format": "int32", "example": 3, "description": "The year of the customer living in Hong Kong.<br><br>maxLength: 3</br>", "allowEmptyValue": false}, "yearsofservices": {"type": "integer", "format": "int32", "example": 2, "description": "The year of the customer working in this company.<br>maxLength: 3</br>", "allowEmptyValue": false}}, "title": "AddCustomerModel"}, "AddCustomerVo": {"type": "object", "properties": {"customerID": {"type": "string", "description": "Unique customer ID in the system.<br>maxLength: 35</br>", "allowEmptyValue": false}, "customernumber": {"type": "string", "description": "Unique customer number in the system.<br>maxLength: 25</br>", "allowEmptyValue": false}}, "title": "AddCustomerVo"}, "AddressCheckApplyModel": {"type": "object", "required": ["address", "customerIDType", "customerId"], "properties": {"address": {"type": "string", "example": "No. 3-5, Wan Chai Gap Road, 香港特别行政区, 中国", "description": "Address where the customer lives.<br>maxLength: 1000</br>", "allowEmptyValue": false}, "customerIDType": {"type": "string", "example": "I", "description": "Customer ID Type.I:IDCard,P:Passport<br>maxLength: 1</br>", "allowEmptyValue": false}, "customerId": {"type": "string", "example": "U735535(9)", "description": "The unique identification of a customer in his/her country.<br></br>maxLength is 35", "allowEmptyValue": false}}, "title": "AddressCheckApplyModel"}, "AllAccountModel": {"type": "object", "properties": {"creditCard": {"type": "array", "description": "credit card accounts and the status of each card", "allowEmptyValue": false, "items": {"$ref": "#/definitions/ListModel"}}, "current": {"type": "array", "description": "current accounts number and the status of each card", "allowEmptyValue": false, "items": {"$ref": "#/definitions/ListModel"}}, "customerName": {"type": "string", "description": "Customer name in the system.<br>maxLength: 140</br>", "allowEmptyValue": false}, "customerNumber": {"type": "string", "description": "Unique customer ID in the system.<br>maxLength: 25</br>", "allowEmptyValue": false}, "fex": {"type": "array", "description": "fex accounts number and the status of each card", "allowEmptyValue": false, "items": {"$ref": "#/definitions/ListModel"}}, "loan": {"type": "array", "description": "All loan card accounts and the status of each card", "allowEmptyValue": false, "items": {"$ref": "#/definitions/ListModel"}}, "mutualFund": {"type": "array", "description": "mutual fund number and the status of each card", "allowEmptyValue": false, "items": {"$ref": "#/definitions/ListModel"}}, "preciousMetal": {"type": "array", "description": "mutual fund number and the status of each card", "allowEmptyValue": false, "items": {"$ref": "#/definitions/ListModel"}}, "saving": {"type": "array", "description": "All savings card accounts and the status of each card", "allowEmptyValue": false, "items": {"$ref": "#/definitions/ListModel"}}, "stock": {"type": "array", "description": "stock number and the status of each card", "allowEmptyValue": false, "items": {"$ref": "#/definitions/ListModel"}}, "termDeposit": {"type": "array", "description": "term deposit accounts number and the status of each card", "allowEmptyValue": false, "items": {"$ref": "#/definitions/ListModel"}}}, "title": "AllAccountModel"}, "BlackListEntity": {"type": "object", "properties": {"chinesename": {"type": "string", "description": "Customer's Chinese name.<br>maxLength: 140</br>", "allowEmptyValue": false}, "customerid": {"type": "string", "description": "The unique identification of a customer in his/her country.<br><br>maxLength: 35</br>", "allowEmptyValue": false}, "dateofbirth": {"type": "number", "description": "Date of birth.<br>maxLength: 20</br>", "allowEmptyValue": false}, "firstname": {"type": "string", "description": "First name of the user.<br>maxLength: 70</br>", "allowEmptyValue": false}, "gender": {"type": "string", "description": "Customer's gender.<br>maxLength: 1</br>", "allowEmptyValue": false}, "id": {"type": "integer", "format": "int32"}, "issuecountry": {"type": "string", "description": "Issuing country of ID card.<br>maxLength: 20</br>", "allowEmptyValue": false}, "lastname": {"type": "string", "description": "Last name of the user.<br>maxLength: 70</br>", "allowEmptyValue": false}, "nationality": {"type": "string", "description": "Customer's nationality.<br>maxLength: 20</br>", "allowEmptyValue": false}, "reasoncode": {"type": "string", "description": "Reason code for customers to be blacklisted.<br>maxLength: 2</br>", "allowEmptyValue": false}, "remark": {"type": "string", "description": "Explanation of customers being blacklisted.<br>maxLength: 200</br>", "allowEmptyValue": false}}, "title": "BlackListEntity"}, "BlacklistModel": {"type": "object", "required": ["customerIDType", "customerId"], "properties": {"customerIDType": {"type": "string", "example": "I", "description": "Customer ID Type.I:ID Card,P:Passport <br>maxLength: 1</br>", "allowEmptyValue": false}, "customerId": {"type": "string", "example": "U735535(9)", "description": "The unique identification of a customer in his/her country.<br></br>maxLength is 35", "allowEmptyValue": false}}, "title": "BlacklistModel"}, "CheckTransactionLogModel": {"type": "object", "required": ["accountnumber", "index", "items", "transFromDate", "transToDate"], "properties": {"accountnumber": {"type": "string", "example": "***********************", "description": "A unique bank account number.<br>maxLength: 34</br>", "allowEmptyValue": false}, "index": {"type": "number", "example": 1.0, "description": " an indicator to identify where your respone result will start to show.<br>maxLength: 20</br>", "allowEmptyValue": false}, "items": {"type": "number", "example": 10.0, "description": "the number of items you'd like to get in the response result.<br>maxLength: 20</br>", "allowEmptyValue": false}, "transFromDate": {"type": "string", "example": *************, "description": "The start date of the transactions.<br>maxLength: 20</br>", "allowEmptyValue": false}, "transToDate": {"type": "string", "example": *************, "description": "The end date of the transactions.<br>maxLength: 20</br>", "allowEmptyValue": false}, "trantype": {"type": "string", "example": "0006", "description": "The type of the transactions.Possible Value: Possible Values:0001 - TD Application,0002 - TD Drawdown,<br></br>0003 - TD Renewal,0004 - Deposit,0005 – Transfer,0006 - Withdrawal,<br></br>0007 - Buying Foreign Exchange,0008 - Selling Foreign Exchange,0009 - Buying stocks,0010 - Selling stocks,<br></br>0011 - Buy fund,0012 - Selling Funds<br></br><br>maxLength: 6</br>", "allowEmptyValue": true}}, "title": "CheckTransactionLogModel"}, "ChequeBookModel": {"type": "object", "required": ["accountNumber", "chequeBookSize", "chequeBookType"], "properties": {"accountNumber": {"type": "string", "example": "***********************", "description": "A current account number.<br><br>maxLength: 34</br>", "allowEmptyValue": false}, "chequeBookSize": {"type": "string", "example": 50, "description": "The size of a cheque book.Possible values:50, 100<br><br>maxLength: 3</br>", "allowEmptyValue": false}, "chequeBookType": {"type": "string", "example": "S", "description": "When it is set S, that means the chequebooktype is Short.Possible values:S-Short,L-Long.<br><br>maxLength: 1</br>", "allowEmptyValue": false}}, "title": "ChequeBookModel"}, "CustomerMaintenanceModel": {"type": "object", "required": ["customerID", "emailaddress", "mailingAddress", "mobilePhoneNumber", "residencephonenumber", "residentialaddress"], "properties": {"customerID": {"type": "string", "example": "U735535(9)", "description": "The unique identification of a customer in his/her country.<br><br>maxLength: 35</br>", "allowEmptyValue": false}, "emailaddress": {"type": "string", "example": "<EMAIL>", "description": "Email Address<br></br>maxLength is 320", "allowEmptyValue": false}, "mailingAddress": {"type": "string", "example": "551 Austin Road,Tsim Sha Tsui,Kowloon", "description": "The mailingaddress is an address to which your mail can be sent.<br><br>maxLength: 1000</br>", "allowEmptyValue": false}, "mobilePhoneNumber": {"type": "string", "example": 64657884, "description": "Customer’s mobile phone number.<br><br>maxLength: 34</br>", "allowEmptyValue": false}, "residencephonenumber": {"type": "string", "example": 34828869, "description": "The phone number of the customer’s residential address in HongKong.<br><br>maxLength: 34</br>", "allowEmptyValue": false}, "residentialaddress": {"type": "string", "example": "779 Yi Chun Street, Sai Kung, New Territory", "description": "The Hong Kong address where the customer lives.<br><br>maxLength: 1000</br>", "allowEmptyValue": false}}, "title": "CustomerMaintenanceModel"}, "CustomerPersonalModel": {"type": "object", "required": ["customerIDType"], "properties": {"accommodation": {"type": "string", "example": "S", "description": "The ownership of the provided residential address. If it is set S, that means the accommodation is self owned.Possible values:S-Self Owned, P-Private property, H-Home ownership scheme,F-Friends/Relatives, U-Public Housing,R-Rented, Q-Quarters.", "allowEmptyValue": false}, "chinesename": {"type": "string", "example": "王研", "description": "The Chinese name of the customer shown in his/her ID card.", "allowEmptyValue": false}, "companyaddress": {"type": "string", "example": "228 Pok Fu Lam Road,Aberdeen,Hong Kong", "description": "The physical address of the company where the customer works.", "allowEmptyValue": false}, "companyphonenumber": {"type": "string", "example": ********, "description": "The phone number of the company where the customer works.", "allowEmptyValue": false}, "countrycode": {"type": "string", "example": "HK", "description": "countrycode", "allowEmptyValue": false}, "customerID": {"type": "string", "example": "U735535(9)", "description": "The unique identification of a customer in his/her country.", "allowEmptyValue": false}, "customerIDType": {"type": "string", "example": "I", "description": "CustomerID Type(I/P)", "allowEmptyValue": false}, "dateOfBirth": {"type": "string", "example": ************, "description": "The birthday of the customer.", "allowEmptyValue": false}, "education": {"type": "string", "example": "U", "description": "The education level of this customer. When it is set U, that means the customer education level is university.Possible values: M-Master,U-University, P-Post Secondary, S-Secondary, J-Primary/Junior,X.", "allowEmptyValue": false}, "employercompanyname": {"type": "string", "example": "Happy Realty", "description": "The name of the company for whom the customer works.", "allowEmptyValue": false}, "issueCountry": {"type": "string", "example": "China", "description": "The issue country of the customer’s ID. If it is set China, that means China is the issuing country for the customer’s ID.", "allowEmptyValue": false}, "mailingAddress": {"type": "string", "example": "551 Austin Road,Tsim Sha Tsui,Kowloon", "description": "The mailingaddress is an address to which your mail can be sent.", "allowEmptyValue": false}, "maritalstatus": {"type": "string", "example": "S", "description": "Marital status describes a person's relationship with a significant person.When it is set S, that means the customer is single.Possible values: S-Single, M-Married, D-Divorced,W.", "allowEmptyValue": false}, "mobilePhoneNumber": {"type": "string", "example": 64657884, "description": "Customer’s mobile phone number.", "allowEmptyValue": false}, "monthlysalary": {"type": "string", "example": 19000.0, "description": "The salary paid by his/her employer company every month.", "allowEmptyValue": false}, "occupation": {"type": "string", "example": "Software", "description": "The job or profession of the customer.", "allowEmptyValue": false}, "permanentresidencestatus": {"type": "string", "example": "Y", "description": "Whether the customer is a Hong Kong permanent resident or not.When it is set Y, it means yes. Possible values:Y-Yes, N-No.", "allowEmptyValue": false}, "position": {"type": "string", "example": "Senior Manager", "description": "The job title of this customer in his/her company. Possible value:CEO, Senior Manager, Consultant, Business Analyst, Chairman, staff, Engineer, Manager, Director.", "allowEmptyValue": false}, "residencephonenumber": {"type": "string", "example": 34828869, "description": "The phone number of the customer’s residential address in HongKong.", "allowEmptyValue": false}, "residentialaddress": {"type": "string", "example": "779 Yi Chun Street, Sai Kung, New Territory", "description": "The Hong Kong address where the customer lives.", "allowEmptyValue": false}, "wechatid": {"type": "string", "example": "W3456754", "description": "The customer’s WeChat ID", "allowEmptyValue": false}, "yearsofresidence": {"type": "integer", "format": "int32", "example": 3, "description": "The year of the customer living in Hong Kong.", "allowEmptyValue": false}, "yearsofservices": {"type": "integer", "format": "int32", "example": 2, "description": "The year of the customer working in this company.", "allowEmptyValue": false}}, "title": "CustomerPersonalModel"}, "CustomerTermDepositEnquiryModel": {"type": "object", "required": ["customerNumber", "index", "items"], "properties": {"customerNumber": {"type": "string", "example": "************", "description": "The unique ID generated when we create a new customer record  in the banking system.<br><br>maxLength: 25</br>", "allowEmptyValue": false}, "index": {"type": "number", "example": 0.0, "description": "an indicator to identify where your respone result will start to show.<br>maxLength: 20</br>", "allowEmptyValue": false}, "items": {"type": "number", "example": 10.0, "description": "the number of items you'd like to get in the response result.<br>maxLength: 20</br>", "allowEmptyValue": false}}, "title": "CustomerTermDepositEnquiryModel"}, "DepositAccountDetailsRetrievalModel": {"type": "object", "properties": {"account": {"$ref": "#/definitions/QueryAccountModel"}, "customer": {"$ref": "#/definitions/QueryCustomerMasterModel"}}, "title": "DepositAccountDetailsRetrievalModel"}, "DepositModel": {"type": "object", "required": ["accountNumber", "currencycode", "depositAmount"], "properties": {"accountNumber": {"type": "string", "example": "***********************", "description": "A unique number used to identify a bank account. In this API, only Saving/Current/Foreign Exchange Account is supported, <br></br>so please input an account number of the supported account types. <br></br>maxLength: 34</br>", "allowEmptyValue": false}, "currencycode": {"type": "string", "example": "HKD", "description": "The currency that the bank account is recorded in. In this API, for Saving/Current Account, <br></br>the currency code is HKD. For Foreign Exchange account, <br></br>the currecy code could be one of the followings: HKD,CNY,USD,AUD,EUR,CHF,CAD,GBP,JPY,NZD,SGD.<br></br> Please make sure you input the correct currency code before doing the deposit. <br><br>maxLength: 3</br>", "allowEmptyValue": false}, "depositAmount": {"type": "number", "example": 300.0, "description": "The amount that you’ll deposit in the bank account.<br><br>maxLength: 18</br>", "allowEmptyValue": false}}, "title": "DepositModel"}, "ExamineModel": {"type": "object", "required": ["serialNum", "status"], "properties": {"serialNum": {"type": "string", "example": "de66ad0ce3a24b0bb6027fec76f535d0", "description": "Serial number obtained when submitting the application.<br>maxLength: 100</br>", "allowEmptyValue": false}, "status": {"type": "string", "example": 1, "description": "Audit status code, Code 0 means not reviewed, code 1 means approved, code 2 means not approved.<br>maxLength: 1</br>", "allowEmptyValue": false}}, "title": "ExamineModel"}, "FatcaEntity": {"type": "object", "properties": {"accountNumber": {"type": "string", "description": "A term deposit account number.<br>maxLength: 34</br>", "allowEmptyValue": false}, "customerId": {"type": "string", "description": "Customer ID, which is unique in the system.<br>maxLength: 35</br>", "allowEmptyValue": false}, "id": {"type": "integer", "format": "int32"}, "nationality": {"type": "string", "description": "Customer's nationality.<br>maxLength: 20</br>", "allowEmptyValue": false}, "report": {"type": "string", "description": "The result of the report requested by the client.<br>maxLength: 3</br>", "allowEmptyValue": false}}, "title": "FatcaEntity"}, "FatcaModel": {"type": "object", "required": ["accountNumber", "customerIDType", "customerId"], "properties": {"accountNumber": {"type": "string", "example": "***********************", "description": "A current account number.<br><br>maxLength: 34</br>", "allowEmptyValue": false}, "customerIDType": {"type": "string", "example": "I", "description": " I：ID Card； P：Passport<br><br>maxLength: 1</br>", "allowEmptyValue": false}, "customerId": {"type": "string", "example": "SZ357325", "description": "The unique identification of a customer in his/her country.<br><br>maxLength: 35</br>", "allowEmptyValue": false}}, "title": "FatcaModel"}, "ListModel": {"type": "object", "properties": {"accountNumber": {"type": "string", "example": "***********************", "description": "A unique number used to identify a loan account.<br>maxLength: 34</br>", "allowEmptyValue": false}, "accountStatus": {"type": "string", "example": "A", "description": "Account status.<br>maxLength: 4</br>", "allowEmptyValue": false}}, "title": "ListModel"}, "PoliticianModel": {"type": "object", "required": ["chineseName"], "properties": {"chineseName": {"type": "string", "example": "杜德印", "description": "The unique identification of a customer in his/her country.<br><br>maxLength: 35</br>", "allowEmptyValue": false}}, "title": "PoliticianModel"}, "QueryAccountModel": {"type": "object", "properties": {"accountnumber": {"type": "string", "example": "********************", "description": "A unique number used to identify a deposit bank account.<br>maxLength: 34</br>", "allowEmptyValue": false}, "accountstatus": {"type": "string", "example": "Active", "description": "If it is set Active, that means the deposit account status is active. Possible values: Active,Closed.<br>maxLength: 10</br>", "allowEmptyValue": false}, "availablebalance": {"type": "number", "example": 300.0, "description": "shows the amount you can freely use to withdraw or transfer. <br>maxLength: 18</br>", "allowEmptyValue": false}, "chequebooksize": {"type": "integer", "format": "int64", "example": 50, "description": "The size of a cheque book.Possible values:0-50, 50-100<br>maxLength: 20</br>", "allowEmptyValue": false}, "chequebooktype": {"type": "string", "example": "S", "description": "When it is set S, that means the chequebooktype is Short.Possible values:S-Short,L-Long.<br>maxLength: 1</br>", "allowEmptyValue": false}, "currencycode": {"type": "string", "example": "HKD", "description": " The currency that the bank account is recorded in. Currently, for Saving/Current/Term Deposit/Loan/Fund/Stock/Precious Metal Account, only HKD is supported at this moment; for foreign exchange account, the following 11 currency is supported:  HKD,CNY,USD,AUD,EUR,CHF,CAD,GBP,JPY,NZD,SGD.<br>maxLength: 3</br>", "allowEmptyValue": false}, "fexbalance": {"type": "array", "description": " the balance of a foreign exchange account.<br>maxLength: 18</br>", "allowEmptyValue": false, "items": {"type": "object"}}, "holdingbalance": {"type": "number", "description": "shows the non-usable amount in your account.<br>maxLength: 18</br>", "allowEmptyValue": false}, "lastupdateddate": {"type": "string", "description": "The latest update date of the bank account.<br>maxLength: 20</br>", "allowEmptyValue": false}, "ledgerbalance": {"type": "number", "description": "shows the total amount in your account including Available Balance and Holding Balance.<br>maxLength: 18</br>", "allowEmptyValue": false}, "relaccountnumber": {"type": "string", "example": "********************", "description": "the Saving/Current account that is associated with a Term Deposit/Loan/Fund/Stock Account.<br>maxLength: 34</br>", "allowEmptyValue": false}}, "title": "QueryAccountModel"}, "QueryCustomerMasterModel": {"type": "object", "properties": {"accommodation": {"type": "string", "example": "S", "description": "The ownership of the provided residential address. If it is set S, that means the accommodation is self owned.Possible values:S-Self Owned, P-Private property, H-Home ownership scheme,F-Friends/Relatives, U-Public Housing,R-Rented, Q-Quarters.<br>maxLength: 1</br>", "allowEmptyValue": false}, "chinesename": {"type": "string", "example": "王研", "description": "The Chinese name of the customer shown in his/her ID card.<br>maxLength: 140</br>", "allowEmptyValue": false}, "customerName": {"type": "string", "example": "<PERSON>,Yan", "description": "First Name + Last Name.<br>maxLength: 140</br>", "allowEmptyValue": false}, "customerid": {"type": "string", "example": "U735535(9)", "description": "The unique identification of a customer in his/her country.<br>maxLength: 35</br>", "allowEmptyValue": false}, "customernumber": {"type": "string", "example": "*********", "description": "The unique ID generated when we create a new customer record  in the banking system.<br>maxLength: 25</br>", "allowEmptyValue": false}, "dateofbirth": {"type": "string", "example": ************, "description": "The birthday of the customer.<br>maxLength: 20</br>", "allowEmptyValue": false}, "education": {"type": "string", "example": "U", "description": "The education level of this customer. When it is set U, that means the customer education level is university.Possible values: U-University, P-Post Secondary, S-Secondary, J-Primary/Junior,O-Others.<br>maxLength: 1</br>", "allowEmptyValue": false}, "employercompanyname": {"type": "string", "example": "Happy Realty", "description": "The name of the company for whom the customer works.<br>maxLength: 140</br>", "allowEmptyValue": false}, "gender": {"type": "string", "example": "M", "description": "The gender of the customer. When it is set M, that means the customer is a Male.Possible values: M-Male, F-Female.<br>maxLength: 1</br>", "allowEmptyValue": false}, "issuecountry": {"type": "string", "example": "China", "description": "The issue country of the customer’s ID. If it is set China, that means China is the issuing country for the customer’s ID.<br>maxLength: 20</br>", "allowEmptyValue": false}, "maritalstatus": {"type": "string", "example": "S", "description": "Marital status describes a person's relationship with a significant person.When it is set S, that means the customer is single.Possible values: S-Single, M-Married, D-Divorced.<br>maxLength: 1</br>", "allowEmptyValue": false}, "monthlysalary": {"type": "string", "example": 19000.0, "description": "The salary paid by his/her employer company every month.<br>maxLength: 18</br>", "allowEmptyValue": false}, "nationality": {"type": "string", "example": "China", "description": "Nationality is a legal relationship between an individual person and a state.\tWhen it is set China, that means the customer is a Chinese. Possible values: China, Japan, German, France.<br>maxLength: 20</br>", "allowEmptyValue": false}, "occupation": {"type": "string", "example": "Software", "description": "The job or profession of the customer.<br>maxLength: 70</br>", "allowEmptyValue": false}, "position": {"type": "string", "example": "Senior Manager", "description": "The job title of this customer in his/her company. Possible value:CEO, Senior Manager, Consultant, Business Analyst, Chairman, staff, Engineer, Manager, Director.<br>maxLength: 500</br>", "allowEmptyValue": false}, "residentialaddress": {"type": "string", "example": "779 Yi Chun Street, Sai Kung, New Territory", "description": "The Hong Kong address where the customer lives.<br>maxLength: 1000</br>", "allowEmptyValue": false}}, "title": "QueryCustomerMasterModel"}, "QueryTdRateModel": {"type": "object", "required": ["tdAmount", "tdPeriod"], "properties": {"tdAmount": {"type": "number", "example": 20000.0, "description": "The amount that you’d like to save in the term deposit. tdAmount >= 10000<br><br>maxLength: 18</br>", "allowEmptyValue": false}, "tdPeriod": {"type": "string", "example": "1month", "description": "The duration that your money is deposited.<br></br> Possible values:1day, 1week, 2weeks, 1month, 2months, 3months, 6months, 9months, 12months<br><br>maxLength: 20</br>", "allowEmptyValue": false}}, "title": "QueryTdRateModel"}, "ResultUtil": {"type": "object", "properties": {"code": {"type": "string"}, "data": {"type": "object"}, "msg": {"type": "string"}}, "title": "ResultUtil"}, "ResultUtil«AllAccountModel»": {"type": "object", "properties": {"code": {"type": "string"}, "data": {"$ref": "#/definitions/AllAccountModel"}, "msg": {"type": "string"}}, "title": "ResultUtil«AllAccountModel»"}, "ResultUtil«BlackListEntity»": {"type": "object", "properties": {"code": {"type": "string"}, "data": {"$ref": "#/definitions/BlackListEntity"}, "msg": {"type": "string"}}, "title": "ResultUtil«BlackListEntity»"}, "ResultUtil«DepositAccountDetailsRetrievalModel»": {"type": "object", "properties": {"code": {"type": "string"}, "data": {"$ref": "#/definitions/DepositAccountDetailsRetrievalModel"}, "msg": {"type": "string"}}, "title": "ResultUtil«DepositAccountDetailsRetrievalModel»"}, "ResultUtil«FatcaEntity»": {"type": "object", "properties": {"code": {"type": "string"}, "data": {"$ref": "#/definitions/FatcaEntity"}, "msg": {"type": "string"}}, "title": "ResultUtil«FatcaEntity»"}, "ResultUtil«List«TermDepositDetailVo»»": {"type": "object", "properties": {"code": {"type": "string"}, "data": {"type": "array", "items": {"$ref": "#/definitions/TermDepositDetailVo"}}, "msg": {"type": "string"}}, "title": "ResultUtil«List«TermDepositDetailVo»»"}, "SanctionModel": {"type": "object", "required": ["customerIDType", "customerId"], "properties": {"customerIDType": {"type": "string", "example": "I", "description": "Customer ID Type.I:ID Card,P:Passport <br>maxLength: 1</br>", "allowEmptyValue": false}, "customerId": {"type": "string", "example": "U735535(9)", "description": "The unique identification of a customer in his/her country.<br><br>maxLength: 35</br>", "allowEmptyValue": false}}, "title": "SanctionModel"}, "TdNumberVo": {"type": "object", "properties": {"tdNumber": {"type": "string", "description": "Customer's fixed deposit number（DepositNumber）.<br>maxLength: 20</br>", "allowEmptyValue": false}}, "title": "TdNumberVo"}, "TermDepositDetailVo": {"type": "object", "properties": {"accountnumber": {"type": "string", "description": "An unique number used to identify a deposit bank account.<br>maxLength: 34</br>", "allowEmptyValue": false}, "createdate": {"type": "string", "description": "Created time.<br>maxLength: 20</br>", "allowEmptyValue": false}, "currencycode": {"type": "string", "description": "Currency type code.<br>maxLength: 3</br>", "allowEmptyValue": false}, "depositamount": {"type": "number", "description": "Fixed deposit amount.<br>maxLength: 18</br>", "allowEmptyValue": false}, "depositnumber": {"type": "string", "description": "Fixed deposit serial number.<br>maxLength: 20</br>", "allowEmptyValue": false}, "maturityamount": {"type": "number", "description": "The amount you can get after the maturity of the fixed deposit.<br>maxLength: 18</br>", "allowEmptyValue": false}, "maturitydate": {"type": "string", "description": "Time deposit expiry time.<br>maxLength: 20</br>", "allowEmptyValue": false}, "maturityinterest": {"type": "number", "description": "Interest earned after maturity.<br>maxLength: 18</br>", "allowEmptyValue": false}, "maturitystatus": {"type": "string", "description": "The status can be used to determine whether it is expired.<br>maxLength: 4</br>", "allowEmptyValue": false}, "systemdate": {"type": "string", "description": "System date.<br>maxLength: 20</br>", "allowEmptyValue": false}, "terminterestrate": {"type": "number", "description": "Fixed deposit rate.<br>maxLength: 11</br>", "allowEmptyValue": false}, "termperiod": {"type": "string", "description": "Term of deposit.<br>maxLength: 20</br>", "allowEmptyValue": false}}, "title": "TermDepositDetailVo"}, "TermDepositDrawDownModel": {"type": "object", "required": ["debitAccountNumber", "tdAccountNumber", "tdNumber"], "properties": {"debitAccountNumber": {"type": "string", "example": "***********************", "description": "A unique Saving/Current account number,to which your money will be transferred from a term deposit account.<br><br>maxLength: 34</br>", "allowEmptyValue": false}, "tdAccountNumber": {"type": "string", "example": "***********************", "description": "The term deposit account where your money is deposited.<br><br>maxLength: 34</br>", "allowEmptyValue": false}, "tdNumber": {"type": "string", "example": "*********", "description": "A unique number that identifies your each term deposit. <br></br>A TD Number is generated after you call term deposit application API successfully.<br><br>maxLength: 20</br>", "allowEmptyValue": false}}, "title": "TermDepositDrawDownModel"}, "TermDepositEnquiryModel": {"type": "object", "required": ["accountnumber", "tdnumber"], "properties": {"accountnumber": {"type": "string", "example": "***********************", "description": "A unique Term Deposit account number.<br><br>maxLength: 34</br>", "allowEmptyValue": false}, "tdnumber": {"type": "string", "example": "*********", "description": "A unique number that identifies your each term deposit.<br><br>maxLength: 20</br>", "allowEmptyValue": false}}, "title": "TermDepositEnquiryModel"}, "TermDepositMasterModel": {"type": "object", "required": ["debitAccountNumber", "tdAccountNumber", "tdAmount", "tdCcy", "tdContractPeriod"], "properties": {"debitAccountNumber": {"type": "string", "example": "***********************", "description": "A unique Saving/Current account number,from which you’d like to transfer money.<br><br>maxLength: 34</br>", "allowEmptyValue": false}, "tdAccountNumber": {"type": "string", "example": "***********************", "description": "The account number of a  term deposit account, to which your money will be transferred and deposited.<br><br>maxLength: 34</br>", "allowEmptyValue": false}, "tdAmount": {"type": "number", "example": 20000.0, "description": "The amount that you’d like to save in the term deposit. tdAmount >= 10000<br><br>maxLength: 18</br>", "allowEmptyValue": false}, "tdCcy": {"type": "string", "example": "HKD", "description": "The currency your deposit is recorded in.<br></br>Possible values:HKD<br><br>maxLength: 3</br>", "allowEmptyValue": false}, "tdContractPeriod": {"type": "string", "example": "1month", "description": "The duration that your money is deposited.<br></br> Possible values:1day, 1week, 2weeks, 1month, 2months, 3months, 6months, 9months, 12months<br><br>maxLength: 20</br>", "allowEmptyValue": false}}, "title": "TermDepositMasterModel"}, "TermDepositRateVo": {"type": "object", "properties": {"depositrange": {"type": "string", "description": "deposit range.<br>maxLength: 50</br>", "allowEmptyValue": false}, "id": {"type": "integer", "format": "int32", "description": "id.<br>maxLength: 20</br>", "allowEmptyValue": false}, "tdinterestrate": {"type": "number", "description": "Term of deposit interest rate.<br>maxLength: 20</br>", "allowEmptyValue": false}, "tdperiod": {"type": "string", "description": "Term of deposit.<br>maxLength: 20</br>", "allowEmptyValue": false}}, "title": "TermDepositRateVo"}, "TermDepositRenewalModel": {"type": "object", "required": ["tdRenewalPeriod", "tdaccountnumber", "tdnumber"], "properties": {"tdRenewalPeriod": {"type": "string", "example": "1month", "description": "The duration that your money is deposited.<br></br> Possible values:1day, 1week, 2weeks, 1month, 2months, 3months, 6months, 9months, 12months<br><br>maxLength: 20</br>", "allowEmptyValue": false}, "tdaccountnumber": {"type": "string", "example": "***********************", "description": "The term deposit account number where you made term deposits.<br><br>maxLength: 34</br>", "allowEmptyValue": false}, "tdnumber": {"type": "string", "example": "*********", "description": "A unique number that identifies your each term deposit.<br><br>maxLength: 20</br>", "allowEmptyValue": false}}, "title": "TermDepositRenewalModel"}, "TransactionLogVo": {"type": "object", "properties": {"accountnumber": {"type": "string", "example": "***********************", "description": "The account number.<br>maxLength: 34</br>", "allowEmptyValue": false}, "actualbalamt": {"type": "number", "example": 25000.0, "description": "The final account balance after the transaction happened.<br>maxLength: 18</br>", "allowEmptyValue": false}, "ccy": {"type": "string", "example": "HKD", "description": "Transaction currency code.<br>maxLength: 3</br>", "allowEmptyValue": false}, "channel": {"type": "string", "example": "API", "description": "The transaction channel.<br>maxLength: 3</br>", "allowEmptyValue": false}, "channelid": {"type": "string", "example": 123, "description": "The unique id that identifies a specific transaction terminal.<br>maxLength: 50</br>", "allowEmptyValue": false}, "crdrmaintind": {"type": "string", "example": "D", "description": "A unique sign to show whether the transaction is deposit or withdraw. Sample: D (means Deposit) Sample: C (means Withdraw)<br>maxLength: 1</br>", "allowEmptyValue": false}, "previousbalamt": {"type": "number", "example": 30000.0, "description": "The initial account balance before the transaction happened. <br>maxLength: 18</br>", "allowEmptyValue": false}, "refaccountnumber": {"type": "string", "example": "********************", "description": "The relevant account number with whom the transaction happened.<br>maxLength: 34</br>", "allowEmptyValue": false}, "tfrseqno": {"type": "string", "example": "BKCHCNBJ40002671927495651", "description": "The transfer trading flow number.<br>maxLength: 70</br>", "allowEmptyValue": false}, "tranamt": {"type": "number", "example": 300.0, "description": "Transaction amount. <br>maxLength: 18</br>", "allowEmptyValue": false}, "trandate": {"type": "number", "description": "The date the transaction happens.<br>maxLength: 20</br>", "allowEmptyValue": false}, "trandesc": {"type": "string", "example": "Insurance payment", "description": "Transaction description.<br>maxLength: 140</br>", "allowEmptyValue": false}, "transeq": {"type": "string", "example": "20190228091829DAPI000003HK0001001", "description": "The transaction ID.<br>maxLength: 70</br>", "allowEmptyValue": false}, "trantype": {"type": "string", "example": "0001", "description": "The transaction type.Possible Values:0001 - TD Application 0002 - TD Drawdown 0003 - TD Renewal 0004 - Deposit 0005 – Transfer 0006 - Withdraw 0007 - Foreign Buy 0008 - Foreign Sell<br>maxLength: 6</br>", "allowEmptyValue": false}}, "title": "TransactionLogVo"}, "TransferModel": {"type": "object", "required": ["transferAmount", "transferInAccountNumber", "transferOutAccountNumber"], "properties": {"transferAmount": {"type": "number", "example": 300.0, "description": "The amount that is transferred between two bank accounts.<br><br>maxLength: 18</br>", "allowEmptyValue": false}, "transferInAccountNumber": {"type": "string", "example": "***********************", "description": "A Saving/Current account, to which you’d like to transfer money.<br><br>maxLength: 34</br>", "allowEmptyValue": false}, "transferOutAccountNumber": {"type": "string", "example": "***********************", "description": "A Saving/Current account, from which you’d like to transfer money out.<br><br>maxLength: 34</br>", "allowEmptyValue": false}}, "title": "TransferModel"}, "WithDrawalModel": {"type": "object", "required": ["accountNumber", "currencycode", "withDrawalAmount"], "properties": {"accountNumber": {"type": "string", "example": "***********************", "description": "In this API, only Saving/Current/Foreign Exchange Account is supported. <br></br>Please input an account number of the supported account types. <br><br>maxLength: 34</br>", "allowEmptyValue": false}, "currencycode": {"type": "string", "example": "HKD", "description": "The currency that the bank account is recorded in. In this API, for Saving/Current Account, the currency code is HKD. <br></br>For Foreign Exchange account, the currecy code could be one of the followings: HKD,CNY,USD,AUD,EUR,CHF,CAD,GBP,JPY,NZD,SGD. <br></br>Please make sure you input the correct currency code before doing the withdrawal. <br><br>maxLength: 3</br> ", "allowEmptyValue": false}, "withDrawalAmount": {"type": "number", "example": 10000.0, "description": "The amount that you’d like to withdraw from your bank account.<br><br>maxLength: 18</br>", "allowEmptyValue": false}}, "title": "WithDrawalModel"}}}