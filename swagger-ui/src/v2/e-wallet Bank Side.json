{"swagger": "2.0", "info": {"description": "APIs here are all about E-wallet service. Before calling these APIs, please make sure you’ve learnt the business logic between these APIs.", "version": "1.0", "title": "E-wallet Service", "contact": {"name": "<EMAIL>"}}, "schemes": ["https"], "host": "simnectz.hsu.edu.hk/lbsgateway/ewallet-experience", "basePath": "/", "tags": [{"name": "E-wallet Account", "description": "The operations related to the e-wallet account include binding/unbinding bank card, wallet withdrawal and user information query."}, {"name": "My wallet’s bank cards", "description": "Wallet-related bank card services, including bound bank card list enquiry  and the bank card balance enquiry."}, {"name": "Transactions", "description": "E-Wallet transaction, includeing merchant list, merchant information and payment."}], "paths": {"/customer/customerID": {"post": {"tags": ["E-wallet Account"], "summary": "Query the user information through the set customer ID", "description": "Query the user information through the set customer ID", "operationId": "customerIdUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.<br>maxLength: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "Message ID in 128 bit random UUID format generated uniquely for every request by F/E side.<br>maxLength: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "client_id generated during consumer onboarding. <br>maxLength: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "vo", "description": "vo", "required": true, "schema": {"$ref": "#/definitions/CustomerIdVo"}}], "responses": {"200": {"description": "Query completed successfully.(Returned By Get)", "schema": {"$ref": "#/definitions/EwalletCustomerMaster"}}, "201": {"description": "Normal execution. The request has succeeded. (Returned By Post)", "schema": {"$ref": "#/definitions/ResultUtil"}}, "403": {"description": "Token has incorrect scope or a security policy was violated. Action: Please check whether you’re using the right token with the legal authorized user account."}, "404": {"description": "The requested  account does not exist.Action: Please make sure the account number and account type you’re inputting are correct."}, "500": {"description": "Something went wrong on the API gateway or micro-service. Action: check your network and try again later."}}}}, "/finance/accountUnbinding": {"post": {"tags": ["E-wallet Account"], "summary": "Through this API, you can unbind the card that has been bound", "description": "Through this API, you can unbind the card that has been bound", "operationId": "accountUnbindingUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.<br>maxLength: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "Message ID in 128 bit random UUID format generated uniquely for every request by F/E side.<br>maxLength: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "client_id generated during consumer onboarding. <br>maxLength: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "validationModel", "description": "validationModel", "required": true, "schema": {"$ref": "#/definitions/ValidationModel"}}], "responses": {"200": {"description": "Query completed successfully.(Returned By Get)", "schema": {"$ref": "#/definitions/ResultUtil"}}, "201": {"description": "Normal execution. The request has succeeded. (Returned By Post)", "schema": {"$ref": "#/definitions/ResultUtil"}}, "403": {"description": "Token has incorrect scope or a security policy was violated. Action: Please check whether you’re using the right token with the legal authorized user account."}, "404": {"description": "The requested  account does not exist.Action: Please make sure the account number and account type you’re inputting are correct."}, "500": {"description": "Something went wrong on the API gateway or micro-service. Action: check your network and try again later."}}}}, "/finance/accountValidation": {"post": {"tags": ["E-wallet Account"], "summary": "This API is designed to make account validation and bind the account into e-wallet system.", "description": "Only Saving/Current and Credit Card account is supported in this API. Before making a E-wallet top up, you may need to call this API.", "operationId": "accountValidationUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.<br>maxLength: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "Message ID in 128 bit random UUID format generated uniquely for every request by F/E side.<br>maxLength: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "client_id generated during consumer onboarding. <br>maxLength: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "validationModel", "description": "validationModel", "required": true, "schema": {"$ref": "#/definitions/ValidationModel"}}], "responses": {"200": {"description": "Query completed successfully.(Returned By Get)", "schema": {"$ref": "#/definitions/ResultUtil"}}, "201": {"description": "Normal execution. The request has succeeded. (Returned By Post)", "schema": {"$ref": "#/definitions/ResultUtil"}}, "403": {"description": "Token has incorrect scope or a security policy was violated. Action: Please check whether you’re using the right token with the legal authorized user account."}, "404": {"description": "The requested  account does not exist.Action: Please make sure the account number and account type you’re inputting are correct."}, "500": {"description": "Something went wrong on the API gateway or micro-service. Action: check your network and try again later."}}}}, "/finance/accounts/customerNumber": {"post": {"tags": ["My wallet’s bank cards"], "summary": "Query the binding card information of the specified user", "description": "Query the binding card information of the specified user", "operationId": "customerNumberUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.<br>maxLength: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "Message ID in 128 bit random UUID format generated uniquely for every request by F/E side.<br>maxLength: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "client_id generated during consumer onboarding. <br>maxLength: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "vo", "description": "vo", "required": true, "schema": {"$ref": "#/definitions/CustomerNumberVo"}}], "responses": {"200": {"description": "Query completed successfully.(Returned By Get)", "schema": {"$ref": "#/definitions/BindCardInfoVo"}}, "201": {"description": "Normal execution. The request has succeeded. (Returned By Post)", "schema": {"$ref": "#/definitions/ResultUtil"}}, "403": {"description": "Token has incorrect scope or a security policy was violated. Action: Please check whether you’re using the right token with the legal authorized user account."}, "404": {"description": "The requested  account does not exist.Action: Please make sure the account number and account type you’re inputting are correct."}, "500": {"description": "Something went wrong on the API gateway or micro-service. Action: check your network and try again later."}}}}, "/finance/authorize": {"post": {"tags": ["E-wallet Account"], "summary": "authorize", "operationId": "authorizeUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.<br>maxLength: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "Message ID in 128 bit random UUID format generated uniquely for every request by F/E side.<br>maxLength: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "client_id generated during consumer onboarding. <br>maxLength: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "eServiceAuthorizeModel", "description": "eServiceAuthorizeModel", "required": true, "schema": {"$ref": "#/definitions/EServiceAuthorizeModel"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ResultUtil«object»"}}}}}, "/finance/bankCardBalance": {"post": {"tags": ["My wallet’s bank cards"], "summary": "cardBalanceEnquiry", "operationId": "cardBalanceEnquiryUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.<br>maxLength: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "Message ID in 128 bit random UUID format generated uniquely for every request by F/E side.<br>maxLength: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "client_id generated during consumer onboarding. <br>maxLength: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "cardBalanceEnquiry", "description": "cardBalanceEnquiry", "required": true, "schema": {"$ref": "#/definitions/CardBalanceEnquiryModel"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ResultUtil«object»"}}}}}, "/finance/cashing": {"post": {"tags": ["E-wallet Account"], "summary": "Through this API, you can withdraw the specified amount to the card", "description": "Through this API, you can withdraw the specified amount to the card", "operationId": "cashingUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.<br>maxLength: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "Message ID in 128 bit random UUID format generated uniquely for every request by F/E side.<br>maxLength: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "client_id generated during consumer onboarding. <br>maxLength: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "vo", "description": "vo", "required": true, "schema": {"$ref": "#/definitions/CashingVo"}}], "responses": {"200": {"description": "Query completed successfully.(Returned By Get)", "schema": {"$ref": "#/definitions/ResultUtil«string»"}}, "201": {"description": "Normal execution. The request has succeeded. (Returned By Post)", "schema": {"$ref": "#/definitions/ResultUtil«string»"}}, "403": {"description": "Token has incorrect scope or a security policy was violated. Action: Please check whether you’re using the right token with the legal authorized user account."}, "404": {"description": "The requested  account does not exist.Action: Please make sure the account number and account type you’re inputting are correct."}, "500": {"description": "Something went wrong on the API gateway or micro-service. Action: check your network and try again later."}}}}, "/finance/merchantDetails": {"post": {"tags": ["Transactions"], "summary": "merchantDetailsEnquiry", "operationId": "merchantDetailsEnquiryUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.<br>maxLength: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "Message ID in 128 bit random UUID format generated uniquely for every request by F/E side.<br>maxLength: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "client_id generated during consumer onboarding. <br>maxLength: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "merchantDetailsEnquiry", "description": "merchantDetailsEnquiry", "required": true, "schema": {"$ref": "#/definitions/MerchantDetailsEnquiryModel"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ResultUtil«object»"}}}}}, "/finance/merchantQR": {"post": {"tags": ["Transactions"], "summary": "This API is designed to generate a QR code with a merchant detailed information. ", "description": "Before making a payment with your e-wallet, you may need to call this API to retrieve merchant information. ", "operationId": "merchantDetailsUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.<br>maxLength: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "Message ID in 128 bit random UUID format generated uniquely for every request by F/E side.<br>maxLength: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "client_id generated during consumer onboarding. <br>maxLength: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "merchantVo", "description": "merchantVo", "required": true, "schema": {"$ref": "#/definitions/MerchantInfoVo"}}], "responses": {"200": {"description": "Query completed successfully.(Returned By Get)", "schema": {"$ref": "#/definitions/ResultUtil"}}, "201": {"description": "Normal execution. The request has succeeded. (Returned By Post)", "schema": {"$ref": "#/definitions/ResultUtil"}}, "403": {"description": "Token has incorrect scope or a security policy was violated. Action: Please check whether you’re using the right token with the legal authorized user account."}, "404": {"description": "The requested  account does not exist.Action: Please make sure the account number and account type you’re inputting are correct."}, "500": {"description": "Something went wrong on the API gateway or micro-service. Action: check your network and try again later."}}}}, "/finance/merchants": {"post": {"tags": ["Transactions"], "summary": "Get detailed information of all merchants.", "description": "Get detailed information of all merchants.", "operationId": "merchantsUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.<br>maxLength: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "Message ID in 128 bit random UUID format generated uniquely for every request by F/E side.<br>maxLength: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "client_id generated during consumer onboarding. <br>maxLength: 50</br>", "required": true, "type": "string", "default": "devin"}], "responses": {"200": {"description": "Query completed successfully.(Returned By Get)", "schema": {"$ref": "#/definitions/ResultUtil"}}, "201": {"description": "Normal execution. The request has succeeded. (Returned By Post)", "schema": {"$ref": "#/definitions/ResultUtil"}}, "403": {"description": "Token has incorrect scope or a security policy was violated. Action: Please check whether you’re using the right token with the legal authorized user account."}, "404": {"description": "The requested  account does not exist.Action: Please make sure the account number and account type you’re inputting are correct."}, "500": {"description": "Something went wrong on the API gateway or micro-service. Action: check your network and try again later."}}}}, "/finance/topUp": {"post": {"tags": ["E-wallet Account"], "summary": "This API is designed to top up an e-wallet.", "description": "After your bank account is validated successfully, you may need to call this API to top up your e-wallet, which makes sure your e-wallet has sufficient fund to make a payment.", "operationId": "toUpUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.<br>maxLength: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "Message ID in 128 bit random UUID format generated uniquely for every request by F/E side.<br>maxLength: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "client_id generated during consumer onboarding. <br>maxLength: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "toUpModel", "description": "toUpModel", "required": true, "schema": {"$ref": "#/definitions/ToUpModel"}}], "responses": {"200": {"description": "Query completed successfully.(Returned By Get)", "schema": {"$ref": "#/definitions/ResultUtil"}}, "201": {"description": "Normal execution. The request has succeeded. (Returned By Post)", "schema": {"$ref": "#/definitions/ResultUtil"}}, "403": {"description": "Token has incorrect scope or a security policy was violated. Action: Please check whether you’re using the right token with the legal authorized user account."}, "404": {"description": "The requested  account does not exist.Action: Please make sure the account number and account type you’re inputting are correct."}, "500": {"description": "Something went wrong on the API gateway or micro-service. Action: check your network and try again later."}}}}, "/finance/transferToMerchant": {"post": {"tags": ["Transactions"], "summary": "This API is designed to make a payment to a merchant.", "description": "Before calling this API, you may need to call other APIs to make sure your e-wallet has enough money and scan the QR code of the merchant to retrieve the merchant account in the bank system. ", "operationId": "transferToMerchantUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.<br>maxLength: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "Message ID in 128 bit random UUID format generated uniquely for every request by F/E side.<br>maxLength: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "client_id generated during consumer onboarding. <br>maxLength: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "merchantModel", "description": "merchantModel", "required": true, "schema": {"$ref": "#/definitions/MerchantModel"}}], "responses": {"200": {"description": "Query completed successfully.(Returned By Get)", "schema": {"$ref": "#/definitions/ResultUtil"}}, "201": {"description": "Normal execution. The request has succeeded. (Returned By Post)", "schema": {"$ref": "#/definitions/ResultUtil"}}, "403": {"description": "Token has incorrect scope or a security policy was violated. Action: Please check whether you’re using the right token with the legal authorized user account."}, "404": {"description": "The requested  account does not exist.Action: Please make sure the account number and account type you’re inputting are correct."}, "500": {"description": "Something went wrong on the API gateway or micro-service. Action: check your network and try again later."}}}}}, "definitions": {"AccountNumberVo": {"type": "object", "properties": {"accountNumber": {"type": "string", "example": "HK720001************001", "description": "The number of a bank account. Supported account types: Saving Account, Current Account and Credit Card Account.<br></br> maxLength: 34", "allowEmptyValue": false}}, "title": "AccountNumberVo"}, "BindCardInfoVo": {"type": "object", "properties": {"creditCard": {"type": "array", "items": {"$ref": "#/definitions/AccountNumberVo"}}, "current": {"type": "array", "items": {"$ref": "#/definitions/AccountNumberVo"}}, "saving": {"type": "array", "items": {"$ref": "#/definitions/AccountNumberVo"}}}, "title": "BindCardInfoVo"}, "CardBalanceEnquiryModel": {"type": "object", "required": ["accountNumber", "walletId"], "properties": {"accountNumber": {"type": "string", "example": "HK720001************001", "description": "The number of a bank account. Supported account types: Saving Account, Current Account and Credit Card Account.<br></br> maxLength: 34", "allowEmptyValue": false}, "walletId": {"type": "string", "example": "************************", "description": "An unique ewallet account Id of the current bank customer, which is generated when registering in the ewallet system.<br></br> maxLength: 64", "allowEmptyValue": false}}, "title": "CardBalanceEnquiryModel"}, "CashingVo": {"type": "object", "required": ["amount", "ccy", "walletId"], "properties": {"accountNumber": {"type": "string", "example": "HK720001************001", "description": "The number of a bank account. Supported account types: Saving Account, Current Account and Credit Card Account.<br></br> maxLength: 34", "allowEmptyValue": false}, "amount": {"type": "number", "example": 100, "description": "Transaction amount.<br>maxLength: 18</br>", "allowEmptyValue": false}, "ccy": {"type": "string", "example": "HKD", "description": "Transaction currency code.Possible value: HKD<br></br> maxLength: 3", "allowEmptyValue": false}, "walletId": {"type": "string", "example": "************************", "description": "An unique ewallet account Id of the current bank customer, which is generated when registering in the ewallet system.<br></br> maxLength: 64", "allowEmptyValue": false}}, "title": "CashingVo"}, "CustomerIdVo": {"type": "object", "required": ["customerId"], "properties": {"customerId": {"type": "string", "example": "U735535(9)", "description": "User ID number.<br></br> maxLength: 25", "allowEmptyValue": false}}, "title": "CustomerIdVo"}, "CustomerNumberVo": {"type": "object", "required": ["customerNumber", "walletId"], "properties": {"customerNumber": {"type": "string", "example": "************", "description": "Bank customer number. After the bank customer is successfully created, the system will generate a unique number for that customer.<br></br> maxLength: 25", "allowEmptyValue": false}, "walletId": {"type": "string", "example": "************************", "description": "An unique ewallet account Id of the current bank customer, which is generated when registering in the ewallet system.<br></br> maxLength: 64", "allowEmptyValue": false}}, "title": "CustomerNumberVo"}, "EServiceAuthorizeModel": {"type": "object", "required": ["customerId", "eWalletCompany", "walletId"], "properties": {"customerId": {"type": "string", "example": "U735535(9)", "description": "User ID number.<br></br> maxLength: 25", "allowEmptyValue": false}, "eWalletCompany": {"type": "string", "example": "e-Wallet", "description": "Third party ewallet enterprises.<br></br> maxLength: 25", "allowEmptyValue": false}, "walletId": {"type": "string", "example": "************************", "description": "An unique ewallet account Id of the current bank customer, which is generated when registering in the ewallet system.<br></br> maxLength: 64", "allowEmptyValue": false}}, "title": "EServiceAuthorizeModel"}, "EwalletCustomerMaster": {"type": "object", "properties": {"chineseName": {"type": "string"}, "createTime": {"type": "number"}, "customerId": {"type": "string"}, "customerNumber": {"type": "string"}, "firstName": {"type": "string"}, "id": {"type": "string"}, "issueCountry": {"type": "string"}, "lastName": {"type": "string"}, "phone": {"type": "string"}}, "title": "EwalletCustomerMaster"}, "MerchantDetailsEnquiryModel": {"type": "object", "required": ["merchantNumber"], "properties": {"merchantNumber": {"type": "string", "example": "******************", "description": "A unique number to identify a merchant in the ewallet system.<br></br> MaxLength:34", "allowEmptyValue": false}}, "title": "MerchantDetailsEnquiryModel"}, "MerchantInfoVo": {"type": "object", "required": ["merchantNumber"], "properties": {"merchantNumber": {"type": "string", "example": "******************", "description": "A unique number to identify a merchant in the ewallet system.<br></br> MaxLength:34", "allowEmptyValue": false}}, "title": "MerchantInfoVo"}, "MerchantModel": {"type": "object", "required": ["accountNumber", "accountType", "merchantNumber", "transactionAmount", "transactionCcy", "walletId"], "properties": {"accountNumber": {"type": "string", "example": "HK720001************001", "description": "The number of a bank account. Supported account types: Saving Account, Current Account and Credit Card Account.<br></br> maxLength: 34", "allowEmptyValue": false}, "accountType": {"type": "string", "example": "SAVI", "description": "The type of bank account that the customer has bound.<br></br>Savings account ending in SAVI-001<br>Current account ending in CURR-002<br>CRED - Credit card account.</br>maxLength: 4", "allowEmptyValue": false}, "merchantNumber": {"type": "string", "example": "******************", "description": "A unique number to identify a merchant in the ewallet system.<br></br> maxLength: 34", "allowEmptyValue": false}, "transactionAmount": {"type": "string", "example": 100, "description": "The payment amount in this transaction.<br></br> maxLength: 18", "allowEmptyValue": false}, "transactionCcy": {"type": "string", "example": "HKD", "description": "Transaction currency code.Possible value: HKD.<br></br> maxLength: 3", "allowEmptyValue": false}, "walletId": {"type": "string", "example": "************************", "description": "An unique ewallet account Id of the current bank customer, which is generated when registering in the ewallet system.<br></br> maxLength: 64", "allowEmptyValue": false}}, "title": "MerchantModel"}, "ResultUtil": {"type": "object", "properties": {"code": {"type": "string"}, "data": {"type": "object"}, "msg": {"type": "string"}}, "title": "ResultUtil"}, "ResultUtil«object»": {"type": "object", "properties": {"code": {"type": "string"}, "data": {"type": "object"}, "msg": {"type": "string"}}, "title": "ResultUtil«object»"}, "ResultUtil«string»": {"type": "object", "properties": {"code": {"type": "string"}, "data": {"type": "string"}, "msg": {"type": "string"}}, "title": "ResultUtil«string»"}, "ToUpModel": {"type": "object", "required": ["accountNumber", "amount", "ccy", "walletID"], "properties": {"accountNumber": {"type": "string", "example": "HK720001************001", "description": "The number of a bank account. Supported account types: Saving Account, Current Account and Credit Card Account.<br></br> maxLength: 34", "allowEmptyValue": false}, "amount": {"type": "string", "example": 100, "description": "Transaction amount.<br></br> maxLength: 18", "allowEmptyValue": false}, "ccy": {"type": "string", "example": "HKD", "description": "Transaction currency code.Possible value: HKD.<br></br> maxLength: 3", "allowEmptyValue": false}, "walletID": {"type": "string", "example": "************************", "description": "An unique ewallet account Id of the current bank customer, which is generated when registering in the ewallet system.<br></br> maxLength: 64", "allowEmptyValue": false}}, "title": "ToUpModel"}, "ValidationModel": {"type": "object", "required": ["accountNumber", "customerNumber", "walletId"], "properties": {"accountNumber": {"type": "string", "example": "HK720001************001", "description": "The number of a bank account. Supported account types: Saving Account, Current Account and Credit Card Account.<br></br> maxLength: 34", "allowEmptyValue": false}, "customerNumber": {"type": "string", "example": "************", "description": "Bank customer number. After the bank customer is successfully created, the system will generate a unique number for that customer.<br></br> maxLength: 25", "allowEmptyValue": false}, "walletId": {"type": "string", "example": "************************", "description": "An unique ewallet account Id of the current bank customer, which is generated when registering in the ewallet system.<br></br> maxLength: 64", "allowEmptyValue": false}}, "title": "ValidationModel"}}}