{"swagger": "2.0", "info": {"description": "APIs here are all about payment service. Before calling these APIs, please make sure you’ve learnt the business logic between these APIs.", "version": "1.0", "title": "Payment Service", "contact": {"name": "<EMAIL>"}}, "schemes": ["https"], "host": "simnectz.hsu.edu.hk/lbsgateway/payment-experience", "basePath": "/", "tags": [{"name": "Payee Management", "description": "Define the payee list for payment processing."}, {"name": "Transaction", "description": "Payment transaction."}], "paths": {"/payment/billPayeeDelete": {"post": {"tags": ["Payee Management"], "summary": "This API is to delete payee information from customer payee list.", "description": "Before calling this API, you may need to call /payment/billPayeeSetup API to build the payee list first. ", "operationId": "billPayeeDeleteUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.<br>maxLength: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "Message ID in 128 bit random UUID format generated uniquely for every request by F/E side.<br>maxLength: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "client_id generated during consumer onboarding. <br>maxLength: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "deleteBillPayeeModel", "description": "deleteBillPayeeModel", "required": true, "schema": {"$ref": "#/definitions/DeleteBillPayeeModel"}}], "responses": {"200": {"description": "Query completed successfully.(Returned By Get)", "schema": {"$ref": "#/definitions/ResultUtil"}}, "201": {"description": "Normal execution. The request has succeeded. (Returned By Post)", "schema": {"$ref": "#/definitions/ResultUtil"}}, "403": {"description": "Token has incorrect scope or a security policy was violated. Action: Please check whether you’re using the right token with the legal authorized user account."}, "404": {"description": "The requested customerAccountNumber  does not exist.Action: Please make sure the account number and account type you’re inputting are correct."}, "500": {"description": "Something went wrong on the API gateway or micro-service. Action: check your network and try again later."}}}}, "/payment/billPayeeSetup": {"post": {"tags": ["Payee Management"], "summary": "This API is to build payee list for customers. ", "description": "Before calling this API, you may need to call /payment/payeeInfoListRetrieval API to retrieve the payee id in a specific category.", "operationId": "billPayeeSetupUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.<br>maxLength: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "Message ID in 128 bit random UUID format generated uniquely for every request by F/E side.<br>maxLength: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "client_id generated during consumer onboarding. <br>maxLength: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "setBillPayeeModel", "description": "setBillPayeeModel", "required": true, "schema": {"$ref": "#/definitions/SetBillPayeeModel"}}], "responses": {"200": {"description": "Query completed successfully.(Returned By Get)", "schema": {"$ref": "#/definitions/ResultUtil"}}, "201": {"description": "Normal execution. The request has succeeded. (Returned By Post)", "schema": {"$ref": "#/definitions/ResultUtil"}}, "403": {"description": "Token has incorrect scope or a security policy was violated. Action: Please check whether you’re using the right token with the legal authorized user account."}, "404": {"description": "The requested customerAccountNumber  does not exist.Action: Please make sure the account number and account type you’re inputting are correct."}, "500": {"description": "Something went wrong on the API gateway or micro-service. Action: check your network and try again later."}}}}, "/payment/customerPayeeRetrieval": {"get": {"tags": ["Payee Management"], "summary": "This API is designed to allow customers to retrieve their payee list. ", "description": "Before calling this API, you may need to call /payment/billPayeeSetup to build payee list for this customer. ", "operationId": "getRetrieveCustomerPayeeListUsingGET", "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.<br>maxLength: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "Message ID in 128 bit random UUID format generated uniquely for every request by F/E side.<br>maxLength: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "client_id generated during consumer onboarding. <br>maxLength: 50</br>", "required": true, "type": "string", "default": "devin"}], "responses": {"200": {"description": "Query completed successfully.(Returned By Get)", "schema": {"$ref": "#/definitions/ResultUtil«List«CustomerPayeeModel»»"}}, "201": {"description": "Normal execution. The request has succeeded. (Returned By Post)", "schema": {"$ref": "#/definitions/ResultUtil«List«CustomerPayeeModel»»"}}, "403": {"description": "Token has incorrect scope or a security policy was violated. Action: Please check whether you’re using the right token with the legal authorized user account."}, "404": {"description": "The requested customerAccountNumber  does not exist.Action: Please make sure the account number and account type you’re inputting are correct."}, "500": {"description": "Something went wrong on the API gateway or micro-service. Action: check your network and try again later."}}}}, "/payment/payeeCategoryList": {"get": {"tags": ["Payee Management"], "summary": "This API is to retrieve payee category list.", "description": "Before calling any other APIs, you may need to call this API to get payee category list, then you'll know what type of payment is supported in this service.", "operationId": "retrievalPayeeCategoryListUsingGET", "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.<br>maxLength: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "Message ID in 128 bit random UUID format generated uniquely for every request by F/E side.<br>maxLength: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "client_id generated during consumer onboarding. <br>maxLength: 50</br>", "required": true, "type": "string", "default": "devin"}], "responses": {"200": {"description": "Query completed successfully.(Returned By Get)", "schema": {"$ref": "#/definitions/ResultUtil«List«PayeeCategoryInformationModel»»"}}, "201": {"description": "Normal execution. The request has succeeded. (Returned By Post)", "schema": {"$ref": "#/definitions/ResultUtil«List«PayeeCategoryInformationModel»»"}}, "403": {"description": "Token has incorrect scope or a security policy was violated. Action: Please check whether you’re using the right token with the legal authorized user account."}, "404": {"description": "The requested deposit account does not exist.Action: Please make sure the account number and account type you’re inputting are correct."}, "500": {"description": "Something went wrong on the API gateway or micro-service. Action: check your network and try again later."}}}}, "/payment/payeeInfoListRetrieval": {"post": {"tags": ["Payee Management"], "summary": "This API is to retrieve the details of payees in a specific payee category.", "description": "After calling /payment/retrievalPayeeCategoryList API, you may need to call this API to retrieve the payee id in a specific category.", "operationId": "retrievalPayeeInfoListUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.<br>maxLength: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "Message ID in 128 bit random UUID format generated uniquely for every request by F/E side.<br>maxLength: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "client_id generated during consumer onboarding. <br>maxLength: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "queryPayeeInfoModel", "description": "queryPayeeInfoModel", "required": true, "schema": {"$ref": "#/definitions/QueryPayeeInfoModel"}}], "responses": {"200": {"description": "Query completed successfully.(Returned By Get)", "schema": {"$ref": "#/definitions/ResultUtil«List«PayeeInformationModel»»"}}, "201": {"description": "Normal execution. The request has succeeded. (Returned By Post)", "schema": {"$ref": "#/definitions/ResultUtil«List«PayeeInformationModel»»"}}, "403": {"description": "Token has incorrect scope or a security policy was violated. Action: Please check whether you’re using the right token with the legal authorized user account."}, "404": {"description": "The requested customerAccountNumber  does not exist.Action: Please make sure the account number and account type you’re inputting are correct."}, "500": {"description": "Something went wrong on the API gateway or micro-service. Action: check your network and try again later."}}}}, "/payment/paymentTransaction": {"post": {"tags": ["Transaction"], "summary": "This API is to make a payment transaction.", "description": "At this moment, only HKD payment is supported and you can only use your Saving/Current and Credit Card account to make the payment. \nBefore calling this API, please make sure you've set up your payee list by calling /payment/billPayeeSetup API.", "operationId": "paymentTransactionUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.<br>maxLength: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "Message ID in 128 bit random UUID format generated uniquely for every request by F/E side.<br>maxLength: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "client_id generated during consumer onboarding. <br>maxLength: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "paymentModel", "description": "paymentModel", "required": true, "schema": {"$ref": "#/definitions/PaymentModel"}}], "responses": {"200": {"description": "Query completed successfully.(Returned By Get)", "schema": {"$ref": "#/definitions/ResultUtil«PaymentResponseModel»"}}, "201": {"description": "Normal execution. The request has succeeded. (Returned By Post)", "schema": {"$ref": "#/definitions/ResultUtil«PaymentResponseModel»"}}, "403": {"description": "Token has incorrect scope or a security policy was violated. Action: Please check whether you’re using the right token with the legal authorized user account."}, "404": {"description": "The requested  account does not exist.Action: Please make sure the account number and account type you’re inputting are correct."}, "500": {"description": "Something went wrong on the API gateway or micro-service. Action: check your network and try again later."}}}}}, "definitions": {"CustomerPayeeModel": {"type": "object", "properties": {"createdate": {"type": "number", "example": *************, "description": "Create date.<br>maxLength: 20</br>", "allowEmptyValue": false}, "customernumber": {"type": "string", "example": "0010********", "description": "The customer number.<br>maxLength: 25</br>", "allowEmptyValue": false}, "id": {"type": "integer", "format": "int32", "example": 1, "description": "The unique id.<br>maxLength: 20</br>", "allowEmptyValue": false}, "lastupdatedate": {"type": "number", "example": *************, "description": "Last updated date.<br>maxLength: 20</br>", "allowEmptyValue": false}, "payeecategoryid": {"type": "string", "example": "001", "description": "A unique ID to identify a payee category, such as a school, a water company and so on.<br><br>maxLength: 50</br>", "allowEmptyValue": false}, "payeeid": {"type": "string", "example": "G123456", "description": "A unique ID to identify a payee in a dedicated payee category.<br><br>maxLength: 50</br>", "allowEmptyValue": false}, "payeenumber": {"type": "string", "example": "********", "description": "The customer has a unique code in the corresponding Payee.<br>maxLength: 50</br>", "allowEmptyValue": false}, "status": {"type": "string", "example": "EFCT", "description": "Status, like EFCT(effective) or INEF(ineffective).<br>maxLength: 4</br>", "allowEmptyValue": false}}, "title": "CustomerPayeeModel"}, "DeleteBillPayeeModel": {"type": "object", "required": ["payeeCategoryID", "payeeID", "payeeNumber"], "properties": {"payeeCategoryID": {"type": "string", "example": "001", "description": "A unique ID to identify a payee category, such as a school, a water company and so on.<br><br>maxLength: 50</br>", "allowEmptyValue": false}, "payeeID": {"type": "string", "example": "G123456", "description": "A unique ID to identify a payee in a dedicated payee category.<br><br>maxLength: 50</br>", "allowEmptyValue": false}, "payeeNumber": {"type": "string", "example": 1, "description": "A unique number or number & string to identify a user of a dedicated payee.<br><br>maxLength: 50</br>", "allowEmptyValue": false}}, "title": "DeleteBillPayeeModel"}, "PayeeCategoryInformationModel": {"type": "object", "properties": {"createdate": {"type": "number", "example": 1584614505389, "description": "Creation time.<br>maxLength: 20</br>", "allowEmptyValue": false}, "id": {"type": "integer", "format": "int32", "example": 174181, "description": "The unique id.<br>maxLength: 20</br>", "allowEmptyValue": false}, "lastupdatedate": {"type": "number", "example": 1584614505389, "description": "Last Updated Time.<br>maxLength: 20</br>", "allowEmptyValue": false}, "payeecategory": {"type": "string", "example": "Government or statutory organisation", "description": "PayeeCategory name.<br>maxLength: 70</br>", "allowEmptyValue": false}, "payeecategoryid": {"type": "string", "example": "001", "description": "A unique ID to identify a payee category, such as a school, a water company and so on.<br>maxLength: 50</br>", "allowEmptyValue": false}}, "title": "PayeeCategoryInformationModel"}, "PayeeInformationModel": {"type": "object", "properties": {"createdate": {"type": "number", "example": *************, "description": "Create date.<br>maxLength: 20</br>", "allowEmptyValue": false}, "id": {"type": "integer", "format": "int32", "example": 174181, "description": "The unique id.<br>maxLength: 20</br>", "allowEmptyValue": false}, "lastupdatedate": {"type": "number", "example": *************, "description": "Lastest update date.<br>maxLength: 20</br>", "allowEmptyValue": false}, "payeeaccountnumber": {"type": "string", "example": "***********************", "description": "Payee account number.<br>maxLength: 34</br>", "allowEmptyValue": false}, "payeeaddressformat": {"type": "string", "example": "F", "description": "Payee address format.Address Format:</br>S - Structure format</br>F - Free format.<br>maxLength: 1</br>", "allowEmptyValue": false}, "payeeaddressline1": {"type": "string", "example": "Central District", "description": "Payee address line 1.<br>maxLength: 70</br>", "allowEmptyValue": false}, "payeeaddressline2": {"type": "string", "example": "Citibank Plaza, 3 Garden Road, Central District", "description": "Payee address line 2.<br>maxLength: 70</br>", "allowEmptyValue": false}, "payeeaddressline3": {"type": "string", "example": "<PERSON><PERSON>, Kwun Tong", "description": "Payee address line 3.<br>maxLength: 70</br>", "allowEmptyValue": false}, "payeeaddressline4": {"type": "string", "example": "Central District", "description": "Payee address line 4.<br>maxLength: 70</br>", "allowEmptyValue": false}, "payeeaddressline5": {"type": "string", "example": "<PERSON><PERSON>, Kwun Tong", "description": "Payee address line 5.<br>maxLength: 70</br>", "allowEmptyValue": false}, "payeeaddressline6": {"type": "string", "example": "<PERSON><PERSON> Tong Ind Bldg, Yau Tong", "description": "Payee address line 6.<br>maxLength: 70</br>", "allowEmptyValue": false}, "payeeaddressline7": {"type": "string", "example": "Central District", "description": "Payee address line 7.<br>maxLength: 70</br>", "allowEmptyValue": false}, "payeebuildingname": {"type": "string", "example": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Name of Payee Building.<br>maxLength: 35</br>", "allowEmptyValue": false}, "payeebuildingnumber": {"type": "string", "example": "D01", "description": "Payee building no.<br>maxLength: 16</br>", "allowEmptyValue": false}, "payeecategoryid": {"type": "string", "example": "001", "description": "A unique ID to identify a payee category, such as a school, a water company and so on.<br><br>maxLength: 50</br>", "allowEmptyValue": false}, "payeecountry": {"type": "string", "example": "HK", "description": "Payee Country Area.<br>maxLength: 2</br>", "allowEmptyValue": false}, "payeecountrysubdivision": {"type": "string", "example": "Kowloon", "description": "Identifies a subdivision of a country such as state, region, county.<br>maxLength: 35</br>", "allowEmptyValue": false}, "payeedepartment": {"type": "string", "example": "Hong Kong Deposit Protection Board", "description": "Payee estates.<br>maxLength: 70</br>", "allowEmptyValue": false}, "payeedistrictname": {"type": "string", "example": "Kowloon City", "description": "Identifies a subdivision within a country sub-division.<br>maxLength: 35</br>", "allowEmptyValue": false}, "payeefloor": {"type": "string", "example": 12, "description": "Payee floor.<br>maxLength: 70</br>", "allowEmptyValue": false}, "payeeid": {"type": "string", "example": "P000012", "description": "Payee unique code.<br>maxLength: 50</br>", "allowEmptyValue": false}, "payeename": {"type": "string", "example": "Hong Kong Deposit Protection Board", "description": "Payee name.<br>maxLength: 140</br>", "allowEmptyValue": false}, "payeepostbox": {"type": "string", "example": "#1202", "description": "Payee email account no.<br>maxLength: 16</br>", "allowEmptyValue": false}, "payeepostcode": {"type": "string", "example": 999077, "description": "Payee Post Code.<br>maxLength: 16</br>", "allowEmptyValue": false}, "payeeroom": {"type": "string", "example": "Room1202", "description": "Payee room number.<br>maxLength: 70</br>", "allowEmptyValue": false}, "payeestreetname": {"type": "string", "example": "Fat Kwong Street, No.68", "description": "Payee street name.<br>maxLength: 70</br>", "allowEmptyValue": false}, "payeesubdepartment": {"type": "string", "example": "Deposit Protection Board", "description": "Payee Estate Period.<br>maxLength: 70</br>", "allowEmptyValue": false}, "payeetownlocationname": {"type": "string", "example": "Kowloon Tong", "description": "Specific location name within the town.<br>maxLength: 35</br>", "allowEmptyValue": false}, "payeetownname": {"type": "string", "example": "Kowloon Tong", "description": "The town of Payee name.<br>maxLength: 35</br>", "allowEmptyValue": false}, "phonenumber": {"type": "string", "example": "2370 9288", "description": "Phone number.<br>maxLength: 34</br>", "allowEmptyValue": false}}, "title": "PayeeInformationModel"}, "PaymentModel": {"type": "object", "required": ["customerAccountNumber", "customerAccountType", "payeeId", "payeeNumber", "paymentAmount", "transactionCurrency"], "properties": {"customerAccountNumber": {"type": "string", "example": ****************, "description": "You can input a saving/current/credit card account number  to make a payment. <br></br> maxLength: 34", "allowEmptyValue": false}, "customerAccountType": {"type": "string", "example": "CRED", "description": "Please input the proper account type which is in accordance with the account number you've inputted in the customerAccountNumber field. <br></br>Possible values: <br></br>SAVI - Saving Account <br></br>CURR - Current Account <br></br>CRED - Credit Card <br></br>maxLength: 4", "allowEmptyValue": false}, "payeeId": {"type": "string", "example": "G123456", "description": "A unique ID to identify a payee in a dedicated payee category. <br></br> maxLength: 50", "allowEmptyValue": false}, "payeeNumber": {"type": "string", "example": ************, "description": "A unique number or number & string to identify a user of a dedicated payee. <br></br> maxLength: 50", "allowEmptyValue": false}, "paymentAmount": {"type": "string", "example": 123, "description": "The payment amount in this transaction.  <br></br> maxLength: 18", "allowEmptyValue": false}, "paymentEffectiveDay": {"type": "string", "example": *************, "description": "The day the payment is actually made successfully by your bank and it should be within 1 year from current time.  <br></br> maxLength: 13", "allowEmptyValue": true}, "remarks": {"type": "string", "example": "Current-PaymentTransaction", "description": " Any remark that you'd like to make in the transaction. <br></br> maxLength: 140", "allowEmptyValue": true}, "transactionCurrency": {"type": "string", "example": "HKD", "description": "The currency type of the payment.Possible value:HKD <br></br> maxLength: 3", "allowEmptyValue": false}}, "title": "PaymentModel"}, "PaymentResponseModel": {"type": "object", "properties": {"currencyCode": {"type": "string", "example": "HKD", "description": "Currency code.<br>maxLength: 50</br>", "allowEmptyValue": false}, "payeeId": {"type": "string", "example": "P000012", "description": "Payee unique code.<br>maxLength: 50</br>", "allowEmptyValue": false}, "payeeNumber": {"type": "string", "example": "********", "description": "The customer has a unique code in the corresponding Payee.<br>maxLength: 50</br>", "allowEmptyValue": false}, "paymentAmount": {"type": "string", "example": 2300, "description": "Payment Amount.<br>maxLength: 18</br>", "allowEmptyValue": false}, "paymentEffectiveDay": {"type": "integer", "format": "int64", "example": *************, "description": "Effective date of payment.<br>maxLength: 20</br>", "allowEmptyValue": false}, "transactiontime": {"type": "integer", "format": "int64", "example": *************, "description": "Transaction time.<br>maxLength: 20</br>", "allowEmptyValue": false}}, "title": "PaymentResponseModel"}, "QueryPayeeInfoModel": {"type": "object", "required": ["payeeCategoryId"], "properties": {"payeeCategoryId": {"type": "string", "example": "001", "description": "A unique ID to identify a payee category, such as a school, a water company and so on. </br> maxLength: 50", "allowEmptyValue": false}}, "title": "QueryPayeeInfoModel"}, "ResultUtil": {"type": "object", "properties": {"code": {"type": "string"}, "data": {"type": "object"}, "msg": {"type": "string"}}, "title": "ResultUtil"}, "ResultUtil«List«CustomerPayeeModel»»": {"type": "object", "properties": {"code": {"type": "string"}, "data": {"type": "array", "items": {"$ref": "#/definitions/CustomerPayeeModel"}}, "msg": {"type": "string"}}, "title": "ResultUtil«List«CustomerPayeeModel»»"}, "ResultUtil«List«PayeeCategoryInformationModel»»": {"type": "object", "properties": {"code": {"type": "string"}, "data": {"type": "array", "items": {"$ref": "#/definitions/PayeeCategoryInformationModel"}}, "msg": {"type": "string"}}, "title": "ResultUtil«List«PayeeCategoryInformationModel»»"}, "ResultUtil«List«PayeeInformationModel»»": {"type": "object", "properties": {"code": {"type": "string"}, "data": {"type": "array", "items": {"$ref": "#/definitions/PayeeInformationModel"}}, "msg": {"type": "string"}}, "title": "ResultUtil«List«PayeeInformationModel»»"}, "ResultUtil«PaymentResponseModel»": {"type": "object", "properties": {"code": {"type": "string"}, "data": {"$ref": "#/definitions/PaymentResponseModel"}, "msg": {"type": "string"}}, "title": "ResultUtil«PaymentResponseModel»"}, "SetBillPayeeModel": {"type": "object", "required": ["payeeCategoryID", "payeeID", "payeeNumber"], "properties": {"payeeCategoryID": {"type": "string", "example": "001", "description": "A unique ID to identify a payee category, such as a school, a water company and so on.<br><br>maxLength: 50</br>", "allowEmptyValue": false}, "payeeID": {"type": "string", "example": "G123456", "description": "A unique ID to identify a payee in a dedicated payee category.<br><br>maxLength: 50</br>", "allowEmptyValue": false}, "payeeNumber": {"type": "string", "example": 1, "description": "A unique number or number & string to identify a user of a dedicated payee.<br><br>maxLength: 50</br>", "allowEmptyValue": false}}, "title": "SetBillPayeeModel"}}}