{"swagger": "2.0", "info": {"description": "APIs here are all about travel insurance. Before calling these APIs, please make sure you’ve learnt the business logic between these APIs. ", "version": "1.0", "title": " Travel Insurance Service", "contact": {"name": "<EMAIL>"}}, "schemes": ["https"], "host": "simnectz.hsu.edu.hk/lbsgateway/insurance-experience", "basePath": "/", "tags": [{"name": "Product Management", "description": "Product Management."}, {"name": "Transaction", "description": "Insurance processing includes quotation, application, subscription."}], "paths": {"/insurance/application": {"post": {"tags": ["Transaction"], "summary": "This API is to apply for a travel insurance product.", "description": "Before applying for a travel insurance product, you need to get a quotation by calling /insurance/travelInsuranceQuote. Customer can buy an insurance product to multiple people, so in the same quotation order all the insured person information is required to input. ", "operationId": "applicationUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.<br>maxLength: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "Message ID in 128 bit random UUID format generated uniquely for every request by F/E side.<br>maxLength: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "client_id generated during consumer onboarding. <br>maxLength: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "applyInsuranceModel", "description": "applyInsuranceModel", "required": true, "schema": {"$ref": "#/definitions/ApplyInsuranceModel"}}], "responses": {"200": {"description": "Query completed successfully.(Returned By Get)", "schema": {"$ref": "#/definitions/ResultUtil«ResponseApplyInsuranceVo»"}}, "201": {"description": "Normal execution. The request has succeeded. (Returned By Post)", "schema": {"$ref": "#/definitions/ResultUtil«ResponseApplyInsuranceVo»"}}, "403": {"description": "Token has incorrect scope or a security policy was violated. Action: Please check whether you’re using the right token with the legal authorized user account."}, "404": {"description": "The requested customerAccountNumber  does not exist.Action: Please make sure the account number and account type you’re inputting are correct."}, "500": {"description": "Something went wrong on the API gateway or micro-service. Action: check your network and try again later."}}}}, "/insurance/productdetailretrieval": {"post": {"tags": ["Product Management"], "summary": "This API is to retrieve a travel insurance product details.", "description": "Before calling this API to retrieve a product details, you may need to call /insurance/ProductListRetrieval API to get product list.", "operationId": "retrievalProductDetailUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.<br>maxLength: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "Message ID in 128 bit random UUID format generated uniquely for every request by F/E side.<br>maxLength: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "client_id generated during consumer onboarding. <br>maxLength: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "productDetailModel", "description": "productDetailModel", "required": true, "schema": {"$ref": "#/definitions/ProductDetailModel"}}], "responses": {"200": {"description": "Query completed successfully.(Returned By Get)", "schema": {"$ref": "#/definitions/ResultUtil«ResponseProductDetailModel»"}}, "201": {"description": "Normal execution. The request has succeeded. (Returned By Post)", "schema": {"$ref": "#/definitions/ResultUtil«ResponseProductDetailModel»"}}, "403": {"description": "Token has incorrect scope or a security policy was violated. Action: Please check whether you’re using the right token with the legal authorized user account."}, "404": {"description": "The requested  account does not exist.Action: Please make sure the account number and account type you’re inputting are correct."}, "500": {"description": "Something went wrong on the API gateway or micro-service. Action: check your network and try again later."}}}}, "/insurance/productlistretrieval": {"post": {"tags": ["Product Management"], "summary": "This API is to retrieve a travel insurance product list.", "description": "Before purchasing insurance, you may need to call this API to obtain the list of insurance products.", "operationId": "retrievalProductListUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.<br>maxLength: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "Message ID in 128 bit random UUID format generated uniquely for every request by F/E side.<br>maxLength: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "client_id generated during consumer onboarding. <br>maxLength: 50</br>", "required": true, "type": "string", "default": "devin"}], "responses": {"200": {"description": "Query completed successfully.(Returned By Get)", "schema": {"$ref": "#/definitions/ResultUtil«List«ResponseProductListModel»»"}}, "201": {"description": "Normal execution. The request has succeeded. (Returned By Post)", "schema": {"$ref": "#/definitions/ResultUtil«List«ResponseProductListModel»»"}}, "403": {"description": "Token has incorrect scope or a security policy was violated. Action: Please check whether you’re using the right token with the legal authorized user account."}, "404": {"description": "The requested  account does not exist.Action: Please make sure the account number and account type you’re inputting are correct."}, "500": {"description": "Something went wrong on the API gateway or micro-service. Action: check your network and try again later."}}}}, "/insurance/subscription": {"post": {"tags": ["Transaction"], "summary": "This API is to subscribe a travel insurance product. ", "description": "Before subscribing a travel insurance product, you need to apply for the product first by calling /insurance/application API.", "operationId": "subscriptionUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.<br>maxLength: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "Message ID in 128 bit random UUID format generated uniquely for every request by F/E side.<br>maxLength: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "client_id generated during consumer onboarding. <br>maxLength: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "insuranceSubscriptionModel", "description": "insuranceSubscriptionModel", "required": true, "schema": {"$ref": "#/definitions/InsuranceSubscriptionModel"}}], "responses": {"200": {"description": "Query completed successfully.(Returned By Get)", "schema": {"$ref": "#/definitions/ResultUtil«ResponseInsuranceSubscriptionVo»"}}, "201": {"description": "Normal execution. The request has succeeded. (Returned By Post)", "schema": {"$ref": "#/definitions/ResultUtil«ResponseInsuranceSubscriptionVo»"}}, "403": {"description": "Token has incorrect scope or a security policy was violated. Action: Please check whether you’re using the right token with the legal authorized user account."}, "404": {"description": "The requested customerAccountNumber  does not exist.Action: Please make sure the account number and account type you’re inputting are correct."}, "500": {"description": "Something went wrong on the API gateway or micro-service. Action: check your network and try again later."}}}}, "/insurance/travelInsuranceQuote": {"post": {"tags": ["Transaction"], "summary": "This API is to get a travel insurance product quote.", "description": "In the insurance product quotation, insured person scope vary along with different coverage types.\n When coverage type is set SIGL, the insured person can be Self, Spouse, My Children, Friends/Relatives;\n When coverage type is set ANLC / ANLG, the insured person can be Self, Spouse, My Children,Parent. \nNote: the maximum insured person is 10.\n the maximum insured children is 6.\n the maximum insured friends/relatives is 6.", "operationId": "travelInsuranceQuoteUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.<br>maxLength: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "Message ID in 128 bit random UUID format generated uniquely for every request by F/E side.<br>maxLength: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "client_id generated during consumer onboarding. <br>maxLength: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "travelInsuranceQuoteMode", "description": "travelInsuranceQuoteMode", "required": true, "schema": {"$ref": "#/definitions/TravelInsuranceQuoteModel"}}], "responses": {"200": {"description": "Query completed successfully.(Returned By Get)", "schema": {"$ref": "#/definitions/ResultUtil«ResponseInsuranceQuoteModel»"}}, "201": {"description": "Normal execution. The request has succeeded. (Returned By Post)", "schema": {"$ref": "#/definitions/ResultUtil«ResponseInsuranceQuoteModel»"}}, "403": {"description": "Token has incorrect scope or a security policy was violated. Action: Please check whether you’re using the right token with the legal authorized user account."}, "404": {"description": "The requested customerAccountNumber  does not exist.Action: Please make sure the account number and account type you’re inputting are correct."}, "500": {"description": "Something went wrong on the API gateway or micro-service. Action: check your network and try again later."}}}}}, "definitions": {"ApplyInsuranceChildModel": {"type": "object", "required": ["dateofbirth", "gender", "givenname", "idnumber", "idtype", "relationship", "surnamename"], "properties": {"dateofbirth": {"type": "string", "example": *************, "description": "Birth day of the insured person.maxLength is 20", "allowEmptyValue": false}, "gender": {"type": "string", "example": "M", "description": "Gender of the insured person.<br></br>maxLength is 1", "allowEmptyValue": false}, "givenname": {"type": "string", "example": "Yun", "description": "The given name printed on HKID card or Passport of the insured person.<br></br>maxLength is 20", "allowEmptyValue": false}, "idnumber": {"type": "string", "example": 610425197504240001, "description": "Number of  HKID card or Passport of the insured person.<br></br>maxLength is 35", "allowEmptyValue": false}, "idtype": {"type": "string", "example": "P", "description": "The type of ID you used to buy an insurance product. <br></br>Possible values: HKID card, Passport.<br></br>maxLength is 1", "allowEmptyValue": false}, "relationship": {"type": "string", "example": "SELF", "description": "The relationship bewteen you and the insured person. <br></br>Possible values:SELF - Self,SPOU - Spouse,<br></br>PARE - Parent,CHIL - Child,FRIE - Friend,RELA - Relative. <br></br>maxLength is 4", "allowEmptyValue": false}, "surnamename": {"type": "string", "example": "Ma", "description": "The surname printed on HKID card or Passport of the insured person.<br></br>maxLength is 20", "allowEmptyValue": false}}, "title": "ApplyInsuranceChildModel"}, "ApplyInsuranceModel": {"type": "object", "required": ["quoteid"], "properties": {"applySub": {"type": "array", "items": {"$ref": "#/definitions/ApplyInsuranceChildModel"}}, "quoteid": {"type": "string", "example": "Q000000002", "description": "The quotation id returned after you call insurance quotation API sucessfully.<br><br>maxLength: 35</br>", "allowEmptyValue": false}}, "title": "ApplyInsuranceModel"}, "InsuranceSubscriptionModel": {"type": "object", "required": ["accountNumber", "accountType", "quoteid"], "properties": {"accountNumber": {"type": "string", "example": ****************, "description": "The number of the account, with which you'd like to pay for the travel insurance product.<br><br>maxLength: 34</br>", "allowEmptyValue": false}, "accountType": {"type": "string", "example": "CRED", "description": "The type of the bank account that is supported in the subscription. <br></br>Possible values: CURR - Current,SAVI - Saving,CRED - Credit Card.<br><br>maxLength: 4</br>", "allowEmptyValue": false}, "quoteid": {"type": "string", "example": "Q000000002", "description": "The quotation id returned after you call insurance application API sucessfully<br><br>maxLength: 35</br>", "allowEmptyValue": false}}, "title": "InsuranceSubscriptionModel"}, "ProductDetailModel": {"type": "object", "required": ["productcode"], "properties": {"productcode": {"type": "string", "example": "T001", "description": "A uniqe code to identify a dedicated travel insurance product.<br></br>maxLength is 35", "allowEmptyValue": false}}, "title": "ProductDetailModel"}, "ResponseApplyInsuranceVo": {"type": "object", "properties": {"applicationTime": {"type": "string", "example": *************, "description": "Insurance application time.<br>maxLength: 20</br>", "allowEmptyValue": false}, "quoteStatus": {"type": "string", "example": "PAID", "description": "Quotation  status of insurance,ACTV,PEND or PAID.<br>maxLength: 10</br>", "allowEmptyValue": false}, "quoteid": {"type": "string", "example": "Q000000002", "description": "The quotation id returned after you call insurance quotation API sucessfully.<br><br>maxLength: 35</br>", "allowEmptyValue": false}}, "title": "ResponseApplyInsuranceVo"}, "ResponseInsuranceQuoteModel": {"type": "object", "properties": {"ccyCode": {"type": "string", "example": "HKD", "description": "Currency type<br>maxLength: 3</br>", "allowEmptyValue": false}, "quoteId": {"type": "string", "example": "Q000679761", "description": "Quotation ID unique.<br>maxLength: 20</br>", "allowEmptyValue": false}, "status": {"type": "string", "example": "PEND", "description": "status.<br>maxLength: 4</br>", "allowEmptyValue": false}, "totalAmount": {"type": "number", "example": 1000, "description": "The total amount of transactions.<br>maxLength: 18</br>", "allowEmptyValue": false}}, "title": "ResponseInsuranceQuoteModel"}, "ResponseInsuranceSubscriptionVo": {"type": "object", "properties": {"ccyCode": {"type": "string", "example": "HKD", "description": "Currency type of insurance.<br>maxLength: 3</br>", "allowEmptyValue": false}, "totalAmount": {"type": "string", "example": 1000, "description": "The total amount of transactions.<br>maxLength: 18</br>", "allowEmptyValue": false}, "transactionID": {"type": "string", "example": 12945, "description": "Transaction id of insurance.<br>maxLength: 20</br>", "allowEmptyValue": false}, "transactionTime": {"type": "string", "example": 1591844654142, "description": "Transaction time of insurance.<br>maxLength: 20</br>", "allowEmptyValue": false}}, "title": "ResponseInsuranceSubscriptionVo"}, "ResponseProductDetailModel": {"type": "object", "properties": {"baggageperbelongs": {"type": "number", "example": "Lovers", "description": "Luggage and personal effects.<br>maxLength: 18</br>", "allowEmptyValue": false}, "delayedbaggage": {"type": "number", "example": 100, "description": "delayed baggage.<br>maxLength: 18</br>", "allowEmptyValue": false}, "losscashdocuments": {"type": "number", "example": 750, "description": "Personal cash and documents missing.<br>maxLength: 18</br>", "allowEmptyValue": false}, "medievacuarepatriation": {"type": "string", "example": "ACTC - Actual Cost", "description": "Medical evacuation and repatriation.<br>maxLength: 4</br>", "allowEmptyValue": false}, "medirelatedexpenses": {"type": "number", "example": 600000, "description": "Medical and other related expenses.<br>maxLength: 18</br>", "allowEmptyValue": false}, "otherassisservice": {"type": "string", "example": "24 hours worldwide emergency assistance services", "description": "other assistance service.<br>maxLength: 140</br>", "allowEmptyValue": false}, "personalaccident": {"type": "number", "example": 600000, "description": "personal accident expense.<br>maxLength: 18</br>", "allowEmptyValue": false}, "personalliability": {"type": "number", "example": 500000, "description": "personal liability.<br>maxLength: 18</br>", "allowEmptyValue": false}, "productcode": {"type": "string", "example": "00001", "description": "Unique product code code.<br>maxLength: 35</br>", "allowEmptyValue": false}, "productname": {"type": "string", "example": "Lovers' suits", "description": "product name.<br>maxLength: 70</br>", "allowEmptyValue": false}, "traveldelay": {"type": "number", "example": 2000, "description": "travel delay.<br>maxLength: 18</br>", "allowEmptyValue": false}, "tripcancellation": {"type": "number", "example": 5000, "description": "trip cancellation.<br>maxLength: 18</br>", "allowEmptyValue": false}, "wintersportscover": {"type": "string", "example": "Y", "description": "Whether winter sports coverage is covered.<br>maxLength: 1</br>", "allowEmptyValue": false}}, "title": "ResponseProductDetailModel"}, "ResponseProductListModel": {"type": "object", "required": ["productcode"], "properties": {"insurcompanyname": {"type": "string", "example": "Tian Ping Auto Insurance Company Limited", "description": "The company name of insurance.<br>maxLength: 140</br>", "allowEmptyValue": false}, "productcategoryname": {"type": "string", "example": "TRAV", "description": "The category name of product.<br>maxLength: 4</br>", "allowEmptyValue": false}, "productcode": {"type": "string", "example": "00001", "description": "Product code <br> maxLength:10 </br>", "allowEmptyValue": false}}, "title": "ResponseProductListModel"}, "ResultUtil«List«ResponseProductListModel»»": {"type": "object", "properties": {"code": {"type": "string"}, "data": {"type": "array", "items": {"$ref": "#/definitions/ResponseProductListModel"}}, "msg": {"type": "string"}}, "title": "ResultUtil«List«ResponseProductListModel»»"}, "ResultUtil«ResponseApplyInsuranceVo»": {"type": "object", "properties": {"code": {"type": "string"}, "data": {"$ref": "#/definitions/ResponseApplyInsuranceVo"}, "msg": {"type": "string"}}, "title": "ResultUtil«ResponseApplyInsuranceVo»"}, "ResultUtil«ResponseInsuranceQuoteModel»": {"type": "object", "properties": {"code": {"type": "string"}, "data": {"$ref": "#/definitions/ResponseInsuranceQuoteModel"}, "msg": {"type": "string"}}, "title": "ResultUtil«ResponseInsuranceQuoteModel»"}, "ResultUtil«ResponseInsuranceSubscriptionVo»": {"type": "object", "properties": {"code": {"type": "string"}, "data": {"$ref": "#/definitions/ResponseInsuranceSubscriptionVo"}, "msg": {"type": "string"}}, "title": "ResultUtil«ResponseInsuranceSubscriptionVo»"}, "ResultUtil«ResponseProductDetailModel»": {"type": "object", "properties": {"code": {"type": "string"}, "data": {"$ref": "#/definitions/ResponseProductDetailModel"}, "msg": {"type": "string"}}, "title": "ResultUtil«ResponseProductDetailModel»"}, "TravelInsuranceQuoteModel": {"type": "object", "required": ["coverageType", "productCode", "startDate"], "properties": {"childrenNo": {"type": "integer", "format": "int32", "example": 1, "description": "The total number of the insured children in this insurance quotation.<br/>Possible values: 0,1,2,3,4,5,6 <br/> maxLength: 2", "allowEmptyValue": true}, "coverageType": {"type": "string", "example": "SIGL", "description": "The coverage type you set in this insurance product quotation. <br/> Possible values: <br/> SIGL - Single Trip Cover <br/>ANLC - Annual China Cover <br/>ANLG - Annual Global Cover <br/>maxLength: 4", "allowEmptyValue": false}, "dateOfBirth": {"type": "number", "example": 978250234000, "description": "Birthday of the insured person. <br/>maxLength: 20", "allowEmptyValue": true}, "destination": {"type": "string", "example": "Mainland China and Macau", "description": "The destination of trip. <br/>Possible values: <br/>Mainland China and Macau, <br/>Asia, <br/>Worldwide <br/>maxLength: 35", "allowEmptyValue": true}, "endDate": {"type": "number", "example": 1639826636000, "description": "The expiry date of the travel insurancce product. <br/>maxLength: 20", "allowEmptyValue": true}, "friendsOrRelatives": {"type": "string", "example": "Y", "description": "If the coverageType field is set ANLC or ANLG, please keep this field empty.<br/> Possible values: Y - Yes <br/>empty value. <br/>maxLength: 1", "allowEmptyValue": true}, "friendsOrRelativesNo": {"type": "integer", "format": "int32", "example": 6, "description": "The total number of the insured friends and relatives in this single trip insurance quotation. <br/>Possible values: 0, 1,2,3,4,5,6 <br/>maxLength: 2", "allowEmptyValue": true}, "idNumber": {"type": "string", "example": 15, "description": "The number of the HKID card or passport of the insured person. <br/> maxLength: 35", "allowEmptyValue": true}, "idType": {"type": "string", "example": "P", "description": "The Id type of the insured person. <br/>Possible values:<br/>I - ID Card,<br/>P - Passport. <br/>maxLength: 1", "allowEmptyValue": true}, "myChildren": {"type": "string", "example": "Y", "description": "Please set this field value as Y if you want to buy the insurance product for your children. Otherwise, keep this field empty.” <br/>Possible values: Y - Yes, <br/>empty value <br/>maxLength: 1", "allowEmptyValue": true}, "parents": {"type": "string", "description": "If coverageType field is set SIGL, please keep this field empty.<br/>Possible values: <br/>Y - Yes<br/>empty value.<br/>maxLength: 1", "allowEmptyValue": true}, "parentsNo": {"type": "integer", "format": "int32", "description": "The total number of the insured parents in this insurance quotation <br/>Possible values: 0,1,2 <br/>maxLength: 2", "allowEmptyValue": true}, "productCode": {"type": "string", "example": "T002", "description": "A uniqe code to identify a dedicated travel insurance product. <br/>maxLength: 35", "allowEmptyValue": false}, "self": {"type": "string", "example": "Y", "description": "If you set this field value as Y, it means you set yourself as a insured person. Otherwise, please keep this field empty.<br/>Possible values:<br/>Y - Yes,<br/>empty value <br/> maxLength: 1", "allowEmptyValue": true}, "spouse": {"type": "string", "example": "Y", "description": "If you set this field value as Y, it means you set your spouse as a insured person. Otherwise, please keep this field empty.<br/>Possible values:<br/>Y - Yes,<br/>empty value <br/> maxLength: 1", "allowEmptyValue": true}, "startDate": {"type": "number", "example": 1608290636000, "description": "The effective date of the travel insurancce product.<br/>maxLength: 20", "allowEmptyValue": false}}, "title": "TravelInsuranceQuoteModel"}}}