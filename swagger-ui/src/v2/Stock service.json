{"swagger": "2.0", "info": {"description": "APIs here are all about stock trading service. Before calling these APIs, please make sure you’ve learnt the business logic between these APIs.", "version": "1.0", "title": "Stock service", "contact": {"name": "<EMAIL>"}}, "schemes": ["https"], "host": "simnectz.hsu.edu.hk/lbsgateway/stock-experience", "basePath": "/", "tags": [{"name": "Market Information", "description": "Retrieve and display latest stock information."}, {"name": "Order & Simulation", "description": "Stock order management & Exchange simulation."}, {"name": "Account Information & Maintenance", "description": "Stock account holding information & settlement."}, {"name": "Transaction", "description": "Transaction processing in customer’s stock account."}], "paths": {"/stock/order/cancellation": {"post": {"tags": ["Order & Simulation"], "summary": "This API is designed to cancel a stock order.", "description": "Before calling this API, you may need to place a stock order by calling /stock/order/orderPlacing API. To cancel an order, please also make sure the order is still in pending status, which means the transaction is still in pending status.", "operationId": "cancellationUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.<br>maxLength: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "Message ID in 128 bit random UUID format generated uniquely for every request by F/E side.<br>maxLength: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "client_id generated during consumer onboarding. <br>maxLength: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "idmodel", "description": "idmodel", "required": true, "schema": {"$ref": "#/definitions/IDModel"}}], "responses": {"200": {"description": "Query completed successfully.(Returned By Get)", "schema": {"$ref": "#/definitions/ResultUtil"}}, "201": {"description": "Normal execution. The request has succeeded. (Returned By Post)", "schema": {"$ref": "#/definitions/ResultUtil"}}, "403": {"description": "Token has incorrect scope or a security policy was violated. Action: Please check whether you’re using the right token with the legal authorized user account."}, "404": {"description": "The requested deposit account does not exist.Action: Please make sure the account number and account type you’re inputting are correct."}, "500": {"description": "Something went wrong on the API gateway or micro-service. Action: check your network and try again later."}}}}, "/stock/order/orderChange": {"post": {"tags": ["Order & Simulation"], "summary": "This API is designed to change a stock order.", "description": "Once an order is placed successfully, but status hasn't been updated yet, you can update the order information,such as sharing number and trading price by calling this API.", "operationId": "orderInfoUpdateUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.<br>maxLength: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "Message ID in 128 bit random UUID format generated uniquely for every request by F/E side.<br>maxLength: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "client_id generated during consumer onboarding. <br>maxLength: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "updateOrderRequestModel", "description": "updateOrderRequestModel", "required": true, "schema": {"$ref": "#/definitions/UpdateOrderRequestModel"}}], "responses": {"200": {"description": "Query completed successfully.(Returned By Get)", "schema": {"$ref": "#/definitions/ResultUtil"}}, "201": {"description": "Normal execution. The request has succeeded. (Returned By Post)", "schema": {"$ref": "#/definitions/ResultUtil"}}, "403": {"description": "Token has incorrect scope or a security policy was violated. Action: Please check whether you’re using the right token with the legal authorized user account."}, "404": {"description": "The requested deposit account does not exist.Action: Please make sure the account number and account type you’re inputting are correct."}, "500": {"description": "Something went wrong on the API gateway or micro-service. Action: check your network and try again later."}}}}, "/stock/order/orderDetailRetrievalById": {"post": {"tags": ["Order & Simulation"], "summary": "This API is designed to retrieve order details by Id.", "description": "Before calling this API to get an order details, please call /stock/order/orderRetrieval API to retrieve the order id.", "operationId": "orderDetailRetrievalByIdUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.<br>maxLength: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "Message ID in 128 bit random UUID format generated uniquely for every request by F/E side.<br>maxLength: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "client_id generated during consumer onboarding. <br>maxLength: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "idmodel", "description": "idmodel", "required": true, "schema": {"$ref": "#/definitions/IDModel"}}], "responses": {"200": {"description": "Query completed successfully.(Returned By Get)", "schema": {"$ref": "#/definitions/ResultUtil«StockOrderInfoModel»"}}, "201": {"description": "Normal execution. The request has succeeded. (Returned By Post)", "schema": {"$ref": "#/definitions/ResultUtil«StockOrderInfoModel»"}}, "403": {"description": "Token has incorrect scope or a security policy was violated. Action: Please check whether you’re using the right token with the legal authorized user account."}, "404": {"description": "The requested deposit account does not exist.Action: Please make sure the account number and account type you’re inputting are correct."}, "500": {"description": "Something went wrong on the API gateway or micro-service. Action: check your network and try again later."}}}}, "/stock/order/orderPlacing": {"post": {"tags": ["Order & Simulation"], "summary": "This API is designed to place a stock order.", "description": "To buy or sell stocks, you always need to call this API first  to place a stock buy/sell order.", "operationId": "orderPlacingUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.<br>maxLength: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "Message ID in 128 bit random UUID format generated uniquely for every request by F/E side.<br>maxLength: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "client_id generated during consumer onboarding. <br>maxLength: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "stockTradingModel", "description": "stockTradingModel", "required": true, "schema": {"$ref": "#/definitions/StockTradingModel"}}], "responses": {"200": {"description": "Query completed successfully.(Returned By Get)", "schema": {"$ref": "#/definitions/ResultUtil"}}, "201": {"description": "Normal execution. The request has succeeded. (Returned By Post)", "schema": {"$ref": "#/definitions/ResultUtil"}}, "403": {"description": "Token has incorrect scope or a security policy was violated. Action: Please check whether you’re using the right token with the legal authorized user account."}, "404": {"description": "The requested deposit account does not exist.Action: Please make sure the account number and account type you’re inputting are correct."}, "500": {"description": "Something went wrong on the API gateway or micro-service. Action: check your network and try again later."}}}}, "/stock/order/orderRetrieval": {"post": {"tags": ["Order & Simulation"], "summary": "This API is designed to retrieve all the stock order records.", "description": "Once an order is placed successfully by calling /stock/order/orderPlacing API , you can retrieve the order details via this API. This API will return all order details within a specific time range based on your search condition.", "operationId": "orderRetrievalUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.<br>maxLength: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "Message ID in 128 bit random UUID format generated uniquely for every request by F/E side.<br>maxLength: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "client_id generated during consumer onboarding. <br>maxLength: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "orderRequestModel", "description": "orderRequestModel", "required": true, "schema": {"$ref": "#/definitions/OrderRequestModel"}}], "responses": {"200": {"description": "Query completed successfully.(Returned By Get)", "schema": {"$ref": "#/definitions/ResultUtil«List«StockOrderInfoModel»»"}}, "201": {"description": "Normal execution. The request has succeeded. (Returned By Post)", "schema": {"$ref": "#/definitions/ResultUtil«List«StockOrderInfoModel»»"}}, "403": {"description": "Token has incorrect scope or a security policy was violated. Action: Please check whether you’re using the right token with the legal authorized user account."}, "404": {"description": "The requested deposit account does not exist.Action: Please make sure the account number and account type you’re inputting are correct."}, "500": {"description": "Something went wrong on the API gateway or micro-service. Action: check your network and try again later."}}}}, "/stock/order/simulatorUpdate": {"post": {"tags": ["Order & Simulation"], "summary": "This API is designed to update stock order simulator.", "description": "After you place an order via /stock/order/orderPlacing API, you need to call /stock/order/orderRetrieval API to retrieve the order id, then you're able to approve/reject this order with the order id via this API.", "operationId": "orderStatusUpdateUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.<br>maxLength: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "Message ID in 128 bit random UUID format generated uniquely for every request by F/E side.<br>maxLength: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "client_id generated during consumer onboarding. <br>maxLength: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "updateOrderStatusModel", "description": "updateOrderStatusModel", "required": true, "schema": {"$ref": "#/definitions/UpdateOrderStatusModel"}}], "responses": {"200": {"description": "Query completed successfully.(Returned By Get)", "schema": {"$ref": "#/definitions/ResultUtil"}}, "201": {"description": "Normal execution. The request has succeeded. (Returned By Post)", "schema": {"$ref": "#/definitions/ResultUtil"}}, "403": {"description": "Token has incorrect scope or a security policy was violated. Action: Please check whether you’re using the right token with the legal authorized user account."}, "404": {"description": "The requested deposit account does not exist.Action: Please make sure the account number and account type you’re inputting are correct."}, "500": {"description": "Something went wrong on the API gateway or micro-service. Action: check your network and try again later."}}}}, "/stock/settlementAccountUpdate": {"post": {"tags": ["Account Information & Maintenance"], "summary": "This API is designed to update the settlement account of a stock account.", "description": "After your stock account is opened successfully, you can update the stock settlement account by calling this API.", "operationId": "settlementAccountUpdateUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.<br>maxLength: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "Message ID in 128 bit random UUID format generated uniquely for every request by F/E side.<br>maxLength: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "client_id generated during consumer onboarding. <br>maxLength: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "updateSettlementAccountModel", "description": "updateSettlementAccountModel", "required": true, "schema": {"$ref": "#/definitions/UpdateSettlementAccountModel"}}], "responses": {"200": {"description": "Query completed successfully.(Returned By Get)", "schema": {"$ref": "#/definitions/ResultUtil"}}, "201": {"description": "Normal execution. The request has succeeded. (Returned By Post)", "schema": {"$ref": "#/definitions/ResultUtil"}}, "403": {"description": "Token has incorrect scope or a security policy was violated. Action: Please check whether you’re using the right token with the legal authorized user account."}, "404": {"description": "The requested deposit account does not exist.Action: Please make sure the account number and account type you’re inputting are correct."}, "500": {"description": "Something went wrong on the API gateway or micro-service. Action: check your network and try again later."}}}}, "/stock/stockHoldingEnquiry": {"post": {"tags": ["Account Information & Maintenance"], "summary": "This API is designed to retrieve a stock account holding information.", "description": "After you buy/ sell stocks successfully, you may want to  check your latest stock holding details by calling this API. Therefore, before calling this API, you need to place orders and update their status, so the stock transaction is really made or rejected successfully.", "operationId": "stockHoldingEnquiryUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.<br>maxLength: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "Message ID in 128 bit random UUID format generated uniquely for every request by F/E side.<br>maxLength: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "client_id generated during consumer onboarding. <br>maxLength: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "stockHoldingEnquiryModel", "description": "stockHoldingEnquiryModel", "required": true, "schema": {"$ref": "#/definitions/StockHoldingEnquiryModel"}}], "responses": {"200": {"description": "Query completed successfully.(Returned By Get)", "schema": {"$ref": "#/definitions/ResultUtil«StockHoldingInfoModel»"}}, "201": {"description": "Normal execution. The request has succeeded. (Returned By Post)", "schema": {"$ref": "#/definitions/ResultUtil«StockHoldingInfoModel»"}}, "403": {"description": "Token has incorrect scope or a security policy was violated. Action: Please check whether you’re using the right token with the legal authorized user account."}, "404": {"description": "The requested deposit account does not exist.Action: Please make sure the account number and account type you’re inputting are correct."}, "500": {"description": "Something went wrong on the API gateway or micro-service. Action: check your network and try again later."}}}}, "/stock/stockList": {"post": {"tags": ["Market Information"], "summary": "This API is designed to retrieve all the stocks.", "description": "Before calling /order/orderPlacing to buy a stock, you may want to check the stocks in the market.", "operationId": "stockListUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.<br>maxLength: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "Message ID in 128 bit random UUID format generated uniquely for every request by F/E side.<br>maxLength: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "client_id generated during consumer onboarding. <br>maxLength: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "queryStockListModel", "description": "queryStockListModel", "required": true, "schema": {"$ref": "#/definitions/QueryStockListModel"}}], "responses": {"200": {"description": "Query completed successfully.(Returned By Get)", "schema": {"$ref": "#/definitions/ResultUtil«List«StockInformationModel»»"}}, "201": {"description": "Normal execution. The request has succeeded. (Returned By Post)", "schema": {"$ref": "#/definitions/ResultUtil«List«StockInformationModel»»"}}, "403": {"description": "Token has incorrect scope or a security policy was violated. Action: Please check whether you’re using the right token with the legal authorized user account."}, "404": {"description": "The requested deposit account does not exist.Action: Please make sure the account number and account type you’re inputting are correct."}, "500": {"description": "Something went wrong on the API gateway or micro-service. Action: check your network and try again later."}}}}, "/stock/stockQuotation": {"post": {"tags": ["Market Information"], "summary": "This API is designed to retrieve a stock quotation details.", "description": "If you just want to check a specific stock market information with a stock code, you can just call this API dierctly.", "operationId": "stockQuotationUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.<br>maxLength: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "Message ID in 128 bit random UUID format generated uniquely for every request by F/E side.<br>maxLength: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "client_id generated during consumer onboarding. <br>maxLength: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "stockCodeModel", "description": "stockCodeModel", "required": true, "schema": {"$ref": "#/definitions/StockCodeModel"}}], "responses": {"200": {"description": "Query completed successfully.(Returned By Get)", "schema": {"$ref": "#/definitions/ResultUtil«StockInformationModel»"}}, "201": {"description": "Normal execution. The request has succeeded. (Returned By Post)", "schema": {"$ref": "#/definitions/ResultUtil«StockInformationModel»"}}, "403": {"description": "Token has incorrect scope or a security policy was violated. Action: Please check whether you’re using the right token with the legal authorized user account."}, "404": {"description": "The requested deposit account does not exist.Action: Please make sure the account number and account type you’re inputting are correct."}, "500": {"description": "Something went wrong on the API gateway or micro-service. Action: check your network and try again later."}}}}, "/stock/transactionRetrieval": {"post": {"tags": ["Transaction"], "summary": "This API is designed to retrieve a stock account history transaction record details.", "description": "After buying/selling stocks, you may want to check the stock account transaction history records within a specific time range or of a specific stock .", "operationId": "retrieveStkTransactionUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.<br>maxLength: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "Message ID in 128 bit random UUID format generated uniquely for every request by F/E side.<br>maxLength: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "client_id generated during consumer onboarding. <br>maxLength: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "queryStockTransModel", "description": "queryStockTransModel", "required": true, "schema": {"$ref": "#/definitions/QueryStockTransModel"}}], "responses": {"200": {"description": "Query completed successfully.(Returned By Get)", "schema": {"$ref": "#/definitions/ResultUtil«List«StockPlatFormLogModel»»"}}, "201": {"description": "Normal execution. The request has succeeded. (Returned By Post)", "schema": {"$ref": "#/definitions/ResultUtil«List«StockPlatFormLogModel»»"}}, "403": {"description": "Token has incorrect scope or a security policy was violated. Action: Please check whether you’re using the right token with the legal authorized user account."}, "404": {"description": "The requested deposit account does not exist.Action: Please make sure the account number and account type you’re inputting are correct."}, "500": {"description": "Something went wrong on the API gateway or micro-service. Action: check your network and try again later."}}}}}, "definitions": {"IDModel": {"type": "object", "required": ["id"], "properties": {"id": {"type": "number", "example": 2.0, "description": "A unique id generated when you place a stock order.<br/> maxValue: **********<br>maxLength: 20</br>", "allowEmptyValue": false}}, "title": "IDModel"}, "OrderRequestModel": {"type": "object", "required": ["fromdate", "index", "items", "stkaccountnumber", "todate"], "properties": {"fromdate": {"type": "number", "example": *************.0, "description": "The start date when you want to retrieve the order records.<br/>maxLength: 20", "allowEmptyValue": false}, "index": {"type": "number", "example": 0.0, "description": "an indicator to identify where your respone result will start to show.<br/>maxLength: 4", "allowEmptyValue": false}, "items": {"type": "number", "example": 3.0, "description": "the number of items you'd like to get in the response result.<br/>maxLength: 4", "allowEmptyValue": false}, "stkaccountnumber": {"type": "string", "example": "***********************", "description": "A stock account number of the customer.<br/> maxLength: 34", "allowEmptyValue": false}, "stockcode": {"type": "string", "example": "00112.HK", "description": "A unique code to identify a stock. <br/> maxLength: 10", "allowEmptyValue": true}, "todate": {"type": "number", "example": *************.0, "description": "The end date when you want to retrieve the order records.<br/>maxLength: 20", "allowEmptyValue": false}}, "title": "OrderRequestModel"}, "QueryStockListModel": {"type": "object", "required": ["index", "items"], "properties": {"index": {"type": "number", "example": 0.0, "description": "an indicator to identify where your respone result will start to show.<br/>maxLength: 4", "allowEmptyValue": false}, "items": {"type": "number", "example": 3.0, "description": "the number of items you'd like to get in the response result.<br/>maxLength: 4", "allowEmptyValue": false}}, "title": "QueryStockListModel"}, "QueryStockTransModel": {"type": "object", "required": ["accountnumber", "index", "items", "transFromDate", "transToDate"], "properties": {"accountnumber": {"type": "string", "example": "***********************", "description": "Stock Trading Account Number <br/> maxLength: 34", "allowEmptyValue": false}, "index": {"type": "number", "example": 0.0, "description": "an indicator to identify where your respone result will start to show.<br/>maxLength: 4", "allowEmptyValue": false}, "items": {"type": "number", "example": 3.0, "description": " the number of items you'd like to get in the response result.<br/>maxLength: 4", "allowEmptyValue": false}, "stocknumber": {"type": "string", "example": "00112.HK", "description": "stock code <br/> maxLength: 10", "allowEmptyValue": true}, "transFromDate": {"type": "number", "example": *************.0, "description": "The start date of the transactions.<br/>maxLength: 20", "allowEmptyValue": false}, "transToDate": {"type": "number", "example": *************.0, "description": "The end date of the transactions.<br/>maxLength: 20", "allowEmptyValue": false}}, "title": "QueryStockTransModel"}, "ResultUtil": {"type": "object", "properties": {"code": {"type": "string"}, "data": {"type": "object"}, "msg": {"type": "string"}}, "title": "ResultUtil"}, "ResultUtil«List«StockInformationModel»»": {"type": "object", "properties": {"code": {"type": "string"}, "data": {"type": "array", "items": {"$ref": "#/definitions/StockInformationModel"}}, "msg": {"type": "string"}}, "title": "ResultUtil«List«StockInformationModel»»"}, "ResultUtil«List«StockOrderInfoModel»»": {"type": "object", "properties": {"code": {"type": "string"}, "data": {"type": "array", "items": {"$ref": "#/definitions/StockOrderInfoModel"}}, "msg": {"type": "string"}}, "title": "ResultUtil«List«StockOrderInfoModel»»"}, "ResultUtil«List«StockPlatFormLogModel»»": {"type": "object", "properties": {"code": {"type": "string"}, "data": {"type": "array", "items": {"$ref": "#/definitions/StockPlatFormLogModel"}}, "msg": {"type": "string"}}, "title": "ResultUtil«List«StockPlatFormLogModel»»"}, "ResultUtil«StockHoldingInfoModel»": {"type": "object", "properties": {"code": {"type": "string"}, "data": {"$ref": "#/definitions/StockHoldingInfoModel"}, "msg": {"type": "string"}}, "title": "ResultUtil«StockHoldingInfoModel»"}, "ResultUtil«StockInformationModel»": {"type": "object", "properties": {"code": {"type": "string"}, "data": {"$ref": "#/definitions/StockInformationModel"}, "msg": {"type": "string"}}, "title": "ResultUtil«StockInformationModel»"}, "ResultUtil«StockOrderInfoModel»": {"type": "object", "properties": {"code": {"type": "string"}, "data": {"$ref": "#/definitions/StockOrderInfoModel"}, "msg": {"type": "string"}}, "title": "ResultUtil«StockOrderInfoModel»"}, "StockCodeModel": {"type": "object", "required": ["stockcode"], "properties": {"stockcode": {"type": "string", "example": "00112.HK", "description": "If you just want to check a specific stock market information with a stock code, you can just call this API dierctly. <br/> maxLength: 10", "allowEmptyValue": false}}, "title": "StockCodeModel"}, "StockHoldingEnquiryModel": {"type": "object", "required": ["stkaccountnumber"], "properties": {"stkaccountnumber": {"type": "string", "example": "***********************", "description": "Stock Trading Account Number <br/> maxLength: 34", "allowEmptyValue": false}, "stockcode": {"type": "string", "example": "00112.HK", "description": "Stock code <br/> maxLength: 10", "allowEmptyValue": true}}, "title": "StockHoldingEnquiryModel"}, "StockHoldingInfoModel": {"type": "object", "properties": {"settlementaccount": {"type": "string", "example": "***********************", "description": "Settlement Account Number.</br> maxLength: 34", "allowEmptyValue": false}, "stkaccountnumber": {"type": "string", "example": "***********************", "description": "A stock account number of the customer.<br/> maxLength: 34", "allowEmptyValue": false}, "stkholdlist": {"type": "array", "items": {"$ref": "#/definitions/StockInvestmentModel"}}, "totalInvestmentAmount": {"type": "string", "example": 100, "description": "total investment amount.<br>maxLength: 18</br>", "allowEmptyValue": false}, "totalNetGainLoss": {"type": "string", "example": 100, "description": "total NetGain Loss.<br>maxLength: 18</br>", "allowEmptyValue": false}, "totalNetGainLossPct": {"type": "string", "example": 100, "description": "total NetGain LossPct.<br>maxLength: 18</br>", "allowEmptyValue": false}, "totalmarkervalue": {"type": "string", "description": "total market value.<br>maxLength: 18</br>", "allowEmptyValue": false}}, "title": "StockHoldingInfoModel"}, "StockInformationModel": {"type": "object", "properties": {"buyprice": {"type": "number", "example": 698.0, "description": "Buying price.<br>maxLength: 18</br>", "allowEmptyValue": false}, "changed": {"type": "string", "example": 1.0, "description": "Changed in stocks.<br>maxLength: 18</br>", "allowEmptyValue": false}, "changedpercent": {"type": "string", "example": 0.143, "description": "Percentage increase in stocks.<br>maxLength: 18</br>", "allowEmptyValue": false}, "eps": {"type": "number", "example": 11.01, "description": "Earnings per share.<br>maxLength: 18</br>", "allowEmptyValue": false}, "high": {"type": "number", "example": 722.0, "description": "The highest stock price of the day.<br>maxLength: 18</br>", "allowEmptyValue": false}, "id": {"type": "integer", "format": "int32", "example": 1, "description": "Unique id.<br>maxLength: 20</br>", "allowEmptyValue": false}, "lastprice": {"type": "number", "example": 698.0, "description": "The latest price.<br>maxLength: 18</br>", "allowEmptyValue": false}, "lasttradingday": {"type": "number", "example": *************.0, "description": "Last trading day.<br>maxLength: 20</br>", "allowEmptyValue": false}, "lotsize": {"type": "number", "example": 100.0, "description": "Stock lot size.<br>maxLength: 18</br>", "allowEmptyValue": false}, "low": {"type": "number", "example": 692.5, "description": "The lowest stock price of the day.<br>maxLength: 18</br>", "allowEmptyValue": false}, "open": {"type": "number", "example": 709.5, "description": "Stock the opening.<br>maxLength: 18</br>", "allowEmptyValue": false}, "previousclose": {"type": "number", "example": 697.0, "description": "Latest closing price.<br>maxLength: 18</br>", "allowEmptyValue": false}, "ratio": {"type": "number", "example": 63.37, "description": "Ratio.<br>maxLength: 18</br>", "allowEmptyValue": false}, "sellprice": {"type": "number", "example": 698.5, "description": "Selling price.<br>maxLength: 18</br>", "allowEmptyValue": false}, "stockcode": {"type": "string", "example": "00700.HK", "description": "Stock code.<br>maxLength: 10</br>", "allowEmptyValue": false}, "stockname": {"type": "string", "example": "TENCENT", "description": "Stock name.<br>maxLength: 140</br>", "allowEmptyValue": false}, "tradingpoint": {"type": "number", "example": 0.5, "description": "Trading posts.<br>maxLength: 18</br>", "allowEmptyValue": false}, "turnover": {"type": "number", "example": ***********.34, "description": "Stock turnover.<br>maxLength: 18</br>", "allowEmptyValue": false}, "volume": {"type": "number", "example": ********.0, "description": "Stock trading volume.<br>maxLength: 18</br>", "allowEmptyValue": false}}, "title": "StockInformationModel"}, "StockInvestmentModel": {"type": "object", "properties": {"accountnumber": {"type": "string", "example": "***********************", "description": "A fund account number. <br/> maxLength: 34", "allowEmptyValue": false}, "availableshare": {"type": "number", "example": 1000.0, "description": "Tradable share.<br>maxLength: 18</br>", "allowEmptyValue": false}, "averageprice": {"type": "number", "example": 60.0, "description": "Stock average price.<br>maxLength: 18</br>", "allowEmptyValue": false}, "currencycode": {"type": "string", "example": "HKD", "description": "Currency code.<br>maxLength: 3</br>", "allowEmptyValue": false}, "investmentAmount": {"type": "number", "example": 500.0, "description": "Investment amount.<br>maxLength: 18</br>", "allowEmptyValue": false}, "lastupdatedate": {"type": "number", "example": *************.0, "description": "Last update date.<br>maxLength: 20</br>", "allowEmptyValue": false}, "marketprice": {"type": "number", "example": 500.0, "description": "Market price.<br>maxLength: 18</br>", "allowEmptyValue": false}, "marketvalue": {"type": "number", "example": 50000.0, "description": "Market value.<br>maxLength: 18</br>", "allowEmptyValue": false}, "netGainLoss": {"type": "number", "example": 3000.0, "description": "Net income loss.<br>maxLength: 18</br>", "allowEmptyValue": false}, "netGainLossPct": {"type": "string", "example": 300, "description": "Net gain Loss Pct.<br>maxLength: 18</br>", "allowEmptyValue": false}, "sharesholdingno": {"type": "number", "example": 1000.0, "description": "Total shareholding number.<br>maxLength: 18</br>", "allowEmptyValue": false}, "stockcode": {"type": "string", "example": "00700.HK", "description": "A unique code to identify a stock. <br/> maxLength: 10", "allowEmptyValue": false}, "stockname": {"type": "string", "example": "TENCENT", "description": "Stock name.<br>maxLength: 140</br>", "allowEmptyValue": false}}, "title": "StockInvestmentModel"}, "StockOrderInfoModel": {"type": "object", "properties": {"currencycode": {"type": "string", "example": "HKD", "description": "Currency code.<br>maxLength: 3</br>", "allowEmptyValue": false}, "custodycharges": {"type": "number", "example": 20.0, "description": "The safekeeping fee.<br>maxLength: 18</br>", "allowEmptyValue": false}, "expirydate": {"type": "number", "example": *************.0, "description": "Expiration time.<br>maxLength: 20</br>", "allowEmptyValue": false}, "id": {"type": "integer", "format": "int32", "example": 1, "description": "Unique id.<br>maxLength: 20</br>", "allowEmptyValue": false}, "lastupdatedate": {"type": "number", "example": *************.0, "description": "Last updated date.<br>maxLength: 20</br>", "allowEmptyValue": false}, "operationdate": {"type": "number", "example": *************.0, "description": "Operation date.<br>maxLength: 20</br>", "allowEmptyValue": false}, "operationreasons": {"type": "string", "description": "Operation reason.<br>maxLength: 140</br>", "allowEmptyValue": false}, "ordertype": {"type": "string", "example": "Market Price", "description": "Stock order type,Allowed value:Market Price /Fix Price<br>maxLength: 20</br>", "allowEmptyValue": false}, "requesttime": {"type": "number", "example": *************.0, "description": "Request time.<br>maxLength: 20</br>", "allowEmptyValue": false}, "settlementaccount": {"type": "string", "example": "***********************", "description": "Settlement account number.<br>maxLength: 34</br>", "allowEmptyValue": false}, "sharingno": {"type": "number", "example": 1000.0, "description": "Buy and sell shares.<br>maxLength: 18</br>", "allowEmptyValue": false}, "status": {"type": "string", "description": "Status.<br>maxLength: 4</br>", "allowEmptyValue": false}, "statuscode": {"type": "string", "example": 1, "description": "Status code.<br>maxLength: 10</br>", "allowEmptyValue": false}, "stockaccountnumber": {"type": "string", "example": "***********************", "description": "Stock account number.<br>maxLength: 34</br>", "allowEmptyValue": false}, "stockcode": {"type": "string", "example": "00700.HK", "description": "Stock code.<br>maxLength: 10</br>", "allowEmptyValue": false}, "stockname": {"type": "string", "example": "TENCENT", "description": "Stock name.<br>maxLength: 140</br>", "allowEmptyValue": false}, "tradingamount": {"type": "number", "example": 5200.0, "description": "Transaction amount.<br>maxLength: 18</br>", "allowEmptyValue": false}, "tradingcommission": {"type": "number", "example": 5.0, "description": "The trading commissions.<br>maxLength: 18</br>", "allowEmptyValue": false}, "tradingoption": {"type": "string", "example": "Buy", "description": "Trading Options (Buy/Sell)<br>maxLength: 10</br>", "allowEmptyValue": false}, "tradingprice": {"type": "number", "example": 52.0, "description": "The stock price.<br>maxLength: 18</br>", "allowEmptyValue": false}, "transactionamount": {"type": "number", "example": 100.0, "description": "Deduction/amount received.<br>maxLength: 18</br>", "allowEmptyValue": false}}, "title": "StockOrderInfoModel"}, "StockPlatFormLogModel": {"type": "object", "properties": {"accountnumber": {"type": "string", "example": "***********************", "description": "Stock account Number </br> maxLength: 34", "allowEmptyValue": false}, "branchcode": {"type": "string", "example": "0001", "description": "Bank branch code.<br>maxLength: 20</br>", "allowEmptyValue": false}, "clearingcode": {"type": "string", "example": "0001", "description": "Bank clearing code.<br>maxLength: 20</br>", "allowEmptyValue": false}, "countrycode": {"type": "string", "example": "HK", "description": "Country code.<br>maxLength: 2</br>", "allowEmptyValue": false}, "custodycharges": {"type": "number", "example": 20.0, "description": "The safekeeping fee.<br>maxLength: 18</br>", "allowEmptyValue": false}, "id": {"type": "integer", "format": "int32", "example": 1, "description": "Unique id.<br>maxLength: 20</br>", "allowEmptyValue": false}, "sharingno": {"type": "number", "example": 1000.0, "description": "Buy and sell shares.<br>maxLength: 18</br>", "allowEmptyValue": false}, "stocknumber": {"type": "string", "example": "00112.HK", "description": "stock code <br/> maxLength: 10", "allowEmptyValue": false}, "stocktrdingamount": {"type": "number", "example": 3000.0, "description": "Stock trading amount.<br>maxLength: 18</br>", "allowEmptyValue": false}, "stocktrdingcommission": {"type": "number", "example": 10.0, "description": "Stock transaction commission.<br>maxLength: 18</br>", "allowEmptyValue": false}, "tradingoption": {"type": "string", "example": "Buy", "description": "Trading Options (Buy/Sell).<br>maxLength: 10</br>", "allowEmptyValue": false}, "transactionamount": {"type": "number", "example": 25000.0, "description": "The transaction amount.<br>maxLength: 18</br>", "allowEmptyValue": false}, "transactiondate": {"type": "number", "example": *************.0, "description": "The transaction date.<br>maxLength: 20</br>", "allowEmptyValue": false}}, "title": "StockPlatFormLogModel"}, "StockTradingModel": {"type": "object", "required": ["orderType", "sharingNo", "stkaccountnumber", "stockCode", "tradingOption"], "properties": {"expiryDate": {"type": "number", "example": *************.0, "description": "Expiry date set for a fix price order. Please keep expiry date field empty when the orderType is Market Price.<br/>maxLength: 20", "allowEmptyValue": true}, "orderType": {"type": "string", "example": "Fix Price", "description": "Whether it's a Market Price order or a Fix Price order. <br/>Possible values: Market Price, Fix Price  <br/> maxLength: 20", "allowEmptyValue": false}, "sharingNo": {"type": "string", "example": 2000, "description": "The stock shares you want to buy/sell. SharingNo must be multiple of the stock lotsize. Also, sharingNo can only be less than  9 integer.<br/>maxLength: 8", "allowEmptyValue": false}, "stkaccountnumber": {"type": "string", "example": "***********************", "description": "Stock Trading Account Number <br/> maxLength: 34", "allowEmptyValue": false}, "stockCode": {"type": "string", "example": "00112.HK", "description": "Stock code. <br/> maxLength: 10", "allowEmptyValue": false}, "tradingOption": {"type": "string", "example": "Buy", "description": "To buy or sell stocks.<br/>Possible values: Buy, Sell  <br/> maxLength: 10", "allowEmptyValue": false}, "tradingPrice": {"type": "string", "example": 5.17, "description": "The price set for the stock trading when orderType is set Fix price.<br/>5 decimals, maxLength: 13", "allowEmptyValue": true}}, "title": "StockTradingModel"}, "UpdateOrderRequestModel": {"type": "object", "required": ["id"], "properties": {"expiryDate": {"type": "number", "example": *************.0, "description": "Expiry date set for a fix price order. Please keep expiry date field empty when the orderType is Market Price.<br/>maxLength: 20", "allowEmptyValue": true}, "id": {"type": "number", "example": 2.0, "description": "A unique id generated when you place a stock order.<br/> maxValue: **********<br>maxLength: 20</br>", "allowEmptyValue": false}, "sharingno": {"type": "string", "example": 2000, "description": "The stock shares you want to buy/sell.<br/>maxLength: 8", "allowEmptyValue": true}, "tradingprice": {"type": "string", "example": 7.2, "description": "The price set for the stock trading when orderType is set Fix price. Please keep this field empty in a Market Price order.<br/>2 decimals,maxLength: 13", "allowEmptyValue": true}}, "title": "UpdateOrderRequestModel"}, "UpdateOrderStatusModel": {"type": "object", "required": ["id", "status"], "properties": {"id": {"type": "number", "example": 2.0, "description": "A unique id generated when you place a stock order.<br/> maxValue: **********<br>maxLength: 20</br>", "allowEmptyValue": false}, "operationreasons": {"type": "string", "description": "The reason why you approve or reject the request. When you reject the request, this field is required. Otherwise, it's optional. <br/> maxLength: 140", "allowEmptyValue": true}, "status": {"type": "string", "example": 1, "description": "The status of the stock order. <br/> Possible values: 1 - approve, 2 - reject  <br/> maxLength: 20", "allowEmptyValue": false}}, "title": "UpdateOrderStatusModel"}, "UpdateSettlementAccountModel": {"type": "object", "required": ["newsettleaccountnumber", "stkaccountnumber"], "properties": {"newsettleaccountnumber": {"type": "string", "example": "***********************", "description": "The new settlement account, to which you’d like to change the current one. <br/> maxLength: 34", "allowEmptyValue": false}, "stkaccountnumber": {"type": "string", "example": "***********************", "description": "A stock account number. <br/> maxLength: 34", "allowEmptyValue": false}}, "title": "UpdateSettlementAccountModel"}}}