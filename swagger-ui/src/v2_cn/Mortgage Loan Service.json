{"swagger": "2.0", "info": {"description": "该服务模块主要包含了所有与房贷业务相关的API。例如房贷申请，房贷还款，房贷还款计划详情等。", "version": "1.0", "title": "房贷服务", "contact": {"name": "admin:<EMAIL>"}}, "schemes": ["https"], "host": "simnectz.hsu.edu.hk/lbsgateway/loan-experience", "basePath": "/", "tags": [{"name": "Loan Application", "description": "房贷申请，产品和报价。"}, {"name": "Account Information", "description": "房贷账户信息。"}, {"name": "Loan Contract", "description": "房贷合同信息。"}, {"name": "Transaction", "description": "房贷处理，包括查询和还款。"}], "paths": {"/mortgage/accountDetailEnquiry": {"post": {"tags": ["Account Information"], "summary": "该API用于 查询房贷账户的详细内容。", "description": "例如账户的持有人基础信息，及账户状态，绑定的还款账户等信息。", "operationId": "accountDetailEnquiryUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "颁发给第三方应用程序的最新客户授权令牌，以JWT格式访问该银行的客户数据。当开发者在调用API访问银行客户数据的时候，都需要用到该令牌。<br>最大长度: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "为前后端的每个请求唯一生成的128位随机UUID格式的消息ID。 该参数为内部参数，在调用API的时候不用修改该参数的内容。直接使用即可。<br>最大长度: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "当用户登录的时候生成的客户id。  该参数为内部参数，在调用API的时候不用修改该参数的内容。直接使用即可。<br>最大长度: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "ase", "description": "ase", "required": true, "schema": {"$ref": "#/definitions/MortgageAccountNumberModel"}}], "responses": {"200": {"description": "查询成功。 （由Get方法返回）", "schema": {"$ref": "#/definitions/ResultUtil"}}, "201": {"description": "正常执行，请求成功。（由Post方法返回）", "schema": {"$ref": "#/definitions/ResultUtil"}}, "403": {"description": "令牌的作用范围不正确或违反了安全策略。操作：请检查您是否使用合法授权用户帐户的正确令牌。"}, "404": {"description": "请求的帐户不存在。操作：请确保您输入的帐号和帐户类型正确。"}, "500": {"description": "API网关或微服务出现问题。操作：检查您的网络，稍后再试。"}}}}, "/mortgage/allContractsRetrieval": {"post": {"tags": ["Loan Contract"], "summary": "该API用于查询某个银行客户的房贷账户下面的所有房贷记录。", "description": "例如，客户的某个房贷账户中有3笔房贷，那么就可以通过这个API来查询到3笔贷款记录，并且可以看到每笔贷款的详情内容，如贷款总金额，贷款利率，贷款总利息等情况。", "operationId": "retrieveContractsUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "颁发给第三方应用程序的最新客户授权令牌，以JWT格式访问该银行的客户数据。当开发者在调用API访问银行客户数据的时候，都需要用到该令牌。<br>最大长度: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "为前后端的每个请求唯一生成的128位随机UUID格式的消息ID。 该参数为内部参数，在调用API的时候不用修改该参数的内容。直接使用即可。<br>最大长度: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "当用户登录的时候生成的客户id。  该参数为内部参数，在调用API的时候不用修改该参数的内容。直接使用即可。<br>最大长度: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "mortgageLoanContractPreModel", "description": "mortgageLoanContractPreModel", "required": true, "schema": {"$ref": "#/definitions/MortgageLoanContractModel"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ResultUtil"}}}}}, "/mortgage/contractRetrieval": {"post": {"tags": ["Loan Contract"], "summary": "该API用于查询某个银行客户的某笔贷款的详情内容。", "description": "例如，客户有2笔房贷记录，调用该API可以输入某一笔房贷合同编号，完成该笔贷款的详情查询。", "operationId": "retrieveContractUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "颁发给第三方应用程序的最新客户授权令牌，以JWT格式访问该银行的客户数据。当开发者在调用API访问银行客户数据的时候，都需要用到该令牌。<br>最大长度: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "为前后端的每个请求唯一生成的128位随机UUID格式的消息ID。 该参数为内部参数，在调用API的时候不用修改该参数的内容。直接使用即可。<br>最大长度: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "当用户登录的时候生成的客户id。  该参数为内部参数，在调用API的时候不用修改该参数的内容。直接使用即可。<br>最大长度: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "loanContractReqModel", "description": "loanContractReqModel", "required": true, "schema": {"$ref": "#/definitions/LoanContractReqModel"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ResultUtil"}}}}}, "/mortgage/loanCalculator": {"post": {"tags": ["Loan Application"], "summary": "该API用于房贷计算器开发。", "description": "客户在贷款之前，可以做价格试算。看看根据自己的薪资能获得多少贷款，以及还款金额利息等如何计算。", "operationId": "loanCalculaterUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "颁发给第三方应用程序的最新客户授权令牌，以JWT格式访问该银行的客户数据。当开发者在调用API访问银行客户数据的时候，都需要用到该令牌。<br>最大长度: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "为前后端的每个请求唯一生成的128位随机UUID格式的消息ID。 该参数为内部参数，在调用API的时候不用修改该参数的内容。直接使用即可。<br>最大长度: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "当用户登录的时候生成的客户id。  该参数为内部参数，在调用API的时候不用修改该参数的内容。直接使用即可。<br>最大长度: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "ase", "description": "ase", "required": true, "schema": {"$ref": "#/definitions/MortgageCalculatorModel"}}], "responses": {"200": {"description": "查询成功。 （由Get方法返回）", "schema": {"$ref": "#/definitions/ResultUtil"}}, "201": {"description": "正常执行，请求成功。（由Post方法返回）", "schema": {"$ref": "#/definitions/ResultUtil"}}, "403": {"description": "令牌的作用范围不正确或违反了安全策略。操作：请检查您是否使用合法授权用户帐户的正确令牌。"}, "404": {"description": "请求的帐户不存在。操作：请确保您输入的帐号和帐户类型正确。"}, "500": {"description": "API网关或微服务出现问题。操作：检查您的网络，稍后再试。"}}}}, "/mortgage/mortgageLoanApplication": {"post": {"tags": ["Loan Application"], "summary": "该API用于房贷的申请业务。", "description": "当用户想要申请贷款的时候，就可以调用该API来完成。 在贷款之前，可能需要先调用/mortgage​/loanCalculator 去在房贷计算器上面做个贷款金额及还款利息等的试算。", "operationId": "mortgageLoanApplicationUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "颁发给第三方应用程序的最新客户授权令牌，以JWT格式访问该银行的客户数据。当开发者在调用API访问银行客户数据的时候，都需要用到该令牌。<br>最大长度: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "为前后端的每个请求唯一生成的128位随机UUID格式的消息ID。 该参数为内部参数，在调用API的时候不用修改该参数的内容。直接使用即可。<br>最大长度: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "当用户登录的时候生成的客户id。  该参数为内部参数，在调用API的时候不用修改该参数的内容。直接使用即可。<br>最大长度: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "ase", "description": "ase", "required": true, "schema": {"$ref": "#/definitions/MortgageLoanApplicationModel"}}], "responses": {"200": {"description": "查询成功。 （由Get方法返回）", "schema": {"$ref": "#/definitions/ResultUtil"}}, "201": {"description": "正常执行，请求成功。（由Post方法返回）", "schema": {"$ref": "#/definitions/ResultUtil"}}, "403": {"description": "令牌的作用范围不正确或违反了安全策略。操作：请检查您是否使用合法授权用户帐户的正确令牌。"}, "404": {"description": "请求的帐户不存在。操作：请确保您输入的帐号和帐户类型正确。"}, "500": {"description": "API网关或微服务出现问题。操作：检查您的网络，稍后再试。"}}}}, "/mortgage/nextRepaymentEnquiry": {"post": {"tags": ["Transaction"], "summary": "该API用于获取下期还款的详情。", "description": "当客户在每一次还款之后，都可以预览下期还款的具体情况。", "operationId": "nextRepaymentEnquiryUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "颁发给第三方应用程序的最新客户授权令牌，以JWT格式访问该银行的客户数据。当开发者在调用API访问银行客户数据的时候，都需要用到该令牌。<br>最大长度: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "为前后端的每个请求唯一生成的128位随机UUID格式的消息ID。 该参数为内部参数，在调用API的时候不用修改该参数的内容。直接使用即可。<br>最大长度: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "当用户登录的时候生成的客户id。  该参数为内部参数，在调用API的时候不用修改该参数的内容。直接使用即可。<br>最大长度: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "ase", "description": "ase", "required": true, "schema": {"$ref": "#/definitions/AccountContractNumberModel"}}], "responses": {"200": {"description": "查询成功。 （由Get方法返回）", "schema": {"$ref": "#/definitions/ResultUtil"}}, "201": {"description": "正常执行，请求成功。（由Post方法返回）", "schema": {"$ref": "#/definitions/ResultUtil"}}, "403": {"description": "令牌的作用范围不正确或违反了安全策略。操作：请检查您是否使用合法授权用户帐户的正确令牌。"}, "404": {"description": "请求的帐户不存在。操作：请确保您输入的帐号和帐户类型正确。"}, "500": {"description": "API网关或微服务出现问题。操作：检查您的网络，稍后再试。"}}}}, "/mortgage/overDueRepaymentEnquiry": {"post": {"tags": ["Transaction"], "summary": "该API用于查询贷款用户的所有逾期房贷记录。", "description": "通过输入客户的贷款账户号码和贷款合同编号，就能获取到该笔贷款中，所有的逾期记录。 ", "operationId": "overDueRepaymentEnquiryUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "颁发给第三方应用程序的最新客户授权令牌，以JWT格式访问该银行的客户数据。当开发者在调用API访问银行客户数据的时候，都需要用到该令牌。<br>最大长度: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "为前后端的每个请求唯一生成的128位随机UUID格式的消息ID。 该参数为内部参数，在调用API的时候不用修改该参数的内容。直接使用即可。<br>最大长度: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "当用户登录的时候生成的客户id。  该参数为内部参数，在调用API的时候不用修改该参数的内容。直接使用即可。<br>最大长度: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "ase", "description": "ase", "required": true, "schema": {"$ref": "#/definitions/AccountContractNumberModel"}}], "responses": {"200": {"description": "查询成功。 （由Get方法返回）", "schema": {"$ref": "#/definitions/ResultUtil"}}, "201": {"description": "正常执行，请求成功。（由Post方法返回）", "schema": {"$ref": "#/definitions/ResultUtil"}}, "403": {"description": "令牌的作用范围不正确或违反了安全策略。操作：请检查您是否使用合法授权用户帐户的正确令牌。"}, "404": {"description": "请求的帐户不存在。操作：请确保您输入的帐号和帐户类型正确。"}, "500": {"description": "API网关或微服务出现问题。操作：检查您的网络，稍后再试。"}}}}, "/mortgage/repayment": {"post": {"tags": ["Transaction"], "summary": "该API用于房贷还款。", "description": "当客户申请房贷之后，需要按期还款，就可以调用该API来完成该功能的开发。 在还款之前，可以查看自己需要还的具体金额，可以调用/mortgage/nextRepaymentEnquiry。", "operationId": "repaymentUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "颁发给第三方应用程序的最新客户授权令牌，以JWT格式访问该银行的客户数据。当开发者在调用API访问银行客户数据的时候，都需要用到该令牌。<br>最大长度: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "为前后端的每个请求唯一生成的128位随机UUID格式的消息ID。 该参数为内部参数，在调用API的时候不用修改该参数的内容。直接使用即可。<br>最大长度: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "当用户登录的时候生成的客户id。  该参数为内部参数，在调用API的时候不用修改该参数的内容。直接使用即可。<br>最大长度: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "ase", "description": "ase", "required": true, "schema": {"$ref": "#/definitions/RepaymentModel"}}], "responses": {"200": {"description": "查询成功。 （由Get方法返回）", "schema": {"$ref": "#/definitions/ResultUtil"}}, "201": {"description": "正常执行，请求成功。（由Post方法返回）", "schema": {"$ref": "#/definitions/ResultUtil"}}, "403": {"description": "令牌的作用范围不正确或违反了安全策略。操作：请检查您是否使用合法授权用户帐户的正确令牌。"}, "404": {"description": "请求的帐户不存在。操作：请确保您输入的帐号和帐户类型正确。"}, "500": {"description": "API网关或微服务出现问题。操作：检查您的网络，稍后再试。"}}}}, "/mortgage/repaymentPlanRetrieval": {"post": {"tags": ["Transaction"], "summary": "该API用于查询客户某笔贷款的所有还款计划详情。", "description": "例如，客户贷款100万，分10年按月还款，那么需要还120期，可以通过这个API查看每期的应还金额及利息等信息。", "operationId": "repaymentPlanUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "颁发给第三方应用程序的最新客户授权令牌，以JWT格式访问该银行的客户数据。当开发者在调用API访问银行客户数据的时候，都需要用到该令牌。<br>最大长度: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "为前后端的每个请求唯一生成的128位随机UUID格式的消息ID。 该参数为内部参数，在调用API的时候不用修改该参数的内容。直接使用即可。<br>最大长度: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "当用户登录的时候生成的客户id。  该参数为内部参数，在调用API的时候不用修改该参数的内容。直接使用即可。<br>最大长度: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "ase", "description": "ase", "required": true, "schema": {"$ref": "#/definitions/AccountContractNumberModel"}}], "responses": {"200": {"description": "查询成功。 （由Get方法返回）", "schema": {"$ref": "#/definitions/ResultUtil"}}, "201": {"description": "正常执行，请求成功。（由Post方法返回）", "schema": {"$ref": "#/definitions/ResultUtil"}}, "403": {"description": "令牌的作用范围不正确或违反了安全策略。操作：请检查您是否使用合法授权用户帐户的正确令牌。"}, "404": {"description": "请求的帐户不存在。操作：请确保您输入的帐号和帐户类型正确。"}, "500": {"description": "API网关或微服务出现问题。操作：检查您的网络，稍后再试。"}}}}, "/mortgage/transactionEnquiry": {"post": {"tags": ["Transaction"], "summary": "该API用于查询某一笔房贷中所有的历史交易记录", "description": "比如还贷款的交易记录。", "operationId": "transactionEnquiryUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "颁发给第三方应用程序的最新客户授权令牌，以JWT格式访问该银行的客户数据。当开发者在调用API访问银行客户数据的时候，都需要用到该令牌。<br>最大长度: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "为前后端的每个请求唯一生成的128位随机UUID格式的消息ID。 该参数为内部参数，在调用API的时候不用修改该参数的内容。直接使用即可。<br>最大长度: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "当用户登录的时候生成的客户id。  该参数为内部参数，在调用API的时候不用修改该参数的内容。直接使用即可。<br>最大长度: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "ase", "description": "ase", "required": true, "schema": {"$ref": "#/definitions/TransactionRequestModel"}}], "responses": {"200": {"description": "查询成功。 （由Get方法返回）", "schema": {"$ref": "#/definitions/ResultUtil"}}, "201": {"description": "正常执行，请求成功。（由Post方法返回）", "schema": {"$ref": "#/definitions/ResultUtil"}}, "403": {"description": "令牌的作用范围不正确或违反了安全策略。操作：请检查您是否使用合法授权用户帐户的正确令牌。"}, "404": {"description": "请求的帐户不存在。操作：请确保您输入的帐号和帐户类型正确。"}, "500": {"description": "API网关或微服务出现问题。操作：检查您的网络，稍后再试。"}}}}}, "definitions": {"AccountContractNumberModel": {"type": "object", "required": ["accountnumber", "contractnumber"], "properties": {"accountnumber": {"type": "string", "example": "***********************", "description": "贷款账户号码。<br>最大长度: 23</br>", "allowEmptyValue": false}, "contractnumber": {"type": "string", "example": "*********", "description": "客户申请房贷成功之后，系统会自动生成一个贷款编号。每一个贷款都是银行和客户的一个合同，因此也叫做合同编号。<br>最大长度: 50</br>", "allowEmptyValue": false}}, "title": "AccountContractNumberModel"}, "LoanContractReqModel": {"type": "object", "required": ["accountnumber", "contractnumber"], "properties": {"accountnumber": {"type": "string", "example": "***********************", "description": "贷款账户号码。<br>最大长度: 23</br>", "allowEmptyValue": false}, "contractnumber": {"type": "string", "example": "*********", "description": "客户申请房贷成功之后，系统会自动生成一个贷款编号。每一个贷款都是银行和客户的一个合同，因此也叫做合同编号。<br>最大长度: 50</br>", "allowEmptyValue": false}}, "title": "LoanContractReqModel"}, "MortgageAccountNumberModel": {"type": "object", "required": ["accountnumber"], "properties": {"accountnumber": {"type": "string", "example": "***********************", "description": "贷款账户号码。<br>最大长度: 23</br>", "allowEmptyValue": false}}, "title": "MortgageAccountNumberModel"}, "MortgageCalculatorModel": {"type": "object", "required": ["ccyCode", "loanPeriod", "monthlysalary"], "properties": {"ccyCode": {"type": "string", "example": "HKD", "description": "交易的货币类型。 支持 HKD。<br>最大长度: 3</br>", "allowEmptyValue": false}, "loanPeriod": {"type": "string", "example": 5, "description": "贷款年限。支持1-30的整数。<br>最大长度: 30</br>", "allowEmptyValue": false}, "monthlysalary": {"type": "number", "example": 10000.0, "description": "月薪。<br>最大长度: 18</br>", "allowEmptyValue": false}}, "title": "MortgageCalculatorModel"}, "MortgageLoanApplicationModel": {"type": "object", "required": ["accountnumber", "borringneeds", "ccyCode", "debitaccountnumber", "loanScheme", "monthlysalary", "propertyAddressFormat", "propertyClassification", "propertyTransactionStatus", "propertyType", "propertyWithCarpark", "propertyWithGarden", "propertyWithRoof", "purchasePrice", "repaymentCycle", "repaymentPeriod", "repaymentPlan", "solicitorsContact<PERSON>erson", "solicitorsFaxNumber", "solicitorsFirm", "solicitorsPhoneNumber"], "properties": {"accountnumber": {"type": "string", "example": "***********************", "description": "贷款账户号码。<br>最大长度: 23</br>", "allowEmptyValue": false}, "borringneeds": {"type": "number", "example": 10000.0, "description": "需要贷款的总金额。<br>最大长度: 18</br>", "allowEmptyValue": false}, "ccyCode": {"type": "string", "example": "HKD", "description": "交易的货币类型。 支持 HKD。<br>最大长度: 3</br>", "allowEmptyValue": false}, "debitaccountnumber": {"type": "string", "example": "***********************", "description": "和该贷款账户关联的储蓄账户。如001结尾的储蓄账户和002结尾的活期账户。当贷款申请成功后，系统会把贷款的钱转到这个绑定的账户上。如果要还款的时候，系统会从这个绑定的账户上面扣款。<br>最大长度: 23</br>", "allowEmptyValue": false}, "loanScheme": {"type": "string", "example": "F", "description": "贷款方案。支持的值： F-表示固定利率计划。<br>最大长度: 1</br>", "allowEmptyValue": false}, "monthlysalary": {"type": "number", "example": 10000.0, "description": "月薪。<br>最大长度: 18</br>", "allowEmptyValue": false}, "propertyAddressFormat": {"type": "string", "example": "S", "description": "物业地址格式。支持的值：S-表示结构化地址，F-自由格式<br>最大长度: 1</br>", "allowEmptyValue": false}, "propertyAddressLine1": {"type": "string", "description": "地址第1行<br>最大长度: 40</br>", "allowEmptyValue": true}, "propertyAddressLine2": {"type": "string", "description": "地址第2行<br>最大长度: 40</br>", "allowEmptyValue": true}, "propertyAddressLine3": {"type": "string", "description": "地址第3行<br>最大长度: 40</br>", "allowEmptyValue": true}, "propertyAddressLine4": {"type": "string", "description": "地址第4行<br>最大长度: 40</br>", "allowEmptyValue": true}, "propertyAreaCode": {"type": "string", "example": "0852", "description": "物业地区号。例如0852代表香港。 <br>最大长度: 4</br>", "allowEmptyValue": true}, "propertyBlock": {"type": "string", "example": "A", "description": "座<br>最大长度: 5</br>", "allowEmptyValue": true}, "propertyBuilding": {"type": "string", "description": "楼号<br>最大长度: 35</br>", "allowEmptyValue": true}, "propertyCarparkNumber": {"type": "string", "example": "A12345", "description": "车位号码。 如果有车位，输入车位号码；如果没有，保持为空。 <br>最大长度: 10</br>", "allowEmptyValue": true}, "propertyClassification": {"type": "string", "example": "F", "description": "物业分类。<br>F -一手楼<br>S - 二手楼<br>最大长度: 1</br>", "allowEmptyValue": false}, "propertyCountry": {"type": "string", "example": "CN", "description": "物业所在国家。<br>最大长度: 2</br>", "allowEmptyValue": true}, "propertyEstate": {"type": "string", "description": "屋邨<br>最大长度: 35</br>", "allowEmptyValue": true}, "propertyFlat": {"type": "string", "example": 73, "description": "号 <br>最大长度: 5</br>", "allowEmptyValue": true}, "propertyFloor": {"type": "string", "example": 19, "description": "层<br>最大长度: 3</br>", "allowEmptyValue": true}, "propertyMonthlyRental": {"type": "number", "example": 10000.0, "description": "物业月租 <br>最大长度: 18</br>", "allowEmptyValue": true}, "propertyPostCode": {"type": "string", "example": 710000, "description": "邮编<br>最大长度: 8</br>", "allowEmptyValue": true}, "propertyRoom": {"type": "string", "example": 1908, "description": "室<br>最大长度: 5</br>", "allowEmptyValue": true}, "propertyStreetName": {"type": "string", "description": "街名<br>最大长度: 35</br>", "allowEmptyValue": true}, "propertyStreetNumber": {"type": "string", "example": "S437", "description": "街号<br>最大长度: 5</br>", "allowEmptyValue": true}, "propertyTransactionStatus": {"type": "string", "example": "T", "description": "状态: V - 空闲, T - 房屋租赁合同 <br>最大长度: 1</br>", "allowEmptyValue": false}, "propertyType": {"type": "string", "example": "R", "description": "物业类别<br>R - 住宅<br>C - 车位<br>V - 别墅<br>T - 村屋<br>O - 写字楼<br>S - 铺位<br>I - 工业<br>H - 独立屋<br>最大长度: 1</br>", "allowEmptyValue": false}, "propertyWithCarpark": {"type": "string", "example": "Y", "description": "是否带车位。 Y- 是, N-不是<br>最大长度: 1</br>", "allowEmptyValue": false}, "propertyWithGarden": {"type": "string", "example": "Y", "description": "是否带花园。 Y- 是, N-不是<br>最大长度: 1</br>", "allowEmptyValue": false}, "propertyWithRoof": {"type": "string", "example": "Y", "description": "是否带天台。Y- 是, N-不是<br>最大长度: 1</br>", "allowEmptyValue": false}, "purchasePrice": {"type": "number", "example": 1000000.0, "description": "买入价格。<br>最大长度: 18</br>", "allowEmptyValue": false}, "repaymentCycle": {"type": "string", "example": "M", "description": "贷款还款周期。支持的值：M-表示按月还款， B-表示双周还款。<br>最大长度: 1</br>", "allowEmptyValue": false}, "repaymentPeriod": {"type": "string", "example": 5, "description": "还款年限。<br>最大长度: 5</br>", "allowEmptyValue": false}, "repaymentPlan": {"type": "string", "example": "L", "description": "还款计划。 支持的值： L-等额本息计划<br>最大长度: 1</br>", "allowEmptyValue": false}, "solicitorsContactPerson": {"type": "string", "description": "律师行联系人<br>最大长度: 140</br>", "allowEmptyValue": false}, "solicitorsFaxNumber": {"type": "string", "description": "律师行传真<br>最大长度: 34</br>", "allowEmptyValue": false}, "solicitorsFirm": {"type": "string", "description": "律师行名称<br>最大长度: 140</br>", "allowEmptyValue": false}, "solicitorsPhoneNumber": {"type": "string", "description": "律师行电话<br>最大长度: 34</br>", "allowEmptyValue": false}}, "title": "MortgageLoanApplicationModel"}, "MortgageLoanContractModel": {"type": "object", "required": ["accountnumber"], "properties": {"accountnumber": {"type": "string", "example": "***********************", "description": "贷款账户号码。<br>最大长度: 23</br>", "allowEmptyValue": false}}, "title": "MortgageLoanContractModel"}, "RepaymentModel": {"type": "object", "required": ["accountnumber", "contractnumber", "repaymentaccountnumber", "repaymentamount"], "properties": {"accountnumber": {"type": "string", "example": "***********************", "description": "贷款账户号码。<br>最大长度: 23</br>", "allowEmptyValue": false}, "contractnumber": {"type": "string", "example": "*********", "description": "客户申请房贷成功之后，系统会自动生成一个贷款编号。每一个贷款都是银行和客户的一个合同，因此也叫做合同编号。<br>最大长度: 50</br>", "allowEmptyValue": false}, "repaymentaccountnumber": {"type": "string", "example": "***********************", "description": "和该贷款账户关联的储蓄账户。如001结尾的储蓄账户和002结尾的活期账户。当如果要还款的时候，系统会从这个绑定的账户上面扣款。<br>最大长度: 23</br>", "allowEmptyValue": false}, "repaymentamount": {"type": "number", "example": 269.19, "description": "还款金额。<br>最大长度: 18</br>", "allowEmptyValue": false}}, "title": "RepaymentModel"}, "ResultUtil": {"type": "object", "properties": {"code": {"type": "string"}, "data": {"type": "object"}, "msg": {"type": "string"}}, "title": "ResultUtil"}, "TransactionRequestModel": {"type": "object", "required": ["accountnumber", "contractnumber"], "properties": {"accountnumber": {"type": "string", "example": "***********************", "description": "贷款账户号码。<br>最大长度: 23</br>", "allowEmptyValue": false}, "contractnumber": {"type": "string", "example": "*********", "description": "客户申请房贷成功之后，系统会自动生成一个贷款编号。每一个贷款都是银行和客户的一个合同，因此也叫做合同编号。<br>最大长度: 50</br>", "allowEmptyValue": false}, "transFromDate": {"type": "number", "example": *************.0, "description": "交易记录开始时间。<br>最大长度: 20</br>", "allowEmptyValue": true}, "transToDate": {"type": "number", "example": *************.0, "description": "交易记录结束时间。<br>最大长度: 20</br>", "allowEmptyValue": true}}, "title": "TransactionRequestModel"}}}