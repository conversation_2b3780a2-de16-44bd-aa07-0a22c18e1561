{"swagger": "2.0", "info": {"description": "该服务模块主要包含了所有基金交易相关的业务API。包含了基金列表，基金市场信息，基金买入， 基金卖出，基金持仓查询及基金交易记录查询。当您开发基金相关的业务模块时，可以在这这里找到对应的API来完成不同业务功能。", "version": "1.0", "title": "基金业务", "contact": {"name": "<EMAIL>"}}, "schemes": ["https"], "host": "simnectz.hsu.edu.hk/lbsgateway/fund-experience", "basePath": "/", "tags": [{"name": "Market Information", "description": "查询并显示最新的基金市场信息。"}, {"name": "Order & Simulation", "description": "基金订单管理和基金公司模拟。"}, {"name": "Account Information & Maintenance", "description": "基金账户持仓信息和账户维护。"}, {"name": "Transaction", "description": "客户基金账户中的交易处理。"}], "paths": {"/fund/fundHoldingEnquiry": {"post": {"tags": ["Account Information & Maintenance"], "summary": "该API用于查询基金账户的持仓详情。", "description": "可以查询客户的基金账户下，对于某个基金的持仓信息和该基金账户下所有的基金持仓信息。如果客户还没有任何持仓信息，可能需要先买入基金，可以调用/fund/order/subscriptionOrderPlacing。", "operationId": "fundHoldingEnquiryUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "颁发给第三方应用程序的最新客户授权令牌，以JWT格式访问该银行的客户数据。当开发者在调用API访问银行客户数据的时候，都需要用到该令牌。<br>最大长度: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "为前后端的每个请求唯一生成的128位随机UUID格式的消息ID。 该参数为内部参数，在调用API的时候不用修改该参数的内容。直接使用即可。<br>最大长度: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "当用户登录的时候生成的客户id。  该参数为内部参数，在调用API的时候不用修改该参数的内容。直接使用即可。<br>最大长度: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "fundHoldingEntityModel", "description": "fundHoldingEntityModel", "required": true, "schema": {"$ref": "#/definitions/FundHoldingEnquiryModel"}}], "responses": {"200": {"description": "查询成功。 （由Get方法返回）", "schema": {"$ref": "#/definitions/ResultUtil«FundHoldInfoModel»"}}, "201": {"description": "正常执行，请求成功。（由Post方法返回）", "schema": {"$ref": "#/definitions/ResultUtil«FundHoldInfoModel»"}}, "403": {"description": "令牌的作用范围不正确或违反了安全策略。操作：请检查您是否使用合法授权用户帐户的正确令牌。"}, "404": {"description": "请求的帐户不存在。操作：请确保您输入的帐号和帐户类型正确。"}, "500": {"description": "API网关或微服务出现问题。操作：检查您的网络，稍后再试。"}}}}, "/fund/fundList": {"post": {"tags": ["Market Information"], "summary": "该API用于获取市场上的所有基金列表及所有基金详情。", "description": "当想要展示基金列表及每个基金的市场信息时，如基金价格，成交量等，可以调用该API。", "operationId": "fundListUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "颁发给第三方应用程序的最新客户授权令牌，以JWT格式访问该银行的客户数据。当开发者在调用API访问银行客户数据的时候，都需要用到该令牌。<br>最大长度: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "为前后端的每个请求唯一生成的128位随机UUID格式的消息ID。 该参数为内部参数，在调用API的时候不用修改该参数的内容。直接使用即可。<br>最大长度: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "当用户登录的时候生成的客户id。  该参数为内部参数，在调用API的时候不用修改该参数的内容。直接使用即可。<br>最大长度: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "queryFundListModel", "description": "queryFundListModel", "required": true, "schema": {"$ref": "#/definitions/QueryFundListModel"}}], "responses": {"200": {"description": "查询成功。 （由Get方法返回）", "schema": {"$ref": "#/definitions/ResultUtil«List«FundMarketInfoModel»»"}}, "201": {"description": "正常执行，请求成功。（由Post方法返回）", "schema": {"$ref": "#/definitions/ResultUtil«List«FundMarketInfoModel»»"}}, "403": {"description": "令牌的作用范围不正确或违反了安全策略。操作：请检查您是否使用合法授权用户帐户的正确令牌。"}, "404": {"description": "请求的帐户不存在。操作：请确保您输入的帐号和帐户类型正确。"}, "500": {"description": "API网关或微服务出现问题。操作：检查您的网络，稍后再试。"}}}}, "/fund/fundQuotation": {"post": {"tags": ["Market Information"], "summary": "该API用于查询某只基金的市场信息。", "description": "例如，当客户想要查看代码为U00001的基金的价格，类型，发行公司等信息，都可以调用该API来完成查询。如果客户想要查询市场所有的基金及市场信息，可以调用/fund/fundList 来完成查询。", "operationId": "fundQuotationUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "颁发给第三方应用程序的最新客户授权令牌，以JWT格式访问该银行的客户数据。当开发者在调用API访问银行客户数据的时候，都需要用到该令牌。<br>最大长度: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "为前后端的每个请求唯一生成的128位随机UUID格式的消息ID。 该参数为内部参数，在调用API的时候不用修改该参数的内容。直接使用即可。<br>最大长度: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "当用户登录的时候生成的客户id。  该参数为内部参数，在调用API的时候不用修改该参数的内容。直接使用即可。<br>最大长度: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "fundCodeModel", "description": "fundCodeModel", "required": true, "schema": {"$ref": "#/definitions/FundCodeModel"}}], "responses": {"200": {"description": "查询成功。 （由Get方法返回）", "schema": {"$ref": "#/definitions/ResultUtil«FundCodeModel»"}}, "201": {"description": "正常执行，请求成功。（由Post方法返回）", "schema": {"$ref": "#/definitions/ResultUtil«FundCodeModel»"}}, "403": {"description": "令牌的作用范围不正确或违反了安全策略。操作：请检查您是否使用合法授权用户帐户的正确令牌。"}, "404": {"description": "请求的帐户不存在。操作：请确保您输入的帐号和帐户类型正确。"}, "500": {"description": "API网关或微服务出现问题。操作：检查您的网络，稍后再试。"}}}}, "/fund/settlementAccountUpdate": {"post": {"tags": ["Account Information & Maintenance"], "summary": "该API用于设置基金账户的关联储蓄账户。", "description": "例如，当银行客户想要买一只基金时，首先这个用户需要有一个基金账户，然后再给这个基金账户绑定一个储蓄账户或活期账户（001结尾或者002结尾的账户）。当客户买入100份额的某只基金，需要花费1000HKD的时候，那么系统就会从绑定的这个储蓄账户中或活期账户直接扣掉1000HKD，与此同时，这个基金账户的持仓中就会多出这100份的基金份额。同理，当客户卖出这100份的基金，卖出所得1200HKD的时候，那么绑定的储蓄账户或活期账户就会收到这个1200HKD，与此同时，该基金账户的持仓中，就会减少这100份额的基金。在绑定一个储蓄账户或者活期账户之前，您可以需要先调用/deposit/account/allAccounts/{customerNumber}/{index}/{items}来查询该银行客户所持有的其他账户，是否有需要的001或002结尾的账户类型。如果没有的话，那么您就需要先调用/deposit/account/accountCreation去创建一个001或者002的账户。", "operationId": "settlementAccountUpdateUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "颁发给第三方应用程序的最新客户授权令牌，以JWT格式访问该银行的客户数据。当开发者在调用API访问银行客户数据的时候，都需要用到该令牌。<br>最大长度: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "为前后端的每个请求唯一生成的128位随机UUID格式的消息ID。 该参数为内部参数，在调用API的时候不用修改该参数的内容。直接使用即可。<br>最大长度: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "当用户登录的时候生成的客户id。  该参数为内部参数，在调用API的时候不用修改该参数的内容。直接使用即可。<br>最大长度: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "updateSettlementAccountModel", "description": "updateSettlementAccountModel", "required": true, "schema": {"$ref": "#/definitions/UpdateSettlementAccountModel"}}], "responses": {"200": {"description": "查询成功。 （由Get方法返回）", "schema": {"$ref": "#/definitions/ResultUtil"}}, "201": {"description": "正常执行，请求成功。（由Post方法返回）", "schema": {"$ref": "#/definitions/ResultUtil"}}, "403": {"description": "令牌的作用范围不正确或违反了安全策略。操作：请检查您是否使用合法授权用户帐户的正确令牌。"}, "404": {"description": "请求的帐户不存在。操作：请确保您输入的帐号和帐户类型正确。"}, "500": {"description": "API网关或微服务出现问题。操作：检查您的网络，稍后再试。"}}}}, "/fund/transactionRetrieval": {"post": {"tags": ["Transaction"], "summary": "该API用于查询某个客户的基金账户的历史交易记录。", "description": "例如，客户用基金账户购买过很多只基金，那么就可以在这里调用相应的API查询某段时间内，某只基金的交易记录，或者该账户在某段时间内的所有交易记录。", "operationId": "retrieveFundTransactionUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "颁发给第三方应用程序的最新客户授权令牌，以JWT格式访问该银行的客户数据。当开发者在调用API访问银行客户数据的时候，都需要用到该令牌。<br>最大长度: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "为前后端的每个请求唯一生成的128位随机UUID格式的消息ID。 该参数为内部参数，在调用API的时候不用修改该参数的内容。直接使用即可。<br>最大长度: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "当用户登录的时候生成的客户id。  该参数为内部参数，在调用API的时候不用修改该参数的内容。直接使用即可。<br>最大长度: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "queryFundTransModel", "description": "queryFundTransModel", "required": true, "schema": {"$ref": "#/definitions/QueryFundTransModel"}}], "responses": {"200": {"description": "查询成功。 （由Get方法返回）", "schema": {"$ref": "#/definitions/ResultUtil«List«FundPlatformLogModel»»"}}, "201": {"description": "正常执行，请求成功。（由Post方法返回）", "schema": {"$ref": "#/definitions/ResultUtil«List«FundPlatformLogModel»»"}}, "403": {"description": "令牌的作用范围不正确或违反了安全策略。操作：请检查您是否使用合法授权用户帐户的正确令牌。"}, "404": {"description": "请求的帐户不存在。操作：请确保您输入的帐号和帐户类型正确。"}, "500": {"description": "API网关或微服务出现问题。操作：检查您的网络，稍后再试。"}}}}, "/fund/order/cancellation": {"post": {"tags": ["Order & Simulation"], "summary": "该API用于基金买卖过程中撤单操作。", "description": "在客户基金买卖过程中，当客户挂单（即想要买入（调用/fund/order/subscriptionOrderPlacing）或者卖出（调用/fund/order/redemptionOrderPlacing）基金时，发起的申请）之后，在未实际成交之前，可以调用该API来取消挂单业务。例如，在基金交易的过程中，客户需要先用自己的基金账户发起买入5000HKD的基金挂单申请，在实际成交之前，可以调用/fund/order/cancellation取消挂单，那么本次交易请求就取消了；也可以调用/fund/order/orderChange 来修改挂单的内容，例如将5000HKD改为6000HKD； 然后再调用/fund/order​/simulatorUpdate API来模拟挂单交易成功还是失败，最终决定本次基金交易是否成功。如果不对挂单进行修改，则无需调用/fund/order/orderChange，直接就可以调用/fund/order​/simulatorUpdate API来模拟挂单交易成功还是失败。", "operationId": "cancellationUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "颁发给第三方应用程序的最新客户授权令牌，以JWT格式访问该银行的客户数据。当开发者在调用API访问银行客户数据的时候，都需要用到该令牌。<br>最大长度: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "为前后端的每个请求唯一生成的128位随机UUID格式的消息ID。 该参数为内部参数，在调用API的时候不用修改该参数的内容。直接使用即可。<br>最大长度: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "当用户登录的时候生成的客户id。  该参数为内部参数，在调用API的时候不用修改该参数的内容。直接使用即可。<br>最大长度: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "idmodel", "description": "idmodel", "required": true, "schema": {"$ref": "#/definitions/IDModel"}}], "responses": {"200": {"description": "查询成功。 （由Get方法返回）", "schema": {"$ref": "#/definitions/ResultUtil"}}, "201": {"description": "正常执行，请求成功。（由Post方法返回）", "schema": {"$ref": "#/definitions/ResultUtil"}}, "403": {"description": "令牌的作用范围不正确或违反了安全策略。操作：请检查您是否使用合法授权用户帐户的正确令牌。"}, "404": {"description": "请求的帐户不存在。操作：请确保您输入的帐号和帐户类型正确。"}, "500": {"description": "API网关或微服务出现问题。操作：检查您的网络，稍后再试。"}}}}, "/fund/order/orderChange": {"post": {"tags": ["Order & Simulation"], "summary": "该API用于修改挂单信息。", "description": "例如当用户想要买入或者卖出基金的时候，都需要先挂单。例如，客户想要买入1000HKD的基金时，就需要调用/fund/order/subscriptionOrderPlacing 来下单。这就是一个挂单的过程。在这个挂单实际成交之前，用户可以修改这个挂单的信息，比如想把1000HKD改成2000HKD，就可以调用该API来完成。但是如果挂单已经调用了/fund/order/simulatorUpdate模拟成交了， 那么就不能再被修改了。 ", "operationId": "orderInfoUpdateUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "颁发给第三方应用程序的最新客户授权令牌，以JWT格式访问该银行的客户数据。当开发者在调用API访问银行客户数据的时候，都需要用到该令牌。<br>最大长度: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "为前后端的每个请求唯一生成的128位随机UUID格式的消息ID。 该参数为内部参数，在调用API的时候不用修改该参数的内容。直接使用即可。<br>最大长度: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "当用户登录的时候生成的客户id。  该参数为内部参数，在调用API的时候不用修改该参数的内容。直接使用即可。<br>最大长度: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "orderInfoUpdateModel", "description": "orderInfoUpdateModel", "required": true, "schema": {"$ref": "#/definitions/OrderInfoUpdateModel"}}], "responses": {"200": {"description": "查询成功。 （由Get方法返回）", "schema": {"$ref": "#/definitions/ResultUtil"}}, "201": {"description": "正常执行，请求成功。（由Post方法返回）", "schema": {"$ref": "#/definitions/ResultUtil"}}, "403": {"description": "令牌的作用范围不正确或违反了安全策略。操作：请检查您是否使用合法授权用户帐户的正确令牌。"}, "404": {"description": "请求的帐户不存在。操作：请确保您输入的帐号和帐户类型正确。"}, "500": {"description": "API网关或微服务出现问题。操作：检查您的网络，稍后再试。"}}}}, "/fund/order/orderDetailRetrievalById": {"post": {"tags": ["Order & Simulation"], "summary": "该API用于某一个挂单记录的查询。", "description": "可以查询某个挂单的详情。例如，客户想要买入1000HKD的基金时，就需要调用/fund/order/subscriptionOrderPlacing 来下单。这就是一个挂单的过程。不管是买入或者卖出都要挂单，挂单之后，会返回一个参数id（即挂单的编号），然后可以调用/fund/order/orderDetailRetrievalById， 输入该编号，就可以获取到该挂单的详细信息了。", "operationId": "orderDetailRetrievalByIdUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "颁发给第三方应用程序的最新客户授权令牌，以JWT格式访问该银行的客户数据。当开发者在调用API访问银行客户数据的时候，都需要用到该令牌。<br>最大长度: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "为前后端的每个请求唯一生成的128位随机UUID格式的消息ID。 该参数为内部参数，在调用API的时候不用修改该参数的内容。直接使用即可。<br>最大长度: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "当用户登录的时候生成的客户id。  该参数为内部参数，在调用API的时候不用修改该参数的内容。直接使用即可。<br>最大长度: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "idmodel", "description": "idmodel", "required": true, "schema": {"$ref": "#/definitions/IDModel"}}], "responses": {"200": {"description": "查询成功。 （由Get方法返回）", "schema": {"$ref": "#/definitions/ResultUtil«FundOrderInfoModel»"}}, "201": {"description": "正常执行，请求成功。（由Post方法返回）", "schema": {"$ref": "#/definitions/ResultUtil«FundOrderInfoModel»"}}, "403": {"description": "令牌的作用范围不正确或违反了安全策略。操作：请检查您是否使用合法授权用户帐户的正确令牌。"}, "404": {"description": "请求的帐户不存在。操作：请确保您输入的帐号和帐户类型正确。"}, "500": {"description": "API网关或微服务出现问题。操作：检查您的网络，稍后再试。"}}}}, "/fund/order/orderRetrieval": {"post": {"tags": ["Order & Simulation"], "summary": "该API用于所有挂单记录的查询。", "description": "可以查询某个基金账户在某段时间内挂单的历史记录及详情。例如，客户想要买入1000HKD的基金时，就需要调用/fund/order/subscriptionOrderPlacing 来下单。这就是一个挂单的过程。不管是买入或者卖出都要挂单，挂单之后，可以调用该API去查看挂单历史记录。", "operationId": "orderRetrievalUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "颁发给第三方应用程序的最新客户授权令牌，以JWT格式访问该银行的客户数据。当开发者在调用API访问银行客户数据的时候，都需要用到该令牌。<br>最大长度: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "为前后端的每个请求唯一生成的128位随机UUID格式的消息ID。 该参数为内部参数，在调用API的时候不用修改该参数的内容。直接使用即可。<br>最大长度: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "当用户登录的时候生成的客户id。  该参数为内部参数，在调用API的时候不用修改该参数的内容。直接使用即可。<br>最大长度: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "orderRequestModel", "description": "orderRequestModel", "required": true, "schema": {"$ref": "#/definitions/OrderRequestModel"}}], "responses": {"200": {"description": "查询成功。 （由Get方法返回）", "schema": {"$ref": "#/definitions/ResultUtil«List«FundOrderInfoModel»»"}}, "201": {"description": "正常执行，请求成功。（由Post方法返回）", "schema": {"$ref": "#/definitions/ResultUtil«List«FundOrderInfoModel»»"}}, "403": {"description": "令牌的作用范围不正确或违反了安全策略。操作：请检查您是否使用合法授权用户帐户的正确令牌。"}, "404": {"description": "请求的帐户不存在。操作：请确保您输入的帐号和帐户类型正确。"}, "500": {"description": "API网关或微服务出现问题。操作：检查您的网络，稍后再试。"}}}}, "/fund/order/simulatorUpdate": {"post": {"tags": ["Order & Simulation"], "summary": "该API用于模拟基金交易中挂单成功后，模拟实际成交的过程。", "description": "例如，客户想要买入1000HKD的基金时，就需要调用/fund/order/subscriptionOrderPlacing 来下单。这个过程就代表挂单成功了，然后就可以再调用/fund/order/simulatorUpdate API来模拟基金能成功交易的过程。直接通过修改参数的数字状态，提交后就可以完成本次基金成交的过程。", "operationId": "orderStatusUpdateUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "颁发给第三方应用程序的最新客户授权令牌，以JWT格式访问该银行的客户数据。当开发者在调用API访问银行客户数据的时候，都需要用到该令牌。<br>最大长度: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "为前后端的每个请求唯一生成的128位随机UUID格式的消息ID。 该参数为内部参数，在调用API的时候不用修改该参数的内容。直接使用即可。<br>最大长度: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "当用户登录的时候生成的客户id。  该参数为内部参数，在调用API的时候不用修改该参数的内容。直接使用即可。<br>最大长度: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "orderStatusUpdateModel", "description": "orderStatusUpdateModel", "required": true, "schema": {"$ref": "#/definitions/OrderStatusUpdateModel"}}], "responses": {"200": {"description": "查询成功。 （由Get方法返回）", "schema": {"$ref": "#/definitions/ResultUtil"}}, "201": {"description": "正常执行，请求成功。（由Post方法返回）", "schema": {"$ref": "#/definitions/ResultUtil"}}, "403": {"description": "令牌的作用范围不正确或违反了安全策略。操作：请检查您是否使用合法授权用户帐户的正确令牌。"}, "404": {"description": "请求的帐户不存在。操作：请确保您输入的帐号和帐户类型正确。"}, "500": {"description": "API网关或微服务出现问题。操作：检查您的网络，稍后再试。"}}}}, "/fund/order/redemptionOrderPlacing": {"post": {"tags": ["Order & Simulation"], "summary": "该API用于卖出基金时，挂单的服务。", "description": "例如，当客户想要卖出500份额的某只持仓基金，就可以调用该的API来完成卖出挂单。在卖出挂单之前，客户可能需要先查看所持仓的基金信息，可以调用/fund​/fundHoldingEnquiry 来查看。\n例如，客户想要买入1000HKD的某只基金时，就需要调用/fund/order/subscriptionOrderPlacing 来下单。这就是一个挂单的过程。挂单之后，如果要进一步在成交之前修改挂单，则可以调用/fund/order/orderChange；如果不需要修改挂单，则可以调用/fund/order/simulatorUpdate 直接模拟交易成功的过程。", "operationId": "redemptionOrderPlacingUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "颁发给第三方应用程序的最新客户授权令牌，以JWT格式访问该银行的客户数据。当开发者在调用API访问银行客户数据的时候，都需要用到该令牌。<br>最大长度: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "为前后端的每个请求唯一生成的128位随机UUID格式的消息ID。 该参数为内部参数，在调用API的时候不用修改该参数的内容。直接使用即可。<br>最大长度: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "当用户登录的时候生成的客户id。  该参数为内部参数，在调用API的时候不用修改该参数的内容。直接使用即可。<br>最大长度: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "redemptionRequestModel", "description": "redemptionRequestModel", "required": true, "schema": {"$ref": "#/definitions/RedemptionRequestModel"}}], "responses": {"200": {"description": "查询成功。 （由Get方法返回）", "schema": {"$ref": "#/definitions/ResultUtil"}}, "201": {"description": "正常执行，请求成功。（由Post方法返回）", "schema": {"$ref": "#/definitions/ResultUtil"}}, "403": {"description": "令牌的作用范围不正确或违反了安全策略。操作：请检查您是否使用合法授权用户帐户的正确令牌。"}, "404": {"description": "请求的帐户不存在。操作：请确保您输入的帐号和帐户类型正确。"}, "500": {"description": "API网关或微服务出现问题。操作：检查您的网络，稍后再试。"}}}}, "/fund/order/subscriptionOrderPlacing": {"post": {"tags": ["Order & Simulation"], "summary": "该API用于买入基金时，挂单的服务。", "description": "例如，客户想要买入1000HKD的某只基金时，就需要调用/fund/order/subscriptionOrderPlacing 来下单。这就是一个挂单的过程。挂单之后，如果要进一步在成交之前修改挂单，则可以调用/fund/order/orderChange；如果不需要修改挂单，则可以调用/fund/order/simulatorUpdate 直接模拟交易成功的过程。", "operationId": "subscriptionOrderPlacingUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "颁发给第三方应用程序的最新客户授权令牌，以JWT格式访问该银行的客户数据。当开发者在调用API访问银行客户数据的时候，都需要用到该令牌。<br>最大长度: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "为前后端的每个请求唯一生成的128位随机UUID格式的消息ID。 该参数为内部参数，在调用API的时候不用修改该参数的内容。直接使用即可。<br>最大长度: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "当用户登录的时候生成的客户id。  该参数为内部参数，在调用API的时候不用修改该参数的内容。直接使用即可。<br>最大长度: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "subscriptioRequestModel", "description": "subscriptioRequestModel", "required": true, "schema": {"$ref": "#/definitions/SubscriptioRequestModel"}}], "responses": {"200": {"description": "查询成功。 （由Get方法返回）", "schema": {"$ref": "#/definitions/ResultUtil"}}, "201": {"description": "正常执行，请求成功。（由Post方法返回）", "schema": {"$ref": "#/definitions/ResultUtil"}}, "403": {"description": "令牌的作用范围不正确或违反了安全策略。操作：请检查您是否使用合法授权用户帐户的正确令牌。"}, "404": {"description": "请求的帐户不存在。操作：请确保您输入的帐号和帐户类型正确。"}, "500": {"description": "API网关或微服务出现问题。操作：检查您的网络，稍后再试。"}}}}}, "definitions": {"FundCodeModel": {"type": "object", "required": ["fundCode"], "properties": {"fundCode": {"type": "string", "example": "U000001", "description": "基金代码。</br> 最大长度: 10", "allowEmptyValue": false}}, "title": "FundCodeModel"}, "FundHoldInfoModel": {"type": "object", "properties": {"fundaccountnumber": {"type": "string", "example": "***********************", "description": "Mutual Fund Account Number.</br> 最大长度: 34", "allowEmptyValue": false}, "fundholdlist": {"type": "array", "items": {"$ref": "#/definitions/FundInvestmentModel"}}, "settlementaccount": {"type": "string", "example": "***********************", "description": "Settlement Account Number.</br> 最大长度: 34", "allowEmptyValue": false}, "totalInvestmentAmount": {"type": "string", "example": 100, "description": "total investment amount.<br>最大长度: 18</br></br>", "allowEmptyValue": false}, "totalNetGainLoss": {"type": "string", "example": 100, "description": "total NetGain Loss.<br>最大长度: 18</br></br>", "allowEmptyValue": false}, "totalNetGainLossPct": {"type": "string", "example": 100, "description": "total NetGain LossPct.<br>最大长度: 18</br></br>", "allowEmptyValue": false}, "totalmarkervalue": {"type": "string", "description": "total market value.<br>最大长度: 18</br></br>", "allowEmptyValue": false}}, "title": "FundHoldInfoModel"}, "FundHoldingEnquiryModel": {"type": "object", "required": ["fundAccountNumber"], "properties": {"fundAccountNumber": {"type": "string", "example": "***********************", "description": "基金账户号码。</br> 最大长度: 34", "allowEmptyValue": false}, "fundCode": {"type": "string", "example": "U000001", "description": "基金代码。</br> 最大长度: 10", "allowEmptyValue": true}}, "title": "FundHoldingEnquiryModel"}, "FundInvestmentModel": {"type": "object", "required": ["accountnumber", "id"], "properties": {"accountnumber": {"type": "string", "example": "***********************", "description": "A fund account number. <br/> 最大长度: 34", "allowEmptyValue": false}, "avaliableholdingno": {"type": "number", "example": 100.0, "description": "Avaliable shareholding number.<br>最大长度: 18</br>", "allowEmptyValue": false}, "averageprice": {"type": "number", "example": 200.0, "description": "Average price.<br>最大长度: 18</br>", "allowEmptyValue": false}, "currencycode": {"type": "string", "example": "HKD", "description": "Currency Code.<br>最大长度: 3</br>", "allowEmptyValue": false}, "fundcode": {"type": "string", "example": "U000001", "description": "Mutual Fund Code.<br>最大长度: 35</br>", "allowEmptyValue": false}, "id": {"type": "integer", "format": "int32", "example": 1, "description": "id.<br>最大长度: 20</br>", "allowEmptyValue": false}, "investmentamount": {"type": "number", "example": 500.0, "description": "Investment amount.<br>最大长度: 18</br>", "allowEmptyValue": false}, "lastupdatedate": {"type": "number", "example": *************.0, "description": "lastest update date.<br>最大长度: 20</br>", "allowEmptyValue": false}, "marketprice": {"type": "number", "example": 500.0, "description": "Market price.<br>最大长度: 18</br>", "allowEmptyValue": false}, "marketvalue": {"type": "number", "example": 50000.0, "description": "Market value.<br>最大长度: 18</br>", "allowEmptyValue": false}, "netGainLoss": {"type": "number", "example": 3000.0, "description": "Net income loss.<br>最大长度: 18</br>", "allowEmptyValue": false}, "netGainLossPct": {"type": "string", "example": 300, "description": "Net gain Loss Pct.<br>最大长度: 18</br>", "allowEmptyValue": false}, "sharesholdingno": {"type": "number", "example": 1000.0, "description": "Total shareholding number.<br>最大长度: 18</br>", "allowEmptyValue": false}}, "title": "FundInvestmentModel"}, "FundMarketInfoModel": {"type": "object", "properties": {"fundCode": {"type": "string", "example": "U000001", "description": "The Fund Code.<br>最大长度: 35</br>", "allowEmptyValue": false}, "fundCurrency": {"type": "string", "example": "USD", "description": "The fund currency type.<br>最大长度: 3</br>", "allowEmptyValue": false}, "fundHouse": {"type": "string", "example": "Aberdeen Intl Fund Managers Ltd", "description": "The fund company.<br>最大长度: 140</br>", "allowEmptyValue": false}, "fundName": {"type": "string", "example": "Aberdeen Global - Asia Pacific Equity Fund (USD) A2", "description": "The name of the fund.<br>最大长度: 140</br>", "allowEmptyValue": false}, "fundType": {"type": "string", "example": "Equity Funds", "description": "The fund type.<br>最大长度: 70</br>", "allowEmptyValue": false}, "geographic": {"type": "string", "example": "Asia Pacific", "description": "geographic.<br>最大长度: 35</br>", "allowEmptyValue": false}, "id": {"type": "integer", "format": "int32", "example": 1, "description": "id,<br>最大长度: 20</br>", "allowEmptyValue": false}, "issueDate": {"type": "number", "example": *************.0, "description": "Fund release date.<br>最大长度: 20</br>", "allowEmptyValue": false}, "lastNAV": {"type": "number", "example": 92.64, "description": "The fund latest net.<br>最大长度: 18</br>", "allowEmptyValue": false}, "managementFee": {"type": "number", "example": 0.018, "description": "fund management fee.<br>最大长度: 18</br>", "allowEmptyValue": false}, "sector": {"type": "string", "example": "General", "description": "Sector.<br>最大长度: 35</br>", "allowEmptyValue": false}, "valuationDate": {"type": "number", "example": *************.0, "description": "The fund valuation date.<br>最大长度: 20</br>", "allowEmptyValue": false}}, "title": "FundMarketInfoModel"}, "FundOrderInfoModel": {"type": "object", "properties": {"currencyCode": {"type": "string", "example": "HKD", "description": "Fund Currency Type.<br>最大长度: 3</br>", "allowEmptyValue": false}, "fundAccountNumber": {"type": "string", "example": "***********************", "description": "Mutual Fund Account Number.</br> 最大长度: 34", "allowEmptyValue": false}, "fundCode": {"type": "string", "example": "U000001", "description": "Fund code </br> 最大长度: 10", "allowEmptyValue": false}, "fundName": {"type": "string", "example": "Aberdeen Global - Asia Pacific Equity Fund (USD) A2.<br>最大长度: 140</br>", "description": "Fund name", "allowEmptyValue": false}, "id": {"type": "integer", "format": "int32", "example": 1, "description": "id.<br>最大长度: 20</br>", "allowEmptyValue": false}, "lastupdateDate": {"type": "number", "example": *************.0, "description": "Last update date.<br>最大长度: 20</br>", "allowEmptyValue": false}, "operationReasons": {"type": "string", "example": "test case", "description": "Operation reason.<br>最大长度: 140</br>", "allowEmptyValue": false}, "operationTime": {"type": "number", "example": *************.0, "description": "Operation time.<br>最大长度: 20</br>", "allowEmptyValue": false}, "requestTime": {"type": "number", "example": *************.0, "description": "Request time.<br>最大长度: 20</br>", "allowEmptyValue": false}, "settlementAccount": {"type": "string", "example": "***********************", "description": "Settlement Account Number.</br> 最大长度: 34", "allowEmptyValue": false}, "sharingNo": {"type": "number", "example": 100.0, "description": "Number of fund shares you'd like to sell. </br>最大长度: 13", "allowEmptyValue": false}, "status": {"type": "string", "example": 200, "description": "Request status.<br>最大长度: 4</br>", "allowEmptyValue": false}, "statusCode": {"type": "string", "example": 200, "description": "Status code.<br>最大长度: 10</br>", "allowEmptyValue": false}, "tradingAmount": {"type": "number", "example": 200.0, "description": "Trading amount. </br>最大长度: 13", "allowEmptyValue": false}, "tradingCommission": {"type": "number", "example": 150.0, "description": "Trading commission. </br>最大长度: 13", "allowEmptyValue": false}, "tradingOption": {"type": "string", "example": "sell", "description": "Fund transaction type", "allowEmptyValue": false}, "tradingPrice": {"type": "number", "example": 100.0, "description": "Trading Price. </br>最大长度: 13", "allowEmptyValue": false}, "transactionAmount": {"type": "number", "example": 150.0, "description": "Transaction amount. </br>最大长度: 13", "allowEmptyValue": false}}, "title": "FundOrderInfoModel"}, "FundPlatformLogModel": {"type": "object", "properties": {"accountNumber": {"type": "string", "example": "***********************", "description": "Fund Account Number </br> 最大长度:34", "allowEmptyValue": false}, "branchCode": {"type": "string", "example": "0001", "description": "Bank branch code<br>最大长度: 4</br>", "allowEmptyValue": false}, "clearingCode": {"type": "string", "example": "0001", "description": "Bank clearing code.<br>最大长度: 4</br>", "allowEmptyValue": false}, "countryCode": {"type": "string", "example": "HK", "description": "Country code.<br>最大长度: 2</br>", "allowEmptyValue": false}, "fundCcy": {"type": "string", "example": "HKD", "description": "fund currency<br>最大长度: 3</br>", "allowEmptyValue": false}, "fundCode": {"type": "string", "example": "U000001", "description": "The Fund Code<br>最大长度: 35</br>", "allowEmptyValue": false}, "id": {"type": "integer", "format": "int32", "example": 1, "description": "id,<br>最大长度: 20</br>", "allowEmptyValue": false}, "sharingNo": {"type": "number", "example": 100.0, "description": "Number of fund shares you’d like to sell. </br>5 decimals,最大长度: 13", "allowEmptyValue": false}, "tradingAmount": {"type": "number", "example": 300.0, "description": "Trading amount in the fund subscription order. </br>5 decimals,最大长度: 13", "allowEmptyValue": false}, "tradingOption": {"type": "string", "example": "sell", "description": "Fund transaction type<br>最大长度: 10</br>", "allowEmptyValue": false}, "transactionAmount": {"type": "number", "example": 100.0, "description": "Transaction amount in the fund order. </br>5 decimals,最大长度: 13", "allowEmptyValue": false}, "transactionDate": {"type": "number", "example": *************.0, "description": "Transaction date<br>最大长度: 20</br>", "allowEmptyValue": false}, "trdingCommission": {"type": "number", "example": 200.0, "description": "Trading commission in the fund order. </br>5 decimals,最大长度: 13", "allowEmptyValue": false}}, "title": "FundPlatformLogModel"}, "IDModel": {"type": "object", "required": ["id"], "properties": {"id": {"type": "number", "example": 1.0, "description": "基金订单编号。当您调用买入或者卖出基金申请API之后，就会返回一个基金订单编号。</br><br>最大长度: 20</br>", "allowEmptyValue": false}}, "title": "IDModel"}, "OrderInfoUpdateModel": {"type": "object", "required": ["id"], "properties": {"id": {"type": "number", "example": 2.0, "description": "基金订单编号。当您调用买入或者卖出基金申请API之后，就会返回一个基金订单编号。<br>最大长度: 20</br>", "allowEmptyValue": false}, "sharingNo": {"type": "number", "example": 100.0, "description": "基金份额。例如当要卖出基金的时候，需要填入卖出多少份额。 当买入基金的时候，不需要填入该数字，请保持为空。 </br>最大长度: 13", "allowEmptyValue": true}, "tradingAmount": {"type": "number", "example": 100.0, "description": "交易金额。</br>最大长度: 13", "allowEmptyValue": true}}, "title": "OrderInfoUpdateModel"}, "OrderRequestModel": {"type": "object", "required": ["accountNumber", "fromdate", "index", "items", "todate"], "properties": {"accountNumber": {"type": "string", "example": "***********************", "description": "基金账户号码。</br> 最大长度: 34", "allowEmptyValue": false}, "fromdate": {"type": "number", "example": *************.0, "description": "想要查询的订单的开始时间范围。</br>最大长度: 20 integers", "allowEmptyValue": false}, "fundCode": {"type": "string", "example": "U000001", "description": "基金代码。 </br> 最大长度: 10", "allowEmptyValue": true}, "index": {"type": "number", "example": 0.0, "description": "API返回数据的开始位置标记。如：当index=1时，则表示从第1条数据开始返回结果。 </br>最大长度: 4 integers", "allowEmptyValue": false}, "items": {"type": "number", "example": 3.0, "description": "API返回的数据条数。如：当items=10. 表示返回10条结果。</br>最大长度: 4 integers", "allowEmptyValue": false}, "todate": {"type": "number", "example": *************.0, "description": "想要查询的订单的结束时间范围。</br>最大长度: 20 integers", "allowEmptyValue": false}}, "title": "OrderRequestModel"}, "OrderStatusUpdateModel": {"type": "object", "required": ["id", "status"], "properties": {"id": {"type": "number", "example": 3.0, "description": "基金订单编号。当您调用买入或者卖出基金申请API之后，就会返回一个基金订单编号。</br> <br>最大长度: 20</br>", "allowEmptyValue": false}, "status": {"type": "string", "example": 1, "description": "用于模拟基金交易 成功还是失败的状态参数。当Status设置为1时，表示模拟该笔基金交易成功。 </br> 最大长度: 4", "allowEmptyValue": false}}, "title": "OrderStatusUpdateModel"}, "QueryFundListModel": {"type": "object", "required": ["index", "items"], "properties": {"index": {"type": "number", "example": 0.0, "description": "API返回数据的开始位置标记。如：当index=1时，则表示从第1条数据开始返回结果</br>最大长度: 4 integers", "allowEmptyValue": false}, "items": {"type": "number", "example": 3.0, "description": "API返回的数据条数。如：当items=10. 表示返回10条结果。</br>最大长度: 4 integers", "allowEmptyValue": false}}, "title": "QueryFundListModel"}, "QueryFundTransModel": {"type": "object", "required": ["accountNumber", "index", "items", "transFromTime", "transToTime"], "properties": {"accountNumber": {"type": "string", "example": "***********************", "description": "基金账户号码。</br> 最大长度:34", "allowEmptyValue": false}, "fundCode": {"type": "string", "example": "U000001", "description": "基金代码。</br> 最大长度:10", "allowEmptyValue": true}, "index": {"type": "number", "example": 0.0, "description": "API返回数据的开始位置标记。如：当index=1时，则表示从第1条数据开始返回结果。</br>最大长度: 4", "allowEmptyValue": false}, "items": {"type": "number", "example": 3.0, "description": "API返回的数据条数。如：当items=10. 表示返回10条结果。</br>最大长度: 4", "allowEmptyValue": false}, "transFromTime": {"type": "number", "example": *************.0, "description": "想要查询的历史交易的开始时间范围。</br>最大长度: 20", "allowEmptyValue": false}, "transToTime": {"type": "number", "example": *************.0, "description": "想要查询的历史交易的结束时间范围。</br>最大长度: 20", "allowEmptyValue": false}}, "title": "QueryFundTransModel"}, "RedemptionRequestModel": {"type": "object", "required": ["fundAccountNumber", "fundCode", "sharingNo"], "properties": {"fundAccountNumber": {"type": "string", "example": "***********************", "description": "基金账户号码。 </br> 最大长度:34", "allowEmptyValue": false}, "fundCode": {"type": "string", "example": "U000001", "description": "基金代码。</br> 最大长度:10", "allowEmptyValue": false}, "sharingNo": {"type": "number", "example": 100.0, "description": "基金份额。例如当要卖出基金的时候，需要填入卖出多少份额。 当买入基金的时候，不需要填入该数字，请保持为空。</br>最大长度: 13", "allowEmptyValue": false}}, "title": "RedemptionRequestModel"}, "ResultUtil": {"type": "object", "properties": {"code": {"type": "string"}, "data": {"type": "object"}, "msg": {"type": "string"}}, "title": "ResultUtil"}, "ResultUtil«FundCodeModel»": {"type": "object", "properties": {"code": {"type": "string"}, "data": {"$ref": "#/definitions/FundCodeModel"}, "msg": {"type": "string"}}, "title": "ResultUtil«FundCodeModel»"}, "ResultUtil«FundHoldInfoModel»": {"type": "object", "properties": {"code": {"type": "string"}, "data": {"$ref": "#/definitions/FundHoldInfoModel"}, "msg": {"type": "string"}}, "title": "ResultUtil«FundHoldInfoModel»"}, "ResultUtil«FundOrderInfoModel»": {"type": "object", "properties": {"code": {"type": "string"}, "data": {"$ref": "#/definitions/FundOrderInfoModel"}, "msg": {"type": "string"}}, "title": "ResultUtil«FundOrderInfoModel»"}, "ResultUtil«List«FundMarketInfoModel»»": {"type": "object", "properties": {"code": {"type": "string"}, "data": {"type": "array", "items": {"$ref": "#/definitions/FundMarketInfoModel"}}, "msg": {"type": "string"}}, "title": "ResultUtil«List«FundMarketInfoModel»»"}, "ResultUtil«List«FundOrderInfoModel»»": {"type": "object", "properties": {"code": {"type": "string"}, "data": {"type": "array", "items": {"$ref": "#/definitions/FundOrderInfoModel"}}, "msg": {"type": "string"}}, "title": "ResultUtil«List«FundOrderInfoModel»»"}, "ResultUtil«List«FundPlatformLogModel»»": {"type": "object", "properties": {"code": {"type": "string"}, "data": {"type": "array", "items": {"$ref": "#/definitions/FundPlatformLogModel"}}, "msg": {"type": "string"}}, "title": "ResultUtil«List«FundPlatformLogModel»»"}, "SubscriptioRequestModel": {"type": "object", "required": ["fundAccountNumber", "fundCode", "tradingAmount"], "properties": {"fundAccountNumber": {"type": "string", "example": "***********************", "description": "基金账户号码。</br> 最大长度: 34", "allowEmptyValue": false}, "fundCode": {"type": "string", "example": "U000001", "description": "基金代码。</br> 最大长度: 10", "allowEmptyValue": false}, "tradingAmount": {"type": "number", "example": 100.0, "description": "交易金额。 </br>最大长度: 13", "allowEmptyValue": false}}, "title": "SubscriptioRequestModel"}, "UpdateSettlementAccountModel": {"type": "object", "required": ["fundAccountNumber", "newSettleAccountNumber"], "properties": {"fundAccountNumber": {"type": "string", "example": "***********************", "description": "基金账户号码。</br> 最大长度: 34", "allowEmptyValue": false}, "newSettleAccountNumber": {"type": "string", "example": "***********************", "description": "为该基金账户绑定的新的储蓄账户（001或者002结尾的账户类型)，用于交易中的扣款或者收款服务。</br> 最大长度: 34", "allowEmptyValue": false}}, "title": "UpdateSettlementAccountModel"}}}