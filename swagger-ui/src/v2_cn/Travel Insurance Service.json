{"swagger": "2.0", "info": {"description": "该服务模块主要包含了所有保险相关的业务API。包含保险报价试算和保险购买等业务功能。", "version": "1.0", "title": "保险业务", "contact": {"name": "admin:<EMAIL>"}}, "schemes": ["https"], "host": "simnectz.hsu.edu.hk/lbsgateway/insurance-experience", "basePath": "/", "tags": [{"name": "Product Management", "description": "产品管理。"}, {"name": "Transaction", "description": "保险处理包括报价、申请、投保。"}], "paths": {"/insurance/application": {"post": {"tags": ["Transaction"], "summary": "该组API用于申请保险产品购买。", "description": "当客户获取调用/insurance/travelInsuranceQuote 获得报价之后，可以调用/insurance/application进一步操作申请购买，然后再调用/insurance/subscription付款。", "operationId": "applicationUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "颁发给第三方应用程序的最新客户授权令牌，以JWT格式访问该银行的客户数据。当开发者在调用API访问银行客户数据的时候，都需要用到该令牌。", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "为前后端的每个请求唯一生成的128位随机UUID格式的消息ID。 该参数为内部参数，在调用API的时候不用修改该参数的内容。直接使用即可。", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "当用户登录的时候生成的客户id。  该参数为内部参数，在调用API的时候不用修改该参数的内容。直接使用即可。 ", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "applyInsuranceModel", "description": "applyInsuranceModel", "required": true, "schema": {"$ref": "#/definitions/ApplyInsuranceModel"}}], "responses": {"200": {"description": "查询成功。 （由Get方法返回）", "schema": {"$ref": "#/definitions/ResultUtil"}}, "201": {"description": "正常执行，请求成功。（由Post方法返回）", "schema": {"$ref": "#/definitions/ResultUtil"}}, "403": {"description": "令牌的作用范围不正确或违反了安全策略。操作：请检查您是否使用合法授权用户帐户的正确令牌。"}, "404": {"description": "请求的帐户不存在。操作：请确保您输入的帐号和帐户类型正确。"}, "500": {"description": "API网关或微服务出现问题。操作：检查您的网络，稍后再试。"}}}}, "/insurance/productdetailretrieval": {"post": {"tags": ["Product Management"], "summary": "该API用于根据某个产品编号获取该产品的详细信息。", "description": "比如某产品编号为001，那么通过这个API就可以直接输入编号001，获取到该产品的详细信息。", "operationId": "retrievalProductDetailUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "颁发给第三方应用程序的最新客户授权令牌，以JWT格式访问该银行的客户数据。当开发者在调用API访问银行客户数据的时候，都需要用到该令牌。", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "为前后端的每个请求唯一生成的128位随机UUID格式的消息ID。 该参数为内部参数，在调用API的时候不用修改该参数的内容。直接使用即可。", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "当用户登录的时候生成的客户id。  该参数为内部参数，在调用API的时候不用修改该参数的内容。直接使用即可。 ", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "productDetailModel", "description": "productDetailModel", "required": true, "schema": {"$ref": "#/definitions/ProductDetailModel"}}], "responses": {"200": {"description": "查询成功。 （由Get方法返回）", "schema": {"$ref": "#/definitions/ResultUtil"}}, "201": {"description": "正常执行，请求成功。（由Post方法返回）", "schema": {"$ref": "#/definitions/ResultUtil"}}, "403": {"description": "令牌的作用范围不正确或违反了安全策略。操作：请检查您是否使用合法授权用户帐户的正确令牌。"}, "404": {"description": "The requested  account does not exist.Action: Please make sure the account number and account type you’re inputting are correct."}, "500": {"description": "API网关或微服务出现问题。操作：检查您的网络，稍后再试。"}}}}, "/insurance/productlistretrieval": {"post": {"tags": ["Product Management"], "summary": "该API用于获取系统中所有的保险产品列表。", "description": "在购买保险之前，您可能需要调用该API去获取保险产品列表。", "operationId": "retrievalProductListUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "颁发给第三方应用程序的最新客户授权令牌，以JWT格式访问该银行的客户数据。当开发者在调用API访问银行客户数据的时候，都需要用到该令牌。", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "为前后端的每个请求唯一生成的128位随机UUID格式的消息ID。 该参数为内部参数，在调用API的时候不用修改该参数的内容。直接使用即可。", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "当用户登录的时候生成的客户id。  该参数为内部参数，在调用API的时候不用修改该参数的内容。直接使用即可。 ", "required": true, "type": "string", "default": "devin"}], "responses": {"200": {"description": "查询成功。 （由Get方法返回）", "schema": {"$ref": "#/definitions/ResultUtil"}}, "201": {"description": "正常执行，请求成功。（由Post方法返回）", "schema": {"$ref": "#/definitions/ResultUtil"}}, "403": {"description": "令牌的作用范围不正确或违反了安全策略。操作：请检查您是否使用合法授权用户帐户的正确令牌。"}, "404": {"description": "The requested  account does not exist.Action: Please make sure the account number and account type you’re inputting are correct."}, "500": {"description": "API网关或微服务出现问题。操作：检查您的网络，稍后再试。"}}}}, "/insurance/subscription": {"post": {"tags": ["Transaction"], "summary": "该API用于保险产品试算报价环节。", "description": "在正式购买产品之前，客户需要选择好目的地，被保人的人数等信息，最终系统给出一个报价。这就是整个试算报价的过程。最后客户接受该报价，就可以正式付款购买了。付款可以调用/insurance/subscription API。\n在保险产品报价过程中，被保险人范围随承保类型的不同而不同。\n当保险类型设置为SIGL时，被保险人可以是本人、配偶、我的子女、朋友/亲属；\n当保险类型设置为ANLC/ANLG时，被保险人可以是自己、配偶、我的子女、父母。\n注：最高参保人数为10人。\n参保儿童最多为6名。\n受保朋友/亲属的最高限额为6人。", "operationId": "subscriptionUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "颁发给第三方应用程序的最新客户授权令牌，以JWT格式访问该银行的客户数据。当开发者在调用API访问银行客户数据的时候，都需要用到该令牌。", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "为前后端的每个请求唯一生成的128位随机UUID格式的消息ID。 该参数为内部参数，在调用API的时候不用修改该参数的内容。直接使用即可。", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "当用户登录的时候生成的客户id。  该参数为内部参数，在调用API的时候不用修改该参数的内容。直接使用即可。 ", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "insuranceSubscriptionModel", "description": "insuranceSubscriptionModel", "required": true, "schema": {"$ref": "#/definitions/InsuranceSubscriptionModel"}}], "responses": {"200": {"description": "查询成功。 （由Get方法返回）", "schema": {"$ref": "#/definitions/ResultUtil"}}, "201": {"description": "正常执行，请求成功。（由Post方法返回）", "schema": {"$ref": "#/definitions/ResultUtil"}}, "403": {"description": "令牌的作用范围不正确或违反了安全策略。操作：请检查您是否使用合法授权用户帐户的正确令牌。"}, "404": {"description": "请求的帐户不存在。操作：请确保您输入的帐号和帐户类型正确。"}, "500": {"description": "API网关或微服务出现问题。操作：检查您的网络，稍后再试。"}}}}, "/insurance/travelInsuranceQuote": {"post": {"tags": ["Transaction"], "summary": "该API用于保险产品试算报价环节。", "description": " 在正式购买产品之前，客户需要选择好目的地，被保人的人数等信息，最终系统给出一个报价。这就是整个试算报价的过程。最后客户接受该报价，就可以正式付款购买了。付款可以调用/insurance/subscription API。\n在保险产品报价过程中，被保险人范围随承保类型的不同而不同。\n当保险类型设置为SIGL时，被保险人可以是本人、配偶、我的子女、朋友/亲属；\n当保险类型设置为ANLC/ANLG时，被保险人可以是自己、配偶、我的子女、父母。\n注：最高参保人数为10人。\n参保儿童最多为6名。\n受保朋友/亲属的最高限额为6人。", "operationId": "travelInsuranceQuoteUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "颁发给第三方应用程序的最新客户授权令牌，以JWT格式访问该银行的客户数据。当开发者在调用API访问银行客户数据的时候，都需要用到该令牌。", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "为前后端的每个请求唯一生成的128位随机UUID格式的消息ID。 该参数为内部参数，在调用API的时候不用修改该参数的内容。直接使用即可。", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "当用户登录的时候生成的客户id。  该参数为内部参数，在调用API的时候不用修改该参数的内容。直接使用即可。 ", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "travelInsuranceQuoteMode", "description": "travelInsuranceQuoteMode", "required": true, "schema": {"$ref": "#/definitions/TravelInsuranceQuoteModel"}}], "responses": {"200": {"description": "查询成功。 （由Get方法返回）", "schema": {"$ref": "#/definitions/ResultUtil"}}, "201": {"description": "正常执行，请求成功。（由Post方法返回）", "schema": {"$ref": "#/definitions/ResultUtil"}}, "403": {"description": "令牌的作用范围不正确或违反了安全策略。操作：请检查您是否使用合法授权用户帐户的正确令牌。"}, "404": {"description": "请求的帐户不存在。操作：请确保您输入的帐号和帐户类型正确。"}, "500": {"description": "API网关或微服务出现问题。操作：检查您的网络，稍后再试。"}}}}}, "definitions": {"ApplyInsuranceChildModel": {"type": "object", "required": ["dateofbirth", "gender", "givenname", "idnumber", "idtype", "relationship", "surnamename"], "properties": {"dateofbirth": {"type": "string", "example": 1583835112160, "description": "客户生日<br>最大长度: 20</br>", "allowEmptyValue": false}, "gender": {"type": "string", "example": "M", "description": "性别。<br>最大长度: 1</br>", "allowEmptyValue": false}, "givenname": {"type": "string", "example": "Yun", "description": "客户名字。<br>最大长度: 20</br>", "allowEmptyValue": false}, "idnumber": {"type": "string", "example": 610425197504240001, "description": "客户证件号码<br>最大长度: 35</br>", "allowEmptyValue": false}, "idtype": {"type": "string", "example": "P", "description": "客户身份证件类型。 HKID card：表示香港身份证； P：表示护照<br>最大长度: 1</br>", "allowEmptyValue": false}, "relationship": {"type": "string", "example": "SELF", "description": "被保人和购买保险人的关系<br>SELF-自己<br>SPOU-配偶<br>PARE-父母<br>CHIL-孩子<br>FRIE-朋友<br>RELA-亲戚<br>最大长度: 4</br>", "allowEmptyValue": false}, "surnamename": {"type": "string", "example": "Ma", "description": "姓<br>最大长度: 20</br>", "allowEmptyValue": false}}, "title": "ApplyInsuranceChildModel"}, "ApplyInsuranceModel": {"type": "object", "required": ["quoteid"], "properties": {"applySub": {"type": "array", "items": {"$ref": "#/definitions/ApplyInsuranceChildModel"}}, "quoteid": {"type": "string", "example": "Q000000002", "description": "报价编号。客户在申请购买之前需要先根据自己的情况试算然后得出一个报价。在调用报价的API之后，系统就会返回一个报价编号。<br>最大长度: 35</br>", "allowEmptyValue": false}}, "title": "ApplyInsuranceModel"}, "InsuranceSubscriptionModel": {"type": "object", "required": ["accountNumber", "accountType", "quoteid"], "properties": {"accountNumber": {"type": "string", "example": ****************, "description": "客户银行账户号码。支持001结尾的储蓄账户；002结尾的活期账户和信用卡账户。<br>最大长度: 34</br>", "allowEmptyValue": false}, "accountType": {"type": "string", "example": "CRED", "description": "银行账户类型。<br>SAVI - 001结尾的储蓄账户<br>CURR - 002结尾的活期账户<br>CRED - 信用卡账户。<br>最大长度: 4</br>", "allowEmptyValue": false}, "quoteid": {"type": "string", "example": "Q000000002", "description": "报价编号。客户在申请购买之前需要先根据自己的情况试算然后得出一个报价。在调用报价的API之后，系统就会返回一个报价编号。<br>最大长度: 34</br>", "allowEmptyValue": false}}, "title": "InsuranceSubscriptionModel"}, "ProductDetailModel": {"type": "object", "required": ["productcode"], "properties": {"productcode": {"type": "string", "example": "T001", "description": "保险产品编号。<br>最大长度: 35</br>", "allowEmptyValue": false}}, "title": "ProductDetailModel"}, "ResultUtil": {"type": "object", "properties": {"code": {"type": "string"}, "data": {"type": "object"}, "msg": {"type": "string"}}, "title": "ResultUtil"}, "TravelInsuranceQuoteModel": {"type": "object", "required": ["coverageType", "productCode", "startDate"], "properties": {"childrenNo": {"type": "integer", "format": "int32", "example": 1, "description": "选择的孩子的数量。 <br>最大长度: 50</br>", "allowEmptyValue": true}, "coverageType": {"type": "string", "example": "SIGL", "description": "覆盖类型。<br>SIGL - 单程保障<br>ANLC - 中国全年保障<br>ANLG - 全球全年保障<br>最大长度: 4</br>", "allowEmptyValue": false}, "dateOfBirth": {"type": "number", "example": 978250234000.0, "description": "被保险人生日<br>最大长度: 20</br>", "allowEmptyValue": true}, "destination": {"type": "string", "example": "Mainland China and Macau", "description": "保险产品目的地。支持的值：<br>Mainland China and Macau：国内和澳门<br>Asia：亚洲<br>Worldwide：全球<br>最大长度: 35</br>", "allowEmptyValue": true}, "endDate": {"type": "number", "example": 1639826636000.0, "description": "保险结束日期。<br>最大长度: 20</br>", "allowEmptyValue": true}, "friendsOrRelatives": {"type": "string", "example": "Y", "description": "如果coverageType设置为ANLC 或者ANLG，请保持该参数值为空。否则，可以输入Y，表示选择给朋友或亲戚买保险。<br>最大长度: 1</br>", "allowEmptyValue": true}, "friendsOrRelativesNo": {"type": "integer", "format": "int32", "example": 6, "description": "朋友或者亲戚的数量。支持的值：0，1，2，3，4，5，6<br>最大长度: 2</br>", "allowEmptyValue": true}, "idNumber": {"type": "string", "example": 15, "description": "客户证件号码<br>最大长度: 35</br>", "allowEmptyValue": true}, "idType": {"type": "string", "example": "P", "description": "客户身份证件类型。 HKID card：表示香港身份证； P：表示护照 <br>最大长度: 1</br>", "allowEmptyValue": true}, "myChildren": {"type": "string", "example": "Y", "description": "如果想给孩子买保险，可以设置值 Y表示买。如果不买，请保持该参数为空。<br>最大长度: 1</br>", "allowEmptyValue": true}, "parents": {"type": "string", "description": "如果coverageType设置为SIGL，请保持该参数为空。否则，可以设置值 Y表示给父母买保险。<br>最大长度: 1</br>", "allowEmptyValue": true}, "parentsNo": {"type": "integer", "format": "int32", "description": "父母总数量。支持的值：0，1，2 <br>最大长度: 2</br>", "allowEmptyValue": true}, "productCode": {"type": "string", "example": "T002", "description": "保险产品编号。 <br>最大长度: 35</br>", "allowEmptyValue": false}, "self": {"type": "string", "example": "Y", "description": "自己。 <br>最大长度: 1</br>", "allowEmptyValue": true}, "spouse": {"type": "string", "example": "Y", "description": "配偶。<br>最大长度: 1</br>", "allowEmptyValue": true}, "startDate": {"type": "number", "example": 1608290636000.0, "description": "保险开始生效日期。<br>最大长度: 20</br>", "allowEmptyValue": false}}, "title": "TravelInsuranceQuoteModel"}}}