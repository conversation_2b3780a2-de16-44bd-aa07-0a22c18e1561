{"swagger": "2.0", "info": {"description": "APIs here are all about insurance service. Before calling these APIs, please make sure you’ve learnt the business logic between these APIs.", "version": "1.0", "title": "第三方保险服务", "contact": {}}, "schemes": ["https"], "host": "simnectz.hsu.edu.hk/vis", "basePath": "/", "tags": [{"name": "客户角色", "description": "客户购买产品保险以及发起理赔申请的流程。"}, {"name": "核赔员角色", "description": "保险人向被保险人或受益人支付保险金。"}, {"name": "调查员角色", "description": "保险人对损失的性质、过程、原因、程度以及保险事故的责任认定。如果不需要索赔调查，下一步进入损失评估流程。"}, {"name": "定责员角色", "description": "确定保险标的的实际责任和损失的过程。"}, {"name": "理算员角色", "description": "确定保险标的的实际责任和损失的过程。"}, {"name": "出纳角色", "description": "保险人向被保险人或受益人支付保险金。"}, {"name": "质检角色", "description": "整个理赔过程完成后，将对整个案件的处理过程进行质检评分。"}], "paths": {"/api/allToken": {"get": {"tags": ["出纳角色", "定责员角色", "核赔员角色", "理算员角色", "调查员角色", "质检角色", "客户角色"], "summary": "该API用于所有用户登录保险系统时获取token，做身份认证。 ", "description": "该系统所有角色登录的时候，都需要调用该API获取token，完成身份认证。", "operationId": "getTokenByCardIDUsingGET", "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.", "required": true, "type": "string", "default": "QFTM1V9wSLtHt6IaIiL3neAsymh1aqtSdGJ5wFCEhmODQjgtcQWKloMGHiHCTIndAeYML3rmxtJxddFR7U+erKMzgLkfOyN8NMMR5QOYSS3X2EcN+dno14KzZIV1lt3VX+emKzPW5bZYI5P0oEXs/Lne7fqGXMyypax+5Bdik7M="}, {"name": "cardId", "in": "query", "description": "用户身份证ID. eg:610124199205242152</br>核赔员ID:*********</br>调查员ID:*********</br>定责员ID:*********</br>理算员ID:*********</br>出纳ID:*********</br>质检员ID:*********</br><br>maxLength: 18</br>", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/response?string?"}}}}}, "/api/audit/cashierdetail": {"get": {"tags": ["出纳角色"], "summary": "该API可以让出纳人员查看某个保险理赔案件的详细内容。", "description": "出纳人员在完成赔付转账之前，可能需要调该API去查看该理赔案件的相关内容。 ", "operationId": "cashierdetailUsingGET", "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.", "required": true, "type": "string", "default": "QFTM1V9wSLtHt6IaIiL3neAsymh1aqtSdGJ5wFCEhmODQjgtcQWKloMGHiHCTIndAeYML3rmxtJxddFR7U+erKMzgLkfOyN8NMMR5QOYSS3X2EcN+dno14KzZIV1lt3VX+emKzPW5bZYI5P0oEXs/Lne7fqGXMyypax+5Bdik7M="}, {"name": "reportNo", "in": "query", "description": "案件编号. eg:LP86671672394116486<br>maxLength: 50</br>", "required": true, "type": "string"}], "responses": {"-10001": {"description": "System error"}, "200": {"description": "success", "schema": {"$ref": "#/definitions/response?object?"}}, "40008": {"description": "The reportNo is not exist"}}}}, "/api/audit/cashiers": {"get": {"tags": ["出纳角色"], "summary": "该API可以让出纳人员根据身份证ID筛选要查看的保险申请案件列表信息，然后对该申请进行转账赔付。", "description": "在调用此API之前，请确保您已经调用/api/audit/paymentdetail完成了赔付金额最终确定的环节。", "operationId": "cashiersUsingGET", "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.", "required": true, "type": "string", "default": "QFTM1V9wSLtHt6IaIiL3neAsymh1aqtSdGJ5wFCEhmODQjgtcQWKloMGHiHCTIndAeYML3rmxtJxddFR7U+erKMzgLkfOyN8NMMR5QOYSS3X2EcN+dno14KzZIV1lt3VX+emKzPW5bZYI5P0oEXs/Lne7fqGXMyypax+5Bdik7M="}, {"in": "body", "name": "cardId", "description": "身份证ID. eg:610124199205242152<br>maxLength: 18</br>", "required": false, "schema": {"type": "string"}}], "responses": {"-10001": {"description": "System error"}, "200": {"description": "success", "schema": {"$ref": "#/definitions/response?object?"}}, "40008": {"description": "The reportNo is not exist"}}}}, "/api/audit/claimdetail": {"get": {"tags": ["核赔员角色"], "summary": "该API用于让审核人员查看理赔申请的详细内容。", "description": "在调用该API之前，请确保您已经调用/api/audit/reports 获取到了用户提交的保险理赔列表。", "operationId": "claimDetailUsingGET", "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.", "required": true, "type": "string", "default": "QFTM1V9wSLtHt6IaIiL3neAsymh1aqtSdGJ5wFCEhmODQjgtcQWKloMGHiHCTIndAeYML3rmxtJxddFR7U+erKMzgLkfOyN8NMMR5QOYSS3X2EcN+dno14KzZIV1lt3VX+emKzPW5bZYI5P0oEXs/Lne7fqGXMyypax+5Bdik7M="}, {"name": "reportNo", "in": "query", "description": "案件编号. eg:LP86671672394116486<br>maxLength: 50</br>", "required": true, "type": "string"}], "responses": {"-10001": {"description": "System error"}, "200": {"description": "success", "schema": {"$ref": "#/definitions/response?object?"}}, "40008": {"description": "The reportNo is not exist"}}}}, "/api/audit/duty": {"post": {"tags": ["定责员角色"], "summary": "该API可以让定责员根据上一流程中审核人提交的审核意见，给出自己的责任审定意见。 ", "description": " 查看理赔案例中上一个流程的处理结果。", "operationId": "dutyUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.", "required": true, "type": "string", "default": "QFTM1V9wSLtHt6IaIiL3neAsymh1aqtSdGJ5wFCEhmODQjgtcQWKloMGHiHCTIndAeYML3rmxtJxddFR7U+erKMzgLkfOyN8NMMR5QOYSS3X2EcN+dno14KzZIV1lt3VX+emKzPW5bZYI5P0oEXs/Lne7fqGXMyypax+5Bdik7M="}, {"in": "body", "name": "dutyDto", "description": "dutyDto", "required": true, "schema": {"$ref": "#/definitions/DutyDto"}}], "responses": {"-10001": {"description": "System error"}, "200": {"description": "success", "schema": {"$ref": "#/definitions/response?object?"}}, "40008": {"description": "The reportNo is not exist"}}}}, "/api/audit/dutydetail": {"get": {"tags": ["定责员角色"], "summary": "该API可以让定责员根据保险理赔编号查看材料详情及上一流程中审核人员提交的审核意见。", "description": "在开始做责任审定之前，您需要调用该API了解该理赔案例的相关内容。 ", "operationId": "dutydetailUsingGET", "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.", "required": true, "type": "string", "default": "QFTM1V9wSLtHt6IaIiL3neAsymh1aqtSdGJ5wFCEhmODQjgtcQWKloMGHiHCTIndAeYML3rmxtJxddFR7U+erKMzgLkfOyN8NMMR5QOYSS3X2EcN+dno14KzZIV1lt3VX+emKzPW5bZYI5P0oEXs/Lne7fqGXMyypax+5Bdik7M="}, {"name": "reportNo", "in": "query", "description": "案件编号. eg:LP86671672394116486<br>maxLength: 50</br>", "required": true, "type": "string"}], "responses": {"-10001": {"description": "System error"}, "200": {"description": "success", "schema": {"$ref": "#/definitions/response?object?"}}, "40008": {"description": "The reportNo is not exist"}}}}, "/api/audit/payment": {"post": {"tags": ["出纳角色"], "summary": "该API可以让出纳员根据最终要赔付的金额和受益人账号，进行转账赔付。", "description": " ", "operationId": "paymentUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.", "required": true, "type": "string", "default": "QFTM1V9wSLtHt6IaIiL3neAsymh1aqtSdGJ5wFCEhmODQjgtcQWKloMGHiHCTIndAeYML3rmxtJxddFR7U+erKMzgLkfOyN8NMMR5QOYSS3X2EcN+dno14KzZIV1lt3VX+emKzPW5bZYI5P0oEXs/Lne7fqGXMyypax+5Bdik7M="}, {"in": "body", "name": "paymentDto", "description": "paymentDto", "required": true, "schema": {"$ref": "#/definitions/PaymentDto"}}], "responses": {"-10001": {"description": "System error"}, "200": {"description": "success", "schema": {"$ref": "#/definitions/response?object?"}}, "40008": {"description": "The reportNo is not exist"}}}}, "/api/audit/paymentcheck": {"post": {"tags": ["理算员角色"], "summary": "该API让理算员根据保险理赔案件中所有环节的审定结果，做出最终的赔付金额计算。", "description": "理算员责任审定的详情，对案件进行审核理算。", "operationId": "paymentcheckUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.", "required": true, "type": "string", "default": "QFTM1V9wSLtHt6IaIiL3neAsymh1aqtSdGJ5wFCEhmODQjgtcQWKloMGHiHCTIndAeYML3rmxtJxddFR7U+erKMzgLkfOyN8NMMR5QOYSS3X2EcN+dno14KzZIV1lt3VX+emKzPW5bZYI5P0oEXs/Lne7fqGXMyypax+5Bdik7M="}, {"in": "body", "name": "repaymentCheckDto", "description": "repaymentCheckDto", "required": true, "schema": {"$ref": "#/definitions/RepaymentCheckDto"}}], "responses": {"-10001": {"description": "System error"}, "200": {"description": "success", "schema": {"$ref": "#/definitions/response?object?"}}, "40008": {"description": "The reportNo is not exist"}}}}, "/api/audit/paymentdetail": {"get": {"tags": ["理算员角色"], "summary": "该API用于查看保险理赔案例中，理算员提交的最终理算方案及赔付金额。", "description": "在调用此API之前，请确保您已经调用了/api/audit/paymentcheck 完成了赔付金额计算流程。", "operationId": "paymentdetailUsingGET", "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.", "required": true, "type": "string", "default": "QFTM1V9wSLtHt6IaIiL3neAsymh1aqtSdGJ5wFCEhmODQjgtcQWKloMGHiHCTIndAeYML3rmxtJxddFR7U+erKMzgLkfOyN8NMMR5QOYSS3X2EcN+dno14KzZIV1lt3VX+emKzPW5bZYI5P0oEXs/Lne7fqGXMyypax+5Bdik7M="}, {"name": "reportNo", "in": "query", "description": "案件编号. eg:LP86671672394116486<br>maxLength: 50</br>", "required": true, "type": "string"}], "responses": {"-10001": {"description": "System error"}, "200": {"description": "success", "schema": {"$ref": "#/definitions/response?object?"}}, "40008": {"description": "The reportNo is not exist"}}}}, "/api/audit/qualitydetail": {"get": {"tags": ["质检角色"], "summary": "该API可以让质检员根据保险理赔申请案件编号查看该理赔案件详情。", "description": "在给理赔案件质检打分之前，您需要先查看理赔过程的资料。 ", "operationId": "qualitydetailUsingGET", "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.", "required": true, "type": "string", "default": "QFTM1V9wSLtHt6IaIiL3neAsymh1aqtSdGJ5wFCEhmODQjgtcQWKloMGHiHCTIndAeYML3rmxtJxddFR7U+erKMzgLkfOyN8NMMR5QOYSS3X2EcN+dno14KzZIV1lt3VX+emKzPW5bZYI5P0oEXs/Lne7fqGXMyypax+5Bdik7M="}, {"name": "reportNo", "in": "query", "description": "案件编号. eg:LP86671672394116486<br>maxLength: 50</br>", "required": true, "type": "string"}], "responses": {"-10001": {"description": "System error"}, "200": {"description": "success", "schema": {"$ref": "#/definitions/response?object?"}}, "40008": {"description": "The reportNo is not exist"}}}}, "/api/audit/rejectclaim": {"post": {"tags": ["定责员角色"], "summary": "该API可以让定责员拒绝理赔。", "description": "对不符合理赔条件的理赔申请，定责员可以做出拒绝理赔的操作。", "operationId": "rejectclaimUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.", "required": true, "type": "string", "default": "QFTM1V9wSLtHt6IaIiL3neAsymh1aqtSdGJ5wFCEhmODQjgtcQWKloMGHiHCTIndAeYML3rmxtJxddFR7U+erKMzgLkfOyN8NMMR5QOYSS3X2EcN+dno14KzZIV1lt3VX+emKzPW5bZYI5P0oEXs/Lne7fqGXMyypax+5Bdik7M="}, {"in": "body", "name": "dutyDto", "description": "dutyDto", "required": true, "schema": {"$ref": "#/definitions/DutyDto"}}], "responses": {"-10001": {"description": "System error"}, "200": {"description": "success", "schema": {"$ref": "#/definitions/response?object?"}}, "40008": {"description": "The reportNo is not exist"}}}}, "/api/audit/rejectecheck": {"post": {"tags": ["核赔员角色"], "summary": "该API用于让审核人员查看理赔申请的详细内容。", "description": "在调用该API之前，请确保您已经调用/api/audit/reports 获取到了用户提交的保险理赔列表。", "operationId": "rejecteCheckUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.", "required": true, "type": "string", "default": "QFTM1V9wSLtHt6IaIiL3neAsymh1aqtSdGJ5wFCEhmODQjgtcQWKloMGHiHCTIndAeYML3rmxtJxddFR7U+erKMzgLkfOyN8NMMR5QOYSS3X2EcN+dno14KzZIV1lt3VX+emKzPW5bZYI5P0oEXs/Lne7fqGXMyypax+5Bdik7M="}, {"in": "body", "name": "checkDto", "description": "checkDto", "required": true, "schema": {"$ref": "#/definitions/CheckDto"}}], "responses": {"-10001": {"description": "System error"}, "200": {"description": "success", "schema": {"$ref": "#/definitions/response?object?"}}, "40007": {"description": "The reportNo cannot be empty"}, "40008": {"description": "The reportNo is not exist"}}}}, "/api/audit/reports": {"get": {"tags": ["定责员角色", "理算员角色", "调查员角色", "核赔员角色"], "summary": "该API可以获取所有用户提交的保险申请列表，并支持通过身份证号码检索相关的保险理赔记录。", "description": "在调用此API之前，请确保您已经提交了保险理赔申请。", "operationId": "reportsUsingGET", "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.", "required": true, "type": "string", "default": "QFTM1V9wSLtHt6IaIiL3neAsymh1aqtSdGJ5wFCEhmODQjgtcQWKloMGHiHCTIndAeYML3rmxtJxddFR7U+erKMzgLkfOyN8NMMR5QOYSS3X2EcN+dno14KzZIV1lt3VX+emKzPW5bZYI5P0oEXs/Lne7fqGXMyypax+5Bdik7M="}, {"name": "cardId", "in": "query", "description": "身份证ID. eg:610124199205242152<br>maxLength: 18</br>", "required": false, "type": "string"}], "responses": {"-10001": {"description": "System error"}, "200": {"description": "success", "schema": {"$ref": "#/definitions/response?object?"}}}}}, "/api/audit/singlecheck": {"post": {"tags": ["核赔员角色"], "summary": "该API可以让审核人员审核用户提交的理赔申请资料。 ", "description": "在调用该API之前，您可能需要先调用/api/user/getpolicy 和 /api/audit/claimdetail 去获取更多的投保和理赔资料详情。", "operationId": "singleCheckUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.", "required": true, "type": "string", "default": "QFTM1V9wSLtHt6IaIiL3neAsymh1aqtSdGJ5wFCEhmODQjgtcQWKloMGHiHCTIndAeYML3rmxtJxddFR7U+erKMzgLkfOyN8NMMR5QOYSS3X2EcN+dno14KzZIV1lt3VX+emKzPW5bZYI5P0oEXs/Lne7fqGXMyypax+5Bdik7M="}, {"in": "body", "name": "checkDto", "description": "checkDto", "required": true, "schema": {"$ref": "#/definitions/CheckDto"}}], "responses": {"-10001": {"description": "System error"}, "200": {"description": "success", "schema": {"$ref": "#/definitions/response?object?"}}, "40007": {"description": "The reportNo cannot be empty"}, "40008": {"description": "The reportNo is not exist"}}}}, "/api/audit/survey": {"post": {"tags": ["调查员角色"], "summary": "此API旨在支持调查员记录调查结果及其评论。", "description": "当用户提交的保险理赔申请材料存在疑点时，保险公司会启动更进一步的现场调查流程，搜集更多信息，最终根据调查结果，给出审核意见。 ", "operationId": "surveyUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.", "required": true, "type": "string", "default": "QFTM1V9wSLtHt6IaIiL3neAsymh1aqtSdGJ5wFCEhmODQjgtcQWKloMGHiHCTIndAeYML3rmxtJxddFR7U+erKMzgLkfOyN8NMMR5QOYSS3X2EcN+dno14KzZIV1lt3VX+emKzPW5bZYI5P0oEXs/Lne7fqGXMyypax+5Bdik7M="}, {"in": "body", "name": "surveyDto", "description": "surveyDto", "required": true, "schema": {"$ref": "#/definitions/SurveyDto"}}], "responses": {"-10001": {"description": "System error"}, "200": {"description": "success", "schema": {"$ref": "#/definitions/response?object?"}}, "40008": {"description": "The reportNo is not exist"}}}}, "/api/audit/surveydetail": {"get": {"tags": ["调查员角色"], "summary": "此API旨在允许用户去查看调查员的访谈结果和评论。", "description": "在调用此API之前，请确保您已经调用了/api/audit/survey API完成了现场勘察审核意见的提交。", "operationId": "surveyDetailUsingGET", "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.", "required": true, "type": "string", "default": "QFTM1V9wSLtHt6IaIiL3neAsymh1aqtSdGJ5wFCEhmODQjgtcQWKloMGHiHCTIndAeYML3rmxtJxddFR7U+erKMzgLkfOyN8NMMR5QOYSS3X2EcN+dno14KzZIV1lt3VX+emKzPW5bZYI5P0oEXs/Lne7fqGXMyypax+5Bdik7M="}, {"name": "reportNo", "in": "query", "description": "案件编号. eg:LP86671672394116486<br>maxLength: 50</br>", "required": true, "type": "string"}], "responses": {"-10001": {"description": "System error"}, "200": {"description": "success", "schema": {"$ref": "#/definitions/response?object?"}}, "40008": {"description": "The reportNo is not exist"}}}}, "/api/quality/getReport": {"get": {"tags": ["质检角色"], "summary": "该API可以让质检员根据保险理赔申请案件编号查看该理赔案件的审核状态。", "description": "已经完成理赔流程处理的案件才可以进行质检的流程。 ", "operationId": "getReportUsingGET", "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.", "required": true, "type": "string", "default": "QFTM1V9wSLtHt6IaIiL3neAsymh1aqtSdGJ5wFCEhmODQjgtcQWKloMGHiHCTIndAeYML3rmxtJxddFR7U+erKMzgLkfOyN8NMMR5QOYSS3X2EcN+dno14KzZIV1lt3VX+emKzPW5bZYI5P0oEXs/Lne7fqGXMyypax+5Bdik7M="}, {"name": "reportNo", "in": "query", "description": "reportNo", "required": false, "type": "string"}], "responses": {"-10001": {"description": "System error"}, "200": {"description": "success", "schema": {"$ref": "#/definitions/response?object?"}}}}}, "/api/quality/reports": {"get": {"tags": ["质检角色"], "summary": "质检员查看的案件列表", "description": "该API可以让质检员根据身份证ID筛选查询待质检的理赔申请案件列表。", "operationId": "reportsUsingGET_1", "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.", "required": true, "type": "string", "default": "QFTM1V9wSLtHt6IaIiL3neAsymh1aqtSdGJ5wFCEhmODQjgtcQWKloMGHiHCTIndAeYML3rmxtJxddFR7U+erKMzgLkfOyN8NMMR5QOYSS3X2EcN+dno14KzZIV1lt3VX+emKzPW5bZYI5P0oEXs/Lne7fqGXMyypax+5Bdik7M="}, {"in": "body", "name": "cardId", "description": "身份证ID. eg:610124199205242152<br>maxLength: 18</br>", "required": false, "schema": {"type": "string"}}], "responses": {"-10001": {"description": "System error"}, "200": {"description": "success", "schema": {"$ref": "#/definitions/response?object?"}}}}}, "/api/quality/score": {"post": {"tags": ["质检角色"], "summary": "该API可以让质检员根据理赔过程中的各个环节的审核处理过程进行满意度评分，并最终得出一个总分。", "description": "在调用此API之前，请确保该理赔申请案例处理流程已经完成。", "operationId": "scoreUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.", "required": true, "type": "string", "default": "QFTM1V9wSLtHt6IaIiL3neAsymh1aqtSdGJ5wFCEhmODQjgtcQWKloMGHiHCTIndAeYML3rmxtJxddFR7U+erKMzgLkfOyN8NMMR5QOYSS3X2EcN+dno14KzZIV1lt3VX+emKzPW5bZYI5P0oEXs/Lne7fqGXMyypax+5Bdik7M="}, {"in": "body", "name": "qualityDto", "description": "qualityDto", "required": true, "schema": {"$ref": "#/definitions/QualityDto"}}], "responses": {"-10001": {"description": "System error"}, "200": {"description": "success", "schema": {"$ref": "#/definitions/response?object?"}}}}}, "/api/user/claim": {"post": {"tags": ["客户角色"], "summary": "该API可以让用户发起理赔申请并提交理赔资料。 ", "description": "在调用此API之前，请确保您已经通过调用保险购买相关的API成功的购买了保险。并且在理赔申请发起之前，第一时间调用了/api/user/report完成了理赔报案。", "operationId": "claimUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.", "required": true, "type": "string", "default": "QFTM1V9wSLtHt6IaIiL3neAsymh1aqtSdGJ5wFCEhmODQjgtcQWKloMGHiHCTIndAeYML3rmxtJxddFR7U+erKMzgLkfOyN8NMMR5QOYSS3X2EcN+dno14KzZIV1lt3VX+emKzPW5bZYI5P0oEXs/Lne7fqGXMyypax+5Bdik7M="}, {"name": "id", "in": "query", "required": false, "type": "string"}, {"name": "reportNo", "in": "query", "required": false, "type": "string"}, {"name": "name", "in": "query", "required": false, "type": "string"}, {"name": "gender", "in": "query", "required": false, "type": "string"}, {"name": "birthdayDate", "in": "query", "required": false, "type": "string"}, {"name": "cardType", "in": "query", "required": false, "type": "string"}, {"name": "cardId", "in": "query", "required": false, "type": "string"}, {"name": "claimItem", "in": "query", "required": false, "type": "string"}, {"name": "accidentDetail", "in": "query", "required": false, "type": "string"}, {"name": "claimAmount", "in": "query", "required": false, "type": "string"}, {"name": "accountNumber", "in": "query", "required": false, "type": "string"}, {"name": "lossPath", "in": "query", "required": false, "type": "string"}, {"name": "invoiceCasePath", "in": "query", "required": false, "type": "string"}, {"name": "relationshipPath", "in": "query", "required": false, "type": "string"}, {"name": "delegate<PERSON><PERSON>", "in": "query", "required": false, "type": "string"}, {"name": "createDate", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "updateDate", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "closeDate", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "status", "in": "query", "required": false, "type": "string"}, {"name": "userID", "in": "query", "required": false, "type": "string"}, {"name": "idcards", "in": "query", "description": "idcards", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "insurances", "in": "query", "description": "insurances", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "invoices", "in": "query", "description": "invoices", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "relationships", "in": "query", "description": "relationships", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "delegaties", "in": "query", "description": "delegaties", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}], "responses": {"-10001": {"description": "System error"}, "200": {"description": "success", "schema": {"$ref": "#/definitions/response?object?"}}}}}, "/api/user/claim/progress": {"get": {"tags": ["客户角色"], "summary": "该API可以查看当前登录用户提交的理赔申请处理进度。", "description": "在调用该API之前，请确保您已经调用/api/user/claim 提交了理赔申请。", "operationId": "progressUsingGET", "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.", "required": true, "type": "string", "default": "QFTM1V9wSLtHt6IaIiL3neAsymh1aqtSdGJ5wFCEhmODQjgtcQWKloMGHiHCTIndAeYML3rmxtJxddFR7U+erKMzgLkfOyN8NMMR5QOYSS3X2EcN+dno14KzZIV1lt3VX+emKzPW5bZYI5P0oEXs/Lne7fqGXMyypax+5Bdik7M="}, {"name": "reportNo", "in": "query", "description": "案件编号. eg:LP86671672394116486<br>maxLength: 50</br>", "required": true, "type": "string"}], "responses": {"-10001": {"description": "System error"}, "200": {"description": "success", "schema": {"$ref": "#/definitions/response?object?"}}}}}, "/api/user/claimdetail": {"get": {"tags": ["客户角色"], "summary": "该API可以查看当前登录用户提交的理赔详情。", "description": "在调用该API之前，请确保您已经调用/api/user/myclaim 获取理赔申请列表。", "operationId": "claimdetailUsingGET", "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.", "required": true, "type": "string", "default": "QFTM1V9wSLtHt6IaIiL3neAsymh1aqtSdGJ5wFCEhmODQjgtcQWKloMGHiHCTIndAeYML3rmxtJxddFR7U+erKMzgLkfOyN8NMMR5QOYSS3X2EcN+dno14KzZIV1lt3VX+emKzPW5bZYI5P0oEXs/Lne7fqGXMyypax+5Bdik7M="}, {"name": "reportNo", "in": "query", "description": "案件编号. eg:LP86671672394116486<br>maxLength: 50</br>", "required": true, "type": "string"}], "responses": {"-10001": {"description": "System error"}, "200": {"description": "success", "schema": {"$ref": "#/definitions/response?object?"}}}}}, "/api/user/getpolicy": {"get": {"tags": ["核赔员角色"], "summary": "该API可以让审核人员获取用户的保单编号列表。", "description": "当审核人员在审核保险理赔的过程中，需要根据用户提供的身份证号码查询该用户在保险公司内部的保单编号。 ", "operationId": "getPolicyUsingGET", "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.", "required": true, "type": "string", "default": "QFTM1V9wSLtHt6IaIiL3neAsymh1aqtSdGJ5wFCEhmODQjgtcQWKloMGHiHCTIndAeYML3rmxtJxddFR7U+erKMzgLkfOyN8NMMR5QOYSS3X2EcN+dno14KzZIV1lt3VX+emKzPW5bZYI5P0oEXs/Lne7fqGXMyypax+5Bdik7M="}, {"name": "cardId", "in": "query", "description": "身份证ID. eg:610124199205242152<br>maxLength: 18</br>", "required": true, "type": "string"}], "responses": {"-10001": {"description": "System error"}, "200": {"description": "success", "schema": {"$ref": "#/definitions/response?object?"}}}}}, "/api/user/message": {"get": {"tags": ["客户角色"], "summary": "该API可以让当前登录用户接收系统发送的补充理赔材料的提示信息。 ", "description": "当用户提交理赔申请之后，如果核赔员认定提交的材料不足，那么就需要调用该API去提醒用户完成资料的补充。", "operationId": "messageUsingGET", "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.", "required": true, "type": "string", "default": "QFTM1V9wSLtHt6IaIiL3neAsymh1aqtSdGJ5wFCEhmODQjgtcQWKloMGHiHCTIndAeYML3rmxtJxddFR7U+erKMzgLkfOyN8NMMR5QOYSS3X2EcN+dno14KzZIV1lt3VX+emKzPW5bZYI5P0oEXs/Lne7fqGXMyypax+5Bdik7M="}], "responses": {"-10001": {"description": "System error"}, "200": {"description": "success", "schema": {"$ref": "#/definitions/response?object?"}}}}}, "/api/user/myclaim": {"get": {"tags": ["客户角色"], "summary": "该API可以查看当前登录用户提交的理赔申请列表。", "description": "在调用该API之前，请确保您已经调用/api/user/claim 提交了理赔申请。", "operationId": "myclaimsUsingGET", "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.", "required": true, "type": "string", "default": "QFTM1V9wSLtHt6IaIiL3neAsymh1aqtSdGJ5wFCEhmODQjgtcQWKloMGHiHCTIndAeYML3rmxtJxddFR7U+erKMzgLkfOyN8NMMR5QOYSS3X2EcN+dno14KzZIV1lt3VX+emKzPW5bZYI5P0oEXs/Lne7fqGXMyypax+5Bdik7M="}, {"name": "cardId", "in": "query", "description": "身份证ID. eg:610124199205242152<br>maxLength: 18</br>", "required": true, "type": "string"}], "responses": {"-10001": {"description": "System error"}, "200": {"description": "success", "schema": {"$ref": "#/definitions/response?object?"}}}}}, "/api/user/paylog": {"get": {"tags": ["客户角色"], "summary": "该API可以查看当前登录用户已经成功投保的保险购买记录。", "description": "在调用此API之前，请确保您已经通过调用保险购买相关的API成功的购买了保险。", "operationId": "paylogUsingGET", "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.", "required": true, "type": "string", "default": "QFTM1V9wSLtHt6IaIiL3neAsymh1aqtSdGJ5wFCEhmODQjgtcQWKloMGHiHCTIndAeYML3rmxtJxddFR7U+erKMzgLkfOyN8NMMR5QOYSS3X2EcN+dno14KzZIV1lt3VX+emKzPW5bZYI5P0oEXs/Lne7fqGXMyypax+5Bdik7M="}], "responses": {"-10001": {"description": "System error"}, "200": {"description": "success", "schema": {"$ref": "#/definitions/response?object?"}}}}}, "/api/user/report": {"post": {"tags": ["客户角色"], "summary": "该API用于让当前登录用户发起理赔报案。", "description": "在调用/api/user/claim API发起理赔申请材料提交之前，您需要先调用此API完成理赔报案。", "operationId": "reportUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.", "required": true, "type": "string", "default": "QFTM1V9wSLtHt6IaIiL3neAsymh1aqtSdGJ5wFCEhmODQjgtcQWKloMGHiHCTIndAeYML3rmxtJxddFR7U+erKMzgLkfOyN8NMMR5QOYSS3X2EcN+dno14KzZIV1lt3VX+emKzPW5bZYI5P0oEXs/Lne7fqGXMyypax+5Bdik7M="}, {"in": "body", "name": "tbReport", "description": "tbReport", "required": true, "schema": {"$ref": "#/definitions/TbReport"}}], "responses": {"-10001": {"description": "System error"}, "200": {"description": "success", "schema": {"$ref": "#/definitions/response?object?"}}}}}, "/insurance/application": {"post": {"tags": ["客户角色"], "summary": "此API用于申请旅行保险产品。", "description": "在申请旅行保险产品之前，您可能需要调用/insurance/travelInsuranceQuote获得报价。客户可以为多人购买保险产品，因此在同一报价单中，需要输入所有投保人信息。", "operationId": "applyInsuranceUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.", "required": true, "type": "string", "default": "QFTM1V9wSLtHt6IaIiL3neAsymh1aqtSdGJ5wFCEhmODQjgtcQWKloMGHiHCTIndAeYML3rmxtJxddFR7U+erKMzgLkfOyN8NMMR5QOYSS3X2EcN+dno14KzZIV1lt3VX+emKzPW5bZYI5P0oEXs/Lne7fqGXMyypax+5Bdik7M="}, {"in": "body", "name": "applyInsuranceModel", "description": "applyInsuranceModel", "required": true, "schema": {"$ref": "#/definitions/ApplyInsuranceModel"}}], "responses": {"-10001": {"description": "System error"}, "200": {"description": "success", "schema": {"$ref": "#/definitions/response"}}}}}, "/insurance/retrievalProductDetail": {"post": {"tags": ["客户角色"], "summary": "此API用于查询旅行保险产品的详细信息。", "description": "在调用该API去查询一个产品详情之前，您可能需要调用/insurance/retrievalProductList API去获取产品列表。", "operationId": "retrievalProductDetailUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.", "required": true, "type": "string", "default": "QFTM1V9wSLtHt6IaIiL3neAsymh1aqtSdGJ5wFCEhmODQjgtcQWKloMGHiHCTIndAeYML3rmxtJxddFR7U+erKMzgLkfOyN8NMMR5QOYSS3X2EcN+dno14KzZIV1lt3VX+emKzPW5bZYI5P0oEXs/Lne7fqGXMyypax+5Bdik7M="}, {"in": "body", "name": "productDetailModel", "description": "productDetailModel", "required": true, "schema": {"$ref": "#/definitions/ProductDetailModel"}}], "responses": {"-10001": {"description": "System error"}, "200": {"description": "success", "schema": {"$ref": "#/definitions/response"}}}}}, "/insurance/retrievalProductList": {"post": {"tags": ["客户角色"], "summary": "此API用于查询旅行保险产品列表。", "description": "查询保险产品的列表信息", "operationId": "retrievalProductListUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.", "required": true, "type": "string", "default": "QFTM1V9wSLtHt6IaIiL3neAsymh1aqtSdGJ5wFCEhmODQjgtcQWKloMGHiHCTIndAeYML3rmxtJxddFR7U+erKMzgLkfOyN8NMMR5QOYSS3X2EcN+dno14KzZIV1lt3VX+emKzPW5bZYI5P0oEXs/Lne7fqGXMyypax+5Bdik7M="}], "responses": {"-10001": {"description": "System error"}, "200": {"description": "success", "schema": {"$ref": "#/definitions/response"}}}}}, "/insurance/subscription": {"post": {"tags": ["客户角色"], "summary": "此API用于购买旅行保险产品。", "description": "在购买旅游保险之前，您需要调用/insurance/application去申请保险产品。", "operationId": "subscriptionInsuranceUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.", "required": true, "type": "string", "default": "QFTM1V9wSLtHt6IaIiL3neAsymh1aqtSdGJ5wFCEhmODQjgtcQWKloMGHiHCTIndAeYML3rmxtJxddFR7U+erKMzgLkfOyN8NMMR5QOYSS3X2EcN+dno14KzZIV1lt3VX+emKzPW5bZYI5P0oEXs/Lne7fqGXMyypax+5Bdik7M="}, {"in": "body", "name": "subscriptionModel", "description": "subscriptionModel", "required": true, "schema": {"$ref": "#/definitions/InsuranceSubscriptionModel"}}], "responses": {"-10001": {"description": "System error"}, "200": {"description": "success", "schema": {"$ref": "#/definitions/response"}}}}}, "/insurance/travelInsuranceQuote": {"post": {"tags": ["客户角色"], "summary": "此API用于购买旅行保险产品。", "description": "在购买旅游保险之前，您需要调用/insurance/application去申请保险产品。", "operationId": "travelInsuranceQuoteUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.", "required": true, "type": "string", "default": "QFTM1V9wSLtHt6IaIiL3neAsymh1aqtSdGJ5wFCEhmODQjgtcQWKloMGHiHCTIndAeYML3rmxtJxddFR7U+erKMzgLkfOyN8NMMR5QOYSS3X2EcN+dno14KzZIV1lt3VX+emKzPW5bZYI5P0oEXs/Lne7fqGXMyypax+5Bdik7M="}, {"in": "body", "name": "travelInsuranceQuoteModel", "description": "travelInsuranceQuoteModel", "required": true, "schema": {"$ref": "#/definitions/TravelInsuranceQuoteModel"}}], "responses": {"-10001": {"description": "System error"}, "200": {"description": "success", "schema": {"$ref": "#/definitions/response"}}}}}}, "definitions": {"ApplyInsuranceChildModel": {"type": "object", "required": ["dateofbirth", "gender", "givenname", "idnumber", "idtype", "relationship", "surnamename"], "properties": {"address": {"type": "string"}, "dateofbirth": {"type": "string", "example": *************, "description": "Birth day of the insured person.<br>maxLength: 20</br>", "allowEmptyValue": false}, "email": {"type": "string"}, "gender": {"type": "string", "example": "M", "description": "Gender of the insured person.<br>maxLength: 1</br>", "allowEmptyValue": false}, "givenname": {"type": "string", "example": "Yun", "description": "The given name printed on HKID card or Passport of the insured person.<br>maxLength: 20</br>", "allowEmptyValue": false}, "idnumber": {"type": "string", "example": 610425197504240001, "description": "Number of  HKID card or Passport of the insured person.<br>maxLength: 35</br>", "allowEmptyValue": false}, "idtype": {"type": "string", "example": "P", "description": "The type of ID you used to buy an insurance product. <br></br>Possible values: HKID card, Passport.<br>maxLength: 1</br>", "allowEmptyValue": false}, "phoneNumber": {"type": "string"}, "relationship": {"type": "string", "example": "SELF", "description": "The relationship bewteen you and the insured person. <br></br>Possible values:SELF - Self,SPOU - Spouse,<br></br>PARE - Parent,CHIL - Child,FRIE - Friend,RELA - Relative. <br>maxLength: 4</br>", "allowEmptyValue": false}, "surnamename": {"type": "string", "example": "Ma", "description": "The surname printed on HKID card or Passport of the insured person.<br>maxLength: 20</br>", "allowEmptyValue": false}}, "title": "ApplyInsuranceChildModel"}, "ApplyInsuranceModel": {"type": "object", "required": ["quoteid"], "properties": {"applySub": {"type": "array", "items": {"$ref": "#/definitions/ApplyInsuranceChildModel"}}, "quoteid": {"type": "string", "example": "Q000000002", "description": "The quotation id returned after you call insurance quotation API sucessfully.<br>maxLength: 35</br>", "allowEmptyValue": false}}, "title": "ApplyInsuranceModel"}, "CheckDto": {"type": "object", "properties": {"auditor": {"type": "string", "example": "核赔员", "description": "审核员.<br>maxLength: 50</br>", "allowEmptyValue": false}, "checkContent": {"type": "string", "example": {"isCoverage": true, "isValid": true, "isDoubt": true}, "description": "审核内容：(1)是否在保险责任内？(2)索赔材料是否全部有效？(3)是否理赔调查？<br>maxLength: 1000</br>", "allowEmptyValue": false}, "checkRemark": {"type": "string", "example": "备注", "description": "审核备注.<br>maxLength: 1000</br>", "allowEmptyValue": false}, "policyId": {"type": "string", "example": "Tr000738819", "description": "保单编号.<br>maxLength: 50</br>", "allowEmptyValue": false}, "reportNo": {"type": "string", "example": "*******************", "description": "报案编号.<br>maxLength: 50</br>", "allowEmptyValue": false}}, "title": "CheckDto"}, "DutyDto": {"type": "object", "properties": {"dutyJudgement": {"type": "string", "example": "已定责，通过", "description": "责任审定意见.<br>maxLength: 50</br>", "allowEmptyValue": false}, "dutyJudger": {"type": "string", "example": "定责员", "description": "责任审定人.<br>maxLength: 50</br>", "allowEmptyValue": false}, "reportNo": {"type": "string", "example": "*******************", "description": "报案编号.<br>maxLength: 50</br>", "allowEmptyValue": false}}, "title": "DutyDto"}, "InsuranceSubscriptionModel": {"type": "object", "required": ["accountNumber", "accountType", "quoteid"], "properties": {"accountNumber": {"type": "string", "example": ****************, "description": "The number of the account, with which you'd like to pay for the travel insurance product.<br>maxLength: 34</br>", "allowEmptyValue": false}, "accountType": {"type": "string", "example": "CRED", "description": "The type of the bank account that is supported in the subscription. <br></br>Possible values: CURR - Current,SAVI - Saving,CRED - Credit Card.<br>maxLength: 4</br>", "allowEmptyValue": false}, "quoteid": {"type": "string", "example": "Q000000002", "description": "The quotation id returned after you call insurance application API sucessfully.<br>maxLength: 35</br>", "allowEmptyValue": false}}, "title": "InsuranceSubscriptionModel"}, "PaymentDto": {"type": "object", "properties": {"amount": {"type": "number", "example": 800, "description": "赔付金额.<br>maxLength: 20</br>", "allowEmptyValue": false}, "reportNo": {"type": "string", "example": "*******************", "description": "报案编号.<br>maxLength: 50</br>", "allowEmptyValue": false}, "transferIn": {"type": "string", "example": "********************", "description": "转入账户.<br>maxLength: 50</br>", "allowEmptyValue": false}, "transferOut": {"type": "string", "example": "********************", "description": "转出账户.<br>maxLength: 50</br>", "allowEmptyValue": false}}, "title": "PaymentDto"}, "ProductDetailModel": {"type": "object", "properties": {"productcode": {"type": "string", "description": "A uniqe code to identify a dedicated travel insurance product.<br>maxLength: 35</br>", "allowEmptyValue": false}}, "title": "ProductDetailModel"}, "QualityDto": {"type": "object", "properties": {"qualityInfo": {"$ref": "#/definitions/QualityVo"}, "reportNo": {"type": "string"}}, "title": "QualityDto"}, "QualityVo": {"type": "object", "properties": {"attitude": {"type": "string"}, "duration": {"type": "string"}, "professional": {"type": "string"}, "satisfaction": {"type": "string"}, "speed": {"type": "string"}}, "title": "QualityVo"}, "RepaymentCheckDto": {"type": "object", "properties": {"claimApprover": {"type": "string", "example": "理算员", "description": "理算人员.<br>maxLength: 50</br>", "allowEmptyValue": false}, "claimFinalAmount": {"type": "number", "example": 800, "description": "最终赔付金额.<br>maxLength: 50</br>", "allowEmptyValue": false}, "claimRemark": {"type": "string", "example": "备注", "description": "理算备注.<br>maxLength: 50</br>", "allowEmptyValue": false}, "claimTotalAmount": {"type": "number", "example": 1000, "description": "申请理赔总金额.<br>maxLength: 50</br>", "allowEmptyValue": false}, "invoiceTotalAmount": {"type": "number", "example": 1000, "description": "发票总金额.<br>maxLength: 50</br>", "allowEmptyValue": false}, "lossRate": {"type": "string", "example": "80%", "description": "比例赔偿方式.<br>maxLength: 50</br>", "allowEmptyValue": false}, "reportNo": {"type": "string", "example": "*******************", "description": "报案编号.<br>maxLength: 50</br>", "allowEmptyValue": false}}, "title": "RepaymentCheckDto"}, "SurveyDto": {"type": "object", "properties": {"reportNo": {"type": "string", "example": "*******************", "description": "报案编号.<br>maxLength: 50</br>", "allowEmptyValue": false}, "surveyRemark": {"type": "string", "example": "备注", "description": "勘查备注.<br>maxLength: 1000</br>", "allowEmptyValue": false}, "surveyResult": {"type": "string", "example": "已勘查，通过", "description": "勘查结果.<br>maxLength: 1000</br>", "allowEmptyValue": false}}, "title": "SurveyDto"}, "TbReport": {"type": "object", "properties": {"applyReason": {"type": "string"}, "cardId": {"type": "string"}, "claimItem": {"type": "string"}, "closeDate": {"type": "string", "format": "date-time"}, "createDate": {"type": "string", "format": "date-time"}, "definitionKey": {"type": "string"}, "diagnosis": {"type": "string"}, "hospital": {"type": "string"}, "id": {"type": "string"}, "lossDate": {"type": "string"}, "lossDetail": {"type": "string"}, "lossLocation": {"type": "string"}, "lossLocationDetail": {"type": "string"}, "name": {"type": "string"}, "overseaLoss": {"type": "boolean"}, "phoneNumber": {"type": "string"}, "processInstanceId": {"type": "string"}, "relationship": {"type": "string"}, "remark": {"type": "string"}, "reportType": {"type": "string"}, "status": {"type": "string"}, "updateDate": {"type": "string", "format": "date-time"}}, "title": "TbReport"}, "TravelInsuranceQuoteModel": {"type": "object", "required": ["coverageType", "productCode", "startDate"], "properties": {"childrenNo": {"type": "integer", "format": "int32", "example": 1, "description": "The total number of the insured children in this insurance quotation.<br/>Possible values: 0,1,2,3,4,5,6 <br/> maxLength: 2", "allowEmptyValue": true}, "coverageType": {"type": "string", "example": "SIGL", "description": "The coverage type you set in this insurance product quotation. <br/> Possible values: <br/> SIGL - Single Trip Cover <br/>ANLC - Annual China Cover <br/>ANLG - Annual Global Cover <br/>maxLength: 4", "allowEmptyValue": false}, "dateOfBirth": {"type": "number", "example": 978250234000, "description": "Birthday of the insured person. <br/>maxLength: 20", "allowEmptyValue": true}, "destination": {"type": "string", "example": "Mainland China and Macau", "description": "The destination of trip. <br/>Possible values: <br/>Mainland China and Macau, <br/>Asia, <br/>Worldwide <br/>maxLength: 35", "allowEmptyValue": true}, "endDate": {"type": "number", "example": 1639826636000, "description": "The expiry date of the travel insurancce product. <br/>maxLength: 20", "allowEmptyValue": true}, "friendsOrRelatives": {"type": "string", "example": "Y", "description": "If the coverageType field is set ANLC or ANLG, please keep this field empty.<br/> Possible values: Y - Yes <br/>empty value. <br/>maxLength: 1", "allowEmptyValue": true}, "friendsOrRelativesNo": {"type": "integer", "format": "int32", "example": 6, "description": "The total number of the insured friends and relatives in this single trip insurance quotation. <br/>Possible values: 0, 1,2,3,4,5,6 <br/>maxLength: 2", "allowEmptyValue": true}, "idNumber": {"type": "string", "example": 15, "description": "The number of the HKID card or passport of the insured person. <br/> maxLength: 35", "allowEmptyValue": true}, "idType": {"type": "string", "example": "P", "description": "The Id type of the insured person. <br/>Possible values:<br/>I - ID Card,<br/>P - Passport. <br/>maxLength: 1", "allowEmptyValue": true}, "myChildren": {"type": "string", "example": "Y", "description": "Please set this field value as Y if you want to buy the insurance product for your children. Otherwise, keep this field empty.” <br/>Possible values: Y - Yes, <br/>empty value <br/>maxLength: 1", "allowEmptyValue": true}, "parents": {"type": "string", "description": "If coverageType field is set SIGL, please keep this field empty.<br/>Possible values: <br/>Y - Yes<br/>empty value.<br/>maxLength: 1", "allowEmptyValue": true}, "parentsNo": {"type": "integer", "format": "int32", "description": "The total number of the insured parents in this insurance quotation <br/>Possible values: 0,1,2 <br/>maxLength: 2", "allowEmptyValue": true}, "productCode": {"type": "string", "example": "T002", "description": "A uniqe code to identify a dedicated travel insurance product. <br/>maxLength: 35", "allowEmptyValue": false}, "self": {"type": "string", "example": "Y", "description": "If you set this field value as Y, it means you set yourself as a insured person. Otherwise, please keep this field empty.<br/>Possible values:<br/>Y - Yes,<br/>empty value <br/> maxLength: 1", "allowEmptyValue": true}, "spouse": {"type": "string", "example": "Y", "description": "If you set this field value as Y, it means you set your spouse as a insured person. Otherwise, please keep this field empty.<br/>Possible values:<br/>Y - Yes,<br/>empty value <br/> maxLength: 1", "allowEmptyValue": true}, "startDate": {"type": "number", "example": 1608290636000, "description": "The effective date of the travel insurancce product.<br/>maxLength: 20", "allowEmptyValue": false}}, "title": "TravelInsuranceQuoteModel"}, "response": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32", "description": "code", "allowEmptyValue": false}, "data": {"type": "object", "description": "data", "allowEmptyValue": false}, "desc": {"type": "string", "description": "desc", "allowEmptyValue": false}}, "title": "response"}, "response?object?": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32", "description": "code", "allowEmptyValue": false}, "data": {"type": "object", "description": "data", "allowEmptyValue": false}, "desc": {"type": "string", "description": "desc", "allowEmptyValue": false}}, "title": "response?object?"}, "response?string?": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32", "description": "code", "allowEmptyValue": false}, "data": {"type": "string", "description": "data", "allowEmptyValue": false}, "desc": {"type": "string", "description": "desc", "allowEmptyValue": false}}, "title": "response?string?"}}}