{"swagger": "2.0", "info": {"description": "该服务模块主要包含了所有外汇交易相关的业务API。包含了外币的买入和卖出业务。", "version": "1.0", "title": "外汇业务", "contact": {"name": "<EMAIL>"}}, "schemes": ["https"], "host": "simnectz.hsu.edu.hk/lbsgateway/foreign-experience", "basePath": "/", "tags": [{"name": "Market Information", "description": "查询并显示最新的外汇市场信息。"}, {"name": "Transaction", "description": "客户外汇账户中的交易处理。"}], "paths": {"/foreignExchange/exchangerateinformation": {"post": {"tags": ["Market Information"], "summary": "这个api是为查询货币汇率信息而设计的。", "description": "在买入或卖出外币之前，您可能需要调用该API去查询当前货币的市场汇率信息。", "operationId": "exchangerateinformationUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "颁发给第三方应用程序的最新客户授权令牌，以JWT格式访问该银行的客户数据。当开发者在调用API访问银行客户数据的时候，都需要用到该令牌。<br>最大长度: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "为前后端的每个请求唯一生成的128位随机UUID格式的消息ID。 该参数为内部参数，在调用API的时候不用修改该参数的内容。直接使用即可。<br>最大长度: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "当用户登录的时候生成的客户id。  该参数为内部参数，在调用API的时候不用修改该参数的内容。直接使用即可。<br>最大长度: 50</br> ", "required": true, "type": "string", "default": "devin"}], "responses": {"200": {"description": "查询成功。 （由Get方法返回）", "schema": {"$ref": "#/definitions/ResultUtil"}}, "201": {"description": "正常执行，请求成功。（由Post方法返回）", "schema": {"$ref": "#/definitions/ResultUtil"}}, "403": {"description": "令牌的作用范围不正确或违反了安全策略。操作：请检查您是否使用合法授权用户帐户的正确令牌。"}, "404": {"description": "请求的帐户不存在。操作：请确保您输入的帐号和帐户类型正确。"}, "500": {"description": "API网关或微服务出现问题。操作：检查您的网络，稍后再试。"}}}}, "/foreignExchange/foreignExchange": {"post": {"tags": ["Transaction"], "summary": "该API用于HKD和USD,CNY,AUD,CAD,CHF,EUR,GBP,JPY,NZD,SGD.之间的兑换业务。", "description": "可以买入或者卖出外币。在买入外币之前，请确保客户已经有了一个外汇账户，即003结尾的账户 (外汇账户)。如果还没有，可能需要先调用/deposit/account/accountCreation 去创建一个外汇账户。", "operationId": "currencyExchangeUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "颁发给第三方应用程序的最新客户授权令牌，以JWT格式访问该银行的客户数据。当开发者在调用API访问银行客户数据的时候，都需要用到该令牌。<br>最大长度: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "为前后端的每个请求唯一生成的128位随机UUID格式的消息ID。 该参数为内部参数，在调用API的时候不用修改该参数的内容。直接使用即可。<br>最大长度: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "当用户登录的时候生成的客户id。  该参数为内部参数，在调用API的时候不用修改该参数的内容。直接使用即可。<br>最大长度: 50</br> ", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "exchangeModel", "description": "exchangeModel", "required": true, "schema": {"$ref": "#/definitions/ExchangeModel"}}], "responses": {"200": {"description": "查询成功。 （由Get方法返回）", "schema": {"$ref": "#/definitions/ResultUtil"}}, "201": {"description": "正常执行，请求成功。（由Post方法返回）", "schema": {"$ref": "#/definitions/ResultUtil"}}, "403": {"description": "令牌的作用范围不正确或违反了安全策略。操作：请检查您是否使用合法授权用户帐户的正确令牌。"}, "404": {"description": "请求的帐户不存在。操作：请确保您输入的帐号和帐户类型正确。"}, "500": {"description": "API网关或微服务出现问题。操作：检查您的网络，稍后再试。"}}}}}, "definitions": {"ExchangeModel": {"type": "object", "required": ["actionCode", "ccycode", "debitaccountnumber", "exchangeAmount", "fexAccountNumber"], "properties": {"actionCode": {"type": "string", "example": "Buy", "description": "买入或者卖出外汇。支持的值：Buy（买入） Sell（卖出）<br>最大长度: 4</br>", "allowEmptyValue": false}, "ccycode": {"type": "string", "example": "USD", "description": "交易的货币类型。 支持 HKD,USD,CNY,AUD,CAD,CHF,EUR,GBP,JPY,NZD,SGD<br>最大长度: 3</br>", "allowEmptyValue": false}, "debitaccountnumber": {"type": "string", "example": "***********************", "description": "和该外汇账户关联的储蓄账户。如001结尾的储蓄账户和002结尾的活期账户。<br>最大长度: 34", "allowEmptyValue": false}, "exchangeAmount": {"type": "number", "example": 100.0, "description": "交易金额。<br>5 decimals,最大长度: 13", "allowEmptyValue": false}, "fexAccountNumber": {"type": "string", "example": "***********************", "description": "外汇账户号码。<br>最大长度: 34", "allowEmptyValue": false}}, "title": "ExchangeModel", "description": "Input Params Model"}, "ResultUtil": {"type": "object", "properties": {"code": {"type": "string"}, "data": {"type": "object"}, "msg": {"type": "string"}}, "title": "ResultUtil"}}}