{"swagger": "2.0", "info": {"description": "该服务模块主要包含了所有与信用卡相关的业务API。包含了信用卡开户，额度审批，信用卡交易，积分兑换，信用卡额度修改，信用卡支付，信用卡还款，信用卡挂失， 信用卡交易记录查询等多个信用卡业务。 当您在开发信用卡相关的业务模块时，可以来这里找到您需要的API。", "version": "1.0", "title": "信用卡业务", "contact": {"name": "<EMAIL>"}}, "schemes": ["https"], "host": "simnectz.hsu.edu.hk/lbsgateway/creditcard-experience", "basePath": "/", "tags": [{"name": "Merchant", "description": "商户信息及其产品/交易查询。"}, {"name": "Repayment", "description": "未付信用卡账单查询 & 还款。"}, {"name": "Account Information & Maintenance", "description": "信用卡处理,包括信用卡申请/取消和限额。"}, {"name": "Rewards", "description": "信用卡奖励积分系统，包括奖励积分兑换、历史记录和积分查询。"}, {"name": "Transaction", "description": "信用卡交易处理和交易详情查询。"}], "paths": {"/creditcard/creditCardRepayment": {"post": {"tags": ["Repayment"], "summary": "该API用于信用卡还款业务。", "description": "例如，当信用卡客户刷卡消费了2000HKD，那么当该客户收到信用卡账单之后，做还款的操作时，就可以调用该API来操作还款，系统会从用户输入的扣款账户和还款金额中扣除相应的金额来完成本次还款操作。在做还款操作之前，客户可能需要先查询一下未还总金额，可以调用/creditcard/outstandingPayment 来查看。", "operationId": "creditCardRepaymentUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "颁发给第三方应用程序的最新客户授权令牌，以JWT格式访问该银行的客户数据。当开发者在调用API访问银行客户数据的时候，都需要用到该令牌。<br>最大长度: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "为前后端的每个请求唯一生成的128位随机UUID格式的消息ID。 该参数为内部参数，在调用API的时候不用修改该参数的内容。直接使用即可。<br>最大长度: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "当用户登录的时候生成的客户id。  该参数为内部参数，在调用API的时候不用修改该参数的内容。直接使用即可。 <br>最大长度: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "repaymentModel", "description": "repaymentModel", "required": true, "schema": {"$ref": "#/definitions/RepaymentModel"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ResultUtil"}}}}}, "/creditcard/creditLimitDetails": {"post": {"tags": ["Account Information & Maintenance"], "summary": "该API可以通过信用卡账户获取信用卡的额度详情。", "description": "如审批额度，可用额度，已用额度，可取现额度。", "operationId": "retCreditLimitDetailsUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "颁发给第三方应用程序的最新客户授权令牌，以JWT格式访问该银行的客户数据。当开发者在调用API访问银行客户数据的时候，都需要用到该令牌。<br>最大长度: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "为前后端的每个请求唯一生成的128位随机UUID格式的消息ID。 该参数为内部参数，在调用API的时候不用修改该参数的内容。直接使用即可。<br>最大长度: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "当用户登录的时候生成的客户id。  该参数为内部参数，在调用API的时候不用修改该参数的内容。直接使用即可。 <br>最大长度: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "creditCardModel", "description": "creditCardModel", "required": true, "schema": {"$ref": "#/definitions/CreditCardModel"}}], "responses": {"200": {"description": "查询成功。 （由Get方法返回）", "schema": {"$ref": "#/definitions/ResultUtil«LimitsRetrieveModel»"}}, "201": {"description": "正常执行，请求成功。（由Post方法返回）", "schema": {"$ref": "#/definitions/ResultUtil«LimitsRetrieveModel»"}}, "403": {"description": "令牌的作用范围不正确或违反了安全策略。操作：请检查您是否使用合法授权用户帐户的正确令牌。"}, "404": {"description": "请求的帐户不存在。操作：请确保您输入的帐号和帐户类型正确。"}, "500": {"description": "API网关或微服务出现问题。操作：检查您的网络，稍后再试。"}}}}, "/creditcard/limitDecrease": {"post": {"tags": ["Account Information & Maintenance"], "summary": "该API用于降低信用卡的信用额度。", "description": "例如，客户的信用卡额度现在是20000HKD，想要降低到15000HKD，可以直接调用该API来完成业务操作。如果需要增加额度，可以调用/creditcard/limitIncrease API。", "operationId": "limitDecreaseUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "颁发给第三方应用程序的最新客户授权令牌，以JWT格式访问该银行的客户数据。当开发者在调用API访问银行客户数据的时候，都需要用到该令牌。<br>最大长度: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "为前后端的每个请求唯一生成的128位随机UUID格式的消息ID。 该参数为内部参数，在调用API的时候不用修改该参数的内容。直接使用即可。<br>最大长度: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "当用户登录的时候生成的客户id。  该参数为内部参数，在调用API的时候不用修改该参数的内容。直接使用即可。 <br>最大长度: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "limitsChangeInputModel", "description": "limitsChangeInputModel", "required": true, "schema": {"$ref": "#/definitions/LimitsChangeInputModel"}}], "responses": {"200": {"description": "查询成功。 （由Get方法返回）", "schema": {"$ref": "#/definitions/ResultUtil"}}, "201": {"description": "正常执行，请求成功。（由Post方法返回）", "schema": {"$ref": "#/definitions/ResultUtil"}}, "403": {"description": "令牌的作用范围不正确或违反了安全策略。操作：请检查您是否使用合法授权用户帐户的正确令牌。"}, "404": {"description": "请求的帐户不存在。操作：请确保您输入的帐号和帐户类型正确。"}, "500": {"description": "API网关或微服务出现问题。操作：检查您的网络，稍后再试。"}}}}, "/creditcard/limitIncrease": {"post": {"tags": ["Account Information & Maintenance"], "summary": "该API用于增加信用卡的信用额度。", "description": "例如，客户的信用卡额度现在是20000HKD，想要增加到25000HKD，可以直接调用该API来完成业务操作。如果需要降低额度，可以调用/creditcard/limitDecrease API。", "operationId": "limitIncreaseUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "颁发给第三方应用程序的最新客户授权令牌，以JWT格式访问该银行的客户数据。当开发者在调用API访问银行客户数据的时候，都需要用到该令牌。<br>最大长度: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "为前后端的每个请求唯一生成的128位随机UUID格式的消息ID。 该参数为内部参数，在调用API的时候不用修改该参数的内容。直接使用即可。<br>最大长度: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "当用户登录的时候生成的客户id。  该参数为内部参数，在调用API的时候不用修改该参数的内容。直接使用即可。 <br>最大长度: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "limitsChangeInputModel", "description": "limitsChangeInputModel", "required": true, "schema": {"$ref": "#/definitions/IncreaseLimitsChangeInputModel"}}], "responses": {"200": {"description": "查询成功。 （由Get方法返回）", "schema": {"$ref": "#/definitions/ResultUtil"}}, "201": {"description": "正常执行，请求成功。（由Post方法返回）", "schema": {"$ref": "#/definitions/ResultUtil"}}, "403": {"description": "令牌的作用范围不正确或违反了安全策略。操作：请检查您是否使用合法授权用户帐户的正确令牌。"}, "404": {"description": "请求的帐户不存在。操作：请确保您输入的帐号和帐户类型正确。"}, "500": {"description": "API网关或微服务出现问题。操作：检查您的网络，稍后再试。"}}}}, "/creditcard/lossReporting": {"post": {"tags": ["Account Information & Maintenance"], "summary": "该API用于信用卡挂失业务。", "description": "当客户的信用卡有丢失的时候，可以调用该API进行挂失业务。挂失后，该账户则不能再进行交易。", "operationId": "lossReportingUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "颁发给第三方应用程序的最新客户授权令牌，以JWT格式访问该银行的客户数据。当开发者在调用API访问银行客户数据的时候，都需要用到该令牌。<br>最大长度: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "为前后端的每个请求唯一生成的128位随机UUID格式的消息ID。 该参数为内部参数，在调用API的时候不用修改该参数的内容。直接使用即可。<br>最大长度: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "当用户登录的时候生成的客户id。  该参数为内部参数，在调用API的时候不用修改该参数的内容。直接使用即可。 <br>最大长度: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "creditCardModel", "description": "creditCardModel", "required": true, "schema": {"$ref": "#/definitions/CreditCardModel"}}], "responses": {"200": {"description": "查询成功。 （由Get方法返回）", "schema": {"$ref": "#/definitions/ResultUtil"}}, "201": {"description": "正常执行，请求成功。（由Post方法返回）", "schema": {"$ref": "#/definitions/ResultUtil"}}, "403": {"description": "令牌的作用范围不正确或违反了安全策略。操作：请检查您是否使用合法授权用户帐户的正确令牌。"}, "404": {"description": "请求的帐户不存在。操作：请确保您输入的帐号和帐户类型正确。"}, "500": {"description": "API网关或微服务出现问题。操作：检查您的网络，稍后再试。"}}}}, "/creditcard/outstandingPayment": {"post": {"tags": ["Repayment"], "summary": "该API用于查询信用卡所有未还金额。", "description": "例如，当用户想要查看自己信用卡未还金额时，就可以调用该API。查看完未还金额之后，如果需要进行还款的话，可以调用/creditCardRepayment来完成。 ", "operationId": "outstandingPaymentUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "颁发给第三方应用程序的最新客户授权令牌，以JWT格式访问该银行的客户数据。当开发者在调用API访问银行客户数据的时候，都需要用到该令牌。<br>最大长度: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "为前后端的每个请求唯一生成的128位随机UUID格式的消息ID。 该参数为内部参数，在调用API的时候不用修改该参数的内容。直接使用即可。<br>最大长度: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "当用户登录的时候生成的客户id。  该参数为内部参数，在调用API的时候不用修改该参数的内容。直接使用即可。 <br>最大长度: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "creditCardModel", "description": "creditCardModel", "required": true, "schema": {"$ref": "#/definitions/CreditCardModel"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ResultUtil«PaymentDetailsModel»"}}}}}, "/creditcard/transactionDetails": {"post": {"tags": ["Transaction"], "summary": "该API可以让客户查询自己的信用卡交易历史。", "description": "通过输入信用卡账户号码和交易起始和结束时间，就可以查询出想要的结果。 如果客户还没有任何交易记录，可能需要调用/creditcard/transactionPosting 来模拟交易的过程，在系统中产生交易记录。", "operationId": "transactionDetailsUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "颁发给第三方应用程序的最新客户授权令牌，以JWT格式访问该银行的客户数据。当开发者在调用API访问银行客户数据的时候，都需要用到该令牌。<br>最大长度: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "为前后端的每个请求唯一生成的128位随机UUID格式的消息ID。 该参数为内部参数，在调用API的时候不用修改该参数的内容。直接使用即可。<br>最大长度: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "当用户登录的时候生成的客户id。  该参数为内部参数，在调用API的时候不用修改该参数的内容。直接使用即可。 <br>最大长度: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "reTransactionDetailsModel", "description": "reTransactionDetailsModel", "required": true, "schema": {"$ref": "#/definitions/ReTransactionDetailsModel"}}], "responses": {"200": {"description": "查询成功。 （由Get方法返回）", "schema": {"$ref": "#/definitions/ResultUtil«List«TransactionDetailsModel»»"}}, "201": {"description": "正常执行，请求成功。（由Post方法返回）", "schema": {"$ref": "#/definitions/ResultUtil«List«TransactionDetailsModel»»"}}, "403": {"description": "令牌的作用范围不正确或违反了安全策略。操作：请检查您是否使用合法授权用户帐户的正确令牌。"}, "404": {"description": "请求的帐户不存在。操作：请确保您输入的帐号和帐户类型正确。"}, "500": {"description": "API网关或微服务出现问题。操作：检查您的网络，稍后再试。"}}}}, "/creditcard/transactionPosting": {"post": {"tags": ["Transaction"], "summary": "该API用于模拟信用卡刷卡消费功能。", "description": "例如，当您想要开发一个模拟的信用卡功能模块时，可能需要开发一个模拟交易的功能，可以生成很多交易记录，那么就可以直接调用该API。", "operationId": "transactionPostingUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "颁发给第三方应用程序的最新客户授权令牌，以JWT格式访问该银行的客户数据。当开发者在调用API访问银行客户数据的时候，都需要用到该令牌。<br>最大长度: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "为前后端的每个请求唯一生成的128位随机UUID格式的消息ID。 该参数为内部参数，在调用API的时候不用修改该参数的内容。直接使用即可。<br>最大长度: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "当用户登录的时候生成的客户id。  该参数为内部参数，在调用API的时候不用修改该参数的内容。直接使用即可。 <br>最大长度: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "postTransDeInputModel", "description": "postTransDeInputModel", "required": true, "schema": {"$ref": "#/definitions/PostTransDeInputModel"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ResultUtil"}}}}}, "/merchant/merchantEnquiry/{merchantNumber}": {"get": {"tags": ["Merchant"], "summary": "该API可以查询信用卡商户的详细信息。", "description": "例如某一个服装商户，想要查询自己在银行的登记信息，如商户的账户余额，商户营业执照，地址，电话等信息。", "operationId": "merchantEnquiryUsingGET", "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "颁发给第三方应用程序的最新客户授权令牌，以JWT格式访问该银行的客户数据。当开发者在调用API访问银行客户数据的时候，都需要用到该令牌。<br>最大长度: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "为前后端的每个请求唯一生成的128位随机UUID格式的消息ID。 该参数为内部参数，在调用API的时候不用修改该参数的内容。直接使用即可。<br>最大长度: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "当用户登录的时候生成的客户id。  该参数为内部参数，在调用API的时候不用修改该参数的内容。直接使用即可。 <br>最大长度: 50</br>", "required": true, "type": "string", "default": "devin"}, {"name": "merchantNumber", "in": "path", "description": "信用卡商户编号。 例如:HK0001001000009.<br>最大长度: 35</br>", "required": true, "type": "string"}], "responses": {"200": {"description": "查询成功。 （由Get方法返回）", "schema": {"$ref": "#/definitions/ResultUtil«ReMerchantReferenceModel»"}}, "201": {"description": "正常执行，请求成功。（由Post方法返回）", "schema": {"$ref": "#/definitions/ResultUtil«ReMerchantReferenceModel»"}}, "403": {"description": "令牌的作用范围不正确或违反了安全策略。操作：请检查您是否使用合法授权用户帐户的正确令牌。"}, "404": {"description": "请求的帐户不存在。操作：请确保您输入的帐号和帐户类型正确。"}, "500": {"description": "API网关或微服务出现问题。操作：检查您的网络，稍后再试。"}}}}, "/merchant/productEnquiry": {"post": {"tags": ["Merchant"], "summary": "该组API用于查询信用卡商户的商品信息。", "description": "当您在开发信用卡商户模块，做商户的产品信息展示时，就可以调用该API。 直接输入商品所属的种类，就可以查询出该种类下所有的商品。", "operationId": "productEnquiryUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "颁发给第三方应用程序的最新客户授权令牌，以JWT格式访问该银行的客户数据。当开发者在调用API访问银行客户数据的时候，都需要用到该令牌。<br>最大长度: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "为前后端的每个请求唯一生成的128位随机UUID格式的消息ID。 该参数为内部参数，在调用API的时候不用修改该参数的内容。直接使用即可。<br>最大长度: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "当用户登录的时候生成的客户id。  该参数为内部参数，在调用API的时候不用修改该参数的内容。直接使用即可。 <br>最大长度: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "searchPointProductModel", "description": "searchPointProductModel", "required": true, "schema": {"$ref": "#/definitions/SearchPointProductModel"}}], "responses": {"200": {"description": "查询成功。 （由Get方法返回）", "schema": {"$ref": "#/definitions/ResultUtil«List«ProductModel»»"}}, "201": {"description": "正常执行，请求成功。（由Post方法返回）", "schema": {"$ref": "#/definitions/ResultUtil«List«ProductModel»»"}}, "403": {"description": "令牌的作用范围不正确或违反了安全策略。操作：请检查您是否使用合法授权用户帐户的正确令牌。"}, "404": {"description": "请求的帐户不存在。操作：请确保您输入的帐号和帐户类型正确。"}, "500": {"description": "API网关或微服务出现问题。操作：检查您的网络，稍后再试。"}}}}, "/merchant/transactions": {"post": {"tags": ["Merchant"], "summary": "该API可以让信用卡商户查询在某一段时间内发生的交易详情。", "description": "通过输入的商户号码，起始和结束时间，就可以查询出结果。但您在开发商户的功能模块时可能需要用到该API。  如果客户还没有和商户发生过交易，那么可以调用/creditcard/transactionPosting来模拟交易过程，产生交易记录。", "operationId": "transactionsUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "颁发给第三方应用程序的最新客户授权令牌，以JWT格式访问该银行的客户数据。当开发者在调用API访问银行客户数据的时候，都需要用到该令牌。<br>最大长度: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "为前后端的每个请求唯一生成的128位随机UUID格式的消息ID。 该参数为内部参数，在调用API的时候不用修改该参数的内容。直接使用即可。<br>最大长度: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "当用户登录的时候生成的客户id。  该参数为内部参数，在调用API的时候不用修改该参数的内容。直接使用即可。 <br>最大长度: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "queryHistoryModel", "description": "queryHistoryModel", "required": true, "schema": {"$ref": "#/definitions/QueryHistoryModel"}}], "responses": {"200": {"description": "查询成功。 （由Get方法返回）", "schema": {"$ref": "#/definitions/ResultUtil«ReHistoryModel»"}}, "201": {"description": "正常执行，请求成功。（由Post方法返回）", "schema": {"$ref": "#/definitions/ResultUtil«ReHistoryModel»"}}, "403": {"description": "令牌的作用范围不正确或违反了安全策略。操作：请检查您是否使用合法授权用户帐户的正确令牌。"}, "404": {"description": "请求的帐户不存在。操作：请确保您输入的帐号和帐户类型正确。"}, "500": {"description": "API网关或微服务出现问题。操作：检查您的网络，稍后再试。"}}}}, "/point/redemption": {"post": {"tags": ["Rewards"], "summary": "该API用于积分兑换业务。", "description": "当客户想要用自己的积分兑换产品，比如用300积分兑换一件衣服时，就可以调用该API。在兑换商品之前，客户可能需要先查看剩余的可用积分，可以调用/point/totalPoint。用积分兑换产品之后，可以调用/point/redemptionHistory 来查询兑换记录。", "operationId": "redemptionUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "颁发给第三方应用程序的最新客户授权令牌，以JWT格式访问该银行的客户数据。当开发者在调用API访问银行客户数据的时候，都需要用到该令牌。<br>最大长度: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "为前后端的每个请求唯一生成的128位随机UUID格式的消息ID。 该参数为内部参数，在调用API的时候不用修改该参数的内容。直接使用即可。<br>最大长度: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "当用户登录的时候生成的客户id。  该参数为内部参数，在调用API的时候不用修改该参数的内容。直接使用即可。 <br>最大长度: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "pointRedemptionModel", "description": "pointRedemptionModel", "required": true, "schema": {"$ref": "#/definitions/PointRedemptionModel"}}], "responses": {"200": {"description": "查询成功。 （由Get方法返回）", "schema": {"$ref": "#/definitions/ResultUtil«TotalConsumptionModel»"}}, "201": {"description": "正常执行，请求成功。（由Post方法返回）", "schema": {"$ref": "#/definitions/ResultUtil«TotalConsumptionModel»"}}, "400": {"description": "Situation: Request has malformed, missing or non-compliant JSON body, URL parameters or header fields.Action: Please go back and refer to the method Model details to recheck the message and make sure your input is correct."}, "401": {"description": "Situation: Authorization header missing or invalid token. Action: Please make sure the token, key or relevant parameters are correct."}, "403": {"description": "Situation: 令牌的作用范围不正确或违反了安全策略。操作：请检查您是否使用合法授权用户帐户的正确令牌。"}, "404": {"description": "Situation: 请求的帐户不存在。操作：请确保您输入的帐号和帐户类型正确。"}, "405": {"description": "Situation: A request method is not supported for the requested deposit account. Action: Please make sure that you’re using a POST method to get the requested deposit account details."}, "408": {"description": "Situation: The server timed out waiting for the request. Action: Try again later."}, "500": {"description": "Situation: API网关或微服务出现问题。操作：检查您的网络，稍后再试。"}, "503": {"description": "Situation: Service version deprecation. Action: contact API platform support to fix the service issue."}}}}, "/point/redemptionHistory": {"post": {"tags": ["Rewards"], "summary": "该API用于信用卡积分兑换历史记录查询，直接输入信用卡账户号码，就可以查询出兑换结果", "description": "客户可能在兑换之前需要先查询自己的可用积分，可以调用​/point​/totalPoint", "operationId": "redemptionHistoryUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "颁发给第三方应用程序的最新客户授权令牌，以JWT格式访问该银行的客户数据。当开发者在调用API访问银行客户数据的时候，都需要用到该令牌。<br>最大长度: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "为前后端的每个请求唯一生成的128位随机UUID格式的消息ID。 该参数为内部参数，在调用API的时候不用修改该参数的内容。直接使用即可。<br>最大长度: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "当用户登录的时候生成的客户id。  该参数为内部参数，在调用API的时候不用修改该参数的内容。直接使用即可。 <br>最大长度: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "checkCreditcardPointModel", "description": "checkCreditcardPointModel", "required": true, "schema": {"$ref": "#/definitions/CheckCreditcardPointModel"}}], "responses": {"200": {"description": "查询成功。 （由Get方法返回）", "schema": {"$ref": "#/definitions/ResultUtil«List«PointRedemptionHistoryModel»»"}}, "201": {"description": "正常执行，请求成功。（由Post方法返回）", "schema": {"$ref": "#/definitions/ResultUtil«List«PointRedemptionHistoryModel»»"}}, "403": {"description": "令牌的作用范围不正确或违反了安全策略。操作：请检查您是否使用合法授权用户帐户的正确令牌。"}, "404": {"description": "请求的帐户不存在。操作：请确保您输入的帐号和帐户类型正确。"}, "500": {"description": "API网关或微服务出现问题。操作：检查您的网络，稍后再试。"}}}}, "/point/totalPoint": {"post": {"tags": ["Rewards"], "summary": "该API用于查询信用卡账户的总积分。", "description": "当客户想要查询信用卡总积分时，或者在用积分兑换产品之前时，都可以调用该API。客户每发生一笔交易时，都会产生新的积分。模拟发生信用卡交易，可以调用/creditcard/transactionPosting", "operationId": "totalPointUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "颁发给第三方应用程序的最新客户授权令牌，以JWT格式访问该银行的客户数据。当开发者在调用API访问银行客户数据的时候，都需要用到该令牌。<br>最大长度: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "为前后端的每个请求唯一生成的128位随机UUID格式的消息ID。 该参数为内部参数，在调用API的时候不用修改该参数的内容。直接使用即可。<br>最大长度: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "当用户登录的时候生成的客户id。  该参数为内部参数，在调用API的时候不用修改该参数的内容。直接使用即可。 <br>最大长度: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "checkCreditcardPointModel", "description": "checkCreditcardPointModel", "required": true, "schema": {"$ref": "#/definitions/CreditcardPointModel"}}], "responses": {"200": {"description": "查询成功。 （由Get方法返回）", "schema": {"$ref": "#/definitions/ResultUtil«TotalPointModel»"}}, "201": {"description": "正常执行，请求成功。（由Post方法返回）", "schema": {"$ref": "#/definitions/ResultUtil«TotalPointModel»"}}, "403": {"description": "令牌的作用范围不正确或违反了安全策略。操作：请检查您是否使用合法授权用户帐户的正确令牌。"}, "404": {"description": "请求的帐户不存在。操作：请确保您输入的帐号和帐户类型正确。"}, "500": {"description": "API网关或微服务出现问题。操作：检查您的网络，稍后再试。"}}}}}, "definitions": {"CheckCreditcardPointModel": {"type": "object", "required": ["creditcardnumber", "index", "items"], "properties": {"creditcardnumber": {"type": "string", "example": ****************, "description": "信用卡账户号码。<br> 最大长度:16 </br>", "allowEmptyValue": false}, "index": {"type": "number", "example": 0.0, "description": "API返回数据的开始位置标记。如：当index=0时，则表示从第1条数据开始返回结果。<br> 最大长度:4 </br>", "allowEmptyValue": false}, "items": {"type": "number", "example": 3.0, "description": "API返回的数据条数。如：当items=10. 表示返回10条结果。<br> 最大长度:4 </br>", "allowEmptyValue": false}}, "title": "CheckCreditcardPointModel"}, "CreditCardModel": {"type": "object", "required": ["creditcardnumber"], "properties": {"creditcardnumber": {"type": "string", "example": 5000010000000032, "description": "信用卡账户号码。<br> 最大长度:16 </br>", "allowEmptyValue": false}}, "title": "CreditCardModel"}, "CreditCardOpenningModel": {"type": "object", "required": ["approvedlimit", "cashadvancelimit", "creditcardtype", "expirydate", "issuancedate", "repaymentcycle", "verificationcode"], "properties": {"approvedlimit": {"type": "number", "example": 10000.0, "description": "信用卡的审批额度.<br>最大长度: 18</br>", "allowEmptyValue": false}, "cashadvancelimit": {"type": "number", "example": 5000.0, "description": "信用卡的现金额度.<br>最大长度: 18</br>", "allowEmptyValue": false}, "creditcardtype": {"type": "string", "example": "V", "description": "信用卡类型.<br>最大长度: 4</br>", "allowEmptyValue": false}, "expirydate": {"type": "number", "example": *************.0, "description": "信用卡过期日期.<br>最大长度: 20</br>", "allowEmptyValue": false}, "issuancedate": {"type": "number", "example": ************.0, "description": "信用卡发行日期.<br>最大长度: 20</br>", "allowEmptyValue": false}, "repaymentaccountnum": {"type": "string", "example": "***********************", "description": "还款账户 <br>最大长度: 34</br>", "allowEmptyValue": false}, "repaymentcycle": {"type": "string", "example": "M", "description": "还款周期 \n可能的值: M-Monthly.<br>最大长度: 5</br>", "allowEmptyValue": false}, "verificationcode": {"type": "string", "example": "001", "description": "信用卡验证码.<br>最大长度: 3</br>", "allowEmptyValue": false}}, "title": "CreditCardOpenningModel"}, "CreditcardPointModel": {"type": "object", "required": ["creditcardnumber"], "properties": {"creditcardnumber": {"type": "string", "example": ****************, "description": "信用卡账户号码。<br> 最大长度:16 </br>", "allowEmptyValue": false}}, "title": "CreditcardPointModel"}, "CustomerCurrencyAccountModel": {"type": "object", "properties": {"account": {"$ref": "#/definitions/CreditCardOpenningModel"}, "customer": {"$ref": "#/definitions/CustomerMasterModel"}}, "title": "CustomerCurrencyAccountModel"}, "CustomerMasterModel": {"type": "object", "required": ["chinesename", "customerid", "emailaddress", "firstname", "issuecountry", "lastname", "mailingaddress", "mobilephonenumber"], "properties": {"accommodation": {"type": "string", "example": "S", "description": "住房性质。R-表示住的房子是租的。<br>M-表示住的房子是有房屋贷款未还完的。<br>S-表示住的房子是完全属于自己的。<br>Q-表示住的宿舍。<br>F-表示住的房子是朋友的。<br>R-表示住的房子是其他类型的。<br><br>最大长度: 10</br>", "allowEmptyValue": false}, "chinesename": {"type": "string", "example": "王研", "description": "表示客户的中文名字。<br>最大长度: 50</br>", "allowEmptyValue": false}, "companyaddress": {"type": "string", "example": "228 Pok Fu Lam Road,Aberdeen,Hong Kong", "description": "表示银行客户服务的公司地址。<br>最大长度: 490</br>", "allowEmptyValue": false}, "companyphonenumber": {"type": "string", "example": 24827781, "description": "表示银行客户服务的公司电话号码。<br>最大长度: 34</br>", "allowEmptyValue": false}, "customerid": {"type": "string", "example": "U735535(9)", "description": "客户证件号码。 例如：如果用身份证开户了，那么该Customer ID就表示身份证号码。如果是护照开户的，那么 Customer ID就表示护照号码。<br>最大长度: 50</br>", "allowEmptyValue": false}, "dateofbirth": {"type": "number", "example": 178163911000.0, "description": "客户生日<br>最大长度: 20</br>", "allowEmptyValue": false}, "education": {"type": "string", "example": "U", "description": "银行客户的受教育程度U-大学<br>P-中学后<br>S-高中<br>J-初中<br>X-其他<br><br>最大长度: 1</br>", "allowEmptyValue": false}, "emailaddress": {"type": "string", "example": "<EMAIL>", "description": "银行客户的邮箱地址。<br>最大长度: 490</br>", "allowEmptyValue": false}, "employercompanyname": {"type": "string", "example": "Happy Realty", "description": "表示银行客户服务的公司名称。<br>最大长度: 50</br>", "allowEmptyValue": false}, "firstname": {"type": "string", "example": "Yan", "description": "表示银行客户的名字。<br>最大长度: 70</br>", "allowEmptyValue": false}, "gender": {"type": "string", "example": "M", "description": "性别。: M-男, F-女.<br>最大长度: 1</br>", "allowEmptyValue": false}, "issuecountry": {"type": "string", "example": "China", "description": "表示银行客户开立账户所用证件的签发国家。例如：客户用的香港身份证开立的账户，那么该身份证的签发国家就是中国香港(HK).<br>最大长度: 50</br>", "allowEmptyValue": false}, "lastname": {"type": "string", "example": "<PERSON>", "description": "表示银行客户的姓。<br>最大长度: 70</br>", "allowEmptyValue": false}, "mailingaddress": {"type": "string", "example": "551 Austin Road,Tsim Sha Tsui,Kowloon", "description": " 收件地址<br>最大长度: 490</br>", "allowEmptyValue": false}, "maritalstatus": {"type": "string", "example": "S", "description": "婚姻状态。S-表示单身<br>M-表示已婚<br>D-表示离婚<br>W-表示丧偶<br><br>最大长度: 1</br>", "allowEmptyValue": false}, "mobilephonenumber": {"type": "string", "example": 64657884, "description": "手机号码<br>最大长度: 11</br>", "allowEmptyValue": false}, "monthlysalary": {"type": "number", "example": 19000.0, "description": "月薪。<br>最大长度: 10</br>", "allowEmptyValue": false}, "nationality": {"type": "string", "example": "China", "description": "客户国籍。例如：一个英国人来该银行开户了，那么这个英国人的国籍就是UK，美国人的国籍就是US，以此类推。<br>最大长度: 50</br>", "allowEmptyValue": false}, "occupation": {"type": "string", "example": "Software", "description": "银行客户工作所在行业。<br>最大长度: 50</br>", "allowEmptyValue": false}, "permanentresidencestatus": {"type": "string", "example": "Y", "description": "表示该银行客户是否是一个永久居民。Y表示是永久居民，N表示不是永久居民。:Y-Yes, N-No.", "allowEmptyValue": false}, "position": {"type": "string", "example": "Senior Manager", "description": "表示该银行客户在公司的职位。如经理，咨询顾问，工程师等等。<br>最大长度: 50</br>", "allowEmptyValue": false}, "residencephonenumber": {"type": "string", "example": 34828869, "description": "居住地电话号码<br>最大长度: 34</br>", "allowEmptyValue": false}, "residentialaddress": {"type": "string", "example": "779 Yi Chun Street, Sai Kung, New Territory", "description": "表示该银行客户的居住地址。<br>最大长度: 490</br>", "allowEmptyValue": false}, "wechatid": {"type": "string", "example": "W3456754", "description": "表示该银行客户的微信号码。<br>最大长度: 50</br>", "allowEmptyValue": false}, "yearsofresidence": {"type": "integer", "format": "int32", "example": 3, "description": "表示该银行客户在当前国家居住的年限。<br>最大长度: 10</br>", "allowEmptyValue": false}, "yearsofservices": {"type": "integer", "format": "int32", "example": 2, "description": "表示该银行客户在当前公司工作年限。<br>最大长度: 50</br>", "allowEmptyValue": false}}, "title": "CustomerMasterModel"}, "IncreaseLimitsChangeInputModel": {"type": "object", "required": ["changeAmount", "creditcardnumber"], "properties": {"changeAmount": {"type": "number", "example": 5000.0, "description": "增加额度后的新的额度。<br> 最大长度:18 </br>", "allowEmptyValue": false}, "creditcardnumber": {"type": "string", "example": ****************, "description": "信用卡账户号码。<br> 最大长度:16 </br>", "allowEmptyValue": false}}, "title": "IncreaseLimitsChangeInputModel"}, "LimitsChangeInputModel": {"type": "object", "required": ["changeAmount", "creditcardnumber"], "properties": {"changeAmount": {"type": "number", "example": 5000.0, "description": "降低额度后的新的额度。<br> 最大长度:18 </br>", "allowEmptyValue": false}, "creditcardnumber": {"type": "string", "example": ****************, "description": "信用卡账户号码。<br> 最大长度:16 </br>", "allowEmptyValue": false}}, "title": "LimitsChangeInputModel"}, "LimitsRetrieveModel": {"type": "object", "properties": {"approvedLimit": {"type": "string", "example": 10000, "description": "The approved limit of a credit card.", "allowEmptyValue": false}, "availableLimit": {"type": "string", "example": 10000, "description": "The available limit of a credit card.", "allowEmptyValue": false}, "cashAdvanceLimit": {"type": "string", "example": 5000, "description": "The cashadvance limit of a credit card.", "allowEmptyValue": false}, "ccyCode": {"type": "string", "example": "HKD", "description": "The currency code of a credit card.", "allowEmptyValue": false}, "usedLimit": {"type": "string", "example": 5000, "description": "The used limit of a credit card.", "allowEmptyValue": false}}, "title": "LimitsRetrieveModel"}, "PaymentDetailsModel": {"type": "object", "properties": {"minimumpayment": {"type": "number", "example": 1000.0, "description": "Minimum repayment amount.<br> 最大长度:18</br>", "allowEmptyValue": false}, "repaymentamount": {"type": "number", "example": 1000.0, "description": "Repayment amount.<br> 最大长度:18</br>", "allowEmptyValue": false}, "repaymentduedate": {"type": "number", "example": 1561540143000.0, "description": "The due date of repayment.<br>最大长度: 20</br>", "allowEmptyValue": false}, "statementdate": {"type": "number", "example": 1561540143000.0, "description": "The statement date of creditcard.<br>最大长度: 20</br>", "allowEmptyValue": false}}, "title": "PaymentDetailsModel"}, "PointRedemptionHistoryModel": {"type": "object", "properties": {"amount": {"type": "number", "example": 1000.0, "description": "Quantity of exchangeable products.<br> 最大长度:18</br>", "allowEmptyValue": false}, "creditcardnumber": {"type": "string", "example": ****************, "description": "Customer credit card number. <br> 最大长度:16 </br>", "allowEmptyValue": false}, "id": {"type": "integer", "format": "int32", "example": 1, "description": "Unique id. <br> 最大长度: 20 </br>", "allowEmptyValue": false}, "merchantname": {"type": "string", "example": "Man Kiu College", "description": "The name of merchant.<br> 最大长度:140 </br>", "allowEmptyValue": false}, "points": {"type": "number", "example": 1000.0, "description": "Generate or charge points.<br> 最大长度:18</br>", "allowEmptyValue": false}, "productcode": {"type": "string", "example": "00001", "description": "product code <br> 最大长度:10 </br>", "allowEmptyValue": false}, "productname": {"type": "string", "example": "Lovers' suits", "description": "The product name.<br> 最大长度:140 </br>", "allowEmptyValue": false}, "transactionoption": {"type": "string", "example": "0001", "description": "Type of points transaction.<br> 最大长度:5 </br>", "allowEmptyValue": false}, "transactiontime": {"type": "number", "example": 1561540143000.0, "description": "The transaction time of Credit Card.<br> 最大长度:20 </br>", "allowEmptyValue": false}}, "title": "PointRedemptionHistoryModel"}, "PointRedemptionModel": {"type": "object", "required": ["amount", "creditcardnumber", "productcode"], "properties": {"amount": {"type": "number", "example": 2.0, "description": "信用卡消费中，产品价格。<br> 最大长度:13 </br>", "allowEmptyValue": false}, "creditcardnumber": {"type": "string", "example": ****************, "description": "信用卡账户号码。<br> 最大长度:16 </br>", "allowEmptyValue": false}, "productcode": {"type": "string", "example": "00001", "description": "信用卡消费中，产品编号。<br> 最大长度:10 </br>", "allowEmptyValue": false}}, "title": "PointRedemptionModel"}, "PostTransDeInputModel": {"type": "object", "required": ["creditcardnumber", "merchantnumber", "transactionamount", "transactionccy", "transactiontime"], "properties": {"creditcardnumber": {"type": "string", "example": ****************, "description": "信用卡账户号码。<br> 最大长度:16 </br>", "allowEmptyValue": false}, "merchantnumber": {"type": "string", "example": "HK0001001000009", "description": "信用卡商户编号。<br> 最大长度:35 </br>", "allowEmptyValue": false}, "transactionamount": {"type": "number", "example": 100.0, "description": "交易金额。<br> 最大长度:18 </br>", "allowEmptyValue": false}, "transactionccy": {"type": "string", "example": "USD", "description": "交易货币类型。支持的类型有HKD和USD。<br> 最大长度:3 </br>", "allowEmptyValue": false}, "transactiontime": {"type": "number", "example": 1561540143000.0, "description": "交易时间。<br> 最大长度:20 </br>", "allowEmptyValue": false}}, "title": "PostTransDeInputModel"}, "ProductModel": {"type": "object", "properties": {"category": {"type": "string", "example": "Clothing", "description": "Credit card merchant category. <br>Possilble Values : Clothing, Dining, Beauty, Jewelry, Education</br> <br> 最大长度:50 </br>", "allowEmptyValue": false}, "merchantname": {"type": "string", "example": "Man Kiu College", "description": "The name of merchant.<br> 最大长度:140 </br>", "allowEmptyValue": false}, "merchantnumber": {"type": "string", "example": "HK0001001000009", "description": "The number of merchant<br>最大长度: 50</br>", "allowEmptyValue": false}, "point": {"type": "number", "example": 1000.0, "description": "Redeemable points.<br> 最大长度:18 </br>", "allowEmptyValue": false}, "productcode": {"type": "string", "example": "00001", "description": "product code <br> 最大长度:10 </br>", "allowEmptyValue": false}, "productinventory": {"type": "number", "example": 100.0, "description": "The inventory of product.<br> 最大长度:18 </br>", "allowEmptyValue": false}, "productname": {"type": "string", "example": "Lovers' suits", "description": "product name.<br> 最大长度:140 </br>", "allowEmptyValue": false}}, "title": "ProductModel"}, "QueryHistoryModel": {"type": "object", "required": ["endDate", "index", "items", "merchantNumber", "startDate"], "properties": {"endDate": {"type": "number", "example": 1562774399000.0, "description": "交易结束时间。<br> 最大长度:20 </br>", "allowEmptyValue": false}, "index": {"type": "number", "example": 0.0, "description": "API返回数据的开始位置标记。如：当index=0时，则表示从第1条数据开始返回结果。<br> 最大长度:4 </br>", "allowEmptyValue": false}, "items": {"type": "number", "example": 3.0, "description": "API返回的数据条数。如：当items=10. 表示返回10条结果。<br> 最大长度:4 </br>", "allowEmptyValue": false}, "merchantNumber": {"type": "string", "example": "HK0001001000009", "description": "信用卡商户编号。<br>最大长度: 50</br>", "allowEmptyValue": false}, "startDate": {"type": "number", "example": 1556269743000.0, "description": "交易开始时间。<br> 最大长度:20 </br>", "allowEmptyValue": false}}, "title": "QueryHistoryModel"}, "ReHistoryModel": {"type": "object", "properties": {"merchant": {"$ref": "#/definitions/ReMerchantReferenceModel"}, "trandetail": {"type": "array", "items": {"$ref": "#/definitions/ReTranDetailModel"}}}, "title": "ReHistoryModel"}, "ReMerchantReferenceModel": {"type": "object", "properties": {"businesslicenseid": {"type": "string", "example": "5e645df8157642c9bcb1811a453bf5a8", "description": "The business license id of merchant.<br>最大长度: 50</br>", "allowEmptyValue": false}, "displayname": {"type": "string", "example": "Toddle Kids Ltd.", "description": "The displayname of merchant.<br>最大长度: 140</br>", "allowEmptyValue": false}, "lastupdateddate": {"type": "number", "example": 1420073798000.0, "description": "The lastupdated date of merchant data<br>最大长度: 20</br>", "allowEmptyValue": false}, "merchantaddress": {"type": "string", "example": "Flat 2, Front Block, 12/F.", "description": "The address of merchant.<br>最大长度: 490</br>", "allowEmptyValue": false}, "merchantname": {"type": "string", "example": "Man Kiu College", "description": "The name of merchant.<br>最大长度: 140</br>", "allowEmptyValue": false}, "merchantnumber": {"type": "string", "example": "HK0001001000009", "description": "The number of merchant.<br>最大长度: 35</br>", "allowEmptyValue": false}, "outstandingbalance": {"type": "number", "example": 0.0, "description": "The outstanding balance of merchant.<br>最大长度: 18</br>", "allowEmptyValue": false}, "paymentterm": {"type": "string", "example": "W", "description": "The payment period.<br>最大长度: 5</br>", "allowEmptyValue": false}, "phone": {"type": "string", "example": "2771 0870", "description": "Merchant’s phone number.<br>最大长度: 34</br>", "allowEmptyValue": false}}, "title": "ReMerchantReferenceModel"}, "ReTranDetailModel": {"type": "object", "properties": {"bookingamount": {"type": "number", "example": 100.0, "description": "Customer credit card booking amount.<br> 最大长度:18 </br>", "allowEmptyValue": false}, "bookingccy": {"type": "string", "example": "USD", "description": "Booking currency type.<br> 最大长度:3 </br>", "allowEmptyValue": false}, "creditcardnumber": {"type": "string", "example": ****************, "description": "Customer credit card number. <br> 最大长度: 16 </br>", "allowEmptyValue": false}, "creditcardtype": {"type": "string", "example": "V", "description": "The Type of Credit Card.<br>最大长度: 4</br>", "allowEmptyValue": false}, "dealnumber": {"type": "string", "example": 20150128000003406, "description": "The dealnumber of Credit Card Transaction log.<br>最大长度: 20</br>", "allowEmptyValue": false}, "transactionamount": {"type": "number", "example": 100.0, "description": "Customer credit card transaction amount.<br> 最大长度:18 </br>", "allowEmptyValue": false}, "transactionccy": {"type": "string", "example": "USD", "description": "Transaction currency type.<br> 最大长度:3 </br>", "allowEmptyValue": false}, "transactiontime": {"type": "number", "example": 1561540143000.0, "description": "The transaction time of Credit Card.<br> 最大长度:20 </br>", "allowEmptyValue": false}}, "title": "ReTranDetailModel"}, "ReTransactionDetailsModel": {"type": "object", "required": ["creditcardnumber", "index", "items", "transFromDate", "transToDate"], "properties": {"creditcardnumber": {"type": "string", "example": ****************, "description": "信用卡账户号码。<br> 最大长度:16 </br>", "allowEmptyValue": false}, "index": {"type": "number", "example": 0.0, "description": "API返回数据的开始位置标记。如：当index=0时，则表示从第1条数据开始返回结果。<br> 最大长度:4 </br>", "allowEmptyValue": false}, "items": {"type": "number", "example": 3.0, "description": "API返回的数据条数。如：当items=10. 表示返回10条结果。<br> 最大长度:4 </br>", "allowEmptyValue": false}, "transFromDate": {"type": "number", "example": *************.0, "description": "交易记录开始时间。<br> 最大长度:20 </br>", "allowEmptyValue": false}, "transToDate": {"type": "number", "example": *************.0, "description": "交易记录结束时间。<br> 最大长度:20 </br>", "allowEmptyValue": false}}, "title": "ReTransactionDetailsModel"}, "RepaymentModel": {"type": "object", "required": ["creditcardnumber", "debitaccountnumber", "repaymentAmount"], "properties": {"creditcardnumber": {"type": "string", "example": ****************, "description": "信用卡账户号码。<br> 最大长度:16 </br>", "allowEmptyValue": false}, "debitaccountnumber": {"type": "string", "example": "***********************", "description": "信用卡绑定的用于还款的储蓄账户号码（如001结尾和002结尾的账户）。<br> 最大长度:23 </br>", "allowEmptyValue": false}, "repaymentAmount": {"type": "number", "example": 1000.0, "description": "还款金额。<br> 最大长度:18</br>", "allowEmptyValue": false}}, "title": "RepaymentModel"}, "ResultUtil": {"type": "object", "properties": {"code": {"type": "string"}, "data": {"type": "object"}, "msg": {"type": "string"}}, "title": "ResultUtil"}, "ResultUtil«CreditCardModel»": {"type": "object", "properties": {"code": {"type": "string"}, "data": {"$ref": "#/definitions/CreditCardModel"}, "msg": {"type": "string"}}, "title": "ResultUtil«CreditCardModel»"}, "ResultUtil«LimitsRetrieveModel»": {"type": "object", "properties": {"code": {"type": "string"}, "data": {"$ref": "#/definitions/LimitsRetrieveModel"}, "msg": {"type": "string"}}, "title": "ResultUtil«LimitsRetrieveModel»"}, "ResultUtil«List«PointRedemptionHistoryModel»»": {"type": "object", "properties": {"code": {"type": "string"}, "data": {"type": "array", "items": {"$ref": "#/definitions/PointRedemptionHistoryModel"}}, "msg": {"type": "string"}}, "title": "ResultUtil«List«PointRedemptionHistoryModel»»"}, "ResultUtil«List«ProductModel»»": {"type": "object", "properties": {"code": {"type": "string"}, "data": {"type": "array", "items": {"$ref": "#/definitions/ProductModel"}}, "msg": {"type": "string"}}, "title": "ResultUtil«List«ProductModel»»"}, "ResultUtil«List«TransactionDetailsModel»»": {"type": "object", "properties": {"code": {"type": "string"}, "data": {"type": "array", "items": {"$ref": "#/definitions/TransactionDetailsModel"}}, "msg": {"type": "string"}}, "title": "ResultUtil«List«TransactionDetailsModel»»"}, "ResultUtil«PaymentDetailsModel»": {"type": "object", "properties": {"code": {"type": "string"}, "data": {"$ref": "#/definitions/PaymentDetailsModel"}, "msg": {"type": "string"}}, "title": "ResultUtil«PaymentDetailsModel»"}, "ResultUtil«ReHistoryModel»": {"type": "object", "properties": {"code": {"type": "string"}, "data": {"$ref": "#/definitions/ReHistoryModel"}, "msg": {"type": "string"}}, "title": "ResultUtil«ReHistoryModel»"}, "ResultUtil«ReMerchantReferenceModel»": {"type": "object", "properties": {"code": {"type": "string"}, "data": {"$ref": "#/definitions/ReMerchantReferenceModel"}, "msg": {"type": "string"}}, "title": "ResultUtil«ReMerchantReferenceModel»"}, "ResultUtil«TotalConsumptionModel»": {"type": "object", "properties": {"code": {"type": "string"}, "data": {"$ref": "#/definitions/TotalConsumptionModel"}, "msg": {"type": "string"}}, "title": "ResultUtil«TotalConsumptionModel»"}, "ResultUtil«TotalPointModel»": {"type": "object", "properties": {"code": {"type": "string"}, "data": {"$ref": "#/definitions/TotalPointModel"}, "msg": {"type": "string"}}, "title": "ResultUtil«TotalPointModel»"}, "SearchPointProductModel": {"type": "object", "required": ["category"], "properties": {"category": {"type": "string", "example": "Clothing", "description": "信用卡消费中，产品的种类。</br> <br> 最大长度:35 </br>", "allowEmptyValue": false}}, "title": "SearchPointProductModel"}, "TotalConsumptionModel": {"type": "object", "properties": {"totalConsumption": {"type": "string", "example": 200, "description": "The total consumption of Credit Card.<br>最大长度: 50</br>", "allowEmptyValue": false}}, "title": "TotalConsumptionModel"}, "TotalPointModel": {"type": "object", "required": ["totalPoint"], "properties": {"totalPoint": {"type": "number", "example": 200.0, "description": "The total point of Credit Card.<br>最大长度: 20</br>", "allowEmptyValue": false}}, "title": "TotalPointModel"}, "TransactionDetailsModel": {"type": "object", "properties": {"bookingamount": {"type": "number", "example": 100.0, "description": "Customer credit card booking amount.<br> 最大长度:18 </br>", "allowEmptyValue": false}, "bookingccy": {"type": "string", "example": "USD", "description": "Booking currency code.<br> 最大长度:3 </br>", "allowEmptyValue": false}, "dealnumber": {"type": "string", "example": 20150128000003406, "description": "The dealnumber of Credit Card Transaction log.<br> 最大长度:20 </br>", "allowEmptyValue": false}, "merchantname": {"type": "string", "example": "Man Kiu College", "description": "The name of merchant.<br> 最大长度:140 </br>", "allowEmptyValue": false}, "merchantnumber": {"type": "string", "example": "HK0001001000009", "description": "The number of merchant<br>最大长度: 35</br>", "allowEmptyValue": false}, "transactionamount": {"type": "number", "example": 100.0, "description": "Customer credit card transaction amount.<br> 最大长度:18 </br>", "allowEmptyValue": false}, "transactionccy": {"type": "string", "example": "USD", "description": "Transaction currency code.<br> 最大长度:3 </br>", "allowEmptyValue": false}, "transactiontime": {"type": "number", "example": 1561540143000.0, "description": "The transaction time of Credit Card.<br> 最大长度:20 </br>", "allowEmptyValue": false}}, "title": "TransactionDetailsModel"}}}