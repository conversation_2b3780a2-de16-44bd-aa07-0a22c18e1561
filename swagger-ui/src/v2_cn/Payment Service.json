{"swagger": "2.0", "info": {"description": "该服务模块主要包含了所有支付相关的业务API。包含收款人列表创建，收款人删除，支付等功能。", "version": "1.0", "title": "支付业务", "contact": {"name": "<EMAIL>"}}, "schemes": ["https"], "host": "simnectz.hsu.edu.hk/lbsgateway/payment-experience", "basePath": "/", "tags": [{"name": "Payee Management", "description": "定义收款人列表。"}, {"name": "Transaction", "description": "支付交易。"}], "paths": {"/payment/billPayeeDelete": {"post": {"tags": ["Payee Management"], "summary": "该API用于删除客户收款列表中的收款商户。", "description": "例如，在客户的收款人列表中，有某个燃气公司账户信息，某个水力公司信息和某个 教育机构信息，当客户想要删除燃气公司账户信息时，就可以调用该API来完成。如果客户还没有创建自己的收款商户列表，可能需要先调用/payment/billPayeeSetup来创建好收款人列表。", "operationId": "billPayeeDeleteUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "颁发给第三方应用程序的最新客户授权令牌，以JWT格式访问该银行的客户数据。当开发者在调用API访问银行客户数据的时候，都需要用到该令牌。<br>最大长度: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "为前后端的每个请求唯一生成的128位随机UUID格式的消息ID。 该参数为内部参数，在调用API的时候不用修改该参数的内容。直接使用即可。<br>最大长度: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "当用户登录的时候生成的客户id。  该参数为内部参数，在调用API的时候不用修改该参数的内容。直接使用即可。<br>最大长度: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "deleteBillPayeeModel", "description": "deleteBillPayeeModel", "required": true, "schema": {"$ref": "#/definitions/DeleteBillPayeeModel"}}], "responses": {"200": {"description": "查询成功。 （由Get方法返回）", "schema": {"$ref": "#/definitions/ResultUtil"}}, "201": {"description": "正常执行，请求成功。（由Post方法返回）", "schema": {"$ref": "#/definitions/ResultUtil"}}, "403": {"description": "令牌的作用范围不正确或违反了安全策略。操作：请检查您是否使用合法授权用户帐户的正确令牌。"}, "404": {"description": "请求的帐户不存在。操作：请确保您输入的帐号和帐户类型正确。"}, "500": {"description": "API网关或微服务出现问题。操作：检查您的网络，稍后再试。"}}}}, "/payment/billPayeeSetup": {"post": {"tags": ["Payee Management"], "summary": "该API用于创建客户收款人列表。 ", "description": "例如，想要添加一个水力公司账户号码，以后开始交水费用，就可以在这里找到合适的API来完成。当客户以后不再交水费时，可以删除该收款人。可以调用/payment/billPayeeDelete来完成删除的操作。", "operationId": "billPayeeSetupUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "颁发给第三方应用程序的最新客户授权令牌，以JWT格式访问该银行的客户数据。当开发者在调用API访问银行客户数据的时候，都需要用到该令牌。<br>最大长度: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "为前后端的每个请求唯一生成的128位随机UUID格式的消息ID。 该参数为内部参数，在调用API的时候不用修改该参数的内容。直接使用即可。<br>最大长度: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "当用户登录的时候生成的客户id。  该参数为内部参数，在调用API的时候不用修改该参数的内容。直接使用即可。<br>最大长度: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "setBillPayeeModel", "description": "setBillPayeeModel", "required": true, "schema": {"$ref": "#/definitions/SetBillPayeeModel"}}], "responses": {"200": {"description": "查询成功。 （由Get方法返回）", "schema": {"$ref": "#/definitions/ResultUtil"}}, "201": {"description": "正常执行，请求成功。（由Post方法返回）", "schema": {"$ref": "#/definitions/ResultUtil"}}, "403": {"description": "令牌的作用范围不正确或违反了安全策略。操作：请检查您是否使用合法授权用户帐户的正确令牌。"}, "404": {"description": "请求的帐户不存在。操作：请确保您输入的帐号和帐户类型正确。"}, "500": {"description": "API网关或微服务出现问题。操作：检查您的网络，稍后再试。"}}}}, "/payment/customerPayeeRetrieval": {"get": {"tags": ["Payee Management"], "summary": "该API用于获取客户的收款人列表信息。", "description": "例如，某个客户经常要交水，电，煤气费用，因此他将自己的水，电，煤气的商户账户都添加到了自己的收款人列表中。想要查看这些收款人列表，就可以调用该API来完成。如果想要再添加新的收款人，可以调用/payment/billPayeeSetup API。如果想要删除收款人列表中的某个收款人时，可以调用/payment/billPayeeDelete", "operationId": "getRetrieveCustomerPayeeListUsingGET", "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "颁发给第三方应用程序的最新客户授权令牌，以JWT格式访问该银行的客户数据。当开发者在调用API访问银行客户数据的时候，都需要用到该令牌。<br>最大长度: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "为前后端的每个请求唯一生成的128位随机UUID格式的消息ID。 该参数为内部参数，在调用API的时候不用修改该参数的内容。直接使用即可。<br>最大长度: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "当用户登录的时候生成的客户id。  该参数为内部参数，在调用API的时候不用修改该参数的内容。直接使用即可。<br>最大长度: 50</br>", "required": true, "type": "string", "default": "devin"}], "responses": {"200": {"description": "查询成功。 （由Get方法返回）", "schema": {"$ref": "#/definitions/ResultUtil«List«CustomerPayeeModel»»"}}, "201": {"description": "正常执行，请求成功。（由Post方法返回）", "schema": {"$ref": "#/definitions/ResultUtil«List«CustomerPayeeModel»»"}}, "403": {"description": "令牌的作用范围不正确或违反了安全策略。操作：请检查您是否使用合法授权用户帐户的正确令牌。"}, "404": {"description": "请求的帐户不存在。操作：请确保您输入的帐号和帐户类型正确。"}, "500": {"description": "API网关或微服务出现问题。操作：检查您的网络，稍后再试。"}}}}, "/payment/payeeCategoryList": {"get": {"tags": ["Payee Management"], "summary": "该API用于获取系统中所有的收费商户列表", "description": "例如交水电费业务中，对应的水，电，煤气公司信息。", "operationId": "retrievalPayeeCategoryListUsingGET", "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "颁发给第三方应用程序的最新客户授权令牌，以JWT格式访问该银行的客户数据。当开发者在调用API访问银行客户数据的时候，都需要用到该令牌。<br>最大长度: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "为前后端的每个请求唯一生成的128位随机UUID格式的消息ID。 该参数为内部参数，在调用API的时候不用修改该参数的内容。直接使用即可。<br>最大长度: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "当用户登录的时候生成的客户id。  该参数为内部参数，在调用API的时候不用修改该参数的内容。直接使用即可。<br>最大长度: 50</br>", "required": true, "type": "string", "default": "devin"}], "responses": {"200": {"description": "查询成功。 （由Get方法返回）", "schema": {"$ref": "#/definitions/ResultUtil«List«PayeeCategoryInformationModel»»"}}, "201": {"description": "正常执行，请求成功。（由Post方法返回）", "schema": {"$ref": "#/definitions/ResultUtil«List«PayeeCategoryInformationModel»»"}}, "403": {"description": "令牌的作用范围不正确或违反了安全策略。操作：请检查您是否使用合法授权用户帐户的正确令牌。"}, "404": {"description": "The requested deposit account does not exist.Action: Please make sure the account number and account type you’re inputting are correct."}, "500": {"description": "API网关或微服务出现问题。操作：检查您的网络，稍后再试。"}}}}, "/payment/payeeInfoListRetrieval": {"post": {"tags": ["Payee Management"], "summary": "该API用于支付业务中根据商户种类的编号获取该商户种类下面包含的商户信息。", "description": "例如根据商户类型查出该种类中包含哪些商户。（编号为001的商户种类中包含了水，电，煤气公司）。", "operationId": "retrievalPayeeInfoListUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "颁发给第三方应用程序的最新客户授权令牌，以JWT格式访问该银行的客户数据。当开发者在调用API访问银行客户数据的时候，都需要用到该令牌。<br>最大长度: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "为前后端的每个请求唯一生成的128位随机UUID格式的消息ID。 该参数为内部参数，在调用API的时候不用修改该参数的内容。直接使用即可。<br>最大长度: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "当用户登录的时候生成的客户id。  该参数为内部参数，在调用API的时候不用修改该参数的内容。直接使用即可。<br>最大长度: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "queryPayeeInfoModel", "description": "queryPayeeInfoModel", "required": true, "schema": {"$ref": "#/definitions/QueryPayeeInfoModel"}}], "responses": {"200": {"description": "查询成功。 （由Get方法返回）", "schema": {"$ref": "#/definitions/ResultUtil«List«PayeeInformationModel»»"}}, "201": {"description": "正常执行，请求成功。（由Post方法返回）", "schema": {"$ref": "#/definitions/ResultUtil«List«PayeeInformationModel»»"}}, "403": {"description": "令牌的作用范围不正确或违反了安全策略。操作：请检查您是否使用合法授权用户帐户的正确令牌。"}, "404": {"description": "请求的帐户不存在。操作：请确保您输入的帐号和帐户类型正确。"}, "500": {"description": "API网关或微服务出现问题。操作：检查您的网络，稍后再试。"}}}}, "/payment/paymentTransaction": {"post": {"tags": ["Transaction"], "summary": "该API用于支付功能。", "description": "当客户交水电费的时候，最后选择好了收款人账户，付款的时候，就可以调用该API。该API支持客户用001结尾的储蓄账户，002结尾的活期账户和信用卡账户做支付。", "operationId": "paymentTransactionUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "颁发给第三方应用程序的最新客户授权令牌，以JWT格式访问该银行的客户数据。当开发者在调用API访问银行客户数据的时候，都需要用到该令牌。<br>最大长度: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "为前后端的每个请求唯一生成的128位随机UUID格式的消息ID。 该参数为内部参数，在调用API的时候不用修改该参数的内容。直接使用即可。<br>最大长度: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "当用户登录的时候生成的客户id。  该参数为内部参数，在调用API的时候不用修改该参数的内容。直接使用即可。<br>最大长度: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "paymentModel", "description": "paymentModel", "required": true, "schema": {"$ref": "#/definitions/PaymentModel"}}], "responses": {"200": {"description": "查询成功。 （由Get方法返回）", "schema": {"$ref": "#/definitions/ResultUtil«PaymentResponseModel»"}}, "201": {"description": "正常执行，请求成功。（由Post方法返回）", "schema": {"$ref": "#/definitions/ResultUtil«PaymentResponseModel»"}}, "403": {"description": "令牌的作用范围不正确或违反了安全策略。操作：请检查您是否使用合法授权用户帐户的正确令牌。"}, "404": {"description": "请求的帐户不存在。操作：请确保您输入的帐号和帐户类型正确。"}, "500": {"description": "API网关或微服务出现问题。操作：检查您的网络，稍后再试。"}}}}}, "definitions": {"CustomerPayeeModel": {"type": "object", "properties": {"createdate": {"type": "number", "example": 1589944839000.0, "description": "Create date.<br>最大长度: 20</br>", "allowEmptyValue": false}, "customernumber": {"type": "string", "example": "001000000001", "description": "The customer number.<br>最大长度: 25</br>", "allowEmptyValue": false}, "id": {"type": "integer", "format": "int32", "example": 1, "description": "The unique id.<br>最大长度: 20</br>", "allowEmptyValue": false}, "lastupdatedate": {"type": "number", "example": 1589944839000.0, "description": "Last updated date.<br>最大长度: 20</br>", "allowEmptyValue": false}, "payeecategoryid": {"type": "string", "example": "001", "description": "A unique ID to identify a payee category, such as a school, a water company and so on.<br><br>最大长度: 50</br>", "allowEmptyValue": false}, "payeeid": {"type": "string", "example": "G123456", "description": "A unique ID to identify a payee in a dedicated payee category.<br><br>最大长度: 50</br>", "allowEmptyValue": false}, "payeenumber": {"type": "string", "example": "00000001", "description": "The customer has a unique code in the corresponding Payee.<br>最大长度: 50</br>", "allowEmptyValue": false}, "status": {"type": "string", "example": "EFCT", "description": "Status, like EFCT(effective) or INEF(ineffective).<br>最大长度: 4</br>", "allowEmptyValue": false}}, "title": "CustomerPayeeModel"}, "DeleteBillPayeeModel": {"type": "object", "required": ["payeeCategoryID", "payeeID", "payeeNumber"], "properties": {"payeeCategoryID": {"type": "string", "example": "001", "description": "收款方所在种类的编号。例如，交水费的话，属于生活缴费，那么生活缴费就是一种类型，给教育机构缴费，那么教育也是一个种类。我们为这一个种类制定了一个编号。<br><br>最大长度: 50</br>", "allowEmptyValue": false}, "payeeID": {"type": "string", "example": "G123456", "description": "收款方编号。例如，交水费的话，是给水利公司交。 这个按照服务大类分的话，就属于生活缴费，那么这个水利公司就会有一个id在系统中唯一标志在生活缴费这个大类下面的这一家水利公司。<br><br>最大长度: 50</br>", "allowEmptyValue": false}, "payeeNumber": {"type": "string", "example": 1, "description": "客户在收款方机构的编号。例如交水费的话，那么客户在水利公司的账户编号用于唯一标志该客户在水利公司的账户。<br><br>最大长度: 50</br>", "allowEmptyValue": false}}, "title": "DeleteBillPayeeModel"}, "PayeeCategoryInformationModel": {"type": "object", "properties": {"createdate": {"type": "number", "example": 1584614505389.0, "description": "Creation time.<br>最大长度: 20</br>", "allowEmptyValue": false}, "id": {"type": "integer", "format": "int32", "example": 174181, "description": "The unique id.<br>最大长度: 20</br>", "allowEmptyValue": false}, "lastupdatedate": {"type": "number", "example": 1584614505389.0, "description": "Last Updated Time.<br>最大长度: 20</br>", "allowEmptyValue": false}, "payeecategory": {"type": "string", "example": "Government or statutory organisation", "description": "PayeeCategory name.<br>最大长度: 70</br>", "allowEmptyValue": false}, "payeecategoryid": {"type": "string", "example": "001", "description": "A unique ID to identify a payee category, such as a school, a water company and so on.<br>最大长度: 50</br>", "allowEmptyValue": false}}, "title": "PayeeCategoryInformationModel"}, "PayeeInformationModel": {"type": "object", "properties": {"createdate": {"type": "number", "example": *************.0, "description": "Create date.<br>最大长度: 20</br>", "allowEmptyValue": false}, "id": {"type": "integer", "format": "int32", "example": 174181, "description": "The unique id.<br>最大长度: 20</br>", "allowEmptyValue": false}, "lastupdatedate": {"type": "number", "example": *************.0, "description": "Lastest update date.<br>最大长度: 20</br>", "allowEmptyValue": false}, "payeeaccountnumber": {"type": "string", "example": "***********************", "description": "Payee account number.<br>最大长度: 34</br>", "allowEmptyValue": false}, "payeeaddressformat": {"type": "string", "example": "F", "description": "Payee address format.Address Format:</br>S - Structure format</br>F - Free format.<br>最大长度: 1</br>", "allowEmptyValue": false}, "payeeaddressline1": {"type": "string", "example": "Central District", "description": "Payee address line 1.<br>最大长度: 70</br>", "allowEmptyValue": false}, "payeeaddressline2": {"type": "string", "example": "Citibank Plaza, 3 Garden Road, Central District", "description": "Payee address line 2.<br>最大长度: 70</br>", "allowEmptyValue": false}, "payeeaddressline3": {"type": "string", "example": "<PERSON><PERSON>, Kwun Tong", "description": "Payee address line 3.<br>最大长度: 70</br>", "allowEmptyValue": false}, "payeeaddressline4": {"type": "string", "example": "Central District", "description": "Payee address line 4.<br>最大长度: 70</br>", "allowEmptyValue": false}, "payeeaddressline5": {"type": "string", "example": "<PERSON><PERSON>, Kwun Tong", "description": "Payee address line 5.<br>最大长度: 70</br>", "allowEmptyValue": false}, "payeeaddressline6": {"type": "string", "example": "<PERSON><PERSON> Tong Ind Bldg, Yau Tong", "description": "Payee address line 6.<br>最大长度: 70</br>", "allowEmptyValue": false}, "payeeaddressline7": {"type": "string", "example": "Central District", "description": "Payee address line 7.<br>最大长度: 70</br>", "allowEmptyValue": false}, "payeebuildingname": {"type": "string", "example": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "description": "Name of Payee Building.<br>最大长度: 35</br>", "allowEmptyValue": false}, "payeebuildingnumber": {"type": "string", "example": "D01", "description": "Payee building no.<br>最大长度: 16</br>", "allowEmptyValue": false}, "payeecategoryid": {"type": "string", "example": "001", "description": "A unique ID to identify a payee category, such as a school, a water company and so on.<br><br>最大长度: 50</br>", "allowEmptyValue": false}, "payeecountry": {"type": "string", "example": "HK", "description": "Payee Country Area.<br>最大长度: 2</br>", "allowEmptyValue": false}, "payeecountrysubdivision": {"type": "string", "example": "Kowloon", "description": "Identifies a subdivision of a country such as state, region, county.<br>最大长度: 35</br>", "allowEmptyValue": false}, "payeedepartment": {"type": "string", "example": "Hong Kong Deposit Protection Board", "description": "Payee estates.<br>最大长度: 70</br>", "allowEmptyValue": false}, "payeedistrictname": {"type": "string", "example": "Kowloon City", "description": "Identifies a subdivision within a country sub-division.<br>最大长度: 35</br>", "allowEmptyValue": false}, "payeefloor": {"type": "string", "example": 12, "description": "Payee floor.<br>最大长度: 70</br>", "allowEmptyValue": false}, "payeeid": {"type": "string", "example": "P000012", "description": "Payee unique code.<br>最大长度: 50</br>", "allowEmptyValue": false}, "payeename": {"type": "string", "example": "Hong Kong Deposit Protection Board", "description": "Payee name.<br>最大长度: 140</br>", "allowEmptyValue": false}, "payeepostbox": {"type": "string", "example": "#1202", "description": "Payee email account no.<br>最大长度: 16</br>", "allowEmptyValue": false}, "payeepostcode": {"type": "string", "example": 999077, "description": "Payee Post Code.<br>最大长度: 16</br>", "allowEmptyValue": false}, "payeeroom": {"type": "string", "example": "Room1202", "description": "Payee room number.<br>最大长度: 70</br>", "allowEmptyValue": false}, "payeestreetname": {"type": "string", "example": "Fat Kwong Street, No.68", "description": "Payee street name.<br>最大长度: 70</br>", "allowEmptyValue": false}, "payeesubdepartment": {"type": "string", "example": "Deposit Protection Board", "description": "Payee Estate Period.<br>最大长度: 70</br>", "allowEmptyValue": false}, "payeetownlocationname": {"type": "string", "example": "Kowloon Tong", "description": "Specific location name within the town.<br>最大长度: 35</br>", "allowEmptyValue": false}, "payeetownname": {"type": "string", "example": "Kowloon Tong", "description": "The town of Payee name.<br>最大长度: 35</br>", "allowEmptyValue": false}, "phonenumber": {"type": "string", "example": "2370 9288", "description": "Phone number.<br>最大长度: 34</br>", "allowEmptyValue": false}}, "title": "PayeeInformationModel"}, "PaymentModel": {"type": "object", "required": ["customerAccountNumber", "customerAccountType", "payeeId", "payeeNumber", "paymentAmount", "transactionCurrency"], "properties": {"customerAccountNumber": {"type": "string", "example": ****************, "description": "客户银行账户。支持001结尾的储蓄账户；002结尾的活期账户和信用卡账户。<br></br> 最大长度: 34", "allowEmptyValue": false}, "customerAccountType": {"type": "string", "example": "CRED", "description": "客户输入的银行账户的类型。<br> SAVI - 001结尾的储蓄账户<br>CURR - 002结尾的活期账户<br>CRED - 信用卡账户。 </br>最大长度: 4", "allowEmptyValue": false}, "payeeId": {"type": "string", "example": "G123456", "description": "收款方编号。例如，交水费的话，是给水利公司交。 这个按照服务大类分的话，就属于生活缴费，那么这个水利公司就会有一个id在系统中唯一标志在生活缴费这个大类下面的这一家水利公司。<br></br> 最大长度: 50", "allowEmptyValue": false}, "payeeNumber": {"type": "string", "example": ************, "description": "收款方的账户编号。例如，交水费的话，这里要添加您家在水利公司的账户编号。<br></br> 最大长度: 50", "allowEmptyValue": false}, "paymentAmount": {"type": "string", "example": 123, "description": "交易金额。<br></br> 最大长度: 18", "allowEmptyValue": false}, "paymentEffectiveDay": {"type": "string", "example": 1607854449000, "description": "交易指定的日期。例如：您想要指定2天后自动交水费，那么就可以设定2天后的日期，系统会在2天后的那天自动扣款缴费。<br></br> 最大长度: 13", "allowEmptyValue": true}, "remarks": {"type": "string", "example": "Current-PaymentTransaction", "description": "备注。<br></br> 最大长度: 140", "allowEmptyValue": true}, "transactionCurrency": {"type": "string", "example": "HKD", "description": "交易货币类型。 支持：HKD。<br></br> 最大长度: 3", "allowEmptyValue": false}}, "title": "PaymentModel"}, "PaymentResponseModel": {"type": "object", "properties": {"currencyCode": {"type": "string", "example": "HKD", "description": "Currency code.<br>最大长度: 50</br>", "allowEmptyValue": false}, "payeeId": {"type": "string", "example": "P000012", "description": "Payee unique code.<br>最大长度: 50</br>", "allowEmptyValue": false}, "payeeNumber": {"type": "string", "example": "00000001", "description": "The customer has a unique code in the corresponding Payee.<br>最大长度: 50</br>", "allowEmptyValue": false}, "paymentAmount": {"type": "string", "example": 2300, "description": "Payment Amount.<br>最大长度: 18</br>", "allowEmptyValue": false}, "paymentEffectiveDay": {"type": "integer", "format": "int64", "example": 1589944839000, "description": "Effective date of payment.<br>最大长度: 20</br>", "allowEmptyValue": false}, "transactiontime": {"type": "integer", "format": "int64", "example": 1589944839000, "description": "Transaction time.<br>最大长度: 20</br>", "allowEmptyValue": false}}, "title": "PaymentResponseModel"}, "QueryPayeeInfoModel": {"type": "object", "required": ["payeeCategoryId"], "properties": {"payeeCategoryId": {"type": "string", "example": "001", "description": "收款方所在种类的编号。例如，交水费的话，属于生活缴费，那么生活缴费就是一种类型，给教育机构缴费，那么教育也是一个种类。我们为这一个种类制定了一个编号。</br> 最大长度: 50", "allowEmptyValue": false}}, "title": "QueryPayeeInfoModel"}, "ResultUtil": {"type": "object", "properties": {"code": {"type": "string"}, "data": {"type": "object"}, "msg": {"type": "string"}}, "title": "ResultUtil"}, "ResultUtil«List«CustomerPayeeModel»»": {"type": "object", "properties": {"code": {"type": "string"}, "data": {"type": "array", "items": {"$ref": "#/definitions/CustomerPayeeModel"}}, "msg": {"type": "string"}}, "title": "ResultUtil«List«CustomerPayeeModel»»"}, "ResultUtil«List«PayeeCategoryInformationModel»»": {"type": "object", "properties": {"code": {"type": "string"}, "data": {"type": "array", "items": {"$ref": "#/definitions/PayeeCategoryInformationModel"}}, "msg": {"type": "string"}}, "title": "ResultUtil«List«PayeeCategoryInformationModel»»"}, "ResultUtil«List«PayeeInformationModel»»": {"type": "object", "properties": {"code": {"type": "string"}, "data": {"type": "array", "items": {"$ref": "#/definitions/PayeeInformationModel"}}, "msg": {"type": "string"}}, "title": "ResultUtil«List«PayeeInformationModel»»"}, "ResultUtil«PaymentResponseModel»": {"type": "object", "properties": {"code": {"type": "string"}, "data": {"$ref": "#/definitions/PaymentResponseModel"}, "msg": {"type": "string"}}, "title": "ResultUtil«PaymentResponseModel»"}, "SetBillPayeeModel": {"type": "object", "required": ["payeeCategoryID", "payeeID", "payeeNumber"], "properties": {"payeeCategoryID": {"type": "string", "example": "001", "description": "收款方所在种类的编号。例如，交水费的话，属于生活缴费，那么生活缴费就是一种类型，给教育机构缴费，那么教育也是一个种类。我们为这一个种类制定了一个编号。<br><br>最大长度: 50</br>", "allowEmptyValue": false}, "payeeID": {"type": "string", "example": "G123456", "description": "收款方编号。例如，交水费的话，是给水利公司交。 这个按照服务大类分的话，就属于生活缴费，那么这个水利公司就会有一个id在系统中唯一标志在生活缴费这个大类下面的这一家水利公司。<br><br>最大长度: 50</br>", "allowEmptyValue": false}, "payeeNumber": {"type": "string", "example": 1, "description": "客户在收款方机构的编号。例如交水费的话，那么客户在水利公司的账户编号用于唯一标志该客户在水利公司的账户。<br><br>最大长度: 50</br>", "allowEmptyValue": false}}, "title": "SetBillPayeeModel"}}}