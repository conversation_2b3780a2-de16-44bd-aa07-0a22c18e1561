{"swagger": "2.0", "info": {"description": "该服务模块主要包含了所有股票交易相关的业务API。包含了股票列表，股票市场信息，股票买入， 股票卖出，股票持仓查询及股票交易记录查询。当您开发股票相关的业务模块时，可以在这这里找到对应的API来完成不同业务功能。", "version": "1.0", "title": "股票业务", "contact": {"name": "<EMAIL>"}}, "schemes": ["https"], "host": "simnectz.hsu.edu.hk/lbsgateway/stock-experience", "basePath": "/", "tags": [{"name": "Market Information", "description": "查询并显示最新的股票市场信息。"}, {"name": "Order & Simulation", "description": "股票订单管理和股票交易模拟。"}, {"name": "Account Information & Maintenance", "description": "股票账户持仓信息和账户维护。"}, {"name": "Transaction", "description": "客户股票账户中的交易处理。"}], "paths": {"/stock/order/cancellation": {"post": {"tags": ["Order & Simulation"], "summary": "该API用于股票买卖过程中撤单操作。", "description": "在客户股票买卖过程中，当客户挂单（即想要买入或者卖出，都可以调用/order/orderPlacing），发起的挂单申请）之后，在未实际成交之前，可以调用该API来取消挂单业务。例如，在股票交易的过程中，客户需要先用自己的股票账户发起买入5000HKD的股票挂单申请，在实际成交之前，可以调用/stock/order/cancellation取消挂单，那么本次交易请求就取消了；也可以调用/stock/order/orderChange 来修改挂单的内容，例如将5000HKD改为6000HKD； 然后再调用/stock/order/simulatorUpdate API来模拟挂单交易成功还是失败，最终决定本次股票交易是否成功。如果不对挂单进行修改，则无需调用/stock/order/orderChange，直接就可以调用/stock/order/simulatorUpdate API来模拟挂单交易成功还是失败。", "operationId": "cancellationUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "颁发给第三方应用程序的最新客户授权令牌，以JWT格式访问该银行的客户数据。当开发者在调用API访问银行客户数据的时候，都需要用到该令牌。<br>最大长度: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "为前后端的每个请求唯一生成的128位随机UUID格式的消息ID。 该参数为内部参数，在调用API的时候不用修改该参数的内容。直接使用即可。<br>最大长度: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "当用户登录的时候生成的客户id。  该参数为内部参数，在调用API的时候不用修改该参数的内容。直接使用即可。 <br>最大长度: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "idmodel", "description": "idmodel", "required": true, "schema": {"$ref": "#/definitions/IDModel"}}], "responses": {"200": {"description": "查询成功。 （由Get方法返回）", "schema": {"$ref": "#/definitions/ResultUtil"}}, "201": {"description": "正常执行，请求成功。（由Post方法返回）", "schema": {"$ref": "#/definitions/ResultUtil"}}, "403": {"description": "令牌的作用范围不正确或违反了安全策略。操作：请检查您是否使用合法授权用户帐户的正确令牌。"}, "404": {"description": "请求的帐户不存在。操作：请确保您输入的帐号和帐户类型正确。"}, "500": {"description": "API网关或微服务出现问题。操作：检查您的网络，稍后再试。"}}}}, "/stock/order/orderChange": {"post": {"tags": ["Order & Simulation"], "summary": "该API用于修改挂单信息。", "description": "例如当用户想要买入或者卖出股票的时候，都需要先挂单。例如，客户想要买入1000份额的某只股票时，就需要调用  /stock/order/orderPlacing 来下单。这就是一个挂单的过程。在这个挂单实际成交之前，用户可以修改这个挂单的信息，比如想把1000份额改成2000份额，就可以调用该API来完成。但是如果挂单已经调用了/stock/order/simulatorUpdate模拟成交了， 那么就不能再被修改了。 ", "operationId": "orderInfoUpdateUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "颁发给第三方应用程序的最新客户授权令牌，以JWT格式访问该银行的客户数据。当开发者在调用API访问银行客户数据的时候，都需要用到该令牌。<br>最大长度: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "为前后端的每个请求唯一生成的128位随机UUID格式的消息ID。 该参数为内部参数，在调用API的时候不用修改该参数的内容。直接使用即可。<br>最大长度: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "当用户登录的时候生成的客户id。  该参数为内部参数，在调用API的时候不用修改该参数的内容。直接使用即可。 <br>最大长度: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "updateOrderRequestModel", "description": "updateOrderRequestModel", "required": true, "schema": {"$ref": "#/definitions/UpdateOrderRequestModel"}}], "responses": {"200": {"description": "查询成功。 （由Get方法返回）", "schema": {"$ref": "#/definitions/ResultUtil"}}, "201": {"description": "正常执行，请求成功。（由Post方法返回）", "schema": {"$ref": "#/definitions/ResultUtil"}}, "403": {"description": "令牌的作用范围不正确或违反了安全策略。操作：请检查您是否使用合法授权用户帐户的正确令牌。"}, "404": {"description": "请求的帐户不存在。操作：请确保您输入的帐号和帐户类型正确。"}, "500": {"description": "API网关或微服务出现问题。操作：检查您的网络，稍后再试。"}}}}, "/stock/order/orderDetailRetrievalById": {"post": {"tags": ["Order & Simulation"], "summary": "该API用于某一个挂单记录的查询。", "description": "可以查询某个挂单的详情。例如，客户想要买入1000份额的某只股票时，就需要调用/stock/order/orderPlacing 来下单。这就是一个挂单的过程。不管是买入或者卖出都要挂单，挂单之后，会返回一个参数id（即挂单的编号），然后可以调用/stock/order/orderDetailRetrievalById， 输入该编号，就可以获取到该挂单的详细信息了。", "operationId": "orderDetailRetrievalByIdUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "颁发给第三方应用程序的最新客户授权令牌，以JWT格式访问该银行的客户数据。当开发者在调用API访问银行客户数据的时候，都需要用到该令牌。<br>最大长度: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "为前后端的每个请求唯一生成的128位随机UUID格式的消息ID。 该参数为内部参数，在调用API的时候不用修改该参数的内容。直接使用即可。<br>最大长度: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "当用户登录的时候生成的客户id。  该参数为内部参数，在调用API的时候不用修改该参数的内容。直接使用即可。 <br>最大长度: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "idmodel", "description": "idmodel", "required": true, "schema": {"$ref": "#/definitions/IDModel"}}], "responses": {"200": {"description": "查询成功。 （由Get方法返回）", "schema": {"$ref": "#/definitions/ResultUtil«StockOrderInfoModel»"}}, "201": {"description": "正常执行，请求成功。（由Post方法返回）", "schema": {"$ref": "#/definitions/ResultUtil«StockOrderInfoModel»"}}, "403": {"description": "令牌的作用范围不正确或违反了安全策略。操作：请检查您是否使用合法授权用户帐户的正确令牌。"}, "404": {"description": "请求的帐户不存在。操作：请确保您输入的帐号和帐户类型正确。"}, "500": {"description": "API网关或微服务出现问题。操作：检查您的网络，稍后再试。"}}}}, "/stock/order/orderPlacing": {"post": {"tags": ["Order & Simulation"], "summary": "该API用于买入卖出股票时，挂单的服务。", "description": "例如当用户想要买入或者卖出股票的时候，都需要先挂单。例如，客户想要买入1000份额的某只股票时，就需要调用  /stock/order/orderPlacing 来下单，输入想买的股票代码，买入价格，买入份额等信息。这就是一个挂单的过程。挂单之后，客户可能需要去修改挂单信息，就可以调用/stock/order/orderChange；如果想要模拟成交或失败交易的情况，可以调用/stock/order/simulatorUpdate。", "operationId": "orderPlacingUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "颁发给第三方应用程序的最新客户授权令牌，以JWT格式访问该银行的客户数据。当开发者在调用API访问银行客户数据的时候，都需要用到该令牌。<br>最大长度: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "为前后端的每个请求唯一生成的128位随机UUID格式的消息ID。 该参数为内部参数，在调用API的时候不用修改该参数的内容。直接使用即可。<br>最大长度: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "当用户登录的时候生成的客户id。  该参数为内部参数，在调用API的时候不用修改该参数的内容。直接使用即可。 <br>最大长度: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "stockTradingModel", "description": "stockTradingModel", "required": true, "schema": {"$ref": "#/definitions/StockTradingModel"}}], "responses": {"200": {"description": "查询成功。 （由Get方法返回）", "schema": {"$ref": "#/definitions/ResultUtil"}}, "201": {"description": "正常执行，请求成功。（由Post方法返回）", "schema": {"$ref": "#/definitions/ResultUtil"}}, "403": {"description": "令牌的作用范围不正确或违反了安全策略。操作：请检查您是否使用合法授权用户帐户的正确令牌。"}, "404": {"description": "请求的帐户不存在。操作：请确保您输入的帐号和帐户类型正确。"}, "500": {"description": "API网关或微服务出现问题。操作：检查您的网络，稍后再试。"}}}}, "/stock/order/orderRetrieval": {"post": {"tags": ["Order & Simulation"], "summary": "该API用于所有挂单记录的查询。", "description": "可以查询某个股票账户在某段时间内挂单的历史记录及详情。例如，客户想要买入1000份额的某只股票时，就需要调用/stock/order/orderPlacing 来下单。这就是一个挂单的过程。不管是买入或者卖出都要挂单，挂单之后，可以调用该API去查看某段时间内的挂单历史记录。", "operationId": "orderRetrievalUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "颁发给第三方应用程序的最新客户授权令牌，以JWT格式访问该银行的客户数据。当开发者在调用API访问银行客户数据的时候，都需要用到该令牌。<br>最大长度: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "为前后端的每个请求唯一生成的128位随机UUID格式的消息ID。 该参数为内部参数，在调用API的时候不用修改该参数的内容。直接使用即可。<br>最大长度: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "当用户登录的时候生成的客户id。  该参数为内部参数，在调用API的时候不用修改该参数的内容。直接使用即可。 <br>最大长度: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "orderRequestModel", "description": "orderRequestModel", "required": true, "schema": {"$ref": "#/definitions/OrderRequestModel"}}], "responses": {"200": {"description": "查询成功。 （由Get方法返回）", "schema": {"$ref": "#/definitions/ResultUtil«List«StockOrderInfoModel»»"}}, "201": {"description": "正常执行，请求成功。（由Post方法返回）", "schema": {"$ref": "#/definitions/ResultUtil«List«StockOrderInfoModel»»"}}, "403": {"description": "令牌的作用范围不正确或违反了安全策略。操作：请检查您是否使用合法授权用户帐户的正确令牌。"}, "404": {"description": "请求的帐户不存在。操作：请确保您输入的帐号和帐户类型正确。"}, "500": {"description": "API网关或微服务出现问题。操作：检查您的网络，稍后再试。"}}}}, "/stock/order/simulatorUpdate": {"post": {"tags": ["Order & Simulation"], "summary": "该API用于模拟股票交易中挂单成功后，模拟实际成交还是失败的过程。", "description": "例如，客户想要买入1000份额的某只股票时，就需要调用 /stock/order/orderPlacing  来下单。这个过程就代表挂单成功了，然后就可以再调用/stock/order/simulatorUpdate API来模拟股票是否能成功交易的过程。直接通过修改参数的数字状态，提交后就可以完成本次股票成交的过程。", "operationId": "orderStatusUpdateUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "颁发给第三方应用程序的最新客户授权令牌，以JWT格式访问该银行的客户数据。当开发者在调用API访问银行客户数据的时候，都需要用到该令牌。<br>最大长度: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "为前后端的每个请求唯一生成的128位随机UUID格式的消息ID。 该参数为内部参数，在调用API的时候不用修改该参数的内容。直接使用即可。<br>最大长度: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "当用户登录的时候生成的客户id。  该参数为内部参数，在调用API的时候不用修改该参数的内容。直接使用即可。 <br>最大长度: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "updateOrderStatusModel", "description": "updateOrderStatusModel", "required": true, "schema": {"$ref": "#/definitions/UpdateOrderStatusModel"}}], "responses": {"200": {"description": "查询成功。 （由Get方法返回）", "schema": {"$ref": "#/definitions/ResultUtil"}}, "201": {"description": "正常执行，请求成功。（由Post方法返回）", "schema": {"$ref": "#/definitions/ResultUtil"}}, "403": {"description": "令牌的作用范围不正确或违反了安全策略。操作：请检查您是否使用合法授权用户帐户的正确令牌。"}, "404": {"description": "请求的帐户不存在。操作：请确保您输入的帐号和帐户类型正确。"}, "500": {"description": "API网关或微服务出现问题。操作：检查您的网络，稍后再试。"}}}}, "/stock/settlementAccountUpdate": {"post": {"tags": ["Account Information & Maintenance"], "summary": "该API用于设置股票账户的关联储蓄账户。", "description": "例如，当银行客户想要买一只股票，首先这个用户需要有一个股票账户，然后再给这个股票账户绑定一个储蓄账户或活期账户（001结尾或者002结尾的账户）。当客户买入100份额的某只股票，需要花费1000HKD的时候，那么系统就会从绑定的这个储蓄账户中或活期账户直接扣掉1000HKD，与此同时，这个股票账户的持仓中就会多出这100份的该只股票份额。同理，当客户卖出这100份的股票，卖出所得1200HKD的时候，那么绑定的储蓄账户或活期账户就会收到这个1200HKD，与此同时，该股票账户的持仓中，就会减少这100份额的股票。在绑定一个储蓄账户或者活期账户之前，您可以需要先调用/deposit/account/allAccounts/{customerNumber}/{index}/{items}来查询该银行客户所持有的其他账户，是否有需要的001或002结尾的账户类型。如果没有的话，那么您就需要先调用/deposit/account/accountCreation去创建一个001或者002的账户。", "operationId": "settlementAccountUpdateUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "颁发给第三方应用程序的最新客户授权令牌，以JWT格式访问该银行的客户数据。当开发者在调用API访问银行客户数据的时候，都需要用到该令牌。<br>最大长度: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "为前后端的每个请求唯一生成的128位随机UUID格式的消息ID。 该参数为内部参数，在调用API的时候不用修改该参数的内容。直接使用即可。<br>最大长度: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "当用户登录的时候生成的客户id。  该参数为内部参数，在调用API的时候不用修改该参数的内容。直接使用即可。 <br>最大长度: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "updateSettlementAccountModel", "description": "updateSettlementAccountModel", "required": true, "schema": {"$ref": "#/definitions/UpdateSettlementAccountModel"}}], "responses": {"200": {"description": "查询成功。 （由Get方法返回）", "schema": {"$ref": "#/definitions/ResultUtil"}}, "201": {"description": "正常执行，请求成功。（由Post方法返回）", "schema": {"$ref": "#/definitions/ResultUtil"}}, "403": {"description": "令牌的作用范围不正确或违反了安全策略。操作：请检查您是否使用合法授权用户帐户的正确令牌。"}, "404": {"description": "请求的帐户不存在。操作：请确保您输入的帐号和帐户类型正确。"}, "500": {"description": "API网关或微服务出现问题。操作：检查您的网络，稍后再试。"}}}}, "/stock/stockHoldingEnquiry": {"post": {"tags": ["Account Information & Maintenance"], "summary": "该API用于查询股票账户的持仓详情。", "description": "可以查询客户的股票账户下，对于某个股票的持仓信息和该股票账户下所有的股票持仓信息。如果客户还没有任何持仓信息，可能需要先买入股票，可以调用/stock/order/orderPlacing来做一个买入股票的挂单。然后调用/stock/order/simulatorUpdate 来模拟买入股票交易成功。", "operationId": "stockHoldingEnquiryUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "颁发给第三方应用程序的最新客户授权令牌，以JWT格式访问该银行的客户数据。当开发者在调用API访问银行客户数据的时候，都需要用到该令牌。<br>最大长度: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "为前后端的每个请求唯一生成的128位随机UUID格式的消息ID。 该参数为内部参数，在调用API的时候不用修改该参数的内容。直接使用即可。<br>最大长度: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "当用户登录的时候生成的客户id。  该参数为内部参数，在调用API的时候不用修改该参数的内容。直接使用即可。 <br>最大长度: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "stockHoldingEnquiryModel", "description": "stockHoldingEnquiryModel", "required": true, "schema": {"$ref": "#/definitions/StockHoldingEnquiryModel"}}], "responses": {"200": {"description": "查询成功。 （由Get方法返回）", "schema": {"$ref": "#/definitions/ResultUtil«StockHoldingInfoModel»"}}, "201": {"description": "正常执行，请求成功。（由Post方法返回）", "schema": {"$ref": "#/definitions/ResultUtil«StockHoldingInfoModel»"}}, "403": {"description": "令牌的作用范围不正确或违反了安全策略。操作：请检查您是否使用合法授权用户帐户的正确令牌。"}, "404": {"description": "请求的帐户不存在。操作：请确保您输入的帐号和帐户类型正确。"}, "500": {"description": "API网关或微服务出现问题。操作：检查您的网络，稍后再试。"}}}}, "/stock/stockList": {"post": {"tags": ["Market Information"], "summary": "该API用于获取市场上的所有股票列表及所有股票详情。", "description": "当想要展示基金列表及每个基金的市场信息时，如股票价格，成交量等，都可以调用该API。", "operationId": "stockListUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "颁发给第三方应用程序的最新客户授权令牌，以JWT格式访问该银行的客户数据。当开发者在调用API访问银行客户数据的时候，都需要用到该令牌。<br>最大长度: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "为前后端的每个请求唯一生成的128位随机UUID格式的消息ID。 该参数为内部参数，在调用API的时候不用修改该参数的内容。直接使用即可。<br>最大长度: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "当用户登录的时候生成的客户id。  该参数为内部参数，在调用API的时候不用修改该参数的内容。直接使用即可。 <br>最大长度: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "queryStockListModel", "description": "queryStockListModel", "required": true, "schema": {"$ref": "#/definitions/QueryStockListModel"}}], "responses": {"200": {"description": "查询成功。 （由Get方法返回）", "schema": {"$ref": "#/definitions/ResultUtil«List«StockInformationModel»»"}}, "201": {"description": "正常执行，请求成功。（由Post方法返回）", "schema": {"$ref": "#/definitions/ResultUtil«List«StockInformationModel»»"}}, "403": {"description": "令牌的作用范围不正确或违反了安全策略。操作：请检查您是否使用合法授权用户帐户的正确令牌。"}, "404": {"description": "请求的帐户不存在。操作：请确保您输入的帐号和帐户类型正确。"}, "500": {"description": "API网关或微服务出现问题。操作：检查您的网络，稍后再试。"}}}}, "/stock/stockQuotation": {"post": {"tags": ["Market Information"], "summary": "该API用于查询某只股票的市场信息。", "description": "例如，当客户想要查看代码为00112.HK的股票价格，涨跌幅等市场信息，都可以调用该API来完成查询。", "operationId": "stockQuotationUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "颁发给第三方应用程序的最新客户授权令牌，以JWT格式访问该银行的客户数据。当开发者在调用API访问银行客户数据的时候，都需要用到该令牌。<br>最大长度: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "为前后端的每个请求唯一生成的128位随机UUID格式的消息ID。 该参数为内部参数，在调用API的时候不用修改该参数的内容。直接使用即可。<br>最大长度: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "当用户登录的时候生成的客户id。  该参数为内部参数，在调用API的时候不用修改该参数的内容。直接使用即可。 <br>最大长度: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "stockCodeModel", "description": "stockCodeModel", "required": true, "schema": {"$ref": "#/definitions/StockCodeModel"}}], "responses": {"200": {"description": "查询成功。 （由Get方法返回）", "schema": {"$ref": "#/definitions/ResultUtil«StockInformationModel»"}}, "201": {"description": "正常执行，请求成功。（由Post方法返回）", "schema": {"$ref": "#/definitions/ResultUtil«StockInformationModel»"}}, "403": {"description": "令牌的作用范围不正确或违反了安全策略。操作：请检查您是否使用合法授权用户帐户的正确令牌。"}, "404": {"description": "请求的帐户不存在。操作：请确保您输入的帐号和帐户类型正确。"}, "500": {"description": "API网关或微服务出现问题。操作：检查您的网络，稍后再试。"}}}}, "/stock/transactionRetrieval": {"post": {"tags": ["Transaction"], "summary": "该API用于查询某个客户的股票账户的历史交易记录。", "description": "例如，客户用股票账户购买过很多只股票，那么就可以在这里调用相应的API查询某段时间内，某只股票的交易记录，或者该账户在某段时间内的所有交易记录。", "operationId": "retrieveStkTransactionUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "颁发给第三方应用程序的最新客户授权令牌，以JWT格式访问该银行的客户数据。当开发者在调用API访问银行客户数据的时候，都需要用到该令牌。<br>最大长度: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "为前后端的每个请求唯一生成的128位随机UUID格式的消息ID。 该参数为内部参数，在调用API的时候不用修改该参数的内容。直接使用即可。<br>最大长度: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "当用户登录的时候生成的客户id。  该参数为内部参数，在调用API的时候不用修改该参数的内容。直接使用即可。 <br>最大长度: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "queryStockTransModel", "description": "queryStockTransModel", "required": true, "schema": {"$ref": "#/definitions/QueryStockTransModel"}}], "responses": {"200": {"description": "查询成功。 （由Get方法返回）", "schema": {"$ref": "#/definitions/ResultUtil«List«StockPlatFormLogModel»»"}}, "201": {"description": "正常执行，请求成功。（由Post方法返回）", "schema": {"$ref": "#/definitions/ResultUtil«List«StockPlatFormLogModel»»"}}, "403": {"description": "令牌的作用范围不正确或违反了安全策略。操作：请检查您是否使用合法授权用户帐户的正确令牌。"}, "404": {"description": "请求的帐户不存在。操作：请确保您输入的帐号和帐户类型正确。"}, "500": {"description": "API网关或微服务出现问题。操作：检查您的网络，稍后再试。"}}}}}, "definitions": {"IDModel": {"type": "object", "required": ["id"], "properties": {"id": {"type": "number", "example": 2.0, "description": "股票订单编号。当您调用买入或者卖出股票申请API之后，就会返回一个股票订单编号。<br>最大长度: 20</br>", "allowEmptyValue": false}}, "title": "IDModel"}, "OrderRequestModel": {"type": "object", "required": ["fromdate", "index", "items", "stkaccountnumber", "todate"], "properties": {"fromdate": {"type": "number", "example": *************.0, "description": "想要查询的订单的开始时间范围。<br/>最大长度: 20", "allowEmptyValue": false}, "index": {"type": "number", "example": 0.0, "description": "API返回数据的开始位置标记。如：当index=0时，则表示从第1条数据开始返回结果。<br/>最大长度: 4", "allowEmptyValue": false}, "items": {"type": "number", "example": 3.0, "description": "API返回的数据条数。如：当items=10. 表示返回10条结果。<br/>最大长度: 4", "allowEmptyValue": false}, "stkaccountnumber": {"type": "string", "example": "***********************", "description": "股票账户号码。<br/> 最大长度: 34", "allowEmptyValue": false}, "stockcode": {"type": "string", "example": "00112.HK", "description": "股票代码。<br/> 最大长度: 10", "allowEmptyValue": true}, "todate": {"type": "number", "example": *************.0, "description": "想要查询的订单的结束时间范围。<br/>最大长度: 20", "allowEmptyValue": false}}, "title": "OrderRequestModel"}, "QueryStockListModel": {"type": "object", "required": ["index", "items"], "properties": {"index": {"type": "number", "example": 0.0, "description": "API返回数据的开始位置标记。如：当index=0时，则表示从第1条数据开始返回结果。<br/>最大长度: 4", "allowEmptyValue": false}, "items": {"type": "number", "example": 3.0, "description": "API返回的数据条数。如：当items=10. 表示返回10条结果。<br/>最大长度: 4", "allowEmptyValue": false}}, "title": "QueryStockListModel"}, "QueryStockTransModel": {"type": "object", "required": ["accountnumber", "index", "items", "transFromDate", "transToDate"], "properties": {"accountnumber": {"type": "string", "example": "***********************", "description": "股票账户号码。<br/> 最大长度: 34", "allowEmptyValue": false}, "index": {"type": "number", "example": 0.0, "description": "API返回数据的开始位置标记。如：当index=0时，则表示从第1条数据开始返回结果。<br/>最大长度: 4", "allowEmptyValue": false}, "items": {"type": "number", "example": 3.0, "description": "API返回的数据条数。如：当items=10. 表示返回10条结果。<br/>最大长度: 4", "allowEmptyValue": false}, "stocknumber": {"type": "string", "example": "00112.HK", "description": "股票代码。<br/> 最大长度: 10", "allowEmptyValue": true}, "transFromDate": {"type": "number", "example": *************.0, "description": "交易记录开始时间。<br/>最大长度: 20", "allowEmptyValue": false}, "transToDate": {"type": "number", "example": *************.0, "description": "交易记录结束时间。<br/>最大长度: 20", "allowEmptyValue": false}}, "title": "QueryStockTransModel"}, "ResultUtil": {"type": "object", "properties": {"code": {"type": "string"}, "data": {"type": "object"}, "msg": {"type": "string"}}, "title": "ResultUtil"}, "ResultUtil«List«StockDateIndexHistoryModel»»": {"type": "object", "properties": {"code": {"type": "string"}, "data": {"type": "array", "items": {"$ref": "#/definitions/StockDateIndexHistoryModel"}}, "msg": {"type": "string"}}, "title": "ResultUtil«List«StockDateIndexHistoryModel»»"}, "ResultUtil«List«StockIndexHistoryModel»»": {"type": "object", "properties": {"code": {"type": "string"}, "data": {"type": "array", "items": {"$ref": "#/definitions/StockIndexHistoryModel"}}, "msg": {"type": "string"}}, "title": "ResultUtil«List«StockIndexHistoryModel»»"}, "ResultUtil«List«StockInformationModel»»": {"type": "object", "properties": {"code": {"type": "string"}, "data": {"type": "array", "items": {"$ref": "#/definitions/StockInformationModel"}}, "msg": {"type": "string"}}, "title": "ResultUtil«List«StockInformationModel»»"}, "ResultUtil«List«StockOrderInfoModel»»": {"type": "object", "properties": {"code": {"type": "string"}, "data": {"type": "array", "items": {"$ref": "#/definitions/StockOrderInfoModel"}}, "msg": {"type": "string"}}, "title": "ResultUtil«List«StockOrderInfoModel»»"}, "ResultUtil«List«StockPlatFormLogModel»»": {"type": "object", "properties": {"code": {"type": "string"}, "data": {"type": "array", "items": {"$ref": "#/definitions/StockPlatFormLogModel"}}, "msg": {"type": "string"}}, "title": "ResultUtil«List«StockPlatFormLogModel»»"}, "ResultUtil«StockHoldingInfoModel»": {"type": "object", "properties": {"code": {"type": "string"}, "data": {"$ref": "#/definitions/StockHoldingInfoModel"}, "msg": {"type": "string"}}, "title": "ResultUtil«StockHoldingInfoModel»"}, "ResultUtil«StockInformationModel»": {"type": "object", "properties": {"code": {"type": "string"}, "data": {"$ref": "#/definitions/StockInformationModel"}, "msg": {"type": "string"}}, "title": "ResultUtil«StockInformationModel»"}, "ResultUtil«StockOrderInfoModel»": {"type": "object", "properties": {"code": {"type": "string"}, "data": {"$ref": "#/definitions/StockOrderInfoModel"}, "msg": {"type": "string"}}, "title": "ResultUtil«StockOrderInfoModel»"}, "StockCodeModel": {"type": "object", "required": ["stockcode"], "properties": {"stockcode": {"type": "string", "example": "00112.HK", "description": "股票代码。<br/> 最大长度: 10", "allowEmptyValue": false}}, "title": "StockCodeModel"}, "StockDateCSI300IndexModel": {"type": "object", "properties": {"selectDate": {"type": "string", "example": "1month", "description": "Query time span,only allow input 1month,3months,6months,1year,3years.<br>最大长度: 20</br>", "allowEmptyValue": false}, "type": {"type": "string", "example": "CSI300", "description": "Index type.<br>最大长度: 50</br>", "allowEmptyValue": true}}, "title": "StockDateCSI300IndexModel"}, "StockDateDJIIndexModel": {"type": "object", "properties": {"selectDate": {"type": "string", "example": "1month", "description": "Query time span,only allow input 1month,3months,6months,1year,3years.<br>最大长度: 20</br>", "allowEmptyValue": false}, "type": {"type": "string", "example": "DJI", "description": "Index type.<br>最大长度: 50</br>", "allowEmptyValue": true}}, "title": "StockDateDJIIndexModel"}, "StockDateHSIIndexModel": {"type": "object", "properties": {"selectDate": {"type": "string", "example": "1month", "description": "Query time span,only allow input 1month,3months,6months,1year,3years.<br>最大长度: 20</br>", "allowEmptyValue": false}, "type": {"type": "string", "example": "HSI", "description": "Index type.<br>最大长度: 50</br>", "allowEmptyValue": true}}, "title": "StockDateHSIIndexModel"}, "StockDateIXICIndexModel": {"type": "object", "properties": {"selectDate": {"type": "string", "example": "1month", "description": "Query time span,only allow input 1month,3months,6months,1year,3years.<br>最大长度: 20</br>", "allowEmptyValue": false}, "type": {"type": "string", "example": "IXIC", "description": "Index type.<br>最大长度: 50</br>", "allowEmptyValue": true}}, "title": "StockDateIXICIndexModel"}, "StockDateIndexHistoryModel": {"type": "object", "properties": {"createDate": {"type": "string", "example": "2018年1月2日", "description": "Index create date.<br>最大长度: 50</br>", "allowEmptyValue": false}, "high": {"type": "number", "example": 30515.31, "description": "The highest index.<br>最大长度: 18</br>", "allowEmptyValue": false}, "id": {"type": "integer", "format": "int32", "example": 1, "description": "id.<br>最大长度: 20</br>", "allowEmptyValue": false}, "last": {"type": "number", "example": 30515.31, "description": "Last index.<br>最大长度: 18</br>", "allowEmptyValue": false}, "low": {"type": "number", "example": 30028.29, "description": "The lowest index.<br>最大长度: 18</br>", "allowEmptyValue": false}, "open": {"type": "number", "example": 30028.29, "description": "Opening index.<br>最大长度: 18</br>", "allowEmptyValue": false}, "type": {"type": "string", "example": "HSI", "description": "Index type.<br>最大长度: 50</br>", "allowEmptyValue": false}, "updateTime": {"type": "number", "example": 1514822400000.0, "description": "Index update date.<br>最大长度: 20</br>", "allowEmptyValue": false}}, "title": "StockDateIndexHistoryModel"}, "StockDateSSECIndexModel": {"type": "object", "properties": {"selectDate": {"type": "string", "example": "1month", "description": "Query time span,only allow input 1month,3months,6months,1year,3years.<br>最大长度: 20</br>", "allowEmptyValue": false}, "type": {"type": "string", "example": "SSEC", "description": "Index type.<br>最大长度: 50</br>", "allowEmptyValue": true}}, "title": "StockDateSSECIndexModel"}, "StockHoldingEnquiryModel": {"type": "object", "required": ["stkaccountnumber"], "properties": {"stkaccountnumber": {"type": "string", "example": "***********************", "description": "股票账户号码。<br/> 最大长度: 34", "allowEmptyValue": false}, "stockcode": {"type": "string", "example": "00112.HK", "description": "股票代码。<br/> 最大长度: 10", "allowEmptyValue": true}}, "title": "StockHoldingEnquiryModel"}, "StockHoldingInfoModel": {"type": "object", "properties": {"settlementaccount": {"type": "string", "example": "***********************", "description": "Settlement Account Number.</br> 最大长度: 34", "allowEmptyValue": false}, "stkaccountnumber": {"type": "string", "example": "***********************", "description": "A stock account number of the customer.<br/> 最大长度: 34", "allowEmptyValue": false}, "stkholdlist": {"type": "array", "items": {"$ref": "#/definitions/StockInvestmentModel"}}, "totalInvestmentAmount": {"type": "string", "example": 100, "description": "total investment amount.<br>最大长度: 18</br>", "allowEmptyValue": false}, "totalNetGainLoss": {"type": "string", "example": 100, "description": "total NetGain Loss.<br>最大长度: 18</br>", "allowEmptyValue": false}, "totalNetGainLossPct": {"type": "string", "example": 100, "description": "total NetGain LossPct.<br>最大长度: 18</br>", "allowEmptyValue": false}, "totalmarkervalue": {"type": "string", "description": "total market value.<br>最大长度: 18</br>", "allowEmptyValue": false}}, "title": "StockHoldingInfoModel"}, "StockIndexHistoryModel": {"type": "object", "properties": {"createDate": {"type": "string", "example": "2018年1月2日", "description": "Index create date.<br>最大长度: 50</br>", "allowEmptyValue": false}, "high": {"type": "number", "example": 30515.31, "description": "The highest index.<br>最大长度: 18</br>", "allowEmptyValue": false}, "id": {"type": "integer", "format": "int32", "example": 1, "description": "id.<br>最大长度: 20</br>", "allowEmptyValue": false}, "last": {"type": "number", "example": 30515.31, "description": "Last index.<br>最大长度: 18</br>", "allowEmptyValue": false}, "low": {"type": "number", "example": 30028.29, "description": "The lowest index.<br>最大长度: 18</br>", "allowEmptyValue": false}, "open": {"type": "number", "example": 30028.29, "description": "Opening index.<br>最大长度: 18</br>", "allowEmptyValue": false}, "type": {"type": "string", "example": "HSI", "description": "Index type.<br>最大长度: 50</br>", "allowEmptyValue": false}, "upDown": {"type": "number", "example": -645.89, "description": "升跌.<br>最大长度: 20</br>", "allowEmptyValue": false}, "upDownPercentage": {"type": "string", "example": "-2.2%", "description": "升跌(%).<br>最大长度: 20</br>", "allowEmptyValue": false}, "upDownWeek": {"type": "string", "example": "-1.2%", "description": "本周升跌(%).<br>最大长度: 20</br>", "allowEmptyValue": false}, "updateTime": {"type": "number", "example": 1514822400000.0, "description": "Index update date.<br>最大长度: 20</br>", "allowEmptyValue": false}}, "title": "StockIndexHistoryModel"}, "StockIndexModel": {"type": "object", "properties": {"selectDate": {"type": "string", "example": 1618217381883, "description": "Query date.<br>最大长度: 20</br>", "allowEmptyValue": true}}, "title": "StockIndexModel"}, "StockInformationModel": {"type": "object", "properties": {"buyprice": {"type": "number", "example": 698.0, "description": "Buying price.<br>最大长度: 18</br>", "allowEmptyValue": false}, "changed": {"type": "string", "example": 1.0, "description": "Changed in stocks.<br>最大长度: 18</br>", "allowEmptyValue": false}, "changedpercent": {"type": "string", "example": 0.143, "description": "Percentage increase in stocks.<br>最大长度: 18</br>", "allowEmptyValue": false}, "eps": {"type": "number", "example": 11.01, "description": "Earnings per share.<br>最大长度: 18</br>", "allowEmptyValue": false}, "high": {"type": "number", "example": 722.0, "description": "The highest stock price of the day.<br>最大长度: 18</br>", "allowEmptyValue": false}, "hs_tech": {"type": "string", "example": "N", "description": "恒生科技指数.<br>最大长度: 18</br>", "allowEmptyValue": false}, "hscei": {"type": "string", "example": "N", "description": "国企指数.<br>最大长度: 18</br>", "allowEmptyValue": false}, "hsi": {"type": "string", "example": "Y", "description": "恒生指数.<br>最大长度: 18</br>", "allowEmptyValue": false}, "id": {"type": "integer", "format": "int32", "example": 1, "description": "Unique id.<br>最大长度: 20</br>", "allowEmptyValue": false}, "industry": {"type": "string", "example": "Property Investment", "description": "Industry.<br>最大长度: 20</br>", "allowEmptyValue": false}, "lastprice": {"type": "number", "example": 698.0, "description": "The latest price.<br>最大长度: 18</br>", "allowEmptyValue": false}, "lasttradingday": {"type": "number", "example": *************.0, "description": "Last trading day.<br>最大长度: 20</br>", "allowEmptyValue": false}, "lotsize": {"type": "number", "example": 100.0, "description": "Stock lot size.<br>最大长度: 18</br>", "allowEmptyValue": false}, "low": {"type": "number", "example": 692.5, "description": "The lowest stock price of the day.<br>最大长度: 18</br>", "allowEmptyValue": false}, "marketcap": {"type": "string", "description": "MarketCap.<br>最大长度: 18</br>", "allowEmptyValue": false}, "open": {"type": "number", "example": 709.5, "description": "Stock the opening.<br>最大长度: 18</br>", "allowEmptyValue": false}, "pe": {"type": "number", "description": "PE.<br>最大长度: 18</br>", "allowEmptyValue": false}, "previousclose": {"type": "number", "example": 697.0, "description": "Latest closing price.<br>最大长度: 18</br>", "allowEmptyValue": false}, "ratio": {"type": "number", "example": 63.37, "description": "Ratio.<br>最大长度: 18</br>", "allowEmptyValue": false}, "sellprice": {"type": "number", "example": 698.5, "description": "Selling price.<br>最大长度: 18</br>", "allowEmptyValue": false}, "shsc": {"type": "string", "example": "Y", "description": "沪港通/深港通.<br>最大长度: 18</br>", "allowEmptyValue": false}, "stockcode": {"type": "string", "example": "00700.HK", "description": "Stock code.<br>最大长度: 10</br>", "allowEmptyValue": false}, "stockname": {"type": "string", "example": "TENCENT", "description": "Stock name.<br>最大长度: 140</br>", "allowEmptyValue": false}, "tradingpoint": {"type": "number", "example": 0.5, "description": "Trading posts.<br>最大长度: 18</br>", "allowEmptyValue": false}, "turnover": {"type": "number", "example": ***********.34, "description": "Stock turnover.<br>最大长度: 18</br>", "allowEmptyValue": false}, "volume": {"type": "number", "example": ********.0, "description": "Stock trading volume.<br>最大长度: 18</br>", "allowEmptyValue": false}, "yield": {"type": "string", "description": "Yield.<br>最大长度: 18</br>", "allowEmptyValue": false}}, "title": "StockInformationModel"}, "StockInvestmentModel": {"type": "object", "properties": {"accountnumber": {"type": "string", "example": "***********************", "description": "A fund account number. <br/> 最大长度: 34", "allowEmptyValue": false}, "availableshare": {"type": "number", "example": 1000.0, "description": "Tradable share.<br>最大长度: 18</br>", "allowEmptyValue": false}, "averageprice": {"type": "number", "example": 60.0, "description": "Stock average price.<br>最大长度: 18</br>", "allowEmptyValue": false}, "currencycode": {"type": "string", "example": "HKD", "description": "Currency code.<br>最大长度: 3</br>", "allowEmptyValue": false}, "investmentAmount": {"type": "number", "example": 500.0, "description": "Investment amount.<br>最大长度: 18</br>", "allowEmptyValue": false}, "lastupdatedate": {"type": "number", "example": *************.0, "description": "Last update date.<br>最大长度: 20</br>", "allowEmptyValue": false}, "marketprice": {"type": "number", "example": 500.0, "description": "Market price.<br>最大长度: 18</br>", "allowEmptyValue": false}, "marketvalue": {"type": "number", "example": 50000.0, "description": "Market value.<br>最大长度: 18</br>", "allowEmptyValue": false}, "netGainLoss": {"type": "number", "example": 3000.0, "description": "Net income loss.<br>最大长度: 18</br>", "allowEmptyValue": false}, "netGainLossPct": {"type": "string", "example": 300, "description": "Net gain Loss Pct.<br>最大长度: 18</br>", "allowEmptyValue": false}, "sharesholdingno": {"type": "number", "example": 1000.0, "description": "Total shareholding number.<br>最大长度: 18</br>", "allowEmptyValue": false}, "stockcode": {"type": "string", "example": "00700.HK", "description": "A unique code to identify a stock. <br/> 最大长度: 10", "allowEmptyValue": false}, "stockname": {"type": "string", "example": "TENCENT", "description": "Stock name.<br>最大长度: 140</br>", "allowEmptyValue": false}}, "title": "StockInvestmentModel"}, "StockModel": {"type": "object", "properties": {"hs_tech": {"type": "string", "example": "N", "description": "恒生科技指数.<br>最大长度: 18</br>", "allowEmptyValue": false}, "hscei": {"type": "string", "example": "N", "description": "国企指数.<br>最大长度: 18</br>", "allowEmptyValue": false}, "hsi": {"type": "string", "example": "Y", "description": "恒生指数.<br>最大长度: 18</br>", "allowEmptyValue": false}, "items": {"type": "number", "example": 3.0, "description": "the number of items you'd like to get in the response result.<br/>最大长度: 4", "allowEmptyValue": false}, "shsc": {"type": "string", "example": "Y", "description": "沪港通/深港通.<br>最大长度: 18</br>", "allowEmptyValue": false}}, "title": "StockModel"}, "StockOrderInfoModel": {"type": "object", "properties": {"currencycode": {"type": "string", "example": "HKD", "description": "Currency code.<br>最大长度: 3</br>", "allowEmptyValue": false}, "custodycharges": {"type": "number", "example": 20.0, "description": "The safekeeping fee.<br>最大长度: 18</br>", "allowEmptyValue": false}, "expirydate": {"type": "number", "example": *************.0, "description": "Expiration time.<br>最大长度: 20</br>", "allowEmptyValue": false}, "id": {"type": "integer", "format": "int32", "example": 1, "description": "Unique id.<br>最大长度: 20</br>", "allowEmptyValue": false}, "lastupdatedate": {"type": "number", "example": *************.0, "description": "Last updated date.<br>最大长度: 20</br>", "allowEmptyValue": false}, "operationdate": {"type": "number", "example": *************.0, "description": "Operation date.<br>最大长度: 20</br>", "allowEmptyValue": false}, "operationreasons": {"type": "string", "description": "Operation reason.<br>最大长度: 140</br>", "allowEmptyValue": false}, "ordertype": {"type": "string", "example": "Market Price", "description": "Stock order type,Allowed value:Market Price /Fix Price<br>最大长度: 20</br>", "allowEmptyValue": false}, "requesttime": {"type": "number", "example": *************.0, "description": "Request time.<br>最大长度: 20</br>", "allowEmptyValue": false}, "settlementaccount": {"type": "string", "example": "***********************", "description": "Settlement account number.<br>最大长度: 34</br>", "allowEmptyValue": false}, "sharingno": {"type": "number", "example": 1000.0, "description": "Buy and sell shares.<br>最大长度: 18</br>", "allowEmptyValue": false}, "status": {"type": "string", "description": "Status.<br>最大长度: 4</br>", "allowEmptyValue": false}, "statuscode": {"type": "string", "example": 1, "description": "Status code.<br>最大长度: 10</br>", "allowEmptyValue": false}, "stockaccountnumber": {"type": "string", "example": "***********************", "description": "Stock account number.<br>最大长度: 34</br>", "allowEmptyValue": false}, "stockcode": {"type": "string", "example": "00700.HK", "description": "Stock code.<br>最大长度: 10</br>", "allowEmptyValue": false}, "stockname": {"type": "string", "example": "TENCENT", "description": "Stock name.<br>最大长度: 140</br>", "allowEmptyValue": false}, "tradingamount": {"type": "number", "example": 5200.0, "description": "Transaction amount.<br>最大长度: 18</br>", "allowEmptyValue": false}, "tradingcommission": {"type": "number", "example": 5.0, "description": "The trading commissions.<br>最大长度: 18</br>", "allowEmptyValue": false}, "tradingoption": {"type": "string", "example": "Buy", "description": "Trading Options (Buy/Sell)<br>最大长度: 10</br>", "allowEmptyValue": false}, "tradingprice": {"type": "number", "example": 52.0, "description": "The stock price.<br>最大长度: 18</br>", "allowEmptyValue": false}, "transactionamount": {"type": "number", "example": 100.0, "description": "Deduction/amount received.<br>最大长度: 18</br>", "allowEmptyValue": false}}, "title": "StockOrderInfoModel"}, "StockPlatFormLogModel": {"type": "object", "properties": {"accountnumber": {"type": "string", "example": "***********************", "description": "Stock account Number </br> 最大长度: 34", "allowEmptyValue": false}, "branchcode": {"type": "string", "example": "0001", "description": "Bank branch code.<br>最大长度: 20</br>", "allowEmptyValue": false}, "clearingcode": {"type": "string", "example": "0001", "description": "Bank clearing code.<br>最大长度: 20</br>", "allowEmptyValue": false}, "countrycode": {"type": "string", "example": "HK", "description": "Country code.<br>最大长度: 2</br>", "allowEmptyValue": false}, "custodycharges": {"type": "number", "example": 20.0, "description": "The safekeeping fee.<br>最大长度: 18</br>", "allowEmptyValue": false}, "id": {"type": "integer", "format": "int32", "example": 1, "description": "Unique id.<br>最大长度: 20</br>", "allowEmptyValue": false}, "sharingno": {"type": "number", "example": 1000.0, "description": "Buy and sell shares.<br>最大长度: 18</br>", "allowEmptyValue": false}, "stocknumber": {"type": "string", "example": "00112.HK", "description": "stock code <br/> 最大长度: 10", "allowEmptyValue": false}, "stocktrdingamount": {"type": "number", "example": 3000.0, "description": "Stock trading amount.<br>最大长度: 18</br>", "allowEmptyValue": false}, "stocktrdingcommission": {"type": "number", "example": 10.0, "description": "Stock transaction commission.<br>最大长度: 18</br>", "allowEmptyValue": false}, "tradingoption": {"type": "string", "example": "Buy", "description": "Trading Options (Buy/Sell).<br>最大长度: 10</br>", "allowEmptyValue": false}, "transactionamount": {"type": "number", "example": 25000.0, "description": "The transaction amount.<br>最大长度: 18</br>", "allowEmptyValue": false}, "transactiondate": {"type": "number", "example": *************.0, "description": "The transaction date.<br>最大长度: 20</br>", "allowEmptyValue": false}}, "title": "StockPlatFormLogModel"}, "StockTradingModel": {"type": "object", "required": ["orderType", "sharingNo", "stkaccountnumber", "stockCode", "tradingOption"], "properties": {"expiryDate": {"type": "number", "example": *************.0, "description": "股票交易订单过期时间，适用于orderType（交易类型）是Fix Price的情况。当orderType（交易类型）是Market Price的时候，请保持该参数为空值。<br/>最大长度: 20", "allowEmptyValue": true}, "orderType": {"type": "string", "example": "Fix Price", "description": "股票订单交易类型。支持的值：Market Price（按照市场价格交易），Fix Price（按照客户设置的固定价格交易) <br/> 最大长度: 20", "allowEmptyValue": false}, "sharingNo": {"type": "string", "example": 2000, "description": "股票份额。例如当要买入或者卖出股票的时候，需要填入卖出多少份额。股票份额必须是整手，例如100，200，是该只股票单笔交易份额的整数倍。<br/>最大长度: 8", "allowEmptyValue": false}, "stkaccountnumber": {"type": "string", "example": "***********************", "description": "股票账户号码。<br/> 最大长度: 34", "allowEmptyValue": false}, "stockCode": {"type": "string", "example": "00112.HK", "description": "股票代码。<br/> 最大长度: 10", "allowEmptyValue": false}, "tradingOption": {"type": "string", "example": "Buy", "description": "买入或者卖出。支持的值：Buy（表示买入）， Sell（表示卖出)<br/> 最大长度: 10", "allowEmptyValue": false}, "tradingPrice": {"type": "string", "example": 5.17, "description": "股票交易价格。适用于orderType（交易类型）是Fix Price的情况。当orderType（交易类型）是Market Price的时候，请保持该参数为空值。系统直接就会按照市场价格做交易了。 <br/>5 decimals, 最大长度: 13", "allowEmptyValue": true}}, "title": "StockTradingModel"}, "UpdateOrderRequestModel": {"type": "object", "required": ["id"], "properties": {"expiryDate": {"type": "number", "example": *************.0, "description": "股票交易订单过期时间，适用于orderType（交易类型）是Fix Price的情况。当orderType（交易类型）是Market Price的时候，请保持该参数为空值。<br/>最大长度: 20", "allowEmptyValue": true}, "id": {"type": "number", "example": 2.0, "description": "股票订单编号。当您调用买入或者卖出股票申请API之后，就会返回一个股票订单编号。<br>最大长度: 20</br>", "allowEmptyValue": false}, "sharingno": {"type": "string", "example": 2000, "description": "股票份额。例如当要买入或者卖出股票的时候，需要填入卖出多少份额。<br/>最大长度: 18", "allowEmptyValue": true}, "tradingprice": {"type": "string", "example": 7.2, "description": "股票交易价格。适用于orderType（交易类型）是Fix Price的情况。当orderType（交易类型）是Market Price的时候，请保持该参数为空值。系统直接就会按照市场价格做交易了。 <br/>2 decimals,最大长度: 13", "allowEmptyValue": true}}, "title": "UpdateOrderRequestModel"}, "UpdateOrderStatusModel": {"type": "object", "required": ["id", "status"], "properties": {"id": {"type": "number", "example": 2.0, "description": "股票订单编号。当您调用买入或者卖出股票申请API之后，就会返回一个股票订单编号。<br>最大长度: 20</br>", "allowEmptyValue": false}, "operationreasons": {"type": "string", "description": "模拟股票成功或者失败的时候，可以添加原因说明。例如当模拟交易失败的时候，可以添加说明”节假日不支持交易“等等。<br/> 最大长度: 140", "allowEmptyValue": true}, "status": {"type": "string", "example": 1, "description": "用于模拟模拟股票交易成功或者失败的状态参数。当Status设置为1时，表示模拟该笔股票交易成功。 当Status设置为2时，表示模拟该笔股票交易失败。<br/> 最大长度: 20", "allowEmptyValue": false}}, "title": "UpdateOrderStatusModel"}, "UpdateSettlementAccountModel": {"type": "object", "required": ["newsettleaccountnumber", "stkaccountnumber"], "properties": {"newsettleaccountnumber": {"type": "string", "example": "***********************", "description": "为该股票账户绑定的新的储蓄账户（001或者002结尾的账户类型)，用于交易中的扣款或者收款服务。<br/> 最大长度: 34", "allowEmptyValue": false}, "stkaccountnumber": {"type": "string", "example": "***********************", "description": "股票账户号码。<br/> 最大长度: 34", "allowEmptyValue": false}}, "title": "UpdateSettlementAccountModel"}}}