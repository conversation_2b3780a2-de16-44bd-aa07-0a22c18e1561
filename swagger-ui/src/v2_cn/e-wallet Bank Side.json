{"swagger": "2.0", "info": {"description": "APIs here are all about E-wallet service. Before calling these APIs, please make sure you’ve learnt the business logic between these APIs.", "version": "1.0", "title": "E-wallet Service", "contact": {"name": "<EMAIL>"}}, "schemes": ["https"], "host": "simnectz.hsu.edu.hk/lbsgateway/ewallet-experience", "basePath": "/", "tags": [{"name": "钱包账户", "description": "电子钱包账户相关的操作，包含绑定/解绑银行卡，钱包取款和用户信息查询。"}, {"name": "银行卡", "description": "钱包相关银行卡服务包括查询客户绑定的银行卡列表和查询绑定的银行余额。"}, {"name": "交易", "description": "电子钱包交易，包括商户列表，商户信息和支付。"}], "paths": {"/customer/customerID": {"post": {"tags": ["钱包账户"], "summary": "通过设置的客户ID查询用户信息", "description": "通过设置的客户ID查询用户信息", "operationId": "customerIdUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "颁发给第三方应用程序的最新客户授权令牌，以JWT格式访问该银行的客户数据。当开发者在调用API访问银行客户数据的时候，都需要用到该令牌。<br>最大长度: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "Message ID in 128 bit random UUID format generated uniquely for every request by F/E side.<br>maxLength: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "client_id generated during consumer onboarding. <br>maxLength: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "vo", "description": "vo", "required": true, "schema": {"$ref": "#/definitions/CustomerIdVo"}}], "responses": {"200": {"description": "Query completed successfully.(Returned By Get)", "schema": {"$ref": "#/definitions/EwalletCustomerMaster"}}, "201": {"description": "Normal execution. The request has succeeded. (Returned By Post)", "schema": {"$ref": "#/definitions/ResultUtil"}}, "403": {"description": "Token has incorrect scope or a security policy was violated. Action: Please check whether you’re using the right token with the legal authorized user account."}, "404": {"description": "The requested  account does not exist.Action: Please make sure the account number and account type you’re inputting are correct."}, "500": {"description": "Something went wrong on the API gateway or micro-service. Action: check your network and try again later."}}}}, "/finance/accountUnbinding": {"post": {"tags": ["钱包账户"], "summary": "通过此API可以解除已绑定的卡", "description": "通过此API可以解除已绑定的卡", "operationId": "accountUnbindingUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "颁发给第三方应用程序的最新客户授权令牌，以JWT格式访问该银行的客户数据。当开发者在调用API访问银行客户数据的时候，都需要用到该令牌。<br>最大长度: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "Message ID in 128 bit random UUID format generated uniquely for every request by F/E side.<br>maxLength: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "client_id generated during consumer onboarding. <br>maxLength: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "validationModel", "description": "validationModel", "required": true, "schema": {"$ref": "#/definitions/ValidationModel"}}], "responses": {"200": {"description": "Query completed successfully.(Returned By Get)", "schema": {"$ref": "#/definitions/ResultUtil"}}, "201": {"description": "Normal execution. The request has succeeded. (Returned By Post)", "schema": {"$ref": "#/definitions/ResultUtil"}}, "403": {"description": "Token has incorrect scope or a security policy was violated. Action: Please check whether you’re using the right token with the legal authorized user account."}, "404": {"description": "The requested  account does not exist.Action: Please make sure the account number and account type you’re inputting are correct."}, "500": {"description": "Something went wrong on the API gateway or micro-service. Action: check your network and try again later."}}}}, "/finance/accountValidation": {"post": {"tags": ["钱包账户"], "summary": "此API用于进行账户验证并将账户绑定到电子钱包系统。", "description": "此 API 仅支持储蓄/活期账户和信用卡账户。在进行电子钱包充值之前，您可能需要调用此 API。", "operationId": "accountValidationUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "颁发给第三方应用程序的最新客户授权令牌，以JWT格式访问该银行的客户数据。当开发者在调用API访问银行客户数据的时候，都需要用到该令牌。<br>最大长度: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "Message ID in 128 bit random UUID format generated uniquely for every request by F/E side.<br>maxLength: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "client_id generated during consumer onboarding. <br>maxLength: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "validationModel", "description": "validationModel", "required": true, "schema": {"$ref": "#/definitions/ValidationModel"}}], "responses": {"200": {"description": "Query completed successfully.(Returned By Get)", "schema": {"$ref": "#/definitions/ResultUtil"}}, "201": {"description": "Normal execution. The request has succeeded. (Returned By Post)", "schema": {"$ref": "#/definitions/ResultUtil"}}, "403": {"description": "Token has incorrect scope or a security policy was violated. Action: Please check whether you’re using the right token with the legal authorized user account."}, "404": {"description": "The requested  account does not exist.Action: Please make sure the account number and account type you’re inputting are correct."}, "500": {"description": "Something went wrong on the API gateway or micro-service. Action: check your network and try again later."}}}}, "/finance/accounts/customerNumber": {"post": {"tags": ["银行卡"], "summary": "查询指定用户的绑定卡信息", "description": "查询指定用户的绑定卡信息", "operationId": "customerNumberUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "颁发给第三方应用程序的最新客户授权令牌，以JWT格式访问该银行的客户数据。当开发者在调用API访问银行客户数据的时候，都需要用到该令牌。<br>最大长度: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "Message ID in 128 bit random UUID format generated uniquely for every request by F/E side.<br>maxLength: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "client_id generated during consumer onboarding. <br>maxLength: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "vo", "description": "vo", "required": true, "schema": {"$ref": "#/definitions/CustomerNumberVo"}}], "responses": {"200": {"description": "Query completed successfully.(Returned By Get)", "schema": {"$ref": "#/definitions/BindCardInfoVo"}}, "201": {"description": "Normal execution. The request has succeeded. (Returned By Post)", "schema": {"$ref": "#/definitions/ResultUtil"}}, "403": {"description": "Token has incorrect scope or a security policy was violated. Action: Please check whether you’re using the right token with the legal authorized user account."}, "404": {"description": "The requested  account does not exist.Action: Please make sure the account number and account type you’re inputting are correct."}, "500": {"description": "Something went wrong on the API gateway or micro-service. Action: check your network and try again later."}}}}, "/finance/authorize": {"post": {"tags": ["钱包账户"], "summary": "第三方授权", "description": "授权第三方电子钱包应用", "operationId": "authorizeUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "颁发给第三方应用程序的最新客户授权令牌，以JWT格式访问该银行的客户数据。当开发者在调用API访问银行客户数据的时候，都需要用到该令牌。<br>最大长度: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "Message ID in 128 bit random UUID format generated uniquely for every request by F/E side.<br>maxLength: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "client_id generated during consumer onboarding. <br>maxLength: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "eServiceAuthorizeModel", "description": "eServiceAuthorizeModel", "required": true, "schema": {"$ref": "#/definitions/EServiceAuthorizeModel"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ResultUtil«object»"}}}}}, "/finance/bankCardBalance": {"post": {"tags": ["银行卡"], "summary": "卡余额查询", "operationId": "cardBalanceEnquiryUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "颁发给第三方应用程序的最新客户授权令牌，以JWT格式访问该银行的客户数据。当开发者在调用API访问银行客户数据的时候，都需要用到该令牌。<br>最大长度: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "Message ID in 128 bit random UUID format generated uniquely for every request by F/E side.<br>maxLength: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "client_id generated during consumer onboarding. <br>maxLength: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "cardBalanceEnquiry", "description": "cardBalanceEnquiry", "required": true, "schema": {"$ref": "#/definitions/CardBalanceEnquiryModel"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ResultUtil«object»"}}}}}, "/finance/cashing": {"post": {"tags": ["钱包账户"], "summary": "通过此API，你可以提现指定金额到卡上", "description": "Through this API, you can withdraw the specified amount to the card", "operationId": "cashingUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "颁发给第三方应用程序的最新客户授权令牌，以JWT格式访问该银行的客户数据。当开发者在调用API访问银行客户数据的时候，都需要用到该令牌。<br>最大长度: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "Message ID in 128 bit random UUID format generated uniquely for every request by F/E side.<br>maxLength: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "client_id generated during consumer onboarding. <br>maxLength: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "vo", "description": "vo", "required": true, "schema": {"$ref": "#/definitions/CashingVo"}}], "responses": {"200": {"description": "Query completed successfully.(Returned By Get)", "schema": {"$ref": "#/definitions/ResultUtil«string»"}}, "201": {"description": "Normal execution. The request has succeeded. (Returned By Post)", "schema": {"$ref": "#/definitions/ResultUtil«string»"}}, "403": {"description": "Token has incorrect scope or a security policy was violated. Action: Please check whether you’re using the right token with the legal authorized user account."}, "404": {"description": "The requested  account does not exist.Action: Please make sure the account number and account type you’re inputting are correct."}, "500": {"description": "Something went wrong on the API gateway or micro-service. Action: check your network and try again later."}}}}, "/finance/merchantDetails": {"post": {"tags": ["交易"], "summary": "商户详情查询", "operationId": "merchantDetailsEnquiryUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "颁发给第三方应用程序的最新客户授权令牌，以JWT格式访问该银行的客户数据。当开发者在调用API访问银行客户数据的时候，都需要用到该令牌。<br>最大长度: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "Message ID in 128 bit random UUID format generated uniquely for every request by F/E side.<br>maxLength: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "client_id generated during consumer onboarding. <br>maxLength: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "merchantDetailsEnquiry", "description": "merchantDetailsEnquiry", "required": true, "schema": {"$ref": "#/definitions/MerchantDetailsEnquiryModel"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ResultUtil«object»"}}}}}, "/finance/merchantQR": {"post": {"tags": ["交易"], "summary": "此API用于生成带有商户详细信息的二维码。", "description": "在使用电子钱包付款之前，您可能需要调用此 API 来检索商家信息。", "operationId": "merchantDetailsUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "颁发给第三方应用程序的最新客户授权令牌，以JWT格式访问该银行的客户数据。当开发者在调用API访问银行客户数据的时候，都需要用到该令牌。<br>最大长度: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "Message ID in 128 bit random UUID format generated uniquely for every request by F/E side.<br>maxLength: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "client_id generated during consumer onboarding. <br>maxLength: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "merchantVo", "description": "merchantVo", "required": true, "schema": {"$ref": "#/definitions/MerchantInfoVo"}}], "responses": {"200": {"description": "Query completed successfully.(Returned By Get)", "schema": {"$ref": "#/definitions/ResultUtil"}}, "201": {"description": "Normal execution. The request has succeeded. (Returned By Post)", "schema": {"$ref": "#/definitions/ResultUtil"}}, "403": {"description": "Token has incorrect scope or a security policy was violated. Action: Please check whether you’re using the right token with the legal authorized user account."}, "404": {"description": "The requested  account does not exist.Action: Please make sure the account number and account type you’re inputting are correct."}, "500": {"description": "Something went wrong on the API gateway or micro-service. Action: check your network and try again later."}}}}, "/finance/merchants": {"post": {"tags": ["交易"], "summary": "获取所有商家的详细信息。", "description": "获取所有商家的详细信息。", "operationId": "merchantsUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "颁发给第三方应用程序的最新客户授权令牌，以JWT格式访问该银行的客户数据。当开发者在调用API访问银行客户数据的时候，都需要用到该令牌。<br>最大长度: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "Message ID in 128 bit random UUID format generated uniquely for every request by F/E side.<br>maxLength: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "client_id generated during consumer onboarding. <br>maxLength: 50</br>", "required": true, "type": "string", "default": "devin"}], "responses": {"200": {"description": "Query completed successfully.(Returned By Get)", "schema": {"$ref": "#/definitions/ResultUtil"}}, "201": {"description": "Normal execution. The request has succeeded. (Returned By Post)", "schema": {"$ref": "#/definitions/ResultUtil"}}, "403": {"description": "Token has incorrect scope or a security policy was violated. Action: Please check whether you’re using the right token with the legal authorized user account."}, "404": {"description": "The requested  account does not exist.Action: Please make sure the account number and account type you’re inputting are correct."}, "500": {"description": "Something went wrong on the API gateway or micro-service. Action: check your network and try again later."}}}}, "/finance/topUp": {"post": {"tags": ["钱包账户"], "summary": "此API用于给电子钱包充值。", "description": "当您的银行账户验证成功后，您可能需要调用此API为您的电子钱包充值，以确保您的电子钱包有足够的资金进行付款。", "operationId": "toUpUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "颁发给第三方应用程序的最新客户授权令牌，以JWT格式访问该银行的客户数据。当开发者在调用API访问银行客户数据的时候，都需要用到该令牌。<br>最大长度: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "Message ID in 128 bit random UUID format generated uniquely for every request by F/E side.<br>maxLength: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "client_id generated during consumer onboarding. <br>maxLength: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "toUpModel", "description": "toUpModel", "required": true, "schema": {"$ref": "#/definitions/ToUpModel"}}], "responses": {"200": {"description": "Query completed successfully.(Returned By Get)", "schema": {"$ref": "#/definitions/ResultUtil"}}, "201": {"description": "Normal execution. The request has succeeded. (Returned By Post)", "schema": {"$ref": "#/definitions/ResultUtil"}}, "403": {"description": "Token has incorrect scope or a security policy was violated. Action: Please check whether you’re using the right token with the legal authorized user account."}, "404": {"description": "The requested  account does not exist.Action: Please make sure the account number and account type you’re inputting are correct."}, "500": {"description": "Something went wrong on the API gateway or micro-service. Action: check your network and try again later."}}}}, "/finance/transferToMerchant": {"post": {"tags": ["交易"], "summary": "此API用于向商家付款。", "description": "调用此API之前，您可能需要调用其他API以确保您的电子钱包有足够的钱，并扫描商户的二维码以在银行系统中检索商户账户。", "operationId": "transferToMerchantUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "颁发给第三方应用程序的最新客户授权令牌，以JWT格式访问该银行的客户数据。当开发者在调用API访问银行客户数据的时候，都需要用到该令牌。<br>最大长度: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "Message ID in 128 bit random UUID format generated uniquely for every request by F/E side.<br>maxLength: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "client_id generated during consumer onboarding. <br>maxLength: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "merchantModel", "description": "merchantModel", "required": true, "schema": {"$ref": "#/definitions/MerchantModel"}}], "responses": {"200": {"description": "Query completed successfully.(Returned By Get)", "schema": {"$ref": "#/definitions/ResultUtil"}}, "201": {"description": "Normal execution. The request has succeeded. (Returned By Post)", "schema": {"$ref": "#/definitions/ResultUtil"}}, "403": {"description": "Token has incorrect scope or a security policy was violated. Action: Please check whether you’re using the right token with the legal authorized user account."}, "404": {"description": "The requested  account does not exist.Action: Please make sure the account number and account type you’re inputting are correct."}, "500": {"description": "Something went wrong on the API gateway or micro-service. Action: check your network and try again later."}}}}}, "definitions": {"AccountNumberVo": {"type": "object", "properties": {"accountNumber": {"type": "string", "example": "HK720001************001", "description": "客户的银行账户。支持001结尾的储蓄账户；002结尾的活期账户和信用卡账户。<br></br> maxLength: 34", "allowEmptyValue": false}}, "title": "AccountNumberVo"}, "BindCardInfoVo": {"type": "object", "properties": {"creditCard": {"type": "array", "items": {"$ref": "#/definitions/AccountNumberVo"}}, "current": {"type": "array", "items": {"$ref": "#/definitions/AccountNumberVo"}}, "saving": {"type": "array", "items": {"$ref": "#/definitions/AccountNumberVo"}}}, "title": "BindCardInfoVo"}, "CardBalanceEnquiryModel": {"type": "object", "required": ["accountNumber", "walletId"], "properties": {"accountNumber": {"type": "string", "example": "HK720001************001", "description": "客户的银行账户。支持001结尾的储蓄账户；002结尾的活期账户和信用卡账户。<br></br> maxLength: 34", "allowEmptyValue": false}, "walletId": {"type": "string", "example": "************************", "description": "当前银行客户的电子钱包编号，该编号由该用户在电子钱包系统注册时产生。<br></br> maxLength: 64", "allowEmptyValue": false}}, "title": "CardBalanceEnquiryModel"}, "CashingVo": {"type": "object", "required": ["amount", "ccy", "walletId"], "properties": {"accountNumber": {"type": "string", "example": "HK720001************001", "description": "客户的银行账户。支持001结尾的储蓄账户；002结尾的活期账户和信用卡账户。<br></br> maxLength: 34", "allowEmptyValue": false}, "amount": {"type": "number", "example": 100, "description": "交易金额。<br>maxLength: 18</br>", "allowEmptyValue": false}, "ccy": {"type": "string", "example": "HKD", "description": "交易货币类型。<br></br> maxLength: 3", "allowEmptyValue": false}, "walletId": {"type": "string", "example": "************************", "description": "当前银行客户的电子钱包编号，该编号由该用户在电子钱包系统注册时产生。<br></br> maxLength: 64", "allowEmptyValue": false}}, "title": "CashingVo"}, "CustomerIdVo": {"type": "object", "required": ["customerId"], "properties": {"customerId": {"type": "string", "example": "U735535(9)", "description": "用户身份证号码。<br></br> maxLength: 25", "allowEmptyValue": false}}, "title": "CustomerIdVo"}, "CustomerNumberVo": {"type": "object", "required": ["customerNumber", "walletId"], "properties": {"customerNumber": {"type": "string", "example": "************", "description": "银行客户编号。当银行客户创建成功后，系统会为该客户生成唯一的一个编号。<br></br> maxLength: 25", "allowEmptyValue": false}, "walletId": {"type": "string", "example": "************************", "description": "当前银行客户的电子钱包编号，该编号由该用户在电子钱包系统注册时产生。<br></br> maxLength: 64", "allowEmptyValue": false}}, "title": "CustomerNumberVo"}, "EServiceAuthorizeModel": {"type": "object", "required": ["customerId", "eWalletCompany", "walletId"], "properties": {"customerId": {"type": "string", "example": "U735535(9)", "description": "用户身份证号码。<br></br> maxLength: 25", "allowEmptyValue": false}, "eWalletCompany": {"type": "string", "example": "e-Wallet", "description": "第三方电子钱包企业。<br></br> maxLength: 25", "allowEmptyValue": false}, "walletId": {"type": "string", "example": "************************", "description": "当前银行客户的电子钱包编号，该编号由该用户在电子钱包系统注册时产生。<br></br> maxLength: 64", "allowEmptyValue": false}}, "title": "EServiceAuthorizeModel"}, "EwalletCustomerMaster": {"type": "object", "properties": {"chineseName": {"type": "string"}, "createTime": {"type": "number"}, "customerId": {"type": "string"}, "customerNumber": {"type": "string"}, "firstName": {"type": "string"}, "id": {"type": "string"}, "issueCountry": {"type": "string"}, "lastName": {"type": "string"}, "phone": {"type": "string"}}, "title": "EwalletCustomerMaster"}, "MerchantDetailsEnquiryModel": {"type": "object", "required": ["merchantNumber"], "properties": {"merchantNumber": {"type": "string", "example": "******************", "description": "系统为入驻的企业产生的编号，用以标注该商户在系统中的唯一性。<br></br> MaxLength:34", "allowEmptyValue": false}}, "title": "MerchantDetailsEnquiryModel"}, "MerchantInfoVo": {"type": "object", "required": ["merchantNumber"], "properties": {"merchantNumber": {"type": "string", "example": "******************", "description": "系统为入驻的企业产生的编号，用以标注该商户在系统中的唯一性。<br></br> MaxLength:34", "allowEmptyValue": false}}, "title": "MerchantInfoVo"}, "MerchantModel": {"type": "object", "required": ["accountNumber", "accountType", "merchantNumber", "transactionAmount", "transactionCcy", "walletId"], "properties": {"accountNumber": {"type": "string", "example": "HK720001************001", "description": "客户的银行账户。支持001结尾的储蓄账户；002结尾的活期账户和信用卡账户。<br></br> maxLength: 34", "allowEmptyValue": false}, "accountType": {"type": "string", "example": "SAVI", "description": "客户输入的银行账户的类型。<br></br>SAVI - 001结尾的储蓄账户<br>CURR - 002结尾的活期账户<br>CRED - 信用卡账户。</br>maxLength: 4", "allowEmptyValue": false}, "merchantNumber": {"type": "string", "example": "******************", "description": "系统为入驻的企业产生的编号，用以标注该商户在系统中的唯一性。<br></br> maxLength: 34", "allowEmptyValue": false}, "transactionAmount": {"type": "string", "example": 100, "description": "支付金额。<br></br> maxLength: 18", "allowEmptyValue": false}, "transactionCcy": {"type": "string", "example": "HKD", "description": "交易货币类型。<br></br> maxLength: 3", "allowEmptyValue": false}, "walletId": {"type": "string", "example": "************************", "description": "当前银行客户的电子钱包编号，该编号由该用户在电子钱包系统注册时产生。<br></br> maxLength: 64", "allowEmptyValue": false}}, "title": "MerchantModel"}, "ResultUtil": {"type": "object", "properties": {"code": {"type": "string"}, "data": {"type": "object"}, "msg": {"type": "string"}}, "title": "ResultUtil"}, "ResultUtil«object»": {"type": "object", "properties": {"code": {"type": "string"}, "data": {"type": "object"}, "msg": {"type": "string"}}, "title": "ResultUtil«object»"}, "ResultUtil«string»": {"type": "object", "properties": {"code": {"type": "string"}, "data": {"type": "string"}, "msg": {"type": "string"}}, "title": "ResultUtil«string»"}, "ToUpModel": {"type": "object", "required": ["accountNumber", "amount", "ccy", "walletID"], "properties": {"accountNumber": {"type": "string", "example": "HK720001************001", "description": "客户的银行账户。支持001结尾的储蓄账户；002结尾的活期账户和信用卡账户。<br></br> maxLength: 34", "allowEmptyValue": false}, "amount": {"type": "string", "example": 100, "description": "交易金额。<br></br> maxLength: 18", "allowEmptyValue": false}, "ccy": {"type": "string", "example": "HKD", "description": "交易货币类型。<br></br> maxLength: 3", "allowEmptyValue": false}, "walletID": {"type": "string", "example": "************************", "description": "当前银行客户的电子钱包编号，该编号由该用户在电子钱包系统注册时产生。<br></br> maxLength: 64", "allowEmptyValue": false}}, "title": "ToUpModel"}, "ValidationModel": {"type": "object", "required": ["accountNumber", "customerNumber", "walletId"], "properties": {"accountNumber": {"type": "string", "example": "HK720001************001", "description": "客户的银行账户。支持001结尾的储蓄账户；002结尾的活期账户和信用卡账户。<br></br> maxLength: 34", "allowEmptyValue": false}, "customerNumber": {"type": "string", "example": "************", "description": "银行客户编号。当银行客户创建成功后，系统会为该客户生成唯一的一个编号。<br></br> maxLength: 25", "allowEmptyValue": false}, "walletId": {"type": "string", "example": "************************", "description": "当前银行客户的电子钱包编号，该编号由该用户在电子钱包系统注册时产生。<br></br> maxLength: 64", "allowEmptyValue": false}}, "title": "ValidationModel"}}}