{
  "swagger": "2.0",
  "info": {
    "description": "該服務模組主要包含了所有與房貸業務相關的API。例如房貸申請，房貸還款，房貸還款計劃詳情等。",
    "version": "1.0",
    "title": "房貸服務",
    "contact": {
      "name": "admin:<EMAIL>"
    }
  },
  "schemes": ["https"],
  "host": "simnectz.hsu.edu.hk/lbsgateway/loan-experience",
  "basePath": "/",
  "tags": [
    {
      "name": "Loan Application",
      "description": "房貸申請，產品和報價。"
    },
    {
      "name": "Account Information",
      "description": "房貸帳號資訊。"
    },
    {
      "name": "Loan Contract",
      "description": "房貸合約資訊。"
    },
    {
      "name": "Transaction",
      "description": "房貸處理，包括查詢和還款。"
    }
  ],
  "paths": {
    "/mortgage/accountDetailEnquiry": {
      "post": {
        "tags": [
          "Account Information"
        ],
        "summary": "此API用於 查詢房貸帳戶的詳細內容。",
        "description": "例如帳戶的持有人基礎訊息，及帳戶狀態，綁定的還款帳戶等資訊。",
        "operationId": "accountDetailEnquiryUsingPOST",
        "consumes": [
          "application/json"
        ],
        "produces": [
          "*/*"
        ],
        "parameters": [
          {
            "name": "token",
            "in": "header",
            "description": "頒發給第三方應用程式的最新客戶授權令牌，以JWT格式存取該銀行的客戶資料。當開發者在調用API存取銀行客戶資料的時候，都需要用到該令牌。< br>最大長度: 1000</br>",
            "required": true,
            "type": "string",
            "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"
          },
          {
            "name": "messageid",
            "in": "header",
            "description": "為前後端的每個請求唯一產生的128位元隨機UUID格式的訊息ID。此參數為內部參數，在呼叫API的時候不用修改該參數的內容。直接使用即可。<br>最大長度: 50</br>",
            "required": true,
            "type": "string",
            "default": "006f7113e5fa48559549c4dfe74e2cd6"
          },
          {
            "name": "clientid",
            "in": "header",
            "description": "當使用者登入的時候產生的客戶id。此參數為內部參數，在呼叫API的時候不用修改該參數的內容。直接使用即可。<br>最大長度: 50</br>",
            "required": true,
            "type": "string",
            "default": "devin"
          },
          {
            "in": "body",
            "name": "ase",
            "description": "ase",
            "required": true,
            "schema": {
              "$ref": "#/definitions/MortgageAccountNumberModel"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "查詢成功。（由Get方法傳回）",
            "schema": {
              "$ref": "#/definitions/ResultUtil"
            }
          },
          "201": {
            "description": "正常執行，請求成功。（由Post方法傳回）",
            "schema": {
              "$ref": "#/definitions/ResultUtil"
            }
          },
          "403": {
            "description": "令牌的作用範圍不正確或違反了安全性原則。操作：請檢查您是否使用合法授權使用者帳號的正確令牌。"
          },
          "404": {
            "description": "要求的帳戶不存在。操作：請確保您輸入的帳號和帳戶類型正確。"
          },
          "500": {
            "description": "API網關或微服務出現問題。操作：檢查您的網絡，稍後再試。"
          }
        }
      }
    },
    "/mortgage/allContractsRetrieval": {
      "post": {
        "tags": [
          "Loan Contract"
        ],
        "summary": "此API用於查詢某個銀行客戶的房貸帳戶下面的所有房貸記錄。",
        "description": "例如，客戶的某個房貸帳戶中有3筆房貸，那麼就可以透過這個API來查詢到3筆貸款記錄，並且可以看到每筆貸款的詳情內容，如貸款總金額，貸款利率，貸款總利息等情形。",
        "operationId": "retrieveContractsUsingPOST",
        "consumes": [
          "application/json"
        ],
        "produces": [
          "*/*"
        ],
        "parameters": [
          {
            "name": "token",
            "in": "header",
            "description": "頒發給第三方應用程式的最新客戶授權令牌，以JWT格式存取該銀行的客戶資料。當開發者在調用API存取銀行客戶資料的時候，都需要用到該令牌。< br>最大長度: 1000</br>",
            "required": true,
            "type": "string",
            "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"
          },
          {
            "name": "messageid",
            "in": "header",
            "description": "為前後端的每個請求唯一產生的128位元隨機UUID格式的訊息ID。此參數為內部參數，在呼叫API的時候不用修改該參數的內容。直接使用即可。<br>最大長度: 50</br>",
            "required": true,
            "type": "string",
            "default": "006f7113e5fa48559549c4dfe74e2cd6"
          },
          {
            "name": "clientid",
            "in": "header",
            "description": "當使用者登入的時候產生的客戶id。此參數為內部參數，在呼叫API的時候不用修改該參數的內容。直接使用即可。<br>最大長度: 50</br>",
            "required": true,
            "type": "string",
            "default": "devin"
          },
          {
            "in": "body",
            "name": "mortgageLoanContractPreModel",
            "description": "mortgageLoanContractPreModel",
            "required": true,
            "schema": {
              "$ref": "#/definitions/MortgageLoanContractModel"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "schema": {
              "$ref": "#/definitions/ResultUtil"
            }
          }
        }
      }
    },
    "/mortgage/contractRetrieval": {
      "post": {
        "tags": [
          "Loan Contract"
        ],
        "summary": "該API用於查詢某筆銀行客戶的某筆貸款的詳情內容。",
        "description": "例如，客戶有2筆房貸記錄，調用該API可以輸入某一筆房貸合約編號，完成該筆貸款的詳情查詢。",
        "operationId": "retrieveContractUsingPOST",
        "consumes": [
          "application/json"
        ],
        "produces": [
          "*/*"
        ],
        "parameters": [
          {
            "name": "token",
            "in": "header",
            "description": "頒發給第三方應用程式的最新客戶授權令牌，以JWT格式存取該銀行的客戶資料。當開發者在調用API存取銀行客戶資料的時候，都需要用到該令牌。< br>最大長度: 1000</br>",
            "required": true,
            "type": "string",
            "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"
          },
          {
            "name": "messageid",
            "in": "header",
            "description": "為前後端的每個請求唯一產生的128位元隨機UUID格式的訊息ID。此參數為內部參數，在呼叫API的時候不用修改該參數的內容。直接使用即可。<br>最大長度: 50</br>",
            "required": true,
            "type": "string",
            "default": "006f7113e5fa48559549c4dfe74e2cd6"
          },
          {
            "name": "clientid",
            "in": "header",
            "description": "當使用者登入的時候產生的客戶id。此參數為內部參數，在呼叫API的時候不用修改該參數的內容。直接使用即可。<br>最大長度: 50</br>",
            "required": true,
            "type": "string",
            "default": "devin"
          },
          {
            "in": "body",
            "name": "loanContractReqModel",
            "description": "loanContractReqModel",
            "required": true,
            "schema": {
              "$ref": "#/definitions/LoanContractReqModel"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "OK",
            "schema": {
              "$ref": "#/definitions/ResultUtil"
            }
          }
        }
      }
    },
    "/mortgage/loanCalculator": {
      "post": {
        "tags": [
          "Loan Application"
        ],
        "summary": "此API用於房貸計算器開發。",
        "description": "客戶在貸款之前，可以做價格試算。看看根據自己的薪資能獲得多少貸款，以及還款金額利息等如何計算。",
        "operationId": "loanCalculaterUsingPOST",
        "consumes": [
          "application/json"
        ],
        "produces": [
          "*/*"
        ],
        "parameters": [
          {
            "name": "token",
            "in": "header",
            "description": "頒發給第三方應用程式的最新客戶授權令牌，以JWT格式存取該銀行的客戶資料。當開發者在調用API存取銀行客戶資料的時候，都需要用到該令牌。< br>最大長度: 1000</br>",
            "required": true,
            "type": "string",
            "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"
          },
          {
            "name": "messageid",
            "in": "header",
            "description": "為前後端的每個請求唯一產生的128位元隨機UUID格式的訊息ID。此參數為內部參數，在呼叫API的時候不用修改該參數的內容。直接使用即可。<br>最大長度: 50</br>",
            "required": true,
            "type": "string",
            "default": "006f7113e5fa48559549c4dfe74e2cd6"
          },
          {
            "name": "clientid",
            "in": "header",
            "description": "當使用者登入的時候產生的客戶id。此參數為內部參數，在呼叫API的時候不用修改該參數的內容。直接使用即可。<br>最大長度: 50</br>",
            "required": true,
            "type": "string",
            "default": "devin"
          },
          {
            "in": "body",
            "name": "ase",
            "description": "ase",
            "required": true,
            "schema": {
              "$ref": "#/definitions/MortgageCalculatorModel"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "查詢成功。（由Get方法傳回）",
            "schema": {
              "$ref": "#/definitions/ResultUtil"
            }
          },
          "201": {
            "description": "正常執行，請求成功。（由Post方法傳回）",
            "schema": {
              "$ref": "#/definitions/ResultUtil"
            }
          },
          "403": {
            "description": "令牌的作用範圍不正確或違反了安全性原則。操作：請檢查您是否使用合法授權使用者帳號的正確令牌。"
          },
          "404": {
            "description": "要求的帳戶不存在。操作：請確保您輸入的帳號和帳戶類型正確。"
          },
          "500": {
            "description": "API網關或微服務出現問題。操作：檢查您的網絡，稍後再試。"
          }
        }
      }
    },
    "/mortgage/mortgageLoanApplication": {
      "post": {
        "tags": [
          "Loan Application"
        ],
        "summary": "該API用於房貸的申請業務。",
        "description": "當用戶想要申請貸款的時候，就可以調用該API來完成。在貸款之前，可能需要先調用/mortgage/loanCalculator 去在房貸計算器上面做個貸款金額及還款利息等的試算。",
        "operationId": "mortgageLoanApplicationUsingPOST",
        "consumes": [
          "application/json"
        ],
        "produces": [
          "*/*"
        ],
        "parameters": [
          {
            "name": "token",
            "in": "header",
            "description": "頒發給第三方應用程式的最新客戶授權令牌，以JWT格式存取該銀行的客戶資料。當開發者在調用API存取銀行客戶資料的時候，都需要用到該令牌。< br>最大長度: 1000</br>",
            "required": true,
            "type": "string",
            "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"
          },
          {
            "name": "messageid",
            "in": "header",
            "description": "為前後端的每個請求唯一產生的128位元隨機UUID格式的訊息ID。此參數為內部參數，在呼叫API的時候不用修改該參數的內容。直接使用即可。<br>最大長度: 50</br>",
            "required": true,
            "type": "string",
            "default": "006f7113e5fa48559549c4dfe74e2cd6"
          },
          {
            "name": "clientid",
            "in": "header",
            "description": "當使用者登入的時候產生的客戶id。此參數為內部參數，在呼叫API的時候不用修改該參數的內容。直接使用即可。<br>最大長度: 50</br>",
            "required": true,
            "type": "string",
            "default": "devin"
          },
          {
            "in": "body",
            "name": "ase",
            "description": "ase",
            "required": true,
            "schema": {
              "$ref": "#/definitions/MortgageLoanApplicationModel"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "查詢成功。（由Get方法傳回）",
            "schema": {
              "$ref": "#/definitions/ResultUtil"
            }
          },
          "201": {
            "description": "正常執行，請求成功。（由Post方法傳回）",
            "schema": {
              "$ref": "#/definitions/ResultUtil"
            }
          },
          "403": {
            "description": "令牌的作用範圍不正確或違反了安全性原則。操作：請檢查您是否使用合法授權使用者帳號的正確令牌。"
          },
          "404": {
            "description": "要求的帳戶不存在。操作：請確保您輸入的帳號和帳戶類型正確。"
          },
          "500": {
            "description": "API網關或微服務出現問題。操作：檢查您的網絡，稍後再試。"
          }
        }
      }
    },
    "/mortgage/nextRepaymentEnquiry": {
      "post": {
        "tags": [
          "Transaction"
        ],
        "summary": "此API用於取得下期還款的詳情。",
        "description": "當客戶在每次還款之後，都可以預覽下期還款的具體情況。",
        "operationId": "nextRepaymentEnquiryUsingPOST",
        "consumes": [
          "application/json"
        ],
        "produces": [
          "*/*"
        ],
        "parameters": [
          {
            "name": "token",
            "in": "header",
            "description": "頒發給第三方應用程式的最新客戶授權令牌，以JWT格式存取該銀行的客戶資料。當開發者在調用API存取銀行客戶資料的時候，都需要用到該令牌。< br>最大長度: 1000</br>",
            "required": true,
            "type": "string",
            "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"
          },
          {
            "name": "messageid",
            "in": "header",
            "description": "為前後端的每個請求唯一產生的128位元隨機UUID格式的訊息ID。此參數為內部參數，在呼叫API的時候不用修改該參數的內容。直接使用即可。<br>最大長度: 50</br>",
            "required": true,
            "type": "string",
            "default": "006f7113e5fa48559549c4dfe74e2cd6"
          },
          {
            "name": "clientid",
            "in": "header",
            "description": "當使用者登入的時候產生的客戶id。此參數為內部參數，在呼叫API的時候不用修改該參數的內容。直接使用即可。<br>最大長度: 50</br>",
            "required": true,
            "type": "string",
            "default": "devin"
          },
          {
            "in": "body",
            "name": "ase",
            "description": "ase",
            "required": true,
            "schema": {
              "$ref": "#/definitions/AccountContractNumberModel"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "查詢成功。（由Get方法傳回）",
            "schema": {
              "$ref": "#/definitions/ResultUtil"
            }
          },
          "201": {
            "description": "正常執行，請求成功。（由Post方法傳回）",
            "schema": {
              "$ref": "#/definitions/ResultUtil"
            }
          },
          "403": {
            "description": "令牌的作用範圍不正確或違反了安全性原則。操作：請檢查您是否使用合法授權使用者帳號的正確令牌。"
          },
          "404": {
            "description": "要求的帳戶不存在。操作：請確保您輸入的帳號和帳戶類型正確。"
          },
          "500": {
            "description": "API網關或微服務出現問題。操作：檢查您的網絡，稍後再試。"
          }
        }
      }
    },
    "/mortgage/overDueRepaymentEnquiry": {
      "post": {
        "tags": [
          "Transaction"
        ],
        "summary": "此API用於查詢貸款使用者的所有逾期房貸記錄。",
        "description": "透過輸入客戶的貸款帳戶號碼和貸款合約編號，就能取得到該筆貸款中，所有的逾期記錄。 ",
        "operationId": "overDueRepaymentEnquiryUsingPOST",
        "consumes": [
          "application/json"
        ],
        "produces": [
          "*/*"
        ],
        "parameters": [
          {
            "name": "token",
            "in": "header",
            "description": "頒發給第三方應用程式的最新客戶授權令牌，以JWT格式存取該銀行的客戶資料。當開發者在調用API存取銀行客戶資料的時候，都需要用到該令牌。< br>最大長度: 1000</br>",
            "required": true,
            "type": "string",
            "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"
          },
          {
            "name": "messageid",
            "in": "header",
            "description": "為前後端的每個請求唯一產生的128位元隨機UUID格式的訊息ID。此參數為內部參數，在呼叫API的時候不用修改該參數的內容。直接使用即可。<br>最大長度: 50</br>",
            "required": true,
            "type": "string",
            "default": "006f7113e5fa48559549c4dfe74e2cd6"
          },
          {
            "name": "clientid",
            "in": "header",
            "description": "當使用者登入的時候產生的客戶id。此參數為內部參數，在呼叫API的時候不用修改該參數的內容。直接使用即可。<br>最大長度: 50</br>",
            "required": true,
            "type": "string",
            "default": "devin"
          },
          {
            "in": "body",
            "name": "ase",
            "description": "ase",
            "required": true,
            "schema": {
              "$ref": "#/definitions/AccountContractNumberModel"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "查詢成功。（由Get方法傳回）",
            "schema": {
              "$ref": "#/definitions/ResultUtil"
            }
          },
          "201": {
            "description": "正常執行，請求成功。（由Post方法傳回）",
            "schema": {
              "$ref": "#/definitions/ResultUtil"
            }
          },
          "403": {
            "description": "令牌的作用範圍不正確或違反了安全性原則。操作：請檢查您是否使用合法授權使用者帳號的正確令牌。"
          },
          "404": {
            "description": "要求的帳戶不存在。操作：請確保您輸入的帳號和帳戶類型正確。"
          },
          "500": {
            "description": "API網關或微服務出現問題。操作：檢查您的網絡，稍後再試。"
          }
        }
      }
    },
    "/mortgage/repayment": {
      "post": {
        "tags": [
          "Transaction"
        ],
        "summary": "此API用於房貸還款。",
        "description": "當客戶申請房貸之後，需要按期還款，就可以調用該API來完成該功能的開發。 在還款之前，可以查看自己需要還的具體金額，可以調用/mortgage/nextRepaymentEnquiry。",
        "operationId": "repaymentUsingPOST",
        "consumes": [
          "application/json"
        ],
        "produces": [
          "*/*"
        ],
        "parameters": [
          {
            "name": "token",
            "in": "header",
            "description": "頒發給第三方應用程式的最新客戶授權令牌，以JWT格式存取該銀行的客戶資料。當開發者在調用API存取銀行客戶資料的時候，都需要用到該令牌。< br>最大長度: 1000</br>",
            "required": true,
            "type": "string",
            "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"
          },
          {
            "name": "messageid",
            "in": "header",
            "description": "為前後端的每個請求唯一產生的128位元隨機UUID格式的訊息ID。此參數為內部參數，在呼叫API的時候不用修改該參數的內容。直接使用即可。<br>最大長度: 50</br>",
            "required": true,
            "type": "string",
            "default": "006f7113e5fa48559549c4dfe74e2cd6"
          },
          {
            "name": "clientid",
            "in": "header",
            "description": "當使用者登入的時候產生的客戶id。此參數為內部參數，在呼叫API的時候不用修改該參數的內容。直接使用即可。<br>最大長度: 50</br>",
            "required": true,
            "type": "string",
            "default": "devin"
          },
          {
            "in": "body",
            "name": "ase",
            "description": "ase",
            "required": true,
            "schema": {
              "$ref": "#/definitions/RepaymentModel"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "查詢成功。（由Get方法傳回）",
            "schema": {
              "$ref": "#/definitions/ResultUtil"
            }
          },
          "201": {
            "description": "正常執行，請求成功。（由Post方法傳回）",
            "schema": {
              "$ref": "#/definitions/ResultUtil"
            }
          },
          "403": {
            "description": "令牌的作用範圍不正確或違反了安全性原則。操作：請檢查您是否使用合法授權使用者帳號的正確令牌。"
          },
          "404": {
            "description": "要求的帳戶不存在。操作：請確保您輸入的帳號和帳戶類型正確。"
          },
          "500": {
            "description": "API網關或微服務出現問題。操作：檢查您的網絡，稍後再試。"
          }
        }
      }
    },
    "/mortgage/repaymentPlanRetrieval": {
      "post": {
        "tags": [
          "Transaction"
        ],
        "summary": "此API用於查詢客戶某筆貸款的所有還款計劃詳情。",
        "description": "例如，客戶貸款100萬，分10年按月還款，那麼需要還120期，可以透過這個API查看每期的應還金額及利息等資訊。",
        "operationId": "repaymentPlanUsingPOST",
        "consumes": [
          "application/json"
        ],
        "produces": [
          "*/*"
        ],
        "parameters": [
          {
            "name": "token",
            "in": "header",
            "description": "頒發給第三方應用程式的最新客戶授權令牌，以JWT格式存取該銀行的客戶資料。當開發者在調用API存取銀行客戶資料的時候，都需要用到該令牌。< br>最大長度: 1000</br>",
            "required": true,
            "type": "string",
            "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"
          },
          {
            "name": "messageid",
            "in": "header",
            "description": "為前後端的每個請求唯一產生的128位元隨機UUID格式的訊息ID。此參數為內部參數，在呼叫API的時候不用修改該參數的內容。直接使用即可。<br>最大長度: 50</br>",
            "required": true,
            "type": "string",
            "default": "006f7113e5fa48559549c4dfe74e2cd6"
          },
          {
            "name": "clientid",
            "in": "header",
            "description": "當使用者登入的時候產生的客戶id。此參數為內部參數，在呼叫API的時候不用修改該參數的內容。直接使用即可。<br>最大長度: 50</br>",
            "required": true,
            "type": "string",
            "default": "devin"
          },
          {
            "in": "body",
            "name": "ase",
            "description": "ase",
            "required": true,
            "schema": {
              "$ref": "#/definitions/AccountContractNumberModel"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "查詢成功。（由Get方法傳回）",
            "schema": {
              "$ref": "#/definitions/ResultUtil"
            }
          },
          "201": {
            "description": "正常執行，請求成功。（由Post方法傳回）",
            "schema": {
              "$ref": "#/definitions/ResultUtil"
            }
          },
          "403": {
            "description": "令牌的作用範圍不正確或違反了安全性原則。操作：請檢查您是否使用合法授權使用者帳號的正確令牌。"
          },
          "404": {
            "description": "要求的帳戶不存在。操作：請確保您輸入的帳號和帳戶類型正確。"
          },
          "500": {
            "description": "API網關或微服務出現問題。操作：檢查您的網絡，稍後再試。"
          }
        }
      }
    },
    "/mortgage/transactionEnquiry": {
      "post": {
        "tags": [
          "Transaction"
        ],
        "summary": "該API用來查詢某一筆房貸中所有的歷史交易記錄",
        "description": "例如還貸款的交易記錄。",
        "operationId": "transactionEnquiryUsingPOST",
        "consumes": [
          "application/json"
        ],
        "produces": [
          "*/*"
        ],
        "parameters": [
          {
            "name": "token",
            "in": "header",
            "description": "頒發給第三方應用程式的最新客戶授權令牌，以JWT格式存取該銀行的客戶資料。當開發者在調用API存取銀行客戶資料的時候，都需要用到該令牌。< br>最大長度: 1000</br>",
            "required": true,
            "type": "string",
            "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"
          },
          {
            "name": "messageid",
            "in": "header",
            "description": "為前後端的每個請求唯一產生的128位元隨機UUID格式的訊息ID。此參數為內部參數，在呼叫API的時候不用修改該參數的內容。直接使用即可。<br>最大長度: 50</br>",
            "required": true,
            "type": "string",
            "default": "006f7113e5fa48559549c4dfe74e2cd6"
          },
          {
            "name": "clientid",
            "in": "header",
            "description": "當使用者登入的時候產生的客戶id。此參數為內部參數，在呼叫API的時候不用修改該參數的內容。直接使用即可。<br>最大長度: 50</br>",
            "required": true,
            "type": "string",
            "default": "devin"
          },
          {
            "in": "body",
            "name": "ase",
            "description": "ase",
            "required": true,
            "schema": {
              "$ref": "#/definitions/TransactionRequestModel"
            }
          }
        ],
        "responses": {
          "200": {
            "description": "查詢成功。（由Get方法傳回）",
            "schema": {
              "$ref": "#/definitions/ResultUtil"
            }
          },
          "201": {
            "description": "正常執行，請求成功。（由Post方法傳回）",
            "schema": {
              "$ref": "#/definitions/ResultUtil"
            }
          },
          "403": {
            "description": "令牌的作用範圍不正確或違反了安全性原則。操作：請檢查您是否使用合法授權使用者帳號的正確令牌。"
          },
          "404": {
            "description": "要求的帳戶不存在。操作：請確保您輸入的帳號和帳戶類型正確。"
          },
          "500": {
            "description": "API網關或微服務出現問題。操作：檢查您的網絡，稍後再試。"
          }
        }
      }
    }
  },
  "definitions": {
    "AccountContractNumberModel": {
      "type": "object",
      "required": [
        "accountnumber",
        "contractnumber"
      ],
      "properties": {
        "accountnumber": {
          "type": "string",
          "example": "***********************",
          "description": "貸款帳號。<br>最大長度: 23</br>",
          "allowEmptyValue": false
        },
        "contractnumber": {
          "type": "string",
          "example": "*********",
          "description": "客戶申請房貸成功之後，系統會自動產生一個貸款編號。每一個貸款都是銀行和客戶的一個合同，因此也叫做合同編號。<br>最大長度: 50</br>",
          "allowEmptyValue": false
        }
      },
      "title": "AccountContractNumberModel"
    },
    "LoanContractReqModel": {
      "type": "object",
      "required": [
        "accountnumber",
        "contractnumber"
      ],
      "properties": {
        "accountnumber": {
          "type": "string",
          "example": "***********************",
          "description": "貸款帳號。<br>最大長度: 23</br>",
          "allowEmptyValue": false
        },
        "contractnumber": {
          "type": "string",
          "example": "*********",
          "description": "客戶申請房貸成功之後，系統會自動產生一個貸款編號。每一個貸款都是銀行和客戶的一個合同，因此也叫做合同編號。<br>最大長度: 50</br>",
          "allowEmptyValue": false
        }
      },
      "title": "LoanContractReqModel"
    },
    "MortgageAccountNumberModel": {
      "type": "object",
      "required": [
        "accountnumber"
      ],
      "properties": {
        "accountnumber": {
          "type": "string",
          "example": "***********************",
          "description": "貸款帳號。<br>最大長度: 23</br>",
          "allowEmptyValue": false
        }
      },
      "title": "MortgageAccountNumberModel"
    },
    "MortgageCalculatorModel": {
      "type": "object",
      "required": [
        "ccyCode",
        "loanPeriod",
        "monthlysalary"
      ],
      "properties": {
        "ccyCode": {
          "type": "string",
          "example": "HKD",
          "description": "交易的貨幣類型。支援 HKD。<br> 最大長度: 3</br>",
          "allowEmptyValue": false
        },
        "loanPeriod": {
          "type": "string",
          "example": 5,
          "description": "貸款年限。支援1-30的整數。<br>最大長度: 30</br>",
          "allowEmptyValue": false
        },
        "monthlysalary": {
          "type": "number",
          "example": 10000.0,
          "description": "月薪。<br>最大長度: 18</br>",
          "allowEmptyValue": false
        }
      },
      "title": "MortgageCalculatorModel"
    },
    "MortgageLoanApplicationModel": {
      "type": "object",
      "required": [
        "accountnumber",
        "borringneeds",
        "ccyCode",
        "debitaccountnumber",
        "loanScheme",
        "monthlysalary",
        "propertyAddressFormat",
        "propertyClassification",
        "propertyTransactionStatus",
        "propertyType",
        "propertyWithCarpark",
        "propertyWithGarden",
        "propertyWithRoof",
        "purchasePrice",
        "repaymentCycle",
        "repaymentPeriod",
        "repaymentPlan",
        "solicitorsContactPerson",
        "solicitorsFaxNumber",
        "solicitorsFirm",
        "solicitorsPhoneNumber"
      ],
      "properties": {
        "accountnumber": {
          "type": "string",
          "example": "***********************",
          "description": "貸款帳號。<br>最大長度: 23</br>",
          "allowEmptyValue": false
        },
        "borringneeds": {
          "type": "number",
          "example": 10000.0,
          "description": "需要貸款的總金額。<br>最大長度: 18</br>",
          "allowEmptyValue": false
        },
        "ccyCode": {
          "type": "string",
          "example": "HKD",
          "description": "交易的貨幣類型。支援 HKD。<br> 最大長度: 3</br>",
          "allowEmptyValue": false
        },
        "debitaccountnumber": {
          "type": "string",
          "example": "HK720001001*********001",
          "description": "和該貸款帳戶關聯的儲蓄帳戶。如001結尾的儲蓄帳戶和002結尾的活期帳戶。當貸款申請成功後，系統會把貸款的錢轉到這個綁定的帳戶上。如果要還款的時候，系統會從這個綁定的帳戶上面扣款。
          "allowEmptyValue": false
        },
        "loanScheme": {
          "type": "string",
          "example": "F",
          "description": "貸款方案。支援的值： F-表示固定利率計劃。<br>最大長度: 1</br>",
          "allowEmptyValue": false
        },
        "monthlysalary": {
          "type": "number",
          "example": 10000.0,
          "description": "月薪。<br>最大長度: 18</br>",
          "allowEmptyValue": false
        },
        "propertyAddressFormat": {
          "type": "string",
          "example": "S",
          "description": "物業地址格式。支援的值：S-表示結構化地址，F-自由格式<br>最大長度: 1</br>",
          "allowEmptyValue": false
        },
        "propertyAddressLine1": {
          "type": "string",
          "description": "位址第1行<br>最大長度: 40</br>",
          "allowEmptyValue": true
        },
        "propertyAddressLine2": {
          "type": "string",
          "description": "位址第2行<br>最大長度: 40</br>",
          "allowEmptyValue": true
        },
        "propertyAddressLine3": {
          "type": "string",
          "description": "位址第3行<br>最大長度: 40</br>",
          "allowEmptyValue": true
        },
        "propertyAddressLine4": {
          "type": "string",
          "description": "位址第4行<br>最大長度: 40</br>",
          "allowEmptyValue": true
        },
        "propertyAreaCode": {
          "type": "string",
          "example": "0852",
          "description": "物業地區號。例如0852代表香港。<br>最大長度: 4</br>",
          "allowEmptyValue": true
        },
        "propertyBlock": {
          "type": "string",
          "example": "A",
          "description": "座位<br>最大長度: 5</br>",
          "allowEmptyValue": true
        },
        "propertyBuilding": {
          "type": "string",
          "description": "樓號<br>最大長度: 35</br>",
          "allowEmptyValue": true
        },
        "propertyCarparkNumber": {
          "type": "string",
          "example": "A12345",
          "description": "車位號碼。 如果有車位，輸入車位號碼；如果沒有，保持為空。<br> 最大長度: 10</br>",
          "allowEmptyValue": true
        },
        "propertyClassification": {
          "type": "string",
          "example": "F",
          "description": "物業分類。<br>F -一手樓<br>S - 二手樓<br>最大長度: 1</br>",
          "allowEmptyValue": false
        },
        "propertyCountry": {
          "type": "string",
          "example": "CN",
          "description": "物業所在國家。<br>最大長度: 2</br>",
          "allowEmptyValue": true
        },
        "propertyEstate": {
          "type": "string",
          "description": "屋鄒<br>最大長度: 35</br>",
          "allowEmptyValue": true
        },
        "propertyFlat": {
          "type": "string",
          "example": 73,
          "description": "編號 <br>最大長度: 5</br>",
          "allowEmptyValue": true
        },
        "propertyFloor": {
          "type": "string",
          "example": 19,
          "description": "層<br>最大長度: 3</br>",
          "allowEmptyValue": true
        },
        "propertyMonthlyRental": {
          "type": "number",
          "example": 10000.0,
          "description": "物業月租 <br>最大長度: 18</br>",
          "allowEmptyValue": true
        },
        "propertyPostCode": {
          "type": "string",
          "example": 710000,
          "description": "郵編<br>最大長度: 8</br>",
          "allowEmptyValue": true
        },
        "propertyRoom": {
          "type": "string",
          "example": 1908,
          "description": "室<br>最大長度: 5</br>",
          "allowEmptyValue": true
        },
        "propertyStreetName": {
          "type": "string",
          "description": "街名<br>最大長度: 35</br>",
          "allowEmptyValue": true
        },
        "propertyStreetNumber": {
          "type": "string",
          "example": "S437",
          "description": "街號<br>最大長度: 5</br>",
          "allowEmptyValue": true
        },
        "propertyTransactionStatus": {
          "type": "string",
          "example": "T",
          "description": "狀態: V - 空閒, T - 房屋租賃合約 <br>最大長度: 1</br>",
          "allowEmptyValue": false
        },
        "propertyType": {
          "type": "string",
          "example": "R",
          "description": "物業類別<br>R - 住宅<br>C - 車位<br>V - 別墅<br>T - 村屋<br>O - 辦公室<br>S - 舖位<br>I - 工業<br>H - 獨立屋<br>最大長度: 1</br>",
          "allowEmptyValue": false
        },
        "propertyWithCarpark": {
          "type": "string",
          "example": "Y",
          "description": "是否帶車位。Y- 是, N-不是<br>最大長度: 1</br>",
          "allowEmptyValue": false
        },
        "propertyWithGarden": {
          "type": "string",
          "example": "Y",
          "description": "是否帶花園。Y- 是, N-不是<br>最大長度: 1</br>",
          "allowEmptyValue": false
        },
        "propertyWithRoof": {
          "type": "string",
          "example": "Y",
          "description": "是否帶天台。Y- 是, N-不是<br>最大長度: 1</br>",
          "allowEmptyValue": false
        },
        "purchasePrice": {
          "type": "number",
          "example": 1000000.0,
          "description": "買入價格。<br>最大長度: 18</br>",
          "allowEmptyValue": false
        },
        "repaymentCycle": {
          "type": "string",
          "example": "M",
          "description": "貸款還款週期。支援的值：M-表示按月還款，B-表示雙週還款。<br>最大長度: 1</br>",
          "allowEmptyValue": false
        },
        "repaymentPeriod": {
          "type": "string",
          "example": 5,
          "description": "還款年限。<br>最大長度: 5</br>",
          "allowEmptyValue": false
        },
        "repaymentPlan": {
          "type": "string",
          "example": "L",
          "description": "還款計劃。支援的值： L-等額本息計劃<br>最大長度: 1</br>",
          "allowEmptyValue": false
        },
        "solicitorsContactPerson": {
          "type": "string",
          "description": "律師行聯絡人<br>最大長度: 140</br>",
          "allowEmptyValue": false
        },
        "solicitorsFaxNumber": {
          "type": "string",
          "description": "律師行傳真<br>最大長度: 34</br>",
          "allowEmptyValue": false
        },
        "solicitorsFirm": {
          "type": "string",
          "description": "律師行名稱<br>最大長度: 140</br>",
          "allowEmptyValue": false
        },
        "solicitorsPhoneNumber": {
          "type": "string",
          "description": "律師行電話<br>最大長度: 34</br>",
          "allowEmptyValue": false
        }
      },
      "title": "MortgageLoanApplicationModel"
    },
    "MortgageLoanContractModel": {
      "type": "object",
      "required": [
        "accountnumber"
      ],
      "properties": {
        "accountnumber": {
          "type": "string",
          "example": "***********************",
          "description": "貸款帳號。<br>最大長度: 23</br>",
          "allowEmptyValue": false
        }
      },
      "title": "MortgageLoanContractModel"
    },
    "RepaymentModel": {
      "type": "object",
      "required": [
        "accountnumber",
        "contractnumber",
        "repaymentaccountnumber",
        "repaymentamount"
      ],
      "properties": {
        "accountnumber": {
          "type": "string",
          "example": "***********************",
          "description": "貸款帳號。<br>最大長度: 23</br>",
          "allowEmptyValue": false
        },
        "contractnumber": {
          "type": "string",
          "example": "*********",
          "description": "客戶申請房貸成功之後，系統會自動產生一個貸款編號。每一個貸款都是銀行和客戶的一個合同，因此也叫做合同編號。<br>最大長度: 50</br>",
          "allowEmptyValue": false
        },
        "repaymentaccountnumber": {
          "type": "string",
          "example": "HK720001001*********001",
          "description": "和該貸款帳戶關聯的儲蓄帳戶。如001結尾的儲蓄帳戶和002結尾的活期帳戶。當如果要還款的時候，系統會從這個綁定的帳戶上面扣款。<br>最大長度: 23</br>",
          "allowEmptyValue": false
        },
        "repaymentamount": {
          "type": "number",
          "example": 269.19,
          "description": "還款金額。<br>最大長度: 18</br>",
          "allowEmptyValue": false
        }
      },
      "title": "RepaymentModel"
    },
    "ResultUtil": {
      "type": "object",
      "properties": {
        "code": {
          "type": "string"
        },
        "data": {
          "type": "object"
        },
        "msg": {
          "type": "string"
        }
      },
      "title": "ResultUtil"
    },
    "TransactionRequestModel": {
      "type": "object",
      "required": [
        "accountnumber",
        "contractnumber"
      ],
      "properties": {
        "accountnumber": {
          "type": "string",
          "example": "***********************",
          "description": "貸款帳號。<br>最大長度: 23</br>",
          "allowEmptyValue": false
        },
        "contractnumber": {
          "type": "string",
          "example": "*********",
          "description": "客戶申請房貸成功之後，系統會自動產生一個貸款編號。每一個貸款都是銀行和客戶的一個合同，因此也叫做合同編號。<br>最大長度: 50</br>",
          "allowEmptyValue": false
        },
        "transFromDate": {
          "type": "number",
          "example": 1.5566688E12,
          "description": "交易記錄開始時間。<br>最大長度: 20</br>",
          "allowEmptyValue": true
        },
        "transToDate": {
          "type": "number",
          "example": 1.584552779E12,
          "description": "交易記錄結束時間。<br>最大長度: 20</br>",
          "allowEmptyValue": true
        }
      },
      "title": "TransactionRequestModel"
    }
  }
}
