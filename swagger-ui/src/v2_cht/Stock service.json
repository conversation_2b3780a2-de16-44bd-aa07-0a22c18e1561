{
    "swagger": "2.0",
    "info": {
        "description": "此服務模組主要包含了所有股票交易相關的業務API。包含了股票列表，股票市場信息，股票買入， 股票賣出，股票持倉查詢及股票交易記錄查詢。當您開發股票相關的業務模組時，可以在這裡找到對應的API來完成不同業務功能。",
        "version": "1.0",
        "title": "股票業務",
        "contact": {
            "name": "<EMAIL>"
        }
    },
    "schemes": ["https"],
    "host": "simnectz.hsu.edu.hk/lbsgateway/stock-experience",
    "basePath": "/",
    "tags": [
        {
            "name": "Market Information",
            "description": "查詢並顯示最新的股票市場資訊。"
        },
        {
            "name": "Order & Simulation",
            "description": "股票訂單管理和股票交易模擬。"
        },
        {
            "name": "Account Information & Maintenance",
            "description": "股票帳戶持倉資訊與帳戶維護。"
        },
        {
            "name": "Transaction",
            "description": "客戶股票帳戶中的交易處理。"
        }
    ],
    "paths": {
        "/stock/order/cancellation": {
            "post": {
                "tags": [
                    "Order & Simulation"
                ],
                "summary": "此API用於股票買賣過程中撤單操作。",
                "description": "在客戶股票買賣過程中，當客戶掛單（即想要買入或賣出，都可以調用/order/orderPlacing），發起的掛單申請）之後，在未實際成交之前，可以調用該API來取消掛單業務。次交易請求就取消了；也可以呼叫/stock/order/orderChange 修改掛單的內容，例如將5000HKD改為6000HKD； 然後再呼叫/stock/order/simulatorUpdate API來模擬掛單交易成功或失敗，最後決定本次股票交易是否成功。",
                "operationId": "cancellationUsingPOST",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "*/*"
                ],
                "parameters": [
                    {
                        "name": "token",
                        "in": "header",
                        "description": "頒發給第三方應用程式的最新客戶授權令牌，以JWT格式存取該銀行的客戶資料。當開發者在調用API存取銀行客戶資料的時候，都需要用到該令牌。< br>最大長度: 1000</br>",
                        "required": true,
                        "type": "string",
                        "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"
                    },
                    {
                        "name": "messageid",
                        "in": "header",
                        "description": "為前後端的每個請求唯一產生的128位元隨機UUID格式的訊息ID。此參數為內部參數，在呼叫API的時候不用修改該參數的內容。直接使用即可。<br>最大長度: 50</br>",
                        "required": true,
                        "type": "string",
                        "default": "006f7113e5fa48559549c4dfe74e2cd6"
                    },
                    {
                        "name": "clientid",
                        "in": "header",
                        "description": "當使用者登入的時候產生的客戶id。此參數為內部參數，在呼叫API的時候不用修改該參數的內容。直接使用即可。<br>最大長度: 50</br>" ,
                        "required": true,
                        "type": "string",
                        "default": "devin"
                    },
                    {
                        "in": "body",
                        "name": "idmodel",
                        "description": "idmodel",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/IDModel"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "查詢成功。（由Get方法傳回）",
                        "schema": {
                            "$ref": "#/definitions/ResultUtil"
                        }
                    },
                    "201": {
                        "description": "正常執行，請求成功。（由Post方法傳回）",
                        "schema": {
                            "$ref": "#/definitions/ResultUtil"
                        }
                    },
                    "403": {
                        "description": "令牌的作用範圍不正確或違反了安全性原則。操作：請檢查您是否使用合法授權使用者帳號的正確令牌。"
                    },
                    "404": {
                        "description": "要求的帳戶不存在。操作：請確保您輸入的帳號和帳戶類型正確。"
                    },
                    "500": {
                        "description": "API網關或微服務出現問題。操作：檢查您的網絡，稍後再試。"
                    }
                }
            }
        },
        "/stock/order/orderChange": {
            "post": {
                "tags": [
                    "Order & Simulation"
                ],
                "summary": "此API用於修改掛單訊息。",
                "description": "例如當用戶想要買入或賣出股票的時候，都需要先掛單。例如，客戶想要買進1000股的某檔股票時，就需要呼叫/stock/order/orderPlacing 來下單。 /order/simulatorUpdate模擬成交了， 那麼就不能再被修改了。",
                "operationId": "orderInfoUpdateUsingPOST",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "*/*"
                ],
                "parameters": [
                    {
                        "name": "token",
                        "in": "header",
                        "description": "頒發給第三方應用程式的最新客戶授權令牌，以JWT格式存取該銀行的客戶資料。當開發者在調用API存取銀行客戶資料的時候，都需要用到該令牌。< br>最大長度: 1000</br>",
                        "required": true,
                        "type": "string",
                        "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"
                    },
                    {
                        "name": "messageid",
                        "in": "header",
                        "description": "為前後端的每個請求唯一產生的128位元隨機UUID格式的訊息ID。此參數為內部參數，在呼叫API的時候不用修改該參數的內容。直接使用即可。<br>最大長度: 50</br>",
                        "required": true,
                        "type": "string",
                        "default": "006f7113e5fa48559549c4dfe74e2cd6"
                    },
                    {
                        "name": "clientid",
                        "in": "header",
                        "description": "當使用者登入的時候產生的客戶id。此參數為內部參數，在呼叫API的時候不用修改該參數的內容。直接使用即可。<br>最大長度: 50</br>" ,
                        "required": true,
                        "type": "string",
                        "default": "devin"
                    },
                    {
                        "in": "body",
                        "name": "updateOrderRequestModel",
                        "description": "updateOrderRequestModel",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/UpdateOrderRequestModel"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "查詢成功。（由Get方法傳回）",
                        "schema": {
                            "$ref": "#/definitions/ResultUtil"
                        }
                    },
                    "201": {
                        "description": "正常執行，請求成功。（由Post方法傳回）",
                        "schema": {
                            "$ref": "#/definitions/ResultUtil"
                        }
                    },
                    "403": {
                        "description": "令牌的作用範圍不正確或違反了安全性原則。操作：請檢查您是否使用合法授權使用者帳號的正確令牌。"
                    },
                    "404": {
                        "description": "要求的帳戶不存在。操作：請確保您輸入的帳號和帳戶類型正確。"
                    },
                    "500": {
                        "description": "API網關或微服務出現問題。操作：檢查您的網絡，稍後再試。"
                    }
                }
            }
        },
        "/stock/order/orderDetailRetrievalById": {
            "post": {
                "tags": [
                    "Order & Simulation"
                ],
                "summary": "該API用於某一個掛單記錄的查詢。",
                "description": "可以查詢某個掛單的詳情。例如，客戶想要買入1000份的某檔股票時，就需要調用/stock/order/orderPlacing 下單。這就是一個掛單的過程。不管是買入或賣出都要掛單，掛單之後，會傳回一個參數id（即掛單的編號），然後可以呼叫/stock/order/orderDetailRetrievalById， 輸入該編號，就可以取得到該掛單的詳細資料了。 ",
                "operationId": "orderDetailRetrievalByIdUsingPOST",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "*/*"
                ],
                "parameters": [
                    {
                        "name": "token",
                        "in": "header",
                        "description": "頒發給第三方應用程式的最新客戶授權令牌，以JWT格式存取該銀行的客戶資料。當開發者在調用API存取銀行客戶資料的時候，都需要用到該令牌。< br>最大長度: 1000</br>",
                        "required": true,
                        "type": "string",
                        "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"
                    },
                    {
                        "name": "messageid",
                        "in": "header",
                        "description": "為前後端的每個請求唯一產生的128位元隨機UUID格式的訊息ID。此參數為內部參數，在呼叫API的時候不用修改該參數的內容。直接使用即可。<br>最大長度: 50</br>",
                        "required": true,
                        "type": "string",
                        "default": "006f7113e5fa48559549c4dfe74e2cd6"
                    },
                    {
                        "name": "clientid",
                        "in": "header",
                        "description": "當使用者登入的時候產生的客戶id。此參數為內部參數，在呼叫API的時候不用修改該參數的內容。直接使用即可。<br>最大長度: 50</br>" ,
                        "required": true,
                        "type": "string",
                        "default": "devin"
                    },
                    {
                        "in": "body",
                        "name": "idmodel",
                        "description": "idmodel",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/IDModel"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "查詢成功。（由Get方法傳回）",
                        "schema": {
                            "$ref": "#/definitions/ResultUtil«StockOrderInfoModel»"
                        }
                    },
                    "201": {
                        "description": "正常執行，請求成功。（由Post方法傳回）",
                        "schema": {
                            "$ref": "#/definitions/ResultUtil«StockOrderInfoModel»"
                        }
                    },
                    "403": {
                        "description": "令牌的作用範圍不正確或違反了安全性原則。操作：請檢查您是否使用合法授權使用者帳號的正確令牌。"
                    },
                    "404": {
                        "description": "要求的帳戶不存在。操作：請確保您輸入的帳號和帳戶類型正確。"
                    },
                    "500": {
                        "description": "API網關或微服務出現問題。操作：檢查您的網絡，稍後再試。"
                    }
                }
            }
        },
        "/stock/order/orderPlacing": {
            "post": {
                "tags": [
                    "Order & Simulation"
                ],
                "summary": "該API用於買入賣出股票時，掛單的服務。",
                "description": "例如當用戶想要買入或賣出股票的時候，都需要先掛單。例如，客戶想要買進1000股的某檔股票時，就需要呼叫/stock/order/orderPlacing 來下單，輸入想買的股票代碼，買入價格，買入份額等信息。模擬成交或失敗交易的情況，可以呼叫/stock/order/simulatorUpdate。",
                "operationId": "orderPlacingUsingPOST",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "*/*"
                ],
                "parameters": [
                    {
                        "name": "token",
                        "in": "header",
                        "description": "頒發給第三方應用程式的最新客戶授權令牌，以JWT格式存取該銀行的客戶資料。當開發者在調用API存取銀行客戶資料的時候，都需要用到該令牌。< br>最大長度: 1000</br>",
                        "required": true,
                        "type": "string",
                        "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"
                    },
                    {
                        "name": "messageid",
                        "in": "header",
                        "description": "為前後端的每個請求唯一產生的128位元隨機UUID格式的訊息ID。此參數為內部參數，在呼叫API的時候不用修改該參數的內容。直接使用即可。<br>最大長度: 50</br>",
                        "required": true,
                        "type": "string",
                        "default": "006f7113e5fa48559549c4dfe74e2cd6"
                    },
                    {
                        "name": "clientid",
                        "in": "header",
                        "description": "當使用者登入的時候產生的客戶id。此參數為內部參數，在呼叫API的時候不用修改該參數的內容。直接使用即可。<br>最大長度: 50</br>" ,
                        "required": true,
                        "type": "string",
                        "default": "devin"
                    },
                    {
                        "in": "body",
                        "name": "stockTradingModel",
                        "description": "stockTradingModel",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/StockTradingModel"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "查詢成功。（由Get方法傳回）",
                        "schema": {
                            "$ref": "#/definitions/ResultUtil"
                        }
                    },
                    "201": {
                        "description": "正常執行，請求成功。（由Post方法傳回）",
                        "schema": {
                            "$ref": "#/definitions/ResultUtil"
                        }
                    },
                    "403": {
                        "description": "令牌的作用範圍不正確或違反了安全性原則。操作：請檢查您是否使用合法授權使用者帳號的正確令牌。"
                    },
                    "404": {
                        "description": "要求的帳戶不存在。操作：請確保您輸入的帳號和帳戶類型正確。"
                    },
                    "500": {
                        "description": "API網關或微服務出現問題。操作：檢查您的網絡，稍後再試。"
                    }
                }
            }
        },
        "/stock/order/orderRetrieval": {
            "post": {
                "tags": [
                    "Order & Simulation"
                ],
                "summary": "此API用於所有掛單記錄的查詢。",
                "description": "可以查詢某檔股票帳戶在某段時間內掛單的歷史記錄及詳情。例如，當客戶想要買入1,000股的某檔股票時，就需要呼叫/stock/order/orderPlacing 來下單。",
                "operationId": "orderRetrievalUsingPOST",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "*/*"
                ],
                "parameters": [
                    {
                        "name": "token",
                        "in": "header",
                        "description": "頒發給第三方應用程式的最新客戶授權令牌，以JWT格式存取該銀行的客戶資料。當開發者在調用API存取銀行客戶資料的時候，都需要用到該令牌。< br>最大長度: 1000</br>",
                        "required": true,
                        "type": "string",
                        "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"
                    },
                    {
                        "name": "messageid",
                        "in": "header",
                        "description": "為前後端的每個請求唯一產生的128位元隨機UUID格式的訊息ID。此參數為內部參數，在呼叫API的時候不用修改該參數的內容。直接使用即可。<br>最大長度: 50</br>",
                        "required": true,
                        "type": "string",
                        "default": "006f7113e5fa48559549c4dfe74e2cd6"
                    },
                    {
                        "name": "clientid",
                        "in": "header",
                        "description": "當使用者登入的時候產生的客戶id。此參數為內部參數，在呼叫API的時候不用修改該參數的內容。直接使用即可。<br>最大長度: 50</br>" ,
                        "required": true,
                        "type": "string",
                        "default": "devin"
                    },
                    {
                        "in": "body",
                        "name": "orderRequestModel",
                        "description": "orderRequestModel",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/OrderRequestModel"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "查詢成功。（由Get方法傳回）",
                        "schema": {
                            "$ref": "#/definitions/ResultUtil«List«StockOrderInfoModel»»"
                        }
                    },
                    "201": {
                        "description": "正常執行，請求成功。（由Post方法傳回）",
                        "schema": {
                            "$ref": "#/definitions/ResultUtil«List«StockOrderInfoModel»»"
                        }
                    },
                    "403": {
                        "description": "令牌的作用範圍不正確或違反了安全性原則。操作：請檢查您是否使用合法授權使用者帳號的正確令牌。"
                    },
                    "404": {
                        "description": "要求的帳戶不存在。操作：請確保您輸入的帳號和帳戶類型正確。"
                    },
                    "500": {
                        "description": "API網關或微服務出現問題。操作：檢查您的網絡，稍後再試。"
                    }
                }
            }
        },
        "/stock/order/simulatorUpdate": {
            "post": {
                "tags": [
                    "Order & Simulation"
                ],
                "summary": "此API用於模擬股票交易中掛單成功後，模擬實際成交還是失敗的過程。",
                "description": "例如，客戶想要買入1000份的某檔股票時，就需要呼叫/stock/order/orderPlacing 下單。這個過程代表掛單成功了，然後就可以再呼叫/stock/order /simulatorUpdate API來模擬股票是否能成功交易的過程。",
                "operationId": "orderStatusUpdateUsingPOST",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "*/*"
                ],
                "parameters": [
                    {
                        "name": "token",
                        "in": "header",
                        "description": "頒發給第三方應用程式的最新客戶授權令牌，以JWT格式存取該銀行的客戶資料。當開發者在調用API存取銀行客戶資料的時候，都需要用到該令牌。< br>最大長度: 1000</br>",
                        "required": true,
                        "type": "string",
                        "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"
                    },
                    {
                        "name": "messageid",
                        "in": "header",
                        "description": "為前後端的每個請求唯一產生的128位元隨機UUID格式的訊息ID。此參數為內部參數，在呼叫API的時候不用修改該參數的內容。直接使用即可。<br>最大長度: 50</br>",
                        "required": true,
                        "type": "string",
                        "default": "006f7113e5fa48559549c4dfe74e2cd6"
                    },
                    {
                        "name": "clientid",
                        "in": "header",
                        "description": "當使用者登入的時候產生的客戶id。此參數為內部參數，在呼叫API的時候不用修改該參數的內容。直接使用即可。<br>最大長度: 50</br>" ,
                        "required": true,
                        "type": "string",
                        "default": "devin"
                    },
                    {
                        "in": "body",
                        "name": "updateOrderStatusModel",
                        "description": "updateOrderStatusModel",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/UpdateOrderStatusModel"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "查詢成功。（由Get方法傳回）",
                        "schema": {
                            "$ref": "#/definitions/ResultUtil"
                        }
                    },
                    "201": {
                        "description": "正常執行，請求成功。（由Post方法傳回）",
                        "schema": {
                            "$ref": "#/definitions/ResultUtil"
                        }
                    },
                    "403": {
                        "description": "令牌的作用範圍不正確或違反了安全性原則。操作：請檢查您是否使用合法授權使用者帳號的正確令牌。"
                    },
                    "404": {
                        "description": "要求的帳戶不存在。操作：請確保您輸入的帳號和帳戶類型正確。"
                    },
                    "500": {
                        "description": "API網關或微服務出現問題。操作：檢查您的網絡，稍後再試。"
                    }
                }
            }
        },
        "/stock/settlementAccountUpdate": {
            "post": {
                "tags": [
                    "Account Information & Maintenance"
                ],
                "summary": "此API用於設定股票帳戶的關聯儲蓄帳戶。",
                "description": "例如，當銀行客戶想要買一隻股票，首先這個用戶需要有一個股票帳戶，然後再給這個股票帳戶綁定一個儲蓄帳戶或活期帳戶（001結尾或002結尾的帳戶）。當客戶買進100股的某檔股票，需要花費1000HKD的時候，那麼系統就會從綁定的這個儲蓄帳戶或活期帳戶直接扣掉1000HKD，同時，這個股票帳戶的持倉就會多出這100份的該隻股票份額。股票帳戶的持股中，就會減少這100個份額的股票。查詢該銀行客戶所持有的其他帳戶，是否有需要的001或002結尾的帳戶類型。",
                "operationId": "settlementAccountUpdateUsingPOST",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "*/*"
                ],
                "parameters": [
                    {
                        "name": "token",
                        "in": "header",
                        "description": "頒發給第三方應用程式的最新客戶授權令牌，以JWT格式存取該銀行的客戶資料。當開發者在調用API存取銀行客戶資料的時候，都需要用到該令牌。< br>最大長度: 1000</br>",
                        "required": true,
                        "type": "string",
                        "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"
                    },
                    {
                        "name": "messageid",
                        "in": "header",
                        "description": "為前後端的每個請求唯一產生的128位元隨機UUID格式的訊息ID。此參數為內部參數，在呼叫API的時候不用修改該參數的內容。直接使用即可。<br>最大長度: 50</br>",
                        "required": true,
                        "type": "string",
                        "default": "006f7113e5fa48559549c4dfe74e2cd6"
                    },
                    {
                        "name": "clientid",
                        "in": "header",
                        "description": "當使用者登入的時候產生的客戶id。此參數為內部參數，在呼叫API的時候不用修改該參數的內容。直接使用即可。<br>最大長度: 50</br>" ,
                        "required": true,
                        "type": "string",
                        "default": "devin"
                    },
                    {
                        "in": "body",
                        "name": "updateSettlementAccountModel",
                        "description": "updateSettlementAccountModel",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/UpdateSettlementAccountModel"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "查詢成功。（由Get方法傳回）",
                        "schema": {
                            "$ref": "#/definitions/ResultUtil"
                        }
                    },
                    "201": {
                        "description": "正常執行，請求成功。（由Post方法傳回）",
                        "schema": {
                            "$ref": "#/definitions/ResultUtil"
                        }
                    },
                    "403": {
                        "description": "令牌的作用範圍不正確或違反了安全性原則。操作：請檢查您是否使用合法授權使用者帳號的正確令牌。"
                    },
                    "404": {
                        "description": "要求的帳戶不存在。操作：請確保您輸入的帳號和帳戶類型正確。"
                    },
                    "500": {
                        "description": "API網關或微服務出現問題。操作：檢查您的網絡，稍後再試。"
                    }
                }
            }
        },
        "/stock/stockHoldingEnquiry": {
            "post": {
                "tags": [
                    "Account Information & Maintenance"
                ],
                "summary": "此API用於查詢股票帳戶的持倉詳情。",
                "description": "可以查詢客戶的股票帳戶下，對於某個股票的持倉信息和該股票賬戶下所有的股票持倉信息。如果客戶還沒有任何持倉信息，可能需要先買入股票，可以調用/stock /order/orderPlacing來做一個買入股票的掛單。",
                "operationId": "stockHoldingEnquiryUsingPOST",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "*/*"
                ],
                "parameters": [
                    {
                        "name": "token",
                        "in": "header",
                        "description": "頒發給第三方應用程式的最新客戶授權令牌，以JWT格式存取該銀行的客戶資料。當開發者在調用API存取銀行客戶資料的時候，都需要用到該令牌。< br>最大長度: 1000</br>",
                        "required": true,
                        "type": "string",
                        "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"
                    },
                    {
                        "name": "messageid",
                        "in": "header",
                        "description": "為前後端的每個請求唯一產生的128位元隨機UUID格式的訊息ID。此參數為內部參數，在呼叫API的時候不用修改該參數的內容。直接使用即可。<br>最大長度: 50</br>",
                        "required": true,
                        "type": "string",
                        "default": "006f7113e5fa48559549c4dfe74e2cd6"
                    },
                    {
                        "name": "clientid",
                        "in": "header",
                        "description": "當使用者登入的時候產生的客戶id。此參數為內部參數，在呼叫API的時候不用修改該參數的內容。直接使用即可。<br>最大長度: 50</br>" ,
                        "required": true,
                        "type": "string",
                        "default": "devin"
                    },
                    {
                        "in": "body",
                        "name": "stockHoldingEnquiryModel",
                        "description": "stockHoldingEnquiryModel",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/StockHoldingEnquiryModel"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "查詢成功。（由Get方法傳回）",
                        "schema": {
                            "$ref": "#/definitions/ResultUtil«StockHoldingInfoModel»"
                        }
                    },
                    "201": {
                        "description": "正常執行，請求成功。（由Post方法傳回）",
                        "schema": {
                            "$ref": "#/definitions/ResultUtil«StockHoldingInfoModel»"
                        }
                    },
                    "403": {
                        "description": "令牌的作用範圍不正確或違反了安全性原則。操作：請檢查您是否使用合法授權使用者帳號的正確令牌。"
                    },
                    "404": {
                        "description": "要求的帳戶不存在。操作：請確保您輸入的帳號和帳戶類型正確。"
                    },
                    "500": {
                        "description": "API網關或微服務出現問題。操作：檢查您的網絡，稍後再試。"
                    }
                }
            }
        },
        "/stock/stockList": {
            "post": {
                "tags": [
                    "Market Information"
                ],
                "summary": "此API用於取得市場上的所有股票清單及所有股票詳情。",
                "description": "當想要展示基金清單及每個基金的市場資訊時，如股票價格，成交量等，都可以調用該API。",
                "operationId": "stockListUsingPOST",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "*/*"
                ],
                "parameters": [
                    {
                        "name": "token",
                        "in": "header",
                        "description": "頒發給第三方應用程式的最新客戶授權令牌，以JWT格式存取該銀行的客戶資料。當開發者在調用API存取銀行客戶資料的時候，都需要用到該令牌。< br>最大長度: 1000</br>",
                        "required": true,
                        "type": "string",
                        "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"
                    },
                    {
                        "name": "messageid",
                        "in": "header",
                        "description": "為前後端的每個請求唯一產生的128位元隨機UUID格式的訊息ID。此參數為內部參數，在呼叫API的時候不用修改該參數的內容。直接使用即可。<br>最大長度: 50</br>",
                        "required": true,
                        "type": "string",
                        "default": "006f7113e5fa48559549c4dfe74e2cd6"
                    },
                    {
                        "name": "clientid",
                        "in": "header",
                        "description": "當使用者登入的時候產生的客戶id。此參數為內部參數，在呼叫API的時候不用修改該參數的內容。直接使用即可。<br>最大長度: 50</br>" ,
                        "required": true,
                        "type": "string",
                        "default": "devin"
                    },
                    {
                        "in": "body",
                        "name": "queryStockListModel",
                        "description": "queryStockListModel",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/QueryStockListModel"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "查詢成功。（由Get方法傳回）",
                        "schema": {
                            "$ref": "#/definitions/ResultUtil«List«StockInformationModel»»"
                        }
                    },
                    "201": {
                        "description": "正常執行，請求成功。（由Post方法傳回）",
                        "schema": {
                            "$ref": "#/definitions/ResultUtil«List«StockInformationModel»»"
                        }
                    },
                    "403": {
                        "description": "令牌的作用範圍不正確或違反了安全性原則。操作：請檢查您是否使用合法授權使用者帳號的正確令牌。"
                    },
                    "404": {
                        "description": "要求的帳戶不存在。操作：請確保您輸入的帳號和帳戶類型正確。"
                    },
                    "500": {
                        "description": "API網關或微服務出現問題。操作：檢查您的網絡，稍後再試。"
                    }
                }
            }
        },
        "/stock/stockQuotation": {
            "post": {
                "tags": [
                    "Market Information"
                ],
                "summary": "此API用於查詢某檔股票的市場資訊。",
                "description": "例如，當客戶想要查看代碼為00112.HK的股票價格，漲跌幅等市場信息，都可以調用該API來完成查詢。",
                "operationId": "stockQuotationUsingPOST",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "*/*"
                ],
                "parameters": [
                    {
                        "name": "token",
                        "in": "header",
                        "description": "頒發給第三方應用程式的最新客戶授權令牌，以JWT格式存取該銀行的客戶資料。當開發者在調用API存取銀行客戶資料的時候，都需要用到該令牌。< br>最大長度: 1000</br>",
                        "required": true,
                        "type": "string",
                        "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"
                    },
                    {
                        "name": "messageid",
                        "in": "header",
                        "description": "為前後端的每個請求唯一產生的128位元隨機UUID格式的訊息ID。此參數為內部參數，在呼叫API的時候不用修改該參數的內容。直接使用即可。<br>最大長度: 50</br>",
                        "required": true,
                        "type": "string",
                        "default": "006f7113e5fa48559549c4dfe74e2cd6"
                    },
                    {
                        "name": "clientid",
                        "in": "header",
                        "description": "當使用者登入的時候產生的客戶id。此參數為內部參數，在呼叫API的時候不用修改該參數的內容。直接使用即可。<br>最大長度: 50</br>" ,
                        "required": true,
                        "type": "string",
                        "default": "devin"
                    },
                    {
                        "in": "body",
                        "name": "stockCodeModel",
                        "description": "stockCodeModel",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/StockCodeModel"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "查詢成功。（由Get方法傳回）",
                        "schema": {
                            "$ref": "#/definitions/ResultUtil«StockInformationModel»"
                        }
                    },
                    "201": {
                        "description": "正常執行，請求成功。（由Post方法傳回）",
                        "schema": {
                            "$ref": "#/definitions/ResultUtil«StockInformationModel»"
                        }
                    },
                    "403": {
                        "description": "令牌的作用範圍不正確或違反了安全性原則。操作：請檢查您是否使用合法授權使用者帳號的正確令牌。"
                    },
                    "404": {
                        "description": "要求的帳戶不存在。操作：請確保您輸入的帳號和帳戶類型正確。"
                    },
                    "500": {
                        "description": "API網關或微服務出現問題。操作：檢查您的網絡，稍後再試。"
                    }
                }
            }
        },
        "/stock/transactionRetrieval": {
            "post": {
                "tags": [
                    "Transaction"
                ],
                "summary": "此API用於查詢某位客戶的股票帳戶的歷史交易記錄。",
                "description": "例如，客戶用股票帳戶購買過很多隻股票，那麼就可以在這裡調用相應的API查詢某段時間內，某隻股票的交易記錄，或者該帳戶在某段時間內的所有交易記錄。",
                "operationId": "retrieveStkTransactionUsingPOST",
                "consumes": [
                    "application/json"
                ],
                "produces": [
                    "*/*"
                ],
                "parameters": [
                    {
                        "name": "token",
                        "in": "header",
                        "description": "頒發給第三方應用程式的最新客戶授權令牌，以JWT格式存取該銀行的客戶資料。當開發者在調用API存取銀行客戶資料的時候，都需要用到該令牌。< br>最大長度: 1000</br>",
                        "required": true,
                        "type": "string",
                        "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"
                    },
                    {
                        "name": "messageid",
                        "in": "header",
                        "description": "為前後端的每個請求唯一產生的128位元隨機UUID格式的訊息ID。此參數為內部參數，在呼叫API的時候不用修改該參數的內容。直接使用即可。<br>最大長度: 50</br>",
                        "required": true,
                        "type": "string",
                        "default": "006f7113e5fa48559549c4dfe74e2cd6"
                    },
                    {
                        "name": "clientid",
                        "in": "header",
                        "description": "當使用者登入的時候產生的客戶id。此參數為內部參數，在呼叫API的時候不用修改該參數的內容。直接使用即可。<br>最大長度: 50</br>" ,
                        "required": true,
                        "type": "string",
                        "default": "devin"
                    },
                    {
                        "in": "body",
                        "name": "queryStockTransModel",
                        "description": "queryStockTransModel",
                        "required": true,
                        "schema": {
                            "$ref": "#/definitions/QueryStockTransModel"
                        }
                    }
                ],
                "responses": {
                    "200": {
                        "description": "查詢成功。（由Get方法傳回）",
                        "schema": {
                            "$ref": "#/definitions/ResultUtil«List«StockPlatFormLogModel»»"
                        }
                    },
                    "201": {
                        "description": "正常執行，請求成功。（由Post方法傳回）",
                        "schema": {
                            "$ref": "#/definitions/ResultUtil«List«StockPlatFormLogModel»»"
                        }
                    },
                    "403": {
                        "description": "令牌的作用範圍不正確或違反了安全性原則。操作：請檢查您是否使用合法授權使用者帳號的正確令牌。"
                    },
                    "404": {
                        "description": "要求的帳戶不存在。操作：請確保您輸入的帳號和帳戶類型正確。"
                    },
                    "500": {
                        "description": "API網關或微服務出現問題。操作：檢查您的網絡，稍後再試。"
                    }
                }
            }
        }
    },
    "definitions": {
        "IDModel": {
            "type": "object",
            "required": [
                "id"
            ],
            "properties": {
                "id": {
                    "type": "number",
                    "example": 2.0,
                    "description": "股票訂單編號。當您呼叫買入或賣出股票申請API之後，就會傳回一個股票訂單編號。<br>最大長度: 20</br>",
                    "allowEmptyValue": false
                }
            },
            "title": "IDModel"
        },
        "OrderRequestModel": {
            "type": "object",
            "required": [
                "fromdate",
                "index",
                "items",
                "stkaccountnumber",
                "todate"
            ],
            "properties": {
                "fromdate": {
                    "type": "number",
                    "example": 1.583824453E12,
                    "description": "想要查詢的訂單的開始時間範圍。<br/>最大長度: 20",
                    "allowEmptyValue": false
                },
                "index": {
                    "type": "number",
                    "example": 0.0,
                    "description": "API傳回資料的開始位置標記。如：當index=0時，則表示從第1條資料開始傳回結果。<br/>最大長度: 4",
                    "allowEmptyValue": false
                },
                "items": {
                    "type": "number",
                    "example": 3.0,
                    "description": "API傳回的資料條數。如：當items=10. 表示傳回10個結果。<br/>最大長度: 4",
                    "allowEmptyValue": false
                },
                "stkaccountnumber": {
                    "type": "string",
                    "example": "***********************",
                    "description": "股票帳號。<br/> 最大長度: 34",
                    "allowEmptyValue": false
                },
                "stockcode": {
                    "type": "string",
                    "example": "00112.HK",
                    "description": "股票代碼。<br/> 最大長度: 10",
                    "allowEmptyValue": true
                },
                "todate": {
                    "type": "number",
                    "example": 1.584429253E12,
                    "description": "想要查詢的訂單的結束時間範圍。<br/>最大長度: 20",
                    "allowEmptyValue": false
                }
            },
            "title": "OrderRequestModel"
        },
        "QueryStockListModel": {
            "type": "object",
            "required": [
                "index",
                "items"
            ],
            "properties": {
                "index": {
                    "type": "number",
                    "example": 0.0,
                    "description": "API傳回資料的開始位置標記。如：當index=0時，則表示從第1條資料開始傳回結果。<br/>最大長度: 4",
                    "allowEmptyValue": false
                },
                "items": {
                    "type": "number",
                    "example": 3.0,
                    "description": "API傳回的資料條數。如：當items=10. 表示傳回10個結果。<br/>最大長度: 4",
                    "allowEmptyValue": false
                }
            },
            "title": "QueryStockListModel"
        },
        "QueryStockTransModel": {
            "type": "object",
            "required": [
                "accountnumber",
                "index",
                "items",
                "transFromDate",
                "transToDate"
            ],
            "properties": {
                "accountnumber": {
                    "type": "string",
                    "example": "***********************",
                    "description": "股票帳號。<br/> 最大長度: 34",
                    "allowEmptyValue": false
                },
                "index": {
                    "type": "number",
                    "example": 0.0,
                    "description": "API傳回資料的開始位置標記。如：當index=0時，則表示從第1條資料開始傳回結果。<br/>最大長度: 4",
                    "allowEmptyValue": false
                },
                "items": {
                    "type": "number",
                    "example": 3.0,
                    "description": "API傳回的資料條數。如：當items=10. 表示傳回10個結果。<br/>最大長度: 4",
                    "allowEmptyValue": false
                },
                "stocknumber": {
                    "type": "string",
                    "example": "00112.HK",
                    "description": "股票代碼。<br/> 最大長度: 10",
                    "allowEmptyValue": true
                },
                "transFromDate": {
                    "type": "number",
                    "example": 1.5185376E12,
                    "description": "交易記錄開始時間。<br/>最大長度: 20",
                    "allowEmptyValue": false
                },
                "transToDate": {
                    "type": "number",
                    "example": 1.568881151136E12,
                    "description": "交易記錄結束時間。<br/>最大長度: 20",
                    "allowEmptyValue": false
                }
            },
            "title": "QueryStockTransModel"
        },
        "ResultUtil": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "string"
                },
                "data": {
                    "type": "object"
                },
                "msg": {
                    "type": "string"
                }
            },
            "title": "ResultUtil"
        },
        "ResultUtil«List«StockDateIndexHistoryModel»»": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "string"
                },
                "data": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/StockDateIndexHistoryModel"
                    }
                },
                "msg": {
                    "type": "string"
                }
            },
            "title": "ResultUtil«List«StockDateIndexHistoryModel»»"
        },
        "ResultUtil«List«StockIndexHistoryModel»»": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "string"
                },
                "data": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/StockIndexHistoryModel"
                    }
                },
                "msg": {
                    "type": "string"
                }
            },
            "title": "ResultUtil«List«StockIndexHistoryModel»»"
        },
        "ResultUtil«List«StockInformationModel»»": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "string"
                },
                "data": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/StockInformationModel"
                    }
                },
                "msg": {
                    "type": "string"
                }
            },
            "title": "ResultUtil«List«StockInformationModel»»"
        },
        "ResultUtil«List«StockOrderInfoModel»»": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "string"
                },
                "data": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/StockOrderInfoModel"
                    }
                },
                "msg": {
                    "type": "string"
                }
            },
            "title": "ResultUtil«List«StockOrderInfoModel»»"
        },
        "ResultUtil«List«StockPlatFormLogModel»»": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "string"
                },
                "data": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/StockPlatFormLogModel"
                    }
                },
                "msg": {
                    "type": "string"
                }
            },
            "title": "ResultUtil«List«StockPlatFormLogModel»»"
        },
        "ResultUtil«StockHoldingInfoModel»": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "string"
                },
                "data": {
                    "$ref": "#/definitions/StockHoldingInfoModel"
                },
                "msg": {
                    "type": "string"
                }
            },
            "title": "ResultUtil«StockHoldingInfoModel»"
        },
        "ResultUtil«StockInformationModel»": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "string"
                },
                "data": {
                    "$ref": "#/definitions/StockInformationModel"
                },
                "msg": {
                    "type": "string"
                }
            },
            "title": "ResultUtil«StockInformationModel»"
        },
        "ResultUtil«StockOrderInfoModel»": {
            "type": "object",
            "properties": {
                "code": {
                    "type": "string"
                },
                "data": {
                    "$ref": "#/definitions/StockOrderInfoModel"
                },
                "msg": {
                    "type": "string"
                }
            },
            "title": "ResultUtil«StockOrderInfoModel»"
        },
        "StockCodeModel": {
            "type": "object",
            "required": [
                "stockcode"
            ],
            "properties": {
                "stockcode": {
                    "type": "string",
                    "example": "00112.HK",
                    "description": "股票代碼。<br/> 最大長度: 10",
                    "allowEmptyValue": false
                }
            },
            "title": "StockCodeModel"
        },
        "StockDateCSI300IndexModel": {
            "type": "object",
            "properties": {
                "selectDate": {
                    "type": "string",
                    "example": "1month",
                    "description": "Query time span,only allow input 1month,3months,6months,1year,3years.<br>最大長度: 20</br>",
                    "allowEmptyValue": false
                },
                "type": {
                    "type": "string",
                    "example": "CSI300",
                    "description": "Index type.<br>最大長度: 50</br>",
                    "allowEmptyValue": true
                }
            },
            "title": "StockDateCSI300IndexModel"
        },
        "StockDateDJIIndexModel": {
            "type": "object",
            "properties": {
                "selectDate": {
                    "type": "string",
                    "example": "1month",
                    "description": "Query time span,only allow input 1month,3months,6months,1year,3years.<br>最大長度: 20</br>",
                    "allowEmptyValue": false
                },
                "type": {
                    "type": "string",
                    "example": "DJI",
                    "description": "Index type.<br>最大長度: 50</br>",
                    "allowEmptyValue": true
                }
            },
            "title": "StockDateDJIIndexModel"
        },
        "StockDateHSIIndexModel": {
            "type": "object",
            "properties": {
                "selectDate": {
                    "type": "string",
                    "example": "1month",
                    "description": "Query time span,only allow input 1month,3months,6months,1year,3years.<br>最大長度: 20</br>",
                    "allowEmptyValue": false
                },
                "type": {
                    "type": "string",
                    "example": "HSI",
                    "description": "Index type.<br>最大長度: 50</br>",
                    "allowEmptyValue": true
                }
            },
            "title": "StockDateHSIIndexModel"
        },
        "StockDateIXICIndexModel": {
            "type": "object",
            "properties": {
                "selectDate": {
                    "type": "string",
                    "example": "1month",
                    "description": "Query time span,only allow input 1month,3months,6months,1year,3years.<br>最大長度: 20</br>",
                    "allowEmptyValue": false
                },
                "type": {
                    "type": "string",
                    "example": "IXIC",
                    "description": "Index type.<br>最大長度: 50</br>",
                    "allowEmptyValue": true
                }
            },
            "title": "StockDateIXICIndexModel"
        },
        "StockDateIndexHistoryModel": {
            "type": "object",
            "properties": {
                "createDate": {
                    "type": "string",
                    "example": "2018年1月2日",
                    "description": "Index create date.<br>最大長度: 50</br>",
                    "allowEmptyValue": false
                },
                "high": {
                    "type": "number",
                    "example": 30515.31,
                    "description": "The highest index.<br>最大長度: 18</br>",
                    "allowEmptyValue": false
                },
                "id": {
                    "type": "integer",
                    "format": "int32",
                    "example": 1,
                    "description": "id.<br>最大長度: 20</br>",
                    "allowEmptyValue": false
                },
                "last": {
                    "type": "number",
                    "example": 30515.31,
                    "description": "Last index.<br>最大長度: 18</br>",
                    "allowEmptyValue": false
                },
                "low": {
                    "type": "number",
                    "example": 30028.29,
                    "description": "The lowest index.<br>最大長度: 18</br>",
                    "allowEmptyValue": false
                },
                "open": {
                    "type": "number",
                    "example": 30028.29,
                    "description": "Opening index.<br>最大長度: 18</br>",
                    "allowEmptyValue": false
                },
                "type": {
                    "type": "string",
                    "example": "HSI",
                    "description": "Index type.<br>最大長度: 50</br>",
                    "allowEmptyValue": false
                },
                "updateTime": {
                    "type": "number",
                    "example": 1.5148224E12,
                    "description": "Index update date.<br>最大長度: 20</br>",
                    "allowEmptyValue": false
                }
            },
            "title": "StockDateIndexHistoryModel"
        },
        "StockDateSSECIndexModel": {
            "type": "object",
            "properties": {
                "selectDate": {
                    "type": "string",
                    "example": "1month",
                    "description": "Query time span,only allow input 1month,3months,6months,1year,3years.<br>最大長度: 20</br>",
                    "allowEmptyValue": false
                },
                "type": {
                    "type": "string",
                    "example": "SSEC",
                    "description": "Index type.<br>最大長度: 50</br>",
                    "allowEmptyValue": true
                }
            },
            "title": "StockDateSSECIndexModel"
        },
        "StockHoldingEnquiryModel": {
            "type": "object",
            "required": [
                "stkaccountnumber"
            ],
            "properties": {
                "stkaccountnumber": {
                    "type": "string",
                    "example": "***********************",
                    "description": "股票帳號。<br/> 最大長度: 34",
                    "allowEmptyValue": false
                },
                "stockcode": {
                    "type": "string",
                    "example": "00112.HK",
                    "description": "股票代碼。<br/> 最大長度: 10",
                    "allowEmptyValue": true
                }
            },
            "title": "StockHoldingEnquiryModel"
        },
        "StockHoldingInfoModel": {
            "type": "object",
            "properties": {
                "settlementaccount": {
                    "type": "string",
                    "example": "***********************",
                    "description": "Settlement Account Number.</br> 最大長度: 34",
                    "allowEmptyValue": false
                },
                "stkaccountnumber": {
                    "type": "string",
                    "example": "***********************",
                    "description": "A stock account number of the customer.<br/> 最大長度: 34",
                    "allowEmptyValue": false
                },
                "stkholdlist": {
                    "type": "array",
                    "items": {
                        "$ref": "#/definitions/StockInvestmentModel"
                    }
                },
                "totalInvestmentAmount": {
                    "type": "string",
                    "example": 100,
                    "description": "total investment amount.<br>最大長度: 18</br>",
                    "allowEmptyValue": false
                },
                "totalNetGainLoss": {
                    "type": "string",
                    "example": 100,
                    "description": "total NetGain Loss.<br>最大長度: 18</br>",
                    "allowEmptyValue": false
                },
                "totalNetGainLossPct": {
                    "type": "string",
                    "example": 100,
                    "description": "total NetGain LossPct.<br>最大長度: 18</br>",
                    "allowEmptyValue": false
                },
                "totalmarkervalue": {
                    "type": "string",
                    "description": "total market value.<br>最大長度: 18</br>",
                    "allowEmptyValue": false
                }
            },
            "title": "StockHoldingInfoModel"
        },
        "StockIndexHistoryModel": {
            "type": "object",
            "properties": {
                "createDate": {
                    "type": "string",
                    "example": "2018年1月2日",
                    "description": "Index create date.<br>最大長度: 50</br>",
                    "allowEmptyValue": false
                },
                "high": {
                    "type": "number",
                    "example": 30515.31,
                    "description": "The highest index.<br>最大長度: 18</br>",
                    "allowEmptyValue": false
                },
                "id": {
                    "type": "integer",
                    "format": "int32",
                    "example": 1,
                    "description": "id.<br>最大長度: 20</br>",
                    "allowEmptyValue": false
                },
                "last": {
                    "type": "number",
                    "example": 30515.31,
                    "description": "Last index.<br>最大長度: 18</br>",
                    "allowEmptyValue": false
                },
                "low": {
                    "type": "number",
                    "example": 30028.29,
                    "description": "The lowest index.<br>最大長度: 18</br>",
                    "allowEmptyValue": false
                },
                "open": {
                    "type": "number",
                    "example": 30028.29,
                    "description": "Opening index.<br>最大長度: 18</br>",
                    "allowEmptyValue": false
                },
                "type": {
                    "type": "string",
                    "example": "HSI",
                    "description": "Index type.<br>最大長度: 50</br>",
                    "allowEmptyValue": false
                },
                "upDown": {
                    "type": "number",
                    "example": -645.89,
                    "description": "升跌.<br>最大長度: 20</br>",
                    "allowEmptyValue": false
                },
                "upDownPercentage": {
                    "type": "string",
                    "example": "-2.2%",
                    "description": "升跌(%).<br>最大長度: 20</br>",
                    "allowEmptyValue": false
                },
                "upDownWeek": {
                    "type": "string",
                    "example": "-1.2%",
                    "description": "本週升跌(%).<br>最大長度: 20</br>",
                    "allowEmptyValue": false
                },
                "updateTime": {
                    "type": "number",
                    "example": 1.5148224E12,
                    "description": "Index update date.<br>最大長度: 20</br>",
                    "allowEmptyValue": false
                }
            },
            "title": "StockIndexHistoryModel"
        },
        "StockIndexModel": {
            "type": "object",
            "properties": {
                "selectDate": {
                    "type": "string",
                    "example": 1618217381883,
                    "description": "Query date.<br>最大長度: 20</br>",
                    "allowEmptyValue": true
                }
            },
            "title": "StockIndexModel"
        },
        "StockInformationModel": {
            "type": "object",
            "properties": {
                "buyprice": {
                    "type": "number",
                    "example": 698.0,
                    "description": "Buying price.<br>最大長度: 18</br>",
                    "allowEmptyValue": false
                },
                "changed": {
                    "type": "string",
                    "example": 1.000,
                    "description": "Changed in stocks.<br>最大長度: 18</br>",
                    "allowEmptyValue": false
                },
                "changedpercent": {
                    "type": "string",
                    "example": 0.143,
                    "description": "Percentage increase in stocks.<br>最大長度: 18</br>",
                    "allowEmptyValue": false
                },
                "eps": {
                    "type": "number",
                    "example": 11.01,
                    "description": "Earnings per share.<br>最大長度: 18</br>",
                    "allowEmptyValue": false
                },
                "high": {
                    "type": "number",
                    "example": 722.0,
                    "description": "The highest stock price of the day.<br>最大長度: 18</br>",
                    "allowEmptyValue": false
                },
                "hs_tech": {
                    "type": "string",
                    "example": "N",
                    "description": "恆生科技指數.<br>最大長度: 18</br>",
                    "allowEmptyValue": false
                },
                "hscei": {
                    "type": "string",
                    "example": "N",
                    "description": "國企指數.<br>最大長度: 18</br>",
                    "allowEmptyValue": false
                },
                "hsi": {
                    "type": "string",
                    "example": "Y",
                    "description": "恆生指數.<br>最大長度: 18</br>",
                    "allowEmptyValue": false
                },
                "id": {
                    "type": "integer",
                    "format": "int32",
                    "example": 1,
                    "description": "Unique id.<br>最大長度: 20</br>",
                    "allowEmptyValue": false
                },
                "industry": {
                    "type": "string",
                    "example": "Property Investment",
                    "description": "Industry.<br>最大長度: 20</br>",
                    "allowEmptyValue": false
                },
                "lastprice": {
                    "type": "number",
                    "example": 698.0,
                    "description": "The latest price.<br>最大長度: 18</br>",
                    "allowEmptyValue": false
                },
                "lasttradingday": {
                    "type": "number",
                    "example": 1.589944839E12,
                    "description": "Last trading day.<br>最大長度: 20</br>",
                    "allowEmptyValue": false
                },
                "lotsize": {
                    "type": "number",
                    "example": 100.0,
                    "description": "Stock lot size.<br>最大長度: 18</br>",
                    "allowEmptyValue": false
                },
                "low": {
                    "type": "number",
                    "example": 692.5,
                    "description": "The lowest stock price of the day.<br>最大長度: 18</br>",
                    "allowEmptyValue": false
                },
                "marketcap": {
                    "type": "string",
                    "description": "MarketCap.<br>最大長度: 18</br>",
                    "allowEmptyValue": false
                },
                "open": {
                    "type": "number",
                    "example": 709.5,
                    "description": "Stock the opening.<br>最大長度: 18</br>",
                    "allowEmptyValue": false
                },
                "pe": {
                    "type": "number",
                    "description": "PE.<br>最大長度: 18</br>",
                    "allowEmptyValue": false
                },
                "previousclose": {
                    "type": "number",
                    "example": 697.0,
                    "description": "Latest closing price.<br>最大長度: 18</br>",
                    "allowEmptyValue": false
                },
                "ratio": {
                    "type": "number",
                    "example": 63.37,
                    "description": "Ratio.<br>最大長度: 18</br>",
                    "allowEmptyValue": false
                },
                "sellprice": {
                    "type": "number",
                    "example": 698.5,
                    "description": "Selling price.<br>最大長度: 18</br>",
                    "allowEmptyValue": false
                },
                "shsc": {
                    "type": "string",
                    "example": "Y",
                    "description": "滬港通/深港通.<br>最大長度: 18</br>",
                    "allowEmptyValue": false
                },
                "stockcode": {
                    "type": "string",
                    "example": "00700.HK",
                    "description": "Stock code.<br>最大長度: 10</br>",
                    "allowEmptyValue": false
                },
                "stockname": {
                    "type": "string",
                    "example": "TENCENT",
                    "description": "Stock name.<br>最大長度: 140</br>",
                    "allowEmptyValue": false
                },
                "tradingpoint": {
                    "type": "number",
                    "example": 0.5,
                    "description": "Trading posts.<br>最大長度: 18</br>",
                    "allowEmptyValue": false
                },
                "turnover": {
                    "type": "number",
                    "example": 1.631104690434E10,
                    "description": "Stock turnover.<br>最大長度: 18</br>",
                    "allowEmptyValue": false
                },
                "volume": {
                    "type": "number",
                    "example": 2.3080003E7,
                    "description": "Stock trading volume.<br>最大長度: 18</br>",
                    "allowEmptyValue": false
                },
                "yield": {
                    "type": "string",
                    "description": "Yield.<br>最大長度: 18</br>",
                    "allowEmptyValue": false
                }
            },
            "title": "StockInformationModel"
        },
        "StockInvestmentModel": {
            "type": "object",
            "properties": {
                "accountnumber": {
                    "type": "string",
                    "example": "***********************",
                    "description": "A fund account number. <br/> 最大長度: 34",
                    "allowEmptyValue": false
                },
                "availableshare": {
                    "type": "number",
                    "example": 1000.0,
                    "description": "Tradable share.<br>最大長度: 18</br>",
                    "allowEmptyValue": false
                },
                "averageprice": {
                    "type": "number",
                    "example": 60.0,
                    "description": "Stock average price.<br>最大長度: 18</br>",
                    "allowEmptyValue": false
                },
                "currencycode": {
                    "type": "string",
                    "example": "HKD",
                    "description": "Currency code.<br>最大長度: 3</br>",
                    "allowEmptyValue": false
                },
                "investmentAmount": {
                    "type": "number",
                    "example": 500.0,
                    "description": "Investment amount.<br>最大長度: 18</br>",
                    "allowEmptyValue": false
                },
                "lastupdatedate": {
                    "type": "number",
                    "example": 1.5994944E12,
                    "description": "Last update date.<br>最大長度: 20</br>",
                    "allowEmptyValue": false
                },
                "marketprice": {
                    "type": "number",
                    "example": 500.0,
                    "description": "Market price.<br>最大長度: 18</br>",
                    "allowEmptyValue": false
                },
                "marketvalue": {
                    "type": "number",
                    "example": 50000.0,
                    "description": "Market value.<br>最大長度: 18</br>",
                    "allowEmptyValue": false
                },
                "netGainLoss": {
                    "type": "number",
                    "example": 3000.0,
                    "description": "Net income loss.<br>最大長度: 18</br>",
                    "allowEmptyValue": false
                },
                "netGainLossPct": {
                    "type": "string",
                    "example": 300,
                    "description": "Net gain Loss Pct.<br>最大長度: 18</br>",
                    "allowEmptyValue": false
                },
                "sharesholdingno": {
                    "type": "number",
                    "example": 1000.0,
                    "description": "Total shareholding number.<br>最大長度: 18</br>",
                    "allowEmptyValue": false
                },
                "stockcode": {
                    "type": "string",
                    "example": "00700.HK",
                    "description": "A unique code to identify a stock. <br/> 最大長度: 10",
                    "allowEmptyValue": false
                },
                "stockname": {
                    "type": "string",
                    "example": "TENCENT",
                    "description": "Stock name.<br>最大長度: 140</br>",
                    "allowEmptyValue": false
                }
            },
            "title": "StockInvestmentModel"
        },
        "StockModel": {
            "type": "object",
            "properties": {
                "hs_tech": {
                    "type": "string",
                    "example": "N",
                    "description": "恆生科技指數.<br>最大長度: 18</br>",
                    "allowEmptyValue": false
                },
                "hscei": {
                    "type": "string",
                    "example": "N",
                    "description": "國企指數.<br>最大長度: 18</br>",
                    "allowEmptyValue": false
                },
                "hsi": {
                    "type": "string",
                    "example": "Y",
                    "description": "恆生指數.<br>最大長度: 18</br>",
                    "allowEmptyValue": false
                },
                "items": {
                    "type": "number",
                    "example": 3.0,
                    "description": "the number of items you'd like to get in the response result.<br/>最大長度: 4",
                    "allowEmptyValue": false
                },
                "shsc": {
                    "type": "string",
                    "example": "Y",
                    "description": "滬港通/深港通.<br>最大長度: 18</br>",
                    "allowEmptyValue": false
                }
            },
            "title": "StockModel"
        },
        "StockOrderInfoModel": {
            "type": "object",
            "properties": {
                "currencycode": {
                    "type": "string",
                    "example": "HKD",
                    "description": "Currency code.<br>最大長度: 3</br>",
                    "allowEmptyValue": false
                },
                "custodycharges": {
                    "type": "number",
                    "example": 20.0,
                    "description": "The safekeeping fee.<br>最大長度: 18</br>",
                    "allowEmptyValue": false
                },
                "expirydate": {
                    "type": "number",
                    "example": 1.589944839E12,
                    "description": "Expiration time.<br>最大長度: 20</br>",
                    "allowEmptyValue": false
                },
                "id": {
                    "type": "integer",
                    "format": "int32",
                    "example": 1,
                    "description": "Unique id.<br>最大長度: 20</br>",
                    "allowEmptyValue": false
                },
                "lastupdatedate": {
                    "type": "number",
                    "example": 1.589944839E12,
                    "description": "Last updated date.<br>最大長度: 20</br>",
                    "allowEmptyValue": false
                },
                "operationdate": {
                    "type": "number",
                    "example": 1.589944839E12,
                    "description": "Operation date.<br>最大長度: 20</br>",
                    "allowEmptyValue": false
                },
                "operationreasons": {
                    "type": "string",
                    "description": "Operation reason.<br>最大長度: 140</br>",
                    "allowEmptyValue": false
                },
                "ordertype": {
                    "type": "string",
                    "example": "Market Price",
                    "description": "Stock order type,Allowed value:M​​arket Price /Fix Price<br>最大長度: 20</br>",
                    "allowEmptyValue": false
                },
                "requesttime": {
                    "type": "number",
                    "example": 1.589944839E12,
                    "description": "Request time.<br>最大長度: 20</br>",
                    "allowEmptyValue": false
                },
                "settlementaccount": {
                    "type": "string",
                    "example": "***********************",
                    "description": "Settlement account number.<br>最大長度: 34</br>",
                    "allowEmptyValue": false
                },
                "sharingno": {
                    "type": "number",
                    "example": 1000.0,
                    "description": "Buy and sell shares.<br>最大長度: 18</br>",
                    "allowEmptyValue": false
                },
                "status": {
                    "type": "string",
                    "description": "Status.<br>最大長度: 4</br>",
                    "allowEmptyValue": false
                },
                "statuscode": {
                    "type": "string",
                    "example": 1,
                    "description": "Status code.<br>最大長度: 10</br>",
                    "allowEmptyValue": false
                },
                "stockaccountnumber": {
                    "type": "string",
                    "example": "***********************",
                    "description": "Stock account number.<br>最大長度: 34</br>",
                    "allowEmptyValue": false
                },
                "stockcode": {
                    "type": "string",
                    "example": "00700.HK",
                    "description": "Stock code.<br>最大長度: 10</br>",
                    "allowEmptyValue": false
                },
                "stockname": {
                    "type": "string",
                    "example": "TENCENT",
                    "description": "Stock name.<br>最大長度: 140</br>",
                    "allowEmptyValue": false
                },
                "tradingamount": {
                    "type": "number",
                    "example": 5200.0,
                    "description": "Transaction amount.<br>最大長度: 18</br>",
                    "allowEmptyValue": false
                },
                "tradingcommission": {
                    "type": "number",
                    "example": 5.0,
                    "description": "The trading commissions.<br>最大長度: 18</br>",
                    "allowEmptyValue": false
                },
                "tradingoption": {
                    "type": "string",
                    "example": "Buy",
                    "description": "Trading Options (Buy/Sell)<br>最大長度: 10</br>",
                    "allowEmptyValue": false
                },
                "tradingprice": {
                    "type": "number",
                    "example": 52.0,
                    "description": "The stock price.<br>最大長度: 18</br>",
                    "allowEmptyValue": false
                },
                "transactionamount": {
                    "type": "number",
                    "example": 100.0,
                    "description": "Deduction/amount received.<br>最大長度: 18</br>",
                    "allowEmptyValue": false
                }
            },
            "title": "StockOrderInfoModel"
        },
        "StockPlatFormLogModel": {
            "type": "object",
            "properties": {
                "accountnumber": {
                    "type": "string",
                    "example": "***********************",
                    "description": "Stock account Number </br> 最大長度: 34",
                    "allowEmptyValue": false
                },
                "branchcode": {
                    "type": "string",
                    "example": "0001",
                    "description": "Bank branch code.<br>最大長度: 20</br>",
                    "allowEmptyValue": false
                },
                "clearingcode": {
                    "type": "string",
                    "example": "0001",
                    "description": "Bank clearing code.<br>最大長度: 20</br>",
                    "allowEmptyValue": false
                },
                "countrycode": {
                    "type": "string",
                    "example": "HK",
                    "description": "Country code.<br>最大長度: 2</br>",
                    "allowEmptyValue": false
                },
                "custodycharges": {
                    "type": "number",
                    "example": 20.0,
                    "description": "The safekeeping fee.<br>最大長度: 18</br>",
                    "allowEmptyValue": false
                },
                "id": {
                    "type": "integer",
                    "format": "int32",
                    "example": 1,
                    "description": "Unique id.<br>最大長度: 20</br>",
                    "allowEmptyValue": false
                },
                "sharingno": {
                    "type": "number",
                    "example": 1000.0,
                    "description": "Buy and sell shares.<br>最大長度: 18</br>",
                    "allowEmptyValue": false
                },
                "stocknumber": {
                    "type": "string",
                    "example": "00112.HK",
                    "description": "stock code <br/> 最大長度: 10",
                    "allowEmptyValue": false
                },
                "stocktrdingamount": {
                    "type": "number",
                    "example": 3000.0,
                    "description": "Stock trading amount.<br>最大長度: 18</br>",
                    "allowEmptyValue": false
                },
                "stocktrdingcommission": {
                    "type": "number",
                    "example": 10.0,
                    "description": "Stock transaction commission.<br>最大長度: 18</br>",
                    "allowEmptyValue": false
                },
                "tradingoption": {
                    "type": "string",
                    "example": "Buy",
                    "description": "Trading Options (Buy/Sell).<br>最大長度: 10</br>",
                    "allowEmptyValue": false
                },
                "transactionamount": {
                    "type": "number",
                    "example": 25000.0,
                    "description": "The transaction amount.<br>最大長度: 18</br>",
                    "allowEmptyValue": false
                },
                "transactiondate": {
                    "type": "number",
                    "example": 1.589944839E12,
                    "description": "The transaction date.<br>最大長度: 20</br>",
                    "allowEmptyValue": false
                }
            },
            "title": "StockPlatFormLogModel"
        },
        "StockTradingModel": {
            "type": "object",
            "required": [
                "orderType",
                "sharingNo",
                "stkaccountnumber",
                "stockCode",
                "tradingOption"
            ],
            "properties": {
                "expiryDate": {
                    "type": "number",
                    "example": 1.563033599999E12,
                    "description": "股票交易訂單過期時間，適用於orderType（交易類型）是Fix Price的情況。當orderType（交易類型）是Market Price的時候，請保持該參數為空值。<br/>最大長度: 20",
                    "allowEmptyValue": true
                },
                "orderType": {
                    "type": "string",
                    "example": "Fix Price",
                    "description": "股票訂單交易類型。支援的值：Market Price（以市價交易），Fix Price（依照客戶設定的固定價格交易) <br/> 最大長度: 20",
                    "allowEmptyValue": false
                },
                "sharingNo": {
                    "type": "string",
                    "example": 2000,
                    "description": "股票份額。例如當要買入或賣出股票的時候，需要填入賣出多少份額。股票份額必須是整手，例如100，200，是該隻股票單筆交易份額的整數倍。
                    "allowEmptyValue": false
                },
                "stkaccountnumber": {
                    "type": "string",
                    "example": "***********************",
                    "description": "股票帳號。<br/> 最大長度: 34",
                    "allowEmptyValue": false
                },
                "stockCode": {
                    "type": "string",
                    "example": "00112.HK",
                    "description": "股票代碼。<br/> 最大長度: 10",
                    "allowEmptyValue": false
                },
                "tradingOption": {
                    "type": "string",
                    "example": "Buy",
                    "description": "買入或賣出。支援的值：Buy（表示買入）， Sell（表示賣出)<br/> 最大長度: 10",
                    "allowEmptyValue": false
                },
                "tradingPrice": {
                    "type": "string",
                    "example": 5.17,
                    "description": "股票交易價格。適用於orderType（交易類型）是Fix Price的情況。當orderType（交易類型）是Market Price的時候，請保持該參數為空值。系統直接就會按照市場價格做交易了。
                    "allowEmptyValue": true
                }
            },
            "title": "StockTradingModel"
        },
        "UpdateOrderRequestModel": {
            "type": "object",
            "required": [
                "id"
            ],
            "properties": {
                "expiryDate": {
                    "type": "number",
                    "example": 1.563033599999E12,
                    "description": "股票交易訂單過期時間，適用於orderType（交易類型）是Fix Price的情況。當orderType（交易類型）是Market Price的時候，請保持該參數為空值。<br/>最大長度: 20",
                    "allowEmptyValue": true
                },
                "id": {
                    "type": "number",
                    "example": 2.0,
                    "description": "股票訂單編號。當您呼叫買入或賣出股票申請API之後，就會傳回一個股票訂單編號。<br>最大長度: 20</br>",
                    "allowEmptyValue": false
                },
                "sharingno": {
                    "type": "string",
                    "example": 2000,
                    "description": "股票份額。例如當要買入或賣出股票的時候，需要填入賣出多少份額。<br/>最大長度: 18",
                    "allowEmptyValue": true
                },
                "tradingprice": {
                    "type": "string",
                    "example": 7.2,
                    "description": "股票交易價格。適用於orderType（交易類型）是Fix Price的情況。當orderType（交易類型）是Market Price的時候，請保持該參數為空值。系統直接就會按照市場價格做交易了。
                    "allowEmptyValue": true
                }
            },
            "title": "UpdateOrderRequestModel"
        },
        "UpdateOrderStatusModel": {
            "type": "object",
            "required": [
                "id",
                "status"
            ],
            "properties": {
                "id": {
                    "type": "number",
                    "example": 2.0,
                    "description": "股票訂單編號。當您呼叫買入或賣出股票申請API之後，就會傳回一個股票訂單編號。<br>最大長度: 20</br>",
                    "allowEmptyValue": false
                },
                "operationreasons": {
                    "type": "string",
                    "description": "模擬股票成功或失敗的時候，可以添加原因說明。例如當模擬交易失敗的時候，可以添加說明”假日不支援交易“等等。<br/> 最大長度: 140",
                    "allowEmptyValue": true
                },
                "status": {
                    "type": "string",
                    "example": 1,
                    "description": "用於模擬模擬股票交易成功或失敗的狀態參數。當Status設定為1時，表示模擬該筆股票交易成功。當Status設定為2時，表示模擬該筆股票交易失敗。<br /> 最大長度: 20",
                    "allowEmptyValue": false
                }
            },
            "title": "UpdateOrderStatusModel"
        },
        "UpdateSettlementAccountModel": {
            "type": "object",
            "required": [
                "newsettleaccountnumber",
                "stkaccountnumber"
            ],
            "properties": {
                "newsettleaccountnumber": {
                    "type": "string",
                    "example": "***********************",
                    "description": "為該股票帳戶綁定的新的儲蓄帳戶（001或002結尾的帳戶類型)，用於交易中的扣款或收款服務。<br/> 最大長度: 34",
                    "allowEmptyValue": false
                },
                "stkaccountnumber": {
                    "type": "string",
                    "example": "***********************",
                    "description": "股票帳號。<br/> 最大長度: 34",
                    "allowEmptyValue": false
                }
            },
            "title": "UpdateSettlementAccountModel"
        }
    }
}
