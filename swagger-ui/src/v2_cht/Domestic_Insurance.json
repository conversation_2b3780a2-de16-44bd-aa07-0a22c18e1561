{"swagger": "2.0", "info": {"description": "APIs here are all about insurance service. Before calling these APIs, please make sure you’ve learnt the business logic between these APIs.", "version": "1.0", "title": "第三人保險服務", "contact": {}}, "schemes": ["https"], "host": "simnectz.hsu.edu.hk/vis", "basePath": "/", "tags": [{"name": "客戶角色", "description": "客戶購買產品保險以及發起理賠申請的流程。"}, {"name": "核賠員角色", "description": "保險人支付被保險人或受益人保險金。"}, {"name": "調查員角色", "description": "保險人對損失的性質、過程、原因、程度以及保險事故的責任認定。如果不需要索賠調查，下一步進入損失評估流程。"}, {"name": "定責員角色", "description": "確定保險標的的實際責任和損失的過程。"}, {"name": "理賠員角色", "description": "確定保險標的的實際責任和損失的過程。"}, {"name": "出納角色", "description": "保險人支付被保險人或受益人保險金。"}, {"name": "質檢角色", "description": "整個理賠過程完成後，將對整個案件的處理過程進行質檢評分。"}], "paths": {"/api/allToken": {"get": {"tags": ["出納角色", "定責員角色", "核賠員角色", "理賠員角色", "調查員角色", "質檢角色", "客戶角色"], "summary": " 此API用於所有使用者登入保險系統時取得token，做身分認證。", "description": "該系統所有角色登入的時候，都需要呼叫該API取得token，完成身分認證。", "operationId": "getTokenByCardIDUsingGET", "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.", "required": true, "type": "string", "default": "QFTM1V9wSLtHt6IaIiL3neAsymh1aqtSdGJ5wFCEhmODQjgtcQWKloMGHiHCTIndAeYML3rmxtJxddFR7U+erKMzgLkfOyN8NMMR5QOYSS3X2EcN+dno14KzZIV1lt3VX+emKzPW5bZYI5P0oEXs/Lne7fqGXMyypax+5Bdik7M="}, {"name": "cardId", "in": "query", "description": "使用者身分證ID. eg:610124199205242152</br>核賠員ID:*********</br>調查員ID:*********</br>定責員ID:*********</br>理賠員ID :*********</br>出納ID:*********</br>質檢員ID:*********</br><br>maxLength: 18</br>", "required": true, "type": "string"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/response?string?"}}}}}, "/api/audit/cashierdetail": {"get": {"tags": ["出納角色"], "summary": "該API可以讓出納人員查看某個保險理賠案件的詳細內容。", "description": "出納人員在完成賠付轉帳之前，可能需要調該API去查看該理賠案件的相關內容。", "operationId": "cashierdetailUsingGET", "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.", "required": true, "type": "string", "default": "QFTM1V9wSLtHt6IaIiL3neAsymh1aqtSdGJ5wFCEhmODQjgtcQWKloMGHiHCTIndAeYML3rmxtJxddFR7U+erKMzgLkfOyN8NMMR5QOYSS3X2EcN+dno14KzZIV1lt3VX+emKzPW5bZYI5P0oEXs/Lne7fqGXMyypax+5Bdik7M="}, {"name": "reportNo", "in": "query", "description": "案件編號. eg:LP86671672394116486<br>maxLength: 50</br>", "required": true, "type": "string"}], "responses": {"-10001": {"description": "System error"}, "200": {"description": "success", "schema": {"$ref": "#/definitions/response?object?"}}, "40008": {"description": "The reportNo is not exist"}}}}, "/api/audit/cashiers": {"get": {"tags": ["出納角色"], "summary": "該API可以讓出納人員根據身份證ID篩選要查看的保險申請案件列表信息，然後對該申請進行轉賬賠付。", "description": "在呼叫此API之前，請確保您已經呼叫/api/audit/paymentdetail完成了賠付金額最終確定的環節。", "operationId": "cashiersUsingGET", "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.", "required": true, "type": "string", "default": "QFTM1V9wSLtHt6IaIiL3neAsymh1aqtSdGJ5wFCEhmODQjgtcQWKloMGHiHCTIndAeYML3rmxtJxddFR7U+erKMzgLkfOyN8NMMR5QOYSS3X2EcN+dno14KzZIV1lt3VX+emKzPW5bZYI5P0oEXs/Lne7fqGXMyypax+5Bdik7M="}, {"in": "body", "name": "cardId", "description": "身分證ID. eg:610124199205242152<br>maxLength: 18</br>", "required": false, "schema": {"type": "string"}}], "responses": {"-10001": {"description": "System error"}, "200": {"description": "success", "schema": {"$ref": "#/definitions/response?object?"}}, "40008": {"description": "The reportNo is not exist"}}}}, "/api/audit/claimdetail": {"get": {"tags": ["核賠員角色"], "summary": "此API用於讓審核人員查看理賠申請的詳細內容。", "description": "在呼叫該API之前，請確保您已經呼叫/api/audit/reports 取得到了使用者提交的保險理賠清單。", "operationId": "claimDetailUsingGET", "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.", "required": true, "type": "string", "default": "QFTM1V9wSLtHt6IaIiL3neAsymh1aqtSdGJ5wFCEhmODQjgtcQWKloMGHiHCTIndAeYML3rmxtJxddFR7U+erKMzgLkfOyN8NMMR5QOYSS3X2EcN+dno14KzZIV1lt3VX+emKzPW5bZYI5P0oEXs/Lne7fqGXMyypax+5Bdik7M="}, {"name": "reportNo", "in": "query", "description": "案件編號. eg:LP86671672394116486<br>maxLength: 50</br>", "required": true, "type": "string"}], "responses": {"-10001": {"description": "System error"}, "200": {"description": "success", "schema": {"$ref": "#/definitions/response?object?"}}, "40008": {"description": "The reportNo is not exist"}}}}, "/api/audit/duty": {"post": {"tags": ["定責員角色"], "summary": " 此API可讓定責員根據上一流程中審核人提交的審核意見，給予自己的責任審核意見。", "description": " 查看理賠案例中上一個流程的處理結果。", "operationId": "dutyUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.", "required": true, "type": "string", "default": "QFTM1V9wSLtHt6IaIiL3neAsymh1aqtSdGJ5wFCEhmODQjgtcQWKloMGHiHCTIndAeYML3rmxtJxddFR7U+erKMzgLkfOyN8NMMR5QOYSS3X2EcN+dno14KzZIV1lt3VX+emKzPW5bZYI5P0oEXs/Lne7fqGXMyypax+5Bdik7M="}, {"in": "body", "name": "dutyDto", "description": "dutyDto", "required": true, "schema": {"$ref": "#/definitions/DutyDto"}}], "responses": {"-10001": {"description": "System error"}, "200": {"description": "success", "schema": {"$ref": "#/definitions/response?object?"}}, "40008": {"description": "The reportNo is not exist"}}}}, "/api/audit/dutydetail": {"get": {"tags": ["定責員角色"], "summary": "此API可請定責員根據保險理賠編號查看資料詳情及上一流程中審核人員提交的審核意見。", "description": "在開始做責任審定之前，您需要呼叫該API以了解該理賠案例的相關內容。", "operationId": "dutydetailUsingGET", "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.", "required": true, "type": "string", "default": "QFTM1V9wSLtHt6IaIiL3neAsymh1aqtSdGJ5wFCEhmODQjgtcQWKloMGHiHCTIndAeYML3rmxtJxddFR7U+erKMzgLkfOyN8NMMR5QOYSS3X2EcN+dno14KzZIV1lt3VX+emKzPW5bZYI5P0oEXs/Lne7fqGXMyypax+5Bdik7M="}, {"name": "reportNo", "in": "query", "description": "案件編號. eg:LP86671672394116486<br>maxLength: 50</br>", "required": true, "type": "string"}], "responses": {"-10001": {"description": "System error"}, "200": {"description": "success", "schema": {"$ref": "#/definitions/response?object?"}}, "40008": {"description": "The reportNo is not exist"}}}}, "/api/audit/payment": {"post": {"tags": ["出納角色"], "summary": "此API可讓出納員根據最終要賠償的金額和受益人帳號，進行轉帳賠償。", "description": " ", "operationId": "paymentUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.", "required": true, "type": "string", "default": "QFTM1V9wSLtHt6IaIiL3neAsymh1aqtSdGJ5wFCEhmODQjgtcQWKloMGHiHCTIndAeYML3rmxtJxddFR7U+erKMzgLkfOyN8NMMR5QOYSS3X2EcN+dno14KzZIV1lt3VX+emKzPW5bZYI5P0oEXs/Lne7fqGXMyypax+5Bdik7M="}, {"in": "body", "name": "paymentDto", "description": "paymentDto", "required": true, "schema": {"$ref": "#/definitions/PaymentDto"}}], "responses": {"-10001": {"description": "System error"}, "200": {"description": "success", "schema": {"$ref": "#/definitions/response?object?"}}, "40008": {"description": "The reportNo is not exist"}}}}, "/api/audit/paymentcheck": {"post": {"tags": ["理賠員角色"], "summary": "此API讓理賠員根據保險理賠案件中所有環節的審定結果，做出最終的賠付金額計算。", "description": "理賠員責任審定的詳情，對案件進行審核理算。", "operationId": "paymentcheckUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.", "required": true, "type": "string", "default": "QFTM1V9wSLtHt6IaIiL3neAsymh1aqtSdGJ5wFCEhmODQjgtcQWKloMGHiHCTIndAeYML3rmxtJxddFR7U+erKMzgLkfOyN8NMMR5QOYSS3X2EcN+dno14KzZIV1lt3VX+emKzPW5bZYI5P0oEXs/Lne7fqGXMyypax+5Bdik7M="}, {"in": "body", "name": "repaymentCheckDto", "description": "repaymentCheckDto", "required": true, "schema": {"$ref": "#/definitions/RepaymentCheckDto"}}], "responses": {"-10001": {"description": "System error"}, "200": {"description": "success", "schema": {"$ref": "#/definitions/response?object?"}}, "40008": {"description": "The reportNo is not exist"}}}}, "/api/audit/paymentdetail": {"get": {"tags": ["理賠員角色"], "summary": "此API用於查看保險理賠案例中，理賠員提交的最終理算方案及賠償金額。", "description": "在呼叫此API之前，請確保您已經呼叫了/api/audit/paymentcheck 完成了賠付金額計算流程。", "operationId": "paymentdetailUsingGET", "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.", "required": true, "type": "string", "default": "QFTM1V9wSLtHt6IaIiL3neAsymh1aqtSdGJ5wFCEhmODQjgtcQWKloMGHiHCTIndAeYML3rmxtJxddFR7U+erKMzgLkfOyN8NMMR5QOYSS3X2EcN+dno14KzZIV1lt3VX+emKzPW5bZYI5P0oEXs/Lne7fqGXMyypax+5Bdik7M="}, {"name": "reportNo", "in": "query", "description": "案件編號. eg:LP86671672394116486<br>maxLength: 50</br>", "required": true, "type": "string"}], "responses": {"-10001": {"description": "System error"}, "200": {"description": "success", "schema": {"$ref": "#/definitions/response?object?"}}, "40008": {"description": "The reportNo is not exist"}}}}, "/api/audit/qualitydetail": {"get": {"tags": ["質檢角色"], "summary": "此API可請質檢員根據保險理賠申請案件編號查看該理賠案件詳情。", "description": " 在理賠案件質檢評分之前，您需要先查看理賠過程的資料。 ", "operationId": "qualitydetailUsingGET", "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.", "required": true, "type": "string", "default": "QFTM1V9wSLtHt6IaIiL3neAsymh1aqtSdGJ5wFCEhmODQjgtcQWKloMGHiHCTIndAeYML3rmxtJxddFR7U+erKMzgLkfOyN8NMMR5QOYSS3X2EcN+dno14KzZIV1lt3VX+emKzPW5bZYI5P0oEXs/Lne7fqGXMyypax+5Bdik7M="}, {"name": "reportNo", "in": "query", "description": "案件編號. eg:LP86671672394116486<br>maxLength: 50</br>", "required": true, "type": "string"}], "responses": {"-10001": {"description": "System error"}, "200": {"description": "success", "schema": {"$ref": "#/definitions/response?object?"}}, "40008": {"description": "The reportNo is not exist"}}}}, "/api/audit/rejectclaim": {"post": {"tags": ["定責員角色"], "summary": "此API可讓定責員拒絕理賠。", "description": "對不符合理賠條件的理賠申請，定責員可以做出拒絕理賠的操作。", "operationId": "rejectclaimUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.", "required": true, "type": "string", "default": "QFTM1V9wSLtHt6IaIiL3neAsymh1aqtSdGJ5wFCEhmODQjgtcQWKloMGHiHCTIndAeYML3rmxtJxddFR7U+erKMzgLkfOyN8NMMR5QOYSS3X2EcN+dno14KzZIV1lt3VX+emKzPW5bZYI5P0oEXs/Lne7fqGXMyypax+5Bdik7M="}, {"in": "body", "name": "dutyDto", "description": "dutyDto", "required": true, "schema": {"$ref": "#/definitions/DutyDto"}}], "responses": {"-10001": {"description": "System error"}, "200": {"description": "success", "schema": {"$ref": "#/definitions/response?object?"}}, "40008": {"description": "The reportNo is not exist"}}}}, "/api/audit/rejectecheck": {"post": {"tags": ["核賠員角色"], "summary": "此API用於讓審核人員查看理賠申請的詳細內容。", "description": "在呼叫該API之前，請確保您已經呼叫/api/audit/reports 取得到了使用者提交的保險理賠清單。", "operationId": "rejecteCheckUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.", "required": true, "type": "string", "default": "QFTM1V9wSLtHt6IaIiL3neAsymh1aqtSdGJ5wFCEhmODQjgtcQWKloMGHiHCTIndAeYML3rmxtJxddFR7U+erKMzgLkfOyN8NMMR5QOYSS3X2EcN+dno14KzZIV1lt3VX+emKzPW5bZYI5P0oEXs/Lne7fqGXMyypax+5Bdik7M="}, {"in": "body", "name": "checkDto", "description": "checkDto", "required": true, "schema": {"$ref": "#/definitions/CheckDto"}}], "responses": {"-10001": {"description": "System error"}, "200": {"description": "success", "schema": {"$ref": "#/definitions/response?object?"}}, "40007": {"description": "The reportNo cannot be empty"}, "40008": {"description": "The reportNo is not exist"}}}}, "/api/audit/reports": {"get": {"tags": ["定責員角色", "理賠員角色", "調查員角色", "核賠員角色"], "summary": "此API可取得所有使用者提交的保險申請列表，並支援透過身分證號碼檢索相關的保險理賠記錄。", "description": "在呼叫此API之前，請確保您已經提交了保險理賠申請。", "operationId": "reportsUsingGET", "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.", "required": true, "type": "string", "default": "QFTM1V9wSLtHt6IaIiL3neAsymh1aqtSdGJ5wFCEhmODQjgtcQWKloMGHiHCTIndAeYML3rmxtJxddFR7U+erKMzgLkfOyN8NMMR5QOYSS3X2EcN+dno14KzZIV1lt3VX+emKzPW5bZYI5P0oEXs/Lne7fqGXMyypax+5Bdik7M="}, {"name": "cardId", "in": "query", "description": "身分證ID. eg:610124199205242152<br>maxLength: 18</br>", "required": false, "type": "string"}], "responses": {"-10001": {"description": "System error"}, "200": {"description": "success", "schema": {"$ref": "#/definitions/response?object?"}}}}}, "/api/audit/singlecheck": {"post": {"tags": ["核賠員角色"], "summary": " 此API可讓審核人員審核使用者提交的理賠申請資料。", "description": "在呼叫該API之前，您可能需要先呼叫/api/user/getpolicy 和 /api/audit/claimdetail 去獲取更多的投保和理賠資料詳情。", "operationId": "singleCheckUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.", "required": true, "type": "string", "default": "QFTM1V9wSLtHt6IaIiL3neAsymh1aqtSdGJ5wFCEhmODQjgtcQWKloMGHiHCTIndAeYML3rmxtJxddFR7U+erKMzgLkfOyN8NMMR5QOYSS3X2EcN+dno14KzZIV1lt3VX+emKzPW5bZYI5P0oEXs/Lne7fqGXMyypax+5Bdik7M="}, {"in": "body", "name": "checkDto", "description": "checkDto", "required": true, "schema": {"$ref": "#/definitions/CheckDto"}}], "responses": {"-10001": {"description": "System error"}, "200": {"description": "success", "schema": {"$ref": "#/definitions/response?object?"}}, "40007": {"description": "The reportNo cannot be empty"}, "40008": {"description": "The reportNo is not exist"}}}}, "/api/audit/survey": {"post": {"tags": ["調查員角色"], "summary": "此API旨在支持調查員記錄調查結果及其評論。", "description": "當使用者提交的保險理賠申請材料存在疑點時，保險公司會啟動更進一步的現場調查流程，蒐集更多信息，最終根據調查結果，給出審核意見。 ", "operationId": "surveyUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.", "required": true, "type": "string", "default": "QFTM1V9wSLtHt6IaIiL3neAsymh1aqtSdGJ5wFCEhmODQjgtcQWKloMGHiHCTIndAeYML3rmxtJxddFR7U+erKMzgLkfOyN8NMMR5QOYSS3X2EcN+dno14KzZIV1lt3VX+emKzPW5bZYI5P0oEXs/Lne7fqGXMyypax+5Bdik7M="}, {"in": "body", "name": "surveyDto", "description": "surveyDto", "required": true, "schema": {"$ref": "#/definitions/SurveyDto"}}], "responses": {"-10001": {"description": "System error"}, "200": {"description": "success", "schema": {"$ref": "#/definitions/response?object?"}}, "40008": {"description": "The reportNo is not exist"}}}}, "/api/audit/surveydetail": {"get": {"tags": ["調查員角色"], "summary": "此API旨在允許使用者去查看調查員的訪談結果和評論。", "description": "在呼叫此API之前，請確保您已經呼叫了/api/audit/survey API完成了現場勘察審核意見的提交。", "operationId": "surveyDetailUsingGET", "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.", "required": true, "type": "string", "default": "QFTM1V9wSLtHt6IaIiL3neAsymh1aqtSdGJ5wFCEhmODQjgtcQWKloMGHiHCTIndAeYML3rmxtJxddFR7U+erKMzgLkfOyN8NMMR5QOYSS3X2EcN+dno14KzZIV1lt3VX+emKzPW5bZYI5P0oEXs/Lne7fqGXMyypax+5Bdik7M="}, {"name": "reportNo", "in": "query", "description": "案件編號. eg:LP86671672394116486<br>maxLength: 50</br>", "required": true, "type": "string"}], "responses": {"-10001": {"description": "System error"}, "200": {"description": "success", "schema": {"$ref": "#/definitions/response?object?"}}, "40008": {"description": "The reportNo is not exist"}}}}, "/api/quality/getReport": {"get": {"tags": ["質檢角色"], "summary": "此API可請質檢員根據保險理賠申請案件編號查看該理賠案件的審核狀態。", "description": " 已經完成理賠流程處理的案件才可以進行質檢的流程。 ", "operationId": "getReportUsingGET", "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.", "required": true, "type": "string", "default": "QFTM1V9wSLtHt6IaIiL3neAsymh1aqtSdGJ5wFCEhmODQjgtcQWKloMGHiHCTIndAeYML3rmxtJxddFR7U+erKMzgLkfOyN8NMMR5QOYSS3X2EcN+dno14KzZIV1lt3VX+emKzPW5bZYI5P0oEXs/Lne7fqGXMyypax+5Bdik7M="}, {"name": "reportNo", "in": "query", "description": "reportNo", "required": false, "type": "string"}], "responses": {"-10001": {"description": "System error"}, "200": {"description": "success", "schema": {"$ref": "#/definitions/response?object?"}}}}}, "/api/quality/reports": {"get": {"tags": ["質檢角色"], "summary": "質檢員查看的案件清單", "description": "此API可請質檢員依照身分證ID篩選查詢待質檢的理賠申請案件清單。", "operationId": "reportsUsingGET_1", "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.", "required": true, "type": "string", "default": "QFTM1V9wSLtHt6IaIiL3neAsymh1aqtSdGJ5wFCEhmODQjgtcQWKloMGHiHCTIndAeYML3rmxtJxddFR7U+erKMzgLkfOyN8NMMR5QOYSS3X2EcN+dno14KzZIV1lt3VX+emKzPW5bZYI5P0oEXs/Lne7fqGXMyypax+5Bdik7M="}, {"in": "body", "name": "cardId", "description": "身分證ID. eg:610124199205242152<br>maxLength: 18</br>", "required": false, "schema": {"type": "string"}}], "responses": {"-10001": {"description": "System error"}, "200": {"description": "success", "schema": {"$ref": "#/definitions/response?object?"}}}}}, "/api/quality/score": {"post": {"tags": ["質檢角色"], "summary": "此API可讓質檢員根據理賠過程中的各個環節的審核處理過程進行滿意度評分，並最終得出一個總分。", "description": "在呼叫此API之前，請確保該理賠申請案處理流程已經完成。", "operationId": "scoreUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.", "required": true, "type": "string", "default": "QFTM1V9wSLtHt6IaIiL3neAsymh1aqtSdGJ5wFCEhmODQjgtcQWKloMGHiHCTIndAeYML3rmxtJxddFR7U+erKMzgLkfOyN8NMMR5QOYSS3X2EcN+dno14KzZIV1lt3VX+emKzPW5bZYI5P0oEXs/Lne7fqGXMyypax+5Bdik7M="}, {"in": "body", "name": "qualityDto", "description": "qualityDto", "required": true, "schema": {"$ref": "#/definitions/QualityDto"}}], "responses": {"-10001": {"description": "System error"}, "200": {"description": "success", "schema": {"$ref": "#/definitions/response?object?"}}}}}, "/api/user/claim": {"post": {"tags": ["客戶角色"], "summary": " 此API可讓使用者發起理賠申請並提交理賠資料。 ", "description": "在調用此API之前，請確保您已經通過調用保險購買相關的API成功的購買了保險。並且在理賠申請發起之前，第一時間調用了/api/user/report完成了理賠報案。", "operationId": "claimUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.", "required": true, "type": "string", "default": "QFTM1V9wSLtHt6IaIiL3neAsymh1aqtSdGJ5wFCEhmODQjgtcQWKloMGHiHCTIndAeYML3rmxtJxddFR7U+erKMzgLkfOyN8NMMR5QOYSS3X2EcN+dno14KzZIV1lt3VX+emKzPW5bZYI5P0oEXs/Lne7fqGXMyypax+5Bdik7M="}, {"name": "id", "in": "query", "required": false, "type": "string"}, {"name": "reportNo", "in": "query", "required": false, "type": "string"}, {"name": "name", "in": "query", "required": false, "type": "string"}, {"name": "gender", "in": "query", "required": false, "type": "string"}, {"name": "birthdayDate", "in": "query", "required": false, "type": "string"}, {"name": "cardType", "in": "query", "required": false, "type": "string"}, {"name": "cardId", "in": "query", "required": false, "type": "string"}, {"name": "claimItem", "in": "query", "required": false, "type": "string"}, {"name": "accidentDetail", "in": "query", "required": false, "type": "string"}, {"name": "claimAmount", "in": "query", "required": false, "type": "string"}, {"name": "accountNumber", "in": "query", "required": false, "type": "string"}, {"name": "lossPath", "in": "query", "required": false, "type": "string"}, {"name": "invoiceCasePath", "in": "query", "required": false, "type": "string"}, {"name": "relationshipPath", "in": "query", "required": false, "type": "string"}, {"name": "delegate<PERSON><PERSON>", "in": "query", "required": false, "type": "string"}, {"name": "createDate", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "updateDate", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "closeDate", "in": "query", "required": false, "type": "string", "format": "date-time"}, {"name": "status", "in": "query", "required": false, "type": "string"}, {"name": "userID", "in": "query", "required": false, "type": "string"}, {"name": "idcards", "in": "query", "description": "idcards", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "insurances", "in": "query", "description": "insurances", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "invoices", "in": "query", "description": "invoices", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "relationships", "in": "query", "description": "relationships", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}, {"name": "delegaties", "in": "query", "description": "delegaties", "required": true, "type": "array", "items": {"type": "string"}, "collectionFormat": "multi"}], "responses": {"-10001": {"description": "System error"}, "200": {"description": "success", "schema": {"$ref": "#/definitions/response?object?"}}}}}, "/api/user/claim/progress": {"get": {"tags": ["客戶角色"], "summary": "此API可以查看目前登入使用者提交的理賠申請處理進度。", "description": "在呼叫該API之前，請確保您已經呼叫/api/user/claim 提交了理賠申請。", "operationId": "progressUsingGET", "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.", "required": true, "type": "string", "default": "QFTM1V9wSLtHt6IaIiL3neAsymh1aqtSdGJ5wFCEhmODQjgtcQWKloMGHiHCTIndAeYML3rmxtJxddFR7U+erKMzgLkfOyN8NMMR5QOYSS3X2EcN+dno14KzZIV1lt3VX+emKzPW5bZYI5P0oEXs/Lne7fqGXMyypax+5Bdik7M="}, {"name": "reportNo", "in": "query", "description": "案件編號. eg:LP86671672394116486<br>maxLength: 50</br>", "required": true, "type": "string"}], "responses": {"-10001": {"description": "System error"}, "200": {"description": "success", "schema": {"$ref": "#/definitions/response?object?"}}}}}, "/api/user/claimdetail": {"get": {"tags": ["客戶角色"], "summary": "此API可以查看目前登入使用者提交的理賠詳情。", "description": "在呼叫該API之前，請確保您已經呼叫/api/user/myclaim 取得理賠申請清單。", "operationId": "claimdetailUsingGET", "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.", "required": true, "type": "string", "default": "QFTM1V9wSLtHt6IaIiL3neAsymh1aqtSdGJ5wFCEhmODQjgtcQWKloMGHiHCTIndAeYML3rmxtJxddFR7U+erKMzgLkfOyN8NMMR5QOYSS3X2EcN+dno14KzZIV1lt3VX+emKzPW5bZYI5P0oEXs/Lne7fqGXMyypax+5Bdik7M="}, {"name": "reportNo", "in": "query", "description": "案件編號. eg:LP86671672394116486<br>maxLength: 50</br>", "required": true, "type": "string"}], "responses": {"-10001": {"description": "System error"}, "200": {"description": "success", "schema": {"$ref": "#/definitions/response?object?"}}}}}, "/api/user/getpolicy": {"get": {"tags": ["核賠員角色"], "summary": "此API可讓審核人員取得使用者的保單號碼清單。", "description": "當審核人員在審核保險理賠的過程中，需要根據使用者提供的身分證號碼查詢該使用者在保險公司內部的保單號碼。 ", "operationId": "getPolicyUsingGET", "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.", "required": true, "type": "string", "default": "QFTM1V9wSLtHt6IaIiL3neAsymh1aqtSdGJ5wFCEhmODQjgtcQWKloMGHiHCTIndAeYML3rmxtJxddFR7U+erKMzgLkfOyN8NMMR5QOYSS3X2EcN+dno14KzZIV1lt3VX+emKzPW5bZYI5P0oEXs/Lne7fqGXMyypax+5Bdik7M="}, {"name": "cardId", "in": "query", "description": "身分證ID. eg:610124199205242152<br>maxLength: 18</br>", "required": true, "type": "string"}], "responses": {"-10001": {"description": "System error"}, "200": {"description": "success", "schema": {"$ref": "#/definitions/response?object?"}}}}}, "/api/user/message": {"get": {"tags": ["客戶角色"], "summary": "此API可讓目前登入使用者接收系統所傳送的補充理賠資料的提示訊息。", "description": "當使用者提交理賠申請之後，如果核賠員認定提交的資料不足，那麼就需要呼叫該API去提醒使用者完成資料的補充。", "operationId": "messageUsingGET", "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.", "required": true, "type": "string", "default": "QFTM1V9wSLtHt6IaIiL3neAsymh1aqtSdGJ5wFCEhmODQjgtcQWKloMGHiHCTIndAeYML3rmxtJxddFR7U+erKMzgLkfOyN8NMMR5QOYSS3X2EcN+dno14KzZIV1lt3VX+emKzPW5bZYI5P0oEXs/Lne7fqGXMyypax+5Bdik7M="}], "responses": {"-10001": {"description": "System error"}, "200": {"description": "success", "schema": {"$ref": "#/definitions/response?object?"}}}}}, "/api/user/myclaim": {"get": {"tags": ["客戶角色"], "summary": "此API可以查看目前登入使用者提交的理賠申請清單。", "description": "在呼叫該API之前，請確保您已經呼叫/api/user/claim 提交了理賠申請。", "operationId": "myclaimsUsingGET", "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.", "required": true, "type": "string", "default": "QFTM1V9wSLtHt6IaIiL3neAsymh1aqtSdGJ5wFCEhmODQjgtcQWKloMGHiHCTIndAeYML3rmxtJxddFR7U+erKMzgLkfOyN8NMMR5QOYSS3X2EcN+dno14KzZIV1lt3VX+emKzPW5bZYI5P0oEXs/Lne7fqGXMyypax+5Bdik7M="}, {"name": "cardId", "in": "query", "description": "身分證ID. eg:610124199205242152<br>maxLength: 18</br>", "required": true, "type": "string"}], "responses": {"-10001": {"description": "System error"}, "200": {"description": "success", "schema": {"$ref": "#/definitions/response?object?"}}}}}, "/api/user/paylog": {"get": {"tags": ["客戶角色"], "summary": "此API可以查看目前登入使用者已成功投保的保險購買記錄。", "description": "在呼叫此API之前，請確保您已經透過呼叫保險購買相關的API成功的購買了保險。", "operationId": "paylogUsingGET", "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.", "required": true, "type": "string", "default": "QFTM1V9wSLtHt6IaIiL3neAsymh1aqtSdGJ5wFCEhmODQjgtcQWKloMGHiHCTIndAeYML3rmxtJxddFR7U+erKMzgLkfOyN8NMMR5QOYSS3X2EcN+dno14KzZIV1lt3VX+emKzPW5bZYI5P0oEXs/Lne7fqGXMyypax+5Bdik7M="}], "responses": {"-10001": {"description": "System error"}, "200": {"description": "success", "schema": {"$ref": "#/definitions/response?object?"}}}}}, "/api/user/report": {"post": {"tags": ["客戶角色"], "summary": "此API用於讓目前登入使用者啟動理賠報案。", "description": "在呼叫/api/user/claim API發起理賠申請資料提交之前，您需要先呼叫此API完成理賠報案。", "operationId": "reportUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.", "required": true, "type": "string", "default": "QFTM1V9wSLtHt6IaIiL3neAsymh1aqtSdGJ5wFCEhmODQjgtcQWKloMGHiHCTIndAeYML3rmxtJxddFR7U+erKMzgLkfOyN8NMMR5QOYSS3X2EcN+dno14KzZIV1lt3VX+emKzPW5bZYI5P0oEXs/Lne7fqGXMyypax+5Bdik7M="}, {"in": "body", "name": "tbReport", "description": "tbReport", "required": true, "schema": {"$ref": "#/definitions/TbReport"}}], "responses": {"-10001": {"description": "System error"}, "200": {"description": "success", "schema": {"$ref": "#/definitions/response?object?"}}}}}, "/insurance/application": {"post": {"tags": ["客戶角色"], "summary": "此API用於申請旅遊保險產品。", "description": "在申請旅遊保險產品之前，您可能需要調用/insurance/travelInsuranceQuote獲得報價。客戶可以為多人購買保險產品，因此在同一報價單中，需要輸入所有投保人資訊。", "operationId": "applyInsuranceUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.", "required": true, "type": "string", "default": "QFTM1V9wSLtHt6IaIiL3neAsymh1aqtSdGJ5wFCEhmODQjgtcQWKloMGHiHCTIndAeYML3rmxtJxddFR7U+erKMzgLkfOyN8NMMR5QOYSS3X2EcN+dno14KzZIV1lt3VX+emKzPW5bZYI5P0oEXs/Lne7fqGXMyypax+5Bdik7M="}, {"in": "body", "name": "applyInsuranceModel", "description": "applyInsuranceModel", "required": true, "schema": {"$ref": "#/definitions/ApplyInsuranceModel"}}], "responses": {"-10001": {"description": "System error"}, "200": {"description": "success", "schema": {"$ref": "#/definitions/response"}}}}}, "/insurance/retrievalProductDetail": {"post": {"tags": ["客戶角色"], "summary": "此API用於查詢旅遊保險產品的詳細資料。", "description": "在呼叫該API去查詢一個產品詳情之前，您可能需要呼叫/insurance/retrievalProductList API去取得產品清單。", "operationId": "retrievalProductDetailUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.", "required": true, "type": "string", "default": "QFTM1V9wSLtHt6IaIiL3neAsymh1aqtSdGJ5wFCEhmODQjgtcQWKloMGHiHCTIndAeYML3rmxtJxddFR7U+erKMzgLkfOyN8NMMR5QOYSS3X2EcN+dno14KzZIV1lt3VX+emKzPW5bZYI5P0oEXs/Lne7fqGXMyypax+5Bdik7M="}, {"in": "body", "name": "productDetailModel", "description": "productDetailModel", "required": true, "schema": {"$ref": "#/definitions/ProductDetailModel"}}], "responses": {"-10001": {"description": "System error"}, "200": {"description": "success", "schema": {"$ref": "#/definitions/response"}}}}}, "/insurance/retrievalProductList": {"post": {"tags": ["客戶角色"], "summary": "此API用於查詢旅遊保險產品清單。", "description": "查詢保險產品的清單資訊", "operationId": "retrievalProductListUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.", "required": true, "type": "string", "default": "QFTM1V9wSLtHt6IaIiL3neAsymh1aqtSdGJ5wFCEhmODQjgtcQWKloMGHiHCTIndAeYML3rmxtJxddFR7U+erKMzgLkfOyN8NMMR5QOYSS3X2EcN+dno14KzZIV1lt3VX+emKzPW5bZYI5P0oEXs/Lne7fqGXMyypax+5Bdik7M="}], "responses": {"-10001": {"description": "System error"}, "200": {"description": "success", "schema": {"$ref": "#/definitions/response"}}}}}, "/insurance/subscription": {"post": {"tags": ["客戶角色"], "summary": "此API用於購買旅遊保險產品。", "description": "在購買旅遊保險之前，您需要呼叫/insurance/application去申請保險產品。", "operationId": "subscriptionInsuranceUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.", "required": true, "type": "string", "default": "QFTM1V9wSLtHt6IaIiL3neAsymh1aqtSdGJ5wFCEhmODQjgtcQWKloMGHiHCTIndAeYML3rmxtJxddFR7U+erKMzgLkfOyN8NMMR5QOYSS3X2EcN+dno14KzZIV1lt3VX+emKzPW5bZYI5P0oEXs/Lne7fqGXMyypax+5Bdik7M="}, {"in": "body", "name": "subscriptionModel", "description": "subscriptionModel", "required": true, "schema": {"$ref": "#/definitions/InsuranceSubscriptionModel"}}], "responses": {"-10001": {"description": "System error"}, "200": {"description": "success", "schema": {"$ref": "#/definitions/response"}}}}}, "/insurance/travelInsuranceQuote": {"post": {"tags": ["客戶角色"], "summary": "此API用於購買旅遊保險產品。", "description": "在購買旅遊保險之前，您需要呼叫/insurance/application去申請保險產品。", "operationId": "travelInsuranceQuoteUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "The most recent customer authorization token to third party app to access customer data from a bank, in JWT format.", "required": true, "type": "string", "default": "QFTM1V9wSLtHt6IaIiL3neAsymh1aqtSdGJ5wFCEhmODQjgtcQWKloMGHiHCTIndAeYML3rmxtJxddFR7U+erKMzgLkfOyN8NMMR5QOYSS3X2EcN+dno14KzZIV1lt3VX+emKzPW5bZYI5P0oEXs/Lne7fqGXMyypax+5Bdik7M="}, {"in": "body", "name": "travelInsuranceQuoteModel", "description": "travelInsuranceQuoteModel", "required": true, "schema": {"$ref": "#/definitions/TravelInsuranceQuoteModel"}}], "responses": {"-10001": {"description": "System error"}, "200": {"description": "success", "schema": {"$ref": "#/definitions/response"}}}}}}, "definitions": {"ApplyInsuranceChildModel": {"type": "object", "required": ["dateofbirth", "gender", "givenname", "idnumber", "idtype", "relationship", "surnamename"], "properties": {"address": {"type": "string"}, "dateofbirth": {"type": "string", "example": *************, "description": "Birth day of the insured person.<br>maxLength: 20</br>"}, "email": {"type": "string"}, "gender": {"type": "string", "example": "M", "description": "Gender of the insured person.<br>maxLength: 1</br>"}, "givenname": {"type": "string", "example": "Yun", "description": "The given name printed on HKID card or Passport of the insured person.<br>maxLength: 20</br>"}, "idnumber": {"type": "string", "example": 610425197504240001, "description": "Number of  HKID card or Passport of the insured person.<br>maxLength: 35</br>"}, "idtype": {"type": "string", "example": "P", "description": "The type of ID you used to buy an insurance product. <br></br>Possible values: HKID card, Passport.<br>maxLength: 1</br>"}, "phoneNumber": {"type": "string"}, "relationship": {"type": "string", "example": "SELF", "description": "The relationship bewteen you and the insured person. <br></br>Possible values:SELF - Self,SPOU - Spouse,<br></br>PARE - Parent,CHIL - Child,FRIE - Friend,RELA - Relative. <br>maxLength: 4</br>"}, "surnamename": {"type": "string", "example": "Ma", "description": "The surname printed on HKID card or Passport of the insured person.<br>maxLength: 20</br>"}}, "title": "ApplyInsuranceChildModel"}, "ApplyInsuranceModel": {"type": "object", "required": ["quoteid"], "properties": {"applySub": {"type": "array", "items": {"$ref": "#/definitions/ApplyInsuranceChildModel"}}, "quoteid": {"type": "string", "example": "Q000000002", "description": "The quotation id returned after you call insurance quotation API sucessfully.<br>maxLength: 35</br>"}}, "title": "ApplyInsuranceModel"}, "CheckDto": {"type": "object", "properties": {"auditor": {"type": "string", "example": "核能人員", "description": "審核員.<br>maxLength: 50</br>"}, "checkContent": {"type": "string", "example": {"isCoverage": true, "isValid": true, "isDoubt": true}, "description": "審核內容：(1)是否在保險責任內？(2)索賠資料是否全部有效？(3)是否理賠調查？<br>maxLength: 1000</br>"}, "checkRemark": {"type": "string", "example": "備註", "description": "審核備註.<br>maxLength: 1000</br>"}, "policyId": {"type": "string", "example": "Tr000738819", "description": "保單號碼.<br>maxLength: 50</br>"}, "reportNo": {"type": "string", "example": "*******************", "description": "報案編號.<br>maxLength: 50</br>"}}, "title": "CheckDto"}, "DutyDto": {"type": "object", "properties": {"dutyJudgement": {"type": "string", "example": "已定責，通過", "description": "責任審定意見.<br>maxLength: 50</br>"}, "dutyJudger": {"type": "string", "example": "定責員", "description": "責任審定人.<br>maxLength: 50</br>"}, "reportNo": {"type": "string", "example": "*******************", "description": "報案編號.<br>maxLength: 50</br>"}}, "title": "DutyDto"}, "InsuranceSubscriptionModel": {"type": "object", "required": ["accountNumber", "accountType", "quoteid"], "properties": {"accountNumber": {"type": "string", "example": ****************, "description": "The number of the account, with which you'd like to pay for the travel insurance product.<br>maxLength: 34</br>"}, "accountType": {"type": "string", "example": "CRED", "description": "The type of the bank account that is supported in the subscription. <br></br>Possible values: CURR - Current,SAVI - Saving,CRED - Credit Card.<br>maxLength: 4</br>"}, "quoteid": {"type": "string", "example": "Q000000002", "description": "The quotation id returned after you call insurance application API sucessfully.<br>maxLength: 35</br>"}}, "title": "InsuranceSubscriptionModel"}, "PaymentDto": {"type": "object", "properties": {"amount": {"type": "number", "example": 800, "description": "賠償金額.<br>maxLength: 20</br>"}, "reportNo": {"type": "string", "example": "*******************", "description": "報案編號.<br>maxLength: 50</br>"}, "transferIn": {"type": "string", "example": "********************", "description": "轉入帳號.<br>maxLength: 50</br>"}, "transferOut": {"type": "string", "example": "********************", "description": "轉出帳號.<br>maxLength: 50</br>"}}, "title": "PaymentDto"}, "ProductDetailModel": {"type": "object", "properties": {"productcode": {"type": "string", "description": "A uniqe code to identify a dedicated travel insurance product.<br>maxLength: 35</br>"}}, "title": "ProductDetailModel"}, "QualityDto": {"type": "object", "properties": {"qualityInfo": {"$ref": "#/definitions/QualityVo"}, "reportNo": {"type": "string"}}, "title": "QualityDto"}, "QualityVo": {"type": "object", "properties": {"attitude": {"type": "string"}, "duration": {"type": "string"}, "professional": {"type": "string"}, "satisfaction": {"type": "string"}, "speed": {"type": "string"}}, "title": "QualityVo"}, "RepaymentCheckDto": {"type": "object", "properties": {"claimApprover": {"type": "string", "example": "理賠員", "description": "理賠人員.<br>maxLength: 50</br>"}, "claimFinalAmount": {"type": "number", "example": 800, "description": "最終賠償金額.<br>maxLength: 50</br>"}, "claimRemark": {"type": "string", "example": "備註", "description": "理算備註.<br>maxLength: 50</br>"}, "claimTotalAmount": {"type": "number", "example": 1000, "description": "申請理賠總金額.<br>maxLength: 50</br>"}, "invoiceTotalAmount": {"type": "number", "example": 1000, "description": "發票總金額.<br>maxLength: 50</br>"}, "lossRate": {"type": "string", "example": "80%", "description": "比例賠償方式.<br>maxLength: 50</br>"}, "reportNo": {"type": "string", "example": "*******************", "description": "報案編號.<br>maxLength: 50</br>"}}, "title": "RepaymentCheckDto"}, "SurveyDto": {"type": "object", "properties": {"reportNo": {"type": "string", "example": "*******************", "description": "報案編號.<br>maxLength: 50</br>"}, "surveyRemark": {"type": "string", "example": "備註", "description": "勘查備註.<br>maxLength: 1000</br>"}, "surveyResult": {"type": "string", "example": "已勘查，通過", "description": "勘查結果.<br>maxLength: 1000</br>"}}, "title": "SurveyDto"}, "TbReport": {"type": "object", "properties": {"applyReason": {"type": "string"}, "cardId": {"type": "string"}, "claimItem": {"type": "string"}, "closeDate": {"type": "string", "format": "date-time"}, "createDate": {"type": "string", "format": "date-time"}, "definitionKey": {"type": "string"}, "diagnosis": {"type": "string"}, "hospital": {"type": "string"}, "id": {"type": "string"}, "lossDate": {"type": "string"}, "lossDetail": {"type": "string"}, "lossLocation": {"type": "string"}, "lossLocationDetail": {"type": "string"}, "name": {"type": "string"}, "overseaLoss": {"type": "boolean"}, "phoneNumber": {"type": "string"}, "processInstanceId": {"type": "string"}, "relationship": {"type": "string"}, "remark": {"type": "string"}, "reportType": {"type": "string"}, "status": {"type": "string"}, "updateDate": {"type": "string", "format": "date-time"}}, "title": "TbReport"}, "TravelInsuranceQuoteModel": {"type": "object", "required": ["coverageType", "productCode", "startDate"], "properties": {"childrenNo": {"type": "integer", "format": "int32", "example": 1, "description": "The total number of the insured children in this insurance quotation.<br/>Possible values: 0,1,2,3,4,5,6 <br/> maxLength: 2", "allowEmptyValue": true}, "coverageType": {"type": "string", "example": "SIGL", "description": "The coverage type you set in this insurance product quotation. <br/> Possible values: <br/> SIGL - Single Trip Cover <br/>ANLC - Annual China Cover <br/>ANLG - Annual Global Cover <br/>maxLength: 4"}, "dateOfBirth": {"type": "number", "example": 978250234000, "description": "Birthday of the insured person. <br/>maxLength: 20", "allowEmptyValue": true}, "destination": {"type": "string", "example": "Mainland China and Macau", "description": "The destination of trip. <br/>Possible values: <br/>Mainland China and Macau, <br/>Asia, <br/>Worldwide <br/>maxLength: 35", "allowEmptyValue": true}, "endDate": {"type": "number", "example": 1639826636000, "description": "The expiry date of the travel insurancce product. <br/>maxLength: 20", "allowEmptyValue": true}, "friendsOrRelatives": {"type": "string", "example": "Y", "description": "If the coverageType field is set ANLC or ANLG, please keep this field empty.<br/> Possible values: Y - Yes <br/>empty value. <br/>maxLength: 1", "allowEmptyValue": true}, "friendsOrRelativesNo": {"type": "integer", "format": "int32", "example": 6, "description": "The total number of the insured friends and relatives in this single trip insurance quotation. <br/>Possible values: 0, 1,2,3,4,5,6 <br/>maxLength: 2", "allowEmptyValue": true}, "idNumber": {"type": "string", "example": 15, "description": "The number of the HKID card or passport of the insured person. <br/> maxLength: 35", "allowEmptyValue": true}, "idType": {"type": "string", "example": "P", "description": "The Id type of the insured person. <br/>Possible values:<br/>I - ID Card,<br/>P - Passport. <br/>maxLength: 1", "allowEmptyValue": true}, "myChildren": {"type": "string", "example": "Y", "description": "Please set this field value as Y if you want to buy the insurance product for your children. Otherwise, keep this field empty.” <br/>Possible values: Y - Yes, <br/>empty value <br/>maxLength: 1", "allowEmptyValue": true}, "parents": {"type": "string", "description": "If coverageType field is set SIGL, please keep this field empty.<br/>Possible values: <br/>Y - Yes<br/>empty value.<br/>maxLength: 1", "allowEmptyValue": true}, "parentsNo": {"type": "integer", "format": "int32", "description": "The total number of the insured parents in this insurance quotation <br/>Possible values: 0,1,2 <br/>maxLength: 2", "allowEmptyValue": true}, "productCode": {"type": "string", "example": "T002", "description": "A uniqe code to identify a dedicated travel insurance product. <br/>maxLength: 35"}, "self": {"type": "string", "example": "Y", "description": "If you set this field value as Y, it means you set yourself as a insured person. Otherwise, please keep this field empty.<br/>Possible values:<br/>Y - Yes,<br/>empty value <br/> maxLength: 1", "allowEmptyValue": true}, "spouse": {"type": "string", "example": "Y", "description": "If you set this field value as Y, it means you set your spouse as a insured person. Otherwise, please keep this field empty.<br/>Possible values:<br/>Y - Yes,<br/>empty value <br/> maxLength: 1", "allowEmptyValue": true}, "startDate": {"type": "number", "example": 1608290636000, "description": "The effective date of the travel insurancce product.<br/>maxLength: 20"}}, "title": "TravelInsuranceQuoteModel"}, "response": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32", "description": "code"}, "data": {"type": "object", "description": "data"}, "desc": {"type": "string", "description": "desc"}}, "title": "response"}, "response?object?": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32", "description": "code"}, "data": {"type": "object", "description": "data"}, "desc": {"type": "string", "description": "desc"}}, "title": "response?object?"}, "response?string?": {"type": "object", "properties": {"code": {"type": "integer", "format": "int32", "description": "code"}, "data": {"type": "string", "description": "data"}, "desc": {"type": "string", "description": "desc"}}, "title": "response?string?"}}}