{"swagger": "2.0", "info": {"version": "1.0", "title": "Personal Loan service", "contact": {"name": "Pim li:lihua<PERSON>@chinasofti.com"}}, "schemes": ["https"], "host": "simnectz.hsu.edu.hk/lbsgateway/personal-loan-experience", "basePath": "/", "tags": [{"name": "Loan Application", "description": "個人消費貸申請，產品和報價。"}, {"name": "Loan Contract", "description": "貸款合約資訊。"}, {"name": "Transaction", "description": "貸款處理，包括查詢和還款。"}], "paths": {"/personal/loan/application": {"post": {"tags": ["Loan Application"], "summary": "該API用於個人消費貸款的申請業務。", "description": "在調用此API申請個人消費貸款之前，您可能需要調用/personal/loan/calculator API來嘗試計算您可以藉到的最大貸款金額，以及使用不同的還款選項需要支付的金額。 ", "operationId": "applicationUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "頒發給第三方應用程式的最新客戶授權令牌，以JWT格式存取該銀行的客戶資料。當開發者在調用API存取銀行客戶資料的時候，都需要用到該令牌。", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "Message ID in 128 bit random UUID format generated uniquely for every request by F/E side.", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "client_id generated during consumer onboarding. ", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "applicationModel", "description": "applicationModel", "required": true, "schema": {"$ref": "#/definitions/ApplicationModel"}}], "responses": {"200": {"description": "Query completed successfully.(Returned By Get)", "schema": {"$ref": "#/definitions/ResultUtil"}}, "201": {"description": "Normal execution. The request has succeeded. (Returned By Post)", "schema": {"$ref": "#/definitions/ResultUtil"}}, "403": {"description": "Token has incorrect scope or a security policy was violated. Action: Please check whether you’re using the right token with the legal authorized user account."}, "404": {"description": "The requested deposit account does not exist.Action: Please make sure the account number and account type you’re inputting are correct."}, "500": {"description": "Something went wrong on the API gateway or micro-service. Action: check your network and try again later."}}}}, "/personal/loan/calculator": {"post": {"tags": ["Loan Application"], "summary": "該API是個人消費貸款計算器。", "description": "在調用/personal/loan/application API申請個人消費貸款之前，您可能需要調用此API來嘗試計算您可以藉到的最大貸款金額以及使用不同的還款選項需要支付的金額。", "operationId": "calculatorUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "頒發給第三方應用程式的最新客戶授權令牌，以JWT格式存取該銀行的客戶資料。當開發者在調用API存取銀行客戶資料的時候，都需要用到該令牌。", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "Message ID in 128 bit random UUID format generated uniquely for every request by F/E side.", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "client_id generated during consumer onboarding. ", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "calculatorModel", "description": "calculatorModel", "required": true, "schema": {"$ref": "#/definitions/CalculatorModel"}}], "responses": {"200": {"description": "Query completed successfully.(Returned By Get)", "schema": {"$ref": "#/definitions/ResultUtil"}}, "201": {"description": "Normal execution. The request has succeeded. (Returned By Post)", "schema": {"$ref": "#/definitions/ResultUtil"}}, "403": {"description": "Token has incorrect scope or a security policy was violated. Action: Please check whether you’re using the right token with the legal authorized user account."}, "404": {"description": "The requested deposit account does not exist.Action: Please make sure the account number and account type you’re inputting are correct."}, "500": {"description": "Something went wrong on the API gateway or micro-service. Action: check your network and try again later."}}}}, "/personal/loan/enquiryContractDetails": {"post": {"tags": ["Loan Contract"], "summary": "此API用於查詢個人消費貸款合約詳細資料。", "description": "在調用此API之前，請確保您已完成個人消費貸款申請流程並獲得貸款合約。", "operationId": "enquiryContractDetailsUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "頒發給第三方應用程式的最新客戶授權令牌，以JWT格式存取該銀行的客戶資料。當開發者在調用API存取銀行客戶資料的時候，都需要用到該令牌", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "Message ID in 128 bit random UUID format generated uniquely for every request by F/E side.", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "client_id generated during consumer onboarding. ", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "contractDetailEnquiryModel", "description": "contractDetailEnquiryModel", "required": true, "schema": {"$ref": "#/definitions/ContractDetailEnquiryModel"}}], "responses": {"200": {"description": "Query completed successfully.(Returned By Get)", "schema": {"$ref": "#/definitions/ResultUtil"}}, "201": {"description": "Normal execution. The request has succeeded. (Returned By Post)", "schema": {"$ref": "#/definitions/ResultUtil"}}, "403": {"description": "Token has incorrect scope or a security policy was violated. Action: Please check whether you’re using the right token with the legal authorized user account."}, "404": {"description": "The requested deposit account does not exist.Action: Please make sure the account number and account type you’re inputting are correct."}, "500": {"description": "Something went wrong on the API gateway or micro-service. Action: check your network and try again later."}}}}, "/personal/loan/enquiryContracts": {"post": {"tags": ["Loan Contract"], "summary": " 此API用於查詢某一個貸款帳戶中所有的個人消費貸款記錄清單。", "description": "在調用此API之前，請確保您已完成個人消費貸款申請流程並獲得貸款合約。", "operationId": "enquiryContractsUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "頒發給第三方應用程式的最新客戶授權令牌，以JWT格式存取該銀行的客戶資料。當開發者在調用API存取銀行客戶資料的時候，都需要用到該令牌。", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "Message ID in 128 bit random UUID format generated uniquely for every request by F/E side.", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "client_id generated during consumer onboarding. ", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "allContractsEnquiryModel", "description": "allContractsEnquiryModel", "required": true, "schema": {"$ref": "#/definitions/AllContractsEnquiryModel"}}], "responses": {"200": {"description": "Query completed successfully.(Returned By Get)", "schema": {"$ref": "#/definitions/ResultUtil"}}, "201": {"description": "Normal execution. The request has succeeded. (Returned By Post)", "schema": {"$ref": "#/definitions/ResultUtil"}}, "403": {"description": "Token has incorrect scope or a security policy was violated. Action: Please check whether you’re using the right token with the legal authorized user account."}, "404": {"description": "The requested deposit account does not exist.Action: Please make sure the account number and account type you’re inputting are correct."}, "500": {"description": "Something went wrong on the API gateway or micro-service. Action: check your network and try again later."}}}}, "/personal/loan/enquiryNextRepaymentPlan": {"post": {"tags": ["Transaction"], "summary": " 此API用於查詢個人消費貸款合約中下一期的應還金額。 ", "description": "在呼叫 /personal/loan/repayment API做還款之前，您可能需要呼叫此API來查看應還款詳細資料。", "operationId": "enquiryNextRepaymentPlanUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "頒發給第三方應用程式的最新客戶授權令牌，以JWT格式存取該銀行的客戶資料。當開發者在調用API存取銀行客戶資料的時候，都需要用到該令牌。", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "Message ID in 128 bit random UUID format generated uniquely for every request by F/E side.", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "client_id generated during consumer onboarding. ", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "contractDetailEnquiryModel", "description": "contractDetailEnquiryModel", "required": true, "schema": {"$ref": "#/definitions/ContractDetailEnquiryModel"}}], "responses": {"200": {"description": "Query completed successfully.(Returned By Get)", "schema": {"$ref": "#/definitions/ResultUtil"}}, "201": {"description": "Normal execution. The request has succeeded. (Returned By Post)", "schema": {"$ref": "#/definitions/ResultUtil"}}, "403": {"description": "Token has incorrect scope or a security policy was violated. Action: Please check whether you’re using the right token with the legal authorized user account."}, "404": {"description": "The requested deposit account does not exist.Action: Please make sure the account number and account type you’re inputting are correct."}, "500": {"description": "Something went wrong on the API gateway or micro-service. Action: check your network and try again later."}}}}, "/personal/loan/enquiryOverDueContractDetails": {"post": {"tags": ["Transaction"], "summary": "此API用於查詢個人消費貸款合約中逾期未還的金額。", "description": "如果您有任何逾期還款，在呼叫/personal/loan/repayment API進行還款之前，您可能需要呼叫此API查看還款詳情，包括罰息。", "operationId": "enquiryOverDueContractDetailsUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "頒發給第三方應用程式的最新客戶授權令牌，以JWT格式存取該銀行的客戶資料。當開發者在調用API存取銀行客戶資料的時候，都需要用到該令牌。", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "Message ID in 128 bit random UUID format generated uniquely for every request by F/E side.", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "client_id generated during consumer onboarding. ", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "contractDetailEnquiryModel", "description": "contractDetailEnquiryModel", "required": true, "schema": {"$ref": "#/definitions/ContractDetailEnquiryModel"}}], "responses": {"200": {"description": "Query completed successfully.(Returned By Get)", "schema": {"$ref": "#/definitions/ResultUtil"}}, "201": {"description": "Normal execution. The request has succeeded. (Returned By Post)", "schema": {"$ref": "#/definitions/ResultUtil"}}, "403": {"description": "Token has incorrect scope or a security policy was violated. Action: Please check whether you’re using the right token with the legal authorized user account."}, "404": {"description": "The requested deposit account does not exist.Action: Please make sure the account number and account type you’re inputting are correct."}, "500": {"description": "Something went wrong on the API gateway or micro-service. Action: check your network and try again later."}}}}, "/personal/loan/enquiryOverDueHistory": {"post": {"tags": ["Transaction"], "summary": " 此API用於查詢個人消費貸款合約中逾期未還的歷史記錄。", "description": "如果您有任何逾期還款，在調用/personal/loan/repayment API 進行還款之前，您可能需要調用此API來查看所有逾期還款歷史記錄。", "operationId": "enquiryOverDueHistoryUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "頒發給第三方應用程式的最新客戶授權令牌，以JWT格式存取該銀行的客戶資料。當開發者在調用API存取銀行客戶資料的時候，都需要用到該令牌。", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "Message ID in 128 bit random UUID format generated uniquely for every request by F/E side.", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "client_id generated during consumer onboarding. ", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "overDueHistoryEnquiryModel", "description": "overDueHistoryEnquiryModel", "required": true, "schema": {"$ref": "#/definitions/OverDueHistoryEnquiryModel"}}], "responses": {"200": {"description": "Query completed successfully.(Returned By Get)", "schema": {"$ref": "#/definitions/ResultUtil"}}, "201": {"description": "Normal execution. The request has succeeded. (Returned By Post)", "schema": {"$ref": "#/definitions/ResultUtil"}}, "403": {"description": "Token has incorrect scope or a security policy was violated. Action: Please check whether you’re using the right token with the legal authorized user account."}, "404": {"description": "The requested deposit account does not exist.Action: Please make sure the account number and account type you’re inputting are correct."}, "500": {"description": "Something went wrong on the API gateway or micro-service. Action: check your network and try again later."}}}}, "/personal/loan/loanProcess": {"post": {"tags": ["Loan Application"], "summary": "此API用於模擬個人消費貸款的核准流程。", "description": "在調用此API之前，請確保您已調用/personal/loan/application提交貸款申請並獲取申請id。", "operationId": "loanProcessUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "頒發給第三方應用程式的最新客戶授權令牌，以JWT格式存取該銀行的客戶資料。當開發者在調用API存取銀行客戶資料的時候，都需要用到該令牌。", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "Message ID in 128 bit random UUID format generated uniquely for every request by F/E side.", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "client_id generated during consumer onboarding. ", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "loanProcessModel", "description": "loanProcessModel", "required": true, "schema": {"$ref": "#/definitions/LoanProcessModel"}}], "responses": {"200": {"description": "Query completed successfully.(Returned By Get)", "schema": {"$ref": "#/definitions/ResultUtil"}}, "201": {"description": "Normal execution. The request has succeeded. (Returned By Post)", "schema": {"$ref": "#/definitions/ResultUtil"}}, "403": {"description": "Token has incorrect scope or a security policy was violated. Action: Please check whether you’re using the right token with the legal authorized user account."}, "404": {"description": "The requested deposit account does not exist.Action: Please make sure the account number and account type you’re inputting are correct."}, "500": {"description": "Something went wrong on the API gateway or micro-service. Action: check your network and try again later."}}}}, "/personal/loan/queryProduct": {"post": {"tags": ["Loan Application"], "summary": "此API用於查詢個人消費貸款產品資訊。", "description": "在呼叫/personal/loan/application API申請個人消費貸款產品之前，您可能需要呼叫此API來查看貸款產品資訊。", "operationId": "queryProductUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "頒發給第三方應用程式的最新客戶授權令牌，以JWT格式存取該銀行的客戶資料。當開發者在調用API存取銀行客戶資料的時候，都需要用到該令牌。", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "Message ID in 128 bit random UUID format generated uniquely for every request by F/E side.", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "client_id generated during consumer onboarding. ", "required": true, "type": "string", "default": "devin"}], "responses": {"200": {"description": "Query completed successfully.(Returned By Get)", "schema": {"$ref": "#/definitions/ResultUtil"}}, "201": {"description": "Normal execution. The request has succeeded. (Returned By Post)", "schema": {"$ref": "#/definitions/ResultUtil"}}, "403": {"description": "Token has incorrect scope or a security policy was violated. Action: Please check whether you’re using the right token with the legal authorized user account."}, "404": {"description": "The requested deposit account does not exist.Action: Please make sure the account number and account type you’re inputting are correct."}, "500": {"description": "Something went wrong on the API gateway or micro-service. Action: check your network and try again later."}}}}, "/personal/loan/repayment": {"post": {"tags": ["Transaction"], "summary": "此API用於個人消費貸款的還款作業。", "description": "在調用此API之前，請確保您已調用/personal/loan/application提交貸款申請，並調用/persential/loan/loanProcess API模擬批准貸款申請並成功獲得貸款合約。", "operationId": "repaymentUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "頒發給第三方應用程式的最新客戶授權令牌，以JWT格式存取該銀行的客戶資料。當開發者在調用API存取銀行客戶資料的時候，都需要用到該令牌。", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "Message ID in 128 bit random UUID format generated uniquely for every request by F/E side.", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "client_id generated during consumer onboarding. ", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "repaymentModel", "description": "repaymentModel", "required": true, "schema": {"$ref": "#/definitions/RepaymentModel"}}], "responses": {"200": {"description": "Query completed successfully.(Returned By Get)", "schema": {"$ref": "#/definitions/ResultUtil"}}, "201": {"description": "Normal execution. The request has succeeded. (Returned By Post)", "schema": {"$ref": "#/definitions/ResultUtil"}}, "403": {"description": "Token has incorrect scope or a security policy was violated. Action: Please check whether you’re using the right token with the legal authorized user account."}, "404": {"description": "The requested deposit account does not exist.Action: Please make sure the account number and account type you’re inputting are correct."}, "500": {"description": "Something went wrong on the API gateway or micro-service. Action: check your network and try again later."}}}}, "/personal/loan/transactionEnquiry": {"post": {"tags": ["Transaction"], "summary": "此API用於查詢所有的個人消費貸款的交易記錄。", "description": "當您做了任何貸款相關操作之後，都可以呼叫該API去取得交易記錄。", "operationId": "transactionEnquiryUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "頒發給第三方應用程式的最新客戶授權令牌，以JWT格式存取該銀行的客戶資料。當開發者在調用API存取銀行客戶資料的時候，都需要用到該令牌。", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "Message ID in 128 bit random UUID format generated uniquely for every request by F/E side.", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "client_id generated during consumer onboarding. ", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "transactionEnquiryModel", "description": "transactionEnquiryModel", "required": true, "schema": {"$ref": "#/definitions/TransactionEnquiryModel"}}], "responses": {"200": {"description": "Query completed successfully.(Returned By Get)", "schema": {"$ref": "#/definitions/ResultUtil"}}, "201": {"description": "Normal execution. The request has succeeded. (Returned By Post)", "schema": {"$ref": "#/definitions/ResultUtil"}}, "403": {"description": "Token has incorrect scope or a security policy was violated. Action: Please check whether you’re using the right token with the legal authorized user account."}, "404": {"description": "The requested deposit account does not exist.Action: Please make sure the account number and account type you’re inputting are correct."}, "500": {"description": "Something went wrong on the API gateway or micro-service. Action: check your network and try again later."}}}}}, "definitions": {"AllContractsEnquiryModel": {"type": "object", "required": ["accountNumber"], "properties": {"accountNumber": {"type": "string", "example": "***********************", "description": "A unique number used to identify a loan account.<br>maxValue: 23</br>", "allowEmptyValue": false}}, "title": "AllContractsEnquiryModel"}, "ApplicationModel": {"type": "object", "required": ["accountNumber", "ccyCode", "debitAccountNumber", "loanAmount", "loanPurpose", "productCode", "questionOne", "questionThree", "questionTwo", "repaymentPeriod", "salary"], "properties": {"accountNumber": {"type": "string", "example": "***********************", "description": "A unique number used to identify a loan account.<br>maxValue: 23</br>", "allowEmptyValue": false}, "ccyCode": {"type": "string", "example": "HKD", "description": "Currency Code <br>maxLength: 3</br>", "allowEmptyValue": false}, "debitAccountNumber": {"type": "string", "example": "***********************", "description": "A unique number used to identify a saving/current account.<br>maxValue: 23</br>", "allowEmptyValue": false}, "loanAmount": {"type": "number", "example": 50000, "description": "<br>maxLength: 18</br>", "allowEmptyValue": false}, "loanPurpose": {"type": "string", "example": "F", "description": "F-Family Uses, T-Travelling, E-Further Education, S-Standby Credit, O-Others <br>maxLength: 140</br>", "allowEmptyValue": false}, "productCode": {"type": "string", "example": "PIL001", "description": "<br>maxLength: 10</br>", "allowEmptyValue": false}, "questionOne": {"type": "string", "example": "Y", "description": "Do you possess any unsecured loans offered by other financial institutions(except banks)?Possible Value: Y-Yes, N-No<br>maxValue: 1</br>", "allowEmptyValue": false}, "questionThree": {"type": "string", "example": "Y", "description": "Do you possess any unsecured loans under application but not yet drawdown(excluding this application) ? Possible Value: Y-Yes, N-No<br>maxValue: 1</br>", "allowEmptyValue": false}, "questionTwo": {"type": "string", "example": "Y", "description": "Do you possess any unsecured loans (including mortgage and/or overdraft) ? Possible Value: Y-Yes, N-No<br>maxValue: 1</br>", "allowEmptyValue": false}, "repaymentPeriod": {"type": "string", "example": "6M", "description": "Total No, of Installment <br>maxLength: 5</br>", "allowEmptyValue": false}, "salary": {"type": "number", "example": 1000, "description": "<br>maxLength: 18</br>", "allowEmptyValue": false}}, "title": "ApplicationModel"}, "CalculatorModel": {"type": "object", "required": ["ccyCode", "interestRate", "loanAmount", "repaymentPeriod"], "properties": {"ccyCode": {"type": "string", "example": "HKD", "description": "Currency Code <br>maxValue: 3</br>", "allowEmptyValue": false}, "interestRate": {"type": "number", "example": 0.0005, "allowEmptyValue": false}, "loanAmount": {"type": "number", "example": 50000, "allowEmptyValue": false}, "repaymentPeriod": {"type": "string", "example": "6M", "description": "Total No, of Installment <br>maxValue: 5</br>", "allowEmptyValue": false}}, "title": "CalculatorModel"}, "ContractDetailEnquiryModel": {"type": "object", "required": ["accountNumber"], "properties": {"accountNumber": {"type": "string", "example": "***********************", "description": "A unique number used to identify a loan account.<br>maxValue: 23</br>", "allowEmptyValue": false}, "contractNumber": {"type": "string", "example": "P000000001", "description": "A unique number used to identify a personal loan contract.<br>maxValue: 50</br>", "allowEmptyValue": true}}, "title": "ContractDetailEnquiryModel"}, "LoanProcessModel": {"type": "object", "required": ["applicationId"], "properties": {"applicationId": {"type": "string", "example": "APL000000001", "description": "<br>maxLength: 20</br>", "allowEmptyValue": false}}, "title": "LoanProcessModel"}, "OverDueHistoryEnquiryModel": {"type": "object", "required": ["accountNumber", "endDate", "startDate"], "properties": {"accountNumber": {"type": "string", "example": "***********************", "description": "A unique number used to identify a loan account.<br>maxValue: 23</br>", "allowEmptyValue": false}, "contractNumber": {"type": "string", "example": "P000000001", "description": "A unique number used to identify a personal loan contract.<br>maxValue: 50</br>", "allowEmptyValue": true}, "endDate": {"type": "number", "example": *************, "description": "The end date when you want to retrieve the order records.", "allowEmptyValue": false}, "startDate": {"type": "number", "example": *************, "description": "The start date when you want to retrieve the order records.", "allowEmptyValue": false}}, "title": "OverDueHistoryEnquiryModel"}, "RepaymentModel": {"type": "object", "required": ["accountNumber", "ccyCode", "contractNumber", "debitAccountNumber", "repaymentAmount"], "properties": {"accountNumber": {"type": "string", "example": "***********************", "description": "A unique number used to identify a loan account.<br>maxValue: 23</br>", "allowEmptyValue": false}, "ccyCode": {"type": "string", "example": "HKD", "description": "Currency Code <br>maxLength: 3</br>", "allowEmptyValue": false}, "contractNumber": {"type": "string", "example": "P000000001", "description": "A unique number used to identify a personal loan contract.<br>maxValue: 50</br>", "allowEmptyValue": false}, "debitAccountNumber": {"type": "string", "example": "***********************", "description": "A unique number used to identify a saving/current account.<br>maxValue: 23</br>", "allowEmptyValue": false}, "repaymentAmount": {"type": "number", "example": 2018.23938, "description": "maxValue: 18", "allowEmptyValue": false}}, "title": "RepaymentModel"}, "ResultUtil": {"type": "object", "properties": {"code": {"type": "string"}, "data": {"type": "object"}, "msg": {"type": "string"}}, "title": "ResultUtil"}, "TransactionEnquiryModel": {"type": "object", "required": ["accountNumber", "fromdate", "todate"], "properties": {"accountNumber": {"type": "string", "example": "***********************", "description": "A unique number used to identify a loan account.<br>maxValue: 23</br>", "allowEmptyValue": false}, "contractNumber": {"type": "string", "example": "P000000001", "description": "A unique number used to identify a personal loan contract.<br>maxValue: 50</br>", "allowEmptyValue": true}, "fromdate": {"type": "number", "example": *************, "description": "The start date when you want to retrieve the order records.", "allowEmptyValue": false}, "todate": {"type": "number", "example": *************, "description": "The end date when you want to retrieve the order records.", "allowEmptyValue": false}}, "title": "TransactionEnquiryModel"}}}