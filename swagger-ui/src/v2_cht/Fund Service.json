{"swagger": "2.0", "info": {"description": "該服務模組主要包含了所有基金交易相關的業務API。包含了基金列表，基金市場信息，基金買入， 基金賣出，基金持倉查詢及基金交易記錄查詢。當您開發基金相關的業務模組時，可以在這裡找到對應的API來完成不同業務功能。", "version": "1.0", "title": "基金業務", "contact": {"name": "<EMAIL>"}}, "schemes": ["https"], "host": "simnectz.hsu.edu.hk/lbsgateway/fund-experience", "basePath": "/", "tags": [{"name": "Market Information", "description": "查詢並顯示最新的基金市場資訊。"}, {"name": "Order & Simulation", "description": "基金訂單管理和基金公司模擬。"}, {"name": "Account Information & Maintenance", "description": "基金帳戶持倉資訊及帳戶維護。"}, {"name": "Transaction", "description": "客戶基金帳戶中的交易處理。"}], "paths": {"/fund/fundHoldingEnquiry": {"post": {"tags": ["Account Information & Maintenance"], "summary": "此API用於查詢基金帳戶的持倉詳情。", "description": "可以查詢客戶的基金帳戶下，對於某個基金的持倉信息和該基金賬戶下所有的基金持倉信息。如果客戶還沒有任何持倉信息，可能需要先買入基金，可以調用/fund /order/subscriptionOrderPlacing。", "operationId": "fundHoldingEnquiryUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "頒發給第三方應用程式的最新客戶授權令牌，以JWT格式存取該銀行的客戶資料。當開發者在調用API存取銀行客戶資料的時候，都需要用到該令牌。< br>最大長度: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "為前後端的每個請求唯一產生的128位元隨機UUID格式的訊息ID。此參數為內部參數，在呼叫API的時候不用修改該參數的內容。直接使用即可。<br>最大長度: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "當使用者登入的時候產生的客戶id。此參數為內部參數，在呼叫API的時候不用修改該參數的內容。直接使用即可。<br>最大長度: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "fundHoldingEntityModel", "description": "fundHoldingEntityModel", "required": true, "schema": {"$ref": "#/definitions/FundHoldingEnquiryModel"}}], "responses": {"200": {"description": "查詢成功。（由Get方法傳回）", "schema": {"$ref": "#/definitions/ResultUtil«FundHoldInfoModel»"}}, "201": {"description": "正常執行，請求成功。（由Post方法傳回）", "schema": {"$ref": "#/definitions/ResultUtil«FundHoldInfoModel»"}}, "403": {"description": "令牌的作用範圍不正確或違反了安全性原則。操作：請檢查您是否使用合法授權使用者帳號的正確令牌。"}, "404": {"description": "要求的帳戶不存在。操作：請確保您輸入的帳號和帳戶類型正確。"}, "500": {"description": "API網關或微服務出現問題。操作：檢查您的網絡，稍後再試。"}}}}, "/fund/fundList": {"post": {"tags": ["Market Information"], "summary": "此API用於取得市場上的所有基金清單及所有基金詳情。", "description": "當想要展示基金清單及每個基金的市場資訊時，如基金價格，成交量等，可以調用該API。", "operationId": "fundListUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "頒發給第三方應用程式的最新客戶授權令牌，以JWT格式存取該銀行的客戶資料。當開發者在調用API存取銀行客戶資料的時候，都需要用到該令牌。< br>最大長度: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "為前後端的每個請求唯一產生的128位元隨機UUID格式的訊息ID。此參數為內部參數，在呼叫API的時候不用修改該參數的內容。直接使用即可。<br>最大長度: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "當使用者登入的時候產生的客戶id。此參數為內部參數，在呼叫API的時候不用修改該參數的內容。直接使用即可。<br>最大長度: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "queryFundListModel", "description": "queryFundListModel", "required": true, "schema": {"$ref": "#/definitions/QueryFundListModel"}}], "responses": {"200": {"description": "查詢成功。（由Get方法傳回）", "schema": {"$ref": "#/definitions/ResultUtil«List«FundMarketInfoModel»»"}}, "201": {"description": "正常執行，請求成功。（由Post方法傳回）", "schema": {"$ref": "#/definitions/ResultUtil«List«FundMarketInfoModel»»"}}, "403": {"description": "令牌的作用範圍不正確或違反了安全性原則。操作：請檢查您是否使用合法授權使用者帳號的正確令牌。"}, "404": {"description": "要求的帳戶不存在。操作：請確保您輸入的帳號和帳戶類型正確。"}, "500": {"description": "API網關或微服務出現問題。操作：檢查您的網絡，稍後再試。"}}}}, "/fund/fundQuotation": {"post": {"tags": ["Market Information"], "summary": "此API用於查詢某檔基金的市場資訊。", "description": "例如，當客戶想要查看代碼為U00001的基金的價格，類型，發行公司等信息，都可以調用該API來完成查詢。如果客戶想要查詢市場所有的基金及市場信息，可以呼叫/fund/fundList 來完成查詢。", "operationId": "fundQuotationUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "頒發給第三方應用程式的最新客戶授權令牌，以JWT格式存取該銀行的客戶資料。當開發者在調用API存取銀行客戶資料的時候，都需要用到該令牌。< br>最大長度: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "為前後端的每個請求唯一產生的128位元隨機UUID格式的訊息ID。此參數為內部參數，在呼叫API的時候不用修改該參數的內容。直接使用即可。<br>最大長度: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "當使用者登入的時候產生的客戶id。此參數為內部參數，在呼叫API的時候不用修改該參數的內容。直接使用即可。<br>最大長度: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "fundCodeModel", "description": "fundCodeModel", "required": true, "schema": {"$ref": "#/definitions/FundCodeModel"}}], "responses": {"200": {"description": "查詢成功。（由Get方法傳回）", "schema": {"$ref": "#/definitions/ResultUtil«FundCodeModel»"}}, "201": {"description": "正常執行，請求成功。（由Post方法傳回）", "schema": {"$ref": "#/definitions/ResultUtil«FundCodeModel»"}}, "403": {"description": "令牌的作用範圍不正確或違反了安全性原則。操作：請檢查您是否使用合法授權使用者帳號的正確令牌。"}, "404": {"description": "要求的帳戶不存在。操作：請確保您輸入的帳號和帳戶類型正確。"}, "500": {"description": "API網關或微服務出現問題。操作：檢查您的網絡，稍後再試。"}}}}, "/fund/settlementAccountUpdate": {"post": {"tags": ["Account Information & Maintenance"], "summary": "此API用於設定基金帳戶的關聯儲蓄帳戶。", "description": "例如，當銀行客戶想要買一隻基金時，首先這個用戶需要有一個基金帳戶，然後再給這個基金帳戶綁定一個儲蓄帳戶或活期帳戶（001結尾或002結尾的帳戶）。當客戶買進100股的某檔基金，需要花費1000HKD的時候，那麼系統就會從綁定的這個儲蓄帳戶或活期帳戶直接扣掉1000HKD，同時，這個基金帳戶的持倉就會多出這100份基金的份額。帳戶的持股中，就會減少這100份的基金。該銀行客戶所持有的其他帳戶，是否有需要的001或002結尾的帳戶類型。", "operationId": "settlementAccountUpdateUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "頒發給第三方應用程式的最新客戶授權令牌，以JWT格式存取該銀行的客戶資料。當開發者在調用API存取銀行客戶資料的時候，都需要用到該令牌。< br>最大長度: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "為前後端的每個請求唯一產生的128位元隨機UUID格式的訊息ID。此參數為內部參數，在呼叫API的時候不用修改該參數的內容。直接使用即可。<br>最大長度: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "當使用者登入的時候產生的客戶id。此參數為內部參數，在呼叫API的時候不用修改該參數的內容。直接使用即可。<br>最大長度: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "updateSettlementAccountModel", "description": "updateSettlementAccountModel", "required": true, "schema": {"$ref": "#/definitions/UpdateSettlementAccountModel"}}], "responses": {"200": {"description": "查詢成功。（由Get方法傳回）", "schema": {"$ref": "#/definitions/ResultUtil"}}, "201": {"description": "正常執行，請求成功。（由Post方法傳回）", "schema": {"$ref": "#/definitions/ResultUtil"}}, "403": {"description": "令牌的作用範圍不正確或違反了安全性原則。操作：請檢查您是否使用合法授權使用者帳號的正確令牌。"}, "404": {"description": "要求的帳戶不存在。操作：請確保您輸入的帳號和帳戶類型正確。"}, "500": {"description": "API網關或微服務出現問題。操作：檢查您的網絡，稍後再試。"}}}}, "/fund/transactionRetrieval": {"post": {"tags": ["Transaction"], "summary": "該API用於查詢某位客戶的基金帳戶的歷史交易記錄。", "description": "例如，客戶用基金帳戶購買過很多隻基金，那麼就可以在這裡調用相應的API查詢某段時間內，某隻基金的交易記錄，或者該帳戶在某段時間內的所有交易記錄。", "operationId": "retrieveFundTransactionUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "頒發給第三方應用程式的最新客戶授權令牌，以JWT格式存取該銀行的客戶資料。當開發者在調用API存取銀行客戶資料的時候，都需要用到該令牌。< br>最大長度: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "為前後端的每個請求唯一產生的128位元隨機UUID格式的訊息ID。此參數為內部參數，在呼叫API的時候不用修改該參數的內容。直接使用即可。<br>最大長度: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "當使用者登入的時候產生的客戶id。此參數為內部參數，在呼叫API的時候不用修改該參數的內容。直接使用即可。<br>最大長度: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "queryFundTransModel", "description": "queryFundTransModel", "required": true, "schema": {"$ref": "#/definitions/QueryFundTransModel"}}], "responses": {"200": {"description": "查詢成功。（由Get方法傳回）", "schema": {"$ref": "#/definitions/ResultUtil«List«FundPlatformLogModel»»"}}, "201": {"description": "正常執行，請求成功。（由Post方法傳回）", "schema": {"$ref": "#/definitions/ResultUtil«List«FundPlatformLogModel»»"}}, "403": {"description": "令牌的作用範圍不正確或違反了安全性原則。操作：請檢查您是否使用合法授權使用者帳號的正確令牌。"}, "404": {"description": "要求的帳戶不存在。操作：請確保您輸入的帳號和帳戶類型正確。"}, "500": {"description": "API網關或微服務出現問題。操作：檢查您的網絡，稍後再試。"}}}}, "/fund/order/cancellation": {"post": {"tags": ["Order & Simulation"], "summary": "此API用於基金買賣過程中撤單操作。", "description": "在客戶基金買賣過程中，當客戶掛單（即想要買入（調用/fund/order/subscriptionOrderPlacing）或賣出（調用/fund/order/redemptionOrderPlacing）基金時，發起的申請）之後，在未實際成交之前，可以呼叫該API來取消掛單業務。 fund/order/cancellation取消掛單，那麼本交易請求就取消了；也可以呼叫/fund/order/orderChange 來修改掛單的內容，例如將5000HKD改為6000HKD； 然後再呼叫/fund/order/simulatorUpdate API/simulatorUpdate API來模擬掛單交易成功還是失敗，最終決定本次基金交易是否成功。還是失敗。", "operationId": "cancellationUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "頒發給第三方應用程式的最新客戶授權令牌，以JWT格式存取該銀行的客戶資料。當開發者在調用API存取銀行客戶資料的時候，都需要用到該令牌。< br>最大長度: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "為前後端的每個請求唯一產生的128位元隨機UUID格式的訊息ID。此參數為內部參數，在呼叫API的時候不用修改該參數的內容。直接使用即可。<br>最大長度: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "當使用者登入的時候產生的客戶id。此參數為內部參數，在呼叫API的時候不用修改該參數的內容。直接使用即可。<br>最大長度: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "idmodel", "description": "idmodel", "required": true, "schema": {"$ref": "#/definitions/IDModel"}}], "responses": {"200": {"description": "查詢成功。（由Get方法傳回）", "schema": {"$ref": "#/definitions/ResultUtil"}}, "201": {"description": "正常執行，請求成功。（由Post方法傳回）", "schema": {"$ref": "#/definitions/ResultUtil"}}, "403": {"description": "令牌的作用範圍不正確或違反了安全性原則。操作：請檢查您是否使用合法授權使用者帳號的正確令牌。"}, "404": {"description": "要求的帳戶不存在。操作：請確保您輸入的帳號和帳戶類型正確。"}, "500": {"description": "API網關或微服務出現問題。操作：檢查您的網絡，稍後再試。"}}}}, "/fund/order/orderChange": {"post": {"tags": ["Order & Simulation"], "summary": "此API用於修改掛單訊息。", "description": "例如當用戶想要買進或賣出基金的時候，都需要先掛單。例如，客戶想要買進1000HKD的基金時，就需要呼叫/fund/order/subscriptionOrderPlacing 來下單。這就是一個掛單的過程。成交了， 那麼就不能再被修改了。", "operationId": "orderInfoUpdateUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "頒發給第三方應用程式的最新客戶授權令牌，以JWT格式存取該銀行的客戶資料。當開發者在調用API存取銀行客戶資料的時候，都需要用到該令牌。< br>最大長度: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "為前後端的每個請求唯一產生的128位元隨機UUID格式的訊息ID。此參數為內部參數，在呼叫API的時候不用修改該參數的內容。直接使用即可。<br>最大長度: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "當使用者登入的時候產生的客戶id。此參數為內部參數，在呼叫API的時候不用修改該參數的內容。直接使用即可。<br>最大長度: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "orderInfoUpdateModel", "description": "orderInfoUpdateModel", "required": true, "schema": {"$ref": "#/definitions/OrderInfoUpdateModel"}}], "responses": {"200": {"description": "查詢成功。（由Get方法傳回）", "schema": {"$ref": "#/definitions/ResultUtil"}}, "201": {"description": "正常執行，請求成功。（由Post方法傳回）", "schema": {"$ref": "#/definitions/ResultUtil"}}, "403": {"description": "令牌的作用範圍不正確或違反了安全性原則。操作：請檢查您是否使用合法授權使用者帳號的正確令牌。"}, "404": {"description": "要求的帳戶不存在。操作：請確保您輸入的帳號和帳戶類型正確。"}, "500": {"description": "API網關或微服務出現問題。操作：檢查您的網絡，稍後再試。"}}}}, "/fund/order/orderDetailRetrievalById": {"post": {"tags": ["Order & Simulation"], "summary": "該API用於某一個掛單記錄的查詢。", "description": "可以查詢某個掛單的詳情。例如，客戶想要買入1000HKD的基金時，就需要調用/fund/order/subscriptionOrderPlacing 來下單。這就是一個掛單的過程。不管是買入或者賣出都要掛單，掛單之後，會傳回一個參數id（即掛單的編號），然後可以呼叫/fund/order/orderDetailRetrievalById， 輸入該編號，就可以取得到該掛單的詳細資料了。", "operationId": "orderDetailRetrievalByIdUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "頒發給第三方應用程式的最新客戶授權令牌，以JWT格式存取該銀行的客戶資料。當開發者在調用API存取銀行客戶資料的時候，都需要用到該令牌。< br>最大長度: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "為前後端的每個請求唯一產生的128位元隨機UUID格式的訊息ID。此參數為內部參數，在呼叫API的時候不用修改該參數的內容。直接使用即可。<br>最大長度: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "當使用者登入的時候產生的客戶id。此參數為內部參數，在呼叫API的時候不用修改該參數的內容。直接使用即可。<br>最大長度: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "idmodel", "description": "idmodel", "required": true, "schema": {"$ref": "#/definitions/IDModel"}}], "responses": {"200": {"description": "查詢成功。（由Get方法傳回）", "schema": {"$ref": "#/definitions/ResultUtil«FundOrderInfoModel»"}}, "201": {"description": "正常執行，請求成功。（由Post方法傳回）", "schema": {"$ref": "#/definitions/ResultUtil«FundOrderInfoModel»"}}, "403": {"description": "令牌的作用範圍不正確或違反了安全性原則。操作：請檢查您是否使用合法授權使用者帳號的正確令牌。"}, "404": {"description": "要求的帳戶不存在。操作：請確保您輸入的帳號和帳戶類型正確。"}, "500": {"description": "API網關或微服務出現問題。操作：檢查您的網絡，稍後再試。"}}}}, "/fund/order/orderRetrieval": {"post": {"tags": ["Order & Simulation"], "summary": "此API用於所有掛單記錄的查詢。", "description": "可以查詢某個基金帳戶在某段時間掛單的歷史記錄及詳情。例如，客戶想要買入1000HKD的基金時，就需要調用/fund/order/subscriptionOrderPlacing 來下單。這就是一個掛單的過程。", "operationId": "orderRetrievalUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "頒發給第三方應用程式的最新客戶授權令牌，以JWT格式存取該銀行的客戶資料。當開發者在調用API存取銀行客戶資料的時候，都需要用到該令牌。< br>最大長度: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "為前後端的每個請求唯一產生的128位元隨機UUID格式的訊息ID。此參數為內部參數，在呼叫API的時候不用修改該參數的內容。直接使用即可。<br>最大長度: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "當使用者登入的時候產生的客戶id。此參數為內部參數，在呼叫API的時候不用修改該參數的內容。直接使用即可。<br>最大長度: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "orderRequestModel", "description": "orderRequestModel", "required": true, "schema": {"$ref": "#/definitions/OrderRequestModel"}}], "responses": {"200": {"description": "查詢成功。（由Get方法傳回）", "schema": {"$ref": "#/definitions/ResultUtil«List«FundOrderInfoModel»»"}}, "201": {"description": "正常執行，請求成功。（由Post方法傳回）", "schema": {"$ref": "#/definitions/ResultUtil«List«FundOrderInfoModel»»"}}, "403": {"description": "令牌的作用範圍不正確或違反了安全性原則。操作：請檢查您是否使用合法授權使用者帳號的正確令牌。"}, "404": {"description": "要求的帳戶不存在。操作：請確保您輸入的帳號和帳戶類型正確。"}, "500": {"description": "API網關或微服務出現問題。操作：檢查您的網絡，稍後再試。"}}}}, "/fund/order/simulatorUpdate": {"post": {"tags": ["Order & Simulation"], "summary": "此API用於模擬基金交易中掛單成功後，模擬實際成交的過程。", "description": "例如，客戶想要買進1000HKD的基金時，就需要呼叫/fund/order/subscriptionOrderPlacing 來下單。這個流程就代表掛單成功了，然後就可以再呼叫/fund/order/simulatorUpdate API/simulatorUpdate API/simulatorUpdate API/simulator來模擬基金能成功交易的過程。", "operationId": "orderStatusUpdateUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "頒發給第三方應用程式的最新客戶授權令牌，以JWT格式存取該銀行的客戶資料。當開發者在調用API存取銀行客戶資料的時候，都需要用到該令牌。< br>最大長度: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "為前後端的每個請求唯一產生的128位元隨機UUID格式的訊息ID。此參數為內部參數，在呼叫API的時候不用修改該參數的內容。直接使用即可。<br>最大長度: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "當使用者登入的時候產生的客戶id。此參數為內部參數，在呼叫API的時候不用修改該參數的內容。直接使用即可。<br>最大長度: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "orderStatusUpdateModel", "description": "orderStatusUpdateModel", "required": true, "schema": {"$ref": "#/definitions/OrderStatusUpdateModel"}}], "responses": {"200": {"description": "查詢成功。（由Get方法傳回）", "schema": {"$ref": "#/definitions/ResultUtil"}}, "201": {"description": "正常執行，請求成功。（由Post方法傳回）", "schema": {"$ref": "#/definitions/ResultUtil"}}, "403": {"description": "令牌的作用範圍不正確或違反了安全性原則。操作：請檢查您是否使用合法授權使用者帳號的正確令牌。"}, "404": {"description": "要求的帳戶不存在。操作：請確保您輸入的帳號和帳戶類型正確。"}, "500": {"description": "API網關或微服務出現問題。操作：檢查您的網絡，稍後再試。"}}}}, "/fund/order/redemptionOrderPlacing": {"post": {"tags": ["Order & Simulation"], "summary": "該API用於賣出基金時，掛單的服務。", "description": "例如，當客戶想要賣出500份額的某隻持倉基金，就可以調用該的API來完成賣出掛單。在賣出掛單之前，客戶可能需要先查看所持倉的基金信息，可以呼叫/fund/fundHoldingEnquiry 來查看。若要進一步在成交之前修改掛單，則可以呼叫/fund/order/orderChange；如果不需要修改掛單，則可以呼叫/fund/order/simulatorUpdate 直接模擬交易成功的過程。", "operationId": "redemptionOrderPlacingUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "頒發給第三方應用程式的最新客戶授權令牌，以JWT格式存取該銀行的客戶資料。當開發者在調用API存取銀行客戶資料的時候，都需要用到該令牌。< br>最大長度: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "為前後端的每個請求唯一產生的128位元隨機UUID格式的訊息ID。此參數為內部參數，在呼叫API的時候不用修改該參數的內容。直接使用即可。<br>最大長度: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "當使用者登入的時候產生的客戶id。此參數為內部參數，在呼叫API的時候不用修改該參數的內容。直接使用即可。<br>最大長度: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "redemptionRequestModel", "description": "redemptionRequestModel", "required": true, "schema": {"$ref": "#/definitions/RedemptionRequestModel"}}], "responses": {"200": {"description": "查詢成功。（由Get方法傳回）", "schema": {"$ref": "#/definitions/ResultUtil"}}, "201": {"description": "正常執行，請求成功。（由Post方法傳回）", "schema": {"$ref": "#/definitions/ResultUtil"}}, "403": {"description": "令牌的作用範圍不正確或違反了安全性原則。操作：請檢查您是否使用合法授權使用者帳號的正確令牌。"}, "404": {"description": "要求的帳戶不存在。操作：請確保您輸入的帳號和帳戶類型正確。"}, "500": {"description": "API網關或微服務出現問題。操作：檢查您的網絡，稍後再試。"}}}}, "/fund/order/subscriptionOrderPlacing": {"post": {"tags": ["Order & Simulation"], "summary": "該API用於買入基金時，掛單的服務。", "description": "例如，客戶想要買進1000HKD的某檔基金時，就需要呼叫/fund/order/subscriptionOrderPlacing 來下單。這就是一個掛單的過程。掛單之後，如果要進一步在成交之前修改掛單，則可以呼叫/fund/order/orderChange；如果不需要修改掛單，則可以呼叫/fund/order/simulatorUpdate 直接模擬交易成功的過程。", "operationId": "subscriptionOrderPlacingUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "頒發給第三方應用程式的最新客戶授權令牌，以JWT格式存取該銀行的客戶資料。當開發者在調用API存取銀行客戶資料的時候，都需要用到該令牌。< br>最大長度: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "為前後端的每個請求唯一產生的128位元隨機UUID格式的訊息ID。此參數為內部參數，在呼叫API的時候不用修改該參數的內容。直接使用即可。<br>最大長度: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "當使用者登入的時候產生的客戶id。此參數為內部參數，在呼叫API的時候不用修改該參數的內容。直接使用即可。<br>最大長度: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "subscriptioRequestModel", "description": "subscriptioRequestModel", "required": true, "schema": {"$ref": "#/definitions/SubscriptioRequestModel"}}], "responses": {"200": {"description": "查詢成功。（由Get方法傳回）", "schema": {"$ref": "#/definitions/ResultUtil"}}, "201": {"description": "正常執行，請求成功。（由Post方法傳回）", "schema": {"$ref": "#/definitions/ResultUtil"}}, "403": {"description": "令牌的作用範圍不正確或違反了安全性原則。操作：請檢查您是否使用合法授權使用者帳號的正確令牌。"}, "404": {"description": "要求的帳戶不存在。操作：請確保您輸入的帳號和帳戶類型正確。"}, "500": {"description": "API網關或微服務出現問題。操作：檢查您的網絡，稍後再試。"}}}}}, "definitions": {"FundCodeModel": {"type": "object", "required": ["fundCode"], "properties": {"fundCode": {"type": "string", "example": "U000001", "description": "基金代码。</br> 最大长度: 10", "allowEmptyValue": false}}, "title": "FundCodeModel"}, "FundHoldInfoModel": {"type": "object", "properties": {"fundaccountnumber": {"type": "string", "example": "***********************", "description": "Mutual Fund Account Number.</br> 最大长度: 34", "allowEmptyValue": false}, "fundholdlist": {"type": "array", "items": {"$ref": "#/definitions/FundInvestmentModel"}}, "settlementaccount": {"type": "string", "example": "***********************", "description": "Settlement Account Number.</br> 最大长度: 34", "allowEmptyValue": false}, "totalInvestmentAmount": {"type": "string", "example": 100, "description": "total investment amount.<br>最大长度: 18</br></br>", "allowEmptyValue": false}, "totalNetGainLoss": {"type": "string", "example": 100, "description": "total NetGain Loss.<br>最大长度: 18</br></br>", "allowEmptyValue": false}, "totalNetGainLossPct": {"type": "string", "example": 100, "description": "total NetGain LossPct.<br>最大长度: 18</br></br>", "allowEmptyValue": false}, "totalmarkervalue": {"type": "string", "description": "total market value.<br>最大长度: 18</br></br>", "allowEmptyValue": false}}, "title": "FundHoldInfoModel"}, "FundHoldingEnquiryModel": {"type": "object", "required": ["fundAccountNumber"], "properties": {"fundAccountNumber": {"type": "string", "example": "***********************", "description": "基金账户号码。</br> 最大长度: 34", "allowEmptyValue": false}, "fundCode": {"type": "string", "example": "U000001", "description": "基金代码。</br> 最大长度: 10", "allowEmptyValue": true}}, "title": "FundHoldingEnquiryModel"}, "FundInvestmentModel": {"type": "object", "required": ["accountnumber", "id"], "properties": {"accountnumber": {"type": "string", "example": "***********************", "description": "A fund account number. <br/> 最大长度: 34", "allowEmptyValue": false}, "avaliableholdingno": {"type": "number", "example": 100.0, "description": "Avaliable shareholding number.<br>最大长度: 18</br>", "allowEmptyValue": false}, "averageprice": {"type": "number", "example": 200.0, "description": "Average price.<br>最大长度: 18</br>", "allowEmptyValue": false}, "currencycode": {"type": "string", "example": "HKD", "description": "Currency Code.<br>最大长度: 3</br>", "allowEmptyValue": false}, "fundcode": {"type": "string", "example": "U000001", "description": "Mutual Fund Code.<br>最大长度: 35</br>", "allowEmptyValue": false}, "id": {"type": "integer", "format": "int32", "example": 1, "description": "id.<br>最大长度: 20</br>", "allowEmptyValue": false}, "investmentamount": {"type": "number", "example": 500.0, "description": "Investment amount.<br>最大长度: 18</br>", "allowEmptyValue": false}, "lastupdatedate": {"type": "number", "example": *************.0, "description": "lastest update date.<br>最大长度: 20</br>", "allowEmptyValue": false}, "marketprice": {"type": "number", "example": 500.0, "description": "Market price.<br>最大长度: 18</br>", "allowEmptyValue": false}, "marketvalue": {"type": "number", "example": 50000.0, "description": "Market value.<br>最大长度: 18</br>", "allowEmptyValue": false}, "netGainLoss": {"type": "number", "example": 3000.0, "description": "Net income loss.<br>最大长度: 18</br>", "allowEmptyValue": false}, "netGainLossPct": {"type": "string", "example": 300, "description": "Net gain Loss Pct.<br>最大长度: 18</br>", "allowEmptyValue": false}, "sharesholdingno": {"type": "number", "example": 1000.0, "description": "Total shareholding number.<br>最大长度: 18</br>", "allowEmptyValue": false}}, "title": "FundInvestmentModel"}, "FundMarketInfoModel": {"type": "object", "properties": {"fundCode": {"type": "string", "example": "U000001", "description": "The Fund Code.<br>最大長度: 35</br>", "allowEmptyValue": false}, "fundCurrency": {"type": "string", "example": "USD", "description": "The fund currency type.<br>最大長度: 3</br>", "allowEmptyValue": false}, "fundHouse": {"type": "string", "example": "Aberdeen Intl Fund Managers Ltd", "description": "The fund company.<br>最大長度: 140</br>", "allowEmptyValue": false}, "fundName": {"type": "string", "example": "Aberdeen Global - Asia Pacific Equity Fund (USD) A2", "description": "The name of the fund.<br>最大長度: 140</br>", "allowEmptyValue": false}, "fundType": {"type": "string", "example": "Equity Funds", "description": "The fund type.<br>最大長度: 70</br>", "allowEmptyValue": false}, "geographic": {"type": "string", "example": "Asia Pacific", "description": "geographic.<br>最大長度: 35</br>", "allowEmptyValue": false}, "id": {"type": "integer", "format": "int32", "example": 1, "description": "id,<br>最大長度: 20</br>", "allowEmptyValue": false}, "issueDate": {"type": "number", "example": *************.0, "description": "Fund release date.<br>最大長度: 20</br>", "allowEmptyValue": false}, "lastNAV": {"type": "number", "example": 92.64, "description": "The fund latest net.<br>最大長度: 18</br>", "allowEmptyValue": false}, "managementFee": {"type": "number", "example": 0.018, "description": "fund management fee.<br>最大長度: 18</br>", "allowEmptyValue": false}, "sector": {"type": "string", "example": "General", "description": "Sector.<br>最大長度: 35</br>", "allowEmptyValue": false}, "valuationDate": {"type": "number", "example": *************.0, "description": "The fund valuation date.<br>最大長度: 20</br>", "allowEmptyValue": false}}, "title": "FundMarketInfoModel"}, "FundOrderInfoModel": {"type": "object", "properties": {"currencyCode": {"type": "string", "example": "HKD", "description": "Fund Currency Type.<br>最大長度: 3</br>", "allowEmptyValue": false}, "fundAccountNumber": {"type": "string", "example": "***********************", "description": "Mutual Fund Account Number.</br> 最大長度: 34", "allowEmptyValue": false}, "fundCode": {"type": "string", "example": "U000001", "description": "Fund code </br> 最大長度: 10", "allowEmptyValue": false}, "fundName": {"type": "string", "example": "Aberdeen Global - Asia Pacific Equity Fund (USD) A2.<br>最大長度: 140</br>", "description": "Fund name", "allowEmptyValue": false}, "id": {"type": "integer", "format": "int32", "example": 1, "description": "id.<br>最大長度: 20</br>", "allowEmptyValue": false}, "lastupdateDate": {"type": "number", "example": *************.0, "description": "Last update date.<br>最大長度: 20</br>", "allowEmptyValue": false}, "operationReasons": {"type": "string", "example": "test case", "description": "Operation reason.<br>最大長度: 140</br>", "allowEmptyValue": false}, "operationTime": {"type": "number", "example": *************.0, "description": "Operation time.<br>最大長度: 20</br>", "allowEmptyValue": false}, "requestTime": {"type": "number", "example": *************.0, "description": "Request time.<br>最大長度: 20</br>", "allowEmptyValue": false}, "settlementAccount": {"type": "string", "example": "***********************", "description": "Settlement Account Number.</br> 最大長度: 34", "allowEmptyValue": false}, "sharingNo": {"type": "number", "example": 100.0, "description": "Number of fund shares you'd like to sell. </br>最大長度: 13", "allowEmptyValue": false}, "status": {"type": "string", "example": 200, "description": "Request status.<br>最大長度: 4</br>", "allowEmptyValue": false}, "statusCode": {"type": "string", "example": 200, "description": "Status code.<br>最大長度: 10</br>", "allowEmptyValue": false}, "tradingAmount": {"type": "number", "example": 200.0, "description": "Trading amount. </br>最大長度: 13", "allowEmptyValue": false}, "tradingCommission": {"type": "number", "example": 150.0, "description": "Trading commission. </br>最大長度: 13", "allowEmptyValue": false}, "tradingOption": {"type": "string", "example": "sell", "description": "Fund transaction type", "allowEmptyValue": false}, "tradingPrice": {"type": "number", "example": 100.0, "description": "Trading Price. </br>最大長度: 13", "allowEmptyValue": false}, "transactionAmount": {"type": "number", "example": 150.0, "description": "Transaction amount. </br>最大長度: 13", "allowEmptyValue": false}}, "title": "FundOrderInfoModel"}, "FundPlatformLogModel": {"type": "object", "properties": {"accountNumber": {"type": "string", "example": "***********************", "description": "Fund Account Number </br> 最大長度:34", "allowEmptyValue": false}, "branchCode": {"type": "string", "example": "0001", "description": "Bank branch code<br>最大長度: 4</br>", "allowEmptyValue": false}, "clearingCode": {"type": "string", "example": "0001", "description": "Bank clearing code.<br>最大長度: 4</br>", "allowEmptyValue": false}, "countryCode": {"type": "string", "example": "HK", "description": "Country code.<br>最大長度: 2</br>", "allowEmptyValue": false}, "fundCcy": {"type": "string", "example": "HKD", "description": "fund currency<br>最大長度: 3</br>", "allowEmptyValue": false}, "fundCode": {"type": "string", "example": "U000001", "description": "The Fund Code<br>最大長度: 35</br>", "allowEmptyValue": false}, "id": {"type": "integer", "format": "int32", "example": 1, "description": "id,<br>最大長度: 20</br>", "allowEmptyValue": false}, "sharingNo": {"type": "number", "example": 100.0, "description": "Number of fund shares you’d like to sell. </br>5 decimals,最大長度: 13", "allowEmptyValue": false}, "tradingAmount": {"type": "number", "example": 300.0, "description": "Trading amount in the fund subscription order. </br>5 decimals,最大長度: 13", "allowEmptyValue": false}, "tradingOption": {"type": "string", "example": "sell", "description": "Fund transaction type<br>最大長度: 10</br>", "allowEmptyValue": false}, "transactionAmount": {"type": "number", "example": 100.0, "description": "Transaction amount in the fund order. </br>5 decimals,最大長度: 13", "allowEmptyValue": false}, "transactionDate": {"type": "number", "example": *************.0, "description": "Transaction date<br>最大長度: 20</br>", "allowEmptyValue": false}, "trdingCommission": {"type": "number", "example": 200.0, "description": "Trading commission in the fund order. </br>5 decimals,最大長度: 13", "allowEmptyValue": false}}, "title": "FundPlatformLogModel"}, "IDModel": {"type": "object", "required": ["id"], "properties": {"id": {"type": "number", "example": 1.0, "description": "基金訂單編號。當您調用買入或賣出基金申請API之後，就會返回一個基金訂單編號。</br><br>最大長度: 20</br>", "allowEmptyValue": false}}, "title": "IDModel"}, "OrderInfoUpdateModel": {"type": "object", "required": ["id"], "properties": {"id": {"type": "number", "example": 2.0, "description": "基金訂單編號。當您呼叫買入或賣出基金申請API之後，就會回傳一個基金訂單編號。<br>最大長度: 20</br>", "allowEmptyValue": false}, "sharingNo": {"type": "number", "example": 100.0, "description": "基金份額。例如當要賣出基金的時候，需要填入賣出多少份額。 當買入基金的時候，不需要填入該數字，請保持為空。</br> 最大長度: 13", "allowEmptyValue": true}, "tradingAmount": {"type": "number", "example": 100.0, "description": "交易金額。</br>最大長度: 13", "allowEmptyValue": true}}, "title": "OrderInfoUpdateModel"}, "OrderRequestModel": {"type": "object", "required": ["accountNumber", "fromdate", "index", "items", "todate"], "properties": {"accountNumber": {"type": "string", "example": "***********************", "description": "基金帳號。</br> 最大長度: 34", "allowEmptyValue": false}, "fromdate": {"type": "number", "example": *************.0, "description": "想要查詢的訂單的開始時間範圍。</br>最大長度: 20 integers", "allowEmptyValue": false}, "fundCode": {"type": "string", "example": "U000001", "description": "基金代碼。</br> 最大長度: 10", "allowEmptyValue": true}, "index": {"type": "number", "example": 0.0, "description": "API傳回資料的開始位置標記。如：當index=1時，則表示從第1條資料開始傳回結果。</br> 最大長度: 4 integers", "allowEmptyValue": false}, "items": {"type": "number", "example": 3.0, "description": "API傳回的資料條數。如：當items=10. 表示傳回10個結果。</br>最大長度: 4 integers", "allowEmptyValue": false}, "todate": {"type": "number", "example": *************.0, "description": "想要查詢的訂單的結束時間範圍。</br>最大長度: 20 integers", "allowEmptyValue": false}}, "title": "OrderRequestModel"}, "OrderStatusUpdateModel": {"type": "object", "required": ["id", "status"], "properties": {"id": {"type": "number", "example": 3.0, "description": "基金訂單編號。當您調用買入或賣出基金申請API之後，就會返回一個基金訂單編號。</br> <br>最大長度: 20</br>", "allowEmptyValue": false}, "status": {"type": "string", "example": 1, "description": "用於模擬基金交易 成功或失敗的狀態參數。當Status設定為1時，表示模擬該基金交易成功。</br> 最大長度: 4", "allowEmptyValue": false}}, "title": "OrderStatusUpdateModel"}, "QueryFundListModel": {"type": "object", "required": ["index", "items"], "properties": {"index": {"type": "number", "example": 0.0, "description": "API傳回資料的開始位置標記。如：當index=1時，則表示從第1條資料開始傳回結果</br>最大長度: 4 integers", "allowEmptyValue": false}, "items": {"type": "number", "example": 3.0, "description": "API傳回的資料條數。如：當items=10. 表示傳回10個結果。</br>最大長度: 4 integers", "allowEmptyValue": false}}, "title": "QueryFundListModel"}, "QueryFundTransModel": {"type": "object", "required": ["accountNumber", "index", "items", "transFromTime", "transToTime"], "properties": {"accountNumber": {"type": "string", "example": "***********************", "description": "基金帳號。</br> 最大長度:34", "allowEmptyValue": false}, "fundCode": {"type": "string", "example": "U000001", "description": "基金代碼。</br> 最大長度:10", "allowEmptyValue": true}, "index": {"type": "number", "example": 0.0, "description": "API傳回資料的開始位置標記。如：當index=1時，則表示從第1條資料開始傳回結果。</br>最大長度: 4", "allowEmptyValue": false}, "items": {"type": "number", "example": 3.0, "description": "API傳回的資料條數。如：當items=10. 表示傳回10個結果。</br>最大長度: 4", "allowEmptyValue": false}, "transFromTime": {"type": "number", "example": *************.0, "description": "想要查詢的歷史交易的開始時間範圍。</br>最大長度: 20", "allowEmptyValue": false}, "transToTime": {"type": "number", "example": *************.0, "description": "想要查詢的歷史交易的結束時間範圍。</br>最大長度: 20", "allowEmptyValue": false}}, "title": "QueryFundTransModel"}, "RedemptionRequestModel": {"type": "object", "required": ["fundAccountNumber", "fundCode", "sharingNo"], "properties": {"fundAccountNumber": {"type": "string", "example": "***********************", "description": "基金帳號。</br> 最大長度:34", "allowEmptyValue": false}, "fundCode": {"type": "string", "example": "U000001", "description": "基金代碼。</br> 最大長度:10", "allowEmptyValue": false}, "sharingNo": {"type": "number", "example": 100.0, "description": "基金份額。例如當要賣出基金的時候，需要填入賣出多少份額。當買入基金的時候，不需要填入該數字，請保持為空。</br>最大長度: 13", "allowEmptyValue": false}}, "title": "RedemptionRequestModel"}, "ResultUtil": {"type": "object", "properties": {"code": {"type": "string"}, "data": {"type": "object"}, "msg": {"type": "string"}}, "title": "ResultUtil"}, "ResultUtil«FundCodeModel»": {"type": "object", "properties": {"code": {"type": "string"}, "data": {"$ref": "#/definitions/FundCodeModel"}, "msg": {"type": "string"}}, "title": "ResultUtil«FundCodeModel»"}, "ResultUtil«FundHoldInfoModel»": {"type": "object", "properties": {"code": {"type": "string"}, "data": {"$ref": "#/definitions/FundHoldInfoModel"}, "msg": {"type": "string"}}, "title": "ResultUtil«FundHoldInfoModel»"}, "ResultUtil«FundOrderInfoModel»": {"type": "object", "properties": {"code": {"type": "string"}, "data": {"$ref": "#/definitions/FundOrderInfoModel"}, "msg": {"type": "string"}}, "title": "ResultUtil«FundOrderInfoModel»"}, "ResultUtil«List«FundMarketInfoModel»»": {"type": "object", "properties": {"code": {"type": "string"}, "data": {"type": "array", "items": {"$ref": "#/definitions/FundMarketInfoModel"}}, "msg": {"type": "string"}}, "title": "ResultUtil«List«FundMarketInfoModel»»"}, "ResultUtil«List«FundOrderInfoModel»»": {"type": "object", "properties": {"code": {"type": "string"}, "data": {"type": "array", "items": {"$ref": "#/definitions/FundOrderInfoModel"}}, "msg": {"type": "string"}}, "title": "ResultUtil«List«FundOrderInfoModel»»"}, "ResultUtil«List«FundPlatformLogModel»»": {"type": "object", "properties": {"code": {"type": "string"}, "data": {"type": "array", "items": {"$ref": "#/definitions/FundPlatformLogModel"}}, "msg": {"type": "string"}}, "title": "ResultUtil«List«FundPlatformLogModel»»"}, "SubscriptioRequestModel": {"type": "object", "required": ["fundAccountNumber", "fundCode", "tradingAmount"], "properties": {"fundAccountNumber": {"type": "string", "example": "***********************", "description": "基金帳號。</br> 最大長度: 34", "allowEmptyValue": false}, "fundCode": {"type": "string", "example": "U000001", "description": "基金代號。</br> 最大長度: 10", "allowEmptyValue": false}, "tradingAmount": {"type": "number", "example": 100.0, "description": "交易金額。</br> 最大長度: 13", "allowEmptyValue": false}}, "title": "SubscriptioRequestModel"}, "UpdateSettlementAccountModel": {"type": "object", "required": ["fundAccountNumber", "newSettleAccountNumber"], "properties": {"fundAccountNumber": {"type": "string", "example": "***********************", "description": "基金帳號。</br> 最大長度: 34", "allowEmptyValue": false}, "newSettleAccountNumber": {"type": "string", "example": "***********************", "description": "為該基金帳戶綁定的新的儲蓄帳戶（001或002結尾的帳戶類型)，用於交易中的扣款或收款服務。</br> 最大長度: 34", "allowEmptyValue": false}}, "title": "UpdateSettlementAccountModel"}}}