{"swagger": "2.0", "info": {"description": "這裡的API都是關於電子錢包服務的。在呼叫這些API之前，請確保您已經了解這些API之間的業務邏輯。", "version": "1.0", "title": "E-wallet Service", "contact": {"name": "<EMAIL>"}}, "schemes": ["https"], "host": "simnectz.hsu.edu.hk/lbsgateway/ewallet-experience", "basePath": "/", "tags": [{"name": "錢包帳戶", "description": "電子錢包帳戶相關的操作，包含綁定/解綁銀行卡，錢包取款和用戶資訊查詢。"}, {"name": "銀行卡", "description": "錢包相關銀行卡服務包括查詢客戶綁定的銀行卡列表和查詢綁定的銀行餘額。"}, {"name": "交易", "description": "電子錢包交易，包括商家列表，商家資訊與付款。"}], "paths": {"/customer/customerID": {"post": {"tags": ["錢包帳戶"], "summary": "透過設定的客戶ID查詢使用者資訊", "description": "透過設定的客戶ID查詢使用者資料", "operationId": "customerIdUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "頒發給第三方應用程式的最新客戶授權令牌，以JWT格式存取該銀行的客戶資料。當開發者在調用API存取銀行客戶資料的時候，都需要用到該令牌。< br>最大長度: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "Message ID in 128 bit random UUID format generated uniquely for every request by F/E side.<br>maxLength: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "client_id generated during consumer onboarding. <br>maxLength: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "vo", "description": "vo", "required": true, "schema": {"$ref": "#/definitions/CustomerIdVo"}}], "responses": {"200": {"description": "Query completed successfully.(Returned By Get)", "schema": {"$ref": "#/definitions/EwalletCustomerMaster"}}, "201": {"description": "Normal execution. The request has succeeded. (Returned By Post)", "schema": {"$ref": "#/definitions/ResultUtil"}}, "403": {"description": "Token has incorrect scope or a security policy was violated. Action: Please check whether you’re using the right token with the legal authorized user account."}, "404": {"description": "The requested account does not exist.Action: Please make sure the account number and account type you’re inputting are correct."}, "500": {"description": "Something went wrong on the API gateway or micro-service. Action: check your network and try again later."}}}}, "/finance/accountUnbinding": {"post": {"tags": ["錢包帳戶"], "summary": "透過此API可以解除已綁定的卡片", "description": "透過此API可以解除已綁定的卡片", "operationId": "accountUnbindingUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "頒發給第三方應用程式的最新客戶授權令牌，以JWT格式存取該銀行的客戶資料。當開發者在調用API存取銀行客戶資料的時候，都需要用到該令牌。< br>最大長度: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "Message ID in 128 bit random UUID format generated uniquely for every request by F/E side.<br>maxLength: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "client_id generated during consumer onboarding. <br>maxLength: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "validationModel", "description": "validationModel", "required": true, "schema": {"$ref": "#/definitions/ValidationModel"}}], "responses": {"200": {"description": "Query completed successfully.(Returned By Get)", "schema": {"$ref": "#/definitions/ResultUtil"}}, "201": {"description": "Normal execution. The request has succeeded. (Returned By Post)", "schema": {"$ref": "#/definitions/ResultUtil"}}, "403": {"description": "Token has incorrect scope or a security policy was violated. Action: Please check whether you’re using the right token with the legal authorized user account."}, "404": {"description": "The requested account does not exist.Action: Please make sure the account number and account type you’re inputting are correct."}, "500": {"description": "Something went wrong on the API gateway or micro-service. Action: check your network and try again later."}}}}, "/finance/accountValidation": {"post": {"tags": ["錢包帳戶"], "summary": "此API用於進行帳戶驗證並將帳戶綁定到電子錢包系統。", "description": "此 API 僅支援儲蓄/活期帳戶和信用卡帳戶。在進行電子錢包充值之前，您可能需要呼叫此 API。", "operationId": "accountValidationUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "頒發給第三方應用程式的最新客戶授權令牌，以JWT格式存取該銀行的客戶資料。當開發者在調用API存取銀行客戶資料的時候，都需要用到該令牌。< br>最大長度: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "Message ID in 128 bit random UUID format generated uniquely for every request by F/E side.<br>maxLength: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "client_id generated during consumer onboarding. <br>maxLength: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "validationModel", "description": "validationModel", "required": true, "schema": {"$ref": "#/definitions/ValidationModel"}}], "responses": {"200": {"description": "Query completed successfully.(Returned By Get)", "schema": {"$ref": "#/definitions/ResultUtil"}}, "201": {"description": "Normal execution. The request has succeeded. (Returned By Post)", "schema": {"$ref": "#/definitions/ResultUtil"}}, "403": {"description": "Token has incorrect scope or a security policy was violated. Action: Please check whether you’re using the right token with the legal authorized user account."}, "404": {"description": "The requested account does not exist.Action: Please make sure the account number and account type you’re inputting are correct."}, "500": {"description": "Something went wrong on the API gateway or micro-service. Action: check your network and try again later."}}}}, "/finance/accounts/customerNumber": {"post": {"tags": ["銀行卡"], "summary": "查詢指定使用者的綁定卡片資訊", "description": "查詢指定使用者的綁定卡資訊", "operationId": "customerNumberUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "頒發給第三方應用程式的最新客戶授權令牌，以JWT格式存取該銀行的客戶資料。當開發者在調用API存取銀行客戶資料的時候，都需要用到該令牌。< br>最大長度: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "Message ID in 128 bit random UUID format generated uniquely for every request by F/E side.<br>maxLength: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "client_id generated during consumer onboarding. <br>maxLength: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "vo", "description": "vo", "required": true, "schema": {"$ref": "#/definitions/CustomerNumberVo"}}], "responses": {"200": {"description": "Query completed successfully.(Returned By Get)", "schema": {"$ref": "#/definitions/BindCardInfoVo"}}, "201": {"description": "Normal execution. The request has succeeded. (Returned By Post)", "schema": {"$ref": "#/definitions/ResultUtil"}}, "403": {"description": "Token has incorrect scope or a security policy was violated. Action: Please check whether you’re using the right token with the legal authorized user account."}, "404": {"description": "The requested account does not exist.Action: Please make sure the account number and account type you’re inputting are correct."}, "500": {"description": "Something went wrong on the API gateway or micro-service. Action: check your network and try again later."}}}}, "/finance/authorize": {"post": {"tags": ["錢包帳戶"], "summary": "第三方授權", "description": "授權第三方電子錢包應用", "operationId": "authorizeUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "頒發給第三方應用程式的最新客戶授權令牌，以JWT格式存取該銀行的客戶資料。當開發者在調用API存取銀行客戶資料的時候，都需要用到該令牌。< br>最大長度: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "Message ID in 128 bit random UUID format generated uniquely for every request by F/E side.<br>maxLength: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "client_id generated during consumer onboarding. <br>maxLength: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "eServiceAuthorizeModel", "description": "eServiceAuthorizeModel", "required": true, "schema": {"$ref": "#/definitions/EServiceAuthorizeModel"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ResultUtil«object»"}}}}}, "/finance/bankCardBalance": {"post": {"tags": ["銀行卡"], "summary": "卡片餘額查詢", "operationId": "cardBalanceEnquiryUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "頒發給第三方應用程式的最新客戶授權令牌，以JWT格式存取該銀行的客戶資料。當開發者在調用API存取銀行客戶資料的時候，都需要用到該令牌。< br>最大長度: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "Message ID in 128 bit random UUID format generated uniquely for every request by F/E side.<br>maxLength: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "client_id generated during consumer onboarding. <br>maxLength: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "cardBalanceEnquiry", "description": "cardBalanceEnquiry", "required": true, "schema": {"$ref": "#/definitions/CardBalanceEnquiryModel"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ResultUtil«object»"}}}}}, "/finance/cashing": {"post": {"tags": ["錢包帳戶"], "summary": "透過此API，你可以提現指定金額到卡片上", "description": "Through this API, you can withdraw the specified amount to the card", "operationId": "cashingUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "頒發給第三方應用程式的最新客戶授權令牌，以JWT格式存取該銀行的客戶資料。當開發者在調用API存取銀行客戶資料的時候，都需要用到該令牌。< br>最大長度: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "Message ID in 128 bit random UUID format generated uniquely for every request by F/E side.<br>maxLength: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "client_id generated during consumer onboarding. <br>maxLength: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "vo", "description": "vo", "required": true, "schema": {"$ref": "#/definitions/CashingVo"}}], "responses": {"200": {"description": "Query completed successfully.(Returned By Get)", "schema": {"$ref": "#/definitions/ResultUtil«string»"}}, "201": {"description": "Normal execution. The request has succeeded. (Returned By Post)", "schema": {"$ref": "#/definitions/ResultUtil«string»"}}, "403": {"description": "Token has incorrect scope or a security policy was violated. Action: Please check whether you’re using the right token with the legal authorized user account."}, "404": {"description": "The requested account does not exist.Action: Please make sure the account number and account type you’re inputting are correct."}, "500": {"description": "Something went wrong on the API gateway or micro-service. Action: check your network and try again later."}}}}, "/finance/merchantDetails": {"post": {"tags": ["交易"], "summary": "商家詳情查詢", "operationId": "merchantDetailsEnquiryUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "頒發給第三方應用程式的最新客戶授權令牌，以JWT格式存取該銀行的客戶資料。當開發者在調用API存取銀行客戶資料的時候，都需要用到該令牌。< br>最大長度: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "Message ID in 128 bit random UUID format generated uniquely for every request by F/E side.<br>maxLength: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "client_id generated during consumer onboarding. <br>maxLength: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "merchantDetailsEnquiry", "description": "merchantDetailsEnquiry", "required": true, "schema": {"$ref": "#/definitions/MerchantDetailsEnquiryModel"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/ResultUtil«object»"}}}}}, "/finance/merchantQR": {"post": {"tags": ["交易"], "summary": "此API用於產生帶有商家詳細資料的二維碼。", "description": "在使用電子錢包付款之前，您可能需要呼叫此 API 來檢索商家資訊。", "operationId": "merchantDetailsUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "頒發給第三方應用程式的最新客戶授權令牌，以JWT格式存取該銀行的客戶資料。當開發者在調用API存取銀行客戶資料的時候，都需要用到該令牌。< br>最大長度: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "Message ID in 128 bit random UUID format generated uniquely for every request by F/E side.<br>maxLength: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "client_id generated during consumer onboarding. <br>maxLength: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "merchantVo", "description": "merchantVo", "required": true, "schema": {"$ref": "#/definitions/MerchantInfoVo"}}], "responses": {"200": {"description": "Query completed successfully.(Returned By Get)", "schema": {"$ref": "#/definitions/ResultUtil"}}, "201": {"description": "Normal execution. The request has succeeded. (Returned By Post)", "schema": {"$ref": "#/definitions/ResultUtil"}}, "403": {"description": "Token has incorrect scope or a security policy was violated. Action: Please check whether you’re using the right token with the legal authorized user account."}, "404": {"description": "The requested account does not exist.Action: Please make sure the account number and account type you’re inputting are correct."}, "500": {"description": "Something went wrong on the API gateway or micro-service. Action: check your network and try again later."}}}}, "/finance/merchants": {"post": {"tags": ["交易"], "summary": "取得所有商家的詳細資料。", "description": "取得所有商家的詳細資料。", "operationId": "merchantsUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "頒發給第三方應用程式的最新客戶授權令牌，以JWT格式存取該銀行的客戶資料。當開發者在調用API存取銀行客戶資料的時候，都需要用到該令牌。< br>最大長度: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "Message ID in 128 bit random UUID format generated uniquely for every request by F/E side.<br>maxLength: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "client_id generated during consumer onboarding. <br>maxLength: 50</br>", "required": true, "type": "string", "default": "devin"}], "responses": {"200": {"description": "Query completed successfully.(Returned By Get)", "schema": {"$ref": "#/definitions/ResultUtil"}}, "201": {"description": "Normal execution. The request has succeeded. (Returned By Post)", "schema": {"$ref": "#/definitions/ResultUtil"}}, "403": {"description": "Token has incorrect scope or a security policy was violated. Action: Please check whether you’re using the right token with the legal authorized user account."}, "404": {"description": "The requested account does not exist.Action: Please make sure the account number and account type you’re inputting are correct."}, "500": {"description": "Something went wrong on the API gateway or micro-service. Action: check your network and try again later."}}}}, "/finance/topUp": {"post": {"tags": ["錢包帳戶"], "summary": "此API用於為電子錢包充值。", "description": "當您的銀行帳戶驗證成功後，您可能需要呼叫此API為您的電子錢包充值，以確保您的電子錢包有足夠的資金進行付款。", "operationId": "toUpUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "頒發給第三方應用程式的最新客戶授權令牌，以JWT格式存取該銀行的客戶資料。當開發者在調用API存取銀行客戶資料的時候，都需要用到該令牌。< br>最大長度: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "Message ID in 128 bit random UUID format generated uniquely for every request by F/E side.<br>maxLength: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "client_id generated during consumer onboarding. <br>maxLength: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "toUpModel", "description": "toUpModel", "required": true, "schema": {"$ref": "#/definitions/ToUpModel"}}], "responses": {"200": {"description": "Query completed successfully.(Returned By Get)", "schema": {"$ref": "#/definitions/ResultUtil"}}, "201": {"description": "Normal execution. The request has succeeded. (Returned By Post)", "schema": {"$ref": "#/definitions/ResultUtil"}}, "403": {"description": "Token has incorrect scope or a security policy was violated. Action: Please check whether you’re using the right token with the legal authorized user account."}, "404": {"description": "The requested account does not exist.Action: Please make sure the account number and account type you’re inputting are correct."}, "500": {"description": "Something went wrong on the API gateway or micro-service. Action: check your network and try again later."}}}}, "/finance/transferToMerchant": {"post": {"tags": ["交易"], "summary": "此API用於向商家付款。", "description": "在呼叫此API之前，您可能需要呼叫其他API以確保您的電子錢包有足夠的錢，並掃描商家的二維碼以在銀行系統中檢索商家帳戶。", "operationId": "transferToMerchantUsingPOST", "consumes": ["application/json"], "produces": ["*/*"], "parameters": [{"name": "token", "in": "header", "description": "頒發給第三方應用程式的最新客戶授權令牌，以JWT格式存取該銀行的客戶資料。當開發者在調用API存取銀行客戶資料的時候，都需要用到該令牌。< br>最大長度: 1000</br>", "required": true, "type": "string", "default": "eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA"}, {"name": "messageid", "in": "header", "description": "Message ID in 128 bit random UUID format generated uniquely for every request by F/E side.<br>maxLength: 50</br>", "required": true, "type": "string", "default": "006f7113e5fa48559549c4dfe74e2cd6"}, {"name": "clientid", "in": "header", "description": "client_id generated during consumer onboarding. <br>maxLength: 50</br>", "required": true, "type": "string", "default": "devin"}, {"in": "body", "name": "merchantModel", "description": "merchantModel", "required": true, "schema": {"$ref": "#/definitions/MerchantModel"}}], "responses": {"200": {"description": "Query completed successfully.(Returned By Get)", "schema": {"$ref": "#/definitions/ResultUtil"}}, "201": {"description": "Normal execution. The request has succeeded. (Returned By Post)", "schema": {"$ref": "#/definitions/ResultUtil"}}, "403": {"description": "Token has incorrect scope or a security policy was violated. Action: Please check whether you’re using the right token with the legal authorized user account."}, "404": {"description": "The requested account does not exist.Action: Please make sure the account number and account type you’re inputting are correct."}, "500": {"description": "Something went wrong on the API gateway or micro-service. Action: check your network and try again later."}}}}}, "definitions": {"AccountNumberVo": {"type": "object", "properties": {"accountNumber": {"type": "string", "example": "HK720001************001", "description": "客戶的銀行帳戶。支援001結束的儲蓄帳戶；002結尾的活期帳戶和信用卡帳戶。<br></br> maxLength: 34", "allowEmptyValue": false}}, "title": "AccountNumberVo"}, "BindCardInfoVo": {"type": "object", "properties": {"creditCard": {"type": "array", "items": {"$ref": "#/definitions/AccountNumberVo"}}, "current": {"type": "array", "items": {"$ref": "#/definitions/AccountNumberVo"}}, "saving": {"type": "array", "items": {"$ref": "#/definitions/AccountNumberVo"}}}, "title": "BindCardInfoVo"}, "CardBalanceEnquiryModel": {"type": "object", "required": ["accountNumber", "walletId"], "properties": {"accountNumber": {"type": "string", "example": "HK720001************001", "description": "客戶的銀行帳戶。支援001結束的儲蓄帳戶；002結尾的活期帳戶和信用卡帳戶。<br></br> maxLength: 34", "allowEmptyValue": false}, "walletId": {"type": "string", "example": "************************", "description": "當前銀行客戶的電子錢包編號，該編號由該用戶在電子錢包系統註冊時產生。<br></br> maxLength: 64", "allowEmptyValue": false}}, "title": "CardBalanceEnquiryModel"}, "CashingVo": {"type": "object", "required": ["amount", "ccy", "walletId"], "properties": {"accountNumber": {"type": "string", "example": "HK720001************001", "description": "客戶的銀行帳戶。支援001結束的儲蓄帳戶；002結尾的活期帳戶和信用卡帳戶。<br></br> maxLength: 34", "allowEmptyValue": false}, "amount": {"type": "number", "example": 100, "description": "交易金額。<br>maxLength: 18</br>", "allowEmptyValue": false}, "ccy": {"type": "string", "example": "HKD", "description": "交易貨幣類型。<br></br> maxLength: 3", "allowEmptyValue": false}, "walletId": {"type": "string", "example": "************************", "description": "當前銀行客戶的電子錢包編號，該編號由該用戶在電子錢包系統註冊時產生。<br></br> maxLength: 64", "allowEmptyValue": false}}, "title": "CashingVo"}, "CustomerIdVo": {"type": "object", "required": ["customerId"], "properties": {"customerId": {"type": "string", "example": "U735535(9)", "description": "使用者身分證號碼。<br></br> maxLength: 25", "allowEmptyValue": false}}, "title": "CustomerIdVo"}, "CustomerNumberVo": {"type": "object", "required": ["customerNumber", "walletId"], "properties": {"customerNumber": {"type": "string", "example": "************", "description": "銀行客戶編號。當銀行客戶創建成功後，系統會為該客戶產生唯一的一個編號。<br></br> maxLength: 25", "allowEmptyValue": false}, "walletId": {"type": "string", "example": "************************", "description": "當前銀行客戶的電子錢包編號，該編號由該用戶在電子錢包系統註冊時產生。<br></br> maxLength: 64", "allowEmptyValue": false}}, "title": "CustomerNumberVo"}, "EServiceAuthorizeModel": {"type": "object", "required": ["customerId", "eWalletCompany", "walletId"], "properties": {"customerId": {"type": "string", "example": "U735535(9)", "description": "使用者身分證號碼。<br></br> maxLength: 25", "allowEmptyValue": false}, "eWalletCompany": {"type": "string", "example": "e-Wallet", "description": "第三方電子錢包企業。<br></br> maxLength: 25", "allowEmptyValue": false}, "walletId": {"type": "string", "example": "************************", "description": "當前銀行客戶的電子錢包編號，該編號由該用戶在電子錢包系統註冊時產生。<br></br> maxLength: 64", "allowEmptyValue": false}}, "title": "EServiceAuthorizeModel"}, "EwalletCustomerMaster": {"type": "object", "properties": {"chineseName": {"type": "string"}, "createTime": {"type": "number"}, "customerId": {"type": "string"}, "customerNumber": {"type": "string"}, "firstName": {"type": "string"}, "id": {"type": "string"}, "issueCountry": {"type": "string"}, "lastName": {"type": "string"}, "phone": {"type": "string"}}, "title": "EwalletCustomerMaster"}, "MerchantDetailsEnquiryModel": {"type": "object", "required": ["merchantNumber"], "properties": {"merchantNumber": {"type": "string", "example": "******************", "description": "系統為入駐的企業所產生的編號，用以標註該商家在系統中的唯一性。<br></br> MaxLength:34", "allowEmptyValue": false}}, "title": "MerchantDetailsEnquiryModel"}, "MerchantInfoVo": {"type": "object", "required": ["merchantNumber"], "properties": {"merchantNumber": {"type": "string", "example": "******************", "description": "系統為入駐的企業所產生的編號，用以標註該商家在系統中的唯一性。<br></br> MaxLength:34", "allowEmptyValue": false}}, "title": "MerchantInfoVo"}, "MerchantModel": {"type": "object", "required": ["accountNumber", "accountType", "merchantNumber", "transactionAmount", "transactionCcy", "walletId"], "properties": {"accountNumber": {"type": "string", "example": "HK720001************001", "description": "客戶的銀行帳戶。支援001結束的儲蓄帳戶；002結尾的活期帳戶和信用卡帳戶。<br></br> maxLength: 34", "allowEmptyValue": false}, "accountType": {"type": "string", "example": "SAVI", "description": "客戶輸入的銀行帳戶的類型。<br></br>SAVI - 001結尾的儲蓄帳戶<br>CURR - 002結尾的活期帳戶<br>CRED - 信用卡帳戶。</br>maxLength : 4", "allowEmptyValue": false}, "merchantNumber": {"type": "string", "example": "******************", "description": "系統為入駐的企業所產生的編號，用以標註該商家在系統中的唯一性。<br></br> maxLength: 34", "allowEmptyValue": false}, "transactionAmount": {"type": "string", "example": 100, "description": "支付金額。<br></br> maxLength: 18", "allowEmptyValue": false}, "transactionCcy": {"type": "string", "example": "HKD", "description": "交易貨幣類型。<br></br> maxLength: 3", "allowEmptyValue": false}, "walletId": {"type": "string", "example": "************************", "description": "當前銀行客戶的電子錢包編號，該編號由該用戶在電子錢包系統註冊時產生。<br></br> maxLength: 64", "allowEmptyValue": false}}, "title": "MerchantModel"}, "ResultUtil": {"type": "object", "properties": {"code": {"type": "string"}, "data": {"type": "object"}, "msg": {"type": "string"}}, "title": "ResultUtil"}, "ResultUtil«object»": {"type": "object", "properties": {"code": {"type": "string"}, "data": {"type": "object"}, "msg": {"type": "string"}}, "title": "ResultUtil«object»"}, "ResultUtil«string»": {"type": "object", "properties": {"code": {"type": "string"}, "data": {"type": "string"}, "msg": {"type": "string"}}, "title": "ResultUtil«string»"}, "ToUpModel": {"type": "object", "required": ["accountNumber", "amount", "ccy", "walletID"], "properties": {"accountNumber": {"type": "string", "example": "HK720001************001", "description": "客戶的銀行帳戶。支援001結束的儲蓄帳戶；002結尾的活期帳戶和信用卡帳戶。<br></br> maxLength: 34", "allowEmptyValue": false}, "amount": {"type": "string", "example": 100, "description": "交易金額。<br></br> maxLength: 18", "allowEmptyValue": false}, "ccy": {"type": "string", "example": "HKD", "description": "交易貨幣類型。<br></br> maxLength: 3", "allowEmptyValue": false}, "walletID": {"type": "string", "example": "************************", "description": "當前銀行客戶的電子錢包編號，該編號由該用戶在電子錢包系統註冊時產生。<br></br> maxLength: 64", "allowEmptyValue": false}}, "title": "ToUpModel"}, "ValidationModel": {"type": "object", "required": ["accountNumber", "customerNumber", "walletId"], "properties": {"accountNumber": {"type": "string", "example": "HK720001************001", "description": "客戶的銀行帳戶。支援001結束的儲蓄帳戶；002結尾的活期帳戶和信用卡帳戶。<br></br> maxLength: 34", "allowEmptyValue": false}, "customerNumber": {"type": "string", "example": "************", "description": "銀行客戶編號。當銀行客戶創建成功後，系統會為該客戶產生唯一的一個編號。<br></br> maxLength: 25", "allowEmptyValue": false}, "walletId": {"type": "string", "example": "************************", "description": "當前銀行客戶的電子錢包編號，該編號由該用戶在電子錢包系統註冊時產生。<br></br> maxLength: 64", "allowEmptyValue": false}}, "title": "ValidationModel"}}}