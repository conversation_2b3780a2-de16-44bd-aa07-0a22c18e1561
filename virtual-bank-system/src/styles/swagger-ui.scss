.swagger-ui {
  line-height: 1.5 !important;
  table {
    width: 100%;
    word-break: break-word;
    padding: 0 10px;
    border-collapse: collapse;
  }
  .info .title {
    font-size: 30px;
    margin: 0;
    font-family: sans-serif;
    color: #3b4151;
  }
  table tbody tr td:first-of-type {
    width: 20%!important;
    min-width: 20em;
    padding: 10px 10px;
  }
  .col {
    width: auto !important;
  }
  .auth-container input[type="text"] {
    min-width: 550px;
  }
  .info__tos {
    display: none;
  }
  .info hgroup.main a {
    font-size: 12px;
    display: none;
  }
  .info .base-url {
    font-size: 18px !important;
    font-weight: 300 !important;
    margin: 0 !important;
    font-family: monospace !important;
    color: #109eae !important;
  }
  .border-box,
  a,
  article,
  body,
  code,
  dd,
  div,
  dl,
  dt,
  fieldset,
  footer,
  form,
  h1,
  h2,
  h3,
  h4,
  h5,
  h6,
  header,
  html,
  input[type="email"],
  input[type="number"],
  input[type="password"],
  input[type="tel"],
  input[type="text"],
  input[type="url"],
  legend,
  li,
  main,
  ol,
  p,
  pre,
  section,
  table,
  td,
  textarea,
  th,
  tr,
  ul {
    color: #109eae;
  }
  pre,
  tr{
    border: none;
  }
  .body-param-options label select {
    margin: 3px 0 0;
    width: 200px;
  }
  .button {
    height: 2rem;
  }
}
