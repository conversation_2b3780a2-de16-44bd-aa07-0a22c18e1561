#app {

  // 主体区域 Main container
  .main-container {
    min-height: 100%;
    transition: margin-left .28s;
    margin-left: $sideBarWidth;
    position: relative;
    background-color: #fff;
  }

  // 侧边栏 Sidebar container
  .sidebar-container {
    transition: width 0.28s;
    width: $sideBarWidth !important;
    height: 100%;
    position: fixed;
    font-size: 0px;
    top: 0;
    bottom: 0;
    left: 0;
    z-index: 1001;
    overflow: hidden;
    -webkit-box-shadow: 0 2px 12px 0 #0000001a;
    box-shadow: 0 2px 12px 0 #0000001a;

    //reset element-ui css
    .horizontal-collapse-transition {
      transition: 0s width ease-in-out, 0s padding-left ease-in-out, 0s padding-right ease-in-out;
    }

    .scrollbar-wrapper {
      overflow-x: hidden !important;

      .el-scrollbar__view {
        height: 100%;
      }
    }

    .el-scrollbar__bar.is-vertical {
      right: 0px;
    }

    .is-horizontal {
      display: none;
    }

    a {
      display: inline-block;
      width: 100%;
      overflow: hidden;
    }

    .svg-icon {
      margin-right: 16px;
      font-size: 18px;
    }

    .el-menu {
      //   border: none;
      height: 100%;
      width: 100% !important;
    }

    .el-submenu {
      .el-menu-item {
        width: 229px;
      }
    }

  }

  .hideSidebar {
    .sidebar-container {
      width: 36px !important;
    }

    .main-container {
      margin-left: 36px;
    }

    .submenu-title-noDropdown {
      padding-left: 6px !important;
      position: relative;

      .el-tooltip {
        padding: 0 6px !important;
      }
    }

    .el-submenu {
      overflow: hidden;

      &>.el-submenu__title {
        padding-left: 6px !important;
        .el-submenu__icon-arrow {
          display: none;
        }
      }
    }

    .el-menu--collapse {
      .el-submenu {
        &>.el-submenu__title {
          &>span {
            height: 0;
            width: 0;
            overflow: hidden;
            visibility: hidden;
            display: inline-block;
          }
        }
      }
    }
  }

  .sidebar-container .nest-menu .el-submenu>.el-submenu__title,
  .sidebar-container .el-submenu .el-menu-item {
    min-width: $sideBarWidth !important;
    // background-color: $subMenuBg;
    //padding-left: 30px !important;

    &:hover {
      background-color: $menuHover !important;
    }
  }

  .el-menu--collapse .el-menu .el-submenu {
    min-width: $sideBarWidth !important;
  }

  // 适配移动端, Mobile responsive
  .mobile {
    .main-container {
      margin-left: 0px;
    }

    .sidebar-container {
      transition: transform .28s;
      width: $sideBarWidth !important;
    }

    &.hideSidebar {
      .sidebar-container {
        transition-duration: 0.3s;
        transform: translate3d(-$sideBarWidth, 0, 0);
      }
    }
  }

  .withoutAnimation {

    .main-container,
    .sidebar-container {
      transition: none;
    }
  }
}

.el-menu--vertical {
  &>.el-menu {
    .svg-icon {
      margin-right: 16px;
    }
  }
}
.el-menu-item:hover,
.el-submenu__title:hover,
#app .sidebar-container .is-active > .el-submenu__title:hover {
  background-color: #109eae!important;
  color: #FFF!important;
}
#app .sidebar-container .is-active > .el-submenu__title {
  color: #109eae !important;
}
.menu-wrapper{
  border-top: 1px solid #eee;
}
.el-menu-item.is-active{
  background-color: #109eae!important;
}
