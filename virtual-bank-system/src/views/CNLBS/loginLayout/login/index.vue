<template>
  <div
    v-loading.fullscreen.lock="fullscreenLoading"
    class="login-container"
    style="position: relative;top: -70px;"
  >
    <el-tooltip effect="dark" placement="right">
      <div slot="content" style="width: 300px">
        <p>{{ loginTip }}</p>
        <p>{{ tokenTip }}</p>
      </div>
      <el-form
        ref="loginForm"
        :model="loginForm"
        :rules="loginRules"
        class="login-form"
        auto-complete="on"
        label-position="left"
      >
        <div class="title-container">
          <h3 class="title">{{ $t('lbs.login.login') }}</h3>
        </div>
        <div style="text-align: right;margin-bottom: 22px;display: block;">{{ $t('lbs.login.language') }}：
          <lang-select class="set-language" />
        </div>
        <div style="position:relative">
          <div class="tips">
            <span>{{ $t('lbs.login.customerNumber') }}：</span>
          </div>
        </div>
        <el-form-item prop="customernumber">
          <span class="svg-container">
            <svg-icon icon-class="user" />
          </span>
          <el-input
            v-model="loginForm.customernumber"
            :placeholder="$t('lbs.login.customerNumber')"
            name="customernumber"
            type="text"
            auto-complete="on"
            tabindex="0"
            autofocus="true"
            @keyup.enter.native="signIn"
          />
        </el-form-item>
        <div style="position:relative">
          <div class="tips">
            <span>{{ $t('lbs.login.username') }}：</span>
          </div>
        </div>
        <el-form-item prop="username">
          <span class="svg-container">
            <svg-icon icon-class="user" />
          </span>
          <el-input
            v-model="loginForm.loginname"
            :placeholder="$t('lbs.login.username')"
            name="loginname"
            type="text"
            auto-complete="on"
            tabindex="1"
            autofocus="true"
            @keyup.enter.native="signIn"
          />
        </el-form-item>
        <div style="position:relative">
          <div class="tips">
            <span>{{ $t('lbs.common.password') }}：</span>
          </div>
        </div>
        <el-form-item prop="password">
          <span class="svg-container">
            <svg-icon icon-class="password" />
          </span>
          <el-input
            v-model="loginForm.loginpwd"
            tabindex="2"
            :type="passwordType"
            :placeholder="$t('lbs.common.password')"
            name="password"
            auto-complete="on"
            @keyup.enter.native="signIn"
          />
          <span class="show-pwd" @click="showPwd">
            <svg-icon :icon-class="pwdSvg" />
          </span>
        </el-form-item>
        <el-button
          :loading="loading"
          type="primary"
          style="width:100%;margin-bottom:30px;"
          @click.native.prevent="signIn()"
        >{{ $t('lbs.login.login') }}
        </el-button>
      </el-form>
    </el-tooltip>
  </div>
</template>

<script>
import LangSelect from '@/components/LangSelect'

export default {
  name: 'LbsLogin',
  components: { LangSelect },
  data() {
    const validatePassword = (rule, value, callback) => {
      if (value && value.length < 6) {
        callback(new Error(this.$t('login.tip2')))
      } else {
        callback()
      }
    }
    return {
      loginForm: {
        loginname: '',
        // loginname: "admin",
        loginpwd: '',
        customernumber: '',
        // customernumber:"***********",
        src: ''
        // accountType: '1',
      },
      loginRules: {
        loginname: [
          {
            required: true,
            trigger: 'blur'
          }
        ],
        password: [
          {
            required: true,
            trigger: 'blur',
            validator: validatePassword
          }
        ],
        customernumber: [
          {
            required: true,
            trigger: 'blur'
          }
        ]
      },
      passwordType: 'password',
      pwdSvg: 'eye',
      loading: false,
      fullscreenLoading: false,
      redirect: undefined,
      loginTip: this.$t('lbsTips.loginTip'),
      tokenTip: this.$t('lbsTips.tokenTip')
    }
  },
  computed: {},
  watch: {
    $route: {
      handler: function(route) {
        var vue = this
        // //console.log('----------window.location.href:' + window.location.href)
        const hrefs = window.location.href
        if (hrefs === 'https://simnectzplatform.com:9000/?redirect=%2Fdashboard&state=-1#/login') {
          this.$message({
            message: vue.$t('login.Emailverificationfailed'),
            center: true,
            type: 'error'
          })
        } else if (hrefs === 'https://simnectzplatform.com:9000/?redirect=%2Fdashboard&state=0#/login') {
          this.$router.push({ path: '/emailSuccessVerification' })
        }
        this.redirect = route.query && route.query.redirect
        // //console.log('-----this.redirect:', decodeURIComponent(window.location.href.split("redirect=")[1]))
        if (window.location.href.split('redirect=')[1]) {
          window.localStorage.setItem(
            'redirect',
            decodeURIComponent(window.location.href.split('redirect=')[1])
          )
        } else {
          window.localStorage.setItem('redirect', '')
        }
      },
      immediate: true
    }
  },
  created() {
    window.sessionStorage.setItem('login_type', 'CNLBS')
    this.loginForm = {
      loginname: this.$route.query.loginName,
      loginpwd: this.$route.query.loginPassword,
      customernumber: this.$route.query.customerNumber
    }
  },
  mounted() {
    this.refreshVerificationCode()
  },
  methods: {
    showPwd() {
      if (this.passwordType === 'password') {
        this.passwordType = ''
        this.pwdSvg = 'openeye'
      } else {
        this.passwordType = 'password'
        this.pwdSvg = 'eye'
      }
    },
    getUrlKey: function(name) {
      return (
        decodeURIComponent(
          (new RegExp('[?|&]' + name + '=' + '([^&;]+?)(&|#|;|$)').exec(
            location.href
          ) || [''])[1].replace(/\+/g, '%20')
        ) || null
      )
    },
    refreshVerificationCode() {
      var requestCodeId = Math.random()
        .toString(36)
        .slice(-8) // 截取最后八位 : "yo82mvyr"
      window.localStorage.setItem('requestCodeId', requestCodeId)
      this.loginForm.src =
          this.host +
          '/api/portal/developers/code_image?requestCodeId=' +
          requestCodeId
    },
    signIn() {
      var vue = this
      this.$mui.ajax(this.CNLBSGateway + '/domestic-sysadmin-process/sysadmin/login/loginIn',
        {
          data: vue.loginForm,
          dataType: 'json', // 服务器返回json格式数据
          type: 'post', // HTTP请求类型
          timeout: 60000,
          headers: {
            'accept': '*/*',
            'Content-Type': 'application/json',
            'token': 'eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA'
          },
          beforeSend: function() {
            // vue.loading = true
          },
          complete: function() {
          },
          success: function(data) {
            if (data.code === '200') {
              console.log(JSON.stringify(data))
              window.sessionStorage.setItem('customernumber', vue.loginForm.customernumber)
              window.sessionStorage.setItem('username', vue.loginForm.loginname)
              // plus.storage.setItem("pk",data.data.loginPk);
              // plus.webview.open("authoriz.html","authoriz.html",{scrollIndicator:'none',scalable:false,popGesture:'close'},{preate:true});
              // window.sessionStorage.setItem("username",username);
              // window.sessionStorage.setItem("pk",data.data.loginPk);
              // vue.$router.push({path: "/lbsDemo/authoriz"})
              window.sessionStorage.setItem('needReload', 1)
              vue.isAuth(data.data.loginPk)
            } else {
              console.log(data)
              vue.$mui.alert('Login failed! The response is: \n' + JSON.stringify(data) + '.', 'Error', 'OK')
            }
          },
          error: function(xhr, type, errorThrown) {
            console.log(xhr)
            console.log(type)
            var msg = 'Login failed! The response is: \n' + xhr.responseText + '.'
            if (type === 'timeout') {
              msg = 'Login failed. Time out!'
            }
            vue.$mui.alert(msg, 'Error', 'OK')
          }
        })
    },
    isAuth(pk) {
      window.sessionStorage.setItem('pk', pk)
      var vue = this
      this.$mui.ajax(this.CNLBSGateway + '/domestic-sysadmin-process/sysadmin/token/getToken?loginPK=' + pk, {
        dataType: 'json', // 服务器返回json格式数据
        type: 'post', // HTTP请求类型
        timeout: 60000,
        headers: {
          'accept': '*/*',
          'Content-Type': 'application/json',
          'token': 'eyJhbGciOiJIUzUxMiIsInppcCI6IkRFRiJ9.eNo8y00OwiAQhuG7zNoFkBLUpbqwadI7ADNWEn4aWozGeHchNs7yme99A9KDfJop9xc4gkTaG6XtTQrRCWEPhMKojhnDLVdKwg5sKnHNr3NCqsF1aORJZxenzRhjvKrJOtr73xr5NLk46tBkdqGlZVlToDyWYCj_hmy7Viw64ik9e6wv-HwBAAD__w.alc0ibAbJotnPxSQL2wtt9Qo8h0YYzl4WkxOK65PnGy1fK4SDmNRRVEohqOya_K7qOXJOt5Cjdm10cejK3PViA'
        },
        beforeSend: function() {
          // plus.nativeUI.showWaiting("Loading…", "div");
          // mask.show();
          // document.getElementById("load").style.display = "inline";
        },
        complete: function() {
          // plus.nativeUI.closeWaiting();
          // mask.close();
          // document.getElementById("load").style.display = "none";
        },
        success: function(data) {
          if (data.code === '200') {
            window.sessionStorage.setItem('token', data.data.token)
            // var webview = plus.webview.getWebviewById('subpage-Me.html');
            // mui.alert(webview.id)
            // mui.fire(webview, 'refresh');
            // plus.webview.getLaunchWebview().reload();
            // plus.webview.getLaunchWebview().show();
            // plus.runtime.restart();
            // vue.$parent.getaccoutlist();
            // vue.$parent.isShowBtn()
            // vue.loading = true
            window.sessionStorage.setItem('role', data.data.role)// 登录角色
            window.sessionStorage.setItem('LoginName_Opear', data.data.loginName)// 登录名
            window.sessionStorage.setItem('staffNumber_Opear', data.data.staffNumber) // 员工编号
            window.sessionStorage.setItem('department', data.data.department) // 登录部门
            console.log(data.data.role)
            const loginType = window.sessionStorage.getItem('loginType')
            if (data.data.role === 'Manager') {
              if (loginType === 'supplychainfinance') {
                vue.$router.push({ path: '/lbssupplychainfinanceadmin/index' })
              } else {
                vue.$router.push({ path: '/lbssystemparam' })
              }
            } else if (data.data.role === 'Teller') { // 用户
              if (loginType === 'supplychainfinance') {
                vue.$router.push({ path: '/lbssupplychainfinancebank/index' })
              } else {
                vue.$router.push({ path: '/tellerlbscustomeroverview/checkcustomer' })
              }
            } else if (data.data.role === 'Operation') { // 运营\
              vue.$router.push({ path: '/lbsOperate/index' })
            } else if (data.data.role === 'Supervisor') { // 运营主管
              vue.$router.push({ path: '/lbssupervisor/index' })
            } else {
              if (loginType === 'supplychainfinance') {
                vue.$router.push({ path: '/lbssupplychainfinancecustomer/index' })
              } else {
                vue.$router.push({ path: '/cn/lbs/init' })
              }
            }
          } else {
            vue.$mui.alert('Authorize failed! The response is: \n' + JSON.stringify(data) + '.', 'Error', 'OK')
          }
        },
        error: function(xhr, type, errorThrown) {
          console.log(type)
          var msg = 'Authorize failed! The response is: \n' + xhr.responseText + '.'
          if (type === 'timeout') {
            msg = 'Authorize failed. Time out!'
          }
          vue.$mui.alert(msg, 'Error', 'OK')
        }
      })
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
  $bg: #fff;
  $dark_gray: #333;
  $light_gray: #333;

  .login-container {
    min-height: 100%;
    width: 100%;
    overflow: hidden;

    .header {
      width: 1200px;
      margin: 50px auto 0;

      h2 {
        float: right;
        font-size: 30px;
      }
    }

    .login-form {
      position: relative;
      width: 520px;
      max-width: 100%;
      padding: 0 35px 0;
      margin: 0 auto;
    }

    .tips {
      font-size: 16px;
      color: #333;
      margin-bottom: 10px;

      span {
        &:first-of-type {
          margin-right: 16px;
        }
      }
    }

    .svg-container {
      padding: 6px 5px 6px 15px;
      color: $dark_gray;
      vertical-align: middle;
      width: 30px;
      display: inline-block;
    }

    .title-container {
      position: relative;

      .title {
        font-size: 26px;
        color: $light_gray;
        margin: 0px auto 20px auto;
        text-align: center;
        font-weight: bold;
      }

      .set-language {
        color: #333;
        position: absolute;
        top: 5px;
        right: 0px;
      }
    }

    .show-pwd {
      position: absolute;
      right: 10px;
      top: 7px;
      font-size: 16px;
      color: $dark_gray;
      cursor: pointer;
      user-select: none;
    }

    .thirdparty-button {
      position: absolute;
      right: 0;
      bottom: 6px;
    }

    .footer {
      text-align: center;
      margin: 150px auto 50px;
    }
  }

  .applogin {
    .el-form-item {
      float: left;
      margin-left: 40px;
      width: 320px;
    }

    .el-input {
      width: 100%;

      input {
        text-align: center !important;
        padding: 0 !important;
      }
    }
  }
</style>

