<template>
  <div v-loading.fullscreen.lock="loading" />
</template>

<script>
export default {
  name: 'Initdata',
  data() {
    return {
      loading: false,
      token: null,
      messageid: '006f7113e5fa48559549c4dfe74e2cd6',
      clientid: 'devin',
      customernumber: window.sessionStorage.getItem('customernumber')
    }
  },
  created() {
    this.loading = true
    var needReload = window.sessionStorage.getItem('needReload')
    if (needReload === '1') {
      window.sessionStorage.setItem('needReload', 0)
      window.location.reload()
    }
    this.token = window.sessionStorage.getItem('token')
    this.getaccoutlist()
    // this.initUserInfo(this.getcurrentnumberpickerdata()[0].value)
  },
  methods: {
    // 获取当前用户account列表
    getaccoutlist() {
      var vue = this
      vue.loading = true
      this.$mui.ajax(this.CNLBSGateway + '/domestic-deposit-experience/deposit/account/allAccounts/' + this.customernumber + '/0/100', {
        dataType: 'json', // 服务器返回json格式数据
        type: 'post', // HTTP请求类型
        timeout: 60000,
        async: false,
        headers: {
          'accept': '*/*',
          'token': vue.token,
          'clientid': vue.clientid,
          'messageid': vue.messageid,
          'Content-Type': 'application/json'
        },
        beforeSend: function() {
          vue.loading = true
        },
        complete: function() {
          vue.loading = false
        },
        success: function(data) {
          // console.log(JSON.stringify(data))
          if (data.code === '200') {
            window.sessionStorage.setItem('savingaccountlist', JSON.stringify(data.data.saving))
            window.sessionStorage.setItem('currentaccountlist', JSON.stringify(data.data.current))
            window.sessionStorage.setItem('termDepositaccountlist', JSON.stringify(data.data.termDeposit))
            window.sessionStorage.setItem('fexaccountlist', JSON.stringify(data.data.fex))
            window.sessionStorage.setItem('stockaccountlist', JSON.stringify(data.data.stock))
            window.sessionStorage.setItem('mutualFundaccountlist', JSON.stringify(data.data.mutualFund))
            window.sessionStorage.setItem('preciousMetalaccountlist', JSON.stringify(data.data.preciousMetal))
            window.sessionStorage.setItem('creditCardaccountlist', JSON.stringify(data.data.creditCard))
            window.sessionStorage.setItem('loanaccountlist', JSON.stringify(data.data.loan))
            vue.initUserInfo(vue.getcurrentnumberpickerdata()[0].value)
          } else {
            vue.$mui.alert('Get accout list failed! The response is: \n' + JSON.stringify(data) + '.', 'Error', 'OK')
            vue.$router.push({ path: '/cn/lbs/login' })
          }
        },
        error: function(xhr, type, errorThrown) {
          console.log(type)
          var msg = 'Get accout list failed! The response is: \n' + xhr.responseText + '.'
          if (type === 'timeout') {
            msg = 'Get accout list failed. Time out!'
          }
          vue.$mui.alert(msg, 'Error', 'OK')
          vue.$router.push({ path: '/cn/lbs/login' })
        }
      })
    },
    initUserInfo(account) {
      var vue = this
      this.$mui.ajax(this.CNLBSGateway + '/domestic-deposit-experience/deposit/account/accountDetails/' + account, {
        dataType: 'json', // 服务器返回json格式数据
        type: 'post', // HTTP请求类型
        timeout: 60000,
        headers: {
          'accept': '*/*',
          'token': vue.token,
          'clientid': vue.clientid,
          'messageid': vue.messageid,
          'Content-Type': 'application/json'
        },
        beforeSend: function() {
          // plus.nativeUI.showWaiting("Loading…", "div");
          // mask.show();
          // document.getElementById("load").style.display = "inline";
        },
        complete: function() {
          vue.loading = false
          // plus.nativeUI.closeWaiting();
          // mask.close();
          // document.getElementById("load").style.display = "none";
        },
        success: function(data) {
          if (data.code === '200') {
            var response = data.data
            window.sessionStorage.setItem('userInfo', JSON.stringify(response.customer))
            // vue.$router.push({path: "/cn/lbs/index"})
            vue.$router.push({ path: '/cn/lbs/cross/board/payment' })
          } else {
            vue.$mui.alert('Get user info failed! The response is: \n' + JSON.stringify(data) + '.', 'Error', 'OK')
            vue.$router.push({ path: '/cn/lbs/login' })
          }
        },
        error: function(xhr, type, errorThrown) {
          console.log(type)
          var msg = 'Get user info failed! The response is: \n' + xhr.responseText + '.'
          if (type === 'timeout') {
            msg = 'Get user info failed. Time out!'
          }
          vue.$mui.alert(msg, 'Error', 'OK')
          vue.$router.push({ path: '/cn/lbs/login' })
        }
      })
    },
    getcurrentnumberpickerdata() {
      var list = window.sessionStorage.getItem('currentaccountlist')
      var accountlist = JSON.parse(list)
      var pickerdata = []
      for (var i = 0; i < accountlist.length; i++) {
        if (accountlist[i].accountStatus === 'A') {
          pickerdata.push({
            label: accountlist[i].accountNumber,
            value: accountlist[i].accountNumber
          })
        }
      }
      if (pickerdata.length === 0) {
        list = window.sessionStorage.getItem('savingaccountlist')
        accountlist = JSON.parse(list)
        pickerdata = []
        for (var j = 0; j < accountlist.length; j++) {
          if (accountlist[j].accountStatus === 'A') {
            pickerdata.push({
              label: accountlist[j].accountNumber,
              value: accountlist[j].accountNumber
            })
          }
        }
      }
      return pickerdata
    }
  }
}
</script>

<style scoped>

</style>
