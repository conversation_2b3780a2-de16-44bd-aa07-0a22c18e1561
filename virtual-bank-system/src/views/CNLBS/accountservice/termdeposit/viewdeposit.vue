<template>
  <div class="viewdeposit" v-loading.fullscreen.lock="loading">
    <el-row>
      <el-col :span="24">
        <div class="main">
          <p class="title">{{$t('lbs.title4')}}</p>
        </div>
      </el-col>
      <el-tooltip effect="dark" placement="bottom">
        <div slot="content" style="width: 330px">{{$t('lbsTips.accountDetailTip.Status')}}</div>
        <el-col :lg="16" :xs="24">
          <el-row>
            <el-col :lg="12" :xs="24">
              <div class="status status-item">
                <p class="p-status">{{$t('lbsTips.accountDetailTip.tip23')}}:</p>
              </div>
            </el-col>
            <el-col :lg="12" :xs="24">
              <div class="status-item item-select">
                <el-select
                  placeholder="Please Select … "
                  v-model="status"
                  class="select-picker"
                  @change="submit()"
                >
                  <el-option :label="$t('lbsTips.accountDetailTip.tip36')" value="All"></el-option>
                  <el-option :label="$t('lbsTips.accountDetailTip.tip35')" value="Completed"></el-option>
                  <el-option :label="$t('lbsTips.accountDetailTip.tip34')" value="ON-Going"></el-option>
                </el-select>
              </div>
            </el-col>
          </el-row>
        </el-col>
      </el-tooltip>
      <el-col :lg="8" :xs="24">
        <div class="item-button">
          <el-tooltip effect="dark" placement="bottom">
            <div slot="content" style="width: 330px">{{$t('lbsTips.accountDetailTip.SetUpFixedDeposit')}}</div>
            <el-button
              class="button btn1"
              style="margin-top: 40px"
              type="primary"
              @click="jumpPage('Apply Term Deposit')"
            >{{$t('lbsTips.accountDetailTip.tip33')}}
            </el-button>
          </el-tooltip>
          <br/>
          <!--          <el-tooltip effect="dark" placement="bottom">-->
          <!--            <div slot="content" style="width: 330px">{{$t('lbsTips.accountDetailTip.SetUpCallDeposit')}}</div>-->
          <!--            <el-button-->
          <!--              class="button btn2"-->
          <!--              type="primary"-->
          <!--              @click="jumpPage('Apply Deposit')"-->
          <!--            >{{$t('lbsTips.accountDetailTip.tip32')}}</el-button>-->
          <!--          </el-tooltip>-->
        </div>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="24">
        <div class="div-bottom" id="deposit-table">
          <p class="title">{{$t('lbsTips.accountDetailTip.tip31')}}</p>
          <el-card shadow="never">
            <div class="el-table__header-wrapper">
              <table cellspacing="0" cellpadding="0" border="0" class="el-table el-table__header"
                     style="width: 100%;">
                <colgroup></colgroup>
                <thead class="has-gutter">
                <tr class="">
                  <th colspan="1" rowspan="1" class="el-table_24_column_116 is-leaf" width="100">
                    <div class="cell">{{$t('lbsTips.accountDetailTip.tip10')}}</div>
                  </th>
                  <th colspan="1" rowspan="1" class="el-table_24_column_117 is-leaf">
                    <el-tooltip effect="dark" placement="top">
                      <div slot="content" style="width: 300px">
                        {{$t('lbsTips.accountDetailTip.EffectiveDate')}}
                      </div>
                      <div class="cell">{{$t('lbsTips.accountDetailTip.tip18')}}</div>
                    </el-tooltip>
                  </th>
                  <th colspan="1" rowspan="1" class="el-table_24_column_118 is-leaf">
                    <el-tooltip effect="dark" placement="top">
                      <div slot="content" style="width: 300px">
                        {{$t('lbsTips.accountDetailTip.Tenor')}}
                      </div>
                      <div class="cell">{{$t('lbsTips.accountDetailTip.tip19')}}</div>
                    </el-tooltip>
                  </th>
                  <th colspan="1" rowspan="1" class="el-table_24_column_119 is-leaf">
                    <el-tooltip effect="dark" placement="top">
                      <div slot="content" style="width: 300px">
                        {{$t('lbsTips.accountDetailTip.Currency')}}
                      </div>
                      <div class="cell">{{$t('lbsTips.accountDetailTip.tip20')}}</div>
                    </el-tooltip>
                  </th>
                  <th colspan="1" rowspan="1" class="el-table_24_column_120 is-leaf">
                    <el-tooltip effect="dark" placement="top">
                      <div slot="content" style="width: 300px">
                        {{$t('lbsTips.accountDetailTip.Interest')}}
                      </div>
                      <div class="cell">{{$t('lbsTips.accountDetailTip.tip21')}}</div>
                    </el-tooltip>
                  </th>
                  <th colspan="1" rowspan="1" class="el-table_24_column_121 is-leaf">
                    <el-tooltip effect="dark" placement="top">
                      <div slot="content" style="width: 300px">
                        {{$t('lbsTips.accountDetailTip.Principal')}}
                      </div>
                      <div class="cell">{{$t('lbsTips.accountDetailTip.tip22')}}</div>
                    </el-tooltip>
                  </th>
                  <th colspan="1" rowspan="1" class="el-table_24_column_122 is-leaf">
                    <el-tooltip effect="dark" placement="top">
                      <div slot="content" style="width: 300px">
                        {{$t('lbsTips.accountDetailTip.Status')}}
                      </div>
                      <div class="cell">{{$t('lbsTips.accountDetailTip.tip23')}}</div>
                    </el-tooltip>
                  </th>
                  <th class="gutter" style="width: 15px;">
                  </th>
                </tr>
                </thead>
              </table>
            </div>
            <el-table :data="tableData" style="width: 100%" height="350" :show-header="false">
              <el-table-column fixed :label="$t('lbsTips.accountDetailTip.tip10')" width="100">
                <template slot-scope="scope">
                  <svg-icon
                    class="icon-view"
                    icon-class="awesome-eye"
                    @click.native.prevent="termDepositDetail(scope.$index, tableData)"
                  />
                </template>
              </el-table-column>
              <el-table-column prop="createdate" :label="$t('lbsTips.accountDetailTip.tip18')"></el-table-column>
              <el-table-column prop="termperiod" :label="$t('lbsTips.accountDetailTip.tip19')"></el-table-column>
              <el-table-column prop="currencycode" :label="$t('lbsTips.accountDetailTip.tip20')"></el-table-column>
              <el-table-column prop="terminterestrate" :label="$t('lbsTips.accountDetailTip.tip21')"></el-table-column>
              <el-table-column prop="depositamount" :label="$t('lbsTips.accountDetailTip.tip22')"></el-table-column>
              <el-table-column prop="maturitystatus" :label="$t('lbsTips.accountDetailTip.tip23')"></el-table-column>
            </el-table>
          </el-card>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
  export default {
    name: "viewdeposit",
    data() {
      return {
        loading: false,
        status: "All",
        value1: "",
        value2: "",
        tableData: [],
        depositDetailList: [],
        completedDepositDetail: [],
        onGoingDepositDetail: []
      };
    },
    mounted() {
      if (window.sessionStorage.getItem("showTips") == "true" && window.sessionStorage.getItem("token")) {
        this.$alert(this.$t('lbs.popupTermDepositList'), this.$t('lbs.Tip'), {
          dangerouslyUseHTMLString: true
        });
      }
      this.depositDetailList = JSON.parse(
        window.sessionStorage.getItem("depositDetailList")
      );
      this.submit();
    },
    methods: {
      submit() {
        if (this.status === "All") {
          this.tableData = this.depositDetailList;
        } else if (this.status === "Completed") {
          for (var i = 0; i < this.depositDetailList.length; i++) {
            if (this.depositDetailList[i].maturitystatus === "Completed") {
              this.completedDepositDetail.push(this.depositDetailList[i]);
            }
          }
          this.tableData = this.completedDepositDetail;
        } else if (this.status === "ON-Going") {
          for (var i = 0; i < this.depositDetailList.length; i++) {
            if (this.depositDetailList[i].maturitystatus === "OnGoing") {
              this.onGoingDepositDetail.push(this.depositDetailList[i]);
            }
          }
          this.tableData = this.onGoingDepositDetail;
        }
        // document.getElementById('deposit-table').removeAttribute("hidden")
      },
      termDepositDetail(index, rows) {
        window.sessionStorage.setItem("index", index);
        window.sessionStorage.setItem(
          "path",
          "/cn/lbs/account/service/termdeposit/applytermdeposit"
        );
        this.$router.push({path: "/cn/lbs/account/service/termdeposit/depositdetail"});
      },
      jumpPage(type) {
        if (type === "Apply Term Deposit") {
          this.$router.push({
            path: "/cn/lbs/account/service/termdeposit/applytermdeposit"
          });
        } else if (type === "Apply Deposit") {
          this.$router.push({path: "/cn/lbs/account/service/termdeposit/applydeposit"});
        }
      }
    }
  };
</script>

<style scoped>
  .viewdeposit p {
    line-height: 1;
  }

  .viewdeposit .main {
    padding: 40px 55px 40px;
  }

  .viewdeposit .title {
    color: #22c1e6;
    font-size: 35px;
  }

  .viewdeposit .status {
    width: 100px;
    margin: 10px auto 0;
  }

  .viewdeposit .p-status {
    font-size: 30px;
  }

  .viewdeposit .button {
    width: 260px;
  }

  .viewdeposit .btn2 {
    margin-top: 10px;
    margin-left: 0;
  }

  .viewdeposit .status-item {
    height: 102px;
    padding-top: 30px;
  }

  .viewdeposit .item-select {
    width: 450px;
    margin: 0 auto;
  }

  .viewdeposit .item-button {
    width: 260px;
    margin: 0 auto;
  }

  .viewdeposit .btn2 {
    margin-top: 30px;
  }

  .viewdeposit .to {
    color: #707070;
    line-height: 36px;
  }

  .viewdeposit .datepicker-center {
    width: 400px;
    margin: 0 auto;
  }

  .viewdeposit .datepicker-to {
    text-align: center;
  }

  .viewdeposit .select-date {
    margin-top: 50px;
  }

  .viewdeposit .div-bottom {
    padding: 40px 60px 0;
  }

  .viewdeposit .icon-view {
    width: 22px;
    height: 22px;
    cursor: pointer;
  }
</style>
<style>
  .viewdeposit input[type="text"] {
    margin-bottom: 0;
  }

  .viewdeposit .el-input--medium .el-input__inner {
    height: 50px;
    width: 450px;
    font-size: 18px;
    color: #707070;
  }

  .viewdeposit .datepicker .el-input--medium .el-input__inner {
    height: 36px;
    width: 400px;
    font-size: 15px;
  }
</style>
