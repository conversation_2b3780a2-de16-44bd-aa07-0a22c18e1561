<template>
  <div>
    <el-row v-loading.fullscreen.lock="loading" class="transaction">
      <el-col :psan="24">
        <el-row>
          <el-col :span="24">
            <div class="title">
              <el-tooltip effect="dark" placement="bottom">
                <div slot="content" style="width: 300px">{{ $t('lbsTips.transactionTips.WithDrawal') }}</div>
                <p style="display: inline-block" class="max-title">{{ $t("route.withdraw") }}</p>
              </el-tooltip>
            </div>
          </el-col>
        </el-row>
      </el-col>
    </el-row>
    <el-row class="applydeposit">
      <el-col :lg="10" :xs="24">
        <div class="main-left">
          <el-tooltip effect="dark" placement="bottom">
            <div slot="content" style="width: 330px">{{ $t('lbsTips.transactionTips.AccountTip') }}</div>
            <p class="title">{{ $t('lbsTips.transactionTips.Account') }}</p>
          </el-tooltip>
          <div class="div-info">
            <el-select
              v-model="depositAccountNumber"
              :placeholder="$t('lbsTips.transactionTips.AccountTip')"
              class="el-input"
            >
              <el-option
                v-for="(item,index) in debitList"
                :key="index"
                :label="item.type + ' ' + item.label"
                :value="item.value"
              />
            </el-select>
            <p v-show="depositAccountNumber.length > 0" style="font-size: 12px;color: #707070;margin-top: 3px">{{ `(
              ${$t('lbs.Balance')} (${currency})：${balance} )` }}</p>
            <el-tooltip effect="dark" placement="bottom">
              <div slot="content" style="width: 330px">{{ $t('lbsTips.accountDetailTip.Amount') }}</div>
              <p class="title">{{ $t('lbsTips.accountDetailTip.tip37') }}</p>
            </el-tooltip>
            <el-input
              v-model="amount"
              :placeholder="$t('lbsTips.accountDetailTip.tip59')"
              class="input-with-select"
              :maxlength="50"
            >
              <el-select
                slot="prepend"
                v-model="currency"
                placeholder
                style="width: 80px"
                @change="changeCurrencyGetBalance"
              >
                <div v-if="depositAccountNumber.substring(depositAccountNumber.length - 3) === '003'">
                  <el-option
                    v-for="(item,index) in currencyList"
                    :key="index"
                    :label="item.ccycode"
                    :value="item.ccycode"
                    :disabled="depositAccountNumber.substring(depositAccountNumber.length - 3) === '003' && item.ccycode === 'CNY'"
                  />
                </div>
                <div v-else>
                  <el-option
                    v-for="(item,index) in currencyList"
                    :key="index"
                    :label="item.ccycode"
                    :value="item.ccycode"
                    :disabled="item.ccycode !== 'CNY'"
                  />
                </div>
              </el-select>
            </el-input>
          </div>
        </div>
      </el-col>
      <el-col :lg="14" :xs="24">
        <div class="main-right">
          <p class="summary">{{ $t('lbsTips.accountDetailTip.tip41') }}</p>
          <el-row>
            <el-col :lg="9" :xs="24">
              <el-tooltip effect="dark" placement="bottom">
                <div slot="content" style="width: 330px">{{ $t('lbsTips.accountDetailTip.Amount') }}</div>
                <p class="select-result">{{ $t('lbsTips.accountDetailTip.tip37') }}:</p>
              </el-tooltip>
            </el-col>
            <el-col :lg="15" :xs="24">
              <p v-if="amount.length === 0" class="select-result">{{ 0 + ' ' +currency }}</p>
              <p v-else class="select-result">{{ currency + ' ' + $lbs.decimal_format(amount) }}</p>
            </el-col>
            <el-col :lg="9" :xs="24">
              <el-tooltip effect="dark" placement="bottom">
                <div slot="content" style="width: 330px">{{ $t('lbsTips.transactionTips.AccountTip') }}</div>
                <p class="select-result">{{ $t('lbsTips.transactionTips.Account') }}:</p>
              </el-tooltip>
            </el-col>
            <el-col :lg="15" :xs="24">
              <p v-if="depositAccountNumber.length === 0" class="select-result">
                {{ $t('lbsTips.accountDetailTip.tip43') }}</p>
              <p v-else class="select-result">{{ depositAccountNumber }}</p>
            </el-col>
          </el-row>
          <el-col :span="24">
            <el-button :disabled="Number(balance) <= 0" class="button btn1" type="primary" @click="submit('next')">
              {{ $t('lbs.Next') }}
            </el-button>
          </el-col>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import axios from 'axios'

export default {
  name: 'Applydeposit',
  data() {
    return {
      token: null,
      amount: '',
      currency: 'CNY',
      depositAccountNumber: '',
      savingList: [],
      currentList: [],
      fexlist: [],
      debitList: [],
      loading: false,
      currencyList: [],
      balance: ''
    }
  },
  watch: {
    amount: {
      handler() {
        this.clearNoNum()
      }
    },
    'depositAccountNumber'() {
      if (this.depositAccountNumber.substring(this.depositAccountNumber.length - 3) === '003') {
        this.currency = 'USD'
      } else {
        this.currency = 'CNY'
      }
      this.selectAccountBalance()
    }
  },
  mounted() {
    this.token = window.sessionStorage.getItem('token')
    if (window.sessionStorage.getItem('showTips') == 'true' && window.sessionStorage.getItem('token')) {
      this.$alert(this.$t('lbs.popupWithdraw'), this.$t('lbs.Tip'), {
        dangerouslyUseHTMLString: true
      })
    }
    this.initdata()
    this.getCurrencyList()
  },
  methods: {
    changeCurrencyGetBalance() {
      if (this.depositAccountNumber.substring(this.depositAccountNumber.length - 3) === '003') {
        this.selectAccountBalance()
      }
    },
    getCurrencyList() {
      const _this = this
      _this.loading = true
      axios.get(`${_this.CNLBSGateway}/domestic-sysadmin-process/sysadmin/currency/currencyTypeRetrieval`, { headers: { token: _this.token }})
        .then(response => {
          _this.loading = false
          if (response.data.code === '200') {
            _this.currencyList = response.data.data
          } else {
            _this.$message.error(response.data.msg, _this.$t('lbs.common.error'))
          }
        })
        .catch(error => {
          _this.loading = false
          _this.$message.error(error.message, _this.$t('lbs.common.error'))
        })
    },
    initdata() {
      const savingAccountList = JSON.parse(
        window.sessionStorage.getItem('savingaccountlist')
      )
      const currentaccountlist = JSON.parse(
        window.sessionStorage.getItem('currentaccountlist')
      )
      const fexaccountlist = JSON.parse(
        window.sessionStorage.getItem('fexaccountlist')
      )
      for (var i = 0; i < savingAccountList.length; i++) {
        if (savingAccountList[i].accountStatus === 'A') {
          this.savingList.push({
            label: savingAccountList[i].accountNumber,
            value: savingAccountList[i].accountNumber,
            type: 'Saving'
          })
        }
      }

      for (var i = 0; i < currentaccountlist.length; i++) {
        if (currentaccountlist[i].accountStatus === 'A') {
          this.currentList.push({
            label: currentaccountlist[i].accountNumber,
            value: currentaccountlist[i].accountNumber,
            type: 'Current'
          })
        }
      }

      for (var i = 0; i < fexaccountlist.length; i++) {
        if (fexaccountlist[i].accountStatus === 'A') {
          this.fexlist.push({
            label: fexaccountlist[i].accountNumber,
            value: fexaccountlist[i].accountNumber,
            type: 'Fex'
          })
        }
      }
      this.debitList = this.savingList.concat(this.currentList).concat(this.fexlist)
    },
    clearNoNum() {
      this.amount = this.amount.replace(/[^\d.]/g, '') // 清除"数字"和"."以外的字符
      this.amount = this.amount.replace(/^\./g, '') // 验证第一个字符是数字而不是字符
      this.amount = this.amount.replace(/\.{2,}/g, '.') // 只保留第一个.清除多余的
      this.amount = this.amount
        .replace('.', '$#$')
        .replace(/\./g, '')
        .replace('$#$', '.')
      this.amount = this.amount.replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3') // 只能输入两个小数
    },
    submit(type) {
      if (
        !this.amount ||
          this.amount.length === 0 ||
          !(typeof Number(this.amount) && !isNaN(this.amount))
      ) {
        this.$mui.alert('Please enter the Amount', 'Warning', 'OK')
      } else if (
        !this.depositAccountNumber ||
          this.depositAccountNumber.length === 0
      ) {
        this.$mui.alert(
          'Please select the Account Number.',
          'Warning',
          'OK'
        )
      } else if (!this.currency || this.currency.length === 0) {
        this.$mui.alert('Please enter the TD Ccy.', 'Warning', 'OK')
      } else {
        const requestData = {
          accountNumber: this.depositAccountNumber,
          withDrawalAmount: this.amount,
          currencycode: this.currency
        }
        window.sessionStorage.setItem(
          'depositInfo',
          JSON.stringify(requestData)
        )
        this.$router.push({
          path: '/cn/lbs/transaction/withdrawform'
        })
      }
    },
    selectAccountBalance() {
      const _this = this
      this.loading = true
      axios.post(`${_this.CNLBSGateway}/domestic-deposit-experience/deposit/account/accountDetails/${_this.depositAccountNumber}`, {}, { headers: { token: _this.token }})
        .then(response => {
          _this.loading = false
          if (response.data.code === '200') {
            if (this.depositAccountNumber.substring(this.depositAccountNumber.length - 3) === '003') {
              // 外汇账户
              if (response.data.data.account.fexbalance !== null && response.data.data.account.fexbalance !== undefined && response.data.data.account.fexbalance.length > 0) {
                _this.balance = 0
                for (let i = 0; i < response.data.data.account.fexbalance.length; i++) {
                  if (response.data.data.account.fexbalance[i].ccy === _this.currency) {
                    _this.balance = response.data.data.account.fexbalance[i].balance
                  }
                }
              } else {
                _this.balance = 0
              }
            } else {
              //  储蓄账户
              _this.balance = response.data.data.account.availablebalance
            }
            console.log(_this.balance)
          } else {
            _this.$message.error(response.data.msg, _this.$t('lbs.common.error'))
          }
        })
        .catch(error => {
          _this.loading = false
          _this.$mui.alert(error.message, _this.$t('lbs.common.error'), _this.$t('lbs.common.confirm'))
        })
    }
  }
}
</script>

<style scoped>
  .transaction .title {
    padding: 30px 28px;
    height: 140px;
    background-color: #FFFFFF;
    margin: 0 25px 0 25px;
    border-radius: 50px;
    background-image: url("../../components/title-bg.png");
    background-repeat: no-repeat;
    background-size: 574px 130px;
    background-position: right;
    box-shadow: 5px 5px 5px #bbbbbb;
  }

  .transaction p {
    color: #707070;
    margin-bottom: 0;
    line-height: 1;
  }

  .transaction .max-title {
    font-size: 50px;
    margin-bottom: 40px;
    font-weight: 500;
    color: #22C1E6;
    margin-top: 15px;
  }

  .transaction .little-title {
    font-size: 30px;
  }

  .applydeposit p {
    line-height: 1;
  }

  .applydeposit .main-left {
    padding: 0 50px 30px;
  }

  .applydeposit .title {
    font-size: 30px;
    color: #707070;
    margin: 100px 0 40px;
  }

  .applydeposit .summary {
    color: #707070;
    font-size: 42px;
    margin-bottom: 60px;
    margin-top: 50px;
  }

  .applydeposit .main-right {
    margin-top: 40px;
    padding: 30px 50px 30px;
    border-left: 1px #cccccc solid;
    height: 430px;
  }

  .applydeposit .select-result {
    color: #707070;
    font-size: 30px;
    margin-bottom: 60px;
  }

  .applydeposit .button {
    float: right;
    width: 100px;
    margin-bottom: 30px;
  }

  .applydeposit .btn2 {
    margin-right: 20px;
  }
</style>

<style>
  .applydeposit .el-input__icon {
    height: 40px;
  }

  .applydeposit .el-input__inner {
    margin-bottom: 0;
    height: 40px;
  }
</style>

