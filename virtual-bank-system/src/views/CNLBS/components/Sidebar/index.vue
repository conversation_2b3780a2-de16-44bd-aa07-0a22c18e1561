<template>
  <div class="lbs-index">
    <div class="imgBox">
      <img alt="" :src="logo" style="cursor:pointer;" @click="jump2()">
    </div>
    <el-scrollbar wrap-class="scrollbar-wrapper" style="padding-bottom: 146px;height: 100%">
      <el-menu
        :show-timeout="200"
        :default-active="activeMenu"
        :unique-opened="false"
        :default-openeds="openeds"
        :collapse="isCollapse"
        mode="vertical"
        text-color="#333333"
        active-text-color="#FFFFFF"
      >
        <sidebar-item
          v-for="route in permission_routers"
          :key="route.path"
          :item="route"
          :base-path="route.path"
        />
      </el-menu>
    </el-scrollbar>
    <div class="div-form">
      <div v-if="sidebar.opened" style="cursor:pointer;" @click="jump2">{{ $t('login.copyright') }}</div>
      <div v-if="sidebar.opened">
        <a rel="noopener noreferrer" href="https://beian.miit.gov.cn/" target="_blank">陕ICP备20000279号-2</a>
      </div>
    </div>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import SidebarItem from './SidebarItem'
import axios from 'axios'

export default {
  components: { SidebarItem },
  data() {
    return {
      openeds: [],
      opened: true,
      logo: require(`@/assets/images/logo-${process.env.VUE_APP_TEMPORARY_PERMISSION}.png`)
    }
  },
  // 判断路由是/UserManagement且接口返回的parentEmail值是null时，就表示这个用户是个父用户，显示管理用户的界面。
  async created() {
    var vue = this
    const data = await axios.get(
      vue.host +
        '/api/portal/developers/personalinfo/' +
        window.localStorage.getItem('username')
    )
    this.sidebar.opened = true
    if (data.data.code === 0) {
      // console.log("Guest:"+JSON.stringify(this.permission_routers))
      this.permission_routers.map(item => {
        vue.openeds.push(item.path)
        if (data.data.data.parentEmail && item.path === '/UserManagement') {
          item.hidden = true
        }
      })
    }
  },
  methods: {
    jump2() {
      this.$router.push({ path: '/guest/introduction' })
    }
  },
  computed: {
    ...mapGetters(['permission_routers', 'sidebar']),
    activeMenu() {
      const route = this.$route
      const { meta, path } = route
      // if set path, the sidebar will highlight the path you set
      if (meta.activeMenu) {
        return meta.activeMenu
      }
      return path
    },
    isCollapse() {
      return !this.sidebar.opened
    }
  }
}
</script>

<style scoped lang="scss">
  .lbs-index .imgBox {
    width: 100%;
    height: 62px;
    line-height: 62px;
    text-align: center;
    background: #fff;
    border-bottom: 1px solid #333;

    img {
      width: 60%;
    }
  }

  .lbs-index .div-form {
    position: absolute;
    background: #fff;
    width: 100%;
    height: 84px;
    text-align: center;
    border-top: 1px solid #ccc;
    margin: 0;
    left: 0;
    bottom: 0;
    font: 10px "Open Sans", sans-serif;
    padding-top: 5px;

    .icon-form {
      width: 40px;
      height: 40px;
    }

    div {
      padding: 10px;
    }
  }
</style>
