<template>
  <div v-loading.fullscreen.lock="loading" class="fund-index">
    <el-row style="margin-bottom: 30px">
      <!-- 我的基金账户 -->
      <el-col :span="12">
        <p class="fund-title">{{ $t('lbs.FundText.MyFundAccount') }}</p>
        <el-card style="margin-bottom: 30px;min-height: 330px">
          <div class="fund-account">
            <p class="text-account color-info" style="margin-top: 30px">{{ $t('lbs.FundText.Account') }}</p>
            <el-select
              v-model="myFundAccount"
              class="account-select"
              :placeholder="$t('lbs.FundText.SelectFundAccount')"
              size="small"
              @change="getFundAccountInformation('info')"
            >
              <el-option
                v-for="item in fundAccountOption"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </div>
          <div class="fund-account fund-account-info">
            <div class="fund-account-ytd">
              <p class="color-info label">{{ $t('lbs.FundText.TotalInvestmentAmount') }}</p>
              <p class="color-info value">{{ fundAccountInfo.totalInvestmentAmount }}</p>
            </div>
            <div class="fund-account-accumulative-return">
              <p class="color-info label">{{ $t('lbs.FundText.TotalMarkerValue') }}</p>
              <p class="color-info value">{{ fundAccountInfo.totalmarkervalue }}</p>
            </div>
            <div class="fund-account-ytd">
              <p class="color-info label">{{ $t('lbs.FundText.TotalNetGainLoss') }}</p>
              <p class="color-info value">{{ fundAccountInfo.totalNetGainLoss }}</p>
            </div>
            <div class="fund-account-accumulative-return">
              <p class="color-info label">{{ $t('lbs.FundText.TotalNetGainLossPct') }}</p>
              <p class="color-info value">{{ fundAccountInfo.totalNetGainLossPct }}</p>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="11" :offset="1">
        <!-- 我的订阅 -->
        <p class="fund-title">{{ $t('lbs.FundText.MySubscription') }}</p>
        <el-card>
          <div class="fund-account" style="margin-bottom: 0">
            <p class="text-account color-info">{{ $t('lbs.FundText.Account') }}</p>
            <el-select
              v-model="fundAccount"
              class="account-select"
              :placeholder="$t('lbs.FundText.SelectFundAccount')"
              size="small"
              @change="getFundAccountInformation('list')"
            >
              <el-option
                v-for="item in fundAccountOption"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </div>
          <el-table :data="mySubscriptionList" height="245" :header-cell-style="{fontSize: '13px'}">
            <el-table-column type="expand">
              <template slot-scope="props">
                <el-form label-position="left" inline class="demo-table-expand">
                  <el-form-item :label="$t('lbs.FundText.FundCode')" :label-width="labelWidth">
                    <span>{{ props.row.fundcode }}</span>
                  </el-form-item>
                  <el-form-item :label="$t('lbs.FundText.AvailableHoldingNo')" :label-width="labelWidth">
                    <span>{{ props.row.avaliableholdingno }}</span>
                  </el-form-item>
                  <el-form-item :label="$t('lbs.FundText.AveragePrice')" :label-width="labelWidth">
                    <span>{{ props.row.averageprice }}</span>
                  </el-form-item>
                  <el-form-item :label="$t('lbs.FundText.CurrencyCode')" :label-width="labelWidth">
                    <span>{{ props.row.currencycode }}</span>
                  </el-form-item>
                  <el-form-item :label="$t('lbs.FundText.InvestmentAmount')" :label-width="labelWidth">
                    <span>{{ props.row.investmentamount }}</span>
                  </el-form-item>
                  <el-form-item :label="$t('lbs.FundText.LastUpdateDate')" :label-width="labelWidth">
                    <span>{{ props.row.lastupdatedate }}</span>
                  </el-form-item>
                  <el-form-item :label="$t('lbs.FundText.MarketPrice')" :label-width="labelWidth">
                    <span>{{ props.row.marketprice }}</span>
                  </el-form-item>
                  <el-form-item :label="$t('lbs.FundText.MarketValue')" :label-width="labelWidth">
                    <span>{{ props.row.marketvalue }}</span>
                  </el-form-item>
                  <el-form-item :label="$t('lbs.FundText.NetGainLoss')" :label-width="labelWidth">
                    <span>{{ props.row.netGainLoss }}</span>
                  </el-form-item>
                  <el-form-item :label="$t('lbs.FundText.NetGainLossPct')" :label-width="labelWidth">
                    <span>{{ props.row.netGainLossPct }}</span>
                  </el-form-item>
                  <el-form-item :label="$t('lbs.FundText.SharesHoldingNo')" :label-width="labelWidth">
                    <span>{{ props.row.sharesholdingno }}</span>
                  </el-form-item>
                </el-form>
              </template>
            </el-table-column>
            <el-table-column width="94" :label="$t('lbs.FundText.FundCode')" align="center">
              <template slot-scope="scope">
                <span class="fund-code" @click="viewThisFund(scope.row.fundcode)">{{ scope.row.fundcode }}</span>
              </template>
            </el-table-column>
            <el-table-column :label="$t('lbs.FundText.AvailableHoldingNo')" prop="avaliableholdingno" align="center" />
            <el-table-column :label="$t('lbs.FundText.SharesHoldingNo')" prop="sharesholdingno" align="center" />
            <el-table-column width="113" :label="$t('lbs.FundText.AveragePrice')" prop="averageprice" align="center" />
            <el-table-column width="121" :label="$t('lbs.FundText.CurrencyCode')" prop="currencycode" align="center" />
          </el-table>
        </el-card>
      </el-col>
      <!-- 基金列表 -->
      <el-col :span="24">
        <p class="fund-title">{{ $t('lbs.FundText.FundListing') }}</p>
        <el-card>
          <el-table :data="fundList" height="627">
            <el-table-column width="94" :label="$t('lbs.FundText.FundCode')" align="center">
              <template slot-scope="scope">
                <span class="fund-code" @click="viewThisFund(scope.row.fundCode)">{{ scope.row.fundCode }}</span>
              </template>
            </el-table-column>
            <el-table-column :label="$t('lbs.FundText.FundName')" prop="fundName" align="center" />
            <el-table-column :label="$t('lbs.FundText.FundCurrency')" prop="fundCurrency" align="center" />
            <el-table-column :label="$t('lbs.FundText.FundType')" prop="fundType" align="center" />
            <el-table-column :label="$t('lbs.FundText.Geographic')" prop="geographic" align="center" />
            <el-table-column :label="$t('lbs.FundText.LastNAV')" prop="lastNAV" align="center" />
            <el-table-column :label="$t('lbs.FundText.ManagementFee')" prop="managementFee" align="center" />
            <el-table-column :label="$t('lbs.FundText.ValuationDate')" prop="valuationDate" align="center" />
          </el-table>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import axios from 'axios'

export default {
  data: () => ({
    token: null,
    loading: false,
    labelWidth: '260px',
    myFundAccount: '',
    fundAccount: '',
    fundAccountOption: [],
    fundAccountInfo: {},
    mySubscriptionList: [],
    fundList: []
  }),
  computed: {},
  watch: {},
  created() {
    this.token = window.sessionStorage.getItem('token')
    this.getFundAccountOption()
    this.getFundList()
    this.getFundAccountInformation()
  },
  mounted() {
  },
  methods: {
    // 查看基金详情
    viewThisFund: function(fundCode) {
      this.$router.push({ path: `/cn/lbs/investment/fundtrading/detailquotation?fundCode=${fundCode}` })
    },
    // 获取基金账户的详情信息
    getFundAccountInformation: function(type) {
      const _this = this
      if (!_this.myFundAccount) return
      _this.loading = true
      const requestData = { fundAccountNumber: _this.myFundAccount, fundCode: '' }
      axios.post(`${_this.CNLBSGateway}/fund-experience/fund/fundHoldingEnquiry`, requestData, { headers: { token: _this.token }})
        .then(response => {
          _this.loading = false
          if (response.data.code === '200') {
            if (type === 'info') {
              _this.fundAccountInfo = response.data.data
            } else if (type === 'list') {
              _this.mySubscriptionList = response.data.data.fundList
            } else {
              _this.fundAccountInfo = response.data.data
              _this.mySubscriptionList = response.data.data.fundholdlist
            }
          } else if (response.data.code === '404010') {
            // _this.$message.error(response.data.msg, _this.$t('lbs.common.error'))
          } else {
            _this.$message.error(response.data.msg, _this.$t('lbs.common.error'))
          }
        })
        .catch(error => {
          _this.loading = false
          _this.$message.error(error.message, _this.$t('lbs.common.error'))
        })
    },
    // 获取基金列表
    getFundList() {
      const _this = this
      _this.loading = true
      const requestData = { index: 0, items: 999 }
      axios.post(`${_this.CNLBSGateway}/fund-experience/fund/fundList`, requestData, { headers: { token: _this.token }})
        .then(response => {
          _this.loading = false
          if (response.data.code === '200') {
            const result = response.data.data
            for (const item of result) {
              item.valuationDate = _this.$moment(item.valuationDate).format('YYYY-MM-DD HH:mm:ss')
            }
            _this.fundList = response.data.data
          } else if (response.data.code === '404010') {
            // _this.$message.error(response.data.msg, _this.$t('lbs.common.error'))
          } else {
            _this.$message.error(response.data.msg, _this.$t('lbs.common.error'))
          }
        })
        .catch(error => {
          _this.loading = false
          _this.$message.error(error.message, _this.$t('lbs.common.error'))
        })
    },
    // 获取状态为A的基金账户列表
    getFundAccountOption() {
      const tempMutualFundAccountList = JSON.parse(window.sessionStorage.getItem('mutualFundaccountlist'))
      for (const item of tempMutualFundAccountList) {
        if (item.accountStatus === 'A') {
          this.fundAccountOption.push(
            {
              label: item.accountNumber,
              value: item.accountNumber
            }
          )
        }
      }
      if (this.fundAccountOption && this.fundAccountOption[0]) {
        this.myFundAccount = this.fundAccountOption[0].value
      }
      if (this.fundAccountOption && this.fundAccountOption[0]) {
        this.fundAccount = this.fundAccountOption[0].value
      }
    }
  }
}
</script>

<style lang="scss" scoped>
  .fund-index {
    padding: 0 50px 30px;

    .fund-title {
      color: #22C1E6;
      font-size: 20px;
      display: inline-block;
    }

    .text-account {
      margin-right: 30px;
      display: inline-block;
      font-size: 18px;
    }

    .fund-account {
      text-align: center;
      margin-bottom: 20px;
    }

    .account-select {
      width: 260px;
    }

    .color-info {
      color: #707070;
    }

    .total-amount-label {
      margin: 0 0 0 10px;
    }

    .total-amount-value {
      width: 50%;
      font-size: 20px;
    }

    .fund-account-ytd {
      width: 50%;
      display: inline-block;
      float: left;
    }

    .fund-account-accumulative-return {
      width: 50%;
      display: inline-block;
      float: left;
    }

    .fund-account-info {
      margin: 0 auto;
    }

    .label {
      margin-bottom: 0;
      margin-top: 10px;
    }

    .value {
      margin-bottom: 30px;
      color: #22C1E6;
    }

    .fund-search {
      background-color: #ADF0FF;
    }

    .text--center {
      text-align: center;
    }

    .search-info {
      width: 70%;
      margin: 0 auto;
    }

    .amount-label {
      width: 50%;
      float: left;
      display: inline-block;
      margin-bottom: 15px;
    }

    .amount-value {
      width: 50%;
      float: right;
      display: inline-block;
      margin-bottom: 15px;
    }

    .fund-code {
      color: #22C1E6;
      text-decoration: underline;
      cursor: pointer;
    }

    .fund-code:hover {
      color: #315efb;
    }

  }
</style>

<style lang="scss">
  .fund-index {
    .el-form-item {
      margin-bottom: 0;
    }

    label {
      font-weight: 400;
      color: #99a9bf;
    }

    .el-form-item--medium .el-form-item__content, .el-form-item--medium .el-form-item__label {
      line-height: 20px;
    }

    .el-table__expanded-cell[class*=cell] {
      padding: 5px 68px;
    }

    .el-table--medium td, .el-table--medium th {
      padding: 5px 0;
    }
  }
</style>
