<template>
  <div v-loading.fullscreen.lock="loading">
    <!--支付收款人信息表 Fixeddeposit-->
    <template>
      <el-table
        :data="tableDataList_six.slice((currentPage-1)*pagesize,currentPage*pagesize)"
        stripe
        style="width: 100%"
      >
        <!--序号:label="$t('lbs.common.number')"-->
        <el-table-column
          type="index"
          :label="$t('lbs.common.number')"
          align="center"
        />
        <!--收款人行业编号-->
        <el-table-column
          prop="payeecategoryid"
          :label="$t('Workflow.industryNum')"
          align="center"
        />

        <!--收款人行业-->
        <el-table-column
          prop="payeecategory"
          :label="$t('Workflow.Payeeindustry')"
          align="center"
        />
        <!--更新时间-->
        <el-table-column
          prop="lastupdatedate"
          :label="$t('Workflow.UpdateTime')"
          align="center"
        />
        <!--创建时间-->
        <el-table-column
          prop="createdate"
          :label="$t('Workflow.Cteatime')"
          align="center"
        />

        <!--支付收款人操作-->
        <el-table-column :label="$t('Workflow.operation')" width="200" align="center">
          <template slot-scope="scope">
            <el-button
              v-if="scope"
              size="mini"
              type="text"
              @click="PaymentEditFun(scope.row)"
            >
              {{ $t("Workflow.Edit") }}
            </el-button>
            <el-button
              v-if="scope"
              size="mini"
              type="text"
              @click="PaymentDelFun(scope.row)"
            >
              {{ $t("Workflow.Delete") }}
            </el-button>

          </template>
        </el-table-column>
      </el-table>
    </template>
    <div class="block">
      <el-pagination
        :current-page="currentPage"
        :page-sizes="[5, 10, 20, 40]"
        :page-size="pagesize"
        :total="tableDataList_six.length"
        layout="total, prev, pager, next"
        style="margin-top: 20px; text-align: right;"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>
    <!--定存利率表弹框start-->
    <el-dialog
      :title="dialogVisiblesix.title === 'add' ? $t('Operate.IncreasePayee') : $t('Operate.Payeeedit')"
      :visible.sync="dialogVisiblesix.show"
      width="40%"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <el-form ref="ruleFormList" :model="PayeeIndustry" label-width="180px" label-position="left" size="small">
        <!--收款人行业编号-->
        <el-form-item :label="$t('Workflow.industryNum')">
          <el-input v-model="PayeeIndustry.payeecategoryid" />
        </el-form-item>
        <!--收款人行业-->
        <el-form-item :label="$t('Workflow.Payeeindustry')">
          <el-input v-model="PayeeIndustry.payeecategory" />
        </el-form-item>
        <!--更新时间-->
        <el-form-item :label="$t('Workflow.UpdateTime')">
          <div class="block">
            <el-date-picker
              v-model="PayeeIndustry.lastupdatedate"
              type="date"
              align="center"
              style="width: 100%"
            />
          </div>
        </el-form-item>
        <!--创建时间-->
        <el-form-item :label="$t('Workflow.Cteatime')">
          <div class="block">
            <el-date-picker
              v-model="PayeeIndustry.createdate"
              type="date"
              align="center"
              style="width: 100%"
            />
          </div>
        </el-form-item>

      </el-form>

      <span slot="footer" class="dialog-footer">
        <!--取消-->
        <el-button size="small" @click="dialogVisiblesix.show = false">{{ $t('Workflow.Cancel') }}</el-button>
        <!--支付收款人增加确定-->
        <el-button v-if="dialogVisiblesix.title === 'add'" size="small" type="primary" @click="PayAdd()">{{ $t('Operate.Determine') }}</el-button>
        <!--支付收款人编辑确定-->
        <el-button v-if="dialogVisiblesix.title === 'edit'" size="small" type="primary" @click="PayEdit()">{{ $t('Operate.Okedit') }}</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import moment from 'moment'
import axios from 'axios'

export default {
  components: {},
  props: {},
  data() {
    return {
      loading: false,
      name: '',
      tableDataList_six: [], // 外汇利率标的数据
      createdate: [],
      lastupdatedate: [],
      PayeeIndustry: { // 弹框表单
        createdate: '',
        lastupdatedate: '',
        payeecategory: '',
        payeecategoryid: '',
        id: '',
        start: 0,
        end: 10000
      },
      currentPage: 1,
      pagesize: 10,
      totalPages: 0,
      token: null,
      dialogVisiblesix: {
        show: false,
        title: 'add'
      }

    }
  },
  watch: {},
  created() {
  },
  mounted() {
    this.token = window.sessionStorage.getItem('token')
  },
  beforeDestroy() {
  },
  methods: {
    getPaymentFun() {
      this.loading = true
      // 支付收款人信息表
      axios.post(`${this.LBSGateway}/payment-service/payment/category/findMany`, { start: 0, end: 10000 }, { headers: { token: this.token }}).then(res => {
        if (res.data.code === '200') {
          const datas = res.data.data
          const arr = JSON.parse(datas)
          this.tableDataList_six = arr
          this.tableDataList_six.forEach(item => {
            item.createdate = moment(item.createdate).format('YYYY-MM-DD HH:mm:ss')
            item.lastupdatedate = moment(item.lastupdatedate).format('YYYY-MM-DD HH:mm:ss')
          })
        } else {
          this.$message({
            showClose: true,
            message: res.data.msg,
            type: 'error'
          })
        }
      })
        .finally(() => {
          this.loading = false
        })
    },
    // 点击加号出现弹框
    goPayplus() {
      this.dialogVisiblesix.title === 'add'
      this.dialogVisiblesix.show = true
      this.PayeeIndustry = {}
    },
    // 确认添加 /payment-service/payment/category/insert  new Date(this.Insurance.startDate).getTime()
    PayAdd() {
      const str = {
        createdate: new Date(moment(this.PayeeIndustry.createdate).format('YYYY-MM-DD HH:mm:ss')).getTime(),
        lastupdatedate: new Date(moment(this.PayeeIndustry.lastupdatedate).format('YYYY-MM-DD HH:mm:ss')).getTime(),
        payeecategory: this.PayeeIndustry.payeecategory,
        payeecategoryid: this.PayeeIndustry.payeecategoryid,
        id: this.PayeeIndustry.id,
        start: 0,
        end: 10000
      }
      axios.post(`${this.LBSGateway}/payment-service/payment/category/insert`, str, { headers: { token: this.token }}).then(res => {
        if (res.data.code === '200') {
          this.dialogVisiblesix.show = false // 关闭弹框
          this.getPaymentFun() // 刷新当前外汇利率列表
        } else {
          this.$message({
            showClose: true,
            message: res.data.msg,
            type: 'error'
          })
        }
        this.dialogVisiblesix.show = false
      })
    },
    // 点击编辑出现弹框
    PaymentEditFun(row) {
      this.dialogVisiblesix.show = true
      this.dialogVisiblesix.title = 'edit'
      this.PayeeIndustry = row
    },
    // 确认编辑 /payment-service/payment/category/update
    PayEdit() {
      const str = {
        createdate: new Date(moment(this.PayeeIndustry.createdate).format('YYYY-MM-DD HH:mm:ss')).getTime(),
        lastupdatedate: new Date(moment(this.PayeeIndustry.lastupdatedate).format('YYYY-MM-DD HH:mm:ss')).getTime(),
        payeecategory: this.PayeeIndustry.payeecategory,
        payeecategoryid: this.PayeeIndustry.payeecategoryid,
        id: this.PayeeIndustry.id,
        start: 0,
        end: 10000
      }
      axios.post(`${this.LBSGateway}/payment-service/payment/category/update`, str, { headers: { token: this.token }}).then(res => {
        if (res.data.code === '200') {
          this.dialogVisiblesix.show = false // 关闭弹框
          this.getPaymentFun() // 刷新当前外汇利率列表
        } else {
          this.$message({
            showClose: true,
            message: res.data.msg,
            type: 'error'
          })
          this.dialogVisiblesix.show = false
        }
      })
    },

    // 删除
    PaymentDelFun(row) {
      this.$confirm(this.$t('lbs.common.delMsg'), this.$t('lbs.common.tip'), {
        confirmButtonText: this.$t('lbs.common.confirm'),
        cancelButtonText: this.$t('lbs.common.cancel'),
        type: 'warning'
      }).then(() => {
        const str = {
          id: row.id,
          start: 0,
          end: 10000
        }
        axios.post(`${this.LBSGateway}/payment-service/payment/category/delete`, str, { headers: { token: this.token }}).then(res => {
          if (res.data.code === '200') {
            this.dialogVisiblesix.show = false // 关闭弹框
            this.getPaymentFun() // 刷新当前列表
          } else {
            this.$message({
              showClose: true,
              message: res.data.msg,
              type: 'error'
            })
          }
        })
      }).catch(() => {})
    },

    handleSizeChange: function(size) {
      this.pagesize = size
    },
    handleCurrentChange: function(currentPage) {
      this.currentPage = currentPage
    }
  }

}
</script>

<style lang='scss' scoped>
  .modalTitle{
    width: 100%;
    height: 43px;
    padding-left: 10px;
    font-weight: 600;

    h4{
      line-height: 43px;
      font-size: 15px;
    }
  }
  .plus{
    font-size: 30px;
    font-weight: bold;
  }
</style>
