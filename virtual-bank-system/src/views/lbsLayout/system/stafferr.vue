<template>
  <div>
    <el-card v-if="!dialogCaptureDemographic" v-loading.fullscreen.lock="loading" shadow="never" class="workflow">
      <div slot="header" class="clearfix">
        <span>{{ $t("Workflow.Employee") }}</span>
      </div>

      <div class="data-action-button-group">
        <div>
          <el-button size="small" icon="el-icon-plus" type="primary" plain @click="getinsetList()">{{ $t('permission.create') }}</el-button>
        </div>

        <!-- 刷新按钮 -->
        <el-button icon="el-icon-refresh" size="small" circle @click="getOpearFun()" />
      </div>

      <el-card class="content" shadow="never">
        <el-table
          class="params-table"
          style="margin-top: 10px"
          :data="tableData.slice((currentPage-1)*pagesize,currentPage*pagesize)"
        >
          <el-table-column type="index" />

          <el-table-column :label="$t('Workflow.Emplonumber')" width="200" align="center">
            <template slot-scope="scope">
              <span
                style="color: #0a58ca;cursor: pointer"
                @click="Rdatail(scope.row)"
              >
                {{ scope.row.staffNumber }}
              </span>
            </template>
          </el-table-column>
          <el-table-column
            prop="emailAddress"
            :label="$t('Workflow.emailAddress')"
            align="center"
            show-overflow-tooltip
          />
          <el-table-column
            prop="username"
            :label="$t('Workflow.username')"
            align="center"
          />
          <!--部门-->
          <el-table-column
            prop="department"
            :label="$t('Workflow.department')"
            align="center"
          />
          <!--角色-->
          <el-table-column
            prop="role"
            :label="$t('Workflow.role')"
            align="center"
          />
          <!--更新时间-->
          <el-table-column
            prop="lastupdatedate"
            :label="$t('Workflow.UpdateT')"
            align="center"
          />
          <!--配置时间-->
          <el-table-column
            prop="createdate"
            :label="$t('Workflow.Configuration')"
            align="center"
          />
          <!--操作-->
          <el-table-column :label="$t('Workflow.operation')" width="200" align="center">
            <template slot-scope="scope">
              <el-button
                v-if="scope"
                size="mini"
                type="text"
                @click="getEditFun(scope.row)"
              >
                {{ $t("Workflow.Edit") }}
              </el-button>
              <el-button
                v-if="scope"
                size="mini"
                type="text"
                @click="getDelFun(scope.row)"
              >
                {{ $t("Workflow.Delete") }}
              </el-button>
            </template>
          </el-table-column>
        </el-table>

        <div class="block">
          <el-pagination
            :current-page="currentPage"
            :page-sizes="[5, 10, 20, 40]"
            :page-size="pagesize"
            :total="tableData.length"
            layout="total, prev, pager, next"
            style="margin-top: 20px; text-align: right;"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-card>

      <el-dialog
        :title="dialogFormInset.title === 'add' ?
          $t('lbs.common.addStaffer') : dialogFormInset.title === 'edit' ?
            $t('lbs.common.editStaffer') : $t('lbs.common.viewStaffer')"
        :visible.sync="dialogFormInset.show"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        width="40%"
        destroy-on-close
      >
        <el-form ref="fromList" :model="fromList" :rules="rules" label-width="150px" label-position="left" size="small" :disabled="dialogFormInset.title === 'details'">
          <!--label="员工编号:"-->
          <el-form-item :label="$t('lbs.common.stafferNumber')" prop="staffNumber">
            <el-input v-model="fromList.staffNumber" />
          </el-form-item>
          <!--部门-->
          <el-form-item :label="$t('Workflow.department')" prop="departmentId">
            <el-select v-model="fromList.departmentId" :placeholder="$t('Workflow.pleaseSelect')" style="width: 100%" @change="changeSelect">
              <el-option
                v-for="(item,index) in brandOptions"
                :key="index"
                :label="item.department"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
          <!--角色-->
          <el-form-item :label="$t('Workflow.role')" prop="roleId">
            <el-select v-model="fromList.roleId" :placeholder="$t('Workflow.pleaseSelect')" style="width: 100%">
              <el-option
                v-for="(item,index) in typeOptions"
                :key="'role'+index"
                :label="item.role"
                :value="item.id"
              />
            </el-select>
          </el-form-item>

          <!--用户名-->
          <el-form-item :label="$t('lbs.common.username')" prop="username">
            <el-input v-model="fromList.username" />
          </el-form-item>
          <!--邮箱-->
          <el-form-item :label="$t('Workflow.emailaddress')" prop="emailAddress">
            <el-input v-model="fromList.emailAddress" />
          </el-form-item>
          <!--密码-->
          <el-form-item :label="$t('lbs.common.password')" prop="pwd">
            <el-input v-model="fromList.pwd" />
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button size="small" @click="dialogFormInset.show = false">{{ $t('lbs.common.cancel') }}</el-button>
          <!--新 增 确 定-->
          <el-button v-if="dialogFormInset.title === 'add'" type="primary" size="small" @click="getInsetFun">{{ $t('Operate.Determine') }}</el-button>
          <!--编 辑 确 定-->
          <el-button v-if="dialogFormInset.title === 'edit'" type="primary" size="small" @click="getListEdit">{{ $t('Operate.Okedit') }}</el-button>
        </div>
      </el-dialog>

    </el-card>
  </div>
</template>

<script>
import { validateEMail } from '@/utils/validateVal'
import moment from 'moment'
import axios from 'axios'

export default {
  components: {
  },
  props: [],
  data() {
    // 邮箱
    const validateEmailTex = (rule, value, callback) => {
      if (!validateEMail(value).result) {
        callback(new Error(this.$t('Operate.enter')))
      } else {
        callback()
      }
    }
    return {
      name: '',
      dialogCaptureDemographic: false,
      tableData: [], // 列表table
      loading: false,
      token: null,
      currentPage: 1,
      pagesize: 10,
      totalPages: 0,
      fromList: {},
      // 第一个下拉内容
      brandOptions: [],
      // 第二个下拉内容
      typeOptions: [],
      MobilyJson: {}, // 下拉
      dialogFormInset: {
        show: false, // 是否显示
        title: 'add' // 默认弹框类型-编辑
      },
      rules: {
        staffNumber: [
          { required: true, trigger: 'blur', message: this.$t('Workflow.staffNumberIsRequired') }
        ],
        departmentId: [
          { required: true, trigger: 'blur', message: this.$t('Workflow.departmentIsRequired') }
        ],
        roleId: [
          { required: true, trigger: 'blur', message: this.$t('Workflow.roleIsRequired') }
        ],
        username: [
          { required: true, trigger: 'blur', message: this.$t('Workflow.usernameIsRequired') }
        ],
        emailAddress: [
          { required: true, trigger: 'blur', message: this.$t('Workflow.emailIsRequired') },
          { trigger: 'blur', validator: validateEmailTex }
        ],
        pwd: [
          { required: true, trigger: 'blur', message: this.$t('Workflow.pwdIsRequired') }
        ]
      }
    }
  },
  watch: {},
  created() {
    this.token = window.sessionStorage.getItem('token')
  },
  mounted() {
    this.getOpearFun()
  },
  beforeDestroy() {
  },
  methods: {
    Rdatail(row) {
      this.dialogFormInset.title = 'details'
      this.dialogFormInset.show = true
      this.fromList = row
      console.log(row)
    },
    // 列表
    getOpearFun() {
      const _this = this
      _this.loading = true
      axios.post(`${_this.LBSGateway}/sysadmin-process/sysadmin/adminuser/findMany`, { start: 0, end: 10000 }, { headers: { token: _this.token }}).then(res => {
        _this.loading = false
        if (res.data.code === '200') {
          this.tableData = res.data.data.data
          this.tableData.forEach(item => {
            item.createdate = moment(item.createdate).format('YYYY-MM-DD HH:mm:ss')
            item.lastupdatedate = moment(item.lastupdatedate).format('YYYY-MM-DD HH:mm:ss ')
          })
        } else {
          this.$message({
            showClose: true,
            message: res.data.msg,
            type: 'error'
          })
        }
      })
    },

    // 删除
    getDelFun(row) {
      this.$confirm(this.$t('lbs.common.delMsg'), this.$t('lbs.common.tip'), {
        confirmButtonText: this.$t('lbs.common.confirm'),
        cancelButtonText: this.$t('lbs.common.cancel'),
        type: 'warning'
      }).then(() => {
        const str = {
          id: row.id
        }
        axios.post(`${this.LBSGateway}/sysadmin-process/sysadmin/adminuser/delete`, str, { headers: { token: this.token }}).then(res => {
          if (res.data.code === '200') {
            this.getOpearFun()
          } else {
            this.$message({
              showClose: true,
              message: res.data.msg,
              type: 'error'
            })
          }
        })
      }).catch(() => {})
    },

    // 编辑
    getEditFun(row) {
      this.getOneSel()// 下拉
      this.dialogFormInset.show = true
      this.dialogFormInset.title = 'edit'
      this.fromList = JSON.parse(JSON.stringify(row))
      this.getDepartmentSel(this.fromList.departmentId)
    },
    getDepartmentSel(id) {
      const _this = this
      _this.loading = true
      const requestData = {
        departmentId: id,
        start: 0,
        end: 10000
      }
      axios.post(`${_this.LBSGateway}/sysadmin-service/role/findMany`, requestData, { headers: { token: _this.token }}).then(res => {
        _this.loading = false
        if (res.data.code === '200') {
          this.typeOptions = JSON.parse(res.data.data)
        } else {
          this.$message({
            showClose: true,
            message: res.data.msg,
            type: 'error'
          })
        }
      })
    },
    // 新增
    getinsetList() {
      this.dialogFormInset.show = true
      this.dialogFormInset.title = 'add'
      this.fromList = {}
      this.getOneSel()// 下拉
    },

    getInsetFun() {
      // 请填写员工编号
      if (this.fromList.staffNumber === '' || this.fromList.staffNumber === null || this.fromList.staffNumber === undefined) {
        this.$message({ showClose: true, message: this.$t('Operate.PleaseNumber'), type: 'error' })
        return
      }
      // 请选择员工部门
      if (this.fromList.departmentId === '' || this.fromList.departmentId === null || this.fromList.departmentId === undefined) {
        this.$message({ showClose: true, message: this.$t('Operate.PleaseDepartment'), type: 'error' })
        return
      }
      // 请选择员工角色
      if (this.fromList.roleId === '' || this.fromList.roleId === null || this.fromList.roleId === undefined) {
        this.$message({ showClose: true, message: this.$t('Operate.PleaseRole'), type: 'error' })
        return
      }
      // 请填写用户名
      if (this.fromList.username === '' || this.fromList.username === null || this.fromList.username === undefined) {
        this.$message({ showClose: true, message: this.$t('Operate.PleaseUser'), type: 'error' })
        return
      }
      // 请填写电子邮件
      if (this.fromList.emailAddress === '' || this.fromList.emailAddress === null || this.fromList.emailAddress === undefined) {
        this.$message({ showClose: true, message: this.$t('Operate.PleasePass'), type: 'error' })
        return
      }
      // 请填写用户密码
      if (this.fromList.pwd === '' || this.fromList.pwd === null || this.fromList.pwd === undefined) {
        this.$message({ showClose: true, message: this.$t('Operate.PleaseEmail'), type: 'error' })
        return
      }

      this.$refs.fromList.validate((valid) => {
        if (valid) {
          this.loading = true
          const requestData = {
            departmentId: this.fromList.departmentId,
            roleId: this.fromList.roleId,
            staffNumber: this.fromList.staffNumber,
            emailAddress: this.fromList.emailAddress,
            pwd: this.fromList.pwd,
            username: this.fromList.username
          }
          axios.post(`${this.LBSGateway}/sysadmin-process/sysadmin/adminuser/insert`, requestData, { headers: { token: this.token }}).then(res => {
            this.loading = false
            if (res.data.code === '200') {
              this.dialogFormInset.show = false
              // this.$router.push({path: `/system/index`})
              this.getOpearFun() // 列表
            } else {
              this.$message({
                showClose: true,
                message: res.data.msg,
                type: 'error'
              })
            }
          })
        }
      })
    },
    // 修改提交/adminuser/update
    getListEdit() {
      // 请填写员工编号
      if (this.fromList.staffNumber === '' || this.fromList.staffNumber === null || this.fromList.staffNumber === undefined) {
        this.$message({ showClose: true, message: this.$t('Operate.PleaseNumber'), type: 'error' })
        return
      }
      // 请选择员工部门
      if (this.fromList.departmentId === '' || this.fromList.departmentId === null || this.fromList.departmentId === undefined) {
        this.$message({ showClose: true, message: this.$t('Operate.PleaseDepartment'), type: 'error' })
        return
      }
      // 请选择员工角色
      if (this.fromList.roleId === '' || this.fromList.roleId === null || this.fromList.roleId === undefined) {
        this.$message({ showClose: true, message: this.$t('Operate.PleaseRole'), type: 'error' })
        return
      }
      // 请填写用户名
      if (this.fromList.username === '' || this.fromList.username === null || this.fromList.username === undefined) {
        this.$message({ showClose: true, message: this.$t('Operate.PleaseUser'), type: 'error' })
        return
      }
      // 请填写电子邮件
      if (this.fromList.emailAddress === '' || this.fromList.emailAddress === null || this.fromList.emailAddress === undefined) {
        this.$message({ showClose: true, message: this.$t('Operate.PleaseEmail'), type: 'error' })
        return
      }
      // 请填写用户密码
      if (this.fromList.pwd === '' || this.fromList.pwd === null || this.fromList.pwd === undefined) {
        this.$message({ showClose: true, message: this.$t('Operate.PleasePass'), type: 'error' })
        return
      }
      this.$refs.fromList.validate((valid) => {
        if (valid) {
          const _this = this
          _this.loading = true
          const requestData = {
            branchCode: this.fromList.branchCode,
            id: this.fromList.id,
            departmentId: this.fromList.departmentId,
            roleId: this.fromList.roleId,
            emailAddress: this.fromList.emailAddress,
            staffNumber: this.fromList.staffNumber,
            username: this.fromList.username,
            pwd: this.fromList.pwd
          }
          axios.post(`${_this.LBSGateway}/sysadmin-process/sysadmin/adminuser/update`, requestData, { headers: { token: _this.token }}).then(response => {
            // 点击登录提交的时候，给本地储存资料
            // this.$utils.store.set("studyUrl", data.body.data);
            _this.loading = false
            if (response.data.code === '200') {
              this.getOpearFun()
              this.dialogFormInset.show = false
            } else {
              this.$message({
                showClose: true,
                message: response.data.desc,
                type: 'error'
              })
            }
          })
        }
      })
    },
    getRoleInfoByRole(role) {
      return axios.post(`${this.LBSGateway}/sysadmin-service/role/findOne`, { role: role }, { headers: { token: this.token }}).then(res => {
        if (res.data.code === '200') {
          const roleInfo = JSON.parse(res.data.data)
          this.fromList.roleId = roleInfo.id
          return roleInfo.id
        } else {
          this.$message({
            showClose: true,
            message: res.data.desc,
            type: 'error'
          })
          return null
        }
      })
    },
    // 点击第一个下拉
    changeSelect(val) {
      const item = this.brandOptions.filter(v => v.id === val)[0]
      this.getTwoSel(item)
    },
    // 部门下拉框的内容 //获取第一个下拉数据 department/findMany
    getOneSel() {
      const _this = this
      _this.loading = true
      const requestData = {
        start: 0,
        end: 10000
      }
      axios.post(`${_this.LBSGateway}/sysadmin-service/department/findMany`, requestData, { headers: { token: _this.token }}).then(res => {
        _this.loading = false
        if (res.data.code === '200') {
          this.brandOptions = JSON.parse(res.data.data)
        } else {
          this.$message({
            showClose: true,
            message: res.data.msg,
            type: 'error'
          })
        }
      })
    },
    // 获取第二个下拉数据
    getTwoSel(request) {
      const _this = this
      const requestData = {
        departmentId: request.id,
        start: 0,
        end: 10000
      }
      console.log(requestData)
      axios.post(`${_this.LBSGateway}/sysadmin-service/role/findMany`, requestData, { headers: { token: _this.token }}).then(res => {
        if (res.data.code === '200') {
          const datas = res.data.data
          if (datas) {
            if (this.fromList.roleId) {
              this.fromList.roleId = this.typeOptions.id
            }
            this.typeOptions = JSON.parse(res.data.data)
          } else {
            this.typeOptions = []
          }
        } else {
          this.$message({
            showClose: true,
            message: res.data.desc,
            type: 'error'
          })
        }
      })
    },

    handleSizeChange: function(size) {
      this.pagesize = size
    },
    handleCurrentChange: function(currentPage) {
      this.currentPage = currentPage
    }
  }

}
</script>

<style lang='scss' scoped>
  .workflow {
    min-height: 840px;

    .data-action-button-group {
      margin-bottom: 10px;
      display: flex;
      justify-content: space-between;
    }

    .list-group1 {
      display: inline-block;
      width: 100%;
      height: 66px;
    }

    .list-group2 {
      display: inline-block;
      width: 100%;
      height: 61px;
    }

    .w-group-item {
      width: 146px;
      margin: 0 5px 5px 0;
      text-align: center;
      cursor: move;
    }
  }
  .content{
    width: 100%;
    position: relative;
    .content01{
      position: absolute;
      left: 30px;
      top: 15px;
      width: 60px;
      height: 30px;
      font-size: 14px;
      text-align: center;
      line-height: 30px;
      border-radius: 5px;
      cursor: pointer;
      color: white;
      background:#109eae;
    }
  }
  .content_one{
    width: 100%;
    text-align: center;
  }

  .contenthex{
    width: 100%;
    .box{
      border: 3px solid white;
      background: #ffffff;
      width: 60%;
      margin: 0
      auto;
      padding-top: 30px;
    }
  }
  .content_onehex{
    width: 100%;
    text-align: center;
    font-size: 22px;
  }
  .content_1{
    width: 80%;
    padding: 15px;
    margin: 10px auto;
    display: flex;
    justify-content: center;
    .Yuangong{
      line-height: 38px;
      font-size: 16px;
      margin-right: 20px;
    }
  }
  .content_2{
    width: 80%;
    display: flex;
    justify-content: center;
    margin-left: 15px;
    margin-top: 30px;
    .Yuangong{
      line-height: 38px;
      font-size: 13px;
    }
  }
  .box_two{
    position: relative;
    height: 60px;
    line-height: 60px;
    .box_two01{
      position: absolute;
      top:10px;
      left: -43px;
    }
  }
  .departmentC{
    margin-top: 40px;
    h4{
      font-weight: bold;
      color: #109eae ;
    }
  }
.modalTitle{
  width: 100%;
  height: 43px;
  padding-left: 10px;
  font-weight: 600;

  h4{
    line-height: 43px;
    font-size: 15px;
  }
}
</style>
