<template>
  <div v-loading="loading" class="kyc-facta-container">
    <el-card>
      <!-- 搜索表单 -->
      <el-form ref="searchForm" :model="searchForm" size="small" label-width="240px" label-position="left" :show-message="false">
        <el-row :gutter="20">
          <el-col :span="8">
            <!-- 客户ID -->
            <el-form-item :label="$t('kyc.customer.customerId')" prop="customerId">
              <el-input v-model="searchForm.customerId" :placeholder="$t('kyc.customer.customerIdPlaceholder')" clearable class="search-form-item" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <!-- 客户ID类型 -->
            <el-form-item :label="$t('kyc.customer.customerIdType')" prop="customerIdType">
              <el-select v-model="searchForm.customerIdType" :placeholder="$t('kyc.customer.customerIdTypePlaceholder')" clearable class="search-form-item">
                <el-option v-for="item in customerIdTypeOptions" :key="item.value" :label="$t(item.label)" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <!-- 签发国家 -->
            <el-form-item :label="$t('kyc.customer.issueCountry')" prop="issueCountry">
              <el-select v-model="searchForm.issueCountry" :placeholder="$t('kyc.customer.issueCountryPlaceholder')" clearable class="search-form-item">
                <el-option v-for="item in countryOptions" :key="item.value" :label="$t(item.label)" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <!-- 出生日期 -->
            <el-form-item :label="$t('kyc.customer.dateOfBirth')" prop="dateOfBirth">
              <el-date-picker
                v-model="searchForm.dateOfBirth"
                type="date"
                :placeholder="$t('kyc.customer.dateOfBirthPlaceholder')"
                value-format="timestamp"
                clearable
                class="search-form-item"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <!-- 性别 -->
            <el-form-item :label="$t('kyc.customer.gender')" prop="gender">
              <el-select v-model="searchForm.gender" :placeholder="$t('kyc.customer.genderPlaceholder')" clearable class="search-form-item">
                <el-option v-for="item in genderOptions" :key="item.value" :label="$t(item.label)" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <!-- 国籍 -->
            <el-form-item :label="$t('kyc.customer.nationality')" prop="nationality">
              <el-select v-model="searchForm.nationality" :placeholder="$t('kyc.customer.nationalityPlaceholder')" clearable class="search-form-item">
                <el-option v-for="item in countryOptions" :key="item.value" :label="$t(item.label)" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>
          <!--          <el-col :span="8">
            &lt;!&ndash; 有效期 &ndash;&gt;
            <el-form-item :label="$t('kyc.facta.validityPeriod')" prop="validityPeriod">
              <el-date-picker
                v-model="searchForm.validityPeriod"
                type="datetime"
                :placeholder="$t('kyc.facta.validityPeriodPlaceholder')"
                clearable
                value-format="timestamp"
                class="search-form-item"
              />
            </el-form-item>
          </el-col>-->
          <el-col :span="8">
            <!-- 纳税人识别号 -->
            <el-form-item :label="$t('kyc.facta.taxpayerIdentificationNumber')" prop="taxpayerIdentificationNumber" class="search-form-item">
              <el-input v-model="searchForm.taxpayerIdentificationNumber" :placeholder="$t('kyc.facta.taxpayerIdentificationNumberPlaceholder')" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <!-- 客户编号 -->
            <el-form-item :label="$t('kyc.customer.customerNumber')" prop="customerNumber">
              <el-input v-model="searchForm.customerNumber" :placeholder="$t('kyc.customer.customerNumberPlaceholder')" clearable class="search-form-item" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <!-- 账户号码 -->
            <el-form-item :label="$t('kyc.customer.accountNumber')" prop="accountNumber">
              <el-input v-model="searchForm.accountNumber" :placeholder="$t('kyc.customer.accountNumberPlaceholder')" clearable class="search-form-item" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <!-- FACTA状态 -->
            <el-form-item :label="$t('kyc.facta.factaStatus')" prop="factaStatus">
              <el-select v-model="searchForm.factaStatus" :placeholder="$t('kyc.facta.factaStatusPlaceholder')" clearable class="search-form-item">
                <el-option v-for="item in statusOptions" :key="item.value" :label="$t(item.label)" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <!-- 创建时间范围 -->
            <el-form-item :label="$t('kyc.createDateRange')" prop="dateRange">
              <el-date-picker
                v-model="searchForm.dateRange"
                type="daterange"
                :range-separator="$t('kyc.to')"
                value-format="timestamp"
                :start-placeholder="$t('kyc.startDate')"
                :end-placeholder="$t('kyc.endDate')"
                :default-time="['00:00:00', '23:59:59']"
                unlink-panels
                class="search-form-item"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-button type="primary" size="small" icon="el-icon-search" style="margin-bottom: 18px" @click="getFactaList(true)">{{ $t('kyc.search') }}</el-button>
            <el-button icon="el-icon-refresh" size="small" @click="resetForm('searchForm')">{{ $t('kyc.reset') }}</el-button>
          </el-col>
        </el-row>
      </el-form>

      <!-- 创建和批量删除按钮 -->
      <div class="data-action-button-group">
        <div>
          <!--          <el-button size="small" icon="el-icon-plus" type="primary" plain @click="openCreateFactaDialog()">{{ $t('kyc.create') }}</el-button>-->
        </div>

        <!-- 刷新按钮 -->
        <el-button icon="el-icon-refresh" size="small" circle @click="getFactaList()" />
      </div>

      <!-- FACTA表格 -->
      <el-card shadow="never">
        <el-table v-loading="tableLoading" :data="items" stripe>
          <el-table-column type="index" label="#" align="center" />
          <el-table-column :label="$t('kyc.customer.nationality')" prop="customerMasterInfoEntity.nationality" align="center" width="100" />
          <el-table-column :label="$t('kyc.customer.customerId')" prop="customerMasterInfoEntity.customerId" align="center" width="120" />
          <el-table-column :label="$t('kyc.customer.customerIdType')" prop="customerMasterInfoEntity.customerIdType" align="center" width="140">
            <template slot-scope="scope">
              {{ $t(getLabel("CUSTOMER_ID_TYPE", scope.row.customerMasterInfoEntity.customerIdType)) }}
            </template>
          </el-table-column>
          <el-table-column :label="$t('kyc.customer.issueCountry')" prop="customerMasterInfoEntity.issueCountry" align="center" width="120" />
          <el-table-column :label="$t('kyc.facta.name')" align="center" width="170">
            <template slot-scope="scope">
              {{ `${scope.row.customerMasterInfoEntity.firstName} ${scope.row.customerMasterInfoEntity.lastName}` }}
            </template>
          </el-table-column>
          <el-table-column :label="$t('kyc.customer.gender')" prop="customerMasterInfoEntity.gender" align="center" width="80">
            <template slot-scope="scope">
              {{ $t(getLabel("GENDER", scope.row.customerMasterInfoEntity.gender)) }}
            </template>
          </el-table-column>
          <el-table-column :label="$t('kyc.customer.dateOfBirth')" align="center" width="150">
            <template slot-scope="scope">
              {{ moment(scope.row.customerMasterInfoEntity.dateOfBirth).format('YYYY-MM-DD') }}
            </template>
          </el-table-column>
          <el-table-column :label="$t('kyc.facta.taxpayerIdentificationNumber')" prop="taxpayerIdentificationNumber" align="center" width="230" />
          <el-table-column :label="$t('kyc.customer.customerNumber')" prop="customerMasterInfoEntity.customerNumber" align="center" width="160" />
          <el-table-column :label="$t('kyc.customer.accountNumber')" prop="customerMasterInfoEntity.accountNumber" align="center" width="180" />
          <el-table-column :label="$t('kyc.facta.report')" prop="report" align="center" width="80">
            <template slot-scope="scope">
              {{ $t(getLabel("BOOLEAN", scope.row.report)) }}
            </template>
          </el-table-column>
          <el-table-column :label="$t('kyc.facta.factaStatus')" prop="factaStatus" align="center" width="130">
            <template slot-scope="scope">
              <el-tooltip :content="$t(getLabel('STATUS', scope.row.factaStatus))" placement="right">
                <el-switch
                  v-model="scope.row.factaStatus"
                  active-color="#13ce66"
                  active-value="Active"
                  inactive-value="Cancel"
                  @change="handlerSwitchStatus({ ...scope.row, factaStatus: $event })"
                />
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column :label="$t('kyc.facta.remark')" prop="remark" align="center" width="150" />
          <el-table-column :label="$t('kyc.facta.validityPeriod')" prop="validityPeriod" align="center" width="150">
            <template slot-scope="scope">
              {{ moment(scope.row.validityPeriod).format('YYYY-MM-DD HH:mm:ss') }}
            </template>
          </el-table-column>
          <el-table-column :label="$t('kyc.creationDate')" align="center" width="150">
            <template slot-scope="scope">
              {{ moment(scope.row.creationDate).format('YYYY-MM-DD HH:mm:ss') }}
            </template>
          </el-table-column>
          <el-table-column :label="$t('kyc.updateDate')" align="center" width="150">
            <template slot-scope="scope">
              {{ moment(scope.row.updateDate).format('YYYY-MM-DD HH:mm:ss') }}
            </template>
          </el-table-column>
          <el-table-column :label="$t('kyc.operate')" align="center" width="200" fixed="right">
            <template slot-scope="scope">
              <el-button
                type="text"
                size="mini"
                text
                icon="el-icon-view"
                @click="viewFactaDialog(scope.row)"
              >
                {{ $t('kyc.view') }}
              </el-button>
              <el-button
                type="text"
                size="mini"
                text
                icon="el-icon-edit"
                @click="openUpdateFactaDialog(scope.row)"
              >
                {{ $t('kyc.edit') }}
              </el-button>
              <el-button
                type="text"
                size="mini"
                text
                icon="el-icon-delete"
                @click="batchDeleteFacta(scope.row.id)"
              >
                {{ $t('kyc.delete') }}
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
      <div class="block">
        <el-pagination
          :current-page="currentPage"
          :page-sizes="[5, 10, 20, 40]"
          :page-size="pageSize"
          :total="totalItems"
          layout="total, prev, pager, next"
          style="margin-top: 20px; text-align: right;"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 查看的对话框 -->
    <el-dialog
      :title="$t('kyc.facta.viewDetails')"
      :visible.sync="viewDialogVisible"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      width="60%"
      top="50px"
    >
      <descriptions :view-row="viewRow" />
    </el-dialog>

    <el-dialog
      :title="isUpdate ? $t('kyc.facta.editDetails') : $t('kyc.facta.createDetails')"
      :visible.sync="dataFormDialogVisible"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      width="60%"
      top="10vh"
      destroy-on-close
    >
      <customize-form
        ref="customizeForm"
        form-ref="dataForm"
        size="small"
        :model="formData"
        :form-item-config="formConfig"
        label-width="230px"
        label-position="left"
        :is-update="isUpdate"
      />

      <span slot="footer" class="dialog-footer">
        <el-button size="small" @click="dataFormDialogVisible = false"> {{ $t('kyc.cancel') }}</el-button>
        <el-button v-if="!isUpdate" type="primary" size="small" :loading="confirmButtonLoading" @click="handlerCreateData()">{{ $t('kyc.confirm') }}</el-button>
        <el-button v-if="isUpdate" type="primary" size="small" :loading="confirmButtonLoading" @click="handlerUpdateData()">{{ $t('kyc.update') }}</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import moment from 'moment'
import axios from 'axios'
import {
  countryOptions,
  customerIdTypeOptions,
  genderOptions,
  getLabel,
  handlerCommonData,
  statusOptions
} from '@/views/lbsLayout/system/kyc/options'
import { formConfig } from '@/views/lbsLayout/system/kyc/facta/form-config'
import Descriptions from '@/views/lbsLayout/system/kyc/components/Descriptions.vue'
import CustomizeForm from '@/components/CustomizeForm/CustomizeForm.vue'

export default {
  name: 'KycFacta',
  components: { CustomizeForm, Descriptions },
  data() {
    const hiddenFields = ['otherLanguageName', 'department', 'role', 'position', 'industry']
    const showConfig = formConfig.filter(item => !hiddenFields.includes(item.prop))
    return {
      loading: false,
      tableLoading: false,
      viewDialogVisible: false,
      dataFormDialogVisible: false,
      confirmButtonLoading: false,
      isUpdate: false,
      token: null,
      moment,
      customerIdTypeOptions,
      countryOptions,
      genderOptions,
      statusOptions,
      formConfig: showConfig,
      searchForm: {
        customerId: '',
        customerIdType: '',
        issueCountry: '',
        dateOfBirth: '',
        gender: '',
        nationality: '',
        // validityPeriod: null,
        taxpayerIdentificationNumber: '',
        customerNumber: '',
        factaStatus: '',
        dateRange: [],
        startDate: '',
        endDate: ''
      },
      factaList: [],
      viewRow: [],
      formData: {},
      currentPage: 1,
      pageSize: 20,
      totalItems: 0,
      items: []
    }
  },
  mounted() {
    this.token = window.sessionStorage.getItem('token')

    this.getFactaList()
  },
  methods: {
    handlerCreateData() {
      this.$refs.customizeForm.validate((valid, data) => {
        if (valid) {
          const formData = handlerCommonData(data)
          this.confirmButtonLoading = true
          axios.post(this.LBSGateway + '/sysadmin-service/facta/creation', formData, { headers: { token: this.token }})
            .then((res) => {
              const { code, msg } = res.data
              if (code === '200') {
                this.$message.success(msg)
                this.getFactaList()
                this.dataFormDialogVisible = false
              } else {
                this.$message.error(msg)
              }
            })
            .finally(() => {
              this.confirmButtonLoading = false
            })
        }
      })
    },
    handlerUpdateData() {
      this.$refs.customizeForm.validate((valid, data) => {
        if (valid) {
          const formData = handlerCommonData(data)
          this.$confirm(this.$t('kyc.updateTipMessage'), this.$t('kyc.tip'), {
            confirmButtonText: this.$t('kyc.confirm'),
            cancelButtonText: this.$t('kyc.cancel'),
            type: 'warning'
          }).then(() => {
            this.confirmButtonLoading = true
            axios.post(this.LBSGateway + '/sysadmin-service/facta/edit', formData, { headers: { token: this.token }})
              .then((res) => {
                const { code, msg } = res.data
                if (code === '200') {
                  this.$message.success(msg)
                  this.getFactaList()
                  this.dataFormDialogVisible = false
                } else {
                  this.$message.error(msg)
                }
              })
              .finally(() => {
                this.confirmButtonLoading = false
              })
          }).catch(() => {})
        }
      })
    },
    handlerSwitchStatus(data) {
      delete data.customerMasterInfoEntity.id
      data = { ...data, ...data.customerMasterInfoEntity }
      delete data.customerMasterInfoEntity
      const formData = handlerCommonData(data)
      this.tableLoading = true
      axios.post(this.LBSGateway + '/sysadmin-service/facta/edit', formData, { headers: { token: this.token }})
        .then((res) => {
          const { code, msg } = res.data
          if (code === '200') {
            this.$message.success(msg)
          } else {
            this.$message.error(msg)
            this.getFactaList()
          }
        })
        .finally(() => {
          this.tableLoading = false
        })
    },
    handleSizeChange(size) {
      this.pagesize = size
      this.updateItems() // 更新当前页的数据
    },
    handleCurrentChange(currentPage) {
      this.currentPage = currentPage
      this.updateItems() // 更新当前页的数据
    },
    getLabel,
    viewFactaDialog(row) {
      const description = { ...row, ...row.customerMasterInfoEntity }
      this.viewRow = this.formConfig.map(item => {
        let value = description[item.prop]
        if (item.format) {
          return { key: this.$t(item.label), value: moment(Number(value)).format(item.format.replace('dd', 'DD').replace('yyyy', 'YYYY')) }
        } else {
          if (item.options) {
            const option = item.options.filter(option => option.value === value)
            if (option && option.length) {
              value = this.$t(option[0].label)
            }
          }
          return { key: this.$t(item.label), value: value }
        }
      })
      this.viewDialogVisible = true
    },
    // 打开编辑FACTA对话框
    openUpdateFactaDialog(row) {
      delete row.customerMasterInfoEntity.id
      this.formData = { ...row, ...row.customerMasterInfoEntity }
      delete this.formData.customerMasterInfoEntity
      this.isUpdate = true
      this.dataFormDialogVisible = true
    },
    // 批量删除FACTA
    batchDeleteFacta(id) {
      this.$confirm(this.$t('kyc.deleteTipMessage'), this.$t('kyc.tip'), {
        confirmButtonText: this.$t('kyc.confirm'),
        cancelButtonText: this.$t('kyc.cancel'),
        type: 'warning'
      }).then(() => {
        this.tableLoading = true
        axios.post(this.LBSGateway + '/sysadmin-service/facta/delete', { id }, { headers: { token: this.token }})
          .then((res) => {
            const { code, msg } = res.data
            if (code === '200') {
              this.$message.success(msg)
              this.dataFormDialogVisible = false
              if (this.items.length - 1 === 0 && this.currentPage > 1) {
                this.currentPage-- // 移动到上一页
              }
              this.getFactaList()
            } else {
              this.$message.error(msg)
            }
          })
          .finally(() => {
            this.tableLoading = false
          })
      })
    },
    // 获取海外账户税收合规检测列表
    getFactaList(search = false) {
      if (search) {
        this.currentPage = 1
      }
      this.tableLoading = true

      const requestData = { ...this.searchForm }

      if (this.searchForm.dateRange && this.searchForm.dateRange.length) {
        requestData.startDate = this.searchForm.dateRange[0]
        requestData.endDate = this.searchForm.dateRange[1]
      }

      delete requestData.dateRange

      axios.post(this.LBSGateway + '/sysadmin-service/facta/list', requestData, { headers: { token: this.token }})
        .then((res) => {
          const { code, msg, data: factaList } = res.data
          if (code === '200') {
            this.factaList = factaList
            this.totalItems = factaList.length
            this.updateItems()
          } else {
            this.factaList = []
            this.$message.error(msg)
          }
        })
        .finally(() => {
          this.tableLoading = false
        })
    },
    updateItems() {
      const start = (this.currentPage - 1) * this.pageSize
      this.items = this.factaList.slice(start, start + this.pageSize)
    },
    // 打开创建海外账户税收合规检测对话框
    openCreateFactaDialog() {
      this.isUpdate = false
      this.formData = {}
      if (this.$refs.customizeForm) { this.$refs.customizeForm.resetForm() }
      this.dataFormDialogVisible = true
    },
    // 通过ref属性重置表单
    resetForm(formName) {
      this.$refs[formName].resetFields()
    }
  }
}
</script>

<style scoped lang="scss">
.kyc-facta-container {
  .search-form-item {
    width: 100%;
  }
  .data-action-button-group {
    margin-bottom: 10px;
    display: flex;
    justify-content: space-between;
  }
  .bottom-actions-button-group {
    text-align: right;
  }
}
</style>
