import { formConfig as customerFormConfig } from '@/views/lbsLayout/system/kyc/customer/form-config'
import CustomizeSelect from '@/components/CustomizeForm/CustomizeSelect.vue'
import { booleanOptions, statusOptions } from '@/views/lbsLayout/system/kyc/options'

export const formConfig = [
  ...customerFormConfig,
  {
    'label': 'kyc.facta.taxpayerIdentificationNumber',
    'placeholder': 'kyc.facta.taxpayerIdentificationNumberPlaceholder',
    'prop': 'taxpayerIdentificationNumber',
    'component': 'el-input-number',
    'span': 12,
    'min': 0,
    'step': 1,
    'step-strictly': true,
    'controls': false,
    'precision': 0,
    'width': '100%',
    'clearable': true,
    'rules': [
      { required: true, message: 'kyc.facta.taxpayerIdentificationNumberPlaceholder' }
    ]
  },
  {
    'label': 'kyc.facta.validityPeriod',
    'placeholder': 'kyc.facta.validityPeriodPlaceholder',
    'prop': 'validityPeriod',
    'component': 'el-date-picker',
    'span': 12,
    'width': '100%',
    'clearable': true,
    'type': 'datetime',
    'format': 'yyyy-MM-dd HH:mm:ss',
    'valueFormat': 'timestamp',
    'rules': [
      { required: true, message: 'kyc.facta.validityPeriodPlaceholder' }
    ]
  },
  {
    'label': 'kyc.facta.report',
    'placeholder': 'kyc.facta.reportPlaceholder',
    'prop': 'report',
    'component': CustomizeSelect,
    'span': 12,
    'width': '100%',
    'clearable': true,
    'options': booleanOptions,
    'rules': [
      { required: true, message: 'kyc.facta.reportPlaceholder' }
    ]
  },
  {
    'label': 'kyc.facta.factaStatus',
    'placeholder': 'kyc.facta.factaStatusPlaceholder',
    'prop': 'factaStatus',
    'component': CustomizeSelect,
    'span': 12,
    'width': '100%',
    'clearable': true,
    'options': statusOptions,
    'hidden': true,
    'rules': [
      { required: true, message: 'kyc.facta.factaStatusPlaceholder' }
    ]
  },
  {
    'label': 'kyc.facta.remark',
    'placeholder': 'kyc.facta.remarkPlaceholder',
    'prop': 'remark',
    'component': 'el-input',
    'span': 24,
    'type': 'textarea',
    'width': '100%',
    'clearable': true,
    'maxlength': 255
  },
  {
    'label': 'kyc.creationDate',
    'placeholder': 'kyc.creationDatePlaceholder',
    'prop': 'creationDate',
    'component': 'el-date-picker',
    'span': 12,
    'width': '100%',
    'clearable': true,
    'disabled': true,
    'type': 'datetime',
    'format': 'yyyy-MM-dd HH:mm:ss',
    'valueFormat': 'timestamp',
    'hidden': true,
    'rules': [
      { required: true, message: 'kyc.creationDatePlaceholder' }
    ]
  },
  {
    'label': 'kyc.updateDate',
    'placeholder': 'kyc.updateDatePlaceholder',
    'prop': 'updateDate',
    'component': 'el-date-picker',
    'span': 12,
    'width': '100%',
    'clearable': true,
    'disabled': true,
    'type': 'datetime',
    'format': 'yyyy-MM-dd HH:mm:ss',
    'valueFormat': 'timestamp',
    'hidden': true,
    'rules': [
      { required: true, message: 'kyc.updateDatePlaceholder' }
    ]
  }
]
