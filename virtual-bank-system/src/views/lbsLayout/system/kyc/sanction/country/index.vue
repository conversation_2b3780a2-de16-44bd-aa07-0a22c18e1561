<template>
  <div v-loading="loading" class="kyc-sanction-country-container">
    <el-card>
      <!-- 搜索表单 -->
      <el-form ref="searchForm" :model="searchForm" size="small" label-width="140px" label-position="left" :show-message="false">
        <el-row :gutter="20">
          <el-col :span="6">
            <!-- 国家代码 -->
            <el-form-item :label="$t('kyc.sanctionCountry.countryCode')" prop="countryCode">
              <el-select v-model="searchForm.countryCode" :placeholder="$t('kyc.sanctionCountry.countryCodePlaceholder')" clearable class="search-form-item">
                <el-option v-for="item in countryOptions" :key="item.value" :label="$t(item.label)" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <!-- 制裁状态 -->
            <el-form-item :label="$t('kyc.sanctionCountry.sanctionStatus')" prop="sanctionStatus">
              <el-select v-model="searchForm.sanctionStatus" :placeholder="$t('kyc.sanctionCountry.sanctionStatusPlaceholder')" clearable class="search-form-item">
                <el-option v-for="item in statusOptions" :key="item.value" :label="$t(item.label)" :value="item.value" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <!-- 创建时间范围 -->
            <el-form-item :label="$t('kyc.createDateRange')" prop="dateRange">
              <el-date-picker
                v-model="searchForm.dateRange"
                type="daterange"
                :range-separator="$t('kyc.to')"
                value-format="timestamp"
                :start-placeholder="$t('kyc.startDate')"
                :end-placeholder="$t('kyc.endDate')"
                :default-time="['00:00:00', '23:59:59']"
                unlink-panels
                class="search-form-item"
              />
            </el-form-item>
          </el-col>
          <el-col :span="6">
            <el-button type="primary" size="small" icon="el-icon-search" style="margin-bottom: 18px" @click="getSanctionCountryList(true)">{{ $t('kyc.search') }}</el-button>
            <el-button icon="el-icon-refresh" size="small" @click="resetForm('searchForm')">{{ $t('kyc.reset') }}</el-button>
          </el-col>
        </el-row>
      </el-form>

      <!-- 创建和批量删除按钮 -->
      <div class="data-action-button-group">
        <div>
          <el-button size="small" icon="el-icon-plus" type="primary" plain @click="openCreateSanctionCountryDialog()">{{ $t('kyc.create') }}</el-button>
        </div>

        <!-- 刷新按钮 -->
        <el-button icon="el-icon-refresh" size="small" circle @click="getSanctionCountryList()" />
      </div>

      <!-- 国家制裁 -->
      <el-card shadow="never">
        <el-table v-loading="tableLoading" :data="items" stripe>
          <el-table-column type="index" label="#" align="center" />
          <el-table-column :label="$t('kyc.sanctionCountry.countryCode')" prop="countryCode" align="center" width="130" />
          <el-table-column :label="$t('kyc.sanctionCountry.countryName')" prop="countryName" align="center" width="130" />
          <el-table-column :label="$t('kyc.sanctionCountry.sanctionImposedBy')" prop="sanctionImposedBy" align="center" width="180" />
          <el-table-column :label="$t('kyc.sanctionCountry.sanctionType')" prop="sanctionType" align="center" width="130">
            <template slot-scope="scope">
              {{ $t(getLabel("SANCTION_TYPE", scope.row.sanctionType)) }}
            </template>
          </el-table-column>
          <el-table-column :label="$t('kyc.sanctionCountry.sanctionStatus')" prop="sanctionStatus" align="center" width="140">
            <template slot-scope="scope">
              <el-tooltip :content="$t(getLabel('STATUS', scope.row.sanctionStatus))" placement="right">
                <el-switch
                  v-model="scope.row.sanctionStatus"
                  active-color="#13ce66"
                  active-value="Active"
                  inactive-value="Cancel"
                  @change="handlerSwitchStatus({ ...scope.row, sanctionStatus: $event })"
                />
              </el-tooltip>
            </template>
          </el-table-column>
          <el-table-column :label="$t('kyc.sanctionCountry.remark')" prop="remark" align="center" width="150" />
          <el-table-column :label="$t('kyc.sanctionCountry.expiryDate')" align="center" width="160">
            <template slot-scope="scope">
              {{ moment(scope.row.expiryDate).format('YYYY-MM-DD HH:mm:ss') }}
            </template>
          </el-table-column>
          <el-table-column :label="$t('kyc.creationDate')" align="center" width="160">
            <template slot-scope="scope">
              {{ moment(scope.row.creationDate).format('YYYY-MM-DD HH:mm:ss') }}
            </template>
          </el-table-column>
          <el-table-column :label="$t('kyc.updateDate')" align="center" width="160">
            <template slot-scope="scope">
              {{ moment(scope.row.updateDate).format('YYYY-MM-DD HH:mm:ss') }}
            </template>
          </el-table-column>
          <el-table-column :label="$t('kyc.operate')" align="center" fixed="right" width="200">
            <template slot-scope="scope">
              <el-button
                type="text"
                size="mini"
                text
                icon="el-icon-view"
                @click="viewSanctionCountryDialog(scope.row)"
              >
                {{ $t('kyc.view') }}
              </el-button>
              <el-button
                type="text"
                size="mini"
                text
                icon="el-icon-edit"
                @click="openUpdateSanctionCountryDialog(scope.row)"
              >
                {{ $t('kyc.edit') }}
              </el-button>
              <el-button
                type="text"
                size="mini"
                text
                icon="el-icon-delete"
                @click="deleteSanctionCountry(scope.row.id)"
              >
                {{ $t('kyc.delete') }}
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
      <div class="block">
        <el-pagination
          :current-page="currentPage"
          :page-sizes="[5, 10, 20, 40]"
          :page-size="pageSize"
          :total="totalItems"
          layout="total, prev, pager, next"
          style="margin-top: 20px; text-align: right;"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 查看的对话框 -->
    <el-dialog
      :title="$t('kyc.sanctionCountry.viewDetails')"
      :visible.sync="viewDialogVisible"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      width="60%"
      top="50px"
    >
      <descriptions :view-row="viewRow" />
    </el-dialog>

    <el-dialog
      :title="isUpdate ? $t('kyc.sanctionCountry.editDetails') : $t('kyc.sanctionCountry.createDetails')"
      :visible.sync="dataFormDialogVisible"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      width="60%"
      top="10vh"
      destroy-on-close
    >
      <customize-form
        ref="customizeForm"
        form-ref="dataForm"
        size="small"
        :model="formData"
        :form-item-config="formConfig"
        label-width="170px"
        label-position="left"
        :is-update="isUpdate"
      />

      <span slot="footer" class="dialog-footer">
        <el-button size="small" @click="dataFormDialogVisible = false"> {{ $t('kyc.cancel') }}</el-button>
        <el-button v-if="!isUpdate" type="primary" size="small" :loading="confirmButtonLoading" @click="handlerCreateData()">{{ $t('kyc.confirm') }}</el-button>
        <el-button v-if="isUpdate" type="primary" size="small" :loading="confirmButtonLoading" @click="handlerUpdateData()">{{ $t('kyc.update') }}</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import moment from 'moment'
import axios from 'axios'
import { countryOptions, getLabel, handlerCommonData, statusOptions } from '@/views/lbsLayout/system/kyc/options'
import Descriptions from '@/views/lbsLayout/system/kyc/components/Descriptions.vue'
import { formConfig } from '@/views/lbsLayout/system/kyc/sanction/country/form-config'
import CustomizeForm from '@/components/CustomizeForm/CustomizeForm.vue'

export default {
  name: 'KycSanctionCountry',
  components: { CustomizeForm, Descriptions },
  data() {
    return {
      isUpdate: false,
      loading: false,
      tableLoading: false,
      viewDialogVisible: false,
      dataFormDialogVisible: false,
      confirmButtonLoading: false,
      token: null,
      countryOptions,
      statusOptions,
      moment,
      formConfig,
      searchForm: {
        countryCode: '',
        sanctionStatus: '',
        dateRange: [],
        startDate: '',
        endDate: ''
      },
      sanctionCountryList: [],
      viewRow: [],
      formData: {},
      currentPage: 1,
      pageSize: 20,
      totalItems: 0,
      items: []
    }
  },
  mounted() {
    this.token = window.sessionStorage.getItem('token')

    this.getSanctionCountryList()
  },
  methods: {
    handlerCreateData() {
      this.$refs.customizeForm.validate((valid, data) => {
        if (valid) {
          const formData = handlerCommonData(data)
          this.confirmButtonLoading = true
          axios.post(this.LBSGateway + '/sysadmin-service/sanction/country/creation', formData, { headers: { token: this.token }})
            .then((res) => {
              const { code, msg } = res.data
              if (code === '200') {
                this.$message.success(msg)
                this.getSanctionCountryList()
                this.dataFormDialogVisible = false
              } else {
                this.$message.error(msg)
              }
            })
            .finally(() => {
              this.confirmButtonLoading = false
            })
        }
      })
    },
    handlerUpdateData() {
      this.$refs.customizeForm.validate((valid, data) => {
        if (valid) {
          const formData = handlerCommonData(data)
          this.$confirm(this.$t('kyc.updateTipMessage'), this.$t('kyc.tip'), {
            confirmButtonText: this.$t('kyc.confirm'),
            cancelButtonText: this.$t('kyc.cancel'),
            type: 'warning'
          }).then(() => {
            this.confirmButtonLoading = true
            axios.post(this.LBSGateway + '/sysadmin-service/sanction/country/edit', formData, { headers: { token: this.token }})
              .then((res) => {
                const { code, msg } = res.data
                if (code === '200') {
                  this.$message.success(msg)
                  this.getSanctionCountryList()
                  this.dataFormDialogVisible = false
                } else {
                  this.$message.error(msg)
                }
              })
              .finally(() => {
                this.confirmButtonLoading = false
              })
          }).catch(() => {})
        }
      })
    },
    handlerSwitchStatus(data) {
      this.tableLoading = true
      axios.post(this.LBSGateway + '/sysadmin-service/sanction/country/edit', data, { headers: { token: this.token }})
        .then((res) => {
          const { code, msg } = res.data
          if (code === '200') {
            this.$message.success(msg)
          } else {
            this.$message.error(msg)
            this.getSanctionCountryList()
          }
        })
        .finally(() => {
          this.tableLoading = false
        })
    },
    handleSizeChange(size) {
      this.pagesize = size
      this.updateItems() // 更新当前页的数据
    },
    handleCurrentChange(currentPage) {
      this.currentPage = currentPage
      this.updateItems() // 更新当前页的数据
    },
    getLabel,
    viewSanctionCountryDialog(row) {
      const description = { ...row, ...row.customerMasterInfoEntity }
      this.viewRow = this.formConfig.map(item => {
        let value = description[item.prop]
        if (item.format) {
          return { key: this.$t(item.label), value: moment(Number(value)).format(item.format.replace('dd', 'DD').replace('yyyy', 'YYYY')) }
        } else {
          if (item.options) {
            const option = item.options.filter(option => option.value === value)
            if (option && option.length) {
              value = this.$t(option[0].label)
            }
          }
          return { key: this.$t(item.label), value: value }
        }
      })
      this.viewDialogVisible = true
    },
    // 打开编辑国家制裁对话框
    openUpdateSanctionCountryDialog(row) {
      this.formData = JSON.parse(JSON.stringify(row))
      this.isUpdate = true
      this.dataFormDialogVisible = true
    },
    // 删除国家制裁
    deleteSanctionCountry(id) {
      this.$confirm(this.$t('kyc.deleteTipMessage'), this.$t('kyc.tip'), {
        confirmButtonText: this.$t('kyc.confirm'),
        cancelButtonText: this.$t('kyc.cancel'),
        type: 'warning'
      }).then(() => {
        this.tableLoading = true
        axios.post(this.LBSGateway + '/sysadmin-service/sanction/country/delete', { id }, { headers: { token: this.token }})
          .then((res) => {
            const { code, msg } = res.data
            if (code === '200') {
              this.$message.success(msg)
              this.dataFormDialogVisible = false
              if (this.items.length - 1 === 0 && this.currentPage > 1) {
                this.currentPage-- // 移动到上一页
              }
              this.getSanctionCountryList()
            } else {
              this.$message.error(msg)
            }
          })
          .finally(() => {
            this.tableLoading = false
          })
      })
    },
    // 获取国家制裁列表
    getSanctionCountryList(search = false) {
      if (search) {
        this.currentPage = 1
      }
      this.tableLoading = true

      const requestData = { ...this.searchForm }

      if (this.searchForm.dateRange && this.searchForm.dateRange.length) {
        requestData.startDate = this.searchForm.dateRange[0]
        requestData.endDate = this.searchForm.dateRange[1]
      }

      delete requestData.dateRange

      axios.post(this.LBSGateway + '/sysadmin-service/sanction/country/list', requestData, { headers: { token: this.token }})
        .then((res) => {
          const { code, msg, data: sanctionCountryList } = res.data
          if (code === '200') {
            this.sanctionCountryList = sanctionCountryList
            this.totalItems = sanctionCountryList.length
            this.updateItems()
          } else {
            this.sanctionCountryList = []
            this.$message.error(msg)
          }
        })
        .finally(() => {
          this.tableLoading = false
        })
    },
    updateItems() {
      const start = (this.currentPage - 1) * this.pageSize
      this.items = this.sanctionCountryList.slice(start, start + this.pageSize)
    },
    // 打开创建国家制裁对话框
    openCreateSanctionCountryDialog() {
      this.isUpdate = false
      this.formData = {}
      if (this.$refs.customizeForm) { this.$refs.customizeForm.resetForm() }
      this.dataFormDialogVisible = true
    },
    // 通过ref属性重置表单
    resetForm(formName) {
      this.$refs[formName].resetFields()
    }
  }
}
</script>

<style scoped lang="scss">
.kyc-sanction-country-container {
  .search-form-item {
    width: 100%;
  }
  .data-action-button-group {
    margin-bottom: 10px;
    display: flex;
    justify-content: space-between;
  }
  .bottom-actions-button-group {
    text-align: right;
  }
}
</style>
