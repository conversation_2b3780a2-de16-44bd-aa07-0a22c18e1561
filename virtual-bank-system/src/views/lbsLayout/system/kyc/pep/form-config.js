import { formConfig as customerFormConfig } from '@/views/lbsLayout/system/kyc/customer/form-config'
import CustomizeSelect from '@/components/CustomizeForm/CustomizeSelect.vue'
import { statusOptions } from '@/views/lbsLayout/system/kyc/options'

export const formConfig = [
  ...customerFormConfig,
  {
    'label': 'kyc.pep.pepStatus',
    'placeholder': 'kyc.pep.pepStatusPlaceholder',
    'prop': 'pepStatus',
    'component': CustomizeSelect,
    'span': 12,
    'width': '100%',
    'clearable': true,
    'options': statusOptions,
    'hidden': true,
    'rules': [
      { required: true, message: 'kyc.pep.pepStatusPlaceholder' }
    ]
  },
  {
    'label': 'kyc.pep.remark',
    'placeholder': 'kyc.pep.remarkPlaceholder',
    'prop': 'remark',
    'component': 'el-input',
    'span': 24,
    'width': '100%',
    'clearable': true,
    'type': 'textarea',
    'maxlength': 255
  },
  {
    'label': 'kyc.creationDate',
    'placeholder': 'kyc.creationDatePlaceholder',
    'prop': 'creationDate',
    'component': 'el-date-picker',
    'span': 12,
    'width': '100%',
    'clearable': true,
    'disabled': true,
    'type': 'datetime',
    'format': 'yyyy-MM-dd HH:mm:ss',
    'valueFormat': 'timestamp',
    'hidden': true,
    'rules': [
      { required: true, message: 'kyc.creationDatePlaceholder' }
    ]
  },
  {
    'label': 'kyc.updateDate',
    'placeholder': 'kyc.updateDatePlaceholder',
    'prop': 'updateDate',
    'component': 'el-date-picker',
    'span': 12,
    'width': '100%',
    'clearable': true,
    'disabled': true,
    'type': 'datetime',
    'format': 'yyyy-MM-dd HH:mm:ss',
    'valueFormat': 'timestamp',
    'hidden': true,
    'rules': [
      { required: true, message: 'kyc.updateDatePlaceholder' }
    ]
  }
]
