<template>
  <div>
    <el-card v-if="!dialogCaptureDemographic" v-loading.fullscreen.lock="loading" shadow="never" class="workflow">
      <div slot="header" class="clearfix">
        <span>{{ $t("Workflow.Employee") }}</span>
      </div>
      <div class="content">
        <el-form inline size="small">
          <el-form-item :label="$t('Workflow.Emplonumber')">
            <el-input
              v-model="input1"
              :placeholder="$t('Operate.PleaseStaffNumber')"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="Looker()">{{ $t("Workflow.query") }}</el-button>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="goAddPage">{{ $t("Workflow.Add") }}</el-button>
          </el-form-item>
        </el-form>
        <div class="box">
          <div v-if="LookerShow" class="content_2">
            <el-form ref="form" :model="form" label-width="200px">
              <el-form-item :label="$t('Workflow.departments')">
                <el-select v-model="adminList.department" :placeholder="$t('Workflow.pleaseSelect')" disabled @change="changeSelect">
                  <el-option
                    v-for="(item,index) in brandOptions"
                    :key="index"
                    :label="item.department"
                    :value="item.id"
                  />
                </el-select>
              </el-form-item>
              <!--角色-->
              <el-form-item :label="$t('Workflow.role')">
                <el-select v-model="adminList.role" :placeholder="$t('Workflow.pleaseSelect')" disabled>
                  <el-option
                    v-for="(item,index) in typeOptions"
                    :key="'role'+index"
                    :label="item"
                    :value="item"
                  />
                </el-select>
              </el-form-item>
              <!--用户名-->
              <el-form-item :label="$t('lbs.common.username')" style="margin-top: 20px">
                <el-input v-model="adminList.username" disabled />
              </el-form-item>
              <!--用户密码-->
              <el-form-item :label="$t('lbs.common.password')">
                <el-input v-model="adminList.pwd" disabled />
              </el-form-item>

              <div class="box_two">
                <!--员工额度配置-->
                <div class="box_two01"><u>{{ $t('Workflow.Employeeconfig') }}</u></div>
              </div>
              <!--"Workflow.Retail":"零售额度",-->
              <!--"Industria":"工商额度",-->
              <!--零售额度-->
              <el-form-item :label="$t('Workflow.Retail')" style="margin-top: 8px">
                <!--<el-input v-model="adminList.transLimit" ></el-input>-->
                <el-select v-model="adminList.transLimit" filterable disabled>
                  <el-option value="0" />
                  <el-option value="100000" />
                  <el-option value="500000" />
                  <el-option value="1000000" />
                  <el-option value="1000000" />
                  <el-option value="Na" />
                </el-select>

              </el-form-item>
              <!--工商额度-->
              <el-form-item :label="$t('Workflow.Industria')">
                <!--<el-input v-model="adminList.icLimit" ></el-input>-->
                <el-select v-model="adminList.icLimit" filterable disabled>
                  <el-option value="0" />
                  <el-option value="100000" />
                  <el-option value="500000" />
                  <el-option value="1000000" />
                  <el-option value="1000000" />
                  <el-option value="Na" />
                </el-select>

              </el-form-item>

              <el-form-item>
                <!--Workflow.Edit 编辑-->
                <el-button type="primary" @click="goEditPage">{{ $t('Workflow.Edit') }}</el-button>
                <!--取消-->
                <!--<el-button>{{$t('Workflow.Cancel')}}</el-button>-->
              </el-form-item>
            </el-form>

          </div>

        </div>

      </div>
    </el-card>
  </div>
</template>

<script>
import axios from 'axios'

export default {
  components: {},
  props: [],
  data() {
    return {
      name: '',
      dialogCaptureDemographic: false,
      loading: false,
      input1: '',
      form: {
        date1: '',
        date2: '',
        delivery: false,
        name: '',
        region: '',
        type: [],
        resource: '',
        desc: ''
      },
      // 第一个下拉内容
      brandOptions: [],
      // 第二个下拉内容
      typeOptions: [],
      MobilyJson: {},
      token: null,
      LookerShow: false, // 展示
      adminList: {}, // 数据
      roleId: ''
    }
  },
  watch: {},
  created() {
    this.token = window.sessionStorage.getItem('token')
  },
  mounted() {
    this.getOneSel()
  },
  beforeDestroy() {
  },
  methods: {
    // 查找/adminuser/findOne
    Looker() {
      if (this.input1 === '' || this.input1 === undefined || this.input1 === null) {
        this.$message({
          message: this.$t('Operate.Pleasefirst'),
          type: 'warning'
        })
        return
      } else {
        const requestData = {
          staffNumber: this.input1
        }
        axios.post(`${this.LBSGateway}/sysadmin-process/sysadmin/adminuser/findOne`, requestData, { headers: { token: this.token }}).then(res => {
          if (res.data.code === '200') {
            this.LookerShow = !this.LookerShow
            this.adminList = res.data.data
            this.roleId = this.adminList.id
          } else {
            this.$message({
              showClose: true,
              message: res.data.msg,
              type: 'error'
            })
          }
        })
      }
    },
    // 新增
    goAddPage() {
      window.sessionStorage.setItem('adminList', '')
      this.$router.push({
        path: `/system/rolequota`,
        query: {
          roleId: this.roleId
        }
      })
    },
    // 编辑
    goEditPage() {
      window.sessionStorage.setItem('adminList', JSON.stringify(this.adminList))
      this.$router.push({ path: `/system/rolequota`,
        query: {
          roleId: this.roleId
        }})
    },
    // 点击第一个下拉
    changeSelect(val) {
      const item = this.brandOptions.filter(v => v.id === val)[0]
      this.getTwoSel(item)
    },
    // 部门下拉框的内容 //获取第一个下拉数据 department/findMany
    getOneSel() {
      const _this = this
      _this.loading = true
      const requestData = {
        departmentId: '',
        start: 0,
        end: 10000
      }
      axios.post(`${_this.LBSGateway}/sysadmin-service/department/findMany`, requestData, { headers: { token: _this.token }}).then(res => {
        _this.loading = false
        if (res.data.code === '200') {
          const datas = this.brandOptions = JSON.parse(res.data.data)
          this.MobilyJson.department = datas[0].id

          // 获取二级下拉-默认获取第一个
          // this.getTwoSel(datas[0])
        } else {
          this.$message({
            showClose: true,
            message: res.data.msg,
            type: 'error'
          })
        }
      })
    },
    // 获取第二个下拉数据
    getTwoSel(request) {
      sessionStorage.setItem('idSelect', request.id)
      const _this = this
      const requestData = {
        departmentId: request.id,
        start: 0,
        end: 10000
      }
      axios.post(`${_this.LBSGateway}/sysadmin-service/role/findMany`, requestData, { headers: { token: _this.token }}).then(res => {
        if (res.data.code === '200') {
          const datas = res.data.data
          if (datas) {
            const arr = JSON.parse(res.data.data)
            const adminListRole = []
            arr.forEach(item => {
              adminListRole.push(item.role)
            })

            this.typeOptions = adminListRole
          } else {
            this.typeOptions = []
            this.adminList.role = ''
          }
        } else {
          this.$message({
            showClose: true,
            message: res.data.desc,
            type: 'error'
          })
        }
      })
    }

  }

}
</script>

<style lang='scss' scoped>
  .workflow {
    min-height: 840px;
    .list-group1 {
      display: inline-block;
      width: 100%;
      height: 66px;
    }

    .list-group2 {
      display: inline-block;
      width: 100%;
      height: 61px;
    }

    .w-group-item {
      width: 146px;
      margin: 0 5px 5px 0;
      text-align: center;
      cursor: move;
    }
  }
  .content{
    width: 100%;
   .box{
     border: 3px solid white;
     background: #ffffff;
     width: 60%;
     margin: 0px auto;
     padding-top: 30px;
   }
  }
  .content_one{
    width: 100%;
    text-align: center;
    font-size: 22px;
  }
  .content_1{
    width: 80%;
    padding: 15px;
    margin: 10px auto;
    display: flex;
    justify-content: center;
    margin-top: 30px;
    .Yuangong{
      line-height: 38px;
      font-size: 16px;
      margin-right: 20px;
    }
  }
  .content_2{
    width: 80%;
    padding: 15px;
    display: flex;
    justify-content: center;
    .Yuangong{
      line-height: 38px;
      font-size: 13px;
    }
  }
  .box_two{
    position: relative;
    height: 60px;
    line-height: 60px;
    .box_two01{
     color: #0f5132;
      font-weight: bold;
      font-size: 16px;
      position: absolute;
      top:-6px;
      left: -52px;
    }
  }
  .content01{
    width: 60px;
    height: 30px;
    font-size: 14px;
    text-align: center;
    line-height: 30px;
    border-radius: 5px;
    cursor: pointer;
    color: white;
    background:#109eae;
    margin-left: 30px;
  }
</style>
