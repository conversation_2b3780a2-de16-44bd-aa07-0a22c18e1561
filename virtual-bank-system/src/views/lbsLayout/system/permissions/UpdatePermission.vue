<template>
  <div class="update-permission-container">
    <permission-form
      :permission-form.sync="permission"
      :permission-options="permissionOptions"
      :confirm-button-loading="confirmButtonLoading"
      @validated="updatePermission()"
      @cancel="$emit('cancel')"
    />
  </div>
</template>

<script>
import PermissionForm from '@/views/lbsLayout/system/permissions/PermissionForm.vue'
import axios from 'axios'

export default {
  name: 'UpdatePermission',
  components: { PermissionForm },
  props: {
    permissionId: {
      type: Number,
      required: true
    }
  },
  data() {
    return {
      token: null,
      confirmButtonLoading: false,
      permission: {},
      permissionOptions: []
    }
  },
  async mounted() {
    this.token = window.sessionStorage.getItem('token')

    // 加载权限信息
    this.permission = await this.getPermissionDetails()

    // 加载权限列表
    this.permissionOptions = await this.getPermissionOptions()
  },
  methods: {
    async getPermissionDetails() {
      const { data } = await axios.get(this.LBSGateway + `/sysadmin-process/sysadmin/fun-permission/${this.permissionId}`, { headers: { token: this.token }})
      const { data: permission } = data
      return permission
    },
    async getPermissionOptions() {
      const { data } = await axios.get(this.LBSGateway + '/sysadmin-process/sysadmin/fun-permission', { headers: { token: this.token }})
      const { data: permissionOptions } = data

      function clearEmptyChildren(nodes) {
        nodes.forEach(node => {
          if (!node.children.length) {
            delete node.children
          } else {
            clearEmptyChildren(node.children)
          }
        })
      }

      clearEmptyChildren(permissionOptions)

      return [
        {
          accredit: 'root',
          children: permissionOptions,
          id: 0,
          parentId: 0,
          permissionName: 'Root',
          sortNumber: 0,
          label: 'Root'
        }
      ]
    },
    // 调用API更新权限
    updatePermission() {
      this.confirmButtonLoading = true
      const requestData = { ...this.permission }
      axios.post(this.LBSGateway + '/sysadmin-process/sysadmin/fun-permission/update', requestData, { headers: { token: this.token }})
        .then((res) => {
          const { code, msg } = res.data
          if (code === '200') {
            this.$message.success(msg)
            this.$emit('success')
          } else {
            this.$message.error(msg)
          }
        })
        .finally(() => {
          this.confirmButtonLoading = false
        })
    }
  }
}
</script>

<style scoped lang="scss">
.update-permission-container {
  padding: 0 20px;
}
</style>
