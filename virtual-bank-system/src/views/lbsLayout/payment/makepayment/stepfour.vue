<template>
  <div class="stepfour">
    <el-row>
      <el-col :span="24" offset="2">
        <div style="float: left;margin-right: 20px">
          <div class="el-step__icon is-text is-finish" style="margin-top: 8px">
            <div class="el-step__icon-inner">1</div>
          </div>
          <div style="border-right: 2px #109eae solid;height: 150px;width: 13px" />
          <div class="el-step__icon is-text is-finish">
            <div class="el-step__icon-inner">2</div>
          </div>
          <div style="border-right: 2px #109eae solid;height: 248px;width: 13px" />
          <div class="el-step__icon is-text is-finish">
            <div class="el-step__icon-inner">3</div>
          </div>
          <div style="border-right: 2px #109eae solid;height: 270px;width: 13px" />
          <div class="el-step__icon is-text is-ing">
            <div class="el-step__icon-inner">4</div>
          </div>
        </div>
        <div class="right">
          <p class="text-title is-finish">Move Money From</p>
          <div style="padding: 43px 0 43px">
            <p class="text-account">Account</p>
            <el-select
              slot="prepend"
              v-model="accountNumber"
              disabled
              placeholder=""
              style="width: 400px;margin-right: 200px"
            >
              <el-option
                v-for="(item,index) in accountList"
                :key="index"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
            <div style="display: inline-block">
              <el-checkbox-group v-model="checkList">
                <el-checkbox disabled label=" " />
                <p style="display: inline-block;cursor: pointer;text-decoration: underline" @click="submit()">EDIT</p>
              </el-checkbox-group>
            </div>
          </div>
          <p class="text-title is-finish">Move Money To</p>
          <div style="padding: 20px 0 20px">
            <p class="text-account" style="width: 170px;margin: 20px 0">Category</p>
            <el-select
              v-model="category"
              disabled
              filterable
              placeholder="Please select Payee Category"
              style="width: 400px"
              @change="initPayeeInfoList()"
            >
              <el-option
                v-for="item in categoryList"
                id="category"
                :key="item.payeecategoryid"
                :label="item.payeecategory"
                :value="item.payeecategoryid"
              />
            </el-select>
            <br>
            <p class="text-account" style="width: 170px;margin: 20px 0">Payee Name</p>
            <el-select
              v-model="payeename"
              disabled
              filterable
              placeholder="Please select Payee Name"
              style="width: 400px;margin-right: 200px"
              @change="initCustomerPayeeList()"
            >
              <el-option
                v-for="item in payeeList"
                id="payeename"
                :key="item.payeeid"
                :label="item.payeename"
                :value="item.payeeid"
              />
            </el-select>
            <div style="display: inline-block">
              <el-checkbox-group v-model="checkList" disabled>
                <el-checkbox label="  " />
                <p style="display: inline-block;cursor: pointer;text-decoration: underline" @click="submit('two')">
                  EDIT</p>
              </el-checkbox-group>
            </div>
            <br>
            <p class="text-account" style="width: 170px;margin: 20px 0">Payee Number</p>
            <el-select
              slot="prepend"
              v-model="payeenumber"
              disabled
              filterable
              placeholder="Please select Payee Number"
              style="width: 400px;margin-right: 200px"
            >
              <el-option
                v-for="(item,index) in payeeNumberList"
                :key="index"
                :label="item.payeenumber"
                :value="item.payeenumber"
              />
            </el-select>
          </div>
          <p class="text-title is-finish">Transfer Details</p>
          <p style="display: inline-block;font-size: 20px;margin: 20px 0 20px 0;width: 170px">Amount</p>
          <el-input
            v-model="amount"
            disabled
            placeholder="Enter amount"
            class="input-with-select"
            style="width: 400px"
            maxlength="50"
          >
            <el-select slot="prepend" v-model="currency" disabled placeholder="" style="width: 80px">
              <el-option label="HKD" value="HKD" />
            </el-select>
          </el-input>
          <br>
          <p style="display: inline-block;font-size: 20px;margin: 20px 0 20px 0;width: 170px">Effective Date</p>
          <el-date-picker
            id="effectiveDay"
            v-model="value"
            disabled
            style="width: 400px;margin-right: 200px"
            type="date"
            placeholder=""
            :picker-options="pickerOptions"
          />
          <div style="display: inline-block">
            <el-checkbox-group v-model="checkList" disabled>
              <el-checkbox label="   " />
              <p style="display: inline-block;cursor: pointer;text-decoration: underline" @click="submit('three')">
                EDIT</p>
            </el-checkbox-group>
          </div>
          <br>
          <div style="width: 620px;position: relative">
            <p style="display: inline-block;font-size: 20px;margin: 20px 0 20px 0;width: 170px;position: absolute">
              Reference</p>
            <el-input
              v-model="reference"
              disabled
              type="textarea"
              style="width: 400px;margin-top: 10px;float: right"
              placeholder="(Optional)"
              maxlength="100"
            />
          </div>
          <p class="text-title is-ing">Confirmation</p>
          <div style="width: 800px">
            <el-button class="button" style="float: right;background-color: #109eae;color: #ffffff" icon="el-icon-check" @click="submit('next')">CONFIRM</el-button>
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
export default {
  name: 'Steptwo',
  data() {
    return {
      currency: 'HKD',
      accountNumber: '',
      savingList: [],
      currentList: [],
      creditCardList: [],
      accountList: [],
      paymentInfo: {},
      checkList: [' ', '  ', '   '],
      creditCardAccountList: [],
      category: '',
      payeename: '',
      payeenumber: '',
      categoryList: [],
      payeeList: [],
      payeeNumberList: [],
      amount: [],
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() < Date.now() - 8.64e7 || time.getTime() > Date.now() + 1000 * 60 * 60 * 24 * 365
        }
      },
      value: '',
      reference: ''
    }
  },
  watch: {
    amount: {
      handler() {
        this.clearNoNum()
      }
    }
  },
  mounted() {
    this.token = window.sessionStorage.getItem('token')
    this.paymentInfo = JSON.parse(window.sessionStorage.getItem('paymentDetail'))
    this.initdata()
  },
  methods: {
    initdata() {
      this.accountNumber = this.paymentInfo.accountNumber
      this.category = this.paymentInfo.categoryid
      this.payeename = this.paymentInfo.payeeid
      this.payeenumber = this.paymentInfo.payeenumber
      this.initPayeeCategoryList()
      this.initPayeeInfoList()
      this.initCustomerPayeeList()
      if (this.paymentInfo.currency && this.paymentInfo.amount && this.paymentInfo.effectiveDay) {
        this.currency = this.paymentInfo.currency
        this.amount = this.paymentInfo.amount
        document.getElementById('effectiveDay').value = this.paymentInfo.effectiveDay
        this.reference = this.paymentInfo.reference
      }
      var nowDate = this.$moment.parseZone(new Date().getTime()).local().format('YYYY-MM-DD')
      var effectiveDay = document.getElementById('effectiveDay')
      effectiveDay.value = nowDate
    },
    submit(type) {
      if (type === 'next') {
        this.$router.push({ path: '/lbspayment/makepayment/stepform' })
      } else if (type === 'two') {
        this.$router.push({ path: '/lbspayment/makepayment/steptwo' })
      } else if (type === 'three') {
        this.$router.push({ path: '/lbspayment/makepayment/stepthree' })
      } else {
        this.$router.push({ path: '/lbspayment/makepayment' })
      }
    },
    getcreditcardnumberpickerdata(list) {
      var accountlist = JSON.parse(list)
      var pickerdata = []
      for (var i = 0; i < accountlist.length; i++) {
        if (accountlist[i].accountStatus === 'A') {
          pickerdata.push({
            label: accountlist[i].accountNumber,
            value: accountlist[i].accountNumber
          })
        }
      }
      return pickerdata
    },
    initPayeeCategoryList() {
      var vue = this
      this.$mui.ajax(this.LBSGateway + '/payment-experience/payment/payeeCategoryList', {
        dataType: 'json', // 服务器返回json格式数据
        type: 'get', // HTTP请求类型
        timeout: 60000,
        headers: {
          'accept': '*/*',
          'token': this.token,
          'clientid': vue.$parent.clientid,
          'messageid': vue.$parent.messageid,
          'Content-Type': 'application/json'
        },
        beforeSend: function() {
          // document.getElementById("load").style.display = "inline";
        },
        complete: function() {
          // document.getElementById("load").style.display = "none";
        },
        success: function(data) {
          console.log(data.data)
          // Page show
          vue.categoryList = data.data
          // vue.category = vue.categoryList[0].payeecategoryid
          // vue.initPayeeInfoList()
        },
        error: function(xhr, type, errorThrown) {
          console.log(type)
          var msg = 'Get PayeeCategoryList failed! The response is: \n' + xhr.responseText + '.'
          if (type === 'timeout') {
            msg = 'Get PayeeCategoryList failed. Time out!'
          }
          vue.$mui.alert(msg, 'Error', 'OK')
        }
      })
    },
    initPayeeInfoList() {
      var vue = this
      var requesttimedata = {
        'payeeCategoryId': vue.category
      }
      this.$mui.ajax(this.LBSGateway + '/payment-experience/payment/payeeInfoListRetrieval', {
        data: requesttimedata,
        dataType: 'json', // 服务器返回json格式数据
        type: 'post', // HTTP请求类型
        timeout: 60000,
        headers: {
          'accept': '*/*',
          'token': this.token,
          'clientid': vue.$parent.clientid,
          'messageid': vue.$parent.messageid,
          'Content-Type': 'application/json'
        },
        beforeSend: function() {
          // document.getElementById("load").style.display = "inline";
        },
        complete: function() {
          // document.getElementById("load").style.display = "none";
        },
        success: function(data) {
          // Page show
          vue.payeeList = data.data
          console.log(vue.payeeList)
          // vue.payeename = vue.payeeList[0].payeeid
          // vue.initCustomerPayeeList();
        },
        error: function(xhr, type, errorThrown) {
          console.log(type)
          var msg = 'Get PayeeInfoList failed! The response is: \n' + xhr.responseText + '.'
          if (type === 'timeout') {
            msg = 'Get PayeeInfoList failed. Time out!'
          }
          vue.$mui.alert(msg, 'Error', 'OK')
        }
      })
    },
    initCustomerPayeeList() {
      var vue = this
      this.$mui.ajax(this.LBSGateway + '/payment-experience/payment/customerPayeeRetrieval', {
        dataType: 'json', // 服务器返回json格式数据
        type: 'get', // HTTP请求类型
        timeout: 60000,
        headers: {
          'accept': '*/*',
          'token': this.token,
          'clientid': vue.$parent.clientid,
          'messageid': vue.$parent.messageid,
          'Content-Type': 'application/json'
        },
        beforeSend: function() {
          // document.getElementById("load").style.display = "inline";
        },
        complete: function() {
          // document.getElementById("load").style.display = "none";
        },
        success: function(data) {
          console.log(data)
          vue.payeeListTemp = []
          if (data.code === '200') {
            var payeeList = data.data
            // Page show
            var payeeListTemp = []
            for (var i = 0; i < payeeList.length; i++) {
              if (payeeList[i].payeecategoryid === vue.category && payeeList[i].payeeid === vue.payeename) {
                payeeListTemp.push(payeeList[i])
              }
            }
            console.log(payeeListTemp)
            if (payeeListTemp.length > 0) {
              vue.payeeNumberList = payeeListTemp
              // vue.payeenumber = vue.payeeNumberList[0].payeenumber
            } else {
              vue.payeeNumberList = []
              vue.payeenumber = ''
            }
          }
        },
        error: function(xhr, type, errorThrown) {
          console.log(type)
          var msg = 'Get CustomerPayeeList failed! The response is: \n' + xhr.responseText + '.'
          if (type === 'timeout') {
            msg = 'Get CustomerPayeeList failed. Time out!'
          }
          vue.$mui.alert(msg, 'Error', 'OK')
        }
      })
    },
    clearNoNum() {
      this.amount = this.amount.replace(/[^\d.]/g, '') // 清除"数字"和"."以外的字符
      this.amount = this.amount.replace(/^\./g, '') // 验证第一个字符是数字而不是字符
      this.amount = this.amount.replace(/\.{2,}/g, '.') // 只保留第一个.清除多余的
      this.amount = this.amount.replace('.', '$#$').replace(/\./g, '').replace('$#$', '.')
      this.amount = this.amount.replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3') // 只能输入两个小数
    }
  }
}
</script>

<style scoped>
  .stepfour p {
    line-height: 1;
    color: #707070;
  }

  .stepfour .text-title {
    font-size: 40px;
    color: #707070;
  }

  .stepfour .text-account {
    display: inline-block;
    font-size: 20px;
    margin-right: 100px;
  }

  .stepfour .is-finish {
    color: #109eae;
    border-color: #109eae;
  }

  .stepfour .no-finish {
    color: #707070;
    border-color: #707070;
  }

  .stepfour .is-ing {
    color: #000000;
    border-color: #000000;
  }
</style>

<style>
  .stepfour .el-step__title {
    font-size: 40px;
    margin-left: 20px;
    color: #707070;
  }

  .stepfour .el-input__icon {
    height: 40px;
  }

  .stepfour .el-input__inner {
    margin-bottom: 0;
    height: 40px;
  }

  .stepfour .el-checkbox__inner {
    width: 20px;
    height: 20px;
  }

  .stepfour .el-checkbox__inner::after {
    width: 8px;
    height: 10px;
  }

  .stepfour .el-checkbox {
    color: #109eae;
    cursor: default;
  }

  .stepfour .el-checkbox__input.is-disabled.is-checked .el-checkbox__inner {
    background-color: #109eae;
  }

  .stepfour .el-checkbox__input.is-disabled.is-checked .el-checkbox__inner::after {
    border-color: #ffffff;
  }

  .stepfour .el-input.is-disabled .el-input__inner {
    color: #707070;
  }

  .stepfour .el-checkbox__inner {
    width: 20px;
    height: 20px;
    border-color: #999999;
  }

  .stepfour .el-textarea__inner {
    height: 100px;
  }
</style>
