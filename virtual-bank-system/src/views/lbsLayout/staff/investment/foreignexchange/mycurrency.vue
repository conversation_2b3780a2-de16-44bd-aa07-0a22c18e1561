<template>
  <div v-loading.fullscreen.lock="loading" class="mycurrency">
    <el-row>
      <el-col :span="24">
        <div class="main">
          <p style="font-size: 25px;color: #22C1E6">{{ $t('lbs.ForeignExchangeBalanceInformation') }}</p>
          <div style="text-align: center;margin-top: 50px">
            <span>{{ $t('lbs.ForeignExchangeAccount') }}：</span>
            <el-select v-model="FxAccount" placeholder="" style="width: 350px;">
              <el-option
                v-for="item in FxAccountList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
            <el-card style="width: 71%;margin: 30px auto;padding: 40px">
              <el-table :data="FxBalanceList">
                <el-table-column prop="ccy" :label="$t('lbs.CurrencyCode')" />
                <el-table-column prop="balance" :label="$t('lbs.Balance')" />
                <el-table-column :label="$t('lbs.buy')">
                  <template slot-scope="scope">
                    <el-button style="width: 140px" type="success" @click="goToPage('buy',scope.row.ccy)">{{ $t('lbs.buy') }}</el-button>
                  </template>
                </el-table-column>
                <el-table-column :label="$t('lbs.sell')">
                  <template slot-scope="scope">
                    <el-button style="width: 140px" type="danger" :disabled="Number(scope.row.balance) <= 0" @click="goToPage('sell',scope.row.ccy)">{{ $t('lbs.sell') }}</el-button>
                  </template>
                </el-table-column>
              </el-table>
              <el-button type="primary" style="width: 150px;margin-top: 30px;float: right" @click="$router.go(-1)">{{ $t('lbs.common.back') }}</el-button>
            </el-card>
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import axios from 'axios'

export default {
  data: () => ({
    token: null,
    loading: false,
    FxAccount: null,
    FxAccountList: [],
    FxBalanceList: [],
    customernumber: ''
  }),
  watch: {
    'FxAccount'() {
      this.selectFxBalance()
    }
  },
  created() {
    this.customernumber = window.sessionStorage.getItem('targetCustomerNumber')
    this.token = window.sessionStorage.getItem('token')
  },
  mounted() {
    const FxAccountList = JSON.parse(window.sessionStorage.getItem('fexaccountlist'))
    for (let i = 0; i < FxAccountList.length; i++) {
      if (FxAccountList[i].accountStatus === 'A') {
        this.FxAccountList.push(
          {
            label: FxAccountList[i].accountNumber,
            value: FxAccountList[i].accountNumber
          }
        )
      }
    }
    this.FxAccount = this.FxAccountList[0].label
  },
  methods: {
    selectFxBalance() {
      const _this = this
      this.loading = true
      axios.post(`${_this.LBSGateway}/deposit-experience/deposit/account/accountDetails/${_this.FxAccount}`, {}, { headers: { token: _this.token, customerNumber: _this.customernumber }})
        .then(response => {
          _this.loading = false
          _this.FxBalanceList = []
          if (response.data.code === '200') {
            const result = response.data.data.account.fexbalance
            for (let i = 0; i < result.length; i++) {
              if (Number(result[i].balance) !== 0) {
                _this.FxBalanceList.push(result[i])
              }
            }
          } else {
            _this.$message.error(response.data.msg, _this.$t('lbs.common.error'))
          }
        })
        .catch(error => {
          _this.loading = false
          _this.$mui.alert(error.message, _this.$t('lbs.common.error'), _this.$t('lbs.common.confirm'))
        })
    },
    goToPage(type, code) {
      this.$router.push({ path: '/tellerlbsinvestment/foreignexchange/transaction?type=' + type + '&code=' + code })
    }
  }
}
</script>

<style lang="scss" scoped>

  .mycurrency {
    .main {
      margin: 40px 60px;
    }
  }

</style>
