<template>
  <div v-loading.fullscreen.lock="loading" class="foreignexchangeindex">
    <el-row>
      <el-col :span="7">
        <div class="main">
          <el-tooltip effect="dark" placement="right">
            <div slot="content" style="width: 300px">{{ $t('lbsTips.ForeignExchange.MyFxAccount') }}</div>
            <p class="title">{{ $t('lbs.MyFXAccount') }}</p>
          </el-tooltip>
          <div class="fxaccount">
            <p class="view-more">{{ $t('lbs.ViewMore') }}</p>
            <el-tooltip effect="dark" placement="right">
              <div slot="content" style="width: 300px">{{ $t('lbsTips.ForeignExchange.AccountNumber') }}</div>
              <p class="text-key">{{ $t('lbs.AccountNumber') }}</p>
            </el-tooltip>
            <p class="text-value">{{ FxAccount }}</p>
            <!--            <el-tooltip effect="dark" placement="right">-->
            <!--              <div slot="content" style="width: 300px">{{$t('lbsTips.ForeignExchange.RelAccountNumber')}}</div>-->
            <!--              <p class="text-key">{{$t('lbs.RelAccountNumber')}}</p>-->
            <!--            </el-tooltip>-->
            <!--            <p class="text-value">{{FxAccount}}</p>-->
            <el-tooltip effect="dark" placement="right">
              <div slot="content" style="width: 300px">{{ $t('lbsTips.ForeignExchange.FxBalance') }}</div>
              <p class="text-key">{{ $t('lbs.FxBalance') }}</p>
            </el-tooltip>
            <p class="text-value" style="margin-bottom: 0">HKD {{ availablebalance }}</p>
          </div>
          <div class="button-group">
            <!--            <el-tooltip effect="dark" placement="right">-->
            <!--              <div slot="content" style="width: 300px">{{$t('lbsTips.ForeignExchange.ExchangeCurrency')}}</div>-->
            <!--              <el-button class="button" type="primary">{{$t('lbs.ExchangeCurrency')}}</el-button>-->
            <!--            </el-tooltip>-->
            <!--            <br/>-->
            <el-tooltip effect="dark" placement="right">
              <div slot="content" style="width: 300px">{{ $t('lbsTips.ForeignExchange.ViewMyCurrency') }}</div>
              <el-button
                class="button"
                type="success"
                @click="$router.push({path: '/tellerlbsinvestment/foreignexchange/mycurrency'})"
              >
                {{ $t('lbs.ViewMyCurrency') }}
              </el-button>
            </el-tooltip>
            <el-button class="button" type="primary" style="width: 300px;margin-left: 0" @click="$router.push({path: '/tellerlbsinvestment/foreignexchange/transactionhistory'})">{{ $t('lbs.ViewMyTransactionHistory') }}</el-button>
          </div>
        </div>
      </el-col>
      <el-col :span="17">
        <div class="exchange-group">
          <el-tooltip effect="dark" placement="right">
            <div slot="content" style="width: 600px">{{ $t('lbs.currencyCalculator') }}</div>
            <p style="font-size: 30px;color: #22C1E6;width: 350px">{{ $t('lbs.ExchangeRateCalculator') }}</p>
          </el-tooltip>
          <div class="exchange-card exchange-card-left">
            <el-tooltip effect="dark" placement="top">
              <div slot="content" style="width: 300px">{{ $t('lbsTips.ForeignExchange.EURUSD') }}</div>
              <el-select
                v-model="currencyLeft"
                :placeholder="$t('lbs.SelectCurrency')"
                filterable
                style="width: 145px"
                @change="countFx()"
              >
                <el-option
                  v-for="item in currencyList"
                  :key="item.ccycode"
                  :label="item.ccycode"
                  :value="item.ccycode"
                  :disabled="item.ccycode === currencyRight"
                />
              </el-select>
            </el-tooltip>
            <span style="margin-left: 50px;font-size: 13px">1 {{ currencyLeft }} = {{ $lbs.decimal_format(xbuy / ysell,5) }} {{ currencyRight }}</span>
            <hr>
            <span class="currency">{{ currencyLeft }}</span>
            <input
              v-model="ammountLeft"
              type="text"
              style="width: 30%;border-top: none;border-left: none;border-right: none;font-size: 13px;padding-bottom: 0;margin-left: 70px"
              @change="countFx()"
            >
          </div>

          <div style="display: inline-block;float: left">
            <el-button type="success" style="margin-top: 80px" @click="Exchange">{{ $t('lbs.Exchange') }}</el-button>
          </div>
          <div class="exchange-card exchange-card-right">
            <el-tooltip effect="dark" placement="top">
              <div slot="content" style="width: 300px">{{ $t('lbsTips.ForeignExchange.EURUSD') }}</div>
              <el-select
                v-model="currencyRight"
                :placeholder="$t('lbs.SelectCurrency')"
                filterable
                style="width: 145px"
                @change="countFx()"
              >
                <el-option
                  v-for="item in currencyList"
                  :key="item.ccycode"
                  :label="item.ccycode"
                  :value="item.ccycode"
                  :disabled="item.ccycode === currencyLeft"
                />
              </el-select>
            </el-tooltip>
            <span style="margin-left: 50px;font-size: 13px">1 {{ currencyRight }} = {{ $lbs.decimal_format(ybuy / xsell, 5) }} {{ currencyLeft }}</span>
            <hr>
            <span class="currency">{{ currencyRight }}</span>
            <input
              v-model="ammountRight"
              type="text"
              disabled
              style="background-color: #b5f1ff;width: 30%;border-top: none;border-left: none;border-right: none;font-size: 13px;padding-bottom: 0;margin-left: 70px"
            >
          </div>
        </div>
      </el-col>
      <el-col :span="14" style="float: right">
        <div style="margin-top: 40px;margin-right: 75px;padding: 20px 40px;background-color: #FFF;border-radius: 5px">
          <div style="width: 100%;border-bottom: 1px solid #ccc;padding-bottom: 25px">
            <el-tooltip effect="dark" placement="right">
              <div slot="content" style="width: 300px">{{ $t('lbsTips.ForeignExchange.CurrencyTable') }}</div>
              <span style="font-size: 20px;margin-top: 5px;display: inline-block ">{{ $t('lbs.CurrencyTable') }}</span>
            </el-tooltip>
            <el-tooltip effect="dark" placement="top">
              <div slot="content" style="width: 300px">{{ $t('lbsTips.ForeignExchange.DollarSign') }}</div>
              <el-select
                v-model="currencyTable"
                :placeholder="$t('lbs.SelectCurrency')"
                filterable
                style="width: 145px;float: right;"
                disabled
              >
                <el-option
                  v-for="item in currencyList"
                  :key="item.ccycode"
                  :label="item.ccycode"
                  :value="item.ccycode"
                />
              </el-select>
            </el-tooltip>
          </div>
          <el-table
            height="615"
            :data="currencyTableData"
            style="width: 100%;margin-top: 20px"
          >
            <el-table-column
              prop="ccycode"
              label="Currency Code"
            />
            <el-table-column
              prop="bankbuy"
              label="Bank Buy"
            />
            <el-table-column
              prop="banksell"
              label="Bank Sell"
            />
            <el-table-column
              prop="buy"
            >
              <template slot-scope="scope">
                <el-tooltip effect="dark" placement="right">
                  <div slot="content" style="width: 100px">{{ $t('lbsTips.ForeignExchange.Buy') }}</div>
                  <el-button
                    :disabled="scope.row.ccycode === 'HKD'"
                    type="success"
                    style="width: 100px"
                    @click="goToPage('buy',scope.row.ccycode)"
                  >{{ $t('lbs.buy') }}
                  </el-button>
                </el-tooltip>
              </template>
            </el-table-column>
            <el-table-column
              prop="sell"
            >
              <template slot-scope="scope">
                <el-tooltip effect="dark" placement="right">
                  <div slot="content" style="width: 100px">{{ $t('lbsTips.ForeignExchange.Sell') }}</div>
                  <el-button
                    :disabled="scope.row.ccycode === 'HKD'"
                    type="danger"
                    style="width: 100px"
                    @click="goToPage('sell',scope.row.ccycode)"
                  >{{ $t('lbs.sell') }}
                  </el-button>
                </el-tooltip>
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-col>
      <el-col :span="10">
        <el-tooltip effect="dark" placement="right">
          <div slot="content" style="width: 300px">{{ $t('lbsTips.ForeignExchange.Period') }}</div>
          <span style="display: inline-block;margin-right: 10px;margin-left: 40px">{{ $t('lbs.Period') }}：</span>
        </el-tooltip>
        <el-date-picker
          v-model="value"
          style="width: 420px"
          type="daterange"
          :range-separator="$t('lbs.to')"
          :start-placeholder="$t('lbs.startDate')"
          :end-placeholder="$t('lbs.endDate')"
        />
        <div style="width: 400px;margin:  20px auto 0">
          <div style="width: 365px;margin: 0 auto">
            <el-tooltip effect="dark" placement="bottom">
              <div slot="content" style="width: 300px">{{ $t('lbsTips.ForeignExchange.Days') }}</div>
              <el-button size="mini" type="primary" style="width: 80px">{{ $t('lbs.Days') }}</el-button>
            </el-tooltip>
            <el-tooltip effect="dark" placement="bottom">
              <div slot="content" style="width: 300px">{{ $t('lbsTips.ForeignExchange.Weeks') }}</div>
              <el-button size="mini" type="primary" style="width: 80px">{{ $t('lbs.Weeks') }}</el-button>
            </el-tooltip>
            <el-tooltip effect="dark" placement="bottom">
              <div slot="content" style="width: 300px">{{ $t('lbsTips.ForeignExchange.Months') }}</div>
              <el-button size="mini" type="primary" style="width: 80px">{{ $t('lbs.Months') }}</el-button>
            </el-tooltip>
            <el-tooltip effect="dark" placement="bottom">
              <div slot="content" style="width: 300px">{{ $t('lbsTips.ForeignExchange.Years') }}</div>
              <el-button size="mini" type="primary" style="width: 80px">{{ $t('lbs.Years') }}</el-button>
            </el-tooltip>
          </div>
          <svg-icon icon-class="map" style="width: 400px;height: 350px" />
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import axios from 'axios'

export default {
  name: 'Index',
  data() {
    return {
      token: null,
      loading: false,
      currencyList: [],
      currencyTableData: [],
      currencyLeft: 'HKD',
      currencyRight: 'USD',
      currencyTable: 'HKD',
      ammountLeft: '0',
      ammountRight: '0',
      value: [],
      xbuy: 0,
      xsell: 0,
      ybuy: 0,
      ysell: 0,
      FxAccount: '',
      availablebalance: '',
      customernumber: ''
    }
  },
  created() {
    this.customernumber = window.sessionStorage.getItem('targetCustomerNumber')
    this.token = window.sessionStorage.getItem('token')
    const fxaccountlist = JSON.parse(window.sessionStorage.getItem('fexaccountlist'))
    for (let i = 0; i < fxaccountlist.length; i++) {
      if (fxaccountlist[i].accountStatus === 'A') {
        this.FxAccount = fxaccountlist[0].accountNumber
        break
      }
    }
    this.getCurrencyList()
    this.getFxBalance()
  },
  mounted() {
    if (window.sessionStorage.getItem('showTips') === 'true' && window.sessionStorage.getItem('token')) {
      this.$alert(this.$t('lbs.popupfx'), this.$t('lbs.Tip'), {
        dangerouslyUseHTMLString: true
      })
    }

    // -----------------------------------------------------
    // const params = {
    //   platform: 'Virtual banking system',
    //   userId: window.sessionStorage.getItem('role'),
    //   operationType: '虚拟银行主管角色投资菜单外汇'
    // }
    // this.$store.dispatch('app/VisitsFun', params).then((res) => {
    //   this.loading = false
    //   if (res.data.code !== 20000) {
    //     console.log(response.data.message)
    //   }
    // }).catch(error => {
    //   this.loading = false
    //   console.log(error.message)
    // })
    // -----------------------------------------
  },
  methods: {
    getCurrencyList() {
      var vue = this
      this.$mui.ajax(
        this.LBSGateway +
          '/sysadmin-experience/sysadmin/currency/currencyTypeRetrieval',
        {
          dataType: 'json', // 服务器返回json格式数据
          type: 'get', // HTTP请求类型
          timeout: 60000,
          headers: {
            accept: '*/*',
            token: this.token,
            clientid: vue.$parent.clientid,
            messageid: vue.$parent.messageid,
            'Content-Type': 'application/json',
            customerNumber: vue.customernumber
          },
          beforeSend: function() {
            vue.loading = true
          },
          complete: function() {
            vue.loading = false
          },
          success: function(data) {
            if (data.code === '200') {
              vue.currencyList = data.data
              for (let i = 0; i < vue.currencyList.length; i++) {
                if (vue.currencyList[i].ccycode === 'JPY') {
                  const bankbuy = vue.$lbs.decimal_format(1 / vue.currencyList[i].banksell, 4)
                  const banksell = vue.$lbs.decimal_format(1 / vue.currencyList[i].bankbuy, 4)
                  vue.currencyList[i].bankbuy = bankbuy
                  vue.currencyList[i].banksell = banksell
                }
              }
              for (let i = 0; i < vue.currencyList.length; i++) {
                if (vue.currencyList[i].ccycode !== 'HKD') {
                  vue.currencyTableData.push(vue.currencyList[i])
                }
              }
              vue.countFx()
            } else {
              vue.$mui.alert(
                'Get currencyType failed! The response is: \n' +
                  JSON.stringify(data.msg) +
                  '.',
                'Error',
                'OK'
              )
            }
          },
          error: function(xhr, type, errorThrown) {
            console.log(type)
            var msg =
                'Get currencyType failed! The response is: \n' +
                xhr.responseText +
                '.'
            if (type === 'timeout') {
              msg = 'Get currencyType failed. Time out!'
            }
            vue.$mui.alert(msg, 'Error', 'OK')
          }
        }
      )
    },
    Exchange() {
      const currencyLeft = this.currencyLeft
      this.currencyLeft = this.currencyRight
      this.currencyRight = currencyLeft
      this.countFx()
    },
    getFxBalance() {
      const vue = this
      if (!this.FxAccount) return
      vue.loading = true
      axios.post(
        `${vue.LBSGateway}/deposit-experience/deposit/account/accountDetails/${vue.FxAccount}`, {}, { headers: { token: vue.token, customerNumber: vue.customernumber }})
        .then(response => {
          vue.loading = false
          if (response.data.code === '200') {
            vue.availablebalance = response.data.data.account.availablebalance
          }
        })
        .catch(error => {
          vue.loading = false
          vue.$mui.alert(error.message, vue.$t('lbs.common.error'), vue.$t('lbs.common.confirm'))
        })
    },
    goToPage(type, code) {
      this.$router.push({ path: '/tellerlbsinvestment/foreignexchange/transaction?type=' + type + '&code=' + code })
    },
    countFx() {
      if (this.currencyLeft === this.currencyRight) {
        this.ammountRight = this.ammountLeft
        return
      }
      for (let i = 0; i < this.currencyList.length; i++) {
        if (this.currencyList[i].ccycode === this.currencyLeft) {
          this.xbuy = this.currencyList[i].bankbuy
          this.xsell = this.currencyList[i].banksell
        }

        if (this.currencyList[i].ccycode === this.currencyRight) {
          this.ysell = this.currencyList[i].banksell
          this.ybuy = this.currencyList[i].bankbuy
        }
      }
      this.ammountRight = this.$lbs.decimal_format(Number(this.ammountLeft) * this.xbuy / this.ysell, 5)
    }
  }
}
</script>

<style scoped>
  .foreignexchangeindex .exchange-text {
    display: inline-block;
    width: 330px;
  }

  .foreignexchangeindex hr {
    margin-top: 30px;
  }

  .foreignexchangeindex .exchange-text p {
    text-align: center;
  }

  .foreignexchangeindex .exchange-group {
    margin: 30px auto 0;
  }

  .foreignexchangeindex .exchange-card {
    width: 420px;
    height: 200px;
    float: left;
    border-radius: 10px;
    box-shadow: 3px 3px 3px #bbbbbb;
    padding: 30px 15px;
  }

  .foreignexchangeindex .exchange-card-left {
    background-color: #ffffff;
    margin-right: 20px;
  }

  .foreignexchangeindex .exchange-card-right {
    background-color: #b5f1ff;
    margin-left: 20px;
  }

  .foreignexchangeindex .button {
    width: 300px;
    margin-bottom: 30px;
  }

  .foreignexchangeindex .button-group {
    width: 300px;
    margin: 0 auto;
  }

  .foreignexchangeindex .main {
    padding: 40px 40px 20px 40px;
  }

  .foreignexchangeindex .main p {
    color: #707070;
    line-height: 1;
  }

  .foreignexchangeindex .main .title {
    display: inline-block;
    font-size: 30px;
    color: #22c1e6;
    margin-bottom: 30px;
  }

  .foreignexchangeindex .fxaccount {
    border: 1px solid #22c1e6;
    position: relative;
    padding: 30px 15px;
    margin-bottom: 30px;
    box-shadow: 3px 3px 3px #bbbbbb;
  }

  .foreignexchangeindex .main .view-more {
    color: #999999;
    font-size: 18px;
    text-decoration: underline;
    display: inline-block;
    float: right;
    position: absolute;
    top: 5px;
    right: 10px;
    cursor: pointer;
  }

  .foreignexchangeindex .text-key,
  .text-value {
    border-radius: 3px;
  }

  .foreignexchangeindex .main .text-key {
    font-size: 20px;
    color: #22c1e6;
    margin-bottom: 20px;
  }

  .foreignexchangeindex .main .text-value {
    font-size: 16px;
    color: #ffffff;
    margin: 0 20px 20px;
    padding: 10px 0;
    background-color: #22c1e6;
    text-align: center;
  }

  .foreignexchangeindex .currency {
    font-size: 25px;
    display: inline-block;
    margin-left: 20px;
  }
</style>

<style>
  .foreignexchangeindex .el-input__icon {
    height: 36px;
  }

  .foreignexchangeindex .el-select .el-input__inner {
    margin-bottom: 0;
  }

  .foreignexchangeindex .exchange-card-left .el-select .el-input__inner {
    background-color: #b5f1ff;
  }
</style>
