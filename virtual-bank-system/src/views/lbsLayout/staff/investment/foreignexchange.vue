<template>
  <div class="foreignexchange">
    <el-row>
      <el-col :span="24">
        <div class="title">
          <p class="max-title">{{ $t('lbs.ForeignExchange') }}</p>
        </div>
      </el-col>
      <router-view />
    </el-row>
  </div>
</template>

<script>
export default {
  name: 'Foreignexchange'
}
</script>

<style scoped>
  .foreignexchange .title {
    padding: 30px 28px;
    height: 140px;
    background-color: #FFFFFF;
    margin: 0 25px 0 25px;
    border-radius: 50px;
    background-image: url("../../components/title-bg.png");
    background-repeat: no-repeat;
    background-size: 574px 130px;
    background-position: right;
    box-shadow: 5px 5px 5px #bbbbbb;
  }

  .losforeignexchanges p {
    color: #707070;
    margin-bottom: 0;
    line-height: 1;
  }

  .foreignexchange .max-title {
    font-size: 50px;
    margin-bottom: 40px;
    font-weight: 500;
    display: inline-block;
    color: #22C1E6;
  }
</style>
