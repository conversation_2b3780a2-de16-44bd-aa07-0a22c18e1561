<template>
  <div v-loading.fullscreen.lock="loading" class="stock-transaction-history">
    <el-row>
      <el-col :span="24">
        <div class="lbs-icon-group1">
          <div class="lbs-icon-item">
            <div class="lbs-div-icon" @click="goToLink()">
              <svg-icon icon-class="ionic-md-information-circle-outline" class="lbs-icon" />
            </div>
            <p @click="goToLink()">{{ $t('lbs.marketInformation') }}</p>
          </div>
          <div class="lbs-icon-item">
            <div class="lbs-div-icon" @click="goToLink('stockportfolio')">
              <svg-icon icon-class="Icon material-favorite-border" class="lbs-icon" />
            </div>
            <p @click="goToLink('stockportfolio')">{{ $t('lbs.stockPortfolio') }}</p>
          </div>
          <div class="lbs-icon-item">
            <div class="lbs-div-icon" @click="goToLink('stockholding')">
              <svg-icon icon-class="Icon awesome-wpforms" class="lbs-icon" />
            </div>
            <p @click="goToLink('stockholding')">{{ $t('lbs.StockHolding') }}</p>
          </div>
          <div class="lbs-icon-item">
            <div class="lbs-div-icon lbs-current" @click="goToLink('transactionhistory')">
              <svg-icon icon-class="awesome-history" class="lbs-icon" />
            </div>
            <p class="lbs-current" style="color: #22C1E6" @click="goToLink('transactionhistory')">{{ $t('lbs.TransactionHistory') }}</p>
          </div>
          <div class="lbs-icon-item">
            <div class="lbs-div-icon" @click="goToLink('stockaccount')">
              <svg-icon icon-class="Icon material-person-pin" class="lbs-icon" />
            </div>
            <p @click="goToLink('stockaccount')">{{ $t('lbs.MyStockAccount') }}</p>
          </div>
        </div>
      </el-col>
      <el-col :span="24">
        <p class="title">{{ $t('lbs.TransactionHistory') }}</p>
        <el-card>
          <div class="select-time">
            <el-select v-model="stockAccount" style="width: 270px" @change="getTransactionHistory()">
              <el-option v-for="(item,index) in stockAccountOption" :key="index" :label="item.label" :value="item.value" />
            </el-select>
            <el-date-picker
              v-model="date"
              type="daterange"
              value-format="timestamp"
              :clearable="false"
              unlink-panels
              range-separator="-"
              :start-placeholder="$t('lbs.FundOrder.StartDate')"
              :end-placeholder="$t('lbs.FundOrder.EndDate')"
              @change="getTransactionHistory()"
            />
          </div>
          <el-table :data="transactionHistoryList" height="500">
            <el-table-column :label="$t('lbs.TranDate')" prop="transactiondate" align="center" />
            <el-table-column :label="$t('lbs.CountryCode')" prop="countrycode" align="center" />
            <el-table-column :label="$t('lbs.StockCode')" prop="stocknumber" align="center" />
            <el-table-column :label="$t('lbs.TradingOption')" prop="tradingoption" align="center" />
            <el-table-column :label="$t('lbs.SharingNo')" prop="sharingno" align="center" />
            <el-table-column :label="$t('lbs.TranAmt')" prop="stocktrdingamount" align="center" />
            <el-table-column :label="$t('lbs.StockTradingCommission')" prop="stocktrdingcommission" align="center" />
          </el-table>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import axios from 'axios'

export default {
  name: 'TransactionHistory',
  data: () => ({
    token: null,
    loading: false,
    stockAccount: '',
    stockAccountOption: [],
    date: [new Date().setTime(new Date().getTime() - 3600 * 1000 * 24 * 30), new Date().getTime()],
    transactionHistoryList: [],
    customernumber: ''
  }),
  created() {
    this.customernumber = window.sessionStorage.getItem('targetCustomerNumber')
    this.token = window.sessionStorage.getItem('token')
    this.getStockOption()
  },
  methods: {
    // 获取交易记录
    getTransactionHistory() {
      const _this = this
      if (!_this.stockAccount) return
      _this.loading = true
      const requestData = {
        accountnumber: _this.stockAccount,
        index: 0,
        items: 9999,
        transFromDate: this.date[0],
        transToDate: new Date(this.date[1]).getTime() + 24 * 60 * 60 * 1000 - 1
      }
      axios.post(`${_this.LBSGateway}/stock-experience/stock/transactionRetrieval`, requestData, { headers: { token: _this.token, customerNumber: _this.customernumber }})
        .then(response => {
          _this.loading = false
          _this.transactionHistoryList = []
          if (response.data.code === '200') {
            const tableData = response.data.data
            for (const item of tableData) {
              item.transactiondate = _this.$moment(item.transactiondate).format('YYYY-MM-DD HH:mm:ss')
              item.tradingoption = item.tradingoption.toUpperCase()
            }
            _this.transactionHistoryList = tableData
          } else if (response.data.code === '404010') {
            _this.$message.success(response.data.msg, _this.$t('lbs.common.success'))
          } else {
            _this.$message.error(response.data.msg, _this.$t('lbs.common.error'))
          }
        })
        .catch(error => {
          _this.loading = false
          _this.$message.error(error.message, _this.$t('lbs.common.error'))
        })
    },
    // 获取状态为A的股票账户
    getStockOption() {
      const tempFxOption = JSON.parse(window.sessionStorage.getItem('stockaccountlist'))
      for (const item of tempFxOption) {
        if (item.accountStatus === 'A') {
          this.stockAccountOption.push({
            label: item.accountNumber,
            value: item.accountNumber
          })
        }
      }
      if (this.stockAccountOption && this.stockAccountOption[0]) {
        this.stockAccount = this.stockAccountOption[0].value
      }
      this.getTransactionHistory()
    },
    goToLink(path) {
      if (path === undefined) {
        this.$router.push({ path: `/tellerlbsinvestment/stocktrading` })
      } else {
        this.$router.push({ path: `/tellerlbsinvestment/stocktrading/${path}` })
      }
    }
  }
}
</script>

<style lang="scss" scoped>

  .stock-transaction-history {
    padding: 30px 50px 0;

    .title {
      font-size: 20px;
      color: #22C1E6;
      margin-top: 10px;
    }

    .select-time {
      text-align: center;
      margin-bottom: 20px;
    }

    .lbs-icon {
      height: 35px;
      width: 35px;
    }

    .lbs-icon-item {
      width: 200px;
      text-align: center;
      display: inline-block;
    }

    .lbs-div-icon {
      width: 35px;
      margin: 0 auto;
      cursor: pointer;
    }

    .lbs-icon-group1 {
      width: 1020px;
      margin: 0 auto;
    }

    .lbs-icon-group2 {
      width: 816px;
      margin: 30px auto 0;
    }

    .lbs-icon-group1 .lbs-icon-item p {
      display: inline-block;
      cursor: pointer;
    }

    .lbs-icon-group1 .lbs-icon-item .lbs-div-icon.lbs-current {
      cursor: default;
    }

    .lbs-icon-group1 .lbs-icon-item p.lbs-current {
      cursor: default;
    }

    .lbs-icon-group2 .lbs-icon-item p {
      display: inline-block;
      cursor: pointer;
    }

    .lbs-icon-group2 .lbs-icon-item .lbs-div-icon.lbs-current {
      cursor: default;
    }

    .lbs-icon-group2 .lbs-icon-item p.lbs-current {
      cursor: default;
    }
  }
</style>
