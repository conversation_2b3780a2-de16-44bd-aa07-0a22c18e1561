<template>
  <div v-loading.fullscreen.lock="loading" class="stock-account">
    <div class="lbs-icon-group1">
      <div class="lbs-icon-item">
        <div class="lbs-div-icon" @click="goToLink()">
          <svg-icon icon-class="ionic-md-information-circle-outline" class="lbs-icon" />
        </div>
        <p @click="goToLink()">{{ $t('lbs.marketInformation') }}</p>
      </div>
      <div class="lbs-icon-item">
        <div class="lbs-div-icon" @click="goToLink('stockportfolio')">
          <svg-icon icon-class="Icon material-favorite-border" class="lbs-icon" />
        </div>
        <p @click="goToLink('stockportfolio')">{{ $t('lbs.stockPortfolio') }}</p>
      </div>
      <div class="lbs-icon-item">
        <div class="lbs-div-icon" @click="goToLink('stockholding')">
          <svg-icon icon-class="Icon awesome-wpforms" class="lbs-icon" />
        </div>
        <p @click="goToLink('stockholding')">{{ $t('lbs.StockHolding') }}</p>
      </div>
      <div class="lbs-icon-item">
        <div class="lbs-div-icon" @click="goToLink('transactionhistory')">
          <svg-icon icon-class="Icon awesome-history" class="lbs-icon" />
        </div>
        <p @click="goToLink('transactionhistory')">{{ $t('lbs.TransactionHistory') }}</p>
      </div>
      <div class="lbs-icon-item">
        <div class="lbs-div-icon">
          <svg-icon icon-class="material-person-pin" class="lbs-icon" />
        </div>
        <p style="color: #22C1E6">{{ $t('lbs.MyStockAccount') }}</p>
      </div>
    </div>
    <el-row>
      <p style="font-size: 20px;color: #22C1E6;margin: 90px 0 0 200px">{{ $t('lbs.SettingSettlementAccount') }}</p>
      <el-col :span="24" style="text-align: center;margin-top: 30px">
        <div>
          <span
            style="color: #707070;font-size: 18px;margin-right: 40px;display: inline-block;width: 200px;text-align: left"
          >{{ $t('lbs.StockAccount') }}</span>
          <el-select v-model="stockAccountNumber" style="width: 340px" @change="getStockSettingAccount()">
            <el-option v-for="(item,index) in stockAccountOption" :key="index" :label="item.label" :value="item.value" />
          </el-select>
        </div>
        <div style="margin-top: 40px">
          <span
            style="color: #707070;font-size: 18px;margin-right: 40px;display: inline-block;width: 200px;text-align: left"
          >{{ $t('lbs.settlementAccount') }}</span>
          <el-select v-model="settlementAccount" style="width: 340px" @change="setSettingAccount()">
            <el-option
              v-for="(item,index) in settlementAccountOption"
              :key="index"
              :label="item.label.split('HK')[0]+(item.label.split('HK')[1]?item.label.split('HK')[1].substr(6,15):'')"
              :value="item.value"
            />
          </el-select>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import axios from 'axios'

export default {
  data: () => ({
    token: null,
    loading: false,
    stockAccountNumber: '',
    stockAccountOption: [],
    settlementAccount: '',
    settlementAccountOption: [],
    customernumber: ''
  }),
  created() {
    this.customernumber = window.sessionStorage.getItem('targetCustomerNumber')
    this.token = window.sessionStorage.getItem('token')
    this.getStockAccountOption()
    this.getSettingAccountOption()
    this.getStockSettingAccount()
  },
  methods: {
    // 获取股票账户设置的结算账户
    getStockSettingAccount() {
      const _this = this
      if (!_this.stockAccountNumber) return
      _this.loading = true
      axios.post(`${_this.LBSGateway}/deposit-experience/deposit/account/accountDetails/${_this.stockAccountNumber}`, {}, { headers: { token: _this.token, customerNumber: _this.customernumber }})
        .then(response => {
          _this.loading = false
          if (response.data.code === '200') {
            _this.settlementAccount = response.data.data.account.relaccountnumber
          } else {
            _this.$message.error(response.data.msg, _this.$t('lbs.common.error'))
          }
        })
        .catch(error => {
          _this.loading = false
          _this.$message.error(error.message, _this.$t('lbs.common.error'))
        })
    },
    // 获取状态为 A 的股票账户列表
    getStockAccountOption() {
      const stockAccountOption = JSON.parse(window.sessionStorage.getItem('stockaccountlist'))
      for (const item of stockAccountOption) {
        if (item.accountStatus === 'A') {
          this.stockAccountOption.push(
            {
              label: item.accountNumber,
              value: item.accountNumber
            }
          )
        }
      }
      if (this.stockAccountOption && this.stockAccountOption[0]) {
        this.stockAccountNumber = this.stockAccountOption[0].value
      }
    },
    // 获取结算账户列表(current / saving | Status: A)
    getSettingAccountOption() {
      const savingList = JSON.parse(window.sessionStorage.getItem('savingaccountlist'))
      const currentList = JSON.parse(window.sessionStorage.getItem('currentaccountlist'))
      for (const item of savingList) {
        if (item.accountStatus === 'A') {
          this.settlementAccountOption.push(
            {
              label: `Saving Account ${item.accountNumber}`,
              value: item.accountNumber
            }
          )
        }
      }
      for (const item of currentList) {
        if (item.accountStatus === 'A') {
          this.settlementAccountOption.push(
            {
              label: `Current Account ${item.accountNumber}`,
              value: item.accountNumber
            }
          )
        }
      }
    },
    // 设置结算账户
    setSettingAccount() {
      const _this = this
      if (!_this.stockAccountNumber) return
      _this.loading = true
      const requestData = { stkaccountnumber: _this.stockAccountNumber, newsettleaccountnumber: _this.settlementAccount }
      axios.post(`${_this.LBSGateway}/stock-experience/stock/settlementAccountUpdate`, requestData, { headers: { token: _this.token, customerNumber: _this.customernumber }})
        .then(response => {
          _this.loading = false
          if (response.data.code === '200') {
            _this.$message.success(_this.$t('lbs.settlementTip'), _this.$t('lbs.common.success'))
          } else {
            _this.$message.error(response.data.msg, _this.$t('lbs.common.error'))
          }
        })
        .catch(error => {
          _this.loading = false
          _this.$message.error(error.message, _this.$t('lbs.common.error'))
        })
    },
    goToLink(path) {
      if (path === undefined) {
        this.$router.push({ path: `/tellerlbsinvestment/stocktrading` })
      } else {
        this.$router.push({ path: `/tellerlbsinvestment/stocktrading/${path}` })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
  .stock-account {
    padding: 50px 30px 30px;

    .lbs-icon {
      height: 35px;
      width: 35px;
    }

    .lbs-icon-item {
      width: 200px;
      text-align: center;
      display: inline-block;
    }

    .lbs-div-icon {
      width: 35px;
      margin: 0 auto;
      cursor: pointer;
    }

    .lbs-icon-group1 {
      width: 1020px;
      margin: 0 auto;
    }

    .lbs-icon-group2 {
      width: 816px;
      margin: 30px auto 0;
    }

    .lbs-icon-group1 .lbs-icon-item p {
      display: inline-block;
      cursor: pointer;
    }

    .lbs-icon-group1 .lbs-icon-item .lbs-div-icon.lbs-current {
      cursor: default;
    }

    .lbs-icon-group1 .lbs-icon-item p.lbs-current {
      cursor: default;
    }

    .lbs-icon-group2 .lbs-icon-item p {
      display: inline-block;
      cursor: pointer;
    }

    .lbs-icon-group2 .lbs-icon-item .lbs-div-icon.lbs-current {
      cursor: default;
    }

    .lbs-icon-group2 .lbs-icon-item p.lbs-current {
      cursor: default;
    }

  }
</style>
