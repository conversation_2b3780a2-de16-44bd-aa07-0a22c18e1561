<template>
  <div v-loading.fullscreen.lock="loading" class="stock-index">
    <el-col :span="24">
      <div class="lbs-icon-group1">
        <div class="lbs-icon-item">
          <div class="lbs-div-icon lbs-current">
            <svg-icon icon-class="Icon ionic-md-information-circle-outline" class="lbs-icon" />
          </div>
          <p class="lbs-current" style="color: #22C1E6">{{ $t('lbs.marketInformation') }}</p>
        </div>
        <div class="lbs-icon-item">
          <div class="lbs-div-icon" @click="goToLink('stockportfolio')">
            <svg-icon icon-class="Icon material-favorite-border" class="lbs-icon" />
          </div>
          <p @click="goToLink('stockportfolio')">{{ $t('lbs.stockPortfolio') }}</p>
        </div>
        <div class="lbs-icon-item">
          <div class="lbs-div-icon" @click="goToLink('stockholding')">
            <svg-icon icon-class="Icon awesome-wpforms" class="lbs-icon" />
          </div>
          <p @click="goToLink('stockholding')">{{ $t('lbs.StockHolding') }}</p>
        </div>
        <div class="lbs-icon-item">
          <div class="lbs-div-icon" @click="goToLink('transactionhistory')">
            <svg-icon icon-class="Icon awesome-history" class="lbs-icon" />
          </div>
          <p @click="goToLink('transactionhistory')">{{ $t('lbs.TransactionHistory') }}</p>
        </div>
        <div class="lbs-icon-item">
          <div class="lbs-div-icon" @click="goToLink('stockaccount')">
            <svg-icon icon-class="Icon material-person-pin" class="lbs-icon" />
          </div>
          <p @click="goToLink('stockaccount')">{{ $t('lbs.MyStockAccount') }}</p>
        </div>
      </div>
    </el-col>
    <el-col :span="8">
      <el-tooltip effect="dark" placement="right">
        <div slot="content" style="width: 300px">{{ $t('lbsTips.StockMarketInformationTips.Search') }}</div>
        <p class="lbs-title">{{ $t('lbs.common.search') }}</p>
      </el-tooltip>
      <div class="lbs-div-search">
        <p>{{ $t('lbs.stockCode') }}:</p>
        <el-tooltip effect="dark" placement="right">
          <div slot="content" style="width: 300px">{{ $t('lbsTips.StockMarketInformationTips.StockCode') }}</div>
          <el-input
            v-model="stockCode"
            class="lbs-input"
            placeholder=""
            :disabled="stockName.length > 0"
            clearable
          />
        </el-tooltip>
        <br>
        <div style="margin-top: 30px">
          <p>{{ $t('lbs.stockName') }}:</p>
          <el-tooltip effect="dark" placement="right">
            <div slot="content" style="width: 300px">{{ $t('lbsTips.StockMarketInformationTips.StockName') }}</div>
            <el-input
              v-model="stockName"
              :disabled="stockCode.length > 0"
              class="lbs-input"
              placeholder=""
              clearable
            />
          </el-tooltip>
        </div>
        <br>
        <!--        <p>{{$t('lbs.stockCategory')}}:</p>-->
        <!--        <el-tooltip effect="dark" placement="right">-->
        <!--          <div slot="content" style="width: 300px">{{$t('lbsTips.StockMarketInformationTips.StockCategory')}}</div>-->
        <!--          <el-select v-model="stockCategory" clearable placeholder="" class="lbs-input">-->
        <!--            <el-option-->
        <!--              v-for="item in stockCategoryList"-->
        <!--              :key="item.value"-->
        <!--              :label="item.label"-->
        <!--              :value="item.value">-->
        <!--            </el-option>-->
        <!--          </el-select>-->
        <!--        </el-tooltip>-->
        <!--        <p>{{$t('lbs.market')}}:</p>-->
        <!--        <el-tooltip effect="dark" placement="right">-->
        <!--          <div slot="content" style="width: 300px">{{$t('lbsTips.StockMarketInformationTips.Market')}}</div>-->
        <!--          <el-select v-model="market" clearable placeholder="" class="lbs-input">-->
        <!--            <el-option-->
        <!--              v-for="item in marketList"-->
        <!--              :key="item.value"-->
        <!--              :label="item.label"-->
        <!--              :value="item.value">-->
        <!--            </el-option>-->
        <!--          </el-select>-->
        <!--        </el-tooltip>-->
        <div style="width: 100%">
          <el-tooltip effect="dark" placement="bottom">
            <div slot="content" style="width: 100px;text-align: center">
              {{ $t('lbsTips.StockMarketInformationTips.OK') }}
            </div>
            <el-button size="mini" class="lbs-button" @click="search()">{{ $t('lbs.confirm') }}</el-button>
          </el-tooltip>
        </div>
      </div>
    </el-col>
    <el-col :span="14" :offset="2">
      <div class="lbs-real-time-news">
        <el-tooltip effect="dark" placement="right">
          <div slot="content" style="width: 300px;text-align: center">
            {{ $t('lbsTips.StockMarketInformationTips.RealTimeNews') }}
          </div>
          <p class="lbs-title">{{ $t('lbs.realTimeNews') }}</p>
        </el-tooltip>
        <div style="padding: 0 20px 40px;background-color: #FFF;position: relative">
          <el-table
            :data="realTimeNews"
            style="width: 100%"
          >
            <el-table-column
              prop="time"
            />
            <el-table-column
              prop="type"
            />
            <el-table-column
              prop="name"
            />
          </el-table>
          <el-tooltip effect="dark" placement="bottom">
            <div slot="content" style="width: 300px;text-align: center">
              {{ $t('lbsTips.StockMarketInformationTips.More') }}
            </div>
            <p
              style="text-decoration: underline;position: absolute;display: inline-block;cursor: pointer;right: 20px;bottom: 0"
            >
              {{ $t('lbs.common.more') }}</p>
          </el-tooltip>
        </div>
      </div>
    </el-col>
    <el-col :span="9">
      <el-tooltip effect="dark" placement="right">
        <div slot="content" style="width: 300px">{{ $t('lbsTips.StockMarketInformationTips.RecentlyViewed') }}</div>
        <p class="lbs-title">{{ $t('lbs.recentlyViewed') }}</p>
      </el-tooltip>
      <div class="recently-viewed">
        <table style="width: 100%;">
          <tr style="text-align: center">
            <td style="border-bottom: 1px solid #ccc;border-right: 1px solid #ccc">
              <el-tooltip effect="dark" placement="top">
                <div slot="content" style="width: 300px">{{ $t('lbsTips.StockMarketInformationTips.Code') }}</div>
                <p>{{ $t('lbs.stockCode') }}</p>
              </el-tooltip>
            </td>
            <td style="border-bottom: 1px solid #ccc;border-right: 1px solid #ccc">
              <el-tooltip effect="dark" placement="top">
                <div slot="content" style="width: 300px">{{ $t('lbsTips.StockMarketInformationTips.ClosingPrice') }}</div>
                <p>{{ $t('lbs.closingPrice') }}</p>
              </el-tooltip>
            </td>
            <td style="border-bottom: 1px solid #ccc;border-right: 1px solid #ccc">
              <el-tooltip effect="dark" placement="top">
                <div slot="content" style="width: 300px">{{ $t('lbsTips.StockMarketInformationTips.Change') }}</div>
                <p>{{ $t('lbs.change') }}</p>
              </el-tooltip>
            </td>
            <td style="border-bottom: 1px solid #ccc;">
              <el-tooltip effect="dark" placement="top">
                <div slot="content" style="width: 300px">{{ $t('lbsTips.StockMarketInformationTips.Change8') }}</div>
                <p>{{ $t('lbs.change2') }}</p>
              </el-tooltip>
            </td>
          </tr>
          <tr style="text-align: center">
            <td style="border-right: 1px solid #ccc;">
              <p class="text-top">0005.HK</p>
              <p class="text-bottom">HSBC</p>
            </td>
            <td style="border-right: 1px solid #ccc;">
              <p>36.70</p>
            </td>
            <td style="border-right: 1px solid #ccc;">
              <p>- 0.30</p>
            </td>
            <td>
              <p>- 0.81%</p>
            </td>
          </tr>
          <tr style="text-align: center">
            <td style="border-right: 1px solid #ccc;">
              <p class="text-top">AMZN</p>
              <p class="text-bottom">Amazon</p>
            </td>
            <td style="border-right: 1px solid #ccc;">
              <p>2,692.87</p>
            </td>
            <td style="border-right: 1px solid #ccc;">
              <p>- 61.71</p>
            </td>
            <td>
              <p>- 2.24%</p>
            </td>
          </tr>
          <tr style="text-align: center">
            <td style="border-right: 1px solid #ccc;">
              <p class="text-top">^GSPC</p>
              <p class="text-bottom">S&P 500</p>
            </td>
            <td style="border-right: 1px solid #ccc;">
              <p>3,009.05</p>
            </td>
            <td style="border-right: 1px solid #ccc;">
              <p>- 74.71</p>
            </td>
            <td>
              <p>- 2.42%</p>
            </td>
          </tr>
          <tr>
            <td colspan="4">
              <el-tooltip effect="dark" placement="bottom">
                <div slot="content" style="width: 300px;text-align: center">
                  {{ $t('lbsTips.StockMarketInformationTips.More') }}
                </div>
                <p
                  style="text-decoration: underline;display: inline-block;float: right;margin-right: 25px;cursor: pointer"
                >
                  {{ $t('lbs.common.more') }}</p>
              </el-tooltip>
            </td>
          </tr>
        </table>
      </div>
      <div class="lbs-div-bottom">
        <el-tooltip effect="dark" placement="top">
          <div slot="content" style="width: 300px">{{ $t('lbsTips.StockMarketInformationTips.HSI') }}</div>
          <p
            style="width: 100px;text-align: center;font-size: 20px;border-right: 1px solid #707070;display: inline-block;line-height: 35px"
          >
            HSI</p>
        </el-tooltip>
        <el-tooltip effect="dark" placement="top">
          <div slot="content" style="width: 300px">{{ $t('lbsTips.StockMarketInformationTips.HSCEI') }}</div>
          <p
            style="width: 100px;text-align: center;font-size: 20px;border-right: 1px solid #707070;display: inline-block;line-height: 35px"
          >
            HSCEI</p>
        </el-tooltip>
        <el-tooltip effect="dark" placement="top">
          <div slot="content" style="width: 300px">{{ $t('lbsTips.StockMarketInformationTips.HSMI') }}</div>
          <p style="width: 100px;text-align: center;font-size: 20px;display: inline-block">HSMI</p>
        </el-tooltip>
        <div>
          <div style="width: 100px;height: 150px;background-color: #fff;float: left;margin: 25px 0 0 0">
            <p style="font-size: 18px;width: 100%;text-align: center;margin-top: 10px">HSI</p>
            <p style="font-size: 18px;width: 100%;text-align: center">24,846.88</p>
            <p style="color: red;width: 100%;text-align: center">-60.46</p>
            <p style="color: red;width: 100%;text-align: center">-0.24%</p>
          </div>
          <div style="float: right">
            <svg-icon icon-class="line" style="width: 300px;height: 200px;" />
          </div>
        </div>
      </div>
    </el-col>
    <el-col :span="15">
      <div class="top5" style="padding: 40px 0 0 70px">
        <el-tooltip effect="dark" placement="top">
          <div slot="content" style="width: 300px">{{ $t('lbsTips.StockMarketInformationTips.Top58GainersStocks') }}</div>
          <div
            style="border: 1px solid #ccc;width: 49.5%;display: inline-block;margin-bottom: 5px;background-color: #fff"
          >
            <p class="lbs-table-title">{{ $t('lbs.Gainers5') }}</p>
            <table style="width: 100%;text-align: center">
              <tr v-for="(item, index) in gainersTop5" :key="index">
                <td style="padding-left: 10px"><p>{{ index + 1 }}</p></td>
                <td><p>{{ item.stockname }}</p></td>
                <td v-if="item.changed.slice(0,1) === '-'">
                  <p style="color: red">{{ item.changed }}</p>
                </td>
                <td v-else>
                  <p style="color: green">{{ item.changed }}</p>
                </td>
                <td v-if="item.changedpercent.slice(0,1) === '-'">
                  <p style="color: red">{{ item.changedpercent }}</p>
                </td>
                <td v-else>
                  <p style="color: green">{{ item.changedpercent }}</p>
                </td>
              </tr>
              <tr>
                <td colspan="4">
                  <el-tooltip effect="dark" placement="bottom">
                    <div slot="content" style="width: 300px;text-align: center">
                      {{ $t('lbsTips.StockMarketInformationTips.More') }}
                    </div>
                    <p
                      style="text-decoration: underline;display: inline-block;float: right;margin-right: 25px;cursor: pointer"
                    >
                      {{ $t('lbs.common.more') }}</p>
                  </el-tooltip>
                </td>
              </tr>
            </table>
          </div>
        </el-tooltip>
        <el-tooltip effect="dark" placement="top">
          <div slot="content" style="width: 300px">{{ $t('lbsTips.StockMarketInformationTips.Top58LosersStocks') }}</div>
          <div style="border: 1px solid #ccc;width: 49.5%;display: inline-block;background-color: #fff">
            <p class="lbs-table-title">{{ $t('lbs.Losers5') }}</p>
            <table style="width: 100%;text-align: center">
              <tr v-for="(item, index) in losersTop5" :key="index">
                <td style="padding-left: 10px"><p>{{ index + 1 }}</p></td>
                <td><p>{{ item.stockname }}</p></td>
                <td v-if="item.changed.slice(0,1) === '-'">
                  <p style="color: red">{{ item.changed }}</p>
                </td>
                <td v-else>
                  <p style="color: green">{{ item.changed }}</p>
                </td>
                <td v-if="item.changedpercent.slice(0,1) === '-'">
                  <p style="color: red">{{ item.changedpercent }}</p>
                </td>
                <td v-else>
                  <p style="color: green">{{ item.changedpercent }}</p>
                </td>
              </tr>
              <tr>
                <td colspan="4">
                  <el-tooltip effect="dark" placement="bottom">
                    <div slot="content" style="width: 300px;text-align: center">
                      {{ $t('lbsTips.StockMarketInformationTips.More') }}
                    </div>
                    <p
                      style="text-decoration: underline;display: inline-block;float: right;margin-right: 25px;cursor: pointer"
                    >
                      {{ $t('lbs.common.more') }}</p>
                  </el-tooltip>
                </td>
              </tr>
            </table>
          </div>
        </el-tooltip>
        <el-tooltip effect="dark" placement="top">
          <div slot="content" style="width: 300px">{{ $t('lbsTips.StockMarketInformationTips.Top58TurnoverStocks') }}
          </div>
          <div style="border: 1px solid #ccc;width: 49.5%;display: inline-block;background-color: #fff">
            <p class="lbs-table-title">{{ $t('lbs.Stocks5') }}</p>
            <table style="width: 100%;text-align: center">
              <tr v-for="(item, index) in turnoverStocks" :key="index">
                <td style="padding-left: 10px"><p>{{ index + 1 }}</p></td>
                <td><p>{{ item.stockname }}</p></td>
                <td v-if="item.changed.slice(0,1) === '-'">
                  <p style="color: red">{{ item.changed }}</p>
                </td>
                <td v-else>
                  <p style="color: green">{{ item.changed }}</p>
                </td>
                <td v-if="item.changedpercent.slice(0,1) === '-'">
                  <p style="color: red">{{ item.changedpercent }}</p>
                </td>
                <td v-else>
                  <p style="color: green">{{ item.changedpercent }}</p>
                </td>
              </tr>
              <tr>
                <td colspan="4">
                  <el-tooltip effect="dark" placement="bottom">
                    <div slot="content" style="width: 300px;text-align: center">
                      {{ $t('lbsTips.StockMarketInformationTips.More') }}
                    </div>
                    <p
                      style="text-decoration: underline;display: inline-block;float: right;margin-right: 25px;cursor: pointer"
                    >
                      {{ $t('lbs.common.more') }}</p>
                  </el-tooltip>
                </td>
              </tr>
            </table>
          </div>
        </el-tooltip>
      </div>
    </el-col>
    <el-col :span="24">
      <el-tooltip effect="dark" placement="right">
        <div slot="content" style="width: 300px;">
          {{ $t('lbsTips.StockMarketInformationTips.MarketInformation') }}
        </div>
        <p class="lbs-table-title" style="font-size: 25px;color: #22C1E6;margin-top: 30px;display: inline-block">
          {{ $t('lbs.marketInformation') }}</p>
      </el-tooltip>
      <div style="padding: 40px;background-color: #fff;margin-top: 30px">
        <el-table
          height="500"
          :data="tableData"
          style="width: 100%;"
        >
          <el-table-column
            prop="stockcode"
            sortable
            :label="$t('lbs.stockCode')"
          />
          <el-table-column
            prop="stockname"
            sortable
            :label="$t('lbs.stockName')"
          />
          <el-table-column
            sortable
            prop="changed"
            :label="$t('lbs.change')"
          />
          <el-table-column
            sortable
            prop="changedpercent"
            :label="$t('lbs.change2')"
          />
          <el-table-column
            :label="$t('lbs.operation')"
          >
            <template slot-scope="scope">
              <el-button
                size="mini"
                @click="viewStockDetail(scope.$index, scope.row)"
              >{{ $t('lbs.view') }}
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-col>
  </div>
</template>

<script>
export default {
  data() {
    return {
      token: null,
      loading: false,
      stockCode: '',
      stockName: '',
      stockCategory: '',
      market: '',
      stockCategoryList: [],
      marketList: [],
      realTimeNews: [
        {
          time: '11 : 42',
          type: 'HOT STOCKS',
          name: 'MAPLELEAF EDM quoted ...'
        },
        {
          time: '11 : 37 ',
          type: 'I-BANK FOCUS',
          name: 'JP Morgan lifts ENN ...'
        },
        {
          time: '10 : 32',
          type: 'BGMC (01693)',
          name: 'sees increase in interim ...'
        },
        {
          time: '10 : 20',
          type: 'BGMC (01693)',
          name: 'sees increase in interim ...'
        }
      ],
      gainersTop5: [],
      losersTop5: [],
      turnoverStocks: [],
      turnoverWarrents: [],
      stocklist: [],
      tableData: [],
      customernumber: ''
    }
  },
  mounted() {
    this.customernumber = window.sessionStorage.getItem('targetCustomerNumber')
    if (window.sessionStorage.getItem('showTips') === 'true' && window.sessionStorage.getItem('token')) {
      this.$alert(this.$t('lbs.popupStockMarketInformation'), this.$t('lbs.Tip'), {
        dangerouslyUseHTMLString: true
      })
    }
    this.token = window.sessionStorage.getItem('token')
    this.getStockList()

    // -----------------------------------------------------
    // const params = {
    //   platform: 'Virtual banking system',
    //   userId: window.sessionStorage.getItem('role'),
    //   operationType: '虚拟银行主管角色投资菜单股票交易'
    // }
    // this.$store.dispatch('app/VisitsFun', params).then((res) => {
    //   this.loading = false
    //   if (res.data.code !== 20000) {
    //     console.log(res.data.message)
    //   }
    // }).catch(error => {
    //   this.loading = false
    //   console.log(error.message)
    // })
    // -----------------------------------------
  },
  methods: {
    getStockList() {
      const vue = this
      this.$mui.ajax(vue.LBSGateway + '/stock-experience/stock/stockList', {
        data: {
          'index': 0,
          'items': 9999
        },
        dataType: 'json',
        type: 'post',
        timeout: 60000,
        headers: {
          accept: '*/*',
          token: vue.token,
          clientid: vue.$parent.clientid,
          messageid: vue.$parent.messageid,
          'Content-Type': 'application/json',
          customerNumber: vue.customernumber
        },
        beforeSend: function() {
          vue.loading = true
        },
        complete: function() {
          vue.loading = false
        },
        success: function(data) {
          if (data.code === '200') {
            var response = data.data
            vue.tableData = data.data

            for (let i = 0; i < vue.tableData.length; i++) {
              if (!vue.tableData[i].changedpercent) {
                vue.tableData[i].changedpercent = 'N/A'
              } else if (vue.tableData[i].changedpercent.slice(0, 1) !== '-') {
                vue.tableData[i].changedpercent = '+' + vue.tableData[i].changedpercent
              } else {
                vue.tableData[i].changedpercent = '-' + vue.tableData[i].changedpercent.slice(1)
              }
              if (!vue.tableData[i].changed) {
                vue.tableData[i].changed = 'N/A'
              } else if (vue.tableData[i].changed.slice(0, 1) !== '-') {
                vue.tableData[i].changed = vue.$lbs.decimal_format(vue.tableData[i].changed)
                vue.tableData[i].changed = '+' + vue.tableData[i].changed
              } else {
                vue.tableData[i].changed = '-' + vue.$lbs.decimal_format(vue.tableData[i].changed.slice(1))
              }
            }

            var losers = []
            var gainers = []
            for (var i = 0; i < response.length; i++) {
              if (response[i].changedpercent.slice(0, 1) === '-') {
                losers.push(response[i])
              } else {
                gainers.push(response[i])
              }
            }
            // 涨幅排序
            for (let i = gainers.length - 1; i > 0; i--) {
              for (let j = 0; j < i; j++) {
                if (Number(gainers[j].changedpercent) < Number(gainers[j + 1].changedpercent)) {
                  [gainers[j], gainers[j + 1]] = [gainers[j + 1], gainers[j]]
                }
              }
            }
            // 跌幅排序
            for (let i = losers.length - 1; i > 0; i--) {
              for (let j = 0; j < i; j++) {
                if (Number(losers[j].changedpercent.slice(1)) < Number(losers[j + 1].changedpercent.slice(1))) {
                  [losers[j], losers[j + 1]] = [losers[j + 1], losers[j]]
                }
              }
            }
            vue.gainersTop5 = gainers.slice(0, 5)
            vue.losersTop5 = losers.slice(0, 5)
            // Top 5 Turnover Stocks
            for (let i = 0; i < response.length; i++) {
              for (let j = 0; j < i; j++) {
                if (Number(response[j].turnover) < Number(response[j + 1].turnover)) {
                  [response[j], response[j + 1]] = [response[j + 1], response[j]]
                }
              }
            }
            vue.turnoverStocks = response.slice(0, 5)
            vue.setStockCodeList()
          } else if (data.code !== '404010') {
            vue.$mui.alert('Get Market Information failed! The response is: \n' + JSON.stringify(data.msg) + '.', 'Error', 'OK')
          }
        },
        error: function(xhr, type, errorThrown) {
          var msg = 'Get Market Information failed!'
          if (type === 'timeout') {
            msg = 'Get Market Information failed!. Time out!'
          } else {
            vue.$mui.alert(msg, 'Error', 'OK')
          }
        }
      })
    },
    viewStockDetail(index, row) {
      window.sessionStorage.setItem('stockCode', row.stockcode)
      this.$router.push({ path: '/tellerlbsinvestment/stocktrading/trade' })
    },
    goToLink(path) {
      this.$router.push({ path: `/tellerlbsinvestment/stocktrading/${path}` })
    },
    search() {
      // 根据股票名称搜索股票
      if (this.stockName.length > 0) {
        for (let i = 0; i < this.tableData.length; i++) {
          if (this.tableData[i].stockname === this.stockName) {
            window.sessionStorage.setItem('stockCode', this.tableData[i].stockcode)
            this.setStockCodeList()
            this.$router.push({ path: '/tellerlbsinvestment/stocktrading/trade' })
            return
          }
        }
      }
      // 根据股票代码搜索股票
      if (this.stockCode.length > 0) {
        for (let i = 0; i < this.tableData.length; i++) {
          if (this.tableData[i].stockcode === this.stockCode) {
            window.sessionStorage.setItem('stockCode', this.tableData[i].stockcode)
            this.setStockCodeList()
            this.$router.push({ path: '/tellerlbsinvestment/stocktrading/trade' })
            return
          }
        }
      }
      this.$message.warning(this.$t('lbs.StockSearchTip'))
    },
    setStockCodeList() {
      const stockCodeList = []
      for (let i = 0; i < this.tableData.length; i++) {
        stockCodeList.push(
          {
            stockCode: this.tableData[i].stockcode,
            stockName: this.tableData[i].stockname
          }
        )
      }
      window.sessionStorage.setItem('stockCodeList', JSON.stringify(stockCodeList))
    }
  }

}
</script>

<style scoped>
  .stock-index {
    padding: 50px 30px 30px;
  }

  .stock-index .lbs-icon {
    height: 35px;
    width: 35px;
  }

  .stock-index .lbs-icon-item {
    width: 200px;
    text-align: center;
    display: inline-block;
  }

  .stock-index .lbs-div-icon {
    width: 35px;
    margin: 0 auto;
    cursor: pointer;
  }

  .stock-index .lbs-icon-group1 {
    width: 1020px;
    margin: 0 auto;
  }

  .stock-index .lbs-icon-group2 {
    width: 816px;
    margin: 30px auto 0;
  }

  .stock-index .lbs-icon-group1 .lbs-icon-item p {
    display: inline-block;
    cursor: pointer;
  }

  .stock-index .lbs-icon-group1 .lbs-icon-item .lbs-div-icon.lbs-current {
    cursor: default;
  }

  .stock-index .lbs-icon-group1 .lbs-icon-item p.lbs-current {
    cursor: default;
  }

  .stock-index .lbs-icon-group2 .lbs-icon-item p {
    display: inline-block;
    cursor: pointer;
  }

  .stock-index .lbs-icon-group2 .lbs-icon-item .lbs-div-icon.lbs-current {
    cursor: default;
  }

  .stock-index .lbs-icon-group2 .lbs-icon-item p.lbs-current {
    cursor: default;
  }

  .stock-index .lbs-div-search {
    background-color: #ADF0FF;
    border-radius: 10px;
    width: 465px;
    margin: 0 auto;
    padding: 30px 30px 40px;
  }

  .stock-index .lbs-div-bottom {
    background-color: #ADF0FF;
    border-radius: 10px;
    padding: 0 30px 30px;
  }

  .stock-index .recently-viewed {
    background-color: #FFF;
    width: 465px;
    margin: 0 0 30px 30px;
    padding: 30px;
  }

  .stock-index .lbs-input {
    width: 240px;
  }

  .stock-index .lbs-div-search p {
    display: inline-block;
    width: 160px;
    font-size: 18px;
  }

  .stock-index .lbs-title {
    display: inline-block;
    margin: 30px 0 20px;
    font-size: 25px;
    color: #22C1E6;
  }

  .stock-index .lbs-button {
    width: 20%;
    float: right;
  }

  .stock-index .lbs-real-time-news {
    /*padding-left: 150px;*/
  }

  .stock-index table p {
    font-size: 15px;
    color: #707070;
    margin: 10px 0 10px;
    line-height: 1;
  }

  .stock-index table tr td p.text-bottom {
    font-size: 10px;
    color: #707070;
    margin: 0 0 20px;
    line-height: 1;
  }

  .stock-index table tr td p.text-top {
    margin-top: 20px;
    margin-bottom: 0;
  }

  .stock-index .lbs-div-bottom {
    height: 260px;
  }

  .stock-index .top5 .lbs-table-title {
    color: #333333;
    font-size: 20px;
    border-bottom: 1px solid #cccccc;
    line-height: 45px;
    padding-left: 10px;
  }

  .stock-index .top5 p {
    line-height: 25px;
  }
</style>

<style>
  .stock-index .el-input__inner {
    margin-bottom: 10px;
  }
</style>
