<template>
  <div v-loading.fullscreen.lock="loading" class="stockholding">
    <el-col :span="24">
      <div class="lbs-icon-group1">
        <div class="lbs-icon-item">
          <div class="lbs-div-icon" @click="goToLink()">
            <svg-icon icon-class="ionic-md-information-circle-outline" class="lbs-icon" />
          </div>
          <p @click="goToLink()">{{ $t('lbs.marketInformation') }}</p>
        </div>
        <div class="lbs-icon-item">
          <div class="lbs-div-icon" @click="goToLink('stockportfolio')">
            <svg-icon icon-class="Icon material-favorite-border" class="lbs-icon" />
          </div>
          <p @click="goToLink('stockportfolio')">{{ $t('lbs.stockPortfolio') }}</p>
        </div>
        <div class="lbs-icon-item">
          <div class="lbs-div-icon lbs-current">
            <svg-icon icon-class="awesome-wpforms" class="lbs-icon" />
          </div>
          <p class="lbs-current" style="color: #22C1E6">{{ $t('lbs.StockHolding') }}</p>
        </div>
        <div class="lbs-icon-item">
          <div class="lbs-div-icon" @click="goToLink('transactionhistory')">
            <svg-icon icon-class="Icon awesome-history" class="lbs-icon" />
          </div>
          <p @click="goToLink('transactionhistory')">{{ $t('lbs.TransactionHistory') }}</p>
        </div>
        <div class="lbs-icon-item">
          <div class="lbs-div-icon" @click="goToLink('stockaccount')">
            <svg-icon icon-class="Icon material-person-pin" class="lbs-icon" />
          </div>
          <p @click="goToLink('stockaccount')">{{ $t('lbs.MyStockAccount') }}</p>
        </div>
      </div>
    </el-col>
    <el-col :span="24">
      <div class="title-header">
        <div style="display: inline-block">
          <p class="holding-title">{{ $t('lbs.TotalMarketValue') }}</p>
          <p v-if="stockHoldingInformation === null" class="holding-value">--</p>
          <p v-else class="holding-value">{{ stockHoldingInformation.totalmarkervalue }}</p>
        </div>
        <div style="display: inline-block">
          <p class="holding-title">{{ $t('lbs.TotalNetGainLoss') }}</p>
          <p v-if="stockHoldingInformation === null" class="holding-value">--</p>
          <p
            v-else-if="stockHoldingInformation.totalNetGainLoss.toString().substring(0,1) === '-'"
            class="holding-value"
            style="color: red"
          >
            {{ stockHoldingInformation.totalNetGainLoss }}</p>
          <p v-else class="holding-value" style="color: green">
            {{ stockHoldingInformation.totalNetGainLoss }}</p>
        </div>
      </div>
      <el-card>
        <template>
          <span style="margin-right: 20px">{{ $t('lbs.StockAccount') }}</span>
          <el-select v-model="stockAccountNumber" style="width: 270px" @change="getStockHolding()">
            <el-option v-for="(item,index) in stockAccountOption" :key="index" :label="item.label" :value="item.value" />
          </el-select>
          <el-table
            :data="tableData"
            style="width: 100%"
            height="400px"
          >
            <el-table-column
              prop="stockname"
              :label="$t('lbs.stockName')"
            />
            <el-table-column
              prop="stockcode"
              :label="$t('lbs.stockCode')"
            />
            <el-table-column
              prop="marketprice"
              :label="$t('lbs.marketPrice')"
            />
            <el-table-column
              prop="sharesholdingno"
              :label="$t('lbs.NoOfShares')"
            />
            <el-table-column
              prop="averageprice"
              :label="$t('lbs.averagePrice')"
            />
            <el-table-column
              prop="availableshare"
              :label="$t('lbs.AvailableNo')"
            />
            <el-table-column
              prop="marketvalue"
              :label="$t('lbs.MarketValue')"
            />
            <el-table-column
              prop="netGainLoss"
              :label="$t('lbs.NetGainLoss')"
            />
            <el-table-column
              prop="netGainLossPct"
              :label="$t('lbs.NetGainLossPct')"
            />
            <el-table-column
              :label="$t('lbs.operation')"
              width="90px"
            >
              <template slot-scope="scope">
                <el-button v-if="scope.row.availableshare > 0" size="mini" @click="sellStock(scope.row)">
                  {{ $t('lbs.sell') }}
                </el-button>
                <el-button v-else size="mini" disabled>{{ $t('lbs.sell') }}</el-button>
              </template>
            </el-table-column>
          </el-table>
        </template>
      </el-card>
    </el-col>
  </div>
</template>

<script>
export default {
  data() {
    return {
      token: null,
      loading: false,
      stockAccount: null,
      stockHoldingInformation: null,
      tableData: [],
      stockAccountNumber: '',
      stockAccountOption: [],
      customernumber: ''
    }
  },
  created() {
    this.customernumber = window.sessionStorage.getItem('targetCustomerNumber')
    this.token = window.sessionStorage.getItem('token')
    this.stockAccount = JSON.parse(window.sessionStorage.getItem('stockAccount'))
    this.getStockAccountOption()
    this.getStockHolding()
  },
  methods: {
    getStockAccountOption() {
      const stockAccountOption = JSON.parse(window.sessionStorage.getItem('stockaccountlist'))
      for (const item of stockAccountOption) {
        if (item.accountStatus === 'A') {
          this.stockAccountOption.push(
            {
              label: item.accountNumber,
              value: item.accountNumber
            }
          )
        }
      }
      if (this.stockAccountOption && this.stockAccountOption[0]) {
        this.stockAccountNumber = this.stockAccountOption[0].value
      }
    },
    // 获取总的持股信息
    getStockHolding() {
      const vue = this
      if (!vue.stockAccountNumber) return
      const requestdata = {
        'stkaccountnumber': vue.stockAccountNumber
      }
      vue.$mui.ajax(this.LBSGateway + '/stock-experience/stock/stockHoldingEnquiry', {
        data: requestdata,
        dataType: 'json',
        type: 'post',
        timeout: 60000,
        headers: {
          'accept': '*/*',
          'token': vue.token,
          'Content-Type': 'application/json',
          'customerNumber': vue.customernumber
        },
        beforeSend: function() {
          vue.loading = true
        },
        complete: function() {
          vue.loading = false
        },
        success: function(response) {
          vue.tableData = []
          if (response.code === '200') {
            vue.stockHoldingInformation = response.data
            console.log(vue.stockHoldingInformation)
            vue.tableData = response.data.stkholdlist
          } else if (response.code === '404015') {
            vue.$mui.alert(vue.$t('lbs.NoStockHolding'), vue.$t('lbs.common.success'), vue.$t('lbs.common.confirm'))
          } else {
            vue.$mui.alert(vue.$t('lbs.GetStockHoldingFail'), vue.$t('lbs.common.error'), vue.$t('lbs.common.confirm'))
          }
        },
        error: function(xhr, type, errorThrown) {
          console.log(type)
          let msg = 'Get stock holding information failed! The response is: \n' + xhr.responseText + '.'
          if (type === 'timeout') {
            msg = 'Get stock holding information failed. Time out!'
          }
          vue.$mui.alert(msg, 'Error', 'OK')
        }
      })
    },
    goToLink(path) {
      if (path === undefined) {
        this.$router.push({ path: `/tellerlbsinvestment/stocktrading` })
      } else {
        this.$router.push({ path: `/tellerlbsinvestment/stocktrading/${path}` })
      }
    },
    sellStock(row) {
      window.sessionStorage.setItem('stockCode', row.stockcode)
      this.$router.push({ path: '/tellerlbsinvestment/stocktrading/trade' })
    }
  }
}
</script>

<style scoped>
  .stockholding {
    padding: 50px 30px 30px;
  }

  .stockholding .lbs-icon {
    height: 35px;
    width: 35px;
  }

  .stockholding .lbs-icon-item {
    width: 200px;
    text-align: center;
    display: inline-block;
  }

  .stockholding .lbs-div-icon {
    width: 35px;
    margin: 0 auto;
    cursor: pointer;
  }

  .stockholding .lbs-icon-group1 {
    width: 1020px;
    margin: 0 auto;
  }

  .stockholding .lbs-icon-group2 {
    width: 816px;
    margin: 30px auto 0;
  }

  .stockholding .lbs-icon-group1 .lbs-icon-item p {
    display: inline-block;
    cursor: pointer;
  }

  .stockholding .lbs-icon-group1 .lbs-icon-item .lbs-div-icon.lbs-current {
    cursor: default;
  }

  .stockholding .lbs-icon-group1 .lbs-icon-item p.lbs-current {
    cursor: default;
  }

  .stockholding .lbs-icon-group2 .lbs-icon-item p {
    display: inline-block;
    cursor: pointer;
  }

  .stockholding .lbs-icon-group2 .lbs-icon-item .lbs-div-icon.lbs-current {
    cursor: default;
  }

  .stockholding .lbs-icon-group2 .lbs-icon-item p.lbs-current {
    cursor: default;
  }

  .stockholding .holding-title {
    font-size: 18px;
    width: 500px;
    text-align: center;
    margin-bottom: 0;
  }

  .stockholding .holding-value {
    color: #707070;
    font-size: 15px;
    width: 500px;
    text-align: center;
  }

  .stockholding .title-header {
    width: 1010px;
    margin: 30px auto 20px;
  }
</style>
