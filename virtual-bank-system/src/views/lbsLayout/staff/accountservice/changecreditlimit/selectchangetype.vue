<template xmlns:el-col="http://www.w3.org/1999/html">
  <el-row class="selectchangetype">
    <el-col :span="24">
      <p style="font-size: 23px;line-height: 50px">{{ $t('lbs.changeLimitTip5') }}</p>
      <hr style="margin-left: 0">
      <p style="color: #707070;font-size: 25px;line-height: 50px">{{ $t("lbs.youSelected") }}</p>
      <div style="width: 100%;height: 80px;border: 1px #22C1E6 solid">
        <el-col :span="8">
          <p style="margin: 0;font-size: 20px;text-align: center;line-height: 80px">{{ $t("lbs.visaCard") }}<span
            style="font-size: 17px"
          >{{ changeLimitAccount.accountNumber }}</span></p>
        </el-col>
        <el-col :span="8">
          <p style="margin: 0;font-size: 20px;text-align: center;line-height: 80px"><PERSON></p>
        </el-col>
        <el-col :span="8">
          <p style="margin: 0;font-size: 17px;text-align: center;line-height: 80px">{{ $t('lbs.existingLimit') }}
            {{ changeLimitAccount.ccyCode + ' ' + $lbs.decimal_format(changeLimitAccount.approvedLimit) }}</p>
        </el-col>
      </div>
      <p style="color: #22C1E6;margin: 20px 0 20px;font-size: 30px">{{ $t("lbs.changeLimit") }}</p>
      <el-tabs type="border-card" stretch>
        <el-tab-pane :label="$t('lbs.increase')">
          <el-row>
            <el-col :span="24">
              <div style="background-color: #E2E2E2;height: 45px;width: 808px;color: #000000;margin: 10px auto 0">
                <p style="font-size: 18px;line-height: 45px;text-align: center">
                  {{ $t('lbs.existingLimit') }}
                  {{ changeLimitAccount.ccyCode + ' ' + $lbs.decimal_format(changeLimitAccount.approvedLimit) }}</p>
              </div>
            </el-col>
          </el-row>
          <p style="font-size: 18px;line-height: 60px;margin-left: 20px;margin-bottom: 0">
            {{ $t('lbs.increaseLimitTip1') }}</p>
          <el-row>
            <el-col :span="24">
              <div style="padding: 20px 80px 20px">
                <p style="font-size: 16px;margin-bottom: 20px">{{ $t('lbs.increaseLimitTip2') }}</p>
                <el-row>
                  <el-col :span="12">
                    <p style="margin-bottom: 0;font-size: 16px">{{ $t('lbs.increaseLimitTip3') }}</p>
                    <p style="margin-bottom: 0;font-size: 12px">{{ $t('lbs.changeLimitTip6') }}</p>
                  </el-col>
                  <el-col :span="12">
                    <el-input v-model="increaseLimit" placeholder="HKD" style="width: 400px" />
                  </el-col>
                  <el-col :span="12">
                    <p style="margin-bottom: 0;font-size: 16px;line-height: 36px">{{ $t('lbs.increaseLimitTip5') }}</p>
                  </el-col>
                  <el-col :span="12" style="margin-bottom: 15px">
                    <el-select v-model="reason" :placeholder="$t('lbs.selectReason')" style="width: 400px">
                      <el-option label="Not Provided" value="NotProvided" />
                    </el-select>
                  </el-col>
                  <el-col :span="12">
                    <p style="margin-bottom: 0;font-size: 16px">{{ $t('lbs.increaseLimitTip6') }}</p>
                    <p style="margin-bottom: 0;font-size: 12px">{{ $t('lbs.changeLimitTip6') }}</p>
                  </el-col>
                  <el-col :span="12">
                    <el-input v-model="monthlyIncome" placeholder="HKD" style="width: 400px" />
                  </el-col>
                  <el-col :span="12">
                    <p style="margin-bottom: 0;font-size: 16px;line-height: 36px">{{ $t('lbs.increaseLimitTip7') }}</p>
                  </el-col>
                  <el-col :span="12">
                    <el-checkbox-group v-model="isCounter">
                      <el-checkbox name="type" label="" />
                      <p style="display: inline-block;margin-left: 30px">{{ $t('lbs.increaseLimitTip8') }}</p>
                    </el-checkbox-group>
                  </el-col>
                </el-row>
              </div>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <div style="width: 100%;background-color: #E2E2E2;padding: 15px;margin-top: 30px">
                <p style="color: #000000">{{ $t('lbs.changeLimitTip7') }}</p>
                <p style="color: #000000">{{ $t('lbs.changeLimitTip8') }}</p>
                <p style="color: #000000">{{ $t('lbs.changeLimitTip9') }}</p>
                <p style="margin-bottom: 30px;color: #000000;">{{ $t('lbs.changeLimitTip10') }}</p>
                <el-checkbox name="type" />
                <p style="display: inline-block;color: #000000;margin-left: 50px">{{ $t('lbs.changeLimitTip11') }}</p>
              </div>
            </el-col>
            <el-col :span="24">
              <div style="margin: 20px 20px 20px 0;float: right;width: 300px">
                <el-button type="danger" @click="increase('back')">{{ $t('lbs.cancel') }}</el-button>
                <el-button type="primary" @click="increase('continue')">{{ $t('lbs.agreeContinue') }}</el-button>
              </div>
            </el-col>
          </el-row>
        </el-tab-pane>
        <el-tab-pane :label="$t('lbs.decrease')">
          <el-row>
            <el-col :span="24">
              <div
                style="background-color: #E2E2E2;height: 90px;width: 808px;color: #000000;margin: 10px auto 0;padding: 20px"
              >
                <p style="font-size: 18px;text-align: center">{{ $t('lbs.existingLimit') }}
                  {{ changeLimitAccount.ccyCode + ' ' + $lbs.decimal_format(changeLimitAccount.approvedLimit) }}</p>
                <p style="font-size: 18px;text-align: center">{{ $t('lbs.decreaseLimitTip1') }}
                  {{ changeLimitAccount.ccyCode + ' ' + $lbs.decimal_format(changeLimitAccount.cashAdvanceLimit) }}</p>
              </div>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <div style="padding: 20px 80px 20px">
                <p style="font-size: 16px;margin-bottom: 20px">{{ $t('lbs.decreaseLimitTip2') }}</p>
                <el-row>
                  <el-col :span="12">
                    <p style="margin-bottom: 0;font-size: 16px">{{ $t('lbs.decreaseLimitTip3') }}</p>
                    <p style="margin-bottom: 0;font-size: 12px">{{ $t('lbs.changeLimitTip6') }}</p>
                  </el-col>
                  <el-col :span="12">
                    <el-input v-model="decreaseLimit" placeholder="HKD" style="width: 400px" />
                  </el-col>
                  <el-col :span="12">
                    <p style="margin-bottom: 0;font-size: 16px;line-height: 36px">{{ $t('lbs.decreaseLimitTip4') }}</p>
                  </el-col>
                  <el-col :span="12">
                    <el-select v-model="reason2" :placeholder="$t('lbs.selectReason')" style="width: 400px">
                      <el-option label="Not Provided" value="Not Provided" />
                    </el-select>
                  </el-col>
                </el-row>
              </div>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="24">
              <div style="width: 100%;background-color: #E2E2E2;padding: 15px;margin-top: 30px">
                <p style="color: #000000">{{ $t('lbs.changeLimitTip7') }}</p>
                <p style="color: #000000">{{ $t('lbs.changeLimitTip8') }}</p>
                <p style="color: #000000">{{ $t('lbs.changeLimitTip9') }}</p>
                <p style="margin-bottom: 30px;color: #000000;">{{ $t('lbs.changeLimitTip10') }}</p>
                <el-checkbox name="type" />
                <p style="display: inline-block;color: #000000;margin-left: 50px">{{ $t('lbs.changeLimitTip11') }}</p>
              </div>
            </el-col>
            <el-col :span="24">
              <div style="margin: 20px 20px 20px 0;float: right;width: 300px">
                <el-button type="danger" @click="decrease('back')">{{ $t('lbs.cancel') }}</el-button>
                <el-button type="primary" @click="decrease('continue')">{{ $t('lbs.agreeContinue') }}</el-button>
              </div>
            </el-col>
          </el-row>
        </el-tab-pane>
      </el-tabs>
    </el-col>
  </el-row>
</template>

<script>
export default {
  name: 'Selectchangetype',
  data() {
    return {
      token: null,
      changeLimitAccount: {},
      increaseLimit: null,
      decreaseLimit: null,
      monthlyIncome: null,
      reason: null,
      reason2: null,
      isCounter: []

    }
  },
  watch: {
    increaseLimit: {
      handler() {
        this.clearNoNum('increaseLimit')
      }
    },
    decreaseLimit: {
      handler() {
        this.clearNoNum('decreaseLimit')
      }
    },
    monthlyIncome: {
      handler() {
        this.clearNoNum('monthlyIncome')
      }
    }
  },
  mounted() {
    this.token = window.sessionStorage.getItem('token')
    this.changeLimitAccount = JSON.parse(window.sessionStorage.getItem('changeLimitAccount'))
    this.increaseLimit = this.changeLimitAccount.increaseLimit
    this.decreaseLimit = this.changeLimitAccount.decreaseLimit
    this.monthlyIncome = this.changeLimitAccount.monthlyIncome
    this.reason = this.changeLimitAccount.reason
    this.reason2 = this.changeLimitAccount.reason2
    this.changeLimitAccount.reason
    if (this.changeLimitAccount.isCounter) {
      this.isCounter = ['']
    }
  },
  methods: {
    increase(type) {
      if (type === 'back') {
        this.$router.push({ path: '/tellerlbsaccountservice/creditcarddetail' })
      } else {
        if (this.increaseLimit === '' || this.increaseLimit === undefined) {
          this.$mui.alert(this.$t('lbs.increaseTip1'), 'Error', 'OK')
        } else if (Number(this.increaseLimit) <= Number(this.changeLimitAccount.approvedLimit)) {
          this.$mui.alert(this.$t('lbs.increaseTip2'), 'Error', 'OK')
        } else if (Number(this.increaseLimit) % 1000 != 0) {
          this.$mui.alert(this.$t('lbs.increaseTip3'), 'Error', 'OK')
        } else if (this.reason === '' || this.reason === undefined) {
          this.$mui.alert(this.$t('lbs.increaseTip4'), 'Error', 'OK')
        } else if (this.monthlyIncome === '' || this.monthlyIncome === undefined) {
          this.$mui.alert(this.$t('lbs.increaseTip5'), 'Error', 'OK')
        } else if (Number(this.monthlyIncome) % 1000 != 0 || Number(this.monthlyIncome) <= 0) {
          this.$mui.alert(this.$t('lbs.increaseTip6'), 'Error', 'OK')
        } else {
          this.changeLimitAccount['increaseLimit'] = this.increaseLimit
          this.changeLimitAccount['reason'] = this.reason
          this.changeLimitAccount['monthlyIncome'] = this.monthlyIncome
          if (this.isCounter.length > 0) {
            this.changeLimitAccount['isCounter'] = true
          } else {
            this.changeLimitAccount['isCounter'] = false
          }
          window.sessionStorage.setItem('changeLimitAccount', JSON.stringify(this.changeLimitAccount))
          this.$router.push({ path: '/tellerlbsaccountservice/changecreditlimit/increaselimitform' })
        }
      }
    },
    decrease(type) {
      if (type === 'back') {
        this.$router.push({ path: '/tellerlbsaccountservice/creditcarddetail' })
      } else {
        if (this.decreaseLimit === '' || this.decreaseLimit === undefined) {
          this.$mui.alert(this.$t('lbs.decreaseLimitTip6'), 'Error', 'OK')
        } else if (Number(this.decreaseLimit) < Number(this.changeLimitAccount.cashAdvanceLimit)) {
          this.$mui.alert(this.$t('lbs.decreaseLimitTip7'), 'Error', 'OK')
        } else if (Number(this.decreaseLimit) % 1000 != 0) {
          this.$mui.alert(this.$t('lbs.decreaseLimitTip8'), 'Error', 'OK')
        } else if (this.reason2 === '' || this.reason2 === undefined) {
          this.$mui.alert(this.$t('lbs.decreaseLimitTip9'), 'Error', 'OK')
        } else {
          this.changeLimitAccount['decreaseLimit'] = this.decreaseLimit
          this.changeLimitAccount['reason2'] = this.reason2
          window.sessionStorage.setItem('changeLimitAccount', JSON.stringify(this.changeLimitAccount))
          this.$router.push({ path: '/tellerlbsaccountservice/changecreditlimit/decreaselimitform' })
        }
      }
    },
    clearNoNum(type) {
      if (type === 'increaseLimit' && this.increaseLimit !== undefined) {
        this.increaseLimit = this.increaseLimit.replace(/[^\d.]/g, '') // 清除"数字"和"."以外的字符
        this.increaseLimit = this.increaseLimit.replace(/^\./g, '') // 验证第一个字符是数字而不是字符
        this.increaseLimit = this.increaseLimit.replace(/\.{2,}/g, '.') // 只保留第一个.清除多余的
        this.increaseLimit = this.increaseLimit.replace('.', '$#$').replace(/\./g, '').replace('$#$', '.')
        this.increaseLimit = this.increaseLimit.replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3') // 只能输入两个小数
      } else if (type === 'decreaseLimit' && this.decreaseLimit !== undefined) {
        this.decreaseLimit = this.decreaseLimit.replace(/[^\d.]/g, '') // 清除"数字"和"."以外的字符
        this.decreaseLimit = this.decreaseLimit.replace(/^\./g, '') // 验证第一个字符是数字而不是字符
        this.decreaseLimit = this.decreaseLimit.replace(/\.{2,}/g, '.') // 只保留第一个.清除多余的
        this.decreaseLimit = this.decreaseLimit.replace('.', '$#$').replace(/\./g, '').replace('$#$', '.')
        this.decreaseLimit = this.decreaseLimit.replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3') // 只能输入两个小数
      } else if (type === 'monthlyIncome' && this.monthlyIncome !== undefined) {
        this.monthlyIncome = this.monthlyIncome.replace(/[^\d.]/g, '') // 清除"数字"和"."以外的字符
        this.monthlyIncome = this.monthlyIncome.replace(/^\./g, '') // 验证第一个字符是数字而不是字符
        this.monthlyIncome = this.monthlyIncome.replace(/\.{2,}/g, '.') // 只保留第一个.清除多余的
        this.monthlyIncome = this.monthlyIncome.replace('.', '$#$').replace(/\./g, '').replace('$#$', '.')
        this.monthlyIncome = this.monthlyIncome.replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3') // 只能输入两个小数
      }
    }
  }
}
</script>
<style>
  .selectchangetype .el-tabs--border-card > .el-tabs__content {
    padding: 0;
  }

  .selectchangetype .el-tabs--border-card > .el-tabs__header .el-tabs__item.is-active {
    font-size: 16px;
    color: #FFFFFF;
    background-color: #109eae;
    border-right-color: #DCDFE6;
    border-left-color: #DCDFE6;
    line-height: 50px;
    height: 50px;
  }

  .selectchangetype .el-tabs--border-card > .el-tabs__header .el-tabs__item {
    -webkit-transition: all .3s cubic-bezier(.645, .045, .355, 1);
    transition: all .3s cubic-bezier(.645, .045, .355, 1);
    border: 1px solid transparent;
    margin-top: -1px;
    color: #909399;
    font-size: 16px;
    line-height: 50px;
    height: 50px;
  }

  .selectchangetype .el-input--medium .el-input__inner {
    height: 36px;
    color: #707070;
  }

  .selectchangetype .el-input__icon {
    height: 36px;
  }

  .el-popper[x-placement^=bottom] {
    margin-top: 0;
  }

  .selectchangetype .el-select-dropdown {
    position: absolute;
    z-index: 1001;
    border: 1px solid #E4E7ED;
    border-radius: 4px;
    background-color: #FFF;
    -webkit-box-shadow: 0 2px 12px 0 rgba(0, 0, 0, .1);
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, .1);
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    margin: 0;
  }
</style>
