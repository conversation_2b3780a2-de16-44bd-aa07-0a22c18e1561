<template>
  <div class="loss">
    <el-row>
      <el-col :span="24">
        <div class="title">
          <el-tooltip effect="dark" placement="bottom">
            <div slot="content" style="width: 350px">
              Loss reporting: This is designed to report a credit card loss.
            </div>
            <p class="max-title">Report lost card and request replacement</p>
          </el-tooltip>
        </div>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="24">
        <div class="main">
          <router-view/>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
  export default {
    name: "cardlossreporting"
  }
</script>

<style scoped>
  .loss .title {
    padding: 30px 28px;
    height: 140px;
    background-color: #FFFFFF;
    margin: 0 25px 0 25px;
    border-radius: 50px;
    background-image: url("../../components/title-bg.png");
    background-repeat: no-repeat;
    background-size: 574px 130px;
    background-position: right;
    box-shadow: 5px 5px 5px #bbbbbb;
  }

  .loss p {
    color: #707070;
    margin-bottom: 0;
    line-height: 1;
  }

  .loss .max-title {
    font-size: 50px;
    margin-bottom: 40px;
    margin-top: 15px;
    font-weight: 500;
    display: inline-block;
    color: #22C1E6;
  }

  .loss .little-title {
    font-size: 30px;
  }

  .loss .primary-little {
    color: #22C1E6;
    margin: 10px 0 20px;
  }

  .loss .main{
    padding: 30px;
  }
</style>
