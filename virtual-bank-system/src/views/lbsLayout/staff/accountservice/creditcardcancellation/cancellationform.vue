<template>
  <div v-loading.fullscreen.lock="loading" class="cancellationform">
    <el-row>
      <el-col :span="24">
        <div class="div-select-info">
          <el-row>
            <el-col :xs="24" :lg="8">
              <p class="p-select-info">
                {{ $t('lbs.visaCard') }}
                <span class="min-text">{{ cancellationAccount.accountNumber }}</span>
              </p>
            </el-col>
            <el-col :xs="24" :lg="8">
              <p class="p-select-info"><PERSON></p>
            </el-col>
            <el-col :xs="24" :lg="8">
              <p class="p-select-info min-text">
                {{ $t('lbs.existingLimit') }}
                {{ cancellationAccount.ccyCode + ' ' + $lbs.decimal_format(cancellationAccount.approvedLimit) }}
              </p>
            </el-col>
          </el-row>
        </div>
      </el-col>
    </el-row>
    <el-row>
      <div class="form-header">
        <el-col :span="12">
          <span class="text-form">{{ $t('lbs.youInformation') }}</span>
        </el-col>
        <el-col :span="10" :offset="2">
          <div class="edit" @click="submit('edit')">
            <u class="text-u">
              <span class="text-form">{{ $t('lbs.common.edit') }}</span>
            </u>
            <svg-icon class="lbs-icon" icon-class="awesome-edit" />
          </div>
        </el-col>
      </div>
      <el-col :span="10" :offset="2">
        <span class="text-form-item">{{ $t('lbs.whyCancel') }}</span>
      </el-col>
      <el-col :span="10" :offset="2">
        <span class="text-form-item">{{ cancellationAccount.reason }}</span>
      </el-col>
    </el-row>
    <el-col :span="24">
      <div class="clause">
        <p class="clause-p">{{ $t('lbs.item1') }}</p>
        <p class="clause-p">{{ $t('lbs.item2') }}
        </p>
        <el-checkbox-group v-model="isSelect">
          <el-checkbox name="type" />
          <p class="clause-p clause-ok">{{ $t('lbs.item3') }}
          </p>
        </el-checkbox-group>
      </div>
    </el-col>
    <el-row>
      <el-col :span="24">
        <div class="button-group">
          <el-button type="danger" @click="submit('back')">{{ $t('lbs.cancel') }}</el-button>
          <el-button type="primary" @click="submit('continue')">{{ $t('lbs.Continue') }}</el-button>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
export default {
  name: 'Cancellationform',
  data() {
    return {
      loading: false,
      token: null,
      cancellationAccount: {},
      isSelect: [],
      customernumber: ''
    }
  },
  mounted() {
    this.customernumber = window.sessionStorage.getItem('targetCustomerNumber')
    this.token = window.sessionStorage.getItem('token')
    this.cancellationAccount = JSON.parse(
      window.sessionStorage.getItem('cancellationAccount')
    )
  },
  methods: {
    submit(type) {
      if (type === 'back') {
        this.$router.push({ path: '/tellerlbsaccountservice/creditcarddetail' })
      } else if (type === 'edit') {
        this.$router.push({
          path: '/tellerlbsaccountservice/creditcardcancellation/cancellationinfo'
        })
      } else {
        if (this.isSelect.length === 1) {
          this.getaccountbalance()
        } else {
          this.$mui.alert(
            this.$t('lbs.item5'),
            'Error',
            'OK'
          )
        }
      }
    },
    getaccountbalance() {
      var vue = this
      var requestdata = {
        creditcardnumber: this.cancellationAccount.accountNumber
      }
      this.$mui.ajax(
        this.LBSGateway + '/creditcard-experience/creditcard/cancellation',
        {
          data: requestdata,
          dataType: 'json', // 服务器返回json格式数据
          type: 'post', // HTTP请求类型
          timeout: 60000,
          headers: {
            accept: '*/*',
            token: vue.token,
            clientid: vue.$parent.clientid,
            messageid: vue.$parent.messageid,
            'Content-Type': 'application/json',
            customerNumber: vue.customernumber
          },
          beforeSend: function() {
            vue.loading = true
          },
          complete: function() {
            vue.loading = false
          },
          success: function(data) {
            // mui.alert(JSON.stringify(data))
            if (data.code === '200') {
              vue.$router.push({
                path:
                  '/tellerlbsaccountservice/creditcardcancellation/cancellationresult'
              })
            } else {
              // plus.nativeUI.closeWaiting();
              // mask.close();
              vue.$mui.alert(
                'Cancel failed! The response is: \n' +
                  JSON.stringify(data.msg) +
                  '.',
                'Error',
                'OK'
              )
            }
          },
          error: function(xhr, type, errorThrown) {
            // plus.nativeUI.closeWaiting();
            // mask.close();
            console.log(type)
            var msg =
              'Cancel failed! The response is: \n' + xhr.responseText + '.'
            if (type === 'timeout') {
              msg = 'Cancel failed. Time out!'
            }
            vue.$mui.alert(msg, 'Error', 'OK')
          }
        }
      )
    }
  }
}
</script>

<style scoped>
.cancellationform .div-select-info {
  width: 100%;
  height: auto;
  border: 1px #22c1e6 solid;
  padding: 0 0 30px;
  margin-bottom: 80px;
}

.cancellationform .p-select-info {
  margin: 30px 0 0 0;
  font-size: 20px;
  text-align: center;
}

.cancellationform .lbs-icon {
  width: 30px;
  height: 23px;
}

.cancellationform .text-form {
  font-size: 20px;
}

.cancellationform .text-form-item {
  color: #707070;
}

.cancellationform .text-u {
  color: #707070;
}

.cancellationform .form-header {
  margin-bottom: 80px;
}

.cancellationform .clause {
  width: 100%;
  background-color: #e2e2e2;
  padding: 15px;
  margin-top: 80px;
}

.cancellationform .clause-p {
  color: #000000;
}

.cancellationform .clause-ok {
  display: inline-block;
  margin-left: 50px;
}

.cancellationform .button-group {
  margin: 30px 20px 20px 0;
  float: right;
  min-width: 200px;
}

.cancellationform .edit {
  cursor: pointer;
  width: 100px;
}
</style>
