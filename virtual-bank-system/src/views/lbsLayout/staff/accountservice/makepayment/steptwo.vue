<template>
  <div class="steptwo">
    <el-row>
      <el-col :span="24" offset="2">
        <div style="float: left;margin-right: 20px">
          <div class="el-step__icon is-text is-finish" style="margin-top: 8px">
            <div class="el-step__icon-inner">1</div>
          </div>
          <div style="border-right: 2px #109eae solid;height: 150px;width: 13px" />
          <div class="el-step__icon is-text is-ing">
            <div class="el-step__icon-inner">2</div>
          </div>
          <div style="border-right: 2px #707070 solid;height: 150px;width: 13px" />
          <div class="el-step__icon is-text no-finish">
            <div class="el-step__icon-inner">3</div>
          </div>
          <div style="border-right: 2px #707070 solid;height: 170px;width: 13px" />
          <div class="el-step__icon is-text no-finish">
            <div class="el-step__icon-inner">4</div>
          </div>
        </div>
        <div class="right">
          <p class="text-title is-finish">Move Money From</p>
          <div style="padding: 43px 0 43px">
            <p class="text-account">Account</p>
            <el-select
              slot="prepend"
              v-model="accountNumber"
              disabled
              placeholder=""
              style="width: 400px;margin-right: 200px"
            >
              <el-option
                v-for="(item,index) in accountList"
                :key="index"
                :label="item.type + ' ' + item.label"
                :value="item.value"
              />
            </el-select>
            <div style="display: inline-block">
              <el-checkbox-group v-model="checkList">
                <el-checkbox disabled label=" " />
                <p style="display: inline-block;cursor: pointer;text-decoration: underline" @click="submit()">EDIT</p>
              </el-checkbox-group>
            </div>
          </div>
          <p class="text-title is-ing">Move Money To</p>
          <div style="padding: 40px 0 40px">
            <p class="text-account" style="margin-right: 70px">Credit Card</p>
            <el-select slot="prepend" v-model="creditCardAccount" placeholder="" style="width: 400px;margin-right: 200px">
              <el-option v-for="(item,index) in creditCardAccountList" :key="index" :label="item.label" :value="item.value" />
            </el-select>
            <div style="display: inline-block" @click="submit('next')">
              <el-checkbox-group v-model="checkList">
                <el-checkbox label="  " />
              </el-checkbox-group>
            </div>
          </div>
          <p class="text-title" style="margin-bottom: 155px">Transfer Details</p>
          <p class="text-title">Confirmation</p>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
export default {
  name: 'Steptwo',
  data() {
    return {
      currency: 'HKD',
      accountNumber: '',
      creditCardAccount: '',
      savingList: [],
      currentList: [],
      accountList: [],
      paymentInfo: {},
      checkList: [' '],
      creditCardAccountList: []
    }
  },
  mounted() {
    this.paymentInfo = JSON.parse(window.sessionStorage.getItem('paymentInfo'))
    this.creditCardAccountList = this.getcreditcardnumberpickerdata(window.sessionStorage.getItem('creditCardaccountlist'))
    this.paymentInfo = JSON.parse(window.sessionStorage.getItem('paymentInfo'))
    this.initdata()
  },
  methods: {
    initdata() {
      this.accountNumber = this.paymentInfo.accountNumber
      if (!this.paymentInfo.creditCardAccount) {
        this.creditCardAccount = this.creditCardAccountList[0].value
      } else {
        this.creditCardAccount = this.paymentInfo.creditCardAccount
      }
    },
    submit(type) {
      if (type === 'next') {
        this.paymentInfo['creditCardAccount'] = this.creditCardAccount
        window.sessionStorage.setItem('paymentInfo', JSON.stringify(this.paymentInfo))
        this.$router.push({ path: '/tellerlbsaccountservice/repayment/stepthree' })
      } else {
        this.$router.push({ path: '/tellerlbsaccountservice/repayment' })
      }
    },
    getcreditcardnumberpickerdata(list) {
      var accountlist = JSON.parse(list)
      var pickerdata = []
      for (var i = 0; i < accountlist.length; i++) {
        if (accountlist[i].accountStatus === 'A') {
          pickerdata.push({
            label: accountlist[i].accountNumber,
            value: accountlist[i].accountNumber
          })
        }
      }
      return pickerdata
    }
  }
}
</script>

<style scoped>
  .steptwo p {
    line-height: 1;
    color: #707070;
  }

  .steptwo .text-title {
    font-size: 40px;
    color: #707070;
  }

  .steptwo .text-account {
    display: inline-block;
    font-size: 20px;
    margin-right: 100px;
  }

  .steptwo .is-finish {
    color: #109eae;
    border-color: #109eae;
  }

  .steptwo .no-finish {
    color: #707070;
    border-color: #707070;
  }

  .steptwo .is-ing {
    color: #000000;
    border-color: #000000;
  }
</style>

<style>
  .steptwo .el-step__title {
    font-size: 40px;
    margin-left: 20px;
    color: #707070;
  }

  .steptwo .el-input__icon {
    height: 40px;
  }

  .steptwo .el-input__inner {
    margin-bottom: 0;
    height: 40px;
  }

  .steptwo .el-checkbox__inner {
    width: 20px;
    height: 20px;
  }

  .steptwo .el-checkbox__inner::after {
    width: 8px;
    height: 10px;
  }

  .steptwo .el-checkbox {
    color: #109eae;
    cursor: default;
  }

  .steptwo .el-checkbox__input.is-disabled.is-checked .el-checkbox__inner {
    background-color: #109eae;
  }

  .steptwo .el-checkbox__input.is-disabled.is-checked .el-checkbox__inner::after {
    border-color: #ffffff;
  }

  .steptwo .el-input.is-disabled .el-input__inner {
    color: #707070;
  }

  .steptwo .el-checkbox__inner {
    width: 20px;
    height: 20px;
    border-color: #999999;
  }
</style>
