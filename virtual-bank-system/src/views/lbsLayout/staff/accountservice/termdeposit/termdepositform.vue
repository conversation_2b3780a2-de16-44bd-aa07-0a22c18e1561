<template>
  <div v-loading.fullscreen.lock="loading" class="termdepositform">
    <el-row>
      <el-col :lg="14" :xs="24">
        <div class="main-left">
          <p class="summary">{{ $t('lbsTips.accountDetailTip.tip48') }}</p>
          <el-row>
            <el-col :lg="12" :xs="24" style="height: 110px;">
              <el-tooltip effect="dark" placement="bottom">
                <div slot="content" style="width: 330px">{{ $t('lbsTips.accountDetailTip.Amount') }}</div>
                <p class="select-result">{{ $t('lbsTips.accountDetailTip.tip37') }}:</p>
              </el-tooltip>
            </el-col>
            <el-col :lg="12" :xs="24" style="height: 110px;">
              <p
                class="select-result"
              >{{ termDepositInfo.tdCcy + ' ' + $lbs.decimal_format(termDepositInfo.tdAmount) }}</p>
            </el-col>
            <el-col :lg="12" :xs="24" style="height: 110px;">
              <el-tooltip effect="dark" placement="bottom">
                <div slot="content" style="width: 330px">{{ $t('lbsTips.accountDetailTip.DebitAccount') }}</div>
                <p class="select-result">{{ $t('lbsTips.accountDetailTip.tip38') }}:</p>
              </el-tooltip>
            </el-col>
            <el-col :lg="12" :xs="24" style="height: 110px;">
              <p class="select-result">{{ termDepositInfo.debitAccountNumber }}</p>
            </el-col>
            <el-col :lg="12" :xs="24" style="height: 110px;">
              <el-tooltip effect="dark" placement="bottom">
                <div slot="content" style="width: 330px">{{ $t('lbsTips.accountDetailTip.TermDepositAccount') }}</div>
                <p class="select-result">{{ $t('lbsTips.accountDetailTip.tip39') }}:</p>
              </el-tooltip>
            </el-col>
            <el-col :lg="12" :xs="24" style="height: 110px;">
              <p class="select-result">{{ termDepositInfo.tdAccountNumber }}</p>
            </el-col>
            <el-col :lg="12" :xs="24" style="height: 110px;">
              <el-tooltip effect="dark" placement="bottom">
                <div slot="content" style="width: 330px">{{ $t('lbsTips.accountDetailTip.DepositMaturity') }}</div>
                <p class="select-result">{{ $t('lbsTips.accountDetailTip.tip40') }}:</p>
              </el-tooltip>
            </el-col>
            <el-col :lg="12" :xs="24" style="height: 110px;">
              <p class="select-result">{{ termDepositInfo.tdContractPeriod }}</p>
            </el-col>
          </el-row>
        </div>
      </el-col>

      <el-col :span="24">
        <div class="button-group text-center">
          <el-button class="button btn1" type="primary" @click="submit('next')">{{ $t('lbs.Next') }}</el-button>
          <el-button class="button btn2" type="danger" @click="submit('back')">{{ $t('lbs.Cancel') }}</el-button>
        </div>
      </el-col>
    </el-row>
    <auth v-if="showAuth" @redirect="retry" @hideAuth="hideAuth" />
  </div>
</template>

<script>
import auth from '../../auth.vue'
export default {
  name: 'Termdepositform',
  components: {
    auth
  },
  data() {
    return {
      showAuth: false,
      loading: false,
      token: null,
      termDepositInfo: {},
      password: '',
      customernumber: ''
    }
  },
  mounted() {
    this.customernumber = window.sessionStorage.getItem('targetCustomerNumber')
    this.token = window.sessionStorage.getItem('token')
    this.termDepositInfo = JSON.parse(
      window.sessionStorage.getItem('termDepositInfo')
    )
  },
  methods: {
    retry(param) {
      this.deposit(param)
    },
    hideAuth() {
      this.showAuth = false
    },
    deposit(token) {
      var vue = this
      this.$mui.ajax(
        this.LBSGateway + '/deposit-experience/termDeposit/application',
        {
          data: vue.termDepositInfo,
          dataType: 'json', // 服务器返回json格式数据
          type: 'post', // HTTP请求类型
          timeout: 60000,
          headers: {
            accept: '*/*',
            token: token,
            clientid: vue.$parent.clientid,
            messageid: vue.$parent.messageid,
            'Content-Type': 'application/json',
            customerNumber: vue.customernumber
          },
          beforeSend: function() {
            vue.loading = true
          },
          complete: function() {
            vue.loading = false
          },
          success: function(data) {
            if (data.code === '200') {
              var requesttimedata = {
                item: 'Systemdate'
              }
              vue.$mui.ajax(
                vue.LBSGateway +
                  '/sysadmin-experience/sysadmin/systemParameterRetrieval',
                {
                  data: requesttimedata,
                  dataType: 'json', // 服务器返回json格式数据
                  type: 'post', // HTTP请求类型
                  timeout: 60000,
                  headers: {
                    accept: '*/*',
                    'Content-Type': 'application/json',
                    token: vue.token,
                    customerNumber: vue.customernumber
                  },
                  beforeSend: function() {
                    // plus.nativeUI.showWaiting("Loading…", "div");
                    // mask.show();
                    // document.getElementById("load").style.display = "inline";
                  },
                  complete: function() {
                    // plus.nativeUI.closeWaiting();
                    // mask.close();
                    // document.getElementById("load").style.display = "none";
                  },
                  success: function(data1) {
                    // data.msg = data.msg.replace("TDNumber", "TDNumber").replace("Account", "Account")
                    //
                    // data.msg += "Transaction Time:" + vue.$moment.parseZone(new Date().getTime()).local().format('YYYY-MM-DD HH:mm:ss') +
                    //   "Entry Date:" + vue.$moment.parseZone(Number(data1.data[0].value)).local().format('YYYY-MM-DD')
                    //
                    // var response = data.data;
                    // window.sessionStorage.setItem("TDNumber", data.data)
                    // // mui.alert(handlermsg(data.msg), "Success", "OK")
                    // vue.$mui.alert('Transaction Accepted.', "Success", "OK")
                    vue.$router.push({
                      path: '/tellerlbsaccountservice/termdeposit/termdepositresult'
                    })
                  },
                  error: function(xhr, type, errorThrown) {
                    console.log(type)
                    var msg =
                      'Get System Date failed! The response is: ' +
                      xhr.responseText +
                      '.'
                    if (type === 'timeout') {
                      msg = 'Get System Date failed. Time out!'
                    }
                    vue.$mui.alert(msg, 'Error', 'OK')
                  }
                }
              )
            } else {
              vue.$mui.alert(
                'Term Deposit failed! The response is: ' +
                  JSON.stringify(data) +
                  '.',
                'Error',
                'OK'
              )
            }
          },
          error: function(xhr, type, errorThrown) {
            if (JSON.parse(xhr.response).code === '403005') {
              vue.$mui.alert(vue.$t('lbs.common.limitTip'), vue.$t('lbs.common.warning'), vue.$t('lbs.common.confirm'), function() {
                vue.showAuth = true
              })
            } else {
              console.log(type)
              var msg =
                'Term Deposit failed! The response is: ' + xhr.responseText + '.'
              if (type === 'timeout') {
                msg = 'Term Deposit failed. Time out!'
              }
              vue.$mui.alert(msg, 'Error', 'OK')
            }
          }
        }
      )
    },
    submit(type) {
      if (type === 'back') {
        this.$router.push({
          path: '/tellerlbsaccountservice/termdeposit/applytermdeposit'
        })
      } else if (type === 'next') {
        this.deposit(this.token)
      }
    }
  }
}
</script>

<style scoped>
.termdepositform .summary {
  color: #707070;
  font-size: 42px;
  margin-bottom: 80px;
  margin-top: 30px;
}

.termdepositform .main-left {
  padding: 30px 50px 30px;
}

.termdepositform .select-result {
  color: #707070;
  font-size: 30px;
  margin-bottom: 80px;
}

.termdepositform p {
  line-height: 1;
}

.termdepositform .title {
  font-size: 30px;
  color: #22c1e6;
  margin-top: 100px;
  text-align: center;
  margin-bottom: 40px;
}

.termdepositform .title2 {
  font-size: 28px;
  color: #22c1e6;
  text-align: center;
  margin-bottom: 70px;
}

.termdepositform .verification {
  width: 400px;
  height: 280px;
  margin: 0 auto;
  border: 1px solid #999999;
  padding: 20px;
}

.termdepositform .button {
  /* float: right; */
  width: 150px;
  margin-bottom: 30px;
  margin-top: 60px;
  height: 40px;
}

.termdepositform .btn2 {
  margin-right: 20px;
}

.termdepositform .button-group {
  min-width: 330px;
  margin: 0 auto;
}

.termdepositform .div-button-verify {
  width: 100px;
  margin: 0 auto;
}

.termdepositform .button-verify {
  margin-top: 40px;
  width: 100px;
}
</style>

<style>
.termdepositform .el-input__icon {
  height: 50px;
}

.termdepositform .el-input__inner {
  margin-bottom: 0;
  height: 50px;
}

.termdepositform .el-input-group__append {
  height: 50px;
}
</style>
