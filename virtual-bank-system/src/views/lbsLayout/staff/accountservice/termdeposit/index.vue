<template>
  <div v-loading.fullscreen.lock="loading" class="termdepositindex">
    <el-row>
      <el-col :lg="10" :md="12" :xs="24">
        <div class="div-title">
          <p class="title">{{ $t('lbsTips.accountDetailTip.tip1') }}</p>
          <div class="trem-deposit-info">
            <el-row class="row-top">
              <el-col :span="18">
                <el-tooltip class="item" effect="dark" placement="top">
                  <div slot="content" style="width: 300px">{{ $t('lbsTips.accountDetailTip.OnGoingTermDeposit') }}</div>
                  <p class="p-info">{{ $t('lbsTips.accountDetailTip.tip2') }} :</p>
                </el-tooltip>
              </el-col>
              <el-col :span="6">
                <p class="p-info p-value">{{ onGoingCount }}</p>
              </el-col>
            </el-row>
            <el-row>
              <el-col :span="10">
                <el-tooltip class="item" effect="dark" placement="top">
                  <div slot="content" style="width: 300px">
                    {{ $t('lbsTips.accountDetailTip.OnGoingTermDepositPrincipal') }}
                  </div>
                  <p class="p-info">{{ $t('lbsTips.accountDetailTip.tip3') }} :</p>
                </el-tooltip>
              </el-col>
              <el-col :span="14">
                <p class="p-info p-value">{{ 'HKD' + ' ' + $lbs.decimal_format(onGoingPrincipal) }}</p>
              </el-col>
            </el-row>
          </div>
        </div>
      </el-col>
      <el-col :lg="7" :md="12" :xs="24">
        <div class="button-group">
          <el-tooltip effect="dark" placement="right">
            <div slot="content" style="width: 200px">{{ $t('lbsTips.accountDetailTip.ApplyTermDeposit') }}</div>
            <el-button
              class="button btn2"
              type="primary"
              @click="submit('Apply Term Deposit')"
            >{{ $t('lbsTips.accountDetailTip.tip5') }}
            </el-button>
          </el-tooltip>
          <el-tooltip effect="dark" placement="bottom">
            <div slot="content" style="width: 330px">{{ $t('lbsTips.accountDetailTip.ViewMyAllDeposit') }}</div>
            <el-button
              class="button btn3"
              type="success"
              @click="submit('View My All Deposit')"
            >{{ $t('lbsTips.accountDetailTip.tip6') }}
            </el-button>
          </el-tooltip>
        </div>
      </el-col>
      <el-col :lg="7" :md="12" :xs="24">
        <div class="rate-table">
          <el-tooltip effect="dark" placement="bottom">
            <div slot="content" style="width: 330px">{{ $t('lbsTips.accountDetailTip.TermDepositRateTable') }}</div>
            <p class="title-rate">{{ $t('lbsTips.accountDetailTip.tip7') }}</p>
          </el-tooltip>
          <el-card style="width: 410px;margin: 0 auto">
            <el-table height="300" :data="rateList">
              <el-table-column prop="range" label="Range" />
              <el-table-column prop="TDPeriod" label="TDPeriod" width="84" />
              <el-table-column prop="TDInterestRate" label="TDInterestRate" width="120" />
            </el-table>
          </el-card>
        </div>
      </el-col>
      <el-col :span="24">
        <div class="div-bottom">
          <p class="title">{{ $t('lbsTips.accountDetailTip.tip17') }}</p>
          <div class="el-table__header-wrapper">
            <table
              cellspacing="0"
              cellpadding="0"
              border="0"
              class="el-table el-table__header"
              style="width: 100%;"
            >
              <colgroup />
              <thead class="has-gutter">
                <tr class="">
                  <th colspan="1" rowspan="1" class="el-table_24_column_116 is-leaf" width="200">
                    <div class="cell">{{ $t('lbsTips.accountDetailTip.tip58') }}</div>
                  </th>
                  <th colspan="1" rowspan="1" class="el-table_24_column_117 is-leaf">
                    <el-tooltip effect="dark" placement="top">
                      <div slot="content" style="width: 300px">
                        {{ $t('lbsTips.accountDetailTip.EffectiveDate') }}
                      </div>
                      <div class="cell">{{ $t('lbsTips.accountDetailTip.tip18') }}</div>
                    </el-tooltip>
                  </th>
                  <th colspan="1" rowspan="1" class="el-table_24_column_118 is-leaf">
                    <el-tooltip effect="dark" placement="top">
                      <div slot="content" style="width: 300px">
                        {{ $t('lbsTips.accountDetailTip.Tenor') }}
                      </div>
                      <div class="cell">{{ $t('lbsTips.accountDetailTip.tip19') }}</div>
                    </el-tooltip>
                  </th>
                  <th colspan="1" rowspan="1" class="el-table_24_column_119 is-leaf">
                    <el-tooltip effect="dark" placement="top">
                      <div slot="content" style="width: 300px">
                        {{ $t('lbsTips.accountDetailTip.Currency') }}
                      </div>
                      <div class="cell">{{ $t('lbsTips.accountDetailTip.tip20') }}</div>
                    </el-tooltip>
                  </th>
                  <th colspan="1" rowspan="1" class="el-table_24_column_120 is-leaf">
                    <el-tooltip effect="dark" placement="top">
                      <div slot="content" style="width: 300px">
                        {{ $t('lbsTips.accountDetailTip.Interest') }}
                      </div>
                      <div class="cell">{{ $t('lbsTips.accountDetailTip.tip21') }}</div>
                    </el-tooltip>
                  </th>
                  <th colspan="1" rowspan="1" class="el-table_24_column_121 is-leaf">
                    <el-tooltip effect="dark" placement="top">
                      <div slot="content" style="width: 300px">
                        {{ $t('lbsTips.accountDetailTip.Principal') }}
                      </div>
                      <div class="cell">{{ $t('lbsTips.accountDetailTip.tip22') }}</div>
                    </el-tooltip>
                  </th>
                  <th colspan="1" rowspan="1" class="el-table_24_column_122 is-leaf">
                    <el-tooltip effect="dark" placement="top">
                      <div slot="content" style="width: 300px">
                        {{ $t('lbsTips.accountDetailTip.Status') }}
                      </div>
                      <div class="cell">{{ $t('lbsTips.accountDetailTip.tip23') }}</div>
                    </el-tooltip>
                  </th>
                  <th colspan="1" rowspan="1" class="el-table_24_column_116 is-leaf" width="100">
                    <div class="cell">{{ $t('lbsTips.accountDetailTip.tip10') }}</div>
                  </th>
                  <th class="gutter" style="width: 16px;" />
                </tr>
              </thead>
            </table>
          </div>
          <el-table :data="tableData" style="width: 100%" height="250" :show-header="false">
            <el-table-column prop="depositnumber" :label="$t('lbsTips.accountDetailTip.tip18')" />
            <el-table-column prop="createdate" :label="$t('lbsTips.accountDetailTip.tip18')" />
            <el-table-column prop="termperiod" :label="$t('lbsTips.accountDetailTip.tip19')" />
            <el-table-column prop="currencycode" :label="$t('lbsTips.accountDetailTip.tip20')" />
            <el-table-column prop="terminterestrate" :label="$t('lbsTips.accountDetailTip.tip21')" />
            <el-table-column prop="depositamount" :label="$t('lbsTips.accountDetailTip.tip22')" />
            <el-table-column prop="maturitystatus" :label="$t('lbsTips.accountDetailTip.tip23')" />
            <el-table-column :label="$t('lbsTips.accountDetailTip.tip10')" width="100">
              <template slot-scope="scope">
                <svg-icon
                  class="icon-view"
                  icon-class="awesome-eye"
                  @click.native.prevent="termDepositDetail(scope.$index, tableData)"
                />
              </template>
            </el-table-column>
          </el-table>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
export default {
  name: 'Index',
  data() {
    return {
      loading: false,
      token: null,
      customerNumber: null,
      tableData: [],
      onGoingCount: 0,
      onGoingPrincipal: 0,
      rateList: [
        { range: '10000 - 99999', TDPeriod: '1Day', TDInterestRate: 0 }, { range: '10000 - 99999', TDPeriod: '1Week', TDInterestRate: 0.0015 },
        { range: '10000 - 99999', TDPeriod: '2Weeks', TDInterestRate: 0.0015 },
        { range: '10000 - 99999', TDPeriod: '1Month', TDInterestRate: 0.002 },
        { range: '10000 - 99999', TDPeriod: '2Months', TDInterestRate: 0.002 },
        { range: '10000 - 99999', TDPeriod: '3Months', TDInterestRate: 0.0025 },
        { range: '10000 - 99999', TDPeriod: '6Months', TDInterestRate: 0.003 },
        { range: '10000 - 99999', TDPeriod: '9Months', TDInterestRate: 0.0035 },
        { range: '10000 - 99999', TDPeriod: '12Months', TDInterestRate: 0.004 },
        { range: '100000 - 499999', TDPeriod: '1Day', TDInterestRate: 0 },
        { range: '100000 - 499999', TDPeriod: '1Week', TDInterestRate: 0.0015 },
        { range: '100000 - 499999', TDPeriod: '2Weeks', TDInterestRate: 0.0015 },
        { range: '100000 - 499999', TDPeriod: '1Month', TDInterestRate: 0.002 },
        { range: '100000 - 499999', TDPeriod: '2Months', TDInterestRate: 0.002 },
        { range: '100000 - 499999', TDPeriod: '3Months', TDInterestRate: 0.0025 },
        { range: '100000 - 499999', TDPeriod: '6Months', TDInterestRate: 0.003 },
        { range: '100000 - 499999', TDPeriod: '9Months', TDInterestRate: 0.0035 },
        { range: '100000 - 499999', TDPeriod: '12Months', TDInterestRate: 0.004 },
        { range: '500000 - 999999', TDPeriod: '1Day', TDInterestRate: 0.0015 },
        { range: '500000 - 999999', TDPeriod: '1Week', TDInterestRate: 0.002 },
        { range: '500000 - 999999', TDPeriod: '2Weeks', TDInterestRate: 0.002 },
        { range: '500000 - 999999', TDPeriod: '1Month', TDInterestRate: 0.0025 },
        { range: '500000 - 999999', TDPeriod: '2Months', TDInterestRate: 0.0025 },
        { range: '500000 - 999999', TDPeriod: '3Months', TDInterestRate: 0.003 },
        { range: '500000 - 999999', TDPeriod: '6Months', TDInterestRate: 0.0035 },
        { range: '500000 - 999999', TDPeriod: '9Months', TDInterestRate: 0.004 },
        { range: '500000 - 999999', TDPeriod: '12Months', TDInterestRate: 0.045 },
        { range: '1000000 - Gigantic', TDPeriod: '1Day', TDInterestRate: 0.0015 },
        { range: '1000000 - Gigantic', TDPeriod: '1Week', TDInterestRate: 0.002 },
        { range: '1000000 - Gigantic', TDPeriod: '2Weeks', TDInterestRate: 0.002 },
        { range: '1000000 - Gigantic', TDPeriod: '1Month', TDInterestRate: 0.0025 },
        { range: '1000000 - Gigantic', TDPeriod: '2Months', TDInterestRate: 0.0025 },
        { range: '1000000 - Gigantic', TDPeriod: '3Months', TDInterestRate: 0.003 },
        { range: '1000000 - Gigantic', TDPeriod: '6Months', TDInterestRate: 0.0035 },
        { range: '1000000 - Gigantic', TDPeriod: '9Months', TDInterestRate: 0.004 },
        { range: '1000000 - Gigantic', TDPeriod: '12Months', TDInterestRate: 0.0045 }
      ]
    }
  },
  mounted() {
    if (window.sessionStorage.getItem('showTips') === 'true' && window.sessionStorage.getItem('token')) {
      this.$alert(this.$t('lbs.popupTermDeposit'), this.$t('lbs.Tip'), {
        dangerouslyUseHTMLString: true
      })
    }
    this.token = window.sessionStorage.getItem('token')
    this.customerNumber = window.sessionStorage.getItem('targetCustomerNumber')
    this.getTDaccountbalance()

    // -----------------------------------------------------
    // const params = {
    //   platform: 'Virtual banking system',
    //   userId: window.sessionStorage.getItem('role'),
    //   operationType: '虚拟银行主管角色交易定期存款'
    // }
    // this.$store.dispatch('app/VisitsFun', params).then((res) => {
    //   this.loading = false
    //   if (res.data.code !== 20000) {
    //     console.log(res.data.message)
    //   }
    // }).catch(error => {
    //   this.loading = false
    //   console.log(error.message)
    // })
    // -----------------------------------------
  },
  methods: {
    termDepositDetail(index, rows) {
      window.sessionStorage.setItem('index', index)
      window.sessionStorage.setItem('path', '/lbsaccountservice/termdeposit')
      this.$router.push({ path: '/tellerlbsaccountservice/termdeposit/depositdetail' })
    },
    getTDaccountbalance() {
      var vue = this
      this.$mui.ajax(
        this.LBSGateway + '/deposit-experience/termDeposit/allTermDeposit',
        {
          dataType: 'json', // 服务器返回json格式数据
          type: 'post', // HTTP请求类型
          data: {
            customerNumber: vue.customerNumber,
            index: 0,
            items: 9999
          },
          timeout: 60000,
          headers: {
            accept: '*/*',
            token: this.token,
            clientid: vue.$parent.clientid,
            messageid: vue.$parent.messageid,
            'Content-Type': 'application/json',
            customerNumber: vue.customerNumber
          },
          beforeSend: function() {
            vue.loading = true
          },
          complete: function() {
            vue.loading = false
          },
          success: function(data) {
            if (data.code === '200') {
              var response = data.data

              for (var i = 0; i < response.length; i++) {
                response[i].createdate = vue.$moment
                  .parseZone(Number(response[i].createdate))
                  .local()
                  .format('YYYY-MM-DD')
                response[i].terminterestrate =
                    response[i].terminterestrate * 100 + '%'
                if (response[i].maturitystatus === 'A') {
                  response[i].maturitystatus = 'OnGoing'
                  vue.onGoingCount++
                } else if (response[i].maturitystatus === 'D') {
                  response[i].maturitystatus = 'Completed'
                }
                vue.onGoingPrincipal += response[i].depositamount
                response[i].depositamount = vue.$lbs.decimal_format(
                  response[i].depositamount
                )
                response[i].maturitydate = vue.$moment
                  .parseZone(Number(response[i].maturitydate))
                  .local()
                  .format('YYYY-MM-DD')
                response[i].systemdate = vue.$moment
                  .parseZone(Number(response[i].systemdate))
                  .local()
                  .format('YYYY-MM-DD')
              }
              // 展示最近的10条详情
              vue.tableData = response.slice(0, 10)

              window.sessionStorage.setItem(
                'depositDetailList',
                JSON.stringify(response)
              )
              console.log(response)
            } else if (data.code !== '404002') {
              vue.$mui.alert(
                'Get deposit info failed! The response is: \n' +
                  JSON.stringify(data) +
                  '.',
                'Error',
                'OK'
              )
            }
          },
          error: function(xhr, type, errorThrown) {
            console.log(type)
            var msg =
                'Get deposit info failed! The response is: \n' +
                xhr.responseText +
                '.'
            if (type === 'timeout') {
              msg = 'Get deposit info failed. Time out!'
            } else {
              vue.$mui.alert(msg, 'Error', 'OK')
            }
          }
        }
      )
    },
    submit(type) {
      if (type === 'Apply Term Deposit') {
        this.$router.push({
          path: '/tellerlbsaccountservice/termdeposit/applytermdeposit'
        })
      } else if (type === 'Apply Deposit') {
        this.$router.push({ path: '/tellerlbsaccountservice/termdeposit/applydeposit' })
      } else if (type === 'View My All Deposit') {
        this.$router.push({ path: '/tellerlbsaccountservice/termdeposit/viewdeposit' })
      }
    }
  }
}
</script>
<style>
  .el-message-box {
    width: 800px;
  }
</style>
<style scoped>
  .termdepositindex p,
  span {
    line-height: 1;
  }

  .termdepositindex .title {
    font-size: 30px;
    color: #22c1e6;
    margin-bottom: 20px;
  }

  .termdepositindex .div-title {
    width: auto;
    margin: 0 auto;
    padding: 50px 0 0 40px;
  }

  .termdepositindex .trem-deposit-info {
    padding: 15px 20px;
    background-color: #acefff;
    box-shadow: 3px 3px 3px #bbbbbb;
  }

  .termdepositindex .p-info {
    color: #707070;
    font-size: 20px;
    line-height: 40px;
  }

  .termdepositindex .row-top {
    margin-bottom: 20px;
  }

  .termdepositindex .button {
    width: 100%;
    max-width: 350px;
    height: 45px;
  }

  .termdepositindex .btn2,
  .btn3 {
    margin-left: 0;
    margin-top: 40px;
  }

  .termdepositindex .button-group {
    padding: 65px 20px 0;
    width: auto;
    max-width: 350px;
    margin: 0 auto;
  }

  .termdepositindex .rate-table {
    padding: 30px 0 0 0;
    max-width: 400px;
    width: auto;
    margin: 0 auto;
  }

  .termdepositindex .title-rate {
    font-size: 20px;
    color: #22c1e6;
    text-align: center;
  }

  .termdepositindex .title-rate-info {
    width: 300px;
    height: auto;
    margin: 0 auto;
    border: 1px solid #22c1e6;
    padding: 20px 0 5px;
  }

  .termdepositindex .div-ccy {
    width: 70px;
    height: 30px;
    background-color: #807d7d;
    border-radius: 20px;
    position: relative;
    margin: 0 15px 10px 15px;
  }

  .termdepositindex .ccy {
    color: #ffffff;
    line-height: 30px;
    margin-left: 10px;
  }

  .termdepositindex .icon-item {
    width: 10px;
    height: 10px;
    position: absolute;
    right: 13px;
    top: 10px;
  }

  .termdepositindex .interest {
    font-size: 12px;
    margin: 0 15px 5px 15px;
  }

  .termdepositindex .pa {
    font-size: 20px;
    display: inline-block;
    color: #22c1e6;
    margin: 0 15px 0 15px;
  }

  .termdepositindex .date {
    float: right;
  }

  .termdepositindex .title-rate-info hr {
    color: #707070;
  }

  .termdepositindex .u {
    color: #707070;
  }

  .termdepositindex .viewmore {
    color: #707070;
    font-size: 16px;
    margin: 20px 0 0 215px;
  }

  .termdepositindex .div-bottom {
    padding: 0 40px 0;
  }

  .termdepositindex .icon-view {
    width: 22px;
    height: 22px;
    cursor: pointer;
  }
</style>
