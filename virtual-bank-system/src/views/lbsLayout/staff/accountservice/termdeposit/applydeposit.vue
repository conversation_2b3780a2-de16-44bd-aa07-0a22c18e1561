<template>
  <div v-loading.fullscreen.lock="loading">
    <el-row class="termdeposit">
      <el-col :psan="24">
        <el-row>
          <el-col :span="24">
            <div class="title">
              <el-tooltip effect="dark" placement="bottom">
                <div slot="content" style="width: 300px">{{ $t('lbsTips.accountDetailTip.deposit') }}</div>
                <p style="display: inline-block" class="max-title">{{ $t("lbs.router.deposit") }}</p>
              </el-tooltip>
            </div>
          </el-col>
        </el-row>
      </el-col>
    </el-row>
    <el-row class="applydeposit">
      <el-col :lg="10" :xs="24">
        <div class="main-left">
          <el-tooltip effect="dark" placement="bottom">
            <div slot="content" style="width: 330px">{{ $t('lbsTips.accountDetailTip.DepositAccount') }}</div>
            <p class="title">{{ $t('lbsTips.accountDetailTip.tip38') }}</p>
          </el-tooltip>
          <div class="div-info">
            <el-select
              v-model="depositAccountNumber"
              :placeholder="$t('lbsTips.accountDetailTip.tip55')"
              class="el-input"
            >
              <el-option
                v-for="(item,index) in debitList"
                :key="index"
                :label="item.type + ' ' + item.label"
                :value="item.value"
              />
            </el-select>
            <p style="margin-top: 3px;color: #707070;font-size: 12px">{{ fromAccountBalance }}</p>
            <el-tooltip effect="dark" placement="bottom">
              <div slot="content" style="width: 330px">{{ $t('lbsTips.accountDetailTip.Amount') }}</div>
              <p class="title">{{ $t('lbsTips.accountDetailTip.tip37') }}</p>
            </el-tooltip>
            <el-input
              v-model="amount"
              :placeholder="$t('lbsTips.accountDetailTip.tip56')"
              class="input-with-select"
              :maxlength="50"
            >
              <el-select slot="prepend" v-model="currency" placeholder style="width: 80px">
                <el-option label="HKD" value="HKD" />
              </el-select>
            </el-input>
          </div>
        </div>
      </el-col>
      <el-col :lg="14" :xs="24">
        <div class="main-right">
          <p class="summary">{{ $t('lbsTips.accountDetailTip.tip41') }}</p>
          <el-row>
            <el-col :lg="9" :xs="24">
              <el-tooltip effect="dark" placement="bottom">
                <div slot="content" style="width: 330px">{{ $t('lbsTips.accountDetailTip.Amount') }}</div>
                <p class="select-result">{{ $t('lbsTips.accountDetailTip.tip37') }}:</p>
              </el-tooltip>
            </el-col>
            <el-col :lg="15" :xs="24">
              <p v-if="amount.length === 0" class="select-result">{{ 0 + ' ' +currency }}</p>
              <p v-else class="select-result">{{ currency + ' ' + $lbs.decimal_format(amount) }}</p>
            </el-col>
            <el-col :lg="9" :xs="24">
              <el-tooltip effect="dark" placement="bottom">
                <div slot="content" style="width: 330px">{{ $t('lbsTips.accountDetailTip.DepositAccount') }}</div>
                <p class="select-result">{{ $t('lbsTips.accountDetailTip.tip54') }}:</p>
              </el-tooltip>
            </el-col>
            <el-col :lg="15" :xs="24">
              <p v-if="depositAccountNumber.length === 0" class="select-result">
                {{ $t('lbsTips.accountDetailTip.tip43') }}</p>
              <p v-else class="select-result">{{ depositAccountNumber }}</p>
            </el-col>
          </el-row>
          <el-col :span="24">
            <el-button class="button btn1" type="primary" @click="submit('next')">{{ $t('lbs.Next') }}</el-button>
          </el-col>
        </div>
      </el-col>
      <el-col :span="24">
        <div style="margin: 0 40px">
          <p style="font-size: 30px;color: #22C1E6;margin-bottom: 20px">{{ $t('lbs.DepositRecord') }}</p>
          <el-card>
            <el-table :data="tableData" height="400px">
              <el-table-column prop="trandate" :label="$t('lbs.trandate')" />
              <el-table-column prop="tranamt" :label="$t('lbs.tranamt')" />
              <el-table-column prop="ccy" :label="$t('lbs.ccy')" />
              <el-table-column prop="trandesc" :label="$t('lbs.trandesc')" />
              <el-table-column prop="actualbalamt" :label="$t('lbsTips.accountDetailTip.AccountBalanceTh')" />
            </el-table>
          </el-card>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import axios from 'axios'

export default {
  name: 'Applydeposit',
  data() {
    return {
      token: null,
      loading: false,
      amount: '',
      currency: 'HKD',
      depositAccountNumber: '',
      savingList: [],
      currentList: [],
      debitList: [],
      tableData: [],
      fromAccountBalance: '',
      customernumber: ''
    }
  },
  watch: {
    amount: {
      handler() {
        this.clearNoNum()
      }
    },
    'depositAccountNumber'() {
      this.transactionLog()
      this.getAccountBalance()
    }
  },
  created() {
    this.customernumber = window.sessionStorage.getItem('targetCustomerNumber')
    this.token = window.sessionStorage.getItem('token')
  },
  mounted() {
    if (window.sessionStorage.getItem('showTips') === 'true' && window.sessionStorage.getItem('token')) {
      this.$alert(this.$t('lbs.popupDeposit'), this.$t('lbs.Tip'), {
        dangerouslyUseHTMLString: true
      })
    }
    this.initdata()
  },
  methods: {
    initdata() {
      const savingAccountList = JSON.parse(
        window.sessionStorage.getItem('savingaccountlist')
      )
      const currentaccountlist = JSON.parse(
        window.sessionStorage.getItem('currentaccountlist')
      )
      for (var i = 0; i < savingAccountList.length; i++) {
        if (savingAccountList[i].accountStatus === 'A') {
          this.savingList.push({
            label: savingAccountList[i].accountNumber,
            value: savingAccountList[i].accountNumber,
            type: 'Saving'
          })
        }
      }

      for (var j = 0; j < currentaccountlist.length; j++) {
        if (currentaccountlist[j].accountStatus === 'A') {
          this.currentList.push({
            label: currentaccountlist[j].accountNumber,
            value: currentaccountlist[j].accountNumber,
            type: 'Current'
          })
        }
      }
      this.debitList = this.savingList.concat(this.currentList)
      this.depositAccountNumber = this.debitList[0].value
      this.getAccountBalance()
    },
    clearNoNum() {
      this.amount = this.amount.replace(/[^\d.]/g, '') // 清除"数字"和"."以外的字符
      this.amount = this.amount.replace(/^\./g, '') // 验证第一个字符是数字而不是字符
      this.amount = this.amount.replace(/\.{2,}/g, '.') // 只保留第一个.清除多余的
      this.amount = this.amount
        .replace('.', '$#$')
        .replace(/\./g, '')
        .replace('$#$', '.')
      this.amount = this.amount.replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3') // 只能输入两个小数
    },
    submit(type) {
      if (type === 'back') {
        this.$router.push({ path: '/tellerlbsaccountservice/termdeposit' })
      } else if (type === 'next') {
        if (
          !this.amount ||
            this.amount.length === 0 ||
            !(typeof Number(this.amount) && !isNaN(this.amount))
        ) {
          this.$mui.alert('Please enter the Deposit Amount', 'Warning', 'OK')
        } else if (
          !this.depositAccountNumber ||
            this.depositAccountNumber.length === 0
        ) {
          this.$mui.alert(
            'Please enter the Deposit Account Number.',
            'Warning',
            'OK'
          )
        } else if (!this.currency || this.currency.length === 0) {
          this.$mui.alert('Please enter the TD Ccy.', 'Warning', 'OK')
        } else {
          const requestData = {
            accountNumber: this.depositAccountNumber,
            depositAmount: this.amount,
            currencycode: this.currency
          }
          window.sessionStorage.setItem(
            'depositInfo',
            JSON.stringify(requestData)
          )
          this.$router.push({
            path: '/tellerlbsaccountservice/termdeposit/depositform'
          })
        }
      }
    },
    transactionLog() {
      const _this = this
      _this.loading = true
      const requestData = {
        accountnumber: _this.depositAccountNumber,
        index: 1,
        items: 9999,
        transFromDate: ************,
        transToDate: new Date().getTime(),
        trantype: '0004'
      }
      axios.post(`${_this.LBSGateway}/deposit-experience/transactionLog/enquiry`, requestData, { headers: { token: _this.token, customerNumber: _this.customernumber }})
        .then(response => {
          _this.loading = false
          _this.tableData = []
          if (response.data.code === '200') {
            const tableData = response.data.data
            for (let i = 0; i < tableData.length; i++) {
              if (tableData[i].trandesc === 'deposit') {
                tableData[i].trandate = _this.$moment(tableData[i].trandate).format('YYYY-MM-DD HH:mm:ss')
                tableData[i].tranamt = _this.$lbs.decimal_format(tableData[i].tranamt)
                tableData[i].actualbalamt = _this.$lbs.decimal_format(tableData[i].actualbalamt)
                tableData[i].trandesc = tableData[i].trandesc.toUpperCase()
                _this.tableData.push(tableData[i])
              }
            }
          } else if (response.data.code === '404003') {
            // _this.$message.success(response.data.msg, _this.$t('lbs.common.success'))
          } else {
            _this.$message.error(response.data.msg, _this.$t('lbs.common.error'))
          }
        })
        .catch(error => {
          _this.loading = false
          _this.$message.error(error.message, _this.$t('lbs.common.error'))
        })
    },
    getAccountBalance() {
      var vue = this
      vue.$mui.ajax(vue.LBSGateway + '/deposit-experience/deposit/account/accountDetails/' + vue.depositAccountNumber, {
        dataType: 'json', // 服务器返回json格式数据
        type: 'post', // HTTP请求类型
        timeout: 60000,
        headers: {
          'accept': '*/*',
          'token': vue.token,
          'clientid': vue.$parent.clientid,
          'messageid': vue.$parent.messageid,
          'Content-Type': 'application/json',
          'customerNumber': vue.customernumber
        },
        beforeSend: function() {
          vue.loading = true
        },
        complete: function() {
          vue.loading = false
        },
        success: function(data) {
          if (data.code === '200') {
            var data1 = data.data.account
            if (!data1.availablebalance) data1.availablebalance = 0.0
            if (data1.availablebalance) {
              data1.availablebalance = Number(
                data1.availablebalance
                  .toString()
                  .replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3')
              )
            }
            vue.fromAccountBalance = '( ' + vue.$t('lbs.Balance') + ' (' + data1.currencycode + ')：' + data1.availablebalance + ' )'
          } else {
            vue.fromAccountBalance = 0.0
          }
        },
        error: function(xhr, type, errorThrown) {
          console.log(type)
          var msg = 'Transfer failed! The response is: \n' + xhr.responseText + '.'
          if (type === 'timeout') {
            msg = 'Transfer failed. Time out!'
          }
          vue.$mui.alert(msg, 'Error', 'OK')
        }
      })
    }
  }
}
</script>

<style scoped>
  .termdeposit .title {
    padding: 30px 28px;
    height: 140px;
    background-color: #FFFFFF;
    margin: 0 25px 0 25px;
    border-radius: 50px;
    background-image: url("../../../components/title-bg.png");
    background-repeat: no-repeat;
    background-size: 574px 130px;
    background-position: right;
    box-shadow: 5px 5px 5px #bbbbbb;
  }

  .termdeposit p {
    color: #707070;
    margin-bottom: 0;
    line-height: 1;
  }

  .termdeposit .max-title {
    font-size: 50px;
    margin-bottom: 40px;
    font-weight: 500;
    color: #22C1E6;
    margin-top: 15px;
  }

  .termdeposit .little-title {
    font-size: 30px;
  }

  .applydeposit p {
    line-height: 1;
  }

  .applydeposit .main-left {
    padding: 0 50px 30px;
  }

  .applydeposit .title {
    font-size: 30px;
    color: #707070;
    margin: 100px 0 40px;
  }

  .applydeposit .summary {
    color: #707070;
    font-size: 42px;
    margin-bottom: 60px;
    margin-top: 50px;
  }

  .applydeposit .main-right {
    margin-top: 40px;
    padding: 30px 50px 30px;
    border-left: 1px #cccccc solid;
    height: 480px;
  }

  .applydeposit .select-result {
    color: #707070;
    font-size: 30px;
    margin-bottom: 60px;
  }

  .applydeposit .button {
    float: right;
    width: 100px;
    margin-bottom: 30px;
  }

  .applydeposit .btn2 {
    margin-right: 20px;
  }
</style>

<style>
  .applydeposit .el-input__icon {
    height: 40px;
  }

  .applydeposit .el-input__inner {
    margin-bottom: 0;
    height: 40px;
  }
</style>

