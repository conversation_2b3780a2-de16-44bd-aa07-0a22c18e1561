<template>
  <div class="makepayment">
    <el-row>
      <el-col :span="24">
        <div class="title">
          <el-tooltip effect="dark" placement="bottom">
            <div slot="content" style="width: 300px">
              Credit card repayment: This is designed to make a credit card repayment.
            </div>
            <p class="max-title">Repayment</p>
          </el-tooltip>
        </div>
      </el-col>
    </el-row>
    <router-view/>
  </div>
</template>

<script>
  export default {
    name: "makepayment"
  }
</script>

<style scoped>
  .makepayment .title {
    padding: 30px 28px;
    height: 140px;
    background-color: #FFFFFF;
    margin: 0 25px 40px 25px;
    border-radius: 50px;
    background-image: url("../../components/title-bg.png");
    background-repeat: no-repeat;
    background-size: 574px 130px;
    background-position: right;
    box-shadow: 5px 5px 5px #bbbbbb;
  }

  .makepayment p {
    color: #707070;
    margin-bottom: 0;
    line-height: 1;
  }

  .makepayment .max-title {
    font-size: 50px;
    margin-top: 15px;
    margin-bottom: 40px;
    font-weight: 500;
    display: inline-block;
    color: #22C1E6;
  }
</style>

