<template>
  <div v-loading.fullscreen.lock="loading" class="applymortgage">
    <el-row>
      <el-col :span="24">
        <div class="title">
          <p class="max-title">{{ $t('lbs.MortgageApplication') }}</p>
        </div>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="24">
        <div class="main">
          <el-card>
            <div class="information">
              <p class="title2">{{ $t('lbs.PersonalInformation') }}</p>
              <div>
                <span class="left">{{ $t('lbs.Name') }}</span><span class="right">{{ `${personalInformation.firstname} ${personalInformation.lastname}` }}</span>
              </div>
              <div>
                <span class="left">{{ $t('lbs.Email') }}</span><span
                  class="right"
                >{{ `${personalInformation.emailaddress}` }}</span>
              </div>
              <div>
                <span class="left">{{ $t('lbs.MobileNumber') }}</span><span class="right">{{ `${personalInformation.mobilephonenumber}` }}</span>
              </div>
            </div>
            <div class="information">
              <el-form
                ref="MortgageForm"
                size="mini"
                :model="MortgageForm"
                :rules="MortgageFormRules"
                label-position="left"
              >
                <p class="title2">{{ $t('lbs.LoanInformation') }}</p>
                <div class="info-item">
                  <div class="mortgage-input-select">
                    <el-form-item :label="$t('lbs.monthlysalary')" prop="monthlysalary" :label-width="formLabelWidth">
                      <el-input
                        v-model="MortgageForm.monthlysalary"
                        autocomplete="off"
                        :placeholder="$t('lbs.monthlysalary')"
                        style="width: 80%;"
                      >
                        <el-select
                          slot="prepend"
                          v-model="MortgageForm.ccyCode"
                          placeholder=""
                          style="width: 80px"
                        >
                          <el-option
                            v-for="(item,index) in CurrencyList"
                            :key="index"
                            :label="item.ccycode"
                            :value="item.ccycode"
                            :disabled="item.ccycode !== 'HKD'"
                          />
                        </el-select>
                      </el-input>
                    </el-form-item>
                  </div>
                  <el-form-item :label="$t('lbs.repaymentPeriod')" prop="repaymentPeriod" :label-width="formLabelWidth">
                    <el-slider
                      v-model="MortgageForm.repaymentPeriod"
                      :max="30"
                      :min="1"
                      show-input
                    />
                  </el-form-item>
                  <div class="mortgage-input-select">
                    <el-form-item :label="$t('lbs.borringneeds')" prop="borringneeds" :label-width="formLabelWidth">
                      <el-input
                        v-model="MortgageForm.borringneeds"
                        autocomplete="off"
                        :placeholder="$t('lbs.borringneeds')"
                        style="width: 80%;"
                        maxlength="18"
                      >
                        <el-select
                          slot="prepend"
                          v-model="MortgageForm.ccyCode"
                          placeholder=""
                          style="width: 80px"
                        >
                          <el-option
                            v-for="(item,index) in CurrencyList"
                            :key="index"
                            :label="item.ccycode"
                            :value="item.ccycode"
                            :disabled="item.ccycode !== 'HKD'"
                          />
                        </el-select>
                      </el-input>
                      <p style="margin-top: 10px;color: green;font-size: 12px">{{ `(${$t('lbs.maxLoanAmount')}：
                        ${maxLoanAmount})` }}</p>
                    </el-form-item>
                  </div>
                  <el-form-item :label="$t('lbs.loanScheme')" prop="loanScheme" :label-width="formLabelWidth">
                    <el-select
                      v-model="MortgageForm.loanScheme"
                      :placeholder="$t('lbs.loanScheme')"
                      style="width: 80%"
                    >
                      <el-option
                        v-for="(item,index) in loanSchemeOptions"
                        :key="index"
                        :label="item.label"
                        :disabled="item.value !== 'F'"
                        :value="item.value"
                      />
                    </el-select>
                  </el-form-item>
                  <el-form-item :label="$t('lbs.repaymentPlan')" prop="repaymentPlan" :label-width="formLabelWidth">
                    <el-select
                      v-model="MortgageForm.repaymentPlan"
                      :placeholder="$t('lbs.repaymentPlan')"
                      style="width: 80%"
                    >
                      <el-option
                        v-for="(item,index) in RepaymentPlanOptions"
                        :key="index"
                        :label="item.label"
                        :disabled="item.value !== 'L'"
                        :value="item.value"
                      />
                    </el-select>
                  </el-form-item>
                  <el-form-item :label="$t('lbs.repaymentPlan')" prop="repaymentPlan" :label-width="formLabelWidth">
                    <el-radio-group v-model="MortgageForm.repaymentCycle">
                      <el-radio v-for="item in RepaymentCycleOptions" :key="item.value" :label="item.value">
                        {{ item.label }}
                      </el-radio>
                    </el-radio-group>
                  </el-form-item>
                  <el-form-item
                    :label="$t('lbs.loanAccountNumber')"
                    prop="accountnumber"
                    :label-width="formLabelWidth"
                  >
                    <el-select
                      v-model="MortgageForm.accountnumber"
                      :placeholder="$t('lbs.loanAccountNumber')"
                      style="width: 80%;"
                    >
                      <el-option
                        v-for="item in loanAccountList"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                      />
                    </el-select>
                  </el-form-item>
                  <el-form-item
                    :label="$t('lbs.debitAccountNumber')"
                    prop="debitaccountnumber"
                    :label-width="formLabelWidth"
                  >
                    <el-select
                      v-model="MortgageForm.debitaccountnumber"
                      :placeholder="$t('lbs.debitAccountNumber')"
                      style="width: 80%;"
                    >
                      <el-option-group
                        v-for="group in debitAccountList"
                        :key="group.label"
                        :label="group.label"
                      >
                        <el-option
                          v-for="item in group.options"
                          :key="item.value"
                          :label="item.label"
                          :value="item.value"
                        />
                      </el-option-group>
                    </el-select>
                  </el-form-item>
                </div>
                <hr style="background-color: #cccccc">
                <div class="information">
                  <p class="title2">{{ $t('lbs.MortgagedPropertyInformation') }}</p>
                  <div class="info-item">
                    <el-form-item
                      :label="$t('lbs.propertyClassification')"
                      prop="propertyClassification"
                      :label-width="formLabelWidth"
                    >
                      <el-radio-group v-model="MortgageForm.propertyClassification">
                        <el-radio v-for="item in ClassificationOptions" :key="item.value" :label="item.value">
                          {{ item.label }}
                        </el-radio>
                      </el-radio-group>
                    </el-form-item>
                    <el-form-item :label="$t('lbs.propertyType')" prop="propertyType" :label-width="formLabelWidth">
                      <el-select v-model="MortgageForm.propertyType" :placeholder="$t('lbs.propertyType')">
                        <el-option
                          v-for="item in TypeOptions"
                          :key="item.value"
                          :label="item.label"
                          :value="item.value"
                        />
                      </el-select>
                    </el-form-item>
                    <el-form-item
                      :label="$t('lbs.propertyTransactionStatus')"
                      prop="propertyTransactionStatus"
                      :label-width="formLabelWidth"
                    >
                      <el-radio-group v-model="MortgageForm.propertyTransactionStatus">
                        <el-radio v-for="item in TransactionStatusOptions" :key="item.value" :label="item.value">
                          {{ item.label }}
                        </el-radio>
                      </el-radio-group>
                    </el-form-item>
                    <el-form-item
                      :label="$t('lbs.propertyMonthlyRental')"
                      prop="propertyMonthlyRental"
                      :rules="MortgageForm.propertyTransactionStatus === 'T' ? [{required: true, message: $t('lbs.MortgageTip1'), trigger: 'change'},{pattern: /^\+?[1-9]\d*$/, message: $t('lbs.MortgageTip2'), trigger: 'change'},] : []"
                      :label-width="formLabelWidth"
                    >
                      <el-input
                        v-model="MortgageForm.propertyMonthlyRental"
                        maxlength="18"
                        :disabled="MortgageForm.propertyTransactionStatus === 'V'"
                        :placeholder="$t('lbs.propertyMonthlyRental')"
                      />
                    </el-form-item>
                    <el-form-item
                      :label="$t('lbs.propertyWithCarpark')"
                      prop="propertyWithCarpark"
                      :label-width="formLabelWidth"
                    >
                      <el-radio-group v-model="MortgageForm.propertyWithCarpark">
                        <el-radio v-for="item in BooleanOptions" :key="item.value" :label="item.value">
                          {{ item.label }}
                        </el-radio>
                      </el-radio-group>
                    </el-form-item>
                    <el-form-item
                      :label="$t('lbs.propertyCarparkNumber')"
                      prop="propertyCarparkNumber"
                      :rules="MortgageForm.propertyWithCarpark === 'Y' ? [{required: true, message: $t('lbs.MortgageTip3'), trigger: 'change'}] : []"
                      :label-width="formLabelWidth"
                    >
                      <el-input
                        v-model="MortgageForm.propertyCarparkNumber"
                        autocomplete="off"
                        maxlength="10"
                        :placeholder="$t('lbs.propertyCarparkNumber')"
                        :disabled="MortgageForm.propertyWithCarpark !== 'Y'"
                      />
                    </el-form-item>
                    <el-form-item
                      :label="$t('lbs.propertyWithGarden')"
                      prop="propertyWithGarden"
                      :label-width="formLabelWidth"
                    >
                      <el-radio-group v-model="MortgageForm.propertyWithGarden">
                        <el-radio v-for="item in BooleanOptions" :key="item.value" :label="item.value">
                          {{ item.label }}
                        </el-radio>
                      </el-radio-group>
                    </el-form-item>
                    <el-form-item
                      :label="$t('lbs.propertyWithRoof')"
                      prop="propertyWithRoof"
                      :label-width="formLabelWidth"
                    >
                      <el-radio-group v-model="MortgageForm.propertyWithRoof">
                        <el-radio v-for="item in BooleanOptions" :key="item.value" :label="item.value">
                          {{ item.label }}
                        </el-radio>
                      </el-radio-group>
                    </el-form-item>
                    <el-form-item :label="$t('lbs.purchasePrice')" prop="purchasePrice" :label-width="formLabelWidth">
                      <el-input
                        v-model="MortgageForm.purchasePrice"
                        autocomplete="off"
                        maxlength="20"
                        :placeholder="$t('lbs.purchasePrice')"
                      />
                    </el-form-item>
                    <el-form-item
                      :label="$t('lbs.PropertyAddressFormat')"
                      prop="propertyAddressFormat"
                      :label-width="formLabelWidth"
                    >
                      <el-radio-group v-model="MortgageForm.propertyAddressFormat">
                        <el-radio
                          v-for="item in AddressFormatOptions"
                          :key="item.value"
                          :disabled="item.value === 'F'"
                          :label="item.value"
                        >{{ item.label }}
                        </el-radio>
                      </el-radio-group>
                    </el-form-item>
                    <div v-show="MortgageForm.propertyAddressFormat === 'F'">
                      <el-form-item
                        :label="$t('lbs.propertyAddressLine1')"
                        prop="propertyAddressLine1"
                        :label-width="formLabelWidth"
                      >
                        <el-input
                          v-model="MortgageForm.propertyAddressLine1"
                          autocomplete="off"
                          :placeholder="$t('lbs.propertyAddressLine1')"
                        />
                      </el-form-item>
                      <el-form-item
                        :label="$t('lbs.propertyAddressLine2')"
                        prop="propertyAddressLine2"
                        :label-width="formLabelWidth"
                      >
                        <el-input
                          v-model="MortgageForm.propertyAddressLine2"
                          autocomplete="off"
                          :placeholder="$t('lbs.propertyAddressLine2')"
                        />
                      </el-form-item>
                      <el-form-item
                        :label="$t('lbs.propertyAddressLine3')"
                        prop="propertyAddressLine3"
                        :label-width="formLabelWidth"
                      >
                        <el-input
                          v-model="MortgageForm.propertyAddressLine3"
                          autocomplete="off"
                          :placeholder="$t('lbs.propertyAddressLine3')"
                        />
                      </el-form-item>
                      <el-form-item
                        :label="$t('lbs.propertyAddressLine4')"
                        prop="propertyAddressLine4"
                        :label-width="formLabelWidth"
                      >
                        <el-input
                          v-model="MortgageForm.propertyAddressLine4"
                          autocomplete="off"
                          :placeholder="$t('lbs.propertyAddressLine4')"
                        />
                      </el-form-item>
                    </div>
                    <el-form-item
                      :label="$t('lbs.propertyCountry')"
                      prop="propertyCountry"
                      :label-width="formLabelWidth"
                    >
                      <el-select
                        v-model="MortgageForm.propertyCountry"
                        filterable
                        :placeholder="$t('lbs.propertyCountry')"
                        style="width: 80%"
                      >
                        <!--                        <el-option v-if="language === 'en'" v-for="(item,index) in options" :key="index"-->
                        <!--                                   :label="item.en" :value="item.short"/>-->
                        <el-option
                          v-for="(item,index) in options"
                          :key="index"
                          :label="language === 'en' ? item.en : item.name"
                          :value="item.short"
                        />
                      </el-select>
                    </el-form-item>
                    <el-form-item
                      :label="$t('lbs.propertyAreaCode')"
                      prop="propertyAreaCode"
                      :label-width="formLabelWidth"
                    >
                      <el-input
                        v-model="MortgageForm.propertyAreaCode"
                        disabled
                        autocomplete="off"
                        style="width: 40%;"
                        :placeholder="$t('lbs.propertyAreaCode')"
                      />
                    </el-form-item>
                    <el-form-item
                      :label="$t('lbs.propertyPostCode')"
                      prop="propertyPostCode"
                      :label-width="formLabelWidth"
                    >
                      <el-input
                        v-model="MortgageForm.propertyPostCode"
                        autocomplete="off"
                        maxlength="8"
                        :placeholder="$t('lbs.propertyPostCode')"
                      />
                    </el-form-item>
                    <el-form-item
                      :label="$t('lbs.propertyStreetName')"
                      prop="propertyStreetName"
                      :label-width="formLabelWidth"
                    >
                      <el-input
                        v-model="MortgageForm.propertyStreetName"
                        autocomplete="off"
                        maxlength="35"
                        :placeholder="$t('lbs.propertyStreetName')"
                      />
                    </el-form-item>
                    <el-form-item
                      :label="$t('lbs.propertyStreetNumber')"
                      prop="propertyStreetNumber"
                      :label-width="formLabelWidth"
                    >
                      <el-input
                        v-model="MortgageForm.propertyStreetNumber"
                        autocomplete="off"
                        maxlength="5"
                        :placeholder="$t('lbs.propertyStreetNumber')"
                      />
                    </el-form-item>
                    <el-form-item :label="$t('lbs.propertyEstate')" prop="propertyEstate" :label-width="formLabelWidth">
                      <el-input
                        v-model="MortgageForm.propertyEstate"
                        autocomplete="off"
                        maxlength="35"
                        :placeholder="$t('lbs.propertyEstate')"
                      />
                    </el-form-item>
                    <el-form-item
                      :label="$t('lbs.propertyBuilding')"
                      prop="propertyBuilding"
                      :label-width="formLabelWidth"
                    >
                      <el-input
                        v-model="MortgageForm.propertyBuilding"
                        autocomplete="off"
                        maxlength="35"
                        :placeholder="$t('lbs.propertyBuilding')"
                      />
                    </el-form-item>
                    <el-form-item :label="$t('lbs.propertyBlock')" prop="propertyBlock" :label-width="formLabelWidth">
                      <el-input
                        v-model="MortgageForm.propertyBlock"
                        autocomplete="off"
                        maxlength="5"
                        :placeholder="$t('lbs.propertyBlock')"
                      />
                    </el-form-item>
                    <el-form-item :label="$t('lbs.propertyFloor')" prop="propertyFloor" :label-width="formLabelWidth">
                      <el-input
                        v-model="MortgageForm.propertyFloor"
                        autocomplete="off"
                        maxlength="3"
                        :placeholder="$t('lbs.propertyFloor')"
                      />
                    </el-form-item>
                    <el-form-item :label="$t('lbs.propertyRoom')" prop="propertyRoom" :label-width="formLabelWidth">
                      <el-input
                        v-model="MortgageForm.propertyRoom"
                        autocomplete="off"
                        maxlength="5"
                        :placeholder="$t('lbs.propertyRoom')"
                      />
                    </el-form-item>
                    <el-form-item :label="$t('lbs.propertyFlat')" prop="propertyFlat" :label-width="formLabelWidth">
                      <el-input
                        v-model="MortgageForm.propertyFlat"
                        autocomplete="off"
                        maxlength="5"
                        :placeholder="$t('lbs.propertyFlat')"
                      />
                    </el-form-item>
                  </div>
                </div>
                <p class="title2">{{ $t('lbs.SolicitorsInformation') }}</p>
                <div class="info-item">
                  <el-form-item :label="$t('lbs.solicitorsFirm')" prop="solicitorsFirm" :label-width="formLabelWidth">
                    <el-input
                      v-model="MortgageForm.solicitorsFirm"
                      autocomplete="off"
                      maxlength="140"
                      :placeholder="$t('lbs.solicitorsFirm')"
                    />
                  </el-form-item>
                  <el-form-item
                    :label="$t('lbs.solicitorsContactPerson')"
                    prop="solicitorsContactPerson"
                    :label-width="formLabelWidth"
                  >
                    <el-input
                      v-model="MortgageForm.solicitorsContactPerson"
                      autocomplete="off"
                      maxlength="140"
                      :placeholder="$t('lbs.solicitorsContactPerson')"
                    />
                  </el-form-item>
                  <el-form-item
                    :label="$t('lbs.solicitorsPhoneNumber')"
                    prop="solicitorsPhoneNumber"
                    :label-width="formLabelWidth"
                  >
                    <el-input
                      v-model="MortgageForm.solicitorsPhoneNumber"
                      autocomplete="off"
                      maxlength="34"
                      :placeholder="$t('lbs.solicitorsPhoneNumber')"
                    />
                  </el-form-item>
                  <el-form-item
                    :label="$t('lbs.solicitorsFaxNumber')"
                    prop="solicitorsFaxNumber"
                    :label-width="formLabelWidth"
                  >
                    <el-input
                      v-model="MortgageForm.solicitorsFaxNumber"
                      autocomplete="off"
                      maxlength="34"
                      :placeholder="$t('lbs.solicitorsFaxNumber')"
                    />
                  </el-form-item>
                </div>
              </el-form>
            </div>
            <el-button type="primary" style="float: right;margin-bottom: 20px" @click="NextStep(token)">
              {{ $t('lbs.Submit') }}
            </el-button>
          </el-card>
        </div>
      </el-col>
    </el-row>
    <auth v-if="showAuth" @redirect="retry" @hideAuth="hideAuth" />
  </div>
</template>

<script>
import options from './addressJson'
import auth from '../auth.vue'
import axios from 'axios'

export default {
  components: {
    auth
  },
  data: () => ({
    showAuth: false,
    token: null,
    loading: false,
    options,
    maxLoanAmount: '',
    loanAccount: '',
    loanAccountList: [],
    debitAccountList: [
      { label: 'Saving Account', options: [] },
      { label: 'Current Account', options: [] }
    ],
    personalInformation: {
      firstname: '',
      lastname: '',
      emailaddress: '',
      mobilephonenumber: '',
      monthlysalary: ''
    },
    CurrencyList: [],
    formLabelWidth: '260px',
    MortgageForm: {
      accountnumber: '',
      borringneeds: 0,
      ccyCode: 'HKD',
      debitaccountnumber: '',
      loanScheme: 'F',
      monthlysalary: 0,
      propertyAddressFormat: 'S',
      propertyAddressLine1: '',
      propertyAddressLine2: '',
      propertyAddressLine3: '',
      propertyAddressLine4: '',
      propertyAreaCode: '852',
      propertyBlock: '',
      propertyBuilding: '',
      propertyCarparkNumber: '',
      propertyClassification: 'F',
      propertyCountry: 'HK',
      propertyEstate: '',
      propertyFlat: '',
      propertyFloor: '',
      propertyMonthlyRental: '',
      propertyPostCode: '',
      propertyRoom: '',
      propertyStreetName: '',
      propertyStreetNumber: '',
      propertyTransactionStatus: 'V',
      propertyType: 'R',
      propertyWithCarpark: 'N',
      propertyWithGarden: 'N',
      propertyWithRoof: 'N',
      purchasePrice: 0,
      repaymentCycle: 'M',
      repaymentPeriod: 0,
      repaymentPlan: 'L',
      solicitorsContactPerson: '',
      solicitorsFaxNumber: '',
      solicitorsFirm: '',
      solicitorsPhoneNumber: ''
    },
    MortgageFormRules: {},
    AddressFormatOptions: [],
    ClassificationOptions: [],
    TypeOptions: [],
    TransactionStatusOptions: [],
    BooleanOptions: [],
    loanSchemeOptions: [],
    RepaymentPlanOptions: [],
    RepaymentCycleOptions: [],
    debitAccountNumberOptions: [],
    customernumber: ''
  }),
  computed: {
    language() {
      return this.$store.state.app.language
    }
  },
  watch: {
    'MortgageForm.propertyTransactionStatus'(newValue, oldValue) {
      if (newValue === 'V') {
        this.MortgageForm.propertyMonthlyRental = ''
      }
    },
    'MortgageForm.propertyWithCarpark'(newValue, oldValue) {
      if (newValue !== 'Y') {
        this.MortgageForm.propertyCarparkNumber = ''
      }
    },
    'MortgageForm.propertyCountry'(newValue, oldValue) {
      for (let i = 0; i < this.options.length; i++) {
        if (newValue === this.options[i].short) {
          this.MortgageForm.propertyAreaCode = this.options[i].tel
        }
      }
    },
    'MortgageForm.repaymentPeriod'(newValue, oldValue) {
      if (this.MortgageForm.monthlysalary > 0) { this.loanCalculator() }
    },
    'MortgageForm.borringneeds'(newValue) {
      const maxLoanAmount = this.maxLoanAmount.substring(4).replace(/,/g, '')
      if (Number(newValue) > Number(maxLoanAmount)) {
        this.MortgageForm.borringneeds = parseInt(maxLoanAmount).toString()
      }
    },
    'language'() {
      this.initOptions()
    }
  },
  created() {
    this.customernumber = window.sessionStorage.getItem('targetCustomerNumber')
    this.token = window.sessionStorage.getItem('token')
    const loanAccountList = JSON.parse(window.sessionStorage.getItem('loanaccountlist'))
    const savingAccountList = JSON.parse(
      window.sessionStorage.getItem('savingaccountlist')
    )
    const currentaccountlist = JSON.parse(
      window.sessionStorage.getItem('currentaccountlist')
    )
    for (let i = 0; i < loanAccountList.length; i++) {
      if (loanAccountList[i].accountStatus === 'A') {
        this.loanAccountList.push({
          label: loanAccountList[i].accountNumber,
          value: loanAccountList[i].accountNumber
        })
      }
    }
    for (var i = 0; i < savingAccountList.length; i++) {
      if (savingAccountList[i].accountStatus === 'A') {
        this.debitAccountList[0].options.push({
          label: savingAccountList[i].accountNumber,
          value: savingAccountList[i].accountNumber
        })
      }
    }
    for (var j = 0; j < currentaccountlist.length; j++) {
      if (currentaccountlist[j].accountStatus === 'A') {
        this.debitAccountList[1].options.push({
          label: currentaccountlist[j].accountNumber,
          value: currentaccountlist[j].accountNumber
        })
      }
    }
    if (this.debitAccountList && this.debitAccountList[0] && this.debitAccountList[0].options && this.debitAccountList[0].options[0]) {
      this.MortgageForm.debitaccountnumber = this.debitAccountList[0].options[0].value
    }
    if (this.loanAccountList && this.loanAccountList[0]) {
      this.MortgageForm.accountnumber = this.loanAccountList[0].value
    }
    this.initOptions()
    this.getPersonalInformation()
    this.getCurrencyList()
  },
  mounted() {
    if (window.sessionStorage.getItem('showTips') === 'true' && window.sessionStorage.getItem('token')) {
      this.$alert(this.$t('lbs.popupapplymortgage'), this.$t('lbs.Tip'), {
        dangerouslyUseHTMLString: true
      })
    }
  },
  methods: {
    retry(param) {
      this.NextStep(param)
    },
    hideAuth() {
      this.showAuth = false
    },
    initOptions() {
      this.AddressFormatOptions = [
        { label: this.$t('lbs.StructureFormat'), value: 'S' },
        { label: this.$t('lbs.FreeFormat'), value: 'F' }
      ]
      this.ClassificationOptions = [
        { label: this.$t('lbs.FirstHand'), value: 'F' },
        { label: this.$t('lbs.SecondHand'), value: 'S' }
      ]
      this.TypeOptions = [
        { label: this.$t('lbs.Residential'), value: 'R' },
        { label: this.$t('lbs.CarPark'), value: 'C' },
        { label: this.$t('lbs.Villa'), value: 'V' },
        { label: this.$t('lbs.Village'), value: 'T' },
        { label: this.$t('lbs.Office'), value: 'O' },
        { label: this.$t('lbs.Shop'), value: 'S' },
        { label: this.$t('lbs.Industrial'), value: 'I' },
        { label: this.$t('lbs.House'), value: 'H' }
      ]
      this.TransactionStatusOptions = [
        { label: this.$t('lbs.Vacant'), value: 'V' },
        { label: this.$t('lbs.WithTenancyAgreement'), value: 'T' }
      ]
      this.BooleanOptions = [
        { label: this.$t('lbs.Yes'), value: 'Y' },
        { label: this.$t('lbs.No'), value: 'N' }
      ]
      this.loanSchemeOptions = [
        { label: this.$t('lbs.FixedRatePlan'), value: 'F' },
        { label: this.$t('lbs.HiborMortgagePlan'), value: 'H' },
        { label: this.$t('lbs.HKDPrimeRateMortgagePlan'), value: 'P' }
      ]
      this.RepaymentPlanOptions = [
        { label: this.$t('lbs.StraightLineRepaymentPlan'), value: 'L' },
        { label: this.$t('lbs.EqualPrincipalRepaymentPlan'), value: 'P' },
        { label: this.$t('lbs.StepUpRepaymentPlan'), value: 'U' }
      ]
      this.RepaymentCycleOptions = [
        { label: this.$t('lbs.Monthly'), value: 'M' },
        { label: this.$t('lbs.BiWeekly'), value: 'B' }
      ]
      this.MortgageFormRules = {
        borringneeds: [
          { required: true, message: this.$t('lbs.MortgageTip4'), trigger: 'change' },
          {
            pattern: /^\+?[1-9]\d*$/,
            message: this.$t('lbs.MortgageTip2'),
            trigger: 'change'
          }
        ],
        purchasePrice: [
          { required: true, message: this.$t('lbs.MortgageTip5'), trigger: 'change' },
          {
            pattern: /^\+?[1-9]\d*$/,
            message: this.$t('lbs.MortgageTip2'),
            trigger: 'change'
          }
        ],
        ccyCode: [
          { required: true, message: this.$t('lbs.MortgageTip6'), trigger: 'change' }
        ],
        loanScheme: [
          { required: true, message: this.$t('lbs.MortgageTip7'), trigger: 'change' }
        ],
        debitaccountnumber: [
          { required: true, message: this.$t('lbs.MortgageTip8'), trigger: 'change' }
        ],
        accountnumber: [
          { required: true, message: this.$t('lbs.MortgageTip9'), trigger: 'change' }
        ],
        monthlysalary: [
          { required: true, message: this.$t('lbs.MortgageTip10'), trigger: 'change' },
          {
            pattern: /^\+?[1-9]\d*$/,
            message: this.$t('lbs.MortgageTip2'),
            trigger: 'change'
          }
        ],
        propertyAddressFormat: [
          { required: true, message: this.$t('lbs.MortgageTip11'), trigger: 'change' }
        ],
        propertyAreaCode: [
          { required: true, message: this.$t('lbs.MortgageTip12'), trigger: 'change' }
        ],
        propertyBlock: [
          { required: true, message: this.$t('lbs.MortgageTip13'), trigger: 'change' }
        ],
        propertyBuilding: [
          { required: true, message: this.$t('lbs.MortgageTip14'), trigger: 'change' }
        ],
        propertyClassification: [
          { required: true, message: this.$t('lbs.MortgageTip15'), trigger: 'change' }
        ],
        propertyCountry: [
          { required: true, message: this.$t('lbs.MortgageTip16'), trigger: 'change' }
        ],
        propertyEstate: [
          { required: true, message: this.$t('lbs.MortgageTip17'), trigger: 'change' }
        ],
        propertyFlat: [
          { required: true, message: this.$t('lbs.MortgageTip18'), trigger: 'change' }
        ],
        propertyFloor: [
          { required: true, message: this.$t('lbs.MortgageTip19'), trigger: 'change' }
        ],
        propertyPostCode: [
          { required: true, message: this.$t('lbs.MortgageTip20'), trigger: 'change' }
        ],
        propertyRoom: [
          { required: true, message: this.$t('lbs.MortgageTip21'), trigger: 'change' }
        ],
        propertyStreetName: [
          { required: true, message: this.$t('lbs.MortgageTip22'), trigger: 'change' }
        ],
        propertyStreetNumber: [
          { required: true, message: this.$t('lbs.MortgageTip23'), trigger: 'change' },
          { pattern: /^[0-9]*$/, message: this.$t('lbs.MortgageTip24'), trigger: 'change' }
        ],
        propertyTransactionStatus: [
          { required: true, message: this.$t('lbs.MortgageTip25'), trigger: 'change' }
        ],
        propertyType: [
          { required: true, message: this.$t('lbs.MortgageTip26'), trigger: 'change' }
        ],
        propertyWithCarpark: [
          { required: true, message: this.$t('lbs.MortgageTip27'), trigger: 'change' }
        ],
        propertyWithGarden: [
          { required: true, message: this.$t('lbs.MortgageTip27'), trigger: 'change' }
        ],
        propertyWithRoof: [
          { required: true, message: this.$t('lbs.MortgageTip27'), trigger: 'change' }
        ],
        repaymentCycle: [
          { required: true, message: this.$t('lbs.MortgageTip30'), trigger: 'change' }
        ],
        repaymentPeriod: [
          { required: true, message: this.$t('lbs.MortgageTip31'), trigger: 'change' }
        ],
        repaymentPlan: [
          { required: true, message: this.$t('lbs.MortgageTip32'), trigger: 'change' }
        ],
        solicitorsContactPerson: [
          { required: true, message: this.$t('lbs.MortgageTip33'), trigger: 'change' }
        ],
        solicitorsFaxNumber: [
          { required: true, message: this.$t('lbs.MortgageTip34'), trigger: 'change' }
        ],
        solicitorsFirm: [
          { required: true, message: this.$t('lbs.MortgageTip35'), trigger: 'change' }
        ],
        solicitorsPhoneNumber: [
          { required: true, message: this.$t('lbs.MortgageTip36'), trigger: 'change' }
        ]
      }
    },
    //  贷款账号的获取个人信息
    getPersonalInformation() {
      const _this = this
      if (_this.loanAccountList.length === 0 || !_this.loanAccountList[0].value) return
      _this.loading = true
      axios.post(`${_this.LBSGateway}/loan-experience/mortgage/accountDetailEnquiry`, { accountnumber: _this.loanAccountList[0].value }, { headers: { token: _this.token, customerNumber: _this.customernumber }})
        .then(response => {
          _this.loading = false
          if (response.data.code === '200') {
            _this.personalInformation = response.data.data.customerInfo
            _this.MortgageForm.monthlysalary = _this.personalInformation.monthlysalary
            _this.loanCalculator()
          }
        })
        .catch(error => {
          _this.loading = false
          _this.$mui.alert(error.message, _this.$t('lbs.common.error'), _this.$t('lbs.common.confirm'))
        })
    },
    getCurrencyList() {
      const vue = this
      vue.loading = true
      axios.get(
        `${vue.LBSGateway}/sysadmin-experience/sysadmin/currency/currencyTypeRetrieval`, { headers: { token: vue.token, customerNumber: vue.customernumber }})
        .then(response => {
          vue.loading = false
          if (response.data.code === '200') {
            vue.CurrencyList = response.data.data
          }
        })
        .catch(error => {
          vue.loading = false
          vue.$mui.alert(error.message, vue.$t('lbs.common.error'), vue.$t('lbs.common.confirm'))
        })
    },
    loanCalculator() {
      const _this = this
      _this.loading = true
      axios.post(`${_this.LBSGateway}/loan-experience/mortgage/loanCalculator`, {
        ccyCode: _this.MortgageForm.ccyCode,
        loanPeriod: _this.MortgageForm.repaymentPeriod,
        monthlysalary: _this.MortgageForm.monthlysalary
      }, { headers: { token: _this.token, customerNumber: _this.customernumber }})
        .then(response => {
          _this.loading = false
          if (response.data.code === '200') {
            _this.maxLoanAmount = response.data.data.ccyCode + ' ' + _this.$lbs.decimal_format(parseInt(response.data.data.maxLoanAmount))
          }
        })
        .catch(error => {
          _this.loading = false
          _this.$mui.alert(error.message, _this.$t('lbs.common.error'), _this.$t('lbs.common.confirm'))
        })
    },
    NextStep(token) {
      const _this = this
      _this.$refs['MortgageForm'].validate((valid) => {
        if (valid) {
          _this.loading = true
          axios.post(`${_this.LBSGateway}/loan-experience/mortgage/mortgageLoanApplication`, _this.MortgageForm, { headers: { token: token, customerNumber: _this.customernumber }})
            .then(response => {
              _this.loading = false
              if (response.data.code === '200') {
                _this.$mui.alert(_this.$t('lbs.TransactionSuccess'), _this.$t('lbs.common.success'), _this.$t('lbs.common.confirm'), function() {
                  _this.$router.push({ path: '/tellerlbsmortgage/finishmortgage' })
                })
              }
            })
            .catch(error => {
              _this.loading = false
              if (error.body.code === '403005') {
                _this.$mui.alert(_this.$t('lbs.common.limitTip'), _this.$t('lbs.common.warning'), _this.$t('lbs.common.confirm'), function() {
                  _this.showAuth = true
                })
              } else {
                _this.$mui.alert(error.body.message, _this.$t('lbs.common.error'), _this.$t('lbs.common.confirm'))
              }
            })
        } else {
          _this.$message.warning(_this.$t('lbs.MortgageTip0'), _this.$t('lbs.common.warning'))
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>

  .applymortgage {

    .title {
      padding: 30px 28px;
      height: 140px;
      background-color: #FFFFFF;
      margin: 0 25px 0 25px;
      border-radius: 50px;
      background-image: url("../../components/title-bg.png");
      background-repeat: no-repeat;
      background-size: 574px 130px;
      background-position: right;
      box-shadow: 5px 5px 5px #bbbbbb;
    }

    p {
      color: #707070;
      margin-bottom: 0;
      line-height: 1;
    }

    .max-title {
      font-size: 50px;
      margin-bottom: 40px;
      margin-top: 10px;
      font-weight: 500;
      color: #22C1E6;
    }

    .title2 {
      font-size: 25px;
      color: #22C1E6;
      margin-bottom: 20px;
    }

    .main {
      padding: 20px 100px 60px 60px;
      width: 65%;
      line-height: 40px;
      margin: 0 auto;

      .left, .right {
        display: inline-block;
        font-size: 14px;
        color: #606266;
        font-weight: 700;
        width: 280px;
      }

      .left {
        margin-left: 40px;
      }

      .right {
        float: right;
      }

      .information {
        margin-bottom: 20px;
        border-bottom: 1px #cccccc solid;
      }

      .info-item {
        margin: 0 36px;
      }
    }

  }

</style>

<style>
  .applymortgage .el-radio {
    margin-top: 8px;
  }

  .applymortgage .mortgage-input-select .el-input__icon {
    height: 30px;
  }

  .applymortgage .mortgage-input-select .el-input__inner {
    margin-bottom: 0;
    height: 30px;
  }
</style>
