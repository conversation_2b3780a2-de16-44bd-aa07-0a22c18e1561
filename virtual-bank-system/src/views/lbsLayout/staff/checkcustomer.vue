<template>
  <div class="edituserinfo">
    <div class="text-center" style="text-align:center;">
      <div style="display:inline-block;">
        <el-input
          v-model="customernumber"
          :placeholder="$t('Workflow.CustomerNumber')"
          clearable
        />
      </div>
      <el-button icon="el-icon-search" @click="getaccoutlist()" />
    </div>
    <div v-if="hasData" class="main">
      <div>
        <el-row>
          <el-col :span="12" class="userinfo">
            <el-row>
              <el-col :lg="{span:24}" :xs="24">
                <p class="key">{{ $t('lbs.name') }}</p>
                <p class="value">{{ userInfo.customerName }}</p>
              </el-col>
              <el-col :lg="{span:24}" :xs="24">
                <p class="key">{{ $t('lbs.chineseName') }}</p>
                <p class="value">{{ userInfo.chinesename }}</p>
              </el-col>
              <el-col :lg="{span:24}" :xs="24">
                <p class="key editinput">ID</p>
                <p class="value">{{ userInfo.customerid }}</p>
              </el-col>
              <el-col :lg="{span:24}" :xs="24">
                <p class="key">{{ $t('lbs.gender') }}</p>
                <p class="value">{{ userInfo.gender }}</p>
              </el-col>
              <el-col :lg="{span:24}" :xs="24">
                <p class="key">{{ $t('lbs.nationality') }}</p>
                <p class="value">{{ userInfo.issuecountry }}</p>
              </el-col>
              <el-col :lg="{span:24}" :xs="24">
                <p class="key">{{ $t('lbs.birthDay') }}</p>
                <p class="value">{{ $moment.parseZone(Number(userInfo.dateofbirth)).local().format("YYYY-MM-DD") }}</p>
              </el-col>
            </el-row>
          </el-col>
          <el-col :span="12" class="userinfo" style="padding-top: 0">
            <el-row>
              <el-col :lg="{span:24,offset:1}" :xs="24">
                <p class="key editinput">{{ $t('lbs.mobilePhoneNumber') }}</p>
                <span style="display: inline-block"><el-input
                  v-model="userInfo.mobilephonenumber"
                  placeholder="Mobile Phone Number"
                  clearable
                /></span>
              </el-col>
              <el-col :lg="{span:24,offset:1}" :xs="24">
                <p class="key editinput">{{ $t('lbs.residentialAddress') }}</p>
                <span style="display: inline-block"><el-input
                  v-model="userInfo.residentialaddress"
                  placeholder="Mobile Phone Number"
                  clearable
                /></span>
              </el-col>
              <el-col :lg="{span:24,offset:1}" :xs="24">
                <p class="key editinput">{{ $t('lbs.mailingAddress') }}</p>
                <span style="display: inline-block"><el-input
                  v-model="userInfo.mailingaddress"
                  placeholder="Mobile Phone Number"
                  clearable
                /></span>
              </el-col>
              <el-col :lg="{span:24,offset:1}" :xs="24">
                <p class="key editinput">{{ $t('lbs.residencePhoneNumber') }}</p>
                <span style="display: inline-block"><el-input
                  v-model="userInfo.residencephonenumber"
                  placeholder="Mobile Phone Number"
                  clearable
                /></span>
              </el-col>
              <el-col :lg="{span:24,offset:1}" :xs="24">
                <p class="key editinput">{{ $t('lbs.companyEmail') }}</p>
                <span style="display: inline-block"><el-input
                  v-model="userInfo.emailaddress"
                  placeholder="Mobile Phone Number"
                  clearable
                /></span>
              </el-col>
              <el-col :lg="{span:24,offset:1}" :xs="24">
                <p class="key">{{ $t('lbs.weChatId') }}</p>
                <p class="value">{{ userInfo.wechatid }}</p>
              </el-col>
            </el-row>
          </el-col>
        </el-row>
      </div>
      <div>
        <el-row>
          <el-col :span="24" class="userinfo" style="padding-top: 0">
            <el-row>
              <el-col :lg="{span:24}" :xs="24">
                <p class="key">{{ $t('lbs.occupation') }}</p>
                <p class="value">{{ userInfo.occupation }}</p>
              </el-col>
              <el-col :lg="{span:24}" :xs="24">
                <p class="key">{{ $t('lbs.employerCompanyName') }}</p>
                <p class="value">{{ userInfo.employercompanyname }}</p>
              </el-col>
              <el-col :lg="{span:24}" :xs="24">
                <p class="key">{{ $t('lbs.position') }}</p>
                <p class="value">{{ userInfo.position }}</p>
              </el-col>
              <el-col :lg="{span:24}" :xs="24">
                <p class="key">{{ $t('lbs.companyAddress') }}</p>
                <p class="value">{{ userInfo.companyaddress }}</p>
              </el-col>
              <el-col :lg="{span:24}" :xs="24">
                <p class="key">{{ $t('lbs.companyPhoneNumber') }}</p>
                <p class="value">{{ userInfo.companyphonenumber }}</p>
              </el-col>
              <el-col :lg="{span:24}" :xs="24">
                <p class="key">{{ $t('lbs.yearsOfServices') }}</p>
                <p class="value">{{ userInfo.yearsofservices }}</p>
              </el-col>
              <el-col :lg="{span:24}" :xs="24">
                <p class="key">{{ $t('lbs.monthlySalary') }}</p>
                <p class="value">{{ userInfo.monthlysalary }}</p>
              </el-col>
            </el-row>
          </el-col>
        </el-row>
      </div>
      <el-row>
        <el-col :span="24" style="text-align:center;">
          <el-button type="primary" style="margin: 0 auto;" @click="update()">{{ $t('lbs.update') }}</el-button>
          <el-button type="danger" style="margin: 0 auto;" @click="sensitive()">{{ $t('lbs.common.sensitive') }}</el-button>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Edituserinfo',
  data() {
    return {
      hasData: false,
      userInfo: {},
      loading: false,
      token: null,
      messageid: '006f7113e5fa48559549c4dfe74e2cd6',
      clientid: 'devin',
      customernumber: ''
    }
  },
  mounted() {
    var needReload = window.sessionStorage.getItem('needReload')
    if (needReload === '1') {
      window.sessionStorage.setItem('needReload', 0)
      window.location.reload()
    }
    this.token = window.sessionStorage.getItem('token')

    // -----------------------------------------------------
    // const params = {
    //   platform: 'Virtual banking system',
    //   userId: window.sessionStorage.getItem('role'),
    //   operationType: '虚拟银行主管角色查询用户'
    // }
    // this.$store.dispatch('app/VisitsFun', params).then((res) => {
    //   this.loading = false
    //   if (res.data.code !== 20000) {
    //     console.log(response.data.message)
    //   }
    // }).catch(error => {
    //   this.loading = false
    //   console.log(error.message)
    // })
    // -----------------------------------------
  },

  methods: {
    // 将用户标记为敏感用户
    sensitive() {
      this.$mui.alert('该功能暂未开放')
    },
    // 获取当前用户account列表
    getaccoutlist() {
      var vue = this
      this.$mui.ajax(this.LBSGateway + '/deposit-experience/deposit/account/allAccounts/' + this.customernumber + '/0/100', {
        dataType: 'json', // 服务器返回json格式数据
        type: 'post', // HTTP请求类型
        timeout: 60000,
        async: false,
        headers: {
          'accept': '*/*',
          'token': vue.token,
          'clientid': vue.clientid,
          'messageid': vue.messageid,
          'Content-Type': 'application/json',
          'customerNumber': vue.customernumber
        },
        beforeSend: function() {
          vue.loading = true
        },
        complete: function() {
        },
        success: function(data) {
          // console.log(JSON.stringify(data))
          if (data.code === '200') {
            window.sessionStorage.setItem('savingaccountlist', JSON.stringify(data.data.saving))
            window.sessionStorage.setItem('currentaccountlist', JSON.stringify(data.data.current))
            window.sessionStorage.setItem('termDepositaccountlist', JSON.stringify(data.data.termDeposit))
            window.sessionStorage.setItem('fexaccountlist', JSON.stringify(data.data.fex))
            window.sessionStorage.setItem('stockaccountlist', JSON.stringify(data.data.stock))
            window.sessionStorage.setItem('mutualFundaccountlist', JSON.stringify(data.data.mutualFund))
            window.sessionStorage.setItem('preciousMetalaccountlist', JSON.stringify(data.data.preciousMetal))
            window.sessionStorage.setItem('creditCardaccountlist', JSON.stringify(data.data.creditCard))
            window.sessionStorage.setItem('loanaccountlist', JSON.stringify(data.data.loan))
            vue.initUserInfo(vue.getcurrentnumberpickerdata()[0].value)
          } else if (data.code === '404002') {
            console.log(123)
          } else {
            vue.$mui.alert('Get accout list failed! The response is: \n' + JSON.stringify(data) + '.', 'Error', 'OK')
          }
        },
        error: function(xhr, type, errorThrown) {
          console.log(type)
          var msg = 'Get accout list failed! The response is: \n' + xhr.responseText + '.'
          if (type === 'timeout') {
            msg = 'Get accout list failed. Time out!'
          }
          vue.$mui.alert(msg, 'Error', 'OK')
        }
      })
    },
    initUserInfo(account) {
      var vue = this
      this.$mui.ajax(this.LBSGateway + '/deposit-experience/deposit/account/accountDetails/' + account, {
        dataType: 'json', // 服务器返回json格式数据
        type: 'post', // HTTP请求类型
        timeout: 60000,
        headers: {
          'accept': '*/*',
          'token': vue.token,
          'clientid': vue.clientid,
          'messageid': vue.messageid,
          'Content-Type': 'application/json',
          'customerNumber': vue.customernumber
        },
        beforeSend: function() {
          // plus.nativeUI.showWaiting("Loading…", "div");
          // mask.show();
          // document.getElementById("load").style.display = "inline";
        },
        complete: function() {
          vue.loading = false
          // plus.nativeUI.closeWaiting();
          // mask.close();
          // document.getElementById("load").style.display = "none";
        },
        success: function(data) {
          if (data.code === '200') {
            var response = data.data
            window.sessionStorage.setItem('userInfo', JSON.stringify(response.customer))

            vue.userInfo = response.customer
            vue.hasData = true
            window.sessionStorage.setItem('targetCustomerNumber', vue.customernumber)
          } else {
            vue.hasData = false
            vue.$mui.alert('Get user info failed! The response is: \n' + JSON.stringify(data) + '.', 'Error', 'OK')
          }
        },
        error: function(xhr, type, errorThrown) {
          vue.hasData = false
          console.log(type)
          var msg = 'Get user info failed! The response is: \n' + xhr.responseText + '.'
          if (type === 'timeout') {
            msg = 'Get user info failed. Time out!'
          }
          vue.$mui.alert(msg, 'Error', 'OK')
        }
      })
    },
    getcurrentnumberpickerdata() {
      var list = window.sessionStorage.getItem('currentaccountlist')
      var accountlist = JSON.parse(list)
      var pickerdata = []
      for (var i = 0; i < accountlist.length; i++) {
        if (accountlist[i].accountStatus === 'A') {
          pickerdata.push({
            label: accountlist[i].accountNumber,
            value: accountlist[i].accountNumber
          })
        }
      }
      if (pickerdata.length === 0) {
        list = window.sessionStorage.getItem('savingaccountlist')
        accountlist = JSON.parse(list)
        pickerdata = []
        for (var j = 0; j < accountlist.length; j++) {
          if (accountlist[j].accountStatus === 'A') {
            pickerdata.push({
              label: accountlist[j].accountNumber,
              value: accountlist[j].accountNumber
            })
          }
        }
      }
      return pickerdata
    },
    getparams() {
      if (!this.userInfo.customerid || this.userInfo.customerid.length === 0) {
        this.$mui.alert('Please enter the Customer ID.')
        return false
      }
      if (!this.userInfo.emailaddress || this.userInfo.emailaddress.length === 0) {
        this.$mui.alert('Please enter the Email Address.')
        return false
      }
      if (!this.userInfo.mailingaddress || this.userInfo.mailingaddress.length === 0) {
        this.$mui.alert('Please enter the Mailing Address.', 'Warning', 'OK')
        return false
      }
      if (!this.userInfo.mobilephonenumber || this.userInfo.mobilephonenumber.length === 0) {
        this.$mui.alert('Please enter the Mobile Phone Number.', 'Warning', 'OK')
        return false
      }
      if (!this.userInfo.residencephonenumber || this.userInfo.residencephonenumber.length === 0) {
        this.$mui.alert('Please enter the Residence Phone Number.', 'Warning', 'OK')
        return false
      }
      if (!this.userInfo.residentialaddress || this.userInfo.residentialaddress.length === 0) {
        this.$mui.alert('Please enter the Residential Address.', 'Warning', 'OK')
        return false
      }
      return {
        'customerID': this.userInfo.customerid,
        'emailaddress': this.userInfo.emailaddress,
        'mailingAddress': this.userInfo.mailingaddress,
        'mobilePhoneNumber': this.userInfo.mobilephonenumber,
        'residencephonenumber': this.userInfo.residencephonenumber,
        'residentialaddress': this.userInfo.residentialaddress
      }
    },
    deposit(requestdata) {
      var vue = this
      var token = window.sessionStorage.getItem('token')
      this.$mui.ajax(this.LBSGateway + '/deposit-experience/customer/contactInfoUpdate', {
        data: requestdata,
        dataType: 'json', // 服务器返回json格式数据
        type: 'post', // HTTP请求类型
        timeout: 60000,
        headers: {
          'accept': '*/*',
          'token': token,
          'clientid': vue.$parent.clientid,
          'messageid': vue.$parent.messageid,
          'Content-Type': 'application/json',
          'customerNumber': vue.customernumber
        },
        beforeSend: function() {
          // plus.nativeUI.showWaiting("Loading…", "div");
          // mask.show();
          // document.getElementById("load").style.display = "inline";
        },
        complete: function() {
          // plus.nativeUI.closeWaiting();
          // mask.close();
          // document.getElementById("load").style.display = "none";
        },
        success: function(data) {
          if (data.code === '200') {
            vue.$mui.alert('Update Successed!', 'Success', 'OK', function() {
              window.sessionStorage.setItem('userInfo', JSON.stringify(vue.userInfo))
            })
          }
        },
        error: function(xhr, type, errorThrown) {
          console.log(type)
          var msg = 'Update user info failed! The response is: \n' + xhr.responseText + '.'
          if (type === 'timeout') {
            msg = 'Update user info failed. Time out!'
          }
          vue.$mui.alert(msg, 'Error', 'OK')
        }
      })
    },
    update() {
      var requestdata = this.getparams()
      if (!requestdata) return false
      this.deposit(requestdata)
    }
  }
}
</script>

<style scoped>
  .edituserinfo .title {
    padding: 30px 28px;
    height: 140px;
    background-color: #FFFFFF;
    margin: 0 25px 0 25px;
    border-radius: 50px;
    background-image: url("../components/title-bg.png");
    background-repeat: no-repeat;
    background-size: 574px 130px;
    background-position: right;
    box-shadow: 5px 5px 5px #bbbbbb;
  }

  .edituserinfo p {
    color: #707070;
    margin-bottom: 0;
    line-height: 1;
  }

  .edituserinfo .max-title {
    color: #22C1E6;
    font-size: 50px;
    margin-bottom: 40px;
    line-height: 80px;
    font-weight: 500;
  }

  .edituserinfo .main {
    margin: 40px 100px;
    border-bottom: 1px #cccccc solid;
    padding: 10px 0 40px
  }

  .edituserinfo .title2 {
    text-align: center;
    color: #707070;
    font-size: 40px;
    margin-bottom: 40px;
  }

  .edituserinfo .image {
    width: 308px;
    margin: 0 auto;
  }

  .edituserinfo .userinfo p{
    font-size: 25px;
    line-height: 53px;
    display: inline-block;
  }

  .edituserinfo .userinfo .value{
    font-size: 22px;
    color: #000000;
  }
  .edituserinfo .key{
    width: 300px;
  }

  .edituserinfo .editinput {
    display: inline;
  }
</style>

<style>
  .edituserinfo .el-input--suffix .el-input__inner {
    width: 300px;
    padding-right: 25px;
    margin-bottom: 0;
  }
</style>
