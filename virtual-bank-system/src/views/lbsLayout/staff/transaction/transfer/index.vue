<template>
  <div v-loading.fullscreen.lock="loading">
    <el-row class="makepayment">
      <el-col :psan="24">
        <el-row>
          <el-col :span="24">
            <div class="title">
              <p style="display: inline-block" class="max-title">{{ $t("lbs.router.transfer") }}</p>
            </div>
          </el-col>
        </el-row>
      </el-col>
    </el-row>
    <el-row class="makepaymentindex">
      <el-col :span="9" :offset="2">
        <div style="float: left;margin-right: 20px">
          <div class="el-step__icon is-text is-ing" style="margin-top: 8px">
            <div class="el-step__icon-inner">1</div>
          </div>
          <div style="border-right: 2px #707070 solid;height: 190px;width: 13px" />
          <div class="el-step__icon is-text no-finish">
            <div class="el-step__icon-inner">2</div>
          </div>
          <div style="border-right: 2px #707070 solid;height: 150px;width: 13px" />
          <div class="el-step__icon is-text no-finish">
            <div class="el-step__icon-inner">3</div>
          </div>
          <div style="border-right: 2px #707070 solid;height: 170px;width: 13px" />
          <div class="el-step__icon is-text no-finish">
            <div class="el-step__icon-inner">4</div>
          </div>
        </div>
        <div class="right" style="width:630px;">
          <p class="text-title is-ing">{{ $t('lbsTips.paymentTip.tip1') }}</p>
          <el-tooltip class="item" effect="dark" placement="top">
            <div slot="content" style="width: 300px">{{ $t('lbsTips.paymentTip.Account') }}</div>
            <div style="padding: 43px 0 43px">
              <p class="text-account">{{ $t('lbsTips.paymentTip.tip13') }}</p>
              <el-select slot="prepend" v-model="accountNumber" placeholder="" style="width: 350px;" @change="getAccountBalance">
                <el-option
                  v-for="(item,index) in accountList"
                  :key="index"
                  :label="item.type + ' ' + item.label"
                  :value="item.label"
                />
              </el-select>
              <div style="padding-left: 185px; margin-top: 5px;color: #666;font-size: 12px;">{{ fromAccountBalance }}</div>
            </div>
          </el-tooltip>
          <p class="text-title is-ing">{{ $t('lbsTips.paymentTip.tip2') }}</p>
          <el-tooltip class="item" effect="dark" placement="top">
            <div slot="content" style="width: 300px">{{ $t('lbsTips.accountDetailTip.toAccount') }}</div>
            <div style="padding: 43px 0 43px">
              <p class="text-account">{{ $t('lbsTips.paymentTip.tip13') }}</p>
              <el-select
                v-model="toAccount"
                filterable
                allow-create
                style="width: 350px;"
                default-first-option
              >
                <el-option
                  v-for="(item,index) in accountList"
                  :key="index"
                  :label="item.type + ' ' + item.label"
                  :value="item.label"
                />
              </el-select>
            </div>
          </el-tooltip>
          <p class="text-title is-ing">{{ $t('lbsTips.paymentTip.tip4') }}</p>
          <el-tooltip class="item" effect="dark" placement="top">
            <div slot="content" style="width: 300px">{{ $t('lbsTips.paymentTip.Amount') }}</div>
            <div style="padding: 43px 0 43px">
              <p class="text-account">{{ $t('lbsTips.paymentTip.tip5') }}</p>
              <el-input
                v-model="amount"
                :placeholder="$t('lbsTips.paymentTip.tip11')"
                class="input-with-select"
                style="width: 350px"
                maxlength="50"
              >
                <el-select slot="prepend" v-model="currency" placeholder="" style="width: 80px">
                  <el-option label="HKD" value="HKD" />
                </el-select>
              </el-input>
            </div>
          </el-tooltip>
          <br>
          <p class="text-title">{{ $t('lbsTips.paymentTip.tip8') }}</p>
          <div style="width: 800px;text-align: center;">
            <el-tooltip class="item" effect="dark" placement="top">
              <div slot="content" style="width: 300px">{{ $t('lbsTips.paymentTip.tip47') }}</div>
              <el-button
                class="button"
                style="background-color: #109eae;color: #ffffff"
                icon="el-icon-check"
                @click="submit('next')"
              >{{ $t('lbsTips.paymentTip.tip9') }}
              </el-button>

            </el-tooltip>
          </div>
        </div>
      </el-col>
      <el-col :span="12" :offset="1">
        <div style="margin: 0 20px">
          <p style="font-size: 30px;color: #22C1E6;margin-bottom: 20px">{{ $t('lbs.TransferRecord') }}</p>
          <el-card>
            <el-table :data="tableData" height="500px">
              <el-table-column width="160" prop="trandate" :label="$t('lbs.trandate')" />
              <el-table-column width="110" prop="tranamt" :label="$t('lbs.tranamt')" />
              <el-table-column width="85" prop="ccy" :label="$t('lbs.ccy')" />
              <el-table-column prop="trandesc" :label="$t('lbs.trandesc')" />
              <el-table-column prop="actualbalamt" :label="$t('lbsTips.accountDetailTip.AccountBalanceTh')" />
            </el-table>
          </el-card>
        </div>
      </el-col>
    </el-row>
    <auth v-if="showAuth" :auth-list="authList" @redirect="retry" @hideAuth="hideAuth" />
  </div>
</template>

<script>
import auth from '../../auth.vue'
import axios from 'axios'

export default {
  name: 'Makepaymentindex',
  components: {
    auth
  },
  data() {
    return {
      showAuth: false,
      loading: false,
      currency: 'HKD',
      accountNumber: '',
      savingList: [],
      currentList: [],
      accountList: [],
      toAccount: '',
      amount: '',
      tableData: [],
      fromAccountBalance: '',
      customernumber: '',
      authList: []
    }
  },
  watch: {
    'accountNumber'() {
      this.transactionLog()
    }
  },
  mounted() {
    this.customernumber = window.sessionStorage.getItem('targetCustomerNumber')
    if (window.sessionStorage.getItem('showTips') === 'true' && window.sessionStorage.getItem('token')) {
      this.$alert(this.$t('lbs.popupTransfer'), this.$t('lbs.Tip'), {
        dangerouslyUseHTMLString: true
      })
    }
    this.token = window.sessionStorage.getItem('token')
    this.initdata()
    // -----------------------------------------------------
    // const params = {
    //   platform: 'Virtual banking system',
    //   userId: window.sessionStorage.getItem('role'),
    //   operationType: '虚拟银行主管角色虚拟转账'
    // }
    // this.$store.dispatch('app/VisitsFun', params).then((res) => {
    //   this.loading = false
    //   if (res.data.code !== 20000) {
    //     console.log(response.data.message)
    //   }
    // }).catch(error => {
    //   this.loading = false
    //   console.log(error.message)
    // })
    // -----------------------------------------
  },
  methods: {
    retry(param) {
      this.submitPayment(param)
    },
    hideAuth() {
      this.showAuth = false
    },
    initdata() {
      const savingAccountList = JSON.parse(window.sessionStorage.getItem('savingaccountlist'))
      const currentaccountlist = JSON.parse(window.sessionStorage.getItem('currentaccountlist'))
      for (var i = 0; i < savingAccountList.length; i++) {
        if (savingAccountList[i].accountStatus === 'A') {
          this.savingList.push({
            label: savingAccountList[i].accountNumber,
            value: savingAccountList[i].accountNumber,
            type: 'Saving'
          })
        }
      }

      for (var j = 0; j < currentaccountlist.length; j++) {
        if (currentaccountlist[j].accountStatus === 'A') {
          this.currentList.push({
            label: currentaccountlist[j].accountNumber,
            value: currentaccountlist[j].accountNumber,
            type: 'Current'
          })
        }
      }
      this.accountList = this.savingList.concat(this.currentList)
      this.accountNumber = this.accountList[0].label
      this.getAccountBalance()
    },
    getAccountBalance() {
      var vue = this
      vue.$mui.ajax(vue.LBSGateway + '/deposit-experience/deposit/account/accountDetails/' + vue.accountNumber, {
        dataType: 'json', // 服务器返回json格式数据
        type: 'post', // HTTP请求类型
        timeout: 60000,
        headers: {
          'accept': '*/*',
          'token': vue.token,
          'clientid': vue.$parent.clientid,
          'messageid': vue.$parent.messageid,
          'Content-Type': 'application/json',
          'customerNumber': vue.customernumber
        },
        beforeSend: function() {
          vue.loading = true
        },
        complete: function() {
          vue.loading = false
        },
        success: function(data) {
          if (data.code === '200') {
            var data1 = data.data.account
            if (!data1.availablebalance) data1.availablebalance = 0.0
            if (data1.availablebalance) {
              data1.availablebalance = Number(
                data1.availablebalance
                  .toString()
                  .replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3')
              )
            }
            vue.fromAccountBalance = '( ' + vue.$t('lbs.Balance') + ' (' + data1.currencycode + ')：' + data1.availablebalance + ' )'
          } else {
            vue.fromAccountBalance = 0.0
          }
        },
        error: function(xhr, type, errorThrown) {
          console.log(type)
          var msg = 'Transfer failed! The response is: \n' + xhr.responseText + '.'
          if (type === 'timeout') {
            msg = 'Transfer failed. Time out!'
          }
          vue.$mui.alert(msg, 'Error', 'OK')
        }
      })
    },
    submit() {
      if (this.toAccount.length === 0) {
        this.$mui.alert('Please select to account', 'Error', 'OK')
        return false
      }
      if (this.currency.length === 0) {
        this.$mui.alert('Please select currency', 'Error', 'OK')
        return false
      }

      if (this.amount.length === 0) {
        this.$mui.alert('Please enter amount', 'Error', 'OK')
        return false
      }

      this.submitPayment(this.token)
    },
    submitPayment(token) {
      if (this.accountNumber === this.toAccount) {
        this.$message.warning(this.$t('lbs.accountnotsame'), this.$t('lbs.common.warning'))
        return
      }
      var vue = this
      var requesttimedata = {
        'transferAmount': this.amount,
        'transferInAccountNumber': this.toAccount,
        'transferOutAccountNumber': this.accountNumber
      }
      console.log(requesttimedata)
      var btnArray = ['cancel', 'ok']
      this.$mui.confirm('Do you confirm to transfer?', 'Prompt', btnArray, function(e) {
        if (e.index === 1) {
          vue.$mui.ajax(vue.LBSGateway + '/deposit-experience/deposit/account/transfer', {
            data: requesttimedata,
            dataType: 'json', // 服务器返回json格式数据
            type: 'post', // HTTP请求类型
            timeout: 60000,
            headers: {
              'accept': '*/*',
              'token': token,
              'clientid': vue.$parent.clientid,
              'messageid': vue.$parent.messageid,
              'Content-Type': 'application/json',
              'customerNumber': vue.customernumber
            },
            beforeSend: function() {
              vue.loading = true
            },
            complete: function() {
              vue.loading = false
            },
            success: function(data) {
              if (data.code === '200') {
                vue.$mui.alert('Transfer Succeeds.', vue.$t('lbs.common.success'), vue.$t('lbs.common.confirm'))
                setTimeout(function() {
                  console.log('vue.transactionLog()')
                  vue.transactionLog()
                }, 2000)
                vue.getAccountBalance()
                vue.showAuth = false
              } else {
                vue.$mui.alert('Transfer failed! The response is: \n' + JSON.stringify(data) + '.', vue.$t('lbs.common.error'), vue.$t('lbs.common.confirm'))
              }
            },
            error: function(xhr, type, errorThrown) {
              if (JSON.parse(xhr.response).code === '403005') {
                vue.$mui.alert(vue.$t('lbs.common.limitTip'), vue.$t('lbs.common.warning'), vue.$t('lbs.common.confirm'), function() {
                  vue.authList = JSON.parse(xhr.response).data
                  if (vue.authList && vue.authList.length) {
                    vue.showAuth = true
                  } else {
                    vue.$mui.alert(vue.$t('lbsTips.accountDetailTip.overrideLimitMsg'), vue.$t('lbs.common.error'), vue.$t('lbs.common.confirm'))
                  }
                })
              } else {
                console.log(type)
                var msg = 'Transfer failed! The response is: \n' + xhr.responseText + '.'
                if (type === 'timeout') {
                  msg = 'Transfer failed. Time out!'
                }
                vue.$mui.alert(msg, 'Error', 'OK')
              }
            }
          })
        }
      })
    },
    transactionLog() {
      const _this = this
      _this.loading = true
      const requestData = {
        accountnumber: _this.accountNumber,
        index: 1,
        items: 9999,
        transFromDate: ************,
        transToDate: new Date().getTime(),
        trantype: '0005'
      }
      axios.post(`${_this.LBSGateway}/deposit-experience/transactionLog/enquiry`, requestData, { headers: { token: _this.token, customerNumber: _this.customernumber }})
        .then(response => {
          _this.loading = false
          _this.tableData = []
          if (response.data.code === '200') {
            const tableData = response.data.data
            for (let i = 0; i < tableData.length; i++) {
              tableData[i].trandate = _this.$moment(tableData[i].trandate).format('YYYY-MM-DD HH:mm:ss')
              tableData[i].tranamt = _this.$lbs.decimal_format(tableData[i].tranamt)
              tableData[i].actualbalamt = _this.$lbs.decimal_format(tableData[i].actualbalamt)
              tableData[i].trandesc = tableData[i].trandesc.toUpperCase()
            }
            const tempTableData = []
            for (let i = 0; i < tableData.length; i++) {
              if (tableData[i].trandesc === 'TRANSFER OUT') {
                tempTableData.push(tableData[i])
              }
            }
            _this.tableData = tempTableData
          } else if (response.data.code === '404003') {
            _this.$message.success(response.data.msg, _this.$t('lbs.common.success'))
          } else {
            _this.$message.error(response.data.msg, _this.$t('lbs.common.error'))
          }
        })
        .catch(error => {
          _this.loading = false
          _this.$message.error(error.message, _this.$t('lbs.common.error'))
        })
    }
  }
}
</script>

<style scoped>
  .makepayment .title {
    padding: 30px 28px;
    height: 140px;
    background-color: #FFFFFF;
    margin: 0 25px 40px 25px;
    border-radius: 50px;
    background-image: url("../../../components/title-bg.png");
    background-repeat: no-repeat;
    background-size: 574px 130px;
    background-position: right;
    box-shadow: 5px 5px 5px #bbbbbb;
  }

  .makepayment p {
    color: #707070;
    margin-bottom: 0;
    line-height: 1;
  }

  .makepayment .max-title {
    font-size: 50px;
    line-height: 75px;
    font-weight: 500;
    color: #22C1E6;
    display: inline-block;
  }

  .makepaymentindex p {
    line-height: 1;
    color: #707070;
  }

  .makepaymentindex .text-title {
    font-size: 40px;
    color: #707070;
  }

  .makepaymentindex .text-account {
    display: inline-block;
    font-size: 20px;
    margin-right: 100px;
  }

  .makepaymentindex .is-finish {
    color: #109eae;
    border-color: #109eae;
  }

  .makepaymentindex .no-finish {
    color: #707070;
    border-color: #707070;
  }

  .makepaymentindex .is-ing {
    color: #000000;
    border-color: #000000;
  }
</style>

<style>
  .el-message-box {
    width: 800px;
  }

  .makepaymentindex .el-step__title {
    font-size: 40px;
    margin-left: 20px;
    color: #707070;
  }

  .makepaymentindex .el-input__icon {
    height: 40px;
  }

  .makepaymentindex .el-input__inner {
    margin-bottom: 0;
    height: 40px;
  }

  .makepaymentindex .el-checkbox__inner {
    width: 20px;
    height: 20px;
    border-color: #999999;
  }

  .makepaymentindex .el-checkbox__inner::after {
    width: 8px;
    height: 10px;
  }
</style>
<style scoped>
  .makepaymentindex p {
    line-height: 1;
    color: #707070;
  }

  .makepaymentindex .text-title {
    font-size: 40px;
    color: #707070;
  }

  .makepaymentindex .text-account {
    display: inline-block;
    font-size: 20px;
    margin-right: 100px;
  }

  .makepaymentindex .is-finish {
    color: #109eae;
    border-color: #109eae;
  }

  .makepaymentindex .no-finish {
    color: #707070;
    border-color: #707070;
  }

  .makepaymentindex .is-ing {
    color: #000000;
    border-color: #000000;
  }
</style>

<style>
  .makepaymentindex .el-step__title {
    font-size: 40px;
    margin-left: 20px;
    color: #707070;
  }

  .makepaymentindex .el-input__icon {
    height: 40px;
  }

  .makepaymentindex .el-input__inner {
    margin-bottom: 0;
    height: 40px;
  }

  .makepaymentindex .el-checkbox__inner {
    width: 20px;
    height: 20px;
  }

  .makepaymentindex .el-checkbox__inner::after {
    width: 8px;
    height: 10px;
  }

  .makepaymentindex .el-checkbox {
    color: #109eae;
    cursor: default;
  }

  .makepaymentindex .el-checkbox__input.is-disabled.is-checked .el-checkbox__inner {
    background-color: #109eae;
  }

  .makepaymentindex .el-checkbox__input.is-disabled.is-checked .el-checkbox__inner::after {
    border-color: #ffffff;
  }

  .makepaymentindex .el-input.is-disabled .el-input__inner {
    color: #707070;
  }

  .makepaymentindex .el-checkbox__inner {
    width: 20px;
    height: 20px;
    border-color: #999999;
  }
</style>
