<template>
  <div v-loading.fullscreen.lock="loading">
    <el-row class="payeelist">
      <el-col :span="24">
        <div class="title">
          <p style="display: inline-block" class="max-title">{{ $t("lbsTips.paymentTip.tip18") }}</p>
        </div>
      </el-col>
    </el-row>
    <el-row class="mypayment">
      <el-col :span="24">
        <div class="top-card">
          <el-tooltip effect="dark" placement="bottom">
            <div slot="content" style="width: 300px">{{ $t('lbsTips.paymentTip.MerchantCharity') }}</div>
            <div class="card-item" style="position: relative;background-color: #AFF3FF;cursor: default">
              <svg-icon
                icon-class="awesome-warehouse"
                style="width: 30px; height: 30px;position: absolute;top: 18px;left: 20px"
              />
              <p style="margin: 25px 0 26px;padding-left: 40px">{{ $t('lbsTips.paymentTip.tip18') }}</p>
            </div>
          </el-tooltip>
          <el-tooltip effect="dark" placement="bottom">
            <div slot="content" style="width: 300px">{{ $t('lbsTips.paymentTip.Mypayee') }}</div>
            <div class="card-item" style="position: relative;" @click="jumpTopayeelistpage()">
              <svg-icon
                icon-class="material-group"
                style="width: 30px; height: 30px;position: absolute;top: 20px;left: 20px"
              />
              <p style="margin: 25px 0 26px;padding-left: 40px">{{ $t('lbsTips.paymentTip.tip17') }}</p>
            </div>
          </el-tooltip>
          <el-tooltip effect="dark" placement="bottom">
            <div slot="content" style="width: 300px">
              {{ $t('lbsTips.paymentTip.Addpayee') }}
            </div>
            <div class="card-item" style="position: relative;margin-right: 0" @click="jumppage()">
              <svg-icon
                icon-class="material-group-add"
                style="width: 30px; height: 30px;position: absolute;top: 20px;left: 20px"
              />
              <p style="margin: 25px 0 26px;padding-left: 40px">{{ $t('lbsTips.paymentTip.tip20') }}</p>
            </div>
          </el-tooltip>
        </div>
      </el-col>
    </el-row>
    <el-row class="mypayment">
      <el-col :span="24">
        <div>
          <div style="padding: 20px;background-color: #ffffff;margin-top: 40px">
            <div style="height: 50px;">
              <label>{{ $t('lbsTips.paymentTip.tip23') }}: </label>
              <el-select v-model="value" style="width:300px;" @change="initPayeeInfoList(value)">
                <el-option
                  v-for="item in payeeCategoryList"
                  :key="item.payeecategoryid"
                  :label="item.payeecategory"
                  :value="item.payeecategoryid"
                />
              </el-select>
            </div>
            <el-table
              :data="payeelist"
              style="width: 100%;"
            >
              <el-table-column type="index" :label="$t('lbsTips.paymentTip.tip35')" width="50" />
              <el-table-column
                prop="payeename"
                :label="$t('lbsTips.paymentTip.tip27')"
              />
              <el-table-column
                prop="payeecountry"
                :label="$t('lbsTips.paymentTip.tip32')"
              />
              <el-table-column
                prop="payeeaccountnumber"
                :label="$t('lbsTips.paymentTip.tip33')"
              />
              <el-table-column
                prop="phonenumber"
                :label="$t('lbsTips.paymentTip.tip34')"
              />
            </el-table>
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
export default {
  name: 'Mypayee',
  data() {
    return {
      loading: false,
      token: null,
      name: '',
      category: '',
      payeeCategoryList: [],
      multipleSelection: [],
      payeelist: [],
      payeeInfo: [],
      payeeCategoryIds: [],
      tempList: [],
      tempList2: [],
      flag: false,
      payeelistCopy: [],
      flag2: true,
      payeelistTemp: [],
      value: '',
      customernumber: ''
    }
  },
  mounted() {
    this.customernumber = window.sessionStorage.getItem('targetCustomerNumber')
    this.token = window.sessionStorage.getItem('token')
    this.initPayeeCategoryList()
  },
  methods: {
    initPayeeCategoryList() {
      var vue = this
      this.$mui.ajax(this.LBSGateway + '/payment-experience/payment/payeeCategoryList', {
        dataType: 'json', // 服务器返回json格式数据
        type: 'get', // HTTP请求类型
        timeout: 60000,
        headers: {
          'accept': '*/*',
          'token': this.token,
          'clientid': vue.$parent.clientid,
          'messageid': vue.$parent.messageid,
          'Content-Type': 'application/json',
          customerNumber: vue.customernumber
        },
        beforeSend: function() {
          vue.loading = true
        },
        complete: function() {
          vue.loading = false
        },
        success: function(data) {
          console.log(data.data)
          // Page show
          if (data.code === '200') {
            vue.payeeCategoryList = data.data
            vue.value = vue.payeeCategoryList[0].payeecategoryid
            vue.initPayeeInfoList(vue.payeeCategoryList[0].payeecategoryid)
          }
        },
        error: function(xhr, type, errorThrown) {
          console.log(type)
          var msg = 'Get PayeeCategoryList failed! The response is: \n' + xhr.responseText + '.'
          if (type === 'timeout') {
            msg = 'Get PayeeCategoryList failed. Time out!'
          }
          vue.$mui.alert(msg, 'Error', 'OK')
        }
      })
    },
    initPayeeInfoList(categoryid) {
      var vue = this
      var requesttimedata = {
        'payeeCategoryId': categoryid
      }
      this.$mui.ajax(this.LBSGateway + '/payment-experience/payment/payeeInfoListRetrieval', {
        data: requesttimedata,
        dataType: 'json', // 服务器返回json格式数据
        type: 'post', // HTTP请求类型
        timeout: 60000,
        headers: {
          'accept': '*/*',
          'token': this.token,
          'clientid': vue.$parent.clientid,
          'messageid': vue.$parent.messageid,
          'Content-Type': 'application/json',
          customerNumber: vue.customernumber
        },
        beforeSend: function() {
          vue.loading = true
        },
        complete: function() {
          vue.loading = false
        },
        success: function(data) {
          // Page show
          vue.payeelist = data.data
        },
        error: function(xhr, type, errorThrown) {
          console.log(type)
          var msg = 'Get PayeeInfoList failed! The response is: \n' + xhr.responseText + '.'
          if (type === 'timeout') {
            msg = 'Get PayeeInfoList failed. Time out!'
          }
          vue.$mui.alert(msg, 'Error', 'OK')
        }
      })
    },
    jumppage() {
      this.$router.push({ path: '/tellerlbspayment/payeelist/addpayee' })
    },
    jumpTopayeelistpage() {
      this.$router.push({ path: '/tellerlbspayment/payeelist' })
    }
  }
}
</script>

<style scoped>
  .mypayment .title {
    font-size: 25px;
    color: #707070;
    margin-bottom: 20px;
  }

  .mypayment .search-text {
    margin-top: 10px;
    font-size: 20px;
    color: #707070;
  }

  .mypayment .top-card {
    width: 1050px;
    margin: 0 auto;
  }

  .mypayment .top-card .card-item {
    float: left;
    width: 240px;
    border: 1px solid #22C1E6;
    margin-right: 150px;
    cursor: pointer;
    background-color: #ffffff;
  }

  .mypayment .top-card .card-item p {
    text-align: center;
    color: #707070;
    font-size: 20px;
  }
</style>

<style>
  .mypayment .el-input--medium .el-input__inner {
    margin-bottom: 0;
  }
  .el-message-box {
      width: 800px;
  }
</style>
<style scoped>
  .payeelist .title {
    padding: 30px 28px;
    height: 140px;
    background-color: #FFFFFF;
    margin: 0 25px 40px 25px;
    border-radius: 50px;
    background-image: url("../../../components/title-bg.png");
    background-repeat: no-repeat;
    background-size: 574px 130px;
    background-position: right;
    box-shadow: 5px 5px 5px #bbbbbb;
  }

  .payeelist p {
    color: #707070;
    margin-bottom: 0;
    line-height: 1;
  }

  .payeelist .max-title {
    font-size: 50px;
    margin-top: 15px;
    font-weight: 500;
    color: #22C1E6;
    margin-bottom: 40px;
  }
</style>

