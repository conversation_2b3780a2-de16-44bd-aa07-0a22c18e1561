<template>
  <div class="steptwo">
    <el-row>
      <el-col :span="24" offset="2">
        <div style="float: left;margin-right: 20px">
          <div class="el-step__icon is-text is-finish" style="margin-top: 8px">
            <div class="el-step__icon-inner">1</div>
          </div>
          <div style="border-right: 2px #109eae solid;height: 150px;width: 13px" />
          <div class="el-step__icon is-text is-ing">
            <div class="el-step__icon-inner">2</div>
          </div>
          <div style="border-right: 2px #707070 solid;height: 248px;width: 13px" />
          <div class="el-step__icon is-text no-finish">
            <div class="el-step__icon-inner">3</div>
          </div>
          <div style="border-right: 2px #707070 solid;height: 170px;width: 13px" />
          <div class="el-step__icon is-text no-finish">
            <div class="el-step__icon-inner">4</div>
          </div>
        </div>
        <div class="right">
          <p class="text-title is-finish">Move Money From</p>
          <div style="padding: 43px 0 43px">
            <p class="text-account">Account</p>
            <el-select
              slot="prepend"
              v-model="accountNumber"
              disabled
              placeholder=""
              style="width: 400px;margin-right: 200px"
            >
              <el-option
                v-for="(item,index) in accountList"
                :key="index"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
            <div style="display: inline-block">
              <el-checkbox-group v-model="checkList">
                <el-checkbox disabled label=" " />
                <p style="display: inline-block;cursor: pointer;text-decoration: underline" @click="submit()">EDIT</p>
              </el-checkbox-group>
            </div>
          </div>
          <p class="text-title is-ing">Move Money To</p>
          <div style="padding: 20px 0 20px">
            <p class="text-account" style="width: 170px;margin: 20px 0">Category</p>
            <el-select
              v-model="category"
              filterable
              placeholder="Please select Payee Category"
              style="width: 400px"
              @change="initPayeeInfoList()"
            >
              <el-option
                v-for="item in categoryList"
                id="category"
                :key="item.payeecategoryid"
                :label="item.payeecategory"
                :value="item.payeecategoryid"
              />
            </el-select>
            <br>
            <p class="text-account" style="width: 170px;margin: 20px 0">Payee Name</p>
            <el-select
              v-model="payeename"
              filterable
              placeholder="Please select Payee Name"
              style="width: 400px;margin-right: 200px"
              @change="initCustomerPayeeList()"
            >
              <el-option
                v-for="item in payeeList"
                id="payeename"
                :key="item.payeeid"
                :label="item.payeename"
                :value="item.payeeid"
              />
            </el-select>
            <div style="display: inline-block" @click.prevent="submit('next')">
              <el-checkbox-group v-model="checkList">
                <el-checkbox label="  " />
              </el-checkbox-group>
            </div>
            <br>
            <p class="text-account" style="width: 170px;margin: 20px 0">Payee Number</p>
            <el-select
              slot="prepend"
              v-model="payeenumber"
              filterable
              placeholder="Please select Payee Number"
              style="width: 400px;margin-right: 200px"
            >
              <el-option
                v-for="(item,index) in payeeNumberList"
                :key="index"
                :label="item.payeenumber"
                :value="item.payeenumber"
              />
            </el-select>
          </div>
          <p class="text-title" style="margin-bottom: 155px">Transfer Details</p>
          <p class="text-title">Confirmation</p>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
export default {
  name: 'Steptwo',
  data() {
    return {
      currency: 'HKD',
      accountNumber: '',
      savingList: [],
      currentList: [],
      creditCardList: [],
      accountList: [],
      paymentInfo: {},
      checkList: [' '],
      creditCardAccountList: [],
      category: '',
      payeename: '',
      payeenumber: '',
      categoryList: [],
      payeeList: [],
      payeeNumberList: [],
      customernumber: ''
    }
  },
  mounted() {
    this.customernumber = window.sessionStorage.getItem('targetCustomerNumber')
    this.token = window.sessionStorage.getItem('token')
    this.paymentInfo = JSON.parse(window.sessionStorage.getItem('paymentDetail'))
    this.initdata()
  },
  methods: {
    initdata() {
      this.accountNumber = this.paymentInfo.accountNumber
      if (!this.paymentInfo.categoryid && !this.paymentInfo.payeeid && !this.paymentInfo.payeenumber) {
        this.initPayeeCategoryList()
      } else {
        this.category = this.paymentInfo.categoryid
        this.payeename = this.paymentInfo.payeeid
        this.payeenumber = this.paymentInfo.payeenumber
        this.initPayeeCategoryList()
        this.initPayeeInfoList()
        this.initCustomerPayeeList()
      }
    },
    submit(type) {
      if (type === 'next') {
        if (this.category.length === 0) {
          this.$mui.alert('Please select Payee Category', 'Error', 'OK')
          return false
        }

        if (this.payeename.length === 0) {
          this.$mui.alert('Please select Payee Name', 'Error', 'OK')
          return false
        }

        if (this.payeenumber.length === 0) {
          this.$mui.alert('Please select Payee Number', 'Error', 'OK')
          return false
        }
        this.paymentInfo['categoryid'] = this.category
        this.paymentInfo['categoryname'] = document.getElementById('category').innerText
        this.paymentInfo['payeeid'] = this.payeename
        this.paymentInfo['payeename'] = document.getElementById('payeename').innerText
        this.paymentInfo['payeenumber'] = this.payeenumber
        window.sessionStorage.setItem('paymentDetail', JSON.stringify(this.paymentInfo))
        this.$router.push({ path: '/tellerlbspayment/makepayment/stepthree' })
      } else {
        this.$router.push({ path: '/tellerlbspayment/makepayment' })
      }
    },
    getcreditcardnumberpickerdata(list) {
      var accountlist = JSON.parse(list)
      var pickerdata = []
      for (var i = 0; i < accountlist.length; i++) {
        if (accountlist[i].accountStatus === 'A') {
          pickerdata.push({
            label: accountlist[i].accountNumber,
            value: accountlist[i].accountNumber
          })
        }
      }
      return pickerdata
    },
    initPayeeCategoryList() {
      var vue = this
      this.$mui.ajax(this.LBSGateway + '/payment-experience/payment/payeeCategoryList', {
        dataType: 'json', // 服务器返回json格式数据
        type: 'get', // HTTP请求类型
        timeout: 60000,
        headers: {
          'accept': '*/*',
          'token': this.token,
          'clientid': vue.$parent.clientid,
          'messageid': vue.$parent.messageid,
          'Content-Type': 'application/json',
          customerNumber: vue.customernumber
        },
        beforeSend: function() {
          // document.getElementById("load").style.display = "inline";
        },
        complete: function() {
          // document.getElementById("load").style.display = "none";
        },
        success: function(data) {
          console.log(data.data)
          // Page show
          vue.categoryList = data.data
          // vue.category = vue.categoryList[0].payeecategoryid
          // vue.initPayeeInfoList()
        },
        error: function(xhr, type, errorThrown) {
          console.log(type)
          var msg = 'Get PayeeCategoryList failed! The response is: \n' + xhr.responseText + '.'
          if (type === 'timeout') {
            msg = 'Get PayeeCategoryList failed. Time out!'
          }
          vue.$mui.alert(msg, 'Error', 'OK')
        }
      })
    },
    initPayeeInfoList() {
      var vue = this
      this.payeename = ''
      var requesttimedata = {
        'payeeCategoryId': vue.category
      }
      this.$mui.ajax(this.LBSGateway + '/payment-experience/payment/payeeInfoListRetrieval', {
        data: requesttimedata,
        dataType: 'json', // 服务器返回json格式数据
        type: 'post', // HTTP请求类型
        timeout: 60000,
        headers: {
          'accept': '*/*',
          'token': this.token,
          'clientid': vue.$parent.clientid,
          'messageid': vue.$parent.messageid,
          'Content-Type': 'application/json',
          customerNumber: vue.customernumber
        },
        beforeSend: function() {
          // document.getElementById("load").style.display = "inline";
        },
        complete: function() {
          // document.getElementById("load").style.display = "none";
        },
        success: function(data) {
          // Page show
          vue.payeeList = data.data
          console.log(vue.payeeList)
          // vue.payeename = vue.payeeList[0].payeeid
          // vue.initCustomerPayeeList();
        },
        error: function(xhr, type, errorThrown) {
          console.log(type)
          var msg = 'Get PayeeInfoList failed! The response is: \n' + xhr.responseText + '.'
          if (type === 'timeout') {
            msg = 'Get PayeeInfoList failed. Time out!'
          }
          vue.$mui.alert(msg, 'Error', 'OK')
        }
      })
    },
    initCustomerPayeeList() {
      var vue = this
      this.$mui.ajax(this.LBSGateway + '/payment-experience/payment/customerPayeeRetrieval', {
        dataType: 'json', // 服务器返回json格式数据
        type: 'get', // HTTP请求类型
        timeout: 60000,
        headers: {
          'accept': '*/*',
          'token': this.token,
          'clientid': vue.$parent.clientid,
          'messageid': vue.$parent.messageid,
          'Content-Type': 'application/json',
          customerNumber: vue.customernumber
        },
        beforeSend: function() {
          // document.getElementById("load").style.display = "inline";
        },
        complete: function() {
          // document.getElementById("load").style.display = "none";
        },
        success: function(data) {
          console.log(data)
          vue.payeeListTemp = []
          if (data.code === '200') {
            var payeeList = data.data
            // Page show
            var payeeListTemp = []
            for (var i = 0; i < payeeList.length; i++) {
              if (payeeList[i].payeecategoryid === vue.category && payeeList[i].payeeid === vue.payeename) {
                payeeListTemp.push(payeeList[i])
              }
            }
            console.log(payeeListTemp)
            if (payeeListTemp.length > 0) {
              vue.payeeNumberList = payeeListTemp
              // vue.payeenumber = vue.payeeNumberList[0].payeenumber
            } else {
              vue.payeeNumberList = []
              vue.payeenumber = ''
            }
          }
        },
        error: function(xhr, type, errorThrown) {
          console.log(type)
          var msg = 'Get CustomerPayeeList failed! The response is: \n' + xhr.responseText + '.'
          if (type === 'timeout') {
            msg = 'Get CustomerPayeeList failed. Time out!'
          }
          vue.$mui.alert(msg, 'Error', 'OK')
        }
      })
    }
  }
}
</script>

<style scoped>
  .steptwo p {
    line-height: 1;
    color: #707070;
  }

  .steptwo .text-title {
    font-size: 40px;
    color: #707070;
  }

  .steptwo .text-account {
    display: inline-block;
    font-size: 20px;
    margin-right: 100px;
  }

  .steptwo .is-finish {
    color: #109eae;
    border-color: #109eae;
  }

  .steptwo .no-finish {
    color: #707070;
    border-color: #707070;
  }

  .steptwo .is-ing {
    color: #000000;
    border-color: #000000;
  }
</style>

<style>
  .steptwo .el-step__title {
    font-size: 40px;
    margin-left: 20px;
    color: #707070;
  }

  .steptwo .el-input__icon {
    height: 40px;
  }

  .steptwo .el-input__inner {
    margin-bottom: 0;
    height: 40px;
  }

  .steptwo .el-checkbox__inner {
    width: 20px;
    height: 20px;
  }

  .steptwo .el-checkbox__inner::after {
    width: 8px;
    height: 10px;
  }

  .steptwo .el-checkbox {
    color: #109eae;
    cursor: default;
  }

  .steptwo .el-checkbox__input.is-disabled.is-checked .el-checkbox__inner {
    background-color: #109eae;
  }

  .steptwo .el-checkbox__input.is-disabled.is-checked .el-checkbox__inner::after {
    border-color: #ffffff;
  }

  .steptwo .el-input.is-disabled .el-input__inner {
    color: #707070;
  }

  .steptwo .el-checkbox__inner {
    width: 20px;
    height: 20px;
    border-color: #999999;
  }
</style>
