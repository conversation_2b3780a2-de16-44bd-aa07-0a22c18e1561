<template>
  <div v-loading.fullscreen.lock="loading" class="personalinfo">
    <el-row>
      <el-col :span="24">
        <div class="title">
          <p class="max-title">{{ $t('lbs.TravelInsurance') }}</p>
        </div>
      </el-col>
      <el-col :span="24" class="personalinfo-main">
        <el-steps :active="1" simple>
          <!--          <el-step :title="$t('lbs.SelectPlan')" icon="el-icon-thumb"/>-->
          <el-step :title="$t('lbs.GetAQuote')" icon="el-icon-edit" />
          <el-step :title="$t('lbs.PersonalInfo')" icon="el-icon-user" />
          <el-step :title="$t('lbs.Payment')" icon="el-icon-bank-card" />
          <el-step :title="$t('lbs.Finish')" icon="el-icon-s-promotion" />
        </el-steps>
      </el-col>
      <el-col :span="24">
        <el-form ref="form" :model="form" :inline="true" label-width="130px" size="mini">
          <el-card v-for="(item, index) in form.applySub" :key="index" class="info-item">
            <el-tooltip effect="dark" placement="right">
              <div slot="content" style="width: 300px">{{ $t('lbsTips.InsuredInfo.Insured') }}</div>
              <p class="insured-count"> {{ `${$t('lbs.Insured')} ${index + 1}` }}</p>
            </el-tooltip>
            <el-tooltip effect="dark" placement="right">
              <div slot="content" style="width: 300px">{{ $t('lbsTips.InsuredInfo.FirstName') }}</div>
              <el-form-item :label="$t('lbs.SurnameName')" :rules="rules" :prop="'applySub.' + index + '.surnamename'">
                <el-input v-model="item.surnamename" :placeholder="$t('lbs.SurnameName')" size="mini" />
              </el-form-item>
            </el-tooltip>
            <el-tooltip effect="dark" placement="right">
              <div slot="content" style="width: 300px">{{ $t('lbsTips.InsuredInfo.LastName') }}</div>
              <el-form-item :label="$t('lbs.GivenName')" :rules="rules" :prop="'applySub.' + index + '.givenname'">
                <el-input v-model="item.givenname" :placeholder="$t('lbs.GivenName')" size="mini" />
              </el-form-item>
            </el-tooltip>
            <br>
            <el-form-item :label="$t('lbs.Gender')" :rules="rules" :prop="'applySub.' + index + '.gender'">
              <el-select
                v-model="item.gender"
                :placeholder="$t('lbs.Gender')"
                size="mini"
                style="width: 178px;margin-bottom: 15px"
              >
                <el-option :label="$t('lbs.Male')" value="M" />
                <el-option :label="$t('lbs.Female')" value="F" />
              </el-select>
            </el-form-item>
            <br>
            <el-tooltip effect="dark" placement="right">
              <div slot="content" style="width: 300px">{{ $t('lbsTips.InsuredInfo.DateOfBirth') }}</div>
              <el-form-item :label="$t('lbs.DateOfBirth')" :rules="rules" :prop="'applySub.' + index + '.dateofbirth'">
                <el-date-picker
                  v-model="item.dateofbirth"
                  :picker-options="birthdayPickerOptions"
                  :placeholder="$t('lbs.DateOfBirth')"
                  type="date"
                  size="mini"
                  style="width: 178px;margin-bottom: 15px"
                />
              </el-form-item>
            </el-tooltip>
            <br>

            <el-form-item :label="$t('lbs.IdType')" :rules="rules" :prop="'applySub.' + index + '.idtype'">
              <el-select
                v-model="item.idtype"
                :placeholder="$t('lbs.IdType')"
                size="mini"
                style="width: 178px;margin-bottom: 15px"
              >
                <el-option :label="$t('lbs.IDCard')" value="I" />
                <el-option :label="$t('lbs.Passport')" value="P" />
                <!--                <el-option :label="$t('lbs.CertOfIncorporation')" value="C"/>-->
                <!--                <el-option :label="$t('lbs.BusinessRegistration')" value="B"/>-->
                <!--                <el-option :label="$t('lbs.Others')" value="X"/>-->
              </el-select>
            </el-form-item>
            <el-tooltip effect="dark" placement="right">
              <div slot="content" style="width: 300px">{{ $t('lbsTips.InsuredInfo.Passport') }}</div>
              <el-form-item :label="$t('lbs.IdNumber')" :rules="rules" :prop="'applySub.' + index + '.idnumber'">
                <el-input v-model="item.idnumber" :placeholder="$t('lbs.IdNumber')" size="mini" />
              </el-form-item>
            </el-tooltip>
            <br>
            <el-tooltip effect="dark" placement="right">
              <div slot="content" style="width: 300px">{{ $t('lbsTips.InsuredInfo.Relationship') }}</div>
              <el-form-item
                :label="$t('lbs.Relationship')"
                :rules="rules"
                :prop="'applySub.' + index + '.relationship'"
              >
                <el-select
                  v-model="item.relationship"
                  :placeholder="$t('lbs.Relationship')"
                  size="mini"
                  style="width: 178px;margin-bottom: 15px"
                >
                  <el-option :label="$t('lbs.Self')" value="SELF" />
                  <el-option :label="$t('lbs.Spouse')" value="SPOU" />
                  <el-option :label="$t('lbs.Parent')" value="PARE" />
                  <el-option :label="$t('lbs.Children')" value="CHIL" />
                  <el-option :label="$t('lbs.Friend')" value="FRIE" />
                  <el-option :label="$t('lbs.Relative')" value="RELA" />
                </el-select>
              </el-form-item>
            </el-tooltip>
          </el-card>
          <div style="text-align: right;margin: 30px auto;width: 50%">
            <el-tooltip effect="dark" placement="bottom">
              <div slot="content" style="width: 300px">{{ $t('lbsTips.InsuredInfo.ProceedToNextStep') }}</div>
              <el-form-item>
                <el-button type="primary" style="width: 200px;" size="default" @click="nextStep('form')">
                  {{ $t('lbs.ProceedToNextStep') }}
                </el-button>
              </el-form-item>
            </el-tooltip>
          </div>
        </el-form>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import axios from 'axios'

export default {
  data: () => ({
    token: null,
    loading: false,
    peopleCount: 0,
    stateDataName: 'INSURED_INFO',
    form: {
      quoteid: '',
      applySub: []
    },
    rules: { required: true, message: ' ', trigger: 'blur' },
    birthdayPickerOptions: {
      disabledDate: (time) => {
        return time.getTime() > Date.now()
      }
    }
  }),
  computed: {},
  watch: {
    form: {
      handler(newValue) {
        sessionStorage.setItem(this.stateDataName, JSON.stringify(newValue))
      },
      deep: true
    }
  },
  created() {
    this.token = window.sessionStorage.getItem('token')
    // console.log(this.quoteId)
  },
  mounted() {
    if (window.sessionStorage.getItem('showTips') === 'true' && window.sessionStorage.getItem('token')) {
      this.$alert(this.$t('lbs.popupinsuredinfo'), this.$t('lbs.Tip'), {
        dangerouslyUseHTMLString: true
      })
    }

    this.peopleCount = JSON.parse(window.sessionStorage.getItem('INSURANCE_TYPE')).peopleCount
    this.form.quoteid = JSON.parse(window.sessionStorage.getItem('INSURANCE_TYPE')).quoteId
    const data = JSON.parse(sessionStorage.getItem(this.stateDataName))
    if (data) {
      this.form = data
    } else {
      for (let i = 0; i < this.peopleCount; i++) {
        this.form.applySub.push(
          {
            surnamename: '',
            givenname: '',
            gender: 'M',
            dateofbirth: '',
            idtype: 'I',
            idnumber: '',
            relationship: ''
          }
        )
      }
    }
  },
  methods: {
    nextStep(form) {
      const _this = this
      this.$refs[form].validate((valid) => {
        if (valid) {
          for (let i = 0; i < this.form.applySub.length; i++) {
            this.form.applySub[i].dateofbirth = new Date(this.form.applySub[i].dateofbirth).getTime()
          }
          _this.loading = true
          axios.post(`${_this.LBSGateway}/insurance-experience/insurance/application`, _this.form, { headers: { token: _this.token }})
            .then(response => {
              _this.loading = false
              if (response.data.code === '200') {
                window.sessionStorage.setItem('insuredInfo', JSON.stringify(_this.form.applySub))
                _this.$router.push({ path: '/lbsinsurance/payment' })
              } else if (response.data.code === '202008') {
                // Invalid Id Number Format
                this.$message.warning(this.$t('lbs.insurancetip1'))
              } else if (response.data.code === '202028') {
                // RelationShip Match Incorrect,Please Double Check
                this.$message.warning(this.$t('lbs.insurancetip2'))
              } else if (response.data.code === '202027') {
                // Find a Relationship That is Not in The Quote Information, Please Double Check
                this.$message.warning(this.$t('lbs.insurancetip3'))
              } else if (response.data.code === '202002') {
                // Invalid Date Of Birth
                this.$message.warning(this.$t('lbs.insurancetip4'))
              } else if (response.data.code === '403006') {
                // Invalid Date Of Birth
                this.$message.warning(this.$t('lbs.insurancetip5'))
              }
            })
            .catch(error => {
              _this.loading = false
              _this.$mui.alert(error.message, _this.$t('lbs.common.error'), _this.$t('lbs.common.confirm'))
            })
        } else {
          this.$message.warning(this.$t('lbs.PerfectInformation'))
          return false
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>

  .personalinfo {
    .title {
      padding: 30px 28px;
      height: 140px;
      background-color: #FFFFFF;
      margin: 0 25px 0 25px;
      border-radius: 50px;
      background-image: url("../components/title-bg.png");
      background-repeat: no-repeat;
      background-size: 574px 130px;
      background-position: right;
      box-shadow: 5px 5px 5px #bbbbbb;
    }

    p {
      color: #707070;
      margin-bottom: 0;
      line-height: 1;
    }

    .max-title {
      font-size: 50px;
      margin-bottom: 40px;
      margin-top: 10px;
      font-weight: 500;
      color: #22C1E6;
    }

    .personalinfo-main {
      margin-top: 40px;
    }

    .insured-count {
      font-size: 15px;
      text-align: center;
      margin-bottom: 30px;
    }

    .info-item {
      margin: 5px auto 0;
      border: 1px #cccccc solid;
      width: 50%;
    }
  }

</style>

<style>
  .personalinfo .el-steps--simple {
    background-color: transparent;
  }

  .personalinfo .el-form-item--mini .el-form-item,
  .personalinfo .el-form-item--small .el-form-item {
    margin-bottom: 0;
  }
</style>
