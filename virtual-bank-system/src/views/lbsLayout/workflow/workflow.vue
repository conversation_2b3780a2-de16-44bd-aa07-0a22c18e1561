<template>
  <div>
    <workflowhistory
      v-if="pageList.workflowhistory"
      @showPage="showPage(arguments)"
    />
    <workflowdetail
      v-if="pageList.workflowdetail"
      :data-source="dataSource"
      :table-name="tableName"
      @showPage="showPage"
    />
    <workflowrun
      v-if="pageList.workflowrun"
      :data-source="dataSource"
      :table-name="tableName"
      @showPage="showPage"
    />
  </div>
</template>

<script>
import workflowhistory from './workflowhistory'
import workflowdetail from './workflowdetail'
import workflowrun from './workflowrun'

export default {
  components: {
    workflowhistory,
    workflowdetail,
    workflowrun
  },
  data() {
    return {
      pageList: {
        workflowhistory: true,
        workflowdetail: false,
        workflowrun: false
      },
      tableName: '',
      dataSource: ''
    }
  },
  methods: {
    showPage(params) {
      if (typeof (params) === 'string') {
        for (var key in this.pageList) {
          this.pageList[key] = key === params
        }
      } else {
        this.tableName = params[1].proc_inst_id
        this.dataSource = params[1].group_name
        for (var k in this.pageList) {
          this.pageList[k] = k === params[0]
        }
      }
    }
  }
}
</script>
