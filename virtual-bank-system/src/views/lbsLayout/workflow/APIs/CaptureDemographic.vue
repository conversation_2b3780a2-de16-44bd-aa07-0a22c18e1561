<template>
  <el-dialog
    append-to-body
    :visible.sync="dialogFormVisible"
    :before-close="hide"
    :title="group_name"
    width="600px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <el-form ref="form" :model="form" :rules="formrules" label-width="120px">
      <el-form-item :label="$t(&quot;Workflow.customerID&quot;)" prop="customerID">
        <el-input v-model="form.customerID" :maxlength="35" />
      </el-form-item>
      <el-form-item :label="$t(&quot;Workflow.customerIDType&quot;)" prop="customerIDType">
        <el-select v-model="form.customerIDType" style="width:100%">
          <el-option label="ID Card" value="I" />
          <el-option label="Passport" value="P" />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t(&quot;Workflow.dateOfBirth&quot;)" prop="dateOfBirth">
        <el-date-picker v-model="form.dateOfBirth" type="date" :clearable="false" value-format="timestamp" style="width:100%" />
      </el-form-item>
      <el-form-item :label="$t(&quot;Workflow.firstname&quot;)" prop="firstname">
        <el-input v-model="form.firstname" :maxlength="70" />
      </el-form-item>
      <el-form-item :label="$t(&quot;Workflow.lastname&quot;)" prop="lastname">
        <el-input v-model="form.lastname" :maxlength="70" />
      </el-form-item>
      <el-form-item :label="$t(&quot;Workflow.issueCountry&quot;)" prop="issueCountry">
        <el-input v-model="form.issueCountry" :maxlength="20" />
      </el-form-item>
      <el-form-item :label="$t(&quot;Workflow.mailingAddress&quot;)" prop="mailingAddress">
        <el-input v-model="form.mailingAddress" :maxlength="1000" />
      </el-form-item>
      <el-form-item :label="$t(&quot;Workflow.mobilePhoneNumber&quot;)" prop="mobilePhoneNumber">
        <el-input v-model="form.mobilePhoneNumber" type="number" :maxlength="34" />
      </el-form-item>
      <el-form-item :label="$t(&quot;Workflow.accommodation&quot;)" prop="accommodation">
        <el-select v-model="form.accommodation" style="width:100%">
          <el-option label="Self Owned" value="S" />
          <el-option label="Private property" value="P" />
          <el-option label="Home ownership scheme" value="H" />
          <el-option label="Friends/Relatives" value="F" />
          <el-option label="Public Housing" value="U" />
          <el-option label="Rented" value="R" />
          <el-option label="Quarters" value="Q" />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t(&quot;Workflow.branchcode&quot;)" prop="branchcode">
        <el-input v-model="form.branchcode" :maxlength="20" />
      </el-form-item>
      <el-form-item :label="$t(&quot;Workflow.chinesename&quot;)" prop="chinesename">
        <el-input v-model="form.chinesename" :maxlength="140" />
      </el-form-item>
      <el-form-item :label="$t(&quot;Workflow.clearingcode&quot;)" prop="clearingcode">
        <el-input v-model="form.clearingcode" :maxlength="20" />
      </el-form-item>
      <el-form-item :label="$t(&quot;Workflow.companyaddress&quot;)" prop="companyaddress">
        <el-input v-model="form.companyaddress" :maxlength="1000" />
      </el-form-item>
      <el-form-item :label="$t(&quot;Workflow.companyphonenumber&quot;)" prop="companyphonenumber">
        <el-input v-model="form.companyphonenumber" type="number" :maxlength="34" />
      </el-form-item>
      <el-form-item :label="$t(&quot;Workflow.countrycode&quot;)" prop="countrycode">
        <el-input v-model="form.countrycode" :maxlength="2" />
      </el-form-item>
      <el-form-item :label="$t(&quot;Workflow.education&quot;)" prop="education">
        <el-select v-model="form.education" style="width:100%">
          <el-option label="University" value="U" />
          <el-option label="Post Secondary" value="P" />
          <el-option label="Secondary" value="S" />
          <el-option label="Primary/Junior" value="J" />
          <el-option label="Others" value="O" />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t(&quot;Workflow.emailaddress&quot;)" prop="emailaddress">
        <el-input v-model="form.emailaddress" :maxlength="320" />
      </el-form-item>
      <el-form-item :label="$t(&quot;Workflow.employercompanyname&quot;)" prop="employercompanyname">
        <el-input v-model="form.employercompanyname" :maxlength="140" />
      </el-form-item>
      <el-form-item :label="$t(&quot;Workflow.gender&quot;)" prop="gender">
        <el-select v-model="form.gender" style="width:100%">
          <el-option label="Male" value="M" />
          <el-option label="Female" value="F" />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t(&quot;Workflow.maritalstatus&quot;)" prop="maritalstatus">
        <el-select v-model="form.maritalstatus" style="width:100%">
          <el-option label="Single" value="S" />
          <el-option label="Married" value="M" />
          <el-option label="Divorced" value="D" />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t(&quot;Workflow.monthlysalary&quot;)" prop="monthlysalary">
        <el-input v-model="form.monthlysalary" type="number" :maxlength="18" />
      </el-form-item>
      <el-form-item :label="$t(&quot;Workflow.nationality&quot;)" prop="nationality">
        <el-input v-model="form.nationality" :maxlength="20" />
      </el-form-item>
      <el-form-item :label="$t(&quot;Workflow.occupation&quot;)" prop="occupation">
        <el-input v-model="form.occupation" :maxlength="1000" />
      </el-form-item>
      <el-form-item :label="$t(&quot;Workflow.permanentresidencestatus&quot;)" prop="permanentresidencestatus">
        <el-select v-model="form.permanentresidencestatus" style="width:100%">
          <el-option label="Yes" value="Y" />
          <el-option label="No" value="N" />
        </el-select>
      </el-form-item>
      <el-form-item :label="$t(&quot;Workflow.position&quot;)" prop="position">
        <el-input v-model="form.position" :maxlength="500" />
      </el-form-item>
      <el-form-item :label="$t(&quot;Workflow.residencephonenumber&quot;)" prop="residencephonenumber">
        <el-input v-model="form.residencephonenumber" type="number" :maxlength="34" />
      </el-form-item>
      <el-form-item :label="$t(&quot;Workflow.residentialaddress&quot;)" prop="residentialaddress">
        <el-input v-model="form.residentialaddress" :maxlength="1000" />
      </el-form-item>
      <el-form-item :label="$t(&quot;Workflow.wechatid&quot;)" prop="wechatid">
        <el-input v-model="form.wechatid" :maxlength="30" />
      </el-form-item>
      <el-form-item :label="$t(&quot;Workflow.yearsofresidence&quot;)" prop="yearsofresidence">
        <el-input v-model="form.yearsofresidence" type="number" :maxlength="3" />
      </el-form-item>
      <el-form-item :label="$t(&quot;Workflow.yearsofservices&quot;)" prop="yearsofservices">
        <el-input v-model="form.yearsofservices" type="number" :min="0" :max="999" :maxlength="3" />
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button @click="hide">{{ $t("lbs.common.cancel") }}</el-button>
      <el-button type="primary" @click="submit">{{ $t("lbs.common.confirm") }}</el-button>
    </div>
  </el-dialog>
</template>

<script>

import axios from 'axios'

export default {
  name: 'CaptureDemographic',
  props: {
    processInstanceId: {
      type: String,
      default: ''
    },
    group_name: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      loading: false,
      dialogFormVisible: true,
      userId: window.localStorage.getItem('username'),
      requestData: {
        'processInstanceId': this.processInstanceId,
        'group_name': 'eKYC',
        'api_header': [],
        'api_param': [],
        'api_formdata': [],
        'api_json': ''
      },
      form: {
        accommodation: 'S',
        branchcode: '001',
        chinesename: '王研',
        clearingcode: '0001',
        companyaddress: '228 Pok Fu Lam Road,Aberdeen,Hong Kong',
        companyphonenumber: 24827781,
        countrycode: 'HK',
        customerID: 'U735535(9)',
        customerIDType: 'I',
        dateOfBirth: 178163911000,
        education: 'U',
        emailaddress: '<EMAIL>',
        employercompanyname: 'Happy Realty',
        firstname: 'Yan',
        gender: 'M',
        issueCountry: 'China',
        lastname: 'Wong',
        mailingAddress: '551 Austin Road,Tsim Sha Tsui,Kowloon',
        maritalstatus: 'S',
        mobilePhoneNumber: 64657884,
        monthlysalary: 19000,
        nationality: 'China',
        occupation: 'Software',
        permanentresidencestatus: 'Y',
        position: 'Senior Manager',
        residencephonenumber: 34828869,
        residentialaddress: '779 Yi Chun Street, Sai Kung, New Territory',
        wechatid: 'W3456754',
        yearsofresidence: 3,
        yearsofservices: 2
      },
      formrules: {
        customerID: [{ required: true, message: this.$t('Workflow.customerIDerror'), trigger: 'blur' }],
        dateOfBirth: [{ required: true, message: this.$t('Workflow.dateOfBirtherror'), trigger: 'blur' }],
        firstname: [{ required: true, message: this.$t('Workflow.firstnameerror'), trigger: 'blur' }],
        lastname: [{ required: true, message: this.$t('Workflow.lastnameerror'), trigger: 'blur' }],
        issueCountry: [{ required: true, message: this.$t('Workflow.issueCountryerror'), trigger: 'blur' }],
        mailingAddress: [{ required: true, message: this.$t('Workflow.mailingAddresserror'), trigger: 'blur' }],
        mobilePhoneNumber: [{ required: true, message: this.$t('Workflow.mobilePhoneNumbererror'), trigger: 'blur' }]
      }
    }
  },
  created() {},
  methods: {
    submit: function() {
      const _this = this
      _this.$refs['form'].validate(valid => {
        if (valid) {
          _this.loading = true
          _this.requestData.api_json = _this.form
          axios.post(`${this.workflowHost}/workflow/executeFlow`, _this.requestData,
            { headers: { token: window.localStorage.getItem('accesstoken') }})
            .then(response => {
              _this.loading = false
              if (response.data.code === 20000) {
                _this.$emit('refreshStatus')
              } else {
                _this.$message(response.data.message, 'error')
                _this.$alert(response.data.data.response)
              }
            })
            .catch(error => {
              _this.loading = false
              _this.$message.error(error.message)
            })
        }
      })
    },
    hide() {
      this.$emit('close')
    }
  }
}
</script>
