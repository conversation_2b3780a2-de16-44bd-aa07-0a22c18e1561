<template>
  <div>
    <el-dialog
      append-to-body
      :visible.sync="dialogFormVisible"
      :before-close="hide"
      :title="group_name"
      width="600px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <el-form ref="form" :model="form" :rules="formrules" label-width="120px">
        <el-form-item :label="$t(&quot;Workflow.customerID&quot;)" prop="customerId">
          <el-input v-model="form.customerId" :maxlength="35" />
        </el-form-item>
        <el-form-item :label="$t(&quot;Workflow.residentialaddress&quot;)" prop="address">
          <el-input v-model="form.address" :maxlength="1000" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="hide">{{ $t("lbs.common.cancel") }}</el-button>
        <el-button type="primary" @click="submit">{{ $t("lbs.common.confirm") }}</el-button>
      </div>
    </el-dialog>

    <el-dialog
      append-to-body
      :visible.sync="dialogFormVisible2"
      title="Review"
      width="600px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <el-form ref="publishform" :model="form2" label-width="120px">
        <el-select v-model="form2.status" style="width:100%">
          <el-option label="Approved" value="1" />
          <el-option label="Not Approved" value="2" />
        </el-select>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogFormVisible2 = false">{{ $t("lbs.common.cancel") }}</el-button>
        <el-button type="primary" @click="submit2">{{ $t("lbs.common.confirm") }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>

import axios from 'axios'

export default {
  name: 'VerifyAddress',
  props: {
    processInstanceId: {
      type: String,
      default: ''
    },
    group_name: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      loading: false,
      dialogFormVisible: true,
      dialogFormVisible2: false,
      userId: window.localStorage.getItem('username'),
      requestData: {
        'processInstanceId': this.processInstanceId,
        'group_name': 'eKYC',
        'api_header': [],
        'api_param': [],
        'api_formdata': [],
        'api_json': ''
      },
      form: {
        customerId: 'U735535(9)',
        address: '779 Yi Chun Street, Sai Kung, New Territory'
      },
      formrules: {
        customerId: [{ required: true, message: this.$t('Workflow.customerIDerror'), trigger: 'blur' }],
        address: [{ required: true, message: this.$t('Workflow.residentialaddresserror'), trigger: 'blur' }]
      },
      form2: {
        serialNum: '',
        status: 1
      }
    }
  },
  created() {},
  methods: {
    submit: function() {
      const _this = this
      _this.$refs['form'].validate(valid => {
        if (valid) {
          _this.loading = true
          _this.requestData.api_json = _this.form
          axios.post(`${this.workflowHost}/workflow/executeFlow`, _this.requestData,
            { headers: { token: window.localStorage.getItem('accesstoken') }})
            .then(response => {
              _this.loading = false
              if (response.data.code === 20000) {
              // _this.form2.serialNum = response.data.data
              // _this.dialogFormVisible2 = true
                _this.$emit('refreshStatus')
              } else {
                _this.$message.error(response.data.message)
                _this.$alert(response.data.data.response)
              }
            })
            .catch(error => {
              _this.loading = false
              _this.$message.error(error.message)
            })
        }
      })
    },
    submit2: function() {
      const _this = this
      _this.$refs['form'].validate(valid => {
        if (valid) {
          _this.loading = true
          axios.post(`${this.workflowHost}/workflow/executeFlow`, _this.form2,
            { headers: { token: window.localStorage.getItem('accesstoken') }})
            .then(response => {
              _this.loading = false
              if (response.data.code === 20000) {
                _this.$emit('refreshStatus')
              } else {
                _this.$message.error(response.data.message)
              }
            })
            .catch(error => {
              _this.loading = false
              _this.$message.error(error.message)
            })
        }
      })
    },
    hide() {
      this.$emit('close')
    }
  }
}
</script>
