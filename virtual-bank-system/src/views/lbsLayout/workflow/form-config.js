import CustomizeSelect from '@/components/CustomizeForm/CustomizeSelect.vue'
import {
  booleanOptions,
  countryOptions,
  customerIdTypeOptions,
  genderOptions,
  statusOptions
} from '@/views/lbsLayout/system/kyc/options'

export const commonFormConfig = [
  {
    'label': 'kyc.customer.firstName',
    'placeholder': 'kyc.customer.firstNamePlaceholder',
    'prop': 'firstName',
    'component': 'el-input',
    'span': 12,
    'width': '100%',
    'clearable': true,
    'maxlength': 70,
    'disabled': true,
    'rules': [
      { required: true, message: 'kyc.customer.firstNamePlaceholder' }
    ]
  },
  {
    'label': 'kyc.customer.lastName',
    'placeholder': 'kyc.customer.lastNamePlaceholder',
    'prop': 'lastName',
    'component': 'el-input',
    'span': 12,
    'width': '100%',
    'clearable': true,
    'disabled': true,
    'maxlength': 70,
    'rules': [
      { required: true, message: 'kyc.customer.lastNamePlaceholder' }
    ]
  },
  {
    'label': 'kyc.customer.customerId',
    'placeholder': 'kyc.customer.customerIdPlaceholder',
    'prop': 'customerId',
    'component': 'el-input',
    'span': 12,
    'width': '100%',
    'clearable': true,
    'disabled': true,
    'maxlength': 35,
    'rules': [
      { required: true, message: 'kyc.customer.customerIdPlaceholder' }
    ]
  },
  {
    'label': 'kyc.customer.customerIdType',
    'placeholder': 'kyc.customer.customerIdTypePlaceholder',
    'prop': 'customerIdType',
    'component': CustomizeSelect,
    'span': 12,
    'width': '100%',
    'clearable': true,
    'options': customerIdTypeOptions,
    'disabled': true,
    'rules': [
      { required: true, message: 'kyc.customer.customerIdTypePlaceholder' }
    ]
  },
  {
    'label': 'kyc.customer.issueCountry',
    'placeholder': 'kyc.customer.issueCountryPlaceholder',
    'prop': 'issueCountry',
    'component': CustomizeSelect,
    'span': 12,
    'width': '100%',
    'clearable': true,
    'options': countryOptions,
    'disabled': true,
    'rules': [
      { required: true, message: 'kyc.customer.issueCountryPlaceholder' }
    ]
  },
  {
    'label': 'kyc.customer.dateOfBirth',
    'placeholder': 'kyc.customer.dateOfBirthPlaceholder',
    'prop': 'dateOfBirth',
    'component': 'el-date-picker',
    'span': 12,
    'width': '100%',
    'clearable': true,
    'type': 'date',
    'format': 'yyyy-MM-dd',
    'valueFormat': 'timestamp',
    'disabled': true,
    'rules': [
      { required: true, message: 'kyc.customer.dateOfBirthPlaceholder' }
    ]
  },
  {
    'label': 'kyc.customer.otherLanguageName',
    'placeholder': 'kyc.customer.otherLanguageNamePlaceholder',
    'prop': 'otherLanguageName',
    'component': 'el-input',
    'span': 12,
    'width': '100%',
    'clearable': true,
    'maxlength': 70
    // 'rules': [
    //   { required: true, message: 'kyc.customer.otherLanguageNamePlaceholder' }
    // ]
  },
  {
    'label': 'kyc.customer.gender',
    'placeholder': 'kyc.customer.genderPlaceholder',
    'prop': 'gender',
    'component': CustomizeSelect,
    'span': 12,
    'width': '100%',
    'clearable': true,
    'disabled': true,
    'options': genderOptions,
    'rules': [
      { required: true, message: 'kyc.customer.genderPlaceholder' }
    ]
  },
  {
    'label': 'kyc.customer.nationality',
    'placeholder': 'kyc.customer.nationalityPlaceholder',
    'prop': 'nationality',
    'component': CustomizeSelect,
    'span': 12,
    'width': '100%',
    'clearable': true,
    'disabled': true,
    'options': countryOptions,
    'rules': [
      { required: true, message: 'kyc.customer.nationalityPlaceholder' }
    ]
  },
  {
    'label': 'kyc.customer.department',
    'placeholder': 'kyc.customer.departmentPlaceholder',
    'prop': 'department',
    'component': 'el-input',
    'span': 12,
    'width': '100%',
    'clearable': true,
    'rules': [
      { required: true, message: 'kyc.customer.departmentPlaceholder' }
    ],
    'maxlength': 30
  },
  {
    'label': 'kyc.customer.role',
    'placeholder': 'kyc.customer.rolePlaceholder',
    'prop': 'role',
    'component': 'el-input',
    'span': 12,
    'width': '100%',
    'clearable': true,
    'rules': [
      { required: true, message: 'kyc.customer.rolePlaceholder' }
    ],
    'maxlength': 70
  },
  {
    'label': 'kyc.customer.industry',
    'placeholder': 'kyc.customer.industryPlaceholder',
    'prop': 'industry',
    'component': 'el-input',
    'span': 12,
    'width': '100%',
    'clearable': true,
    'rules': [
      { required: true, message: 'kyc.customer.industryPlaceholder' }
    ],
    'maxlength': 50
  },
  {
    'label': 'kyc.customer.position',
    'placeholder': 'kyc.customer.positionPlaceholder',
    'prop': 'position',
    'component': 'el-input',
    'span': 12,
    'width': '100%',
    'clearable': true,
    'rules': [
      { required: true, message: 'kyc.customer.positionPlaceholder' }
    ],
    'maxlength': 70
  },
  {
    'label': 'kyc.customer.customerNumber',
    'placeholder': 'kyc.customer.customerNumberPlaceholder',
    'prop': 'customerNumber',
    'component': 'el-input',
    'span': 12,
    'width': '100%',
    'clearable': true,
    'rules': [
      { required: true, message: 'kyc.customer.customerNumberPlaceholder' }
    ],
    'maxlength': 25,
    'hidden': true
  },
  {
    'label': 'kyc.customer.accountNumber',
    'placeholder': 'kyc.customer.accountNumberPlaceholder',
    'prop': 'accountNumber',
    'component': 'el-input',
    'span': 12,
    'width': '100%',
    'clearable': true,
    'rules': [
      { required: true, message: 'kyc.customer.accountNumberPlaceholder' }
    ],
    'maxlength': 25,
    'hidden': true
  }
]

export const crsFormConfig = [
  ...commonFormConfig,
  {
    'label': 'kyc.crs.foreignCountryTaxNumber',
    'placeholder': 'kyc.crs.foreignCountryTaxNumberPlaceholder',
    'prop': 'foreignCountryTaxNumber',
    'component': 'el-input-number',
    'span': 12,
    'min': 0,
    'step': 1,
    'step-strictly': true,
    'controls': false,
    'precision': 0,
    'width': '100%',
    'clearable': true,
    'rules': [
      { required: true, message: 'kyc.crs.foreignCountryTaxNumberPlaceholder' }
    ]
  },
  {
    'label': 'kyc.crs.validityPeriod',
    'placeholder': 'kyc.crs.validityPeriodPlaceholder',
    'prop': 'validityPeriod',
    'component': 'el-date-picker',
    'span': 12,
    'width': '100%',
    'clearable': true,
    'type': 'datetime',
    'format': 'yyyy-MM-dd HH:mm:ss',
    'valueFormat': 'timestamp',
    'rules': [
      { required: true, message: 'kyc.crs.validityPeriodPlaceholder' }
    ]
  },
  {
    'label': 'kyc.crs.report',
    'placeholder': 'kyc.crs.reportPlaceholder',
    'prop': 'report',
    'component': CustomizeSelect,
    'span': 12,
    'width': '100%',
    'clearable': true,
    'options': booleanOptions,
    'rules': [
      { required: true, message: 'kyc.crs.reportPlaceholder' }
    ]
  },
  {
    'label': 'kyc.crs.crsStatus',
    'placeholder': 'kyc.crs.crsStatusPlaceholder',
    'prop': 'crsStatus',
    'component': CustomizeSelect,
    'span': 12,
    'width': '100%',
    'options': statusOptions,
    'hidden': true,
    'clearable': true,
    'rules': [
      { required: true, message: 'kyc.crs.crsStatusPlaceholder' }
    ]
  },
  {
    'label': 'kyc.crs.remark',
    'placeholder': 'kyc.crs.remarkPlaceholder',
    'prop': 'remark',
    'component': 'el-input',
    'span': 24,
    'width': '100%',
    'type': 'textarea',
    'clearable': true,
    'maxlength': 255
  },
  {
    'label': 'kyc.creationDate',
    'placeholder': 'kyc.creationDatePlaceholder',
    'prop': 'creationDate',
    'component': 'el-date-picker',
    'span': 12,
    'width': '100%',
    'disabled': true,
    'type': 'datetime',
    'format': 'yyyy-MM-dd HH:mm:ss',
    'valueFormat': 'timestamp',
    'hidden': true,
    'clearable': true,
    'rules': [
      { required: true, message: 'kyc.creationDatePlaceholder' }
    ]
  },
  {
    'label': 'kyc.updateDate',
    'placeholder': 'kyc.updateDatePlaceholder',
    'prop': 'updateDate',
    'component': 'el-date-picker',
    'span': 12,
    'width': '100%',
    'disabled': true,
    'type': 'datetime',
    'format': 'yyyy-MM-dd HH:mm:ss',
    'valueFormat': 'timestamp',
    'hidden': true,
    'clearable': true,
    'rules': [
      { required: true, message: 'kyc.updateDatePlaceholder' }
    ]
  }
]

export const fatcaFormConfig = [
  ...commonFormConfig,
  {
    'label': 'kyc.facta.taxpayerIdentificationNumber',
    'placeholder': 'kyc.facta.taxpayerIdentificationNumberPlaceholder',
    'prop': 'taxpayerIdentificationNumber',
    'component': 'el-input-number',
    'span': 12,
    'min': 0,
    'step': 1,
    'step-strictly': true,
    'controls': false,
    'precision': 0,
    'width': '100%',
    'clearable': true,
    'rules': [
      { required: true, message: 'kyc.facta.taxpayerIdentificationNumberPlaceholder' }
    ]
  },
  {
    'label': 'kyc.facta.validityPeriod',
    'placeholder': 'kyc.facta.validityPeriodPlaceholder',
    'prop': 'validityPeriod',
    'component': 'el-date-picker',
    'span': 12,
    'width': '100%',
    'clearable': true,
    'type': 'datetime',
    'format': 'yyyy-MM-dd HH:mm:ss',
    'valueFormat': 'timestamp',
    'rules': [
      { required: true, message: 'kyc.facta.validityPeriodPlaceholder' }
    ]
  },
  {
    'label': 'kyc.facta.report',
    'placeholder': 'kyc.facta.reportPlaceholder',
    'prop': 'report',
    'component': CustomizeSelect,
    'span': 12,
    'width': '100%',
    'clearable': true,
    'options': booleanOptions,
    'rules': [
      { required: true, message: 'kyc.facta.reportPlaceholder' }
    ]
  },
  {
    'label': 'kyc.facta.factaStatus',
    'placeholder': 'kyc.facta.factaStatusPlaceholder',
    'prop': 'factaStatus',
    'component': CustomizeSelect,
    'span': 12,
    'width': '100%',
    'clearable': true,
    'options': statusOptions,
    'hidden': true,
    'rules': [
      { required: true, message: 'kyc.facta.factaStatusPlaceholder' }
    ]
  },
  {
    'label': 'kyc.facta.remark',
    'placeholder': 'kyc.facta.remarkPlaceholder',
    'prop': 'remark',
    'component': 'el-input',
    'span': 24,
    'type': 'textarea',
    'width': '100%',
    'clearable': true,
    'maxlength': 255
  },
  {
    'label': 'kyc.creationDate',
    'placeholder': 'kyc.creationDatePlaceholder',
    'prop': 'creationDate',
    'component': 'el-date-picker',
    'span': 12,
    'width': '100%',
    'clearable': true,
    'disabled': true,
    'type': 'datetime',
    'format': 'yyyy-MM-dd HH:mm:ss',
    'valueFormat': 'timestamp',
    'hidden': true,
    'rules': [
      { required: true, message: 'kyc.creationDatePlaceholder' }
    ]
  },
  {
    'label': 'kyc.updateDate',
    'placeholder': 'kyc.updateDatePlaceholder',
    'prop': 'updateDate',
    'component': 'el-date-picker',
    'span': 12,
    'width': '100%',
    'clearable': true,
    'disabled': true,
    'type': 'datetime',
    'format': 'yyyy-MM-dd HH:mm:ss',
    'valueFormat': 'timestamp',
    'hidden': true,
    'rules': [
      { required: true, message: 'kyc.updateDatePlaceholder' }
    ]
  }
]
