<template>
  <div v-loading.fullscreen.lock="loading" class="paymentform">
    <el-row>
      <el-col :span="24" offset="2">
        <div style="float: left;margin-right: 20px">
          <div class="el-step__icon is-text is-finish" style="margin-top: 8px">
            <div class="el-step__icon-inner">1</div>
          </div>
          <div style="border-right: 2px #109eae solid;height: 143px;width: 13px" />
          <div class="el-step__icon is-text is-finish">
            <div class="el-step__icon-inner">2</div>
          </div>
          <div style="border-right: 2px #109eae solid;height: 135px;width: 13px" />
          <div class="el-step__icon is-text is-finish">
            <div class="el-step__icon-inner">3</div>
          </div>
          <div style="border-right: 2px #109eae solid;height: 135px;width: 13px" />
          <div class="el-step__icon is-text is-finish">
            <div class="el-step__icon-inner">4</div>
          </div>
        </div>
        <div class="right">
          <p class="text-title is-finish">Move Money From</p>
          <div style="padding: 43px 0 43px">
            <p class="text-account">Account</p>
            <p style="display: inline-block;margin-right: 300px;font-size: 20px">{{ accountNumber }}</p>
            <div style="display: inline-block">
              <el-checkbox-group v-model="checkList">
                <el-checkbox disabled label=" " />
                <p style="display: inline-block;cursor: pointer;text-decoration: underline" @click="submit()">EDIT</p>
              </el-checkbox-group>
            </div>
          </div>
          <p class="text-title is-finish">Move Money To</p>
          <div style="padding: 40px 0 40px">
            <p style="display: inline-block;margin-right: 70px;font-size: 20px">Credit Card</p>
            <p style="display: inline-block;margin-right: 380px;font-size: 20px">{{ creditCardAccount }}</p>
            <div style="display: inline-block">
              <el-checkbox-group v-model="checkList">
                <el-checkbox disabled label="  " />
                <p style="display: inline-block;cursor: pointer;text-decoration: underline" @click="submit('two')">
                  EDIT</p>
              </el-checkbox-group>
            </div>
          </div>
          <p class="text-title is-finish">Transfer Details</p>
          <div style="padding: 20px;position: relative">
            <p style="display: inline-block;font-size: 20px;margin: 20px 100px 30px 0">Amount</p>
            <p style="display: inline-block;width: 560px;font-size: 20px">{{ currency + ' ' + $lbs.decimal_format(amount) }}</p>
            <div style="display: inline-block">
              <el-checkbox-group v-model="checkList">
                <el-checkbox disabled label="   " />
                <p style="display: inline-block;cursor: pointer;text-decoration: underline" @click="submit('three')">
                  EDIT</p>
              </el-checkbox-group>
            </div>
          </div>
          <p class="text-title is-finish">Confirmation</p>
          <div style="width: 800px">
            <el-button
              class="button"
              style="float: right;background-color: #109eae;color: #ffffff"
              icon="el-icon-check"
              @click="submit('submit')"
            >CONFIRM
            </el-button>
            <el-button
              class="button"
              style="float: right;background-color: #ED1B1B;color: #ffffff;margin-right: 20px"
              icon="el-icon-close"
              @click="submit('back')"
            >CANCEL
            </el-button>
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
export default {
  name: 'Paymentform',
  data() {
    return {
      loading: false,
      token: null,
      currency: 'HKD',
      accountNumber: '',
      creditCardAccount: '',
      savingList: [],
      currentList: [],
      accountList: [],
      paymentInfo: {},
      checkList: [' ', '  ', '   '],
      type: 'My Payee',
      creditCardAccountList: [],
      amount: '',
      reference: ''

    }
  },
  watch: {
    amount: {
      handler() {
        this.clearNoNum()
      }
    }
  },
  mounted() {
    this.token = window.sessionStorage.getItem('token')
    this.paymentInfo = JSON.parse(window.sessionStorage.getItem('paymentInfo'))
    this.creditCardAccountList = this.getcreditcardnumberpickerdata(window.sessionStorage.getItem('creditCardaccountlist'))
    this.paymentInfo = JSON.parse(window.sessionStorage.getItem('paymentInfo'))
    this.initdata()
  },
  methods: {
    initdata() {
      this.accountNumber = this.paymentInfo.accountNumber
      this.creditCardAccount = this.paymentInfo.creditCardAccount
      this.amount = this.paymentInfo.amount
      this.reference = this.paymentInfo.reference
    },
    submit(type) {
      if (type === 'back') {
        window.sessionStorage.removeItem('paymentInfo')
        this.$router.push({ path: '/lbsaccountservice/creditcarddetail' })
      } else if (type === 'submit') {
        this.repaymentnow()
        window.sessionStorage.removeItem('paymentInfo')
      } else if (type === 'two') {
        this.$router.push({ path: '/lbsaccountservice/repayment/steptwo' })
      } else if (type === 'three') {
        this.$router.push({ path: '/lbsaccountservice/repayment/stepthree' })
      } else {
        this.$router.push({ path: '/lbsaccountservice/repayment' })
      }
    },
    getcreditcardnumberpickerdata(list) {
      var accountlist = JSON.parse(list)
      var pickerdata = []
      for (var i = 0; i < accountlist.length; i++) {
        if (accountlist[i].accountStatus === 'A') {
          pickerdata.push({
            label: accountlist[i].accountNumber,
            value: accountlist[i].accountNumber
          })
        }
      }
      return pickerdata
    },
    repaymentnow() {
      var vue = this
      var requestdata = {
        'creditcardnumber': this.paymentInfo.creditCardAccount,
        'debitaccountnumber': this.paymentInfo.accountNumber,
        'repaymentAmount': this.paymentInfo.amount
      }

      this.$mui.ajax(this.LBSGateway + '/creditcard-experience/creditcard/creditCardRepayment', {
        data: requestdata,
        dataType: 'json', // 服务器返回json格式数据
        type: 'post', // HTTP请求类型
        timeout: 60000,
        headers: {
          'accept': '*/*',
          'token': vue.token,
          'clientid': vue.$parent.clientid,
          'messageid': vue.$parent.messageid,
          'Content-Type': 'application/json'
        },
        beforeSend: function() {
          vue.loading = true
        },
        complete: function() {
          vue.loading = false
        },
        success: function(data) {
          if (data.code === '200') {
            vue.$mui.alert('Transaction Accepted.', 'Success', 'OK', function() {
              vue.$router.push({ path: '/lbsaccountservice/creditcarddetail' })
            })
          } else {
            vue.$mui.alert('Get account info failed! The response is: \n' + JSON.stringify(data) + '.', 'Error', 'OK')
          }
        },
        error: function(xhr, type, errorThrown) {
          console.log(type)
          var msg = 'Get account info failed! The response is: \n' + xhr.responseText + '.'
          if (type === 'timeout') {
            msg = 'Get account info failed. Time out!'
          }
          vue.$mui.alert(msg, 'Error', 'OK')
        }
      })
    }, clearNoNum() {
      this.amount = this.amount.replace(/[^\d.]/g, '') // 清除"数字"和"."以外的字符
      this.amount = this.amount.replace(/^\./g, '') // 验证第一个字符是数字而不是字符
      this.amount = this.amount.replace(/\.{2,}/g, '.') // 只保留第一个.清除多余的
      this.amount = this.amount.replace('.', '$#$').replace(/\./g, '').replace('$#$', '.')
      this.amount = this.amount.replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3') // 只能输入两个小数
    }
  }
}
</script>

<style scoped>
  .paymentform p {
    line-height: 1;
    color: #707070;
  }

  .paymentform .text-title {
    font-size: 40px;
    color: #707070;
  }

  .paymentform .text-account {
    display: inline-block;
    font-size: 20px;
    margin-right: 100px;
  }

  .paymentform .is-finish {
    color: #109eae;
    border-color: #109eae;
  }

  .paymentform .no-finish {
    color: #707070;
    border-color: #707070;
  }

  .paymentform .is-ing {
    color: #000000;
    border-color: #000000;
  }
</style>

<style>
  .paymentform .el-step__title {
    font-size: 40px;
    margin-left: 20px;
    color: #707070;
  }

  .paymentform .el-input__icon {
    height: 40px;
  }

  .paymentform .el-input__inner {
    margin-bottom: 0;
    height: 40px;
  }

  .paymentform .el-checkbox__inner {
    width: 20px;
    height: 20px;
  }

  .paymentform .el-checkbox__inner::after {
    width: 8px;
    height: 10px;
  }

  .paymentform .el-checkbox {
    color: #109eae;
    cursor: default;
  }

  .paymentform .el-checkbox__input.is-disabled.is-checked .el-checkbox__inner {
    background-color: #109eae;
  }

  .paymentform .el-checkbox__input.is-disabled.is-checked .el-checkbox__inner::after {
    border-color: #ffffff;
  }

  .paymentform .el-input.is-disabled .el-input__inner {
    color: #707070;
  }

  .paymentform .el-checkbox__inner {
    width: 20px;
    height: 20px;
    border-color: #999999;
  }

  .paymentform .el-textarea__inner {
    height: 150px;
  }
</style>
