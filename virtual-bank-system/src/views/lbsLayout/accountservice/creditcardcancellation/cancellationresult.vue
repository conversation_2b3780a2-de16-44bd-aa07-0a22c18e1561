<template>
  <div class="cancellationresult">
    <el-row>
      <el-col :lg="{span:16,offset:8}" :xs="24">
        <p class="successful">{{ $t('lbs.cancelSuccessful') }}</p>
      </el-col>
    </el-row>
    <el-row>
      <el-col :lg="{span:1,offset:16}" :xs="{span:16,offset:8}">
        <el-button class="lbs-button" type="primary" @click="submit()">{{ $t('lbs.Done') }}</el-button>
      </el-col>
    </el-row>
  </div>
</template>

<script>
export default {
  name: 'Cancellationresult',
  methods: {
    submit() {
      this.$router.push({ path: '/lbsaccountservice/creditcarddetail' })
    }
  }
}
</script>

<style scoped>
  .cancellationresult .successful{
    font-size: 40px;
    line-height: 200px;
  }
  .cancellationresult .lbs-button{
    float: right;
  }
</style>
