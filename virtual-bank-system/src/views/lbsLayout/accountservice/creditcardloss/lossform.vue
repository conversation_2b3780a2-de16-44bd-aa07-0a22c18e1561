<template>
  <div v-loading.fullscreen.lock="loading" class="lossform">
    <el-row>
      <el-col :span="24">
        <el-row>
          <el-col :span="24">
            <div class="div-select-info">
              <el-row>
                <el-col :xs="24" :lg="8">
                  <p class="p-select-info">
                    {{ $t('lbs.visaCard') }}
                    <span class="min-text">{{ lossAccount.accountNumber }}</span>
                  </p>
                </el-col>
                <el-col :xs="24" :lg="8">
                  <p class="p-select-info"><PERSON></p>
                </el-col>
                <el-col :xs="24" :lg="8">
                  <p class="p-select-info min-text">

                    {{ lossAccount.ccyCode + ' ' + $lbs.decimal_format(lossAccount.approvedLimit) }}
                  </p>
                </el-col>
              </el-row>
            </div>
          </el-col>
        </el-row>
        <el-row>
          <div class="form-header">
            <el-col :span="12">
              <span class="text-form">{{ $t('lbs.youInformation') }}</span>
            </el-col>
            <el-col :span="10" :offset="2">
              <div class="edit" @click="submit('edit')">
                <u class="text-u">
                  <span class="text-form">{{ $t('lbs.edit') }}</span>
                </u>
                <svg-icon class="lbs-icon" icon-class="awesome-edit" />
              </div>
            </el-col>
          </div>
          <el-col :span="10" :offset="2">
            <div class="form-item">
              <span class="text-form-item">{{ $t('lbs.lossReason') }}</span>
            </div>
          </el-col>
          <el-col :span="10" :offset="2">
            <div class="form-item">
              <span class="text-form-item">{{ lossAccount.reason }}</span>
            </div>
          </el-col>
          <el-col :span="10" :offset="2">
            <div class="form-item">
              <span class="text-form-item">{{ $t('lbs.happenDid') }}</span>
            </div>
          </el-col>
          <el-col :span="10" :offset="2">
            <div class="form-item">
              <span class="text-form-item">{{ lossAccount.happenwhere }}</span>
            </div>
          </el-col>
          <el-col :span="10" :offset="2">
            <div class="form-item">
              <span class="text-form-item">{{ $t('lbs.happenTime') }}</span>
            </div>
          </el-col>
          <el-col :span="10" :offset="2">
            <div class="form-item">
              <span class="text-form-item">{{ lossAccount.happendate }}</span>
            </div>
          </el-col>

          <el-col :span="24">
            <div class="form-header-bottom">
              <span class="text-form">{{ $t('lbs.sendTo') }}</span>
            </div>
          </el-col>

          <el-col :span="10" :offset="2">
            <div class="form-item">
              <span class="text-form-item">{{ $t('lbs.replacement') }}</span>
            </div>
          </el-col>
          <el-col :span="10" :offset="2">
            <div class="form-item">
              <span class="text-form-name">Chan Siu Ming</span>
            </div>
          </el-col>
        </el-row>
        <el-col :span="24">
          <div class="clause">
            <p class="clause-p">{{ $t('lbs.item1') }}</p>
            <p class="clause-p">
              {{ $t('lbs.item2') }}
            </p>
            <el-checkbox-group v-model="isSelect">
              <el-checkbox name="type" />
              <p class="clause-p clause-ok">
                {{ $t('lbs.item6') }}
              </p>
            </el-checkbox-group>
          </div>
        </el-col>
        <el-row>
          <el-col :span="24">
            <div class="button-group">
              <el-button type="danger" @click.prevent="submit('back')">{{ $t('lbs.cancel') }}</el-button>
              <el-button type="primary" @click.prevent="submit('continue')">{{ $t('lbs.Continue') }}</el-button>
            </div>
          </el-col>
        </el-row>
      </el-col>
    </el-row>
  </div>
</template>

<script>
export default {
  name: 'Lossform',
  data() {
    return {
      loading: false,
      token: null,
      lossAccount: {},
      isSelect: [],
      creditCardAccountList: []
    }
  },
  mounted() {
    this.token = window.sessionStorage.getItem('token')
    this.lossAccount = JSON.parse(window.sessionStorage.getItem('lossAccount'))
    this.creditCardAccountList = JSON.parse(window.sessionStorage.getItem('creditCardaccountlist'))
  },
  methods: {
    submit(type) {
      if (type === 'back') {
        this.$router.push({ path: '/lbsaccountservice/creditcarddetail' })
      } else if (type === 'deit') {
        this.$router.push({
          path: '/lbsaccountservice/cardlossreporting/lossinfo'
        })
      } else if (type === 'continue') {
        if (this.isSelect.length === 1) {
          this.getaccountbalance()
        } else {
          this.$mui.alert(
            this.$t('lbs.item5'),
            'Error',
            'OK'
          )
        }
      }
    },
    getaccountbalance(account) {
      var vue = this
      var requestdata = {
        creditcardnumber: this.lossAccount.accountNumber
      }
      this.$mui.ajax(
        this.LBSGateway + '/creditcard-experience/creditcard/lossReporting',
        {
          data: requestdata,
          dataType: 'json', // 服务器返回json格式数据
          type: 'post', // HTTP请求类型
          timeout: 60000,
          headers: {
            accept: '*/*',
            token: vue.token,
            clientid: vue.$parent.clientid,
            messageid: vue.$parent.messageid,
            'Content-Type': 'application/json'
          },
          beforeSend: function() {
            vue.loading = true
          },
          complete: function() {
            vue.loading = false
          },
          success: function(data) {
            if (data.code === '200') {
              for (let i = 0; i < vue.creditCardAccountList.length; i++) {
                if (vue.creditCardAccountList[i].accountNumber === vue.lossAccount.accountNumber) {
                  vue.creditCardAccountList[i].accountStatus = 'L'
                }
              }
              window.sessionStorage.setItem('creditCardaccountlist', JSON.stringify(vue.creditCardAccountList))
              console.log(vue.creditCardAccountList)
              vue.$router.push({
                path: '/lbsaccountservice/cardlossreporting/lossresult'
              })
            } else {
              // plus.nativeUI.closeWaiting();
              // mask.close();
              vue.$mui.alert(
                'Loss reporting failed! The response is: \n' +
                  JSON.stringify(data) +
                  '.',
                'Error',
                'OK'
              )
            }
          },
          error: function(xhr, type, errorThrown) {
            // plus.nativeUI.closeWaiting();
            // mask.close();
            console.log(type)
            var msg =
              'Loss reporting failed! The response is: \n' +
              xhr.responseText +
              '.'
            if (type === 'timeout') {
              msg = 'Loss reporting failed. Time out!'
            }
            vue.$mui.alert(msg, 'Error', 'OK')
          }
        }
      )
    }
  }
}
</script>

<style scoped>
.lossform .div-select-info {
  width: 100%;
  height: auto;
  border: 1px #22c1e6 solid;
  padding: 0 0 30px;
  margin: 20px 0 60px;
}

.lossform .p-select-info {
  margin: 30px 0 0 0;
  font-size: 20px;
  text-align: center;
}

.lossform .lbs-icon {
  width: 30px;
  height: 23px;
}

.lossform .text-form {
  font-size: 20px;
}

.lossform .text-form-item {
  color: #707070;
}

.lossform .text-form-name {
  color: #000000;
}

.lossform .text-u {
  color: #707070;
}

.lossform .form-header {
  margin-bottom: 60px;
}

.lossform .form-header-bottom {
  margin-bottom: 40px;
}

.lossform .clause {
  width: 100%;
  background-color: #e2e2e2;
  padding: 15px;
  margin-top: 20px;
}

.lossform .clause-p {
  color: #000000;
}

.lossform .clause-ok {
  display: inline-block;
  margin-left: 50px;
}

.lossform .button-group {
  margin: 30px 20px 20px 0;
  float: right;
  min-width: 200px;
}

.lossform .form-item {
  margin-bottom: 40px;
}

.lossform .edit {
  cursor: pointer;
  width: 100px;
}
</style>
