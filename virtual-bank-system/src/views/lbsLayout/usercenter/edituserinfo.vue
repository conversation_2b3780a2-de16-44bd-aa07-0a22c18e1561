<template>
  <div class="edituserinfo">
    <el-row>
      <el-col :span="24">
        <div class="title">
          <p class="max-title">{{ $t('lbs.userInformation') }}</p>
        </div>
      </el-col>
    </el-row>
    <div class="main">
      <el-row>
        <el-col :span="8">
          <p class="title2">{{ $t('lbs.personalInformation') }}</p>
          <div class="image">
            <img alt="" src="./header.png">
          </div>
        </el-col>
        <el-col :span="16" class="userinfo">
          <el-row>
            <el-col :lg="{span:24,offset:1}" :xs="24">
              <p class="key">{{ $t('lbs.name') }}</p>
              <p class="value">{{ userInfo.customerName }}</p>
            </el-col>
            <el-col :lg="{span:24,offset:1}" :xs="24">
              <p class="key">{{ $t('lbs.chineseName') }}</p>
              <p class="value">{{ userInfo.chinesename }}</p>
            </el-col>
            <el-col :lg="{span:24,offset:1}" :xs="24">
              <p class="key editinput">ID</p>
              <p class="value">{{ userInfo.customerid }}</p>
            </el-col>
            <el-col :lg="{span:24,offset:1}" :xs="24">
              <p class="key">{{ $t('lbs.gender') }}</p>
              <p class="value">{{ userInfo.gender }}</p>
            </el-col>
            <el-col :lg="{span:24,offset:1}" :xs="24">
              <p class="key">{{ $t('lbs.nationality') }}</p>
              <p class="value">{{ userInfo.issuecountry }}</p>
            </el-col>
            <el-col :lg="{span:24,offset:1}" :xs="24">
              <p class="key">{{ $t('lbs.birthDay') }}</p>
              <p class="value">{{ $moment.parseZone(Number(userInfo.dateofbirth)).local().format("YYYY-MM-DD") }}</p>
            </el-col>
          </el-row>
        </el-col>
      </el-row>
    </div>
    <div class="main">
      <el-row>
        <el-col :span="8">
          <div style="height: 190px">
            <p class="title2" style="margin: 110px 0 10px">{{ $t('lbs.contactInformation') }}</p>
          </div>
        </el-col>
        <el-col :span="16" class="userinfo" style="padding-top: 0">
          <el-row>
            <el-col :lg="{span:24,offset:1}" :xs="24">
              <p class="key editinput">{{ $t('lbs.mobilePhoneNumber') }}</p>
              <span style="display: inline-block"><el-input
                v-model="userInfo.mobilephonenumber"
                placeholder="Mobile Phone Number"
                clearable
              /></span>
            </el-col>
            <el-col :lg="{span:24,offset:1}" :xs="24">
              <p class="key editinput">{{ $t('lbs.residentialAddress') }}</p>
              <span style="display: inline-block"><el-input
                v-model="userInfo.residentialaddress"
                placeholder="Mobile Phone Number"
                clearable
              /></span>
            </el-col>
            <el-col :lg="{span:24,offset:1}" :xs="24">
              <p class="key editinput">{{ $t('lbs.mailingAddress') }}</p>
              <span style="display: inline-block"><el-input
                v-model="userInfo.mailingaddress"
                placeholder="Mobile Phone Number"
                clearable
              /></span>
            </el-col>
            <el-col :lg="{span:24,offset:1}" :xs="24">
              <p class="key editinput">{{ $t('lbs.residencePhoneNumber') }}</p>
              <span style="display: inline-block"><el-input
                v-model="userInfo.residencephonenumber"
                placeholder="Mobile Phone Number"
                clearable
              /></span>
            </el-col>
            <el-col :lg="{span:24,offset:1}" :xs="24">
              <p class="key editinput">{{ $t('lbs.companyEmail') }}</p>
              <span style="display: inline-block"><el-input
                v-model="userInfo.emailaddress"
                placeholder="Mobile Phone Number"
                clearable
              /></span>
            </el-col>
            <el-col :lg="{span:24,offset:1}" :xs="24">
              <p class="key">{{ $t('lbs.weChatId') }}</p>
              <p class="value">{{ userInfo.wechatid }}</p>
            </el-col>
          </el-row>
        </el-col>
      </el-row>
    </div>
    <div class="main" style="border-bottom: none">
      <el-row>
        <el-col :span="8">
          <div style="height: 220px">
            <p class="title2" style="margin: 160px 0 10px">{{ $t('lbs.workingInformation') }}</p>
          </div>
        </el-col>
        <el-col :span="16" class="userinfo" style="padding-top: 0">
          <el-row>
            <el-col :lg="{span:24,offset:1}" :xs="24">
              <p class="key">{{ $t('lbs.occupation') }}</p>
              <p class="value">{{ userInfo.occupation }}</p>
            </el-col>
            <el-col :lg="{span:24,offset:1}" :xs="24">
              <p class="key">{{ $t('lbs.employerCompanyName') }}</p>
              <p class="value">{{ userInfo.employercompanyname }}</p>
            </el-col>
            <el-col :lg="{span:24,offset:1}" :xs="24">
              <p class="key">{{ $t('lbs.position') }}</p>
              <p class="value">{{ userInfo.position }}</p>
            </el-col>
            <el-col :lg="{span:24,offset:1}" :xs="24">
              <p class="key">{{ $t('lbs.companyAddress') }}</p>
              <p class="value">{{ userInfo.companyaddress }}</p>
            </el-col>
            <el-col :lg="{span:24,offset:1}" :xs="24">
              <p class="key">{{ $t('lbs.companyPhoneNumber') }}</p>
              <p class="value">{{ userInfo.companyphonenumber }}</p>
            </el-col>
            <el-col :lg="{span:24,offset:1}" :xs="24">
              <p class="key">{{ $t('lbs.yearsOfServices') }}</p>
              <p class="value">{{ userInfo.yearsofservices }}</p>
            </el-col>
            <el-col :lg="{span:24,offset:1}" :xs="24">
              <p class="key">{{ $t('lbs.monthlySalary') }}</p>
              <p class="value">{{ userInfo.monthlysalary }}</p>
            </el-col>
          </el-row>
        </el-col>
      </el-row>
    </div>
    <el-row>
      <el-col :span="24">
        <el-button type="primary" style="float: right;margin-right: 200px" @click="update()">{{ $t('lbs.update') }}</el-button>
        <el-button type="danger" style="float: right;margin-right: 20px" @click="back()">{{ $t('lbs.cancel') }}</el-button>
      </el-col>
    </el-row>
  </div>
</template>

<script>
export default {
  name: 'Edituserinfo',
  data() {
    return {
      userInfo: {}
    }
  },
  mounted() {
    this.userInfo = JSON.parse(window.sessionStorage.getItem('userInfo'))
  },
  methods: {
    getparams() {
      if (!this.userInfo.customerid || this.userInfo.customerid.length === 0) {
        this.$mui.alert('Please enter the Customer ID.')
        return false
      }
      if (!this.userInfo.emailaddress || this.userInfo.emailaddress.length === 0) {
        this.$mui.alert('Please enter the Email Address.')
        return false
      }
      if (!this.userInfo.mailingaddress || this.userInfo.mailingaddress.length === 0) {
        this.$mui.alert('Please enter the Mailing Address.', 'Warning', 'OK')
        return false
      }
      if (!this.userInfo.mobilephonenumber || this.userInfo.mobilephonenumber.length === 0) {
        this.$mui.alert('Please enter the Mobile Phone Number.', 'Warning', 'OK')
        return false
      }
      if (!this.userInfo.residencephonenumber || this.userInfo.residencephonenumber.length === 0) {
        this.$mui.alert('Please enter the Residence Phone Number.', 'Warning', 'OK')
        return false
      }
      if (!this.userInfo.residentialaddress || this.userInfo.residentialaddress.length === 0) {
        this.$mui.alert('Please enter the Residential Address.', 'Warning', 'OK')
        return false
      }
      return {
        'customerID': this.userInfo.customerid,
        'emailaddress': this.userInfo.emailaddress,
        'mailingAddress': this.userInfo.mailingaddress,
        'mobilePhoneNumber': this.userInfo.mobilephonenumber,
        'residencephonenumber': this.userInfo.residencephonenumber,
        'residentialaddress': this.userInfo.residentialaddress
      }
    },
    deposit(requestdata) {
      var vue = this
      var token = window.sessionStorage.getItem('token')
      this.$mui.ajax(this.LBSGateway + '/deposit-experience/customer/contactInfoUpdate', {
        data: requestdata,
        dataType: 'json', // 服务器返回json格式数据
        type: 'post', // HTTP请求类型
        timeout: 60000,
        headers: {
          'accept': '*/*',
          'token': token,
          'clientid': vue.$parent.clientid,
          'messageid': vue.$parent.messageid,
          'Content-Type': 'application/json'

        },
        beforeSend: function() {
          // plus.nativeUI.showWaiting("Loading…", "div");
          // mask.show();
          // document.getElementById("load").style.display = "inline";
        },
        complete: function() {
          // plus.nativeUI.closeWaiting();
          // mask.close();
          // document.getElementById("load").style.display = "none";
        },
        success: function(data) {
          if (data.code === '200') {
            vue.$mui.alert('Update Successed!', 'Success', 'OK', function() {
              window.sessionStorage.setItem('userInfo', JSON.stringify(vue.userInfo))
              vue.$router.push({ path: '/lbsusercenter/userinformation' })
            })
          }
        },
        error: function(xhr, type, errorThrown) {
          console.log(type)
          var msg = 'Update user info failed! The response is: \n' + xhr.responseText + '.'
          if (type === 'timeout') {
            msg = 'Update user info failed. Time out!'
          }
          vue.$mui.alert(msg, 'Error', 'OK')
        }
      })
    },
    update() {
      var requestdata = this.getparams()
      if (!requestdata) return false
      this.deposit(requestdata)
    },
    back() {
      this.$router.push({ path: '/lbsusercenter/userinformation' })
    }
  }
}
</script>

<style scoped>
  .edituserinfo .title {
    padding: 30px 28px;
    height: 140px;
    background-color: #FFFFFF;
    margin: 0 25px 0 25px;
    border-radius: 50px;
    background-image: url("../components/title-bg.png");
    background-repeat: no-repeat;
    background-size: 574px 130px;
    background-position: right;
    box-shadow: 5px 5px 5px #bbbbbb;
  }

  .edituserinfo p {
    color: #707070;
    margin-bottom: 0;
    line-height: 1;
  }

  .edituserinfo .max-title {
    color: #22C1E6;
    font-size: 50px;
    margin-bottom: 40px;
    line-height: 80px;
    font-weight: 500;
  }

  .edituserinfo .main {
    margin: 40px 100px;
    border-bottom: 1px #cccccc solid;
    padding: 10px 0 40px
  }

  .edituserinfo .title2 {
    text-align: center;
    color: #707070;
    font-size: 40px;
    margin-bottom: 40px;
  }

  .edituserinfo .image {
    width: 308px;
    margin: 0 auto;
  }

  .edituserinfo .userinfo p{
    font-size: 25px;
    line-height: 53px;
    display: inline-block;
  }

  .edituserinfo .userinfo .value{
    font-size: 22px;
    color: #000000;
  }
  .edituserinfo .userinfo {
    padding-top: 40px;
  }
  .edituserinfo .key{
    width: 450px;
  }

  .edituserinfo .editinput {
    display: inline;
  }
</style>

<style>
  .edituserinfo .el-input--suffix .el-input__inner {
    width: 300px;
    padding-right: 25px;
    margin-bottom: 0;
  }
</style>
