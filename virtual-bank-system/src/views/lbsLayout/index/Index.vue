<template>
  <div v-loading.fullscreen.lock="loading" class="lbs-home">
    <el-row>
      <el-col :span="24">
        <div class="title">
          <p>{{ $t('lbs.welcomeBack') }}, {{ username }}</p>
        </div>
      </el-col>
    </el-row>
    <el-row>
      <el-col :lg="18" :xs="24">
        <el-tooltip effect="dark" placement="right">
          <div slot="content" style="width: 300px">
            <p>{{ $t('lbsTips.dashboardTip.mynetworth') }}</p>
          </div>
          <p class="two-title">{{ $t('lbs.title1') }}</p>
        </el-tooltip>
        <div class="net-worth" style="padding-bottom: 20px">
          <el-tooltip effect="dark" placement="right">
            <div slot="content" style="width: 300px">
              <p>{{ $t('lbsTips.dashboardTip.Current') }}</p>
              <p>{{ $t('lbsTips.dashboardTip.Saving') }}</p>
              <p>{{ $t('lbsTips.dashboardTip.TermDeposit') }}</p>
            </div>
            <div id="ihave" style="width: 350px;height: 180px;display: inline-block" />
          </el-tooltip>
          <el-tooltip effect="dark" placement="right">
            <div slot="content" style="width: 300px">
              <p>{{ $t('lbsTips.dashboardTip.CreditCard') }}</p>
              <p>{{ $t('lbsTips.dashboardTip.Loan') }}</p>
            </div>
            <div id="iowe" style="width: 350px;height: 180px;display: inline-block" />
          </el-tooltip>
          <el-tooltip effect="dark" placement="right">
            <div slot="content" style="width: 300px">
              <p>{{ $t('lbsTips.dashboardTip.Stock') }}</p>
              <p>{{ $t('lbsTips.dashboardTip.FEX') }}</p>
              <p>{{ $t('lbsTips.dashboardTip.preciousmetal') }}</p>
              <p>{{ $t('lbsTips.dashboardTip.mutualfund') }}</p>
            </div>
            <div id="investment" style="width: 350px;height: 180px;display: inline-block" />
          </el-tooltip>
          <div>
            <el-tooltip effect="dark" placement="right">
              <div slot="content" style="width: 300px">
                <p>{{ $t('lbsTips.dashboardTip.networth') }}</p>
              </div>
              <div class="amount">
                <p>{{ $t('lbs.tag1') }}</p>
                <p>
                  {{ 'HKD' + ' ' + $lbs.decimal_format(Number(currentAmount + savingAmount + termDepositAmount +
                    stockAmount + mutualfundAmount +
                    preciousmetalAmount + fexAmount + creditCardAmount).toFixed(2)) }}
                </p>
              </div>
            </el-tooltip>
            <el-tooltip effect="dark" placement="right">
              <div slot="content" style="width: 300px">
                <p>{{ $t('lbsTips.dashboardTip.Cash') }}</p>
              </div>
              <div class="amount">
                <p>{{ $t('lbs.tag2') }}</p>
                <p>{{ 'HKD' + ' ' + $lbs.decimal_format(Number(currentAmount + savingAmount)) }}</p>
              </div>
            </el-tooltip>
            <el-tooltip effect="dark" placement="right">
              <div slot="content" style="width: 300px">
                <p>{{ $t('lbsTips.dashboardTip.Debt') }}</p>
              </div>
              <div class="amount">
                <p>{{ $t('lbs.tag3') }}</p>
                <p>{{ 'HKD' + ' ' + $lbs.decimal_format(Number(creditCardAmount + loanAmount + pendingAmount)) }}</p>
              </div>
            </el-tooltip>
          </div>
        </div>
      </el-col>
      <el-col :lg="6" :sm="24" :md="24" :xs="24">
        <p class="two-title">{{ $t('lbs.title2') }}</p>
        <div
          style="margin-top: 15px;padding: 25px;background-color: #FFFFFF;box-shadow: 1px 3px 5px #bbbbbb;"
        >
          <el-row>
            <el-col
              :span="8"
              class="el-col el-col-8 el-col-xs-24 el-col-md-12 el-col-lg-12 el-col-xl-8"
            >
              <div class="quick-item">
                <div class="div-icon">
                  <svg-icon icon-class="feather-credit-card" style="width: 40px; height: 40px" />
                </div>
                <div class="div-text">
                  <p style="color: #707070;">{{ $t('lbs.tag6') }}</p>
                </div>
              </div>
            </el-col>
            <el-tooltip effect="dark" placement="top">
              <div slot="content" style="width: 300px">{{ $t('lbsTips.dashboardTip.NewDeposit') }}</div>
              <el-col
                :span="8"
                class="el-col el-col-8 el-col-xs-24 el-col-md-12 el-col-lg-12 el-col-xl-8"
              >
                <div class="quick-item" @click="$router.push({path: '/lbsaccountservice/termdeposit/applydeposit'})">
                  <div class="div-icon">
                    <svg-icon icon-class="metro-credit-card" style="width: 40px; height: 40px" />
                  </div>
                  <div class="div-text">
                    <p style="color: #707070;">{{ $t('lbs.tag7') }}</p>
                  </div>
                </div>
              </el-col>
            </el-tooltip>
            <el-tooltip effect="dark" placement="top">
              <div
                slot="content"
                style="width: 300px"
              >{{ $t('lbsTips.dashboardTip.InstallmentCalculator') }}
              </div>
              <el-col
                :span="8"
                class="el-col el-col-8 el-col-xs-24 el-col-md-12 el-col-lg-12 el-col-xl-8"
              >
                <div class="quick-item">
                  <div class="div-icon">
                    <svg-icon icon-class="ionic-md-calculator" style="width: 40px; height: 40px" />
                  </div>
                  <div class="div-text">
                    <p style="color: #707070;">{{ $t('lbs.tag8') }}</p>
                  </div>
                </div>
              </el-col>
            </el-tooltip>
            <el-tooltip effect="dark" placement="top">
              <div slot="content" style="width: 300px">{{ $t('lbsTips.dashboardTip.Transfer') }}</div>
              <el-col
                :span="8"
                class="el-col el-col-8 el-col-xs-24 el-col-md-12 el-col-lg-12 el-col-xl-8"
              >
                <div class="quick-item" @click="$router.push({path: '/lbstransaction/transfer'})">
                  <div class="div-icon">
                    <svg-icon
                      icon-class="awesome-hand-holding-usd"
                      style="width: 40px; height: 40px"
                    />
                  </div>
                  <div class="div-text">
                    <p style="color: #707070;">{{ $t('lbs.tag9') }}</p>
                  </div>
                </div>
              </el-col>
            </el-tooltip>
          </el-row>
        </div>
      </el-col>
    </el-row>
    <el-row>
      <el-col :lg="20" :xs="24">
        <div style="padding-left: 65px">
          <svg-icon icon-class="awesome-exchange-alt" style="width: 22px;height: 22px" />
          <el-tooltip effect="dark" placement="right">
            <div
              slot="content"
              style="width: 300px"
            >{{ $t('lbsTips.dashboardTip.RecentTransaction') }}
            </div>
            <p class="two-title" style="margin-left: 20px">{{ $t('lbs.title3') }}</p>
          </el-tooltip>
          <div
            style="margin: 15px 65px 15px 0;padding: 25px;background-color: #FFFFFF;box-shadow: 1px 3px 5px #bbbbbb;"
          >
            <table
              cellspacing="0"
              cellpadding="0"
              border="0"
              class="el-table__header el-table"
              style="width: 100%;line-height: 40px;"
            >
              <colgroup />
              <thead class="has-gutter">
                <tr class>
                  <th colspan="1" rowspan="1" class="el-table_2_column_5 is-leaf">
                    <el-tooltip effect="dark" placement="top">
                      <div
                        slot="content"
                        style="width: 300px"
                      >{{ $t('lbsTips.accountDetailTip.TransactionDate') }}</div>
                      <div class="cell">{{ $t('lbsTips.accountDetailTip.Date') }}</div>
                    </el-tooltip>
                  </th>
                  <th colspan="1" rowspan="1" class="el-table_2_column_6 is-leaf">
                    <el-tooltip effect="dark" placement="top">
                      <div
                        slot="content"
                        style="width: 300px"
                      >{{ $t('lbsTips.accountDetailTip.TransactionDetails') }}</div>
                      <div class="cell">{{ $t('lbsTips.accountDetailTip.Details') }}</div>
                    </el-tooltip>
                  </th>
                  <th colspan="1" rowspan="1" class="el-table_2_column_7 is-leaf">
                    <el-tooltip effect="dark" placement="top">
                      <div
                        slot="content"
                        style="width: 300px"
                      >{{ $t('lbsTips.accountDetailTip.TransactionAmount') }}</div>
                      <div class="cell">{{ $t('lbsTips.accountDetailTip.TransactionAmountTh') }}</div>
                    </el-tooltip>
                  </th>
                  <th class="gutter" style="width: 15px;" />
                </tr>
              </thead>
            </table>
            <el-table
              :data="currentsavingaccounthistory"
              height="225"
              :show-header="false"
              style="width: 100%"
            >
              <el-table-column prop="trandate" :label="$t('lbsTips.accountDetailTip.Date')" />
              <el-table-column prop="trandesc" :label="$t('lbsTips.accountDetailTip.Details')" />
              <el-table-column prop="tranamt" :label="$t('lbsTips.accountDetailTip.TransactionAmountTh')" />
            </el-table>
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>

export default {
  name: 'Index',
  data() {
    return {
      loading: false,
      token: null,
      username: '',
      tableData: [],
      currentAmount: 0,
      savingAmount: 0,
      termDepositAmount: 0,
      stockAmount: 0,
      mutualfundAmount: 0,
      preciousmetalAmount: 0,
      fexAmount: 0,
      creditCardAmount: 0,
      loanAmount: 0,
      pendingAmount: 0,
      currentsavingaccounthistory: null,
      accountList: [],
      investment: {},
      iowe: {},
      ihave: {},
      investmentoption: {},
      ioweoption: {},
      ihaveoption: {}
    }
  },
  computed: {
    language() {
      return this.$store.state.app.language
    }
  },
  watch: {
    language: {
      handler(newValue) {
        if (newValue === 'zh') {
          this.generateTable('53', '53', '67')
        } else {
          this.generateTable('60', '60', '60')
        }
      }
    }
  },
  mounted() {
    if (
      window.sessionStorage.getItem('showTips') === 'true' &&
        window.sessionStorage.getItem('token')
    ) {
      this.$alert(this.$t('lbs.popupDashboard'), this.$t('lbs.Tip'), {
        dangerouslyUseHTMLString: true
      })
    }
    if (window.sessionStorage.getItem('token')) {
      this.token = window.sessionStorage.getItem('token')
      this.username = window.sessionStorage.getItem('username')
      // Current
      this.generalcurrentaccountlist()
      this.generalsavingaccountlist()
      this.generaltermdepositaccountlist()
      this.generalstockaccountlist()
      this.generalmutualfundaccountlist()
      this.generalpreciousmetalaccountlist()
      this.generalfexaccountlist()
      this.generalcreditcardaccountlist()
      this.getcurrentsavingaccounthistory(
        this.$parent.getcurrentnumberpickerdata()[0].value
      )
    } else {
      this.$router.push({ path: '/lbslogin' })
    }
  },
  methods: {
    generalcurrentaccountlist() {
      var vue = this
      var currentaccountlist = this.$parent.getcurrentnumberpickerdata()

      for (var i = 0; i < currentaccountlist.length; i++) {
        this.$mui.ajax(
          this.LBSGateway +
            '/deposit-experience/deposit/account/accountDetails/' +
            currentaccountlist[i].value,
          {
            dataType: 'json',
            headers: {
              accept: '*/*',
              token: vue.token,
              clientid: vue.$parent.clientid,
              messageid: vue.$parent.messageid
            },
            type: 'post',
            timeout: 60000,
            beforeSend: function() {
              vue.loading = true
            },
            complete: function() {
              vue.loading = false
            },
            success: function(result1) {
              // console.log("result1: "+JSON.stringify(result1))
              if (result1 && result1.code === '200') {
                var data1 = result1.data.account
                if (!data1.availablebalance) data1.availablebalance = 0.0
                if (data1.availablebalance) {
                  data1.availablebalance = Number(
                    data1.availablebalance
                      .toString()
                      .replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3')
                  )
                }
                vue.accountList.push({
                  accountType: 'Current',
                  availablebalance: data1.availablebalance,
                  accountNumber: data1.accountnumber,
                  ccy: data1.currencycode
                })
                vue.currentAmount = Number(data1.availablebalance.toFixed(2))
                window.sessionStorage.setItem(
                  'accountList',
                  JSON.stringify(vue.accountList)
                )
                if (vue.$store.state.app.language === 'zh') {
                  vue.generateTable('53', '53', '67')
                } else {
                  vue.generateTable('60', '60', '60')
                }
              }
            },
            error: function(xhr, type, errorThrown) {
              console.log(type)
              var msg =
                  'Get current account detail failed! The response is: \n' +
                  xhr.responseText +
                  '.'
              if (type === 'timeout') {
                msg = 'Get current account detail failed. Time out!'
              }

              if (
                typeof JSON.parse(xhr.responseText) === 'object' &&
                  JSON.parse(xhr.responseText).status === '202001'
              ) {
                msg = JSON.parse(xhr.responseText).error
              } else if (
                typeof JSON.parse(xhr.responseText) === 'object' &&
                  JSON.parse(xhr.responseText).status === '404004'
              ) {
                msg = JSON.parse(xhr.responseText).error
              }
              vue.$mui.alert(msg, 'Error', 'OK')
            }
          }
        )
      }
    },
    // 生成saving account列表
    generalsavingaccountlist() {
      var savingaccountlist = this.$parent.getsavingnumberpickerdata()
      var vue = this

      for (var i = 0; i < savingaccountlist.length; i++) {
        this.$mui.ajax(
          this.LBSGateway +
            '/deposit-experience/deposit/account/accountDetails/' +
            savingaccountlist[i].value,
          {
            dataType: 'json',
            headers: {
              accept: '*/*',
              token: vue.token,
              clientid: vue.$parent.clientid,
              messageid: vue.$parent.messageid
            },
            type: 'post',
            timeout: 60000,
            beforeSend: function() {
              vue.loading = true
            },
            complete: function() {
              vue.loading = false
            },
            success: function(result1) {
              // console.log("result1: "+JSON.stringify(result1))
              if (result1 && result1.code === '200') {
                var data1 = result1.data.account
                if (!data1.availablebalance) data1.availablebalance = 0.0
                if (data1.availablebalance) {
                  data1.availablebalance = Number(
                    data1.availablebalance
                      .toString()
                      .replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3')
                  )
                }
                vue.accountList.push({
                  accountType: 'Saving',
                  availablebalance: data1.availablebalance,
                  accountNumber: data1.accountnumber,
                  ccy: data1.currencycode
                })
                vue.savingAmount = Number(data1.availablebalance.toFixed(2))
                window.sessionStorage.setItem(
                  'accountList',
                  JSON.stringify(vue.accountList)
                )
                if (vue.$store.state.app.language === 'zh') {
                  vue.generateTable('53', '53', '67')
                } else {
                  vue.generateTable('60', '60', '60')
                }
              }
            },
            error: function(xhr, type, errorThrown) {
              console.log(type)
              var msg =
                  'Get saving account detail failed! The response is: \n' +
                  xhr.responseText +
                  '.'
              if (type === 'timeout') {
                msg = 'Get saving account detail failed. Time out!'
              }
              if (
                typeof JSON.parse(xhr.responseText) === 'object' &&
                  JSON.parse(xhr.responseText).code === '202001'
              ) {
                msg = JSON.parse(xhr.responseText).error
              } else if (
                typeof JSON.parse(xhr.responseText) === 'object' &&
                  JSON.parse(xhr.responseText).code === '404004'
              ) {
                msg = JSON.parse(xhr.responseText).error
              }
              vue.$mui.alert(msg, 'Error', 'OK')
            }
          }
        )
      }
    },
    // 生成term deposit account列表
    generaltermdepositaccountlist() {
      var termDepositaccountlist = this.$parent.gettermdepositnumberpickerdata()
      var vue = this

      for (var i = 0; i < termDepositaccountlist.length; i++) {
        this.$mui.ajax(
          this.LBSGateway +
            '/deposit-experience/deposit/account/accountDetails/' +
            termDepositaccountlist[i].value,
          {
            dataType: 'json',
            headers: {
              accept: '*/*',
              token: vue.token,
              clientid: vue.$parent.clientid,
              messageid: vue.$parent.messageid
            },
            type: 'post',
            timeout: 60000,
            beforeSend: function() {
              vue.loading = true
            },
            complete: function() {
              vue.loading = false
            },
            success: function(result1) {
              // console.log("result1: "+JSON.stringify(result1))
              if (result1 && result1.code === '200') {
                var data1 = result1.data.account
                if (!data1.ledgerbalance) data1.ledgerbalance = 0.0
                if (data1.ledgerbalance) {
                  data1.ledgerbalance = Number(
                    data1.ledgerbalance
                      .toString()
                      .replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3')
                  )
                }
                vue.termDepositAmount = Number(data1.ledgerbalance.toFixed(2))
                if (vue.$store.state.app.language === 'zh') {
                  vue.generateTable('53', '53', '67')
                } else {
                  vue.generateTable('60', '60', '60')
                }
              }
            },
            error: function(xhr, type, errorThrown) {
              console.log(type)
              var msg =
                  'Get term deposit account detail failed! The response is: \n' +
                  xhr.responseText +
                  '.'
              if (type === 'timeout') {
                msg = 'Get term deposit account detail failed. Time out!'
              }

              if (
                typeof JSON.parse(xhr.responseText) === 'object' &&
                  JSON.parse(xhr.responseText).code === '202001'
              ) {
                msg = JSON.parse(xhr.responseText).error
              } else if (
                typeof JSON.parse(xhr.responseText) === 'object' &&
                  JSON.parse(xhr.responseText).code === '404004'
              ) {
                msg = JSON.parse(xhr.responseText).error
              }
              vue.$mui.alert(msg, 'Error', 'OK')
            }
          }
        )
      }
    },
    // 生成stock account列表
    generalstockaccountlist() {
      this.stockaccountlist = this.$parent.getstocknumberpickerdata()
      var vue = this
      for (var i = 0; i < this.stockaccountlist.length; i++) {
        this.$mui.ajax(
          this.LBSGateway + '/stock-experience/stock/stockHoldingEnquiry',
          {
            data: {
              stkaccountnumber: vue.stockaccountlist[i].value,
              stockcode: ''
            },
            dataType: 'json',
            headers: {
              accept: '*/*',
              token: vue.token,
              clientid: vue.$parent.clientid,
              messageid: vue.$parent.messageid,
              'Content-Type': 'application/json'
            },
            type: 'post',
            async: false,
            timeout: 60000,
            beforeSend: function() {
              vue.loading = true
            },
            complete: function() {
              vue.loading = false
            },
            success: function(result5) {
              console.log('result5: ' + JSON.stringify(result5))
              if (result5 && result5.code === '200') {
                var data5 = result5.data
                if (!data5.totalmarkervalue) data5.totalmarkervalue = 0.0
                if (typeof data5.totalmarkervalue !== 'number') {
                  data5.totalmarkervalue = Number(
                    data5.totalmarkervalue.replace(
                      /^(\-)*(\d+)\.(\d\d).*$/,
                      '$1$2.$3'
                    )
                  )
                }
                vue.stockAmount = Number(data5.totalmarkervalue.toFixed(2))
                if (vue.$store.state.app.language === 'zh') {
                  vue.generateTable('53', '53', '67')
                } else {
                  vue.generateTable('60', '60', '60')
                }
              }
            },
            error: function(xhr, type, errorThrown) {
              console.log(type)
              var msg =
                  'Get Stock account detail failed! The response is: \n' +
                  xhr.responseText +
                  '.'
              if (type === 'timeout') {
                msg = 'Get Stock account detail failed. Time out!'
              }
              if (
                typeof JSON.parse(xhr.responseText) === 'object' &&
                  JSON.parse(xhr.responseText).code === '202001'
              ) {
                msg = JSON.parse(xhr.responseText).error
                this.$mui.alert(msg, 'Error', 'OK')
              } else if (
                typeof JSON.parse(xhr.responseText) === 'object' &&
                  JSON.parse(xhr.responseText).code === '404004'
              ) {
                msg = JSON.parse(xhr.responseText).error
                this.$mui.alert(msg, 'Error', 'OK')
              } else if (
                typeof JSON.parse(xhr.responseText) === 'object' &&
                  JSON.parse(xhr.responseText).code === '404015'
              ) {
                console.log(JSON.parse(xhr.responseText).error)
              } else {
                this.$mui.alert(msg, 'Error', 'OK')
              }
            }
          }
        )
      }
    },
    // 生成mutual fund account列表
    generalmutualfundaccountlist() {
      var mutualFundaccountlist = this.$parent.getmutualfundnumberpickerdata()
      var vue = this
      for (var i = 0; i < mutualFundaccountlist.length; i++) {
        this.$mui.ajax(
          this.LBSGateway +
            '/deposit-experience/deposit/account/accountDetails/' +
            mutualFundaccountlist[i].value,
          {
            dataType: 'json',
            headers: {
              accept: '*/*',
              token: vue.token,
              clientid: vue.$parent.clientid,
              messageid: vue.$parent.messageid
            },
            type: 'post',
            timeout: 60000,
            beforeSend: function() {
              vue.loading = true
            },
            complete: function() {
              vue.loading = false
            },
            success: function(result6) {
              // console.log("result6: "+JSON.stringify(result6))
              if (result6 && result6.code === '200') {
                var data6 = result6.data.account
                if (!data6.ledgerbalance) data6.ledgerbalance = 0.0
                if (typeof data6.ledgerbalance !== 'number') {
                  data6.ledgerbalance = Number(
                    data6.ledgerbalance.replace(
                      /^(\-)*(\d+)\.(\d\d).*$/,
                      '$1$2.$3'
                    )
                  )
                }
                vue.mutualfundAmount = Number(data6.ledgerbalance.toFixed(2))
                if (vue.$store.state.app.language === 'zh') {
                  vue.generateTable('53', '53', '67')
                } else {
                  vue.generateTable('60', '60', '60')
                }
              }
            },
            error: function(xhr, type, errorThrown) {
              console.log(type)
              var msg =
                  'Get Mutual Fund account detail failed! The response is: \n' +
                  xhr.responseText +
                  '.'
              if (type === 'timeout') {
                msg = 'Get Mutual Fund account detail failed. Time out!'
              }

              if (
                typeof JSON.parse(xhr.responseText) === 'object' &&
                  JSON.parse(xhr.responseText).code === '202001'
              ) {
                msg = JSON.parse(xhr.responseText).error
              } else if (
                typeof JSON.parse(xhr.responseText) === 'object' &&
                  JSON.parse(xhr.responseText).code === '404004'
              ) {
                msg = JSON.parse(xhr.responseText).error
              }
              vue.$mui.alert(msg, 'Error', 'OK')
            }
          }
        )
      }
    },

    // 生成precious Metal account列表
    generalpreciousmetalaccountlist() {
      var preciousMetalaccountlist = this.$parent.getpreciousmetalnumberpickerdata()
      var vue = this
      for (var i = 0; i < preciousMetalaccountlist.length; i++) {
        this.$mui.ajax(
          this.LBSGateway +
            '/deposit-experience/deposit/account/accountDetails/' +
            preciousMetalaccountlist[i].value,
          {
            dataType: 'json',
            headers: {
              accept: '*/*',
              token: vue.token,
              clientid: vue.$parent.clientid,
              messageid: vue.$parent.messageid
            },
            type: 'post',
            timeout: 60000,
            beforeSend: function() {
              vue.loading = true
            },
            complete: function() {
              vue.loading = false
            },
            success: function(result7) {
              // console.log("result7: "+JSON.stringify(result7))
              if (result7 && result7.code === '200') {
                var data7 = result7.data.account
                if (!data7.availablebalance) data7.availablebalance = 0.0
                if (typeof data7.availablebalance !== 'number') {
                  data7.availablebalance = Number(
                    data7.availablebalance.replace(
                      /^(\-)*(\d+)\.(\d\d).*$/,
                      '$1$2.$3'
                    )
                  )
                }
                vue.preciousmetalAmount = Number(
                  data7.availablebalance.toFixed(2)
                )
                if (vue.$store.state.app.language === 'zh') {
                  vue.generateTable('53', '53', '67')
                } else {
                  vue.generateTable('60', '60', '60')
                }
              }
            },
            error: function(xhr, type, errorThrown) {
              console.log(type)
              var msg =
                  'Get Precious Metal account detail failed! The response is: \n' +
                  xhr.responseText +
                  '.'
              if (type === 'timeout') {
                msg = 'Get Precious Metal account detail failed. Time out!'
              }

              if (
                typeof JSON.parse(xhr.responseText) === 'object' &&
                  JSON.parse(xhr.responseText).code === '202001'
              ) {
                msg = JSON.parse(xhr.responseText).error
              } else if (
                typeof JSON.parse(xhr.responseText) === 'object' &&
                  JSON.parse(xhr.responseText).code === '404004'
              ) {
                msg = JSON.parse(xhr.responseText).error
              }
              vue.$mui.alert(msg, 'Error', 'OK')
            }
          }
        )
      }
    },

    // 生成Fex account列表
    generalfexaccountlist() {
      var fexaccountlist = this.$parent.getfexnumberpickerdata()
      var vue = this

      for (var i = 0; i < fexaccountlist.length; i++) {
        this.$mui.ajax(
          this.LBSGateway +
            '/deposit-experience/deposit/account/accountDetails/' +
            fexaccountlist[i].value,
          {
            dataType: 'json',
            headers: {
              accept: '*/*',
              token: vue.token,
              clientid: vue.$parent.clientid,
              messageid: vue.$parent.messageid
            },
            type: 'post',
            timeout: 60000,
            beforeSend: function() {
              vue.loading = true
            },
            complete: function() {
              vue.loading = false
            },
            success: function(result10) {
              // console.log("result10: "+JSON.stringify(result10))
              if (result10 && result10.code === '200') {
                var data10 = result10.data.account
                if (!data10.availablebalance) data10.availablebalance = 0.0
                if (typeof data10.availablebalance !== 'number') {
                  data10.availablebalance = Number(
                    data10.availablebalance.replace(
                      /^(\-)*(\d+)\.(\d\d).*$/,
                      '$1$2.$3'
                    )
                  )
                }
                vue.fexAmount = Number(data10.availablebalance.toFixed(2))
                if (vue.$store.state.app.language === 'zh') {
                  vue.generateTable('53', '53', '67')
                } else {
                  vue.generateTable('60', '60', '60')
                }
              }
            },
            error: function(xhr, type, errorThrown) {
              console.log(type)
              var msg =
                  'Get Foreign Exchange account detail failed! The response is: \n' +
                  xhr.responseText +
                  '.'
              if (type === 'timeout') {
                msg = 'Get Foreign Exchange account detail failed. Time out!'
              }

              if (
                typeof JSON.parse(xhr.responseText) === 'object' &&
                  JSON.parse(xhr.responseText).code === '202001'
              ) {
                msg = JSON.parse(xhr.responseText).error
              } else if (
                typeof JSON.parse(xhr.responseText) === 'object' &&
                  JSON.parse(xhr.responseText).code === '404004'
              ) {
                msg = JSON.parse(xhr.responseText).error
              }
              vue.$mui.alert(msg, 'Error', 'OK')
            }
          }
        )
      }
    },
    // 生成credit card account列表
    generalcreditcardaccountlist() {
      var vue = this
      this.creditCardaccountlist = this.$parent.getcreditcardnumberpickerdata()

      for (var i = 0; i < vue.creditCardaccountlist.length; i++) {
        this.$mui.ajax(
          this.LBSGateway +
            '/creditcard-experience/creditcard/creditLimitDetails',
          {
            dataType: 'json',
            data: { creditcardnumber: vue.creditCardaccountlist[i].value },
            headers: {
              accept: '*/*',
              token: this.token,
              'Content-Type': 'application/json'
            },
            type: 'post',
            async: false,
            timeout: 60000,
            beforeSend: function() {
              vue.loading = true
            },
            complete: function() {
              vue.loading = false
            },
            success: function(result8) {
              console.log('result8: ' + JSON.stringify(result8))
              if (result8 && result8.code === '200') {
                var data8 = result8.data
                vue.accountList.push({
                  accountType: 'Credit Card',
                  availablebalance: data8.availableLimit,
                  accountNumber: vue.creditCardaccountlist[i].value,
                  ccy: data8.ccyCode
                })
                vue.creditCardAmount += Number(data8.availableLimit - data8.approvedLimit)
                window.sessionStorage.setItem(
                  'accountList',
                  JSON.stringify(vue.accountList)
                )
                if (vue.$store.state.app.language === 'zh') {
                  vue.generateTable('53', '53', '67')
                } else {
                  vue.generateTable('60', '60', '60')
                }
              }
            },
            error: function(xhr, type, errorThrown) {
              console.log(type)
              var msg =
                  'Get credit card account detail failed! The response is: \n' +
                  xhr.responseText +
                  '.'
              if (type === 'timeout') {
                msg = 'Get credit card account detail failed. Time out!'
              }
              if (
                typeof JSON.parse(xhr.responseText) === 'object' &&
                  JSON.parse(xhr.responseText).code === '202001'
              ) {
                msg = JSON.parse(xhr.responseText).error
              } else if (
                typeof JSON.parse(xhr.responseText) === 'object' &&
                  JSON.parse(xhr.responseText).code === '404004'
              ) {
                msg = JSON.parse(xhr.responseText).error
              }
              vue.$mui.alert(msg, 'Error', 'OK')
            }
          }
        )
      }
    },
    getcurrentsavingaccounthistory(account) {
      var vue = this
      var requestdata7 = {
        accountnumber: account,
        index: 1,
        items: 9999,
        transFromDate: new Date().getTime() - 1000 * 60 * 60 * 24 * 30,
        transToDate: new Date().getTime(),
        trantype: ''
      }
      console.log(JSON.stringify(requestdata7))
      this.$mui.ajax(
        this.LBSGateway + '/deposit-experience/transactionLog/enquiry',
        {
          data: requestdata7,
          dataType: 'json', // 服务器返回json格式数据
          type: 'post', // HTTP请求类型
          timeout: 60000,
          headers: {
            accept: '*/*',
            token: vue.token,
            clientid: vue.$parent.clientid,
            messageid: vue.$parent.messageid,
            'Content-Type': 'application/json'
          },
          beforeSend: function() {
            vue.loading = true
          },
          complete: function() {
            vue.loading = false
          },
          success: function(data) {
            if (data.code === '200') {
              var response7 = data.data
              // Account Info
              if (response7) {
                var account7 = response7
                account7.sort(function(a, b) {
                  return (
                    Date.parse(Number(b.trandate)) -
                      Date.parse(Number(a.trandate))
                  ) // 时间正序
                })
                account7.length >= 50
                  ? (vue.currentsavingaccounthistory = account7.slice(0, 50))
                  : (vue.currentsavingaccounthistory = account7)
                for (
                  var i = 0;
                  i < vue.currentsavingaccounthistory.length;
                  i++
                ) {
                  vue.currentsavingaccounthistory[
                    i
                  ].trandate = vue.$moment
                    .parseZone(
                      Number(vue.currentsavingaccounthistory[i].trandate)
                    )
                    .local()
                    .format('YYYY-MM-DD')
                  if (
                    vue.currentsavingaccounthistory[i].trandesc ===
                      'tranfer in' ||
                      vue.currentsavingaccounthistory[i].trandesc === 'deposit' ||
                      vue.currentsavingaccounthistory[i].trandesc ===
                      'stock sell' ||
                      vue.currentsavingaccounthistory[i].trandesc ===
                      'Term Deposit Application' ||
                      vue.currentsavingaccounthistory[i].trandesc ===
                      'mortgage loan application'
                  ) {
                    vue.currentsavingaccounthistory[i].tranamt =
                        '+ ' +
                        vue.currentsavingaccounthistory[i].ccy +
                        ' ' +
                        vue.$lbs.decimal_format(
                          vue.currentsavingaccounthistory[i].tranamt
                        )
                  } else if (
                    vue.currentsavingaccounthistory[i].trandesc ===
                      'transfer out' ||
                      vue.currentsavingaccounthistory[i].trandesc ===
                      'stock buy' ||
                      vue.currentsavingaccounthistory[i].trandesc === 'fund buy' ||
                      vue.currentsavingaccounthistory[i].trandesc ===
                      'foreign buy' ||
                      vue.currentsavingaccounthistory[i].trandesc === 'withdrawal' ||
                      vue.currentsavingaccounthistory[i].trandesc ===
                      'mortgage loan repayment'
                  ) {
                    vue.currentsavingaccounthistory[i].tranamt =
                        '- ' +
                        vue.currentsavingaccounthistory[i].ccy +
                        ' ' +
                        vue.$lbs.decimal_format(
                          vue.currentsavingaccounthistory[i].tranamt
                        )
                  }
                  vue.currentsavingaccounthistory[
                    i
                  ].trandesc = vue.currentsavingaccounthistory[
                    i
                  ].trandesc.toUpperCase()
                }
                console.log(vue.currentsavingaccounthistory)
              }
            } else if (data.code === '404003') {
              // No transaction record
            } else {
              vue.$mui.alert(
                'Get history info failed! The response is: \n' +
                  JSON.stringify(data) +
                  '.',
                'Error',
                'OK'
              )
            }
          },
          error: function(xhr, type, errorThrown) {
            console.log(type)
            var msg =
                'Get history info failed! The response is: \n' +
                xhr.responseText +
                '.'
            if (type === 'timeout') {
              msg = 'Get history info failed. Time out!'
            }
            if (
              typeof JSON.parse(xhr.responseText) === 'object' &&
                JSON.parse(xhr.responseText).code === '404003'
            ) {
              vue.$mui.alert(msg, 'Error', 'OK')
            }
          }
        }
      )
    },
    generateTable(x1, x2, x3) {
      var vue = this
      // creat echart object
      this.ihave = this.$echarts.init(document.getElementById('ihave'))
      this.iowe = this.$echarts.init(document.getElementById('iowe'))
      this.investment = this.$echarts.init(document.getElementById('investment'))
      // set data
      this.ihaveoption = {
        title: {
          text: this.$t('lbsTips.dashboardTip.IHave'),
          x: x1,
          y: '80px',
          textStyle: {
            fontSize: '15',
            fontWeight: 200,
            color: '#807D7D'
          }
        },
        tooltip: {
          trigger: 'item',
          formatter: '{b} : <br/>HKD {c} ({d}%)',
          extraCssText: 'width:auto;height:55px;font-size:10px;'
        },
        legend: {
          orient: 'vertical',
          x: '200px',
          y: 'center',
          data: [this.$t('lbsTips.dashboardTip.Currenttitile'), this.$t('lbsTips.dashboardTip.Savingtitile'), this.$t('lbsTips.dashboardTip.TermDeposittitile')],
          textStyle: {
            fontSize: '12',
            color: '#807D7D'
          }
        },
        series: [
          {
            name: this.$t('lbsTips.dashboardTip.Balance'),
            type: 'pie',
            radius: ['50%', '70%'],
            center: ['25%', '50%'],
            avoidLabelOverlap: false,
            label: {
              normal: {
                show: false,
                position: 'center'
              },
              emphasis: {
                show: false,
                textStyle: {
                  fontSize: '14'
                }
              }
            },
            labelLine: {
              normal: {
                show: false
              }
            },
            data: [
              {
                value: vue.currentAmount,
                name: this.$t('lbsTips.dashboardTip.Currenttitile')
              },
              {
                value: vue.savingAmount,
                name: this.$t('lbsTips.dashboardTip.Savingtitile')
              },
              {
                value: vue.termDepositAmount,
                name: this.$t('lbsTips.dashboardTip.TermDeposittitile')
              }
            ]
          }
        ]
      }
      this.ioweoption = {
        title: {
          text: this.$t('lbsTips.dashboardTip.IOwe'),
          x: x2,
          y: '80px',
          textStyle: {
            fontSize: '15',
            fontWeight: 200,
            color: '#807D7D'
          }
        },
        tooltip: {
          trigger: 'item',
          formatter: '{b} : <br/>HKD {c} ({d}%)',
          extraCssText: 'width:auto;height:55px;font-size:10px;'
        },
        legend: {
          orient: 'vertical',
          x: '200px',
          y: 'center',
          data: [this.$t('lbsTips.dashboardTip.CreditCardtitile'), this.$t('lbsTips.dashboardTip.Loantitile')],
          textStyle: {
            fontSize: '12',
            color: '#807D7D'
          }
        },
        series: [
          {
            name: this.$t('lbsTips.dashboardTip.Balance'),
            type: 'pie',
            radius: ['50%', '70%'],
            center: ['25%', '50%'],
            avoidLabelOverlap: false,
            label: {
              normal: {
                show: false,
                position: 'center'
              },
              emphasis: {
                show: false,
                textStyle: {
                  fontSize: '14'
                }
              }
            },
            labelLine: {
              normal: {
                show: false
              }
            },
            data: [
              {
                value: vue.creditCardAmount,
                name: this.$t('lbsTips.dashboardTip.CreditCardtitile')
              },
              {
                value: vue.loanAmount,
                name: this.$t('lbsTips.dashboardTip.Loantitile')
              }
            ]
          }
        ]
      }
      this.investmentoption = {
        title: {
          text: this.$t('lbsTips.dashboardTip.Wealth'),
          x: x3,
          y: '80px',
          textStyle: {
            fontSize: '15',
            fontWeight: 200,
            color: '#807D7D'
          }
        },
        tooltip: {
          trigger: 'item',
          formatter: '{b} : <br/>HKD {c} ({d}%)',
          extraCssText: 'width:auto;height:55px;font-size:10px;'
        },
        legend: {
          orient: 'vertical',
          x: '200px',
          y: 'center',
          data: [this.$t('lbsTips.dashboardTip.Stocktitile'), this.$t('lbsTips.dashboardTip.Fextitile'), this.$t('lbsTips.dashboardTip.PreciousMetaltitile'), this.$t('lbsTips.dashboardTip.MutualFundtitile')],
          textStyle: {
            fontSize: '12',
            color: '#807D7D'
          }
        },
        series: [
          {
            name: this.$t('lbsTips.dashboardTip.Balance'),
            type: 'pie',
            radius: ['50%', '70%'],
            center: ['25%', '50%'],
            avoidLabelOverlap: false,
            label: {
              normal: {
                show: false,
                position: 'center'
              },
              emphasis: {
                show: false,
                textStyle: {
                  fontSize: '14'
                }
              }
            },
            labelLine: {
              normal: {
                show: false
              }
            },
            data: [
              {
                value: vue.stockAmount,
                name: this.$t('lbsTips.dashboardTip.Stocktitile')
              },
              {
                value: vue.fexAmount,
                name: this.$t('lbsTips.dashboardTip.Fextitile')
              },
              {
                value: vue.preciousmetalAmount,
                name: this.$t('lbsTips.dashboardTip.PreciousMetaltitile')
              },
              {
                value: vue.mutualfundAmount,
                name: this.$t('lbsTips.dashboardTip.MutualFundtitile')
              }
            ]
          }
        ]
      }
      // init data
      this.ihave.setOption(this.ihaveoption)
      this.iowe.setOption(this.ioweoption)
      this.investment.setOption(this.investmentoption)
    }
  }
}
</script>

<style scoped>
  .lbs-home .span-text {
    font-size: 15px;
    color: #707070;
  }

  .lbs-home h1 {
    font-size: 50px;
    color: #707070;
    margin-bottom: 20px;
    /*line-height: normal;*/
  }

  .lbs-home .title p {
    color: #22c1e6;
    font-size: 50px;
    line-height: 100px;
  }

  .lbs-home .two-title {
    font-size: 20px;
    color: #22c1e6;
    display: inline-block;
  }

  .lbs-home .div-text p {
    font-size: 15px;
    margin-top: 0;
    margin-bottom: 20px;
    color: #fff;
    text-align: center;
  }

  .lbs-home .amount {
    width: 32%;
    display: inline-block;
  }

  .lbs-home .amount p {
    color: #939292;
    margin-bottom: 20px;
    text-align: center;
    font-size: 18px;
  }

  .lbs-home .net-worth {
    height: auto;
    margin: 15px 55px 0 55px;
    background-color: #ffffff;
    box-shadow: 1px 3px 5px #bbbbbb;
  }

  .lbs-home .two-title {
    margin: 25px 0 0 65px;
  }

  .lbs-home .title {
    padding: 18px 28px;
    height: 140px;
    background-color: #ffffff;
    margin: 0 25px 0 25px;
    border-radius: 50px;
    background-image: url("../components/title-bg.png");
    background-repeat: no-repeat;
    background-size: 574px 130px;
    background-position: right;
    box-shadow: 5px 5px 5px #bbbbbb;
  }

  .lbs-home .div-icon {
    width: 40px;
    height: 50%;
    margin: 10px auto 0;
  }

  .lbs-home .div-text {
    height: 60px;
    max-width: 80px;
    margin: 0 auto;
  }

  .lbs-home .quick-item {
    cursor: pointer;
  }
</style>

<style>
  .lbs-home .el-tooltip__popper.is-dark {
    width: 300px;
  }

  .el-message-box {
    width: 800px;
  }
</style>
