<template>
  <div v-loading.fullscreen.lock="loading" class="fx-transaction-history">
    <el-row>
      <el-col :span="24">
        <p class="title">{{ $t('lbs.TransactionHistory') }}</p>
        <el-card>
          <div class="select-time">
            <el-select v-model="fxAccount" style="width: 270px" @change="getTransactionHistory(type)">
              <el-option v-for="(item,index) in fxAccountOption" :key="index" :label="item.label" :value="item.value" />
            </el-select>
            <el-date-picker
              v-model="date"
              type="daterange"
              value-format="timestamp"
              :clearable="false"
              unlink-panels
              range-separator="-"
              :start-placeholder="$t('lbs.FundOrder.StartDate')"
              :end-placeholder="$t('lbs.FundOrder.EndDate')"
              @change="getTransactionHistory(type)"
            />
            <el-select v-model="type" style="width: 150px" @change="getTransactionHistory(type)">
              <el-option label="FOREIGN SELL" value="FOREIGN SELL" />
              <el-option label="FOREIGN BUY" value="FOREIGN BUY" />
            </el-select>
          </div>
          <el-table :data="transactionHistoryList" height="500">
            <el-table-column :label="$t('lbs.TranDate')" prop="trandate" align="center" />
            <el-table-column :label="$t('lbs.TranDesc')" prop="trandesc" align="center" />
            <el-table-column :label="$t('lbs.TranAmt')" prop="tranamt" align="center" />
            <el-table-column :label="$t('lbs.PreviousBalAmt')" prop="previousbalamt" align="center" />
            <el-table-column :label="$t('lbs.ActualBalAmt')" prop="actualbalamt" align="center" />
            <el-table-column :label="$t('lbs.Currency')" prop="ccy" align="center" />
          </el-table>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import axios from 'axios'

export default {
  name: 'TransactionHistory',
  data: () => ({
    token: null,
    loading: false,
    fxAccount: '',
    fxAccountOption: [],
    date: [new Date().setTime(new Date().getTime() - 3600 * 1000 * 24 * 30), new Date().getTime()],
    transactionHistoryList: [],
    type: 'FOREIGN SELL'
  }),
  created() {
    this.token = window.sessionStorage.getItem('token')
    this.getFxOption()
  },
  methods: {
    // 获取交易记录
    getTransactionHistory(type) {
      if (!this.fxAccount) return
      let trantype = ''
      if (type === 'FOREIGN SELL') {
        trantype = '0008'
      } else {
        trantype = '0007'
      }
      const _this = this
      _this.loading = true
      const requestData = {
        accountnumber: _this.fxAccount,
        index: 1,
        items: 9999,
        transFromDate: _this.date[0],
        transToDate: _this.date[1],
        trantype: trantype
      }
      axios.post(`${_this.LBSGateway}/deposit-experience/transactionLog/enquiry`, requestData, { headers: { token: _this.token }})
        .then(response => {
          _this.loading = false
          _this.transactionHistoryList = []
          if (response.data.code === '200') {
            const tableData = response.data.data
            for (const item of tableData) {
              item.trandate = _this.$moment(item.trandate).format('YYYY-MM-DD HH:mm:ss')
              item.trandesc = item.trandesc.toUpperCase()
            }
            _this.transactionHistoryList = tableData
          } else if (response.data.code === '404003') {
            // _this.$message.success(response.data.msg, _this.$t('lbs.common.success'))
          } else {
            _this.$message.error(response.data.msg, _this.$t('lbs.common.error'))
          }
        })
        .catch(error => {
          _this.loading = false
          _this.$message.error(error.message, _this.$t('lbs.common.error'))
        })
    },
    // 获取状态为A的外汇账户
    getFxOption() {
      const tempFxOption = JSON.parse(window.sessionStorage.getItem('fexaccountlist'))
      for (const item of tempFxOption) {
        if (item.accountStatus === 'A') {
          this.fxAccountOption.push({
            label: item.accountNumber,
            value: item.accountNumber
          })
        }
      }
      if (this.fxAccountOption && this.fxAccountOption[0]) {
        this.fxAccount = this.fxAccountOption[0].value
      }
      this.getTransactionHistory(this.type)
    }
  }
}
</script>

<style lang="scss" scoped>
  .fx-transaction-history {
    padding: 170px 50px 0;

    .title {
      font-size: 20px;
      color: #22C1E6;
    }

    .select-time {
      text-align: center;
      margin-bottom: 20px;
    }
  }
</style>
