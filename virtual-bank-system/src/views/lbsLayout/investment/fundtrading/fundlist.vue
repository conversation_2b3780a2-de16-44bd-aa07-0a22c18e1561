<template>
  <div v-loading.fullscreen.lock="loading" class="fund-list">
    <el-row>
      <el-col :span="24">
        <div class="fund-list-main">
          <p class="fund-list-title">{{ $t('lbs.FundList.FundList') }}</p>
          <el-card>
            <el-table :data="fundList" height="627">
              <el-table-column width="94" :label="$t('lbs.FundText.FundCode')" align="center">
                <template slot-scope="scope">
                  <span class="fund-code" @click="viewThisFund(scope.row.fundCode)">{{ scope.row.fundCode }}</span>
                </template>
              </el-table-column>
              <el-table-column :label="$t('lbs.FundText.FundName')" prop="fundName" align="center" />
              <el-table-column
                :label="$t('lbs.FundText.FundCurrency')"
                prop="fundCurrency"
                align="center"
                :filters="fundCurrencyFilter | getFilter('fundCurrency')"
                :filter-method="filterHandler"
              />
              <el-table-column
                :label="$t('lbs.FundText.FundType')"
                prop="fundType"
                align="center"
                :filters="fundTypeFilter | getFilter('fundType')"
                :filter-method="filterHandler"
              />
              <el-table-column
                :label="$t('lbs.FundText.Geographic')"
                prop="geographic"
                align="center"
                :filters="geographicFilter | getFilter('geographic')"
                :filter-method="filterHandler"
              />
              <el-table-column :label="$t('lbs.FundText.LastNAV')" prop="lastNAV" align="center" sortable />
              <el-table-column :label="$t('lbs.FundText.ManagementFee')" width="160" prop="managementFee" sortable align="center" />
              <el-table-column :label="$t('lbs.FundText.ValuationDate')" width="160" prop="valuationDate" sortable align="center" />
            </el-table>
          </el-card>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import axios from 'axios'

let that
export default {
  filters: {
    getFilter(filter, prop) {
      const tempFilter1 = []
      const tempFilter2 = []
      for (const item of that.fundList) {
        if (tempFilter1.indexOf(item[prop]) === -1) {
          tempFilter1.push(item[prop])
        }
      }
      for (const item of tempFilter1) {
        tempFilter2.push({ text: item, value: item })
      }
      return tempFilter2
    }
  },
  data: () => ({
    token: null,
    loading: false,
    fundList: [],
    fundCurrencyFilter: [],
    fundTypeFilter: [],
    geographicFilter: []
  }),
  created() {
    that = this
    this.token = window.sessionStorage.getItem('token')
    this.getFundList()
  },
  methods: {
    viewThisFund: function(fundCode) {
      this.$router.push({ path: `/lbsinvestment/fundtrading/detailquotation?fundCode=${fundCode}` })
    },
    // 获取基金列表
    getFundList: function() {
      const _this = this
      _this.loading = true
      const requestData = { index: 0, items: 999 }
      axios.post(`${_this.LBSGateway}/fund-experience/fund/fundList`, requestData, { headers: { token: _this.token }})
        .then(response => {
          _this.loading = false
          if (response.data.code === '200') {
            const result = response.data.data
            for (const item of result) {
              item.valuationDate = _this.$moment(item.valuationDate).format('YYYY-MM-DD HH:mm:ss')
            }
            _this.fundList = response.data.data
          } else {
            _this.$message.error(response.data.msg, _this.$t('lbs.common.error'))
          }
        })
        .catch(error => {
          _this.loading = false
          _this.$message.error(error.message, _this.$t('lbs.common.error'))
        })
    },
    filterHandler(value, row, column) {
      const property = column['property']
      return row[property] === value
    }
  }
}
</script>

<style lang="scss" scoped>
  .fund-list {
    .fund-list-main {
      margin: 0 50px;
    }

    .fund-list-title {
      font-size: 20px;
      color: #22C1E6;
    }

    .fund-code {
      color: #22C1E6;
      text-decoration: underline;
      cursor: pointer;
    }

    .fund-code:hover {
      color: #315efb;
    }
  }
</style>
