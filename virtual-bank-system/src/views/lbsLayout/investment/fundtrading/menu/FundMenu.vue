<template>
  <div class="fund-menu" :style="`width: ${labelWidth}`">
    <div class="menu-icon" @click="switchPage(id)">
      <svg-icon class="menu-icon-image" :icon-class="active ? activeIcon : icon" />
    </div>
    <div class="menu-name">
      <p :class="`menu-name-text ${active ? 'active' : ''}`" @click="switchPage()">{{ name }}</p>
    </div>
  </div>
</template>

<script>
export default {
  name: 'FundMenu',
  props: {
    icon: {
      type: String,
      default: ''
    },
    path: {
      type: String,
      default: ''
    },
    name: {
      type: String,
      default: ''
    },
    activeIcon: {
      type: String,
      default: ''
    },
    active: {
      type: Boolean,
      default: false
    },
    labelWidth: {
      type: String,
      default: ''
    },
    id: {
      type: Number,
      default: 0
    }
  },
  methods: {
    switchPage: function() {
      this.$router.push(
        {
          path: this.path
        }
      )
    }
  }
}
</script>

<style lang="scss" scoped>
  .fund-menu {
    display: inline-block;
    margin-bottom: 20px;

    .menu-icon {
      width: 30px;
      height: 30px;
      margin: 0 auto;
    }

    .menu-name {
      text-align: center;
    }

    .menu-name-text {
      font-size: 16px;
      color: #707070;
      cursor: pointer;
      display: inline-block;
    }

    .menu-icon-image {
      width: 30px;
      height: 30px;
      cursor: pointer;
    }

    .active {
      color: #22C1E6;
    }
  }
</style>
