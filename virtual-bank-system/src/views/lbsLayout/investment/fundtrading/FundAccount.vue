<template>
  <div v-loading.fullscreen.lock="loading" class="fund-account">
    <el-row>
      <el-col :span="24">
        <p class="fund-account-title" style="margin: 70px 0 30px 200px;">{{ $t('lbs.FundAccount.SettingSettlementAccount') }}</p>
        <div style="margin-bottom: 20px" class="text-center">
          <span class="fund-account-label">{{ $t('lbs.FundAccount.FundAccount') }}</span>
          <el-select v-model="fundAccount" class="fund-account-input" @change="getFundSettingAccount()">
            <el-option v-for="(item,index) in fundAccountOption" :key="index" :label="item.label" :value="item.value" />
          </el-select>
        </div>
        <div class="text-center">
          <span class="fund-account-label">{{ $t('lbs.FundAccount.SettlementAccount') }}</span>
          <el-select v-model="settlementAccount" class="fund-account-input" @change="setSettingAccount()">
            <el-option v-for="(item,index) in settlementAccountOption" :key="index" :label="item.label.split('HK')[0]+(item.label.split('HK')[1]?item.label.split('HK')[1].substr(6,15):'')" :value="item.value" />
          </el-select>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import axios from 'axios'

export default {
  name: 'FundAccount',
  data: () => ({
    token: null,
    loading: false,
    fundAccount: '',
    fundAccountOption: [],
    settlementAccount: '',
    settlementAccountOption: []
  }),
  created() {
    this.token = window.sessionStorage.getItem('token')
    this.getFundAccountOption()
    this.getSettingAccountOption()
    this.getFundSettingAccount()
  },
  methods: {
    // 设置结算账户
    setSettingAccount() {
      const _this = this
      if (!_this.fundAccount) return
      _this.loading = true
      const requestData = { fundAccountNumber: _this.fundAccount, newSettleAccountNumber: _this.settlementAccount }
      axios.post(`${_this.LBSGateway}/fund-experience/fund/settlementAccountUpdate`, requestData, { headers: { token: _this.token }})
        .then(response => {
          _this.loading = false
          if (response.data.code === '200') {
            _this.$message.success(_this.$t('lbs.settlementTip'), _this.$t('lbs.common.success'))
          } else {
            _this.$message.error(response.data.msg, _this.$t('lbs.common.error'))
          }
        })
        .catch(error => {
          _this.loading = false
          _this.$message.error(error.message, _this.$t('lbs.common.error'))
        })
    },
    // 获取基金账户设置的结算账户
    getFundSettingAccount() {
      const _this = this
      if (!_this.fundAccount) return
      _this.loading = true
      axios.post(`${_this.LBSGateway}/deposit-experience/deposit/account/accountDetails/${_this.fundAccount}`, {}, { headers: { token: _this.token }})
        .then(response => {
          _this.loading = false
          if (response.data.code === '200') {
            _this.settlementAccount = response.data.data.account.relaccountnumber
          } else {
            _this.$message.error(response.data.msg, _this.$t('lbs.common.error'))
          }
        })
        .catch(error => {
          _this.loading = false
          _this.$message.error(error.message, _this.$t('lbs.common.error'))
        })
    },
    // 获取状态为A的基金账户列表
    getFundAccountOption() {
      const tempMutualFundAccountList = JSON.parse(window.sessionStorage.getItem('mutualFundaccountlist'))
      for (const item of tempMutualFundAccountList) {
        if (item.accountStatus === 'A') {
          this.fundAccountOption.push(
            {
              label: item.accountNumber,
              value: item.accountNumber
            }
          )
        }
      }
      if (this.fundAccountOption && this.fundAccountOption[0]) {
        this.fundAccount = this.fundAccountOption[0].value
      }
    },
    // 获取结算账户列表(current / saving & Status: A)
    getSettingAccountOption() {
      const savingList = JSON.parse(window.sessionStorage.getItem('savingaccountlist'))
      const currentList = JSON.parse(window.sessionStorage.getItem('currentaccountlist'))
      for (const item of savingList) {
        if (item.accountStatus === 'A') {
          this.settlementAccountOption.push(
            {
              label: `Saving Account ${item.accountNumber}`,
              value: item.accountNumber
            }
          )
        }
      }
      for (const item of currentList) {
        if (item.accountStatus === 'A') {
          this.settlementAccountOption.push(
            {
              label: `Current Account ${item.accountNumber}`,
              value: item.accountNumber
            }
          )
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
  .fund-account {
    margin: 0 50px;
    .fund-account-title {
      font-size: 20px;
      color: #22C1E6;
    }
    .fund-account-label {
      display: inline-block;
      color: #707070;
      width: 230px;
      text-align: left;
    }
    .fund-account-input {
      width: 350px;
    }
  }
</style>
