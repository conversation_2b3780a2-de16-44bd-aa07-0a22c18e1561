<template>
  <div v-loading.fullscreen.lock="loading" class="transaction-history">
    <el-row>
      <el-col :span="24">
        <div class="text-center">
          <el-select v-model="fundAccount" style="width: 270px" @change="getTransactionHistory">
            <el-option v-for="(item,index) in fundAccountOption" :key="index" :label="item.label" :value="item.value" />
          </el-select>
          <el-date-picker
            v-model="date"
            type="daterange"
            value-format="timestamp"
            :clearable="false"
            unlink-panels
            range-separator="-"
            :start-placeholder="$t('lbs.FundOrder.StartDate')"
            :end-placeholder="$t('lbs.FundOrder.EndDate')"
            @change="getTransactionHistory"
          />
        </div>
      </el-col>
    </el-row>
    <el-row>
      <el-col :span="24">
        <p class="transaction-history-title">{{ $t('lbs.FundTransactionHistory.TransactionHistory') }}</p>
        <el-card>
          <el-table :data="transactionHistoryList" height="500">
            <el-table-column :label="$t('lbs.FundTransactionHistory.FundCode')" width="100" prop="fundCode" align="center" />
            <el-table-column :label="$t('lbs.FundTransactionHistory.CountryCode')" width="120" prop="countryCode" align="center" />
            <el-table-column :label="$t('lbs.FundTransactionHistory.TradingOption')" width="121" prop="tradingOption" align="center" />
            <el-table-column :label="$t('lbs.FundTransactionHistory.SharingNo')" width="99" prop="sharingNo" align="center" />
            <el-table-column :label="$t('lbs.FundTransactionHistory.TradingAmount')" width="129" prop="tradingAmount" align="center" />
            <el-table-column :label="$t('lbs.FundTransactionHistory.TradingCommission')" width="159" prop="trdingCommission" align="center" />
            <el-table-column :label="$t('lbs.FundTransactionHistory.FundCurrency')" width="120" prop="fundCcy" align="center" />
            <el-table-column :label="$t('lbs.FundTransactionHistory.TransactionAmount')" prop="transactionAmount" align="center" />
            <el-table-column :label="$t('lbs.FundTransactionHistory.TransactionDate')" width="200" prop="transactionDate" align="center" />
          </el-table>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import axios from 'axios'

export default {
  name: 'TransactionHistory',
  data: () => ({
    token: null,
    loading: false,
    fundAccount: '',
    fundAccountOption: [],
    date: [new Date().setTime(new Date().getTime() - 3600 * 1000 * 24 * 30), new Date().getTime()],
    transactionHistoryList: []
  }),
  created() {
    this.token = window.sessionStorage.getItem('token')
    this.getFundAccountOption()
    this.getTransactionHistory()
  },
  methods: {
    // 获取基金交易记录
    getTransactionHistory: function() {
      const _this = this
      if (!_this.fundAccount) return
      _this.loading = true
      const requestData = {
        accountNumber: _this.fundAccount,
        index: 0,
        items: 999,
        transFromTime: _this.date[0],
        transToTime: _this.date[1]
      }
      axios.post(`${_this.LBSGateway}/fund-experience/fund/transactionRetrieval`, requestData, { headers: { token: _this.token }})
        .then(response => {
          _this.loading = false
          _this.transactionHistoryList = []
          if (response.data.code === '200') {
            const result = response.data.data
            for (let i = 0; i < result.length; i++) {
              result[i].tradingOption = result[i].tradingOption.toUpperCase()
              // result[i].transactionDate = _this.$moment(result[i].transactionDate).format('YYYY-MM-DD HH:ss:mm')
              result[i].transactionDate = new Date(result[i].transactionDate).toLocaleString()
            }
            _this.transactionHistoryList = result
          } else if (response.data.code === '404010') {
            // _this.$message.success(_this.$t('lbs.FundTransactionHistory.RecordNotFound'), _this.$t('lbs.common.success'))
          } else {
            _this.transactionHistoryList = []
            _this.$message.error(response.data.msg, _this.$t('lbs.common.error'))
          }
        })
        .catch(error => {
          _this.loading = false
          _this.$message.error(error.message, _this.$t('lbs.common.error'))
        })
    },
    // 获取状态为A的基金账户列表
    getFundAccountOption() {
      const tempMutualFundAccountList = JSON.parse(window.sessionStorage.getItem('mutualFundaccountlist'))
      for (const item of tempMutualFundAccountList) {
        if (item.accountStatus === 'A') {
          this.fundAccountOption.push(
            {
              label: item.accountNumber,
              value: item.accountNumber
            }
          )
        }
      }
      if (this.fundAccountOption && this.fundAccountOption[0]) {
        this.fundAccount = this.fundAccountOption[0].value
      }
    }
  }
}
</script>

<style lang="scss" scoped>
  .transaction-history {
    margin: 0 50px;

    .transaction-history-title {
      font-size: 20px;
      color: #22C1E6;
    }

    .text-info {
      color: #707070;
    }
  }
</style>
