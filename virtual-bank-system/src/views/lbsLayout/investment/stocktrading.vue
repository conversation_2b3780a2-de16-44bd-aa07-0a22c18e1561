<template>
  <div class="stocktrading">
    <el-row>
      <el-col :span="24">
        <div class="title">
          <p class="max-title">{{ $t('lbs.Stock') }}</p>
        </div>
      </el-col>
    </el-row>
    <el-row>
      <router-view />
    </el-row>
  </div>
</template>

<script>
export default {
  name: 'Stocktrading',
  mounted() {
    const stockaccountlist = JSON.parse(window.sessionStorage.getItem('stockaccountlist'))
    window.sessionStorage.setItem('stockAccount', JSON.stringify(stockaccountlist[0]))
  }
}
</script>

<style scoped>

  .stocktrading .title {
    padding: 30px 28px;
    height: 140px;
    background-color: #FFFFFF;
    margin: 0 25px 0 25px;
    border-radius: 50px;
    background-image: url("../components/title-bg.png");
    background-repeat: no-repeat;
    background-size: 574px 130px;
    background-position: right;
    box-shadow: 5px 5px 5px #bbbbbb;
  }

  .stocktrading p {
    color: #707070;
    margin-bottom: 0;
    line-height: 1;
  }

  .stocktrading .max-title {
    font-size: 50px;
    margin-bottom: 40px;
    margin-top: 10px;
    font-weight: 500;
    color: #22C1E6;
  }

</style>
