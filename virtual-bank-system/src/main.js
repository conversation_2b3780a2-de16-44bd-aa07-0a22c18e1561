import Vue from 'vue'

import 'normalize.css/normalize.css' // A modern alternative to CSS resets
import ElementUI from 'element-ui'

import '@/styles/index.scss' // global css
import App from '@/App'
import store from '@/store'
import router from '@/router'
import i18n from '@/lang'

import echarts from 'echarts'
import moment from 'moment'
import mui from './assets/mui/mui.min.js'
import './assets/mui/mui.min.css'
import './assets/mui/mui.poppicker.css'
import './assets/mui/mui.picker.css'
// lbs main.js
import lbs from './views/lbsLayout/lbsmain'
import '@/icons' // icon
import '@/permission' // permission control
Vue.prototype.$echarts = echarts

Vue.prototype.$moment = moment
moment.locale('zh-cn')

Vue.prototype.$mui = mui

Vue.use(lbs)

Vue.prototype.host = 'https://127.0.0.1:8080'
Vue.prototype.LBSGateway = process.env.VUE_APP_DOMAIN + '/lbsgateway'
Vue.prototype.CNLBSGateway = process.env.VUE_APP_DOMAIN + '/lbsgateway'
Vue.prototype.workflowHost = process.env.VUE_APP_DOMAIN + '/workflow'

Vue.use(ElementUI, {
  size: 'medium', // set element-ui default size
  i18n: (key, value) => i18n.t(key, value)
})

Vue.config.productionTip = false

new Vue({
  el: '#app',
  router,
  store,
  i18n,
  render: h => h(App)
})
