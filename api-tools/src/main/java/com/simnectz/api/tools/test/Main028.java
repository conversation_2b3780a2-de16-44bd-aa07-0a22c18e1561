package com.simnectz.api.tools.test;

public class Main028 {

    public static void main(String[] args) {
        System.out.println(match("aaa","a*a"));
    }

    public static boolean match (String str, String pattern) {
        int n1 = str.length();
        int n2 = pattern.length();

        boolean[][] dp = new boolean[n1 + 1][n2 + 1];

        for(int i = 0; i <= n1; i++){

            for(int j = 0; j <= n2; j++){

                if(j == 0){
                    dp[i][j] = (i == 0);

                }else{
                    if(pattern.charAt(j - 1) != '*'){

                        if(i > 0 && (str.charAt(i - 1) == pattern.charAt(j - 1) || pattern.charAt(j - 1) == '.')){
                            dp[i][j] = dp[i - 1][j - 1];
                        }
                    }else{

                        if(j >= 2){
                            dp[i][j] |= dp[i][j - 2];
                        }

                        if(i >= 1 && j >= 2 && (str.charAt(i - 1) == pattern.charAt(j - 2) || pattern.charAt(j - 2) == '.')){
                            dp[i][j] |= dp[i - 1][j];
                        }
                    }
                }
            }
        }
        return dp[n1][n2];
    }

}
