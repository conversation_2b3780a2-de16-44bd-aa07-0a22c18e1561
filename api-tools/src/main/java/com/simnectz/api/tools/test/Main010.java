package com.simnectz.api.tools.test;

import java.text.SimpleDateFormat;
import java.util.Calendar;

public class Main010 {

    // 计算当前时间90天后的时间，打印出计算的时间(打印时间格式：yyyy-MM-dd HH:mm:ss)
    public static void main(String[] args) {
        // write your code here
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_YEAR, 90);
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        System.out.println(simpleDateFormat.format(calendar.getTime()));
    }

}
