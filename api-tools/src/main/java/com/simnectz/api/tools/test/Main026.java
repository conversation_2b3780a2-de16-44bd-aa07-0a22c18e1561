package com.simnectz.api.tools.test;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.TreeMap;

public class Main026 {

    public static void main(String[] args) {
        List<Integer> ids = new ArrayList<Integer>(){{
            add(1);
            add(1);
            add(1);
            add(2);
            add(2);
            add(3);
        }};
        System.out.println(minItem(ids, 2));
    }

    public static int minItem(List<Integer> ids, int m) {
        // write your code here
        // 先商品编号和数量的 map
        TreeMap<Integer,Integer> itemCountMap = new TreeMap<>();
        for (Integer id : ids) {
            itemCountMap.merge(id, 1, Integer::sum);
        }

        // 获取值list
        List<Integer> integers = new ArrayList<>(itemCountMap.values());
        Collections.sort(integers);

        // 看最多能减少几个商品
        int total = integers.size();
        for (Integer integer : integers) {
            //从小到大便利，如果能删除的数量 > 当前物品数量，可以删除
            if(m >= integer){
                total -= 1;
                m -= integer;
            }
            if (m <= 0){
                break;
            }

        }
        return total;
    }

}
