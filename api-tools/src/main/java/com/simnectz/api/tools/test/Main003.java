package com.simnectz.api.tools.test;

import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import io.jsonwebtoken.security.Keys;

import java.security.KeyPair;
import java.security.PrivateKey;
import java.security.PublicKey;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

public class Main003 {

    private static final KeyPair keyPair = Keys.keyPairFor(SignatureAlgorithm.RS256);
    private static final PrivateKey pairPrivate = keyPair.getPrivate();
    private static final PublicKey publicKey = keyPair.getPublic();

    public static void main(String[] args) {
        // write your code here
        HashMap<String, String> claims = new HashMap<>();
        claims.put("CustomerID", "001");
        claims.put("Name", "User");
        String jwt = createJwt(claims);
        System.out.println("Token = " + jwt);
        try {
            Claims c = parseJwt(jwt);
            System.out.println("Customer ID = " + c.get("CustomerID"));
            System.out.println("Name = " + c.get("Name"));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static String createJwt(Map<String, String> claims) {
        long currentTimestamp = System.currentTimeMillis();
        return Jwts
                .builder()
                .setClaims(claims)
                .setIssuedAt(new Date(currentTimestamp))
                .signWith(pairPrivate)
                .compact();
    }

    public static Claims parseJwt(String jwsString) {
        return Jwts.parserBuilder()
                .setSigningKey(publicKey)
                .build()
                .parseClaimsJws(jwsString)
                .getBody();
    }

}
