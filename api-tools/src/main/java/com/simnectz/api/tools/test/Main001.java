package com.simnectz.api.tools.test;

import com.simnectz.api.tools.core.HttpRequest;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 计算定期存款到利息
 */
public class Main001 {

    public static void main(String[] args) {
        // write your code here
        List<TermDepositRateInformation> rates = getTermDepositRateList();
        List<TermDepositAmountRangeInformation> ranges = getTermDepositAmountRangeList();

        BigDecimal amount = BigDecimal.valueOf(500000);
        int term = 3;
        String unit = "months";
        int range = getRangeByAmount(amount, ranges);
        System.out.println("range = " + range);
        if (range == -1) {
            System.out.println("No matching amount range found");
            return;
        }
        BigDecimal apr = getInterestRateByRange(range, term + unit, rates);
        System.out.println("apr = " + apr);
        if (apr == null) {
            System.out.println("No matching rate found");
            return;
        }
        BigDecimal interest = calculateInterest(amount, apr, term, unit);
        System.out.println("interest = " + interest);
    }

    public static BigDecimal calculateInterest(BigDecimal amount, BigDecimal apr, int term, String unit) {
        BigDecimal interest;
        BigDecimal dayRate = apr.divide(BigDecimal.valueOf(360), 9, RoundingMode.HALF_UP);
        BigDecimal weekRate = dayRate.multiply(BigDecimal.valueOf(7));
        BigDecimal monthRate = apr.divide(BigDecimal.valueOf(12), 9, RoundingMode.HALF_UP);
        switch (unit) {
            case "day":
                interest = dayRate.multiply(amount).multiply(BigDecimal.valueOf(term)).divide(BigDecimal.ONE, 5, RoundingMode.HALF_UP);
                return interest;
            case "week":
            case "weeks":
                interest = weekRate.multiply(amount).multiply(BigDecimal.valueOf(term)).divide(BigDecimal.ONE, 5, RoundingMode.HALF_UP);
                return interest;
            case "month":
            case "months":
                interest = monthRate.multiply(amount).multiply(BigDecimal.valueOf(term)).divide(BigDecimal.ONE, 5, RoundingMode.HALF_UP);
                return interest;
            default:
                return BigDecimal.ZERO;
        }
    }

    public static BigDecimal getInterestRateByRange(int range, String term, List<TermDepositRateInformation> rates) {
        for (TermDepositRateInformation rate : rates) {
            if (String.valueOf(range).equals(rate.getDepositRange()) && term.equals(rate.getTermDepositPeriod())) {
                return rate.getTermDepositInterestRate();
            }
        }
        return null;
    }

    public static int getRangeByAmount(BigDecimal amount, List<TermDepositAmountRangeInformation> ranges) {
        int range = -1;
        for (TermDepositAmountRangeInformation item : ranges) {
            if (item.getAmountRangeMax() == null) {
                if (amount.compareTo(item.getAmountRangeMin()) >= 0) {
                    range = item.getId();
                }
            } else {
                if (amount.compareTo(item.getAmountRangeMin()) >= 0 && amount.compareTo(item.getAmountRangeMax()) <= 0) {
                    range = item.getId();
                }
            }
        }
        return range;
    }

    public static List<TermDepositAmountRangeInformation> getTermDepositAmountRangeList() {
        // request params
        Map<String, String> apiParams = new HashMap<>();
        // request headers
        Map<String, String> apiHeaders = new HashMap<>();
        apiHeaders.put("Content-Type", "application/json");
        // Call API
        List<TermDepositAmountRangeInformation> items = HttpRequest.postList(
                "https://************:8096/term-deposit/amount-range",
                apiParams,
                apiHeaders,
                TermDepositAmountRangeInformation.class
        );
        return items;
    }

    public static List<TermDepositRateInformation> getTermDepositRateList() {
        // request params
        Map<String, String> apiParams = new HashMap<>();
        // request headers
        Map<String, String> apiHeaders = new HashMap<>();
        apiHeaders.put("Content-Type", "application/json");
        // Call API
        List<TermDepositRateInformation> items = HttpRequest.postList(
                "https://************:8096/term-deposit/rates",
                apiParams,
                apiHeaders,
                TermDepositRateInformation.class
        );
        return items;
    }

}

class TermDepositAmountRangeInformation {

    private int id;

    private String currencyType;

    private String countryCode;

    private String clearingCode;

    private int branchCode;

    private BigDecimal amountRangeMin;

    private BigDecimal amountRangeMax;

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getCurrencyType() {
        return currencyType;
    }

    public void setCurrencyType(String currencyType) {
        this.currencyType = currencyType;
    }

    public String getCountryCode() {
        return countryCode;
    }

    public void setCountryCode(String countryCode) {
        this.countryCode = countryCode;
    }

    public String getClearingCode() {
        return clearingCode;
    }

    public void setClearingCode(String clearingCode) {
        this.clearingCode = clearingCode;
    }

    public int getBranchCode() {
        return branchCode;
    }

    public void setBranchCode(int branchCode) {
        this.branchCode = branchCode;
    }

    public BigDecimal getAmountRangeMin() {
        return amountRangeMin;
    }

    public void setAmountRangeMin(BigDecimal amountRangeMin) {
        this.amountRangeMin = amountRangeMin;
    }

    public BigDecimal getAmountRangeMax() {
        return amountRangeMax;
    }

    public void setAmountRangeMax(BigDecimal amountRangeMax) {
        this.amountRangeMax = amountRangeMax;
    }

    @Override
    public String toString() {
        return "{" +
                "\n    id=" + id +
                "\n    , currencyType='" + currencyType + '\'' +
                "\n    , countryCode='" + countryCode + '\'' +
                "\n    , clearingCode='" + clearingCode + '\'' +
                "\n    , branchCode=" + branchCode +
                "\n    , amountRangeMin=" + amountRangeMin +
                "\n    , amountRangeMax=" + amountRangeMax +
                "}\n";
    }

}

class TermDepositRateInformation {

    private Integer id;

    private String depositRange;

    private String termDepositPeriod;

    private BigDecimal termDepositInterestRate;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getDepositRange() {
        return depositRange;
    }

    public void setDepositRange(String depositRange) {
        this.depositRange = depositRange;
    }

    public String getTermDepositPeriod() {
        return termDepositPeriod;
    }

    public void setTermDepositPeriod(String termDepositPeriod) {
        this.termDepositPeriod = termDepositPeriod;
    }

    public BigDecimal getTermDepositInterestRate() {
        return termDepositInterestRate;
    }

    public void setTermDepositInterestRate(BigDecimal termDepositInterestRate) {
        this.termDepositInterestRate = termDepositInterestRate;
    }

    @Override
    public String toString() {
        return "{" +
                "\n    id=" + id +
                "\n    , depositRange='" + depositRange + '\'' +
                "\n    , termDepositPeriod='" + termDepositPeriod + '\'' +
                "\n    , termDepositInterestRate=" + termDepositInterestRate +
                "}\n";
    }

}
