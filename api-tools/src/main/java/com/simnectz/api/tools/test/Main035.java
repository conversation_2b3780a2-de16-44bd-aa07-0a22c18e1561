package com.simnectz.api.tools.test;

public class Main035 {

    public static void main(String[] args) {
        System.out.println(trans("This is a sample", 16));
    }

    public static String trans(String s, int n) {
        if (n == 0)
            return s;
        StringBuilder res = new StringBuilder();
        for (int i = 0; i < n; i++) {
            if (s.charAt(i) <= 'Z' && s.charAt(i) >= 'A')
                res.append((char) (s.charAt(i) - 'A' + 'a'));
            else if (s.charAt(i) >= 'a' && s.charAt(i) <= 'z')
                res.append((char) (s.charAt(i) - 'a' + 'A'));
            else
                res.append(s.charAt(i));
        }
        res.reverse();
        for (int i = 0; i < n; i++) {
            int j = i;
            while (j < n && res.charAt(j) != ' ')
                j++;
            String temp = res.substring(i, j);
            StringBuilder buffer = new StringBuilder(temp);
            temp = buffer.reverse().toString();
            res.replace(i, j, temp);
            i = j;
        }
        return res.toString();
    }

}
