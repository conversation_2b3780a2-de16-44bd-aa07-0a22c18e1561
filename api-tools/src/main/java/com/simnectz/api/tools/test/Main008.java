package com.simnectz.api.tools.test;

import java.util.Arrays;

public class Main008 {

    // 兑换零钱
    public static void main(String[] args) {
        int[] faceValues = new int[]{1, 2, 5, 10, 20, 50, 100};
        // write code here
        System.out.println(minMoney(faceValues, 73));
    }

    /**
     * 给定数组arr，arr中所有的值都为正整数且不重复。每个值代表一种面值的货币，每种面值的货币可以使用任意张，再给定一个aim，代表要找的钱数，求组成aim的最少货币数。
     * 如果无解，请返回-1.
     */
    public static int minMoney (int[] arr, int aim) {
        if(aim < 1)
            return 0;
        int[] dp = new int[aim + 1];
        Arrays.fill(dp, aim + 1);
        dp[0] = 0;
        for(int i = 1; i <= aim; i++){
            for (int k : arr) {
                if (k <= i)
                    dp[i] = Math.min(dp[i], dp[i - k] + 1);
            }
        }
        return dp[aim] > aim ? -1 : dp[aim];
    }

}
