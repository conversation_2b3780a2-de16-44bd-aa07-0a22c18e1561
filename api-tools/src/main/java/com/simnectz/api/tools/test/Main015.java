package com.simnectz.api.tools.test;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

public class Main015 {

    // 获取当天的开始时间和结束时间
    // 例如：现在时间是 2022-07-22 12:08:29，当天的开始时间为 2022-07-22 00:00:00，结束时间为 2022-07-22 23:59:59
    public static void main(String[] args) {
        Calendar calendar = Calendar.getInstance();
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        Date startTime = calendar.getTime();
        calendar.set(Calendar.HOUR, 23);
        calendar.set(Calendar.MINUTE, 59);
        calendar.set(Calendar.SECOND, 59);
        Date endTime = calendar.getTime();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String startTimeString = sdf.format(startTime);
        String endTimeString = sdf.format(endTime);
        System.out.println("今天的开始时间为：" + startTimeString);
        System.out.println("今天的结束时间为：" + endTimeString);
    }

}
