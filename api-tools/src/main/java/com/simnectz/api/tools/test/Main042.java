package com.simnectz.api.tools.test;

import java.math.BigDecimal;

public class Main042 {

    public static void main(String[] args) {
        BigDecimal currentHolding = BigDecimal.valueOf(2000);
        BigDecimal averagePrice = BigDecimal.valueOf(10);
        BigDecimal buyStocks = BigDecimal.valueOf(500);
        BigDecimal buyPrice = BigDecimal.valueOf(9);
        BigDecimal buyAveragePrice = currentHolding.multiply(averagePrice).add(buyStocks.multiply(buyPrice)).divide(currentHolding.add(buyStocks));
        System.out.println(buyAveragePrice);
    }

}
