package com.simnectz.api.tools.test;

public class Main032 {

    public static void main(String[] args) {
        System.out.println(isMatch("aa", "a"));
        System.out.println(isMatch("aa","aa"));
        System.out.println(isMatch("aaa","aa"));
        System.out.println(isMatch("aa", "*"));
        System.out.println(isMatch("aa", "a*"));
        System.out.println(isMatch("ab", "?*"));
        System.out.println(isMatch("aab", "d*a*b"));
    }

    public static boolean isMatch(String s, String p) {
        // write your code here
        if (p == null || p.isEmpty()) return s == null || s.isEmpty();
        int i = 0, j = 0, istart = -1, jstart = -1, slen = s.length(), plen = p.length();
        while (i < slen) {
            if (j < plen && (s.charAt(i) == p.charAt(j) || p.charAt(j) == '?')) {
                i++;
                j++;
            } else if (j < plen && p.charAt(j) == '*') {
                istart = i;
                jstart = j++;
            } else if (istart > -1) {
                i = ++istart;
                j = jstart + 1;
            } else {
                return false;
            }
        }
        while (j < plen && p.charAt(j) == '*') j++;
        return j == plen;
    }

}
