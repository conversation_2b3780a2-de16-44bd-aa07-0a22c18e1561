package com.simnectz.api.tools.test;

public class Main030 {

    public static void main(String[] args) {
        System.out.println(solve("1024", "1024"));
    }

    public static String solve (String s, String t) {
        // write your code here
        int lenS = s.length(), lenT = t.length();
        int[] arrS = new int[lenS];
        int[] arrT = new int[lenT];
        for(int i = 0; i < lenS; i++) {
            arrS[i] = s.charAt(i) - '0';
        }
        for(int i = 0; i < lenT; i++) {
            arrT[i] = t.charAt(i) - '0';
        }

        int[] res = new int[lenS + lenT];

        for(int i = 0; i < lenS; i++) {
            for(int j = 0; j < lenT; j++) {

                res[i + j + 1] += arrS[i] * arrT[j];
            }
        }

        int carry = 0;

        for(int i = lenS + lenT - 1; i >= 0; i--) {
            res[i] += carry;
            carry = res[i] / 10;
            res[i] = res[i] % 10;
        }
        StringBuilder ans = new StringBuilder();

        int cur = 0;
        while(cur < lenS + lenT && res[cur] == 0) {
            cur++;
        }

        for(int i = cur; i < res.length; i++) {
            ans.append(res[i]);
        }
        return ans.length() == 0 ? "0" : ans.toString();
    }

}
