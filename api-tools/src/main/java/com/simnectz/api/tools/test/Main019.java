package com.simnectz.api.tools.test;

import com.simnectz.api.tools.core.HttpRequest;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class Main019 {

    /**
     * 股票信息排序（快速排序）
     * 使用 快速排序算法 对股票市场的股票信息根据 LastPrice 进行正序排序, 打印排序后的股票信息。
     */
    public static void main(String[] args) {
        List<StockInformation> stocks = callApi();
        quickSort(stocks);
        System.out.println(stocks);
    }

    public static void quickSort(List<StockInformation> stocks) {
        quickSort(stocks, 0, stocks.size() - 1);
    }

    private static void quickSort(List<StockInformation> stocks, int left, int right) {
        if (left >= right) return;
        StockInformation pivot = stocks.get(left);
        int i = left, j = right;
        while (i < j) {
            while (stocks.get(j).getLastprice().compareTo(pivot.getLastprice()) >= 0 && i < j) --j;
            while (stocks.get(i).getLastprice().compareTo(pivot.getLastprice()) <= 0 && i < j) ++i;
            if (i < j) {
                StockInformation temp = stocks.get(i);
                stocks.set(i, stocks.get(j));
                stocks.set(j, temp);
            }
        }
        stocks.set(left, stocks.get(i));
        stocks.set(i, pivot);
        quickSort(stocks, left, i - 1);
        quickSort(stocks, i + 1, right);
    }

    public static List<StockInformation> callApi() {
        // request params
        Map<String, String> apiParams = new HashMap<>();
        apiParams.put("index", "1");
        apiParams.put("items", "9999");
        // request headers
        Map<String, String> apiHeaders = new HashMap<>();
        apiHeaders.put("Content-Type", "application/json");
        // Call API
        return HttpRequest.postList(
                "https://************:8096/stock/stocks",
                apiParams,
                apiHeaders,
                StockInformation.class
        );
    }

    static class StockInformation {

        private int id;

        private String stockcode;

        private String stockname;

        private BigDecimal open;

        private BigDecimal high;

        private BigDecimal low;

        private BigDecimal previousclose;

        private BigDecimal lastprice;

        private String changed;

        private String changedpercent;

        private BigDecimal buyprice;

        private BigDecimal sellprice;

        private BigDecimal volume;

        private BigDecimal turnover;

        private BigDecimal eps;

        private BigDecimal ratio;

        private BigDecimal lotsize;

        private BigDecimal tradingpoint;

        private BigDecimal lasttradingday;

        private String industry;

        private String shsc;

        private String hsi;

        private String hscei;

        private String hs_tech;

        private String marketcap;

        private BigDecimal pe;

        private String yield;

        public int getId() {
            return id;
        }

        public void setId(int id) {
            this.id = id;
        }

        public String getStockcode() {
            return stockcode;
        }

        public void setStockcode(String stockcode) {
            this.stockcode = stockcode;
        }

        public String getStockname() {
            return stockname;
        }

        public void setStockname(String stockname) {
            this.stockname = stockname;
        }

        public BigDecimal getOpen() {
            return open;
        }

        public void setOpen(BigDecimal open) {
            this.open = open;
        }

        public BigDecimal getHigh() {
            return high;
        }

        public void setHigh(BigDecimal high) {
            this.high = high;
        }

        public BigDecimal getLow() {
            return low;
        }

        public void setLow(BigDecimal low) {
            this.low = low;
        }

        public BigDecimal getPreviousclose() {
            return previousclose;
        }

        public void setPreviousclose(BigDecimal previousclose) {
            this.previousclose = previousclose;
        }

        public BigDecimal getLastprice() {
            return lastprice;
        }

        public void setLastprice(BigDecimal lastprice) {
            this.lastprice = lastprice;
        }

        public String getChanged() {
            return changed;
        }

        public void setChanged(String changed) {
            this.changed = changed;
        }

        public String getChangedpercent() {
            return changedpercent;
        }

        public void setChangedpercent(String changedpercent) {
            this.changedpercent = changedpercent;
        }

        public BigDecimal getBuyprice() {
            return buyprice;
        }

        public void setBuyprice(BigDecimal buyprice) {
            this.buyprice = buyprice;
        }

        public BigDecimal getSellprice() {
            return sellprice;
        }

        public void setSellprice(BigDecimal sellprice) {
            this.sellprice = sellprice;
        }

        public BigDecimal getVolume() {
            return volume;
        }

        public void setVolume(BigDecimal volume) {
            this.volume = volume;
        }

        public BigDecimal getTurnover() {
            return turnover;
        }

        public void setTurnover(BigDecimal turnover) {
            this.turnover = turnover;
        }

        public BigDecimal getEps() {
            return eps;
        }

        public void setEps(BigDecimal eps) {
            this.eps = eps;
        }

        public BigDecimal getRatio() {
            return ratio;
        }

        public void setRatio(BigDecimal ratio) {
            this.ratio = ratio;
        }

        public BigDecimal getLotsize() {
            return lotsize;
        }

        public void setLotsize(BigDecimal lotsize) {
            this.lotsize = lotsize;
        }

        public BigDecimal getTradingpoint() {
            return tradingpoint;
        }

        public void setTradingpoint(BigDecimal tradingpoint) {
            this.tradingpoint = tradingpoint;
        }

        public BigDecimal getLasttradingday() {
            return lasttradingday;
        }

        public void setLasttradingday(BigDecimal lasttradingday) {
            this.lasttradingday = lasttradingday;
        }

        public String getIndustry() {
            return industry;
        }

        public void setIndustry(String industry) {
            this.industry = industry;
        }

        public String getShsc() {
            return shsc;
        }

        public void setShsc(String shsc) {
            this.shsc = shsc;
        }

        public String getHsi() {
            return hsi;
        }

        public void setHsi(String hsi) {
            this.hsi = hsi;
        }

        public String getHscei() {
            return hscei;
        }

        public void setHscei(String hscei) {
            this.hscei = hscei;
        }

        public String getHs_tech() {
            return hs_tech;
        }

        public void setHs_tech(String hs_tech) {
            this.hs_tech = hs_tech;
        }

        public String getMarketcap() {
            return marketcap;
        }

        public void setMarketcap(String marketcap) {
            this.marketcap = marketcap;
        }

        public BigDecimal getPe() {
            return pe;
        }

        public void setPe(BigDecimal pe) {
            this.pe = pe;
        }

        public String getYield() {
            return yield;
        }

        public void setYield(String yield) {
            this.yield = yield;
        }

        @Override
        public String toString() {
            return "{" +
                    "\n     id: " + id +
                    ", \n     stockcode: '" + stockcode + '\'' +
                    ", \n     stockname: '" + stockname + '\'' +
                    ", \n     open: " + open +
                    ", \n     high: " + high +
                    ", \n     low: " + low +
                    ", \n     previousclose: " + previousclose +
                    ", \n     lastprice: " + lastprice +
                    ", \n     changed: '" + changed + '\'' +
                    ", \n     changedpercent: '" + changedpercent + '\'' +
                    ", \n     buyprice: " + buyprice +
                    ", \n     sellprice: " + sellprice +
                    ", \n     volume: " + volume +
                    ", \n     turnover: " + turnover +
                    ", \n     eps: " + eps +
                    ", \n     ratio: " + ratio +
                    ", \n     lotsize: " + lotsize +
                    ", \n     tradingpoint: " + tradingpoint +
                    ", \n     lasttradingday: " + lasttradingday +
                    ", \n     industry: '" + industry + '\'' +
                    ", \n     shsc: '" + shsc + '\'' +
                    ", \n     hsi: '" + hsi + '\'' +
                    ", \n     hscei: '" + hscei + '\'' +
                    ", \n     hs_tech: '" + hs_tech + '\'' +
                    ", \n     marketcap: '" + marketcap + '\'' +
                    ", \n     pe: " + pe +
                    ", \n     yield: '" + yield + '\'' +
                    "\n }";
        }
    }

}
