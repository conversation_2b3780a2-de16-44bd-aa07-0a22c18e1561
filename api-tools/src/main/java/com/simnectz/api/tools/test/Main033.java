package com.simnectz.api.tools.test;

public class Main033 {

    public static void main(String[] args) {
        System.out.println(reverse(123));
        System.out.println(reverse(-456));
    }

    public static int reverse(int x) {
        // write code here
        int ans = 0;
        while (x != 0) {
            int pop = x % 10;
            if (ans > Integer.MAX_VALUE / 10 || (ans == Integer.MAX_VALUE / 10 && pop > 7))
                return 0;
            if (ans < Integer.MIN_VALUE / 10 || (ans == Integer.MIN_VALUE / 10 && pop < -8))
                return 0;
            ans = ans * 10 + pop;
            x /= 10;
        }
        return ans;
    }

}
