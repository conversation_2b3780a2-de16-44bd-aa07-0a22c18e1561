package com.simnectz.api.tools.test;

public class Config {

    // 响应体状态码名称
    public static final String RESPONSE_CODE_NAME = "code";
    // 响应体消息名称
    public static final String RESPONSE_MESSAGE_NAME = "desc";
    // 响应体数据名称
    public static final String RESPONSE_DATA_NAME = "data";
    // 请求成功状态码
    public static final String SUCCESS_STATE_CODE = "200";
    // 请求超时时间
    public static final Integer REQUEST_TIMEOUT = 300000;

}
