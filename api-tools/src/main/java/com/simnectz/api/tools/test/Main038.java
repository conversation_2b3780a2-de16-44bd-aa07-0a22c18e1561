package com.simnectz.api.tools.test;

import com.simnectz.api.tools.core.HttpRequest;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class Main038 {

    public static void main(String[] args) {
        // write your code here
        List<StockTransactionLog> transactionLogs = getStockTransactionLogs();
        BigDecimal holdings = getHoldings(transactionLogs);
        System.out.println(holdings);
    }

    private static BigDecimal getHoldings(List<StockTransactionLog> transactionLogs) {
        if (transactionLogs == null || transactionLogs.size() == 0) {
            return BigDecimal.ZERO;
        } else {
            return new BigDecimal(transactionLogs.stream().map(StockTransactionLog::getSharingNo)
                    .reduce(BigDecimal.ZERO, BigDecimal::add).stripTrailingZeros().toPlainString());
        }
    }

    public static List<StockTransactionLog> getStockTransactionLogs() {
        // request params
        Map<String, String> apiParams = new HashMap<>();
        apiParams.put("stockAccountNumber", "***********************");
        apiParams.put("stockCode", "00112.HK");
        // request headers
        Map<String, String> apiHeaders = new HashMap<>();
        apiHeaders.put("Content-Type", "application/json");
        // Call API
        List<StockTransactionLog> items = HttpRequest.postList(
                "https://************:8096/stock/stocks-transaction",
                apiParams,
                apiHeaders,
                StockTransactionLog.class
        );
        return items;
    }

    static class StockTransactionLog {
        private Integer id;
        private String countryCode;
        private String clearingCode;
        private String branchCode;
        private String sandBoxId;
        private String accountNumber;
        private String tradingOption;
        private String stockNumber;
        private BigDecimal sharingNo;
        private BigDecimal stockTrdingAmount;
        private BigDecimal stockTrdingCommission;
        private BigDecimal custodyCharges;
        private BigDecimal transactionAmount;
        private BigDecimal transactionDate;
        private String riskRating;
        private BigDecimal stockPrice;
        private BigDecimal lotSize;
        private String transactionDesc;

        public StockTransactionLog() {
        }

        public StockTransactionLog(Integer id, String countryCode, String clearingCode, String branchCode, String sandBoxId, String accountNumber, String tradingOption, String stockNumber, BigDecimal sharingNo, BigDecimal stockTrdingAmount, BigDecimal stockTrdingCommission, BigDecimal custodyCharges, BigDecimal transactionAmount, BigDecimal transactionDate, String riskRating, BigDecimal stockPrice, BigDecimal lotSize, String transactionDesc) {
            this.id = id;
            this.countryCode = countryCode;
            this.clearingCode = clearingCode;
            this.branchCode = branchCode;
            this.sandBoxId = sandBoxId;
            this.accountNumber = accountNumber;
            this.tradingOption = tradingOption;
            this.stockNumber = stockNumber;
            this.sharingNo = sharingNo;
            this.stockTrdingAmount = stockTrdingAmount;
            this.stockTrdingCommission = stockTrdingCommission;
            this.custodyCharges = custodyCharges;
            this.transactionAmount = transactionAmount;
            this.transactionDate = transactionDate;
            this.riskRating = riskRating;
            this.stockPrice = stockPrice;
            this.lotSize = lotSize;
            this.transactionDesc = transactionDesc;
        }

        public Integer getId() {
            return id;
        }

        public void setId(Integer id) {
            this.id = id;
        }

        public String getCountryCode() {
            return countryCode;
        }

        public void setCountryCode(String countryCode) {
            this.countryCode = countryCode;
        }

        public String getClearingCode() {
            return clearingCode;
        }

        public void setClearingCode(String clearingCode) {
            this.clearingCode = clearingCode;
        }

        public String getBranchCode() {
            return branchCode;
        }

        public void setBranchCode(String branchCode) {
            this.branchCode = branchCode;
        }

        public String getSandBoxId() {
            return sandBoxId;
        }

        public void setSandBoxId(String sandBoxId) {
            this.sandBoxId = sandBoxId;
        }

        public String getAccountNumber() {
            return accountNumber;
        }

        public void setAccountNumber(String accountNumber) {
            this.accountNumber = accountNumber;
        }

        public String getTradingOption() {
            return tradingOption;
        }

        public void setTradingOption(String tradingOption) {
            this.tradingOption = tradingOption;
        }

        public String getStockNumber() {
            return stockNumber;
        }

        public void setStockNumber(String stockNumber) {
            this.stockNumber = stockNumber;
        }

        public BigDecimal getSharingNo() {
            return sharingNo;
        }

        public void setSharingNo(BigDecimal sharingNo) {
            this.sharingNo = sharingNo;
        }

        public BigDecimal getStockTrdingAmount() {
            return stockTrdingAmount;
        }

        public void setStockTrdingAmount(BigDecimal stockTrdingAmount) {
            this.stockTrdingAmount = stockTrdingAmount;
        }

        public BigDecimal getStockTrdingCommission() {
            return stockTrdingCommission;
        }

        public void setStockTrdingCommission(BigDecimal stockTrdingCommission) {
            this.stockTrdingCommission = stockTrdingCommission;
        }

        public BigDecimal getCustodyCharges() {
            return custodyCharges;
        }

        public void setCustodyCharges(BigDecimal custodyCharges) {
            this.custodyCharges = custodyCharges;
        }

        public BigDecimal getTransactionAmount() {
            return transactionAmount;
        }

        public void setTransactionAmount(BigDecimal transactionAmount) {
            this.transactionAmount = transactionAmount;
        }

        public BigDecimal getTransactionDate() {
            return transactionDate;
        }

        public void setTransactionDate(BigDecimal transactionDate) {
            this.transactionDate = transactionDate;
        }

        public String getRiskRating() {
            return riskRating;
        }

        public void setRiskRating(String riskRating) {
            this.riskRating = riskRating;
        }

        public BigDecimal getStockPrice() {
            return stockPrice;
        }

        public void setStockPrice(BigDecimal stockPrice) {
            this.stockPrice = stockPrice;
        }

        public BigDecimal getLotSize() {
            return lotSize;
        }

        public void setLotSize(BigDecimal lotSize) {
            this.lotSize = lotSize;
        }

        public String getTransactionDesc() {
            return transactionDesc;
        }

        public void setTransactionDesc(String transactionDesc) {
            this.transactionDesc = transactionDesc;
        }

        @Override
        public String toString() {
            return "{" +
                    "\n     id='" + id + '\'' +
                    "\n     , countryCode='" + countryCode + '\'' +
                    "\n     , clearingCode='" + clearingCode + '\'' +
                    "\n     , branchCode='" + branchCode + '\'' +
                    "\n     , sandBoxId='" + sandBoxId + '\'' +
                    "\n     , accountNumber='" + accountNumber + '\'' +
                    "\n     , tradingOption='" + tradingOption + '\'' +
                    "\n     , stockNumber='" + stockNumber + '\'' +
                    "\n     , sharingNo='" + sharingNo + '\'' +
                    "\n     , stockTrdingAmount='" + stockTrdingAmount + '\'' +
                    "\n     , stockTrdingCommission='" + stockTrdingCommission + '\'' +
                    "\n     , custodyCharges='" + custodyCharges + '\'' +
                    "\n     , transactionAmount='" + transactionAmount + '\'' +
                    "\n     , transactionDate='" + transactionDate + '\'' +
                    "\n     , riskRating='" + riskRating + '\'' +
                    "\n     , stockPrice='" + stockPrice + '\'' +
                    "\n     , lotSize='" + lotSize + '\'' +
                    "\n     , transactionDesc='" + transactionDesc + '\'' +
                    "}\n";
        }
    }

}
