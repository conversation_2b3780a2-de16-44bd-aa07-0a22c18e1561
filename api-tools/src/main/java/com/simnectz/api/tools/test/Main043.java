package com.simnectz.api.tools.test;

import java.util.HashMap;
import java.util.Map;

public class Main043 {

    //
    public static void main(String[] args) {
        User u1 = new User(2003, "张三", 15000);
        User u2 = new User(2006, "赵六", 16000);
        User u3 = new User(2005, "王五", 5000);
        User u4 = new User(2004, "李四", 17000);
        HashMap<Integer, User> m = new HashMap<Integer, User>();
        m.put(2003, u1);
        m.put(2006, u2);
        m.put(2005, u3);
        m.put(2004, u4);
        for (Map.Entry<Integer, User> entry : m.entrySet()) {
            System.out.println(entry.getValue());
        }
    }
}

class User implements Comparable<Object> {
    String name;
    int id;
    int money;

    public User(int id, String name, int money) {
        this.name = name;
        this.id = id;
        this.money = money;

    }

    public int compareTo(Object o) {
        User u = (User) o;
        if (this.id - u.id > 0) {
            return 1;
        }
        if (this.id - u.id == 0) {
            return this.name.compareTo(u.name);
        }

        return -1;
    }

    @Override
    public String toString() {
        return "User{" +
                "name='" + name + '\'' +
                ", id=" + id +
                ", money=" + money +
                '}';
    }
}
