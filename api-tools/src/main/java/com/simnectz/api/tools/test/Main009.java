package com.simnectz.api.tools.test;

import com.simnectz.api.tools.core.HttpRequest;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class Main009 {

    // 英镑兑换美元
    public static void main(String[] args) {
        // write your code here
        List<CurrencyInformation> currencyList = getCurrencyList();
        // 当前货币代码
        String currentCurrencyCode = "GBP";
        // 要兑换的货币代码
        String exchangeCurrencyCode = "USD";
        // 当前货币金额
        BigDecimal currentAmount = new BigDecimal(5000);
        // 兑换后的金额
        BigDecimal exchangeAmount;

        // 当前货币的信息
        CurrencyInformation currentCurrency = null;
        // 要兑换货币的信息
        CurrencyInformation exchangeCurrency = null;

        for (CurrencyInformation currency : currencyList) {
            if (currency.getCurrencyCode().equalsIgnoreCase(exchangeCurrencyCode)) {
                exchangeCurrency = currency;
            }
            if (currency.getCurrencyCode().equalsIgnoreCase(currentCurrencyCode)) {
                currentCurrency = currency;
            }
        }

        if (exchangeCurrency == null) {
            System.out.println(exchangeCurrencyCode + " information does not exist");
            return;
        }
        if (currentCurrency == null) {
            System.out.println(currentCurrencyCode + " information does not exist");
            return;
        }

        exchangeAmount = currentAmount.multiply(currentCurrency.getBankBuy()).divide(exchangeCurrency.getBankSell(), 2, RoundingMode.HALF_UP);
        System.out.println(currentCurrencyCode + " " + currentAmount + " 可兑换 " + exchangeCurrencyCode + " " + exchangeAmount);
    }

    public static List<CurrencyInformation> getCurrencyList() {
        // request params
        Map<String, String> apiParams = new HashMap<>();
        apiParams.put("index", "1");
        apiParams.put("items", "9999");
        // request headers
        Map<String, String> apiHeaders = new HashMap<>();
        apiHeaders.put("Content-Type", "application/json");
        // Call API
        return HttpRequest.postList(
                "https://************:8096/currency/types",
                apiParams,
                apiHeaders,
                CurrencyInformation.class
        );
    }

    static class CurrencyInformation {

        private Integer id;

        private String currencyName;

        private String currencyCode;

        private String currencyPlaces;

        private BigDecimal bankBuy;

        private BigDecimal bankSell;

        public Integer getId() {
            return id;
        }

        public void setId(Integer id) {
            this.id = id;
        }

        public String getCurrencyName() {
            return currencyName;
        }

        public void setCurrencyName(String currencyName) {
            this.currencyName = currencyName;
        }

        public String getCurrencyCode() {
            return currencyCode;
        }

        public void setCurrencyCode(String currencyCode) {
            this.currencyCode = currencyCode;
        }

        public String getCurrencyPlaces() {
            return currencyPlaces;
        }

        public void setCurrencyPlaces(String currencyPlaces) {
            this.currencyPlaces = currencyPlaces;
        }

        public BigDecimal getBankBuy() {
            return bankBuy;
        }

        public void setBankBuy(BigDecimal bankBuy) {
            this.bankBuy = bankBuy;
        }

        public BigDecimal getBankSell() {
            return bankSell;
        }

        public void setBankSell(BigDecimal bankSell) {
            this.bankSell = bankSell;
        }

        @Override
        public String toString() {
            return "{" +
                    "\n    id: " + id +
                    ",\n    currencyName: '" + currencyName + '\'' +
                    ",\n    currencyCode: '" + currencyCode + '\'' +
                    ",\n    currencyPlaces: '" + currencyPlaces + '\'' +
                    ",\n    bankBuy: " + bankBuy +
                    ",\n    bankSell: " + bankSell +
                    "\n}";
        }

    }


}
