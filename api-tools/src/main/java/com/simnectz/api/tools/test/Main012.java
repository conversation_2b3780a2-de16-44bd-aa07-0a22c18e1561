package com.simnectz.api.tools.test;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

public class Main012 {

    // 计算出给定时间一年以后的时间，打印出计算的时间
    public static void main(String[] args) throws ParseException {
        // write your code here
        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date date = simpleDateFormat.parse("2022-07-25 14:34:27");
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.YEAR, 1);
        System.out.println(simpleDateFormat.format(calendar.getTime()));
    }

}
