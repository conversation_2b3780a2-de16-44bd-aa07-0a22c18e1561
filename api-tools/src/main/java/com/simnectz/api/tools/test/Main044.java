package com.simnectz.api.tools.test;

public class Main044 {

    public static void main(String[] args) {
        TicketCounter tc = new TicketCounter();
        Thread t1 = new Thread(tc);
        Thread t2 = new Thread(tc);
        Thread t3 = new Thread(tc);
        t1.setName("一号站台");
        t2.setName("二号站台");
        t3.setName("三号站台");
        t1.start();
        t2.start();
        t3.start();
    }

    static class TicketCounter implements Runnable {
        int ticket = 100;
        @Override
        public void run() {
            while (true) {
                synchronized(this){
                    if( ticket > 0 ) {
                        try {
                            Thread.sleep(100);
                        } catch (InterruptedException e) {
                            e.printStackTrace();
                        }
                        ticket -=1;
                        System.out.println(Thread.currentThread().getName() + "卖出一张票，剩余" +ticket +" 张票");
                    } else {
                        break;
                    }
                }
            }
        }
    }

}
