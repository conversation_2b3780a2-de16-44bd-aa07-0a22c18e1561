<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.simnectz</groupId>
    <artifactId>soi-plus-platform</artifactId>
    <version>1.0.0</version>
    <packaging>pom</packaging>

    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.7.10</version>
    </parent>

    <modules>
        <module>soi-plus-core-backend</module>
        <module>soi-plus-business-backend</module>
        <module>api-tools</module>
    </modules>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>

        <hutool-all.version>5.8.25</hutool-all.version>
        <mybatis-flex-spring-boot-starter.version>1.7.7</mybatis-flex-spring-boot-starter.version>
        <fastjson2.version>2.0.26</fastjson2.version>
        <fastjson.version>1.2.83</fastjson.version>
        <poi-ooxml.version>4.1.2</poi-ooxml.version>
        <guava.version>27.0.1-android</guava.version>
        <xlsx-streamer.version>2.2.0</xlsx-streamer.version>
        <commons-httpclient.version>3.1</commons-httpclient.version>
        <xstream.version>1.4.7</xstream.version>
        <aspose-cells.version>20.4</aspose-cells.version>
        <aspose-slides.version>20.7</aspose-slides.version>
        <aspose-words.version>18.8</aspose-words.version>
        <aliyun-java-sdk-core.version>4.4.0</aliyun-java-sdk-core.version>
        <aliyun-java-sdk-kms.version>2.6.0</aliyun-java-sdk-kms.version>
        <aliyun-java-sdk-ecs.version>4.16.7</aliyun-java-sdk-ecs.version>
        <aliyun-sdk-oss.version>3.10.2</aliyun-sdk-oss.version>
        <commons.io.version>2.5</commons.io.version>
        <spring-security-oauth2-autoconfigure>2.1.3.RELEASE</spring-security-oauth2-autoconfigure>
        <swagger-core>1.6.0</swagger-core>
        <bitwalker.version>1.21</bitwalker.version>
    </properties>

    <!-- 设置 jitpack.io 插件仓库 -->
    <pluginRepositories>
        <pluginRepository>
            <id>jitpack.io</id>
            <url>https://jitpack.io</url>
        </pluginRepository>
    </pluginRepositories>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>cn.hutool</groupId>
                <artifactId>hutool-all</artifactId>
                <version>${hutool-all.version}</version>
            </dependency>

            <dependency>
                <groupId>com.mybatis-flex</groupId>
                <artifactId>mybatis-flex-spring-boot-starter</artifactId>
                <version>${mybatis-flex-spring-boot-starter.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba.fastjson2</groupId>
                <artifactId>fastjson2</artifactId>
                <version>${fastjson2.version}</version>
            </dependency>

            <dependency>
                <groupId>com.alibaba</groupId>
                <artifactId>fastjson</artifactId>
                <version>${fastjson.version}</version>
            </dependency>

            <dependency>
                <groupId>org.apache.poi</groupId>
                <artifactId>poi-ooxml</artifactId>
                <version>${poi-ooxml.version}</version>
            </dependency>

            <dependency>
                <groupId>com.google.guava</groupId>
                <artifactId>guava</artifactId>
                <version>${guava.version}</version>
            </dependency>

            <dependency>
                <groupId>com.monitorjbl</groupId>
                <artifactId>xlsx-streamer</artifactId>
                <version>${xlsx-streamer.version}</version>
            </dependency>

            <dependency>
                <groupId>commons-httpclient</groupId>
                <artifactId>commons-httpclient</artifactId>
                <version>${commons-httpclient.version}</version>
            </dependency>

            <dependency>
                <groupId>com.thoughtworks.xstream</groupId>
                <artifactId>xstream</artifactId>
                <version>${xstream.version}</version>
            </dependency>

            <dependency>
                <groupId>com.aspose</groupId>
                <artifactId>aspose-cells</artifactId>
                <version>${aspose-cells.version}</version>
            </dependency>

            <dependency>
                <groupId>com.aspose</groupId>
                <artifactId>aspose-slides</artifactId>
                <version>${aspose-slides.version}</version>
            </dependency>

            <dependency>
                <groupId>com.aspose</groupId>
                <artifactId>aspose-words</artifactId>
                <version>${aspose-words.version}</version>
            </dependency>

            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>aliyun-java-sdk-core</artifactId>
                <version>${aliyun-java-sdk-core.version}</version>
            </dependency>

            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>aliyun-java-sdk-kms</artifactId>
                <version>${aliyun-java-sdk-kms.version}</version>
            </dependency>

            <dependency>
                <groupId>com.aliyun</groupId>
                <artifactId>aliyun-java-sdk-ecs</artifactId>
                <version>${aliyun-java-sdk-ecs.version}</version>
            </dependency>

            <dependency>
                <groupId>com.aliyun.oss</groupId>
                <artifactId>aliyun-sdk-oss</artifactId>
                <version>${aliyun-sdk-oss.version}</version>
            </dependency>

            <dependency>
                <groupId>commons-io</groupId>
                <artifactId>commons-io</artifactId>
                <version>${commons.io.version}</version>
            </dependency>

            <dependency>
                <groupId>org.springframework.security.oauth.boot</groupId>
                <artifactId>spring-security-oauth2-autoconfigure</artifactId>
                <version>${spring-security-oauth2-autoconfigure}</version>
            </dependency>

            <dependency>
                <groupId>io.swagger</groupId>
                <artifactId>swagger-core</artifactId>
                <version>${swagger-core}</version>
            </dependency>

            <!-- 解析客户端操作系统、浏览器等 -->
            <dependency>
                <groupId>eu.bitwalker</groupId>
                <artifactId>UserAgentUtils</artifactId>
                <version>${bitwalker.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>

</project>
