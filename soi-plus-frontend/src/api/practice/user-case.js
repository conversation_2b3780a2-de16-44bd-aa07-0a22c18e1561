import { get, post } from '@/api/utils/http'
import { SOI_PLUS_BUSINESS_API_URL } from '@/contains'

export function getListByGroup(params) {
  return get(SOI_PLUS_BUSINESS_API_URL + '/v1/practical/user-case/listByGroup', params)
}

export function getUserCaseSubcategory(params) {
  return get(SOI_PLUS_BUSINESS_API_URL + '/v1/practical/user-case/listByGroup', params)
}

export function getAllUserCase() {
  return get(SOI_PLUS_BUSINESS_API_URL + '/v1/practical/user-case/getAll')
}

export function saveCategory(data) {
  return post(SOI_PLUS_BUSINESS_API_URL + '/v1/practical/user-case/save', data)
}

export function deleteCategory(params) {
  return post(SOI_PLUS_BUSINESS_API_URL + '/v1/practical/user-case/delete', {}, { params })
}
