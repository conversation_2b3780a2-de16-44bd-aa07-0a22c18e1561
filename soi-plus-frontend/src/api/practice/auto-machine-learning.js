import { post, get } from '@/api/utils/http'
import { MACHINE_LEARNING_API_URL } from '@/contains'

export function deleteFile(data) {
  return post(
    MACHINE_LEARNING_API_URL + '/deletefile',
    data,
    { headers: { 'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8' }}
  )
}

export function getFiles(params) {
  return get(
    MACHINE_LEARNING_API_URL + '/getfiles',
    params
  )
}

export function updateFile(data) {
  return post(
    MACHINE_LEARNING_API_URL + '/updatefile',
    data,
    { headers: { 'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8' }}
  )
}

export function getFileColumns(params) {
  return get(
    MACHINE_LEARNING_API_URL + '/getfilecolumns',
    params
  )
}

export function parsefile(params) {
  return get(
    MACHINE_LEARNING_API_URL + '/parsefile',
    params
  )
}

export function deleteTestFile(data) {
  return post(
    MACHINE_LEARNING_API_URL + '/deletetestfile',
    data,
    { headers: { 'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8' }}
  )
}

export function getPredictResult(params) {
  return get(
    MACHINE_LEARNING_API_URL + '/getpredictresult',
    params
  )
}

export function batchPredict(data) {
  return post(
    MACHINE_LEARNING_API_URL + '/batchpredict',
    data,
    { headers: { 'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8' }}
  )
}

export function getBatchStatus(params) {
  return get(
    MACHINE_LEARNING_API_URL + '/getbatchstatus',
    params
  )
}

export function singlePredict(data) {
  return post(
    MACHINE_LEARNING_API_URL + '/predict',
    data,
    { headers: { 'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8' }}
  )
}

export function showModel(params) {
  return get(
    MACHINE_LEARNING_API_URL + '/showmodel',
    params
  )
}

export function updateModel(data) {
  return post(
    MACHINE_LEARNING_API_URL + '/updatemodel',
    data,
    { headers: { 'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8' }}
  )
}

export function getApplications(params) {
  return get(
    MACHINE_LEARNING_API_URL + '/getapplications',
    params
  )
}

export function deleteModel(data) {
  return post(
    MACHINE_LEARNING_API_URL + '/deletemodel',
    data,
    { headers: { 'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8' }}
  )
}

export function getH2oModel(params) {
  return get(
    MACHINE_LEARNING_API_URL + '/geth2omodel',
    params
  )
}

export function sklearnTrain(data) {
  return post(
    MACHINE_LEARNING_API_URL + '/sklearntrain',
    data,
    { headers: { 'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8' }}
  )
}

export function h2oAutomlTrain(data) {
  return post(
    MACHINE_LEARNING_API_URL + '/h2oautomltrain',
    data,
    { headers: { 'Content-Type': 'application/x-www-form-urlencoded;charset=UTF-8' }}
  )
}

export function getAutoSklearnTrainFlag(params) {
  return get(
    MACHINE_LEARNING_API_URL + '/getautosklearntrainflag',
    params
  )
}

export function getH2oAutomlTrainFlag(params) {
  return get(
    MACHINE_LEARNING_API_URL + '/geth2oautomltrainflag',
    params
  )
}
