import { del, get, post } from '@/api/utils/http'
import { SOI_PLUS_BUSINESS_API_URL } from '@/contains'

export function getDataTableList(params) {
  return get(SOI_PLUS_BUSINESS_API_URL + '/v1/practical/data-management/tablelist', params)
}

export function deleteTable(params) {
  return del(SOI_PLUS_BUSINESS_API_URL + '/v1/practical/data-management/deletetable', params)
}

export function getTableData(params) {
  return get(SOI_PLUS_BUSINESS_API_URL + '/v1/practical/data-management/gettabledata', params)
}

export function uploadDataTable(data) {
  return post(SOI_PLUS_BUSINESS_API_URL + '/v1/practical/data-management/uploadtable', data)
}

export function uploadDataDescription(data) {
  return post(SOI_PLUS_BUSINESS_API_URL + '/v1/practical/data-management/uploadintro', data)
}

export function isExistDatabase(params) {
  return get(SOI_PLUS_BUSINESS_API_URL + '/v1/practical/data-management/existdb', params)
}

export function createDatabase(data) {
  return post(SOI_PLUS_BUSINESS_API_URL + '/v1/practical/data-management/createdb', data)
}
