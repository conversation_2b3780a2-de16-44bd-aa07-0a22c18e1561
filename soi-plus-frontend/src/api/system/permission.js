import { del, get, post, put } from '@/api/utils/http'

export function getPermissionOptions() {
  return get('/v1/system/permissions/options')
}

export function createPermission(data) {
  return post('/v1/system/permissions', data)
}

export function updatePermission(data) {
  return put('/v1/system/permissions', data)
}

export function batchDeletePermission(permissionIds) {
  return del('/v1/system/permissions', { permissionIds })
}

export function getPermissionList(params) {
  return get('/v1/system/permissions', params)
}

export function getPermissionDetails(id) {
  return get(`/v1/system/permissions/${id}`)
}
