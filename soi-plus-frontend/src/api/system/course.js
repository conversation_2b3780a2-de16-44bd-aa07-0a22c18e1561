import { del, get, post, put } from '@/api/utils/http'
import { SOI_PLUS_BUSINESS_API_URL } from '@/contains'

export function getCourseCategoryOptions() {
  return get(SOI_PLUS_BUSINESS_API_URL + '/v1/learn/course-category/options')
}

export function getCourseList(params) {
  return get(SOI_PLUS_BUSINESS_API_URL + '/v1/learn/course', params)
}

export function createCourse(data) {
  console.log(data)
  return post(SOI_PLUS_BUSINESS_API_URL + '/v1/learn/course', data)
}

export function updateCourse(data) {
  return put(SOI_PLUS_BUSINESS_API_URL + '/v1/learn/course', data)
}

export function getCourseDetails(courseCode) {
  return get(SOI_PLUS_BUSINESS_API_URL + `/v1/learn/course/code-details/${courseCode}`)
}

export function batchDeleteCourse(ids) {
  return del(SOI_PLUS_BUSINESS_API_URL + '/v1/learn/course', { ids })
}

export function getCourseByCategoryId(categoryId) {
  return get(SOI_PLUS_BUSINESS_API_URL + `/v1/learn/course/category-id/${categoryId}`)
}

export function getCourseByCategoryIds(categoryIds) {
  return get(SOI_PLUS_BUSINESS_API_URL + `/v1/learn/course/category-ids/${categoryIds}`)
}
