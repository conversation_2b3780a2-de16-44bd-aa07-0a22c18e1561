import { get, post } from '@/api/utils/http'
import { WORKFLOW_API_URL } from '@/contains'

export function getWorkflow(params) {
  return get(WORKFLOW_API_URL + '/workflow/getWorkflow', params)
}

export function getFlows(params) {
  return get(WORKFLOW_API_URL + '/workflow/getFlows', params)
}

export function saveFlow(data) {
  return post(WORKFLOW_API_URL + '/workflow/saveflow', data)
}

export function deleteFlow(data) {
  return post(WORKFLOW_API_URL + '/workflow/deleteFlow', data)
}

export function getRules(params) {
  return get(WORKFLOW_API_URL + '/workflow/getRules', params)
}

export function getRulesById(params) {
  return get(WORKFLOW_API_URL + '/workflow/getRulesById', params)
}

export function getGroup(params) {
  return get(WORKFLOW_API_URL + '/workflow/getGroup', params)
}

export function deleteDeployment(data) {
  return post(WORKFLOW_API_URL + '/workflow/deleteDeployment', data)
}

export function getFlowsByGroup(data) {
  return post(WORKFLOW_API_URL + '/workflow/getFlowsbyGroup', data)
}

export function saveRule(data) {
  return post(WORKFLOW_API_URL + '/workflow/saveRule', data)
}

export function deleteRule(data) {
  return post(WORKFLOW_API_URL + '/workflow/deleteRule', data)
}

export function createOrUpdateDeploy(url, data) {
  return post(WORKFLOW_API_URL + url, data)
}
