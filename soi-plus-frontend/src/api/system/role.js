import { del, get, post, put } from '@/api/utils/http'

export function getRoleOptions() {
  return get('/v1/system/roles/options')
}

export function createRole(data) {
  return post('/v1/system/roles', data)
}

export function updateRole(data) {
  return put('/v1/system/roles', data)
}

export function batchDeleteRole(roleIds) {
  return del('/v1/system/roles', { roleIds })
}

export function getRoleList(params) {
  return get('/v1/system/roles', params)
}

export function getRoleDetails(id) {
  return get(`/v1/system/roles/${id}`)
}
