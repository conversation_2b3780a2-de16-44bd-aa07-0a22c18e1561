import { del, get, post, put } from '@/api/utils/http'
import { SOI_PLUS_BUSINESS_API_URL } from '@/contains'

export function getCourseCategoryList(params) {
  return get(SOI_PLUS_BUSINESS_API_URL + '/v1/learn/course-category', params)
}

export function createCourseCategory(data) {
  console.log(data)
  return post(SOI_PLUS_BUSINESS_API_URL + '/v1/learn/course-category', data)
}

export function updateCourseCategory(data) {
  return put(SOI_PLUS_BUSINESS_API_URL + '/v1/learn/course-category', data)
}

export function getCourseCategoryDetails(id) {
  return get(SOI_PLUS_BUSINESS_API_URL + `/v1/learn/course-category/${id}`)
}

export function batchDeleteCourseCategory(ids) {
  return del(SOI_PLUS_BUSINESS_API_URL + '/v1/learn/course-category', { ids })
}
