// translate router.meta.title, be used in breadcrumb sidebar tagsview
export function generateTitle(title) {
  const hasKey = this.$te('soi.router.' + title)

  if (hasKey) {
    // $t :this method from vue-i18n, inject in @/lang/index.js
    return this.$t('soi.router.' + title)
  }
  return title
}

export function getMessage(key) {
  // $t :this method from vue-i18n, inject in @/lang/index.js
  return this.$t(key)
}
