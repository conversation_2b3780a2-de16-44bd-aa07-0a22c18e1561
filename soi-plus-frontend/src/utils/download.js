import { saveAs } from 'file-saver'

export function downloadPdf(url, name) {
  // url = url.replace('https://simnectzplatform.com', 'http://localhost:8848')

  const oReq = new XMLHttpRequest()

  // Configure XMLHttpRequest
  oReq.open('GET', url, true)

  // Important to use the blob response type
  oReq.responseType = 'blob'

  // When the file request finishes
  // Is up to you, the configuration for error events etc.
  oReq.onload = function() {
    // Once the file is downloaded, open a new window with the PDF
    // Remember to allow the POP-UPS in your browser
    const file = new Blob([oReq.response], {
      type: 'application/pdf'
    })

    // Generate file download directly in the browser !
    saveAs(file, name + '.pdf')
  }

  oReq.send()
}

export function downloadMp4(url, name) {
  // url = url.replace('https://simnectzplatform.com', 'http://localhost:8848')

  const oReq = new XMLHttpRequest()

  // Configure XMLHttpRequest
  oReq.open('GET', url, true)

  // Important to use the blob response type
  oReq.responseType = 'blob'

  // When the file request finishes
  // Is up to you, the configuration for error events etc.
  oReq.onload = function() {
    // Once the file is downloaded, open a new window with the PDF
    // Remember to allow the POP-UPS in your browser
    const file = new Blob([oReq.response], {
      type: 'video/mp4'
    })

    // Generate file download directly in the browser !
    saveAs(file, name + '.mp4')
  }

  oReq.send()
}
