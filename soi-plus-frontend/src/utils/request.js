import axios from 'axios'
import { Message } from 'element-ui'
import store from '@/store'
import { getAccessToken } from '@/utils/auth'
import JSONBig from 'json-bigint'
import router from '@/router'
import { SOI_PLUS_CORE_API_URL } from '@/contains'

// create an axios instance
const service = axios.create({
  baseURL: SOI_PLUS_CORE_API_URL, // url = base url + request url
  // withCredentials: true, // send cookies when cross-domain requests
  timeout: 300000, // request timeout
  transformResponse: (data) => {
    try {
      return JSONBig({ storeAsString: true }).parse(data)
    } catch (error) {
      return data
    }
  }
})

// request interceptor
service.interceptors.request.use(
  config => {
    // do something before request is sent

    if (store.getters.accessToken) {
      // let each request carry token
      // ['X-Token'] is a custom headers key
      // please modify it according to the actual situation
      config.headers['Authorization'] = 'Bearer ' + getAccessToken()
    }
    return config
  },
  error => {
    // do something with request error
    console.log(error) // for debug
    return Promise.reject(error)
  }
)

// response interceptor
service.interceptors.response.use(
  /**
   * If you want to get http information such as headers or status
   * Please return  response => response
  */

  /**
   * Determine the request status by custom code
   * Here is just an example
   * You can also judge the status by HTTP Status Code
   */
  response => {
    if (response.config.responseType === 'blob') {
      return response
    }

    const res = response.data

    // if the custom code is not 20000, it is judged as an error.
    if (
      res.code !== 20000 &&
      // 机器学习有关的成功标识
      res.result !== 'success' &&
      typeof res.flag !== 'boolean'
    ) {
      Message({
        message: res.message || res.info || 'Error',
        type: 'error',
        duration: 5 * 1000
      })

      return Promise.reject(res)
    } else {
      return res
    }
  },
  error => {
    console.log('err' + error) // for debug
    if (error.response.status === 401) {
      const message = 'Unauthorized'
      Message({
        message: message,
        type: 'error',
        duration: 5 * 1000
      })

      store.dispatch('user/logout')
      router.push('/authorize/login')
    }
    return Promise.reject(error)
  }
)

export default service
