import Layout from '@/layouts/system/index.vue'

export default [
  {
    path: '/practice-management',
    component: Layout,
    redirect: '/practice-management/workflow-config',
    meta: { module: 'system', title: 'practiceManagement', icon: 'practice-management', source: 'management:practice' },
    children: [
      // T&C
      {
        path: 'tc-management',
        name: 'practice-management-tc-management',
        component: () => import('@/views/admin/practice/tc-management/index.vue'),
        meta: { title: 'tcManagement', icon: 'tc-management', source: 'management:practice:tc' }
      },
      // sandbox
      {
        path: 'sandbox-management',
        name: 'practice-management-sandbox-management',
        component: () => import('@/views/admin/practice/sandbox-management/index.vue'),
        meta: { title: 'sandboxManagement', icon: 'sandbox-management', source: 'management:practice:sandbox' }
      },
      // document
      {
        path: 'document-management',
        name: 'practice-management-document-management',
        component: () => import('@/views/admin/practice/document-management/index.vue'),
        meta: { title: 'documentManagement', icon: 'document-management', source: 'management:practice:document' }
      },
      {
        path: 'document-list/:classId',
        name: 'practice-management-document-list',
        hidden: true,
        component: () => import('@/views/admin/practice/document-management/DocumentList.vue'),
        meta: { title: 'documentList' }
      },
      {
        path: 'manage-document/:classId',
        name: 'practice-management-manage-document',
        hidden: true,
        component: () => import('@/views/admin/practice/document-management/ManageDocument.vue'),
        meta: { title: 'manageDocument' }
      },
      // workflow
      {
        path: 'workflow-config',
        name: 'practice-management-workflow-config',
        component: () => import('@/views/admin/practice/workflow-config/layout.vue'),
        meta: { title: 'workflowConfig', icon: 'workflow-config', source: 'management:practice:workflow-config' },
        children: [
          {
            path: 'workflow-list',
            name: 'practice-management-workflow-list',
            component: () => import('@/views/admin/practice/workflow-config/index.vue'),
            meta: { title: 'workflowList', icon: 'workflow-list' }
          },
          {
            path: 'node-management',
            name: 'practice-management-node-management',
            component: () => import('@/views/admin/practice/workflow-config/NodeManagement.vue'),
            meta: { title: 'nodeManagement', icon: 'node-management' }
          },
          {
            path: 'rule-management',
            name: 'practice-management-rule-management',
            component: () => import('@/views/admin/practice/workflow-config/RuleManagement.vue'),
            meta: { title: 'ruleManagement', icon: 'rule-management' }
          },
          {
            path: 'create-or-update-rule',
            name: 'practice-management-create-or-update-rule',
            hidden: true,
            component: () => import('@/views/admin/practice/workflow-config/CreateOrUpdateRule.vue'),
            meta: { title: 'ruleManagement' }
          }
        ]
      },
      {
        path: 'manage-e-kyc-workflow',
        name: 'practice-management-manage-e-kyc-workflow',
        hidden: true,
        component: () => import('@/views/admin/practice/workflow-config/ManageEKYCWorkflow.vue'),
        meta: { title: 'manageEKYCWorkflow' }
      },
      {
        path: 'manage-insurance-workflow',
        name: 'practice-management-manage-insurance-workflow',
        hidden: true,
        component: () => import('@/views/admin/practice/workflow-config/ManageInsuranceWorkflow.vue'),
        meta: { title: 'manageInsuranceWorkflow' }
      },
      {
        path: 'e-kyc-workflow-details',
        name: 'practice-management-e-kyc-workflow-details',
        hidden: true,
        component: () => import('@/views/admin/practice/workflow-config/EKYCWorkflowDetails.vue'),
        meta: { title: 'eKYCWorkflowDetails' }
      },
      {
        path: 'insurance-workflow-details',
        name: 'practice-management-insurance-workflow-details',
        hidden: true,
        component: () => import('@/views/admin/practice/workflow-config/InsuranceWorkflowDetails.vue'),
        meta: { title: 'insuranceWorkflowDetails' }
      },
      // coding question management
      {
        path: 'coding-question-management',
        name: 'practice-management-coding-question-management',
        component: () => import('@/views/admin/practice/coding-question-management/index.vue'),
        meta: { title: 'codingQuestionManagement', icon: 'coding-question-management', source: 'management:practice:coding-question' }
      },
      // API management
      {
        path: 'apis',
        name: 'practice-management-api',
        component: () => import('@/views/admin/practice/apis/index.vue'),
        meta: { title: 'apis', icon: 'apis', source: 'management:practice:apis' },
        redirect: '/practice-management/apis/api-management',
        children: [
          {
            path: 'api-management',
            name: 'practice-management-api-management',
            component: () => import('@/views/admin/practice/apis/api-management/index.vue'),
            meta: { title: 'apiManagement', icon: 'api-management' }
          },
          {
            path: 'api-category-management',
            name: 'practice-management-api-category-management',
            component: () => import('@/views/admin/practice/apis/api-category-management/index.vue'),
            meta: { title: 'apiCategoryManagement', icon: 'api-category-management' }
          },
          {
            path: 'api-subcategory-management/:classId',
            name: 'practice-management-api-subcategory-management',
            hidden: true,
            component: () => import('@/views/admin/practice/apis/api-subcategory-management/index.vue'),
            meta: { title: 'apiSubcategoryManagement' }
          },
          {
            path: 'api-list/:classId/:subclassId',
            name: 'practice-management-api-list',
            hidden: true,
            component: () => import('@/views/admin/practice/apis/api-list/index.vue'),
            meta: { title: 'apiList' }
          },
          {
            path: 'tryout-api/:apiId',
            name: 'practice-management-tryout-api',
            hidden: true,
            meta: { title: 'tryoutApi' },
            component: () => import('@/views/practice/technological-development/api/api-market-place/tryout-api')
          },
          {
            path: 'add-api',
            name: 'practice-management-add-api',
            hidden: true,
            component: () => import('@/views/admin/practice/apis/add-api/index.vue'),
            meta: { title: 'addApi' }
          },
          {
            path: 'edit-api/:classId/:subclassId/:apiId',
            name: 'practice-management-edit-api',
            hidden: true,
            component: () => import('@/views/admin/practice/apis/edit-api/index.vue'),
            meta: { title: 'editApi' }
          },
          {
            path: 'import-api',
            name: 'practice-management-import-api',
            hidden: true,
            component: () => import('@/views/admin/practice/apis/import-api/index.vue'),
            meta: { title: 'importApi' }
          }
        ]
      }
    ]
  }
]
