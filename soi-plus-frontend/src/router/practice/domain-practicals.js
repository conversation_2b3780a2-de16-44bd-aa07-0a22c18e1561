import PracticeLayout from '@/layouts/practice/index.vue'

export default [
  {
    path: '/domain-practicals',
    component: PracticeLayout,
    meta: { module: 'practice', title: 'domainPracticals', image: 'domain-practicals.png', source: 'practice:domain-practicals' },
    children: [
      {
        path: 'category',
        name: 'domain-practicals-category',
        meta: { title: 'domainPracticals', image: 'domain-practicals.png' },
        component: () => import('@/views/practice/domain-practicals')
      },
      {
        path: 'subcategory/:id',
        name: 'domain-practicals-subcategory',
        hidden: true,
        meta: { title: 'domainPracticals' },
        component: () => import('@/views/practice/domain-practicals/subcategory')
      },
      {
        path: 'document-list',
        name: 'domain-practicals-document-list',
        hidden: true,
        meta: { title: 'domainPracticals' },
        component: () => import('@/views/practice/domain-practicals/document-list')
      },
      {
        path: 'manage-e-kyc-workflow',
        name: 'domain-practicals-manage-e-kyc-workflow',
        hidden: true,
        component: () => import('@/views/admin/practice/workflow-config/ManageEKYCWorkflow.vue'),
        meta: { title: 'manageEKYCWorkflow' }
      },
      {
        path: 'manage-insurance-workflow',
        name: 'domain-practicals-manage-insurance-workflow',
        hidden: true,
        component: () => import('@/views/admin/practice/workflow-config/ManageInsuranceWorkflow.vue'),
        meta: { title: 'manageInsuranceWorkflow' }
      },
      {
        path: 'e-kyc-workflow-details',
        name: 'domain-practicals-e-kyc-workflow-details',
        hidden: true,
        component: () => import('@/views/admin/practice/workflow-config/EKYCWorkflowDetails.vue'),
        meta: { title: 'eKYCWorkflowDetails' }
      },
      {
        path: 'insurance-workflow-details',
        name: 'domain-practicals-insurance-workflow-details',
        hidden: true,
        component: () => import('@/views/admin/practice/workflow-config/InsuranceWorkflowDetails.vue'),
        meta: { title: 'insuranceWorkflowDetails' }
      },
      {
        path: 'public-node-rules',
        name: 'domain-practicals-public-node-rules',
        hidden: true,
        component: () => import('@/views/admin/practice/workflow-config/PublicNodeRules.vue'),
        meta: { title: 'publicNodeRules' }
      }
      // {
      //   path: 'business',
      //   component: PracticeLayout,
      //   meta: { title: 'domainPracticalsBusiness', module: 'practice', image: 'practice-domain-practicals-business.jpg' },
      //   children: [
      //     {
      //       path: 'retail-banking',
      //       component: PracticeLayout,
      //       meta: { title: 'domainPracticalsBusinessRetailBanking', module: 'practice', image: 'practice-domain-practicals-business-retail-banking.png' },
      //       children: []
      //     },
      //     {
      //       path: 'commercial-banking',
      //       component: PracticeLayout,
      //       meta: { title: 'domainPracticalsBusinessCommercialBanking', module: 'practice', image: 'practice-domain-practicals-business-commercial-banking.jpg' },
      //       children: []
      //     },
      //     {
      //       path: 'risk-management',
      //       component: PracticeLayout,
      //       meta: { title: 'domainPracticalsBusinessRiskManagement', module: 'practice', image: 'practice-domain-practicals-business-risk-management.jpg' },
      //       children: [
      //         {
      //           path: 'fraud-risk-management',
      //           component: PracticeLayout,
      //           meta: { title: 'domainPracticalsBusinessFraudRiskManagement', module: 'practice', image: 'practice-domain-practicals-business-fraud-risk-management.png' },
      //           children: []
      //         },
      //         {
      //           path: 'operational-risk',
      //           component: PracticeLayout,
      //           meta: { title: 'domainPracticalsBusinessOperationalRisk', module: 'practice', image: 'practice-domain-practicals-business-operational-risk.jpg' },
      //           children: []
      //         },
      //         {
      //           path: 'regulatory-risk',
      //           component: PracticeLayout,
      //           meta: { title: 'domainPracticalsBusinessRegulatoryRisk', module: 'practice', image: 'practice-domain-practicals-business-regulatory-risk.png' },
      //           children: []
      //         }
      //       ]
      //     }
      //   ]
      // },
      // {
      //   path: 'technology',
      //   component: PracticeLayout,
      //   meta: { title: 'domainPracticalsTechnology', module: 'practice', image: 'practice-domain-practicals-technology.png' },
      //   children: [
      //     {
      //       path: 'blockchain',
      //       component: PracticeLayout,
      //       meta: { title: 'domainPracticalsTechnologyBlockchain', module: 'practice', image: 'practice-domain-practicals-technology-blockchain.png' },
      //       children: []
      //     },
      //     {
      //       path: 'data-analysis',
      //       component: PracticeLayout,
      //       meta: { title: 'domainPracticalsTechnologyDataAnalysis', module: 'practice', image: 'practice-domain-practicals-technology-data-analysis.png' },
      //       children: []
      //     },
      //     {
      //       path: 'programming-language',
      //       component: PracticeLayout,
      //       meta: { title: 'domainPracticalsTechnologyProgrammingLanguage', module: 'practice', image: 'practice-domain-practicals-technology-programming-language.png' },
      //       children: []
      //     }
      //   ]
      // }
    ]
  }
]
