import Vue from 'vue'
import VueI18n from 'vue-i18n'
import Cookies from 'js-cookie'
import elementEnLocale from 'element-ui/lib/locale/lang/en'
import elementZhLocale from 'element-ui/lib/locale/lang/zh-CN'
import elementChtLocale from 'element-ui/lib/locale/lang/zh-TW'
import zhSOILocale from '@/lang/locales/zh.json'
import enSOILocale from '@/lang/locales/en.json'
import chtSOILocale from '@/lang/locales/cht.json'

Vue.use(VueI18n)

const messages = {
  en: {
    ...enSOILocale,
    ...elementEnLocale
  },
  zh: {
    ...zhSOILocale,
    ...elementZhLocale
  },
  cht: {
    ...chtSOILocale,
    ...elementChtLocale
  }
}

const i18n = new VueI18n({
  locale: Cookies.get('language') || (['hsu'].includes(process.env.VUE_APP_SERVER_IP) ? 'en' : 'zh'), // set locale
  messages // set locale messages
})

export default i18n
