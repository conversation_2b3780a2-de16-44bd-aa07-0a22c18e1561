<template>
  <div ref="SQLEditor" :style="`width: ${width}; height: ${height}`" :class="{ 'editor-fullscreen': fullscreen }" />
</template>

<script>
import * as monaco from 'monaco-editor/esm/vs/editor/editor.main.js'
import { language } from 'monaco-editor/esm/vs/basic-languages/mysql/mysql.js'

export default {
  props: {
    value: {
      type: String,
      default: ''
    },
    language: {
      type: String,
      default: 'mysql'
    },
    options: {
      type: Object,
      default: () => {
      }
    },
    height: {
      type: String,
      default: '100%'
    },
    width: {
      type: String,
      default: '100%'
    },
    fullscreen: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      editor: null,
      websocket: null
    }
  },
  computed: {
    codeEditorConfig() {
      const options = {
        value: this.value,
        language: this.language,
        fontSize: 14,
        readOnly: false,
        theme: 'vs-dark',
        automaticLayout: true,
        minimap: {
          enabled: true
        },
        ...this.options
      }
      if (!options.model) {
        delete options.model
      }
      return options
    }
  },
  watch: {
    value(newValue) {
      if (newValue !== this.getCodeEditorValue()) this.setCodeEditorValue(newValue)
    }
  },
  mounted() {
    this.initCodeEditor()
    this.$nextTick(() => {
      this.initCodeChange()
    })
  },
  beforeDestroy() {
    monaco.editor.getModels().forEach(model => model.dispose())
  },
  methods: {
    initCodeEditor() {
      monaco.languages.registerCompletionItemProvider('mysql', {
        provideCompletionItems: function() {
          const suggestions = []
          language.keywords.forEach(item => {
            suggestions.push({
              label: item,
              kind: monaco.languages.CompletionItemKind.Keyword,
              insertText: item
            })
          })
          language.operators.forEach(item => {
            suggestions.push({
              label: item,
              kind: monaco.languages.CompletionItemKind.Operator,
              insertText: item
            })
          })
          language.builtinFunctions.forEach(item => {
            suggestions.push({
              label: item,
              kind: monaco.languages.CompletionItemKind.Function,
              insertText: item
            })
          })
          return {
            suggestions: [
              ...suggestions
            ]
          }
        }
      })
      this.editor = monaco.editor.create(this.$refs.SQLEditor, this.codeEditorConfig)

      this.editor.onDidChangeModelContent(() => {
        this.$emit('input', this.getCodeEditorValue())
      })
    },
    initCodeChange() {
      this.editor.onDidChangeModelContent(() => {
        const value = this.getCodeEditorValue()
        this.$emit('change', value)
      })
    },
    getCodeEditorValue() {
      return this.editor.getValue()
    },
    setCodeEditorValue(val) {
      this.editor.setValue(val)
    }
  }
}
</script>

<style lang="scss" scoped>
.editor-fullscreen {
  position: fixed;
  top: 40px;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 9999;
}
</style>
