<template>
  <div ref="javaEditor" :style="`width: ${width}; height: ${height}`" :class="{ 'editor-fullscreen': fullscreen }" />
</template>

<script>
import { listen } from '@codingame/monaco-jsonrpc'
import * as monaco from 'monaco-editor/esm/vs/editor/editor.main.js'
import { LANGUAGE_SERVER_WS_URL } from '@/contains'

const {
  MonacoLanguageClient,
  CloseAction,
  ErrorAction,
  MonacoServices,
  createConnection
} = require('monaco-languageclient')
export default {
  props: {
    value: {
      type: String,
      default: ''
    },
    language: {
      type: String,
      default: 'java'
    },
    options: {
      type: Object,
      default: () => {
      }
    },
    height: {
      type: String,
      default: '100%'
    },
    width: {
      type: String,
      default: '100%'
    },
    fullscreen: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      editor: null,
      websocket: null,
      connected: false
    }
  },
  computed: {
    codeEditorConfig() {
      const options = {
        value: this.value,
        language: this.language,
        model: this.getModel(),
        fontSize: 14,
        readOnly: false,
        theme: 'vs-dark',
        automaticLayout: true,
        minimap: {
          enabled: true
        },
        ...this.options
      }
      if (!options.model) {
        delete options.model
      }
      return options
    }
  },
  watch: {
    value(newValue) {
      if (newValue !== this.getCodeEditorValue()) this.setCodeEditorValue(newValue)
    }
  },
  mounted() {
    this.initCodeEditor()
    this.$nextTick(() => {
      this.initCodeChange()
    })
  },
  beforeDestroy() {
    this.websocket.close()
    monaco.editor.getModels().forEach(model => model.dispose())
  },
  methods: {
    getModel() {
      monaco.editor.getModels().forEach(model => model.dispose())
      return monaco.editor.createModel(this.value, this.language, monaco.Uri.parse('file:///home/<USER>/java/src/Main.java'))
    },
    initCodeEditor() {
      this.editor = monaco.editor.create(this.$refs.javaEditor, this.codeEditorConfig)

      this.connectLanguageServer()

      MonacoServices.install(monaco)

      // 双向绑定
      this.editor.onDidChangeModelContent(() => {
        this.$emit('input', this.getCodeEditorValue())
      })
    },
    createLanguageClient(connection) {
      return new MonacoLanguageClient({
        name: 'LSP client',
        clientOptions: {
          documentSelector: [this.language],
          errorHandler: {
            error: () => ErrorAction.Continue,
            closed: () => CloseAction.DoNotRestart
          }
        },
        connectionProvider: {
          get: (errorHandler, closeHandler) => {
            return Promise.resolve(createConnection(connection, errorHandler, closeHandler))
          }
        }
      })
    },
    initCodeChange() {
      this.editor.onDidChangeModelContent(() => {
        const value = this.getCodeEditorValue()
        this.$emit('change', value)
      })
    },
    getCodeEditorValue() {
      return this.editor.getValue()
    },
    setCodeEditorValue(val) {
      this.editor.setValue(val)
    },
    connectLanguageServer() {
      const self = this
      this.websocket = new WebSocket(LANGUAGE_SERVER_WS_URL + this.language)
      listen({
        webSocket: self.websocket,
        onConnection: connection => {
          const client = self.createLanguageClient(connection)
          const disposable = client.start()
          connection.onClose(() => disposable.dispose())
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.editor-fullscreen {
  position: fixed;
  top: 40px;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 9999;
}
</style>
