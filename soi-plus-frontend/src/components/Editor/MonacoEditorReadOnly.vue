<template>
  <div ref="MonacoEditorReadOnly" :style="`width: ${width}; height: ${height}`" />
</template>

<script>
import * as monaco from 'monaco-editor/esm/vs/editor/editor.main.js'

export default {
  name: 'MonacoEditorReadOnly',
  props: {
    value: {
      type: String,
      default: ''
    },
    language: {
      type: String,
      required: true
    },
    options: {
      type: Object,
      default: () => {
      }
    },
    height: {
      type: String,
      default: '100%'
    },
    width: {
      type: String,
      default: '100%'
    }
  },
  data() {
    return {
      editor: null,
      websocket: null
    }
  },
  computed: {
    codeEditorConfig() {
      return {
        value: this.value,
        language: this.language,
        fontSize: 14,
        readOnly: false,
        theme: 'vs-dark',
        automaticLayout: true,
        minimap: {
          enabled: true
        },
        ...this.options
      }
    }
  },
  watch: {
    value(newValue) {
      if (newValue !== this.getCodeEditorValue()) this.setCodeEditorValue(newValue)
    }
  },
  mounted() {
    this.initCodeEditor()
    this.$nextTick(() => {
      this.initCodeChange()
    })
  },
  beforeDestroy() {
    this.editor.getModels().forEach(model => model.dispose())
  },
  methods: {
    initCodeEditor() {
      this.editor = monaco.editor.create(this.$refs.MonacoEditorReadOnly, this.codeEditorConfig)

      this.editor.onDidChangeModelContent(() => {
        this.$emit('input', this.getCodeEditorValue())
      })
    },
    initCodeChange() {
      this.editor.onDidChangeModelContent(() => {
        const value = this.getCodeEditorValue()
        this.$emit('change', value)
      })
    },
    getCodeEditorValue() {
      return this.editor.getValue()
    },
    setCodeEditorValue(val) {
      this.editor.setValue(val)
    }
  }
}
</script>
