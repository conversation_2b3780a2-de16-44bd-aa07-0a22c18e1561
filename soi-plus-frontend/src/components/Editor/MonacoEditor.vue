<template>
  <div class="monaco-editor-container">
    <JavaEditor v-if="language === 'java'" v-model="code" :width="width" :height="height" :options="options" />
    <PythonEditor v-if="language === 'python'" v-model="code" :width="width" :height="height" :options="options" />
    <HTMLEditor v-if="language === 'html' || language === 'web'" v-model="code" :width="width" :height="height" :options="options" />
    <MySQLEditor v-if="language === 'mysql'" v-model="code" :width="width" :height="height" :options="options" />
    <OracleEditor v-if="language === 'oracle'" v-model="code" :width="width" :height="height" :options="options" />
  </div>
</template>

<script>
import JavaEditor from '@/components/Editor/JavaEditor.vue'
import PythonEditor from '@/components/Editor/PythonEditor.vue'
import HTMLEditor from '@/components/Editor/HTMLEditor.vue'
import MySQLEditor from '@/components/Editor/MySQLEditor.vue'
import OracleEditor from '@/components/Editor/OracleEditor.vue'

export default {
  name: 'MonacoEditor',
  components: {
    HTMLEditor,
    PythonEditor,
    JavaEditor,
    MySQLEditor,
    OracleEditor
  },
  props: {
    value: {
      type: String,
      default: ''
    },
    language: {
      type: String,
      default: ''
    },
    options: {
      type: Object,
      default: () => {
      }
    },
    height: {
      type: String,
      default: '100%'
    },
    width: {
      type: String,
      default: '100%'
    },
    fullscreen: {
      type: Boolean,
      default: false
    }
  },
  computed: {
    code: {
      get: function() {
        return this.value
      },
      set: function(newValue) {
        this.$emit('input', newValue)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.monaco-editor-container {
  height: 100%;
}
</style>
