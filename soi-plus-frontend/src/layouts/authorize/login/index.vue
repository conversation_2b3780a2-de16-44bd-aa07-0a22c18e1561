<template>
  <div class="authorize-container">
    <div class="header">
      <img :src="logo" class="logo" alt="logo" @click="officialWebsite()">
      <h1>{{ $t('soi.common.platformName') }}</h1>
    </div>

    <el-row class="content">
      <el-col :span="10" :xs="24" class="content-left">
        <img class="authorize-image" src="../../../assets/images/authorize.jpg" alt="">
        <div class="by" style="cursor:pointer;float: right;margin-top: 10px" @click="officialWebsite()">
          {{ $t('soi.common.copyright') }}
        </div>
      </el-col>
      <el-col :span="14" :xs="24" class="content-right">
        <div class="content-container">
          <router-view />
        </div>
      </el-col>

    </el-row>
    <div class="footer">
      <el-link :href="getOfficialWebsiteHome()" target="_blank" class="footer-link">@2024 SIMNECTZ Inc.</el-link>
      <el-link href="https://beian.miit.gov.cn/" target="_blank" class="footer-link">陕ICP备20000279号-2</el-link>
    </div>
  </div>
</template>

<script>
import { OFFICIAL_WEBSITE_URL } from '@/contains'

export default {
  name: 'AuthorizeLayout',
  data() {
    return {
      logo: require(`@/assets/images/logo-${process.env.VUE_APP_TEMPORARY_PERMISSION}.png`)
    }
  },
  methods: {
    officialWebsite() {
      window.open(OFFICIAL_WEBSITE_URL, '_self')
    },
    getOfficialWebsiteHome() {
      return OFFICIAL_WEBSITE_URL
    }
  }
}
</script>

<style lang="scss" scoped>
$bg: #fff;
$width: 1100px;

.authorize-container {
  width: 100%;
  height: 100vh;
  background: $bg url("../../../assets/images/background.jpg") no-repeat center;
  background-size: cover;

  .header {
    padding-top:50px;
    width: $width;
    margin: 0 auto 20px;
    display: flex;
    align-items: center;

    .logo {
      width: 200px;
      height: auto;
    }

    img {
      cursor:pointer;
    }

    h1 {
      display: inline-block;
      font-size: 32px;
      font-weight: 400;
    }
  }

  .content {
    width: $width;
    margin: 0 auto;
    padding-bottom: 50px;

    .content-left {
      font-size: 16px;
      font-family: "Open Sans", sans-serif;
      text-align: center;
      margin-bottom: 40px;

      .authorize-image {
        text-align: center;
        width: 95%;
        border: 1px solid #999;
        border-radius: 10px;
        box-shadow: 10px 10px 10px #999;
        margin-bottom: 10px;
      }
    }
  }

  .footer {
    margin-top: 100px;
    text-align: center;
  }

  .footer-link {
    font-size: 16px;
  }
}
</style>
