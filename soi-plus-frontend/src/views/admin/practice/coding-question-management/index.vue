<template>
  <div v-loading.fullscreen.lock="loading" class="practice-management-coding-question-management-container">
    <customize-card :title="$t('soi.router.codingQuestionManagement')" :show-back-btn="false">
      <!--   Search from   -->
      <el-form ref="searchForm" :model="pageRequest" size="small" inline>
        <el-form-item :label="$t('soi.codingPractice.questionTitleSearch')" prop="key">
          <el-input v-model="pageRequest.key" :placeholder="$t('soi.codingPractice.questionTitleTipSearch')" />
        </el-form-item>
        <el-form-item :label="$t('soi.codingPractice.questionCategory')" prop="category">
          <el-select
            v-model="pageRequest.category"
            :placeholder="$t('soi.codingPractice.questionCategory')"
            clearable
          >
            <el-option-group
              v-for="group in groupOptions"
              :key="group.label"
              :label="['zh', 'cht'].includes(language) ? group.label : group.labelEn"
            >
              <el-option
                v-for="item in group.options"
                :key="item.value"
                :label="['zh', 'cht'].includes(language) ? item.label : item.labelEn"
                :value="item.value"
              />
            </el-option-group>
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('soi.codingPractice.questionStatus')" prop="status">
          <el-select
            v-model="pageRequest.status"
            :placeholder="$t('soi.codingPractice.questionStatus')"
            clearable
          >
            <el-option :value="1" label="Enable" />
            <el-option :value="0" label="Disable" />
          </el-select>
        </el-form-item>

        <!-- 操作按钮 -->
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="paginationQueryCodingQuestions()">{{ $t('soi.common.search') }}</el-button>
          <el-button icon="el-icon-refresh" @click="resetForm('searchForm')">{{ $t('soi.common.reset') }}</el-button>
        </el-form-item>
      </el-form>

      <!-- 创建和批量删除按钮 -->
      <div class="data-action-button-group">
        <div>
          <el-button size="small" icon="el-icon-plus" type="primary" plain @click="openAddQuestionDialog">{{ $t('soi.common.create') }}</el-button>
          <!--          <el-button size="small" icon="el-icon-delete" type="danger" plain :disabled="!multipleSelection.length" @click="batchDeleteUser()">{{ $t('soi.common.delete') }}</el-button>-->
        </div>

        <el-button icon="el-icon-refresh" size="small" circle @click="paginationQueryCodingQuestions()" />
      </div>

      <!--   Data table   -->
      <el-card shadow="never">
        <el-table
          :data="questions"
          :default-sort="{prop: 'createTime', order: 'descending'}"
          style="width: 100%"
          stripe
          highlight-current-row
          @sort-change="handleSortChange"
        >
          <el-table-column :label="$t('soi.codingPractice.serialNumber')" type="index" width="50" />
          <el-table-column
            :show-overflow-tooltip="true"
            :label="$t('soi.codingPractice.questionTitleTable')"
            prop="questionTitle"
            width="200"
          >
            <template slot-scope="scope">
              {{ ['zh', 'cht'].includes(language) ? scope.row.questionTitle : scope.row.questionTitleEn }}
            </template>
          </el-table-column>
          <el-table-column
            :show-overflow-tooltip="true"
            :label="$t('soi.codingPractice.questionDescriptionTable')"
            prop="description"
          >
            <template slot-scope="scope">
              {{ ['zh', 'cht'].includes(language) ? scope.row.description : scope.row.descriptionEn }}
            </template>
          </el-table-column>
          <el-table-column :label="$t('soi.codingPractice.questionSubcategoryTable')" width="120" align="center">
            <template slot-scope="scope">
              {{ ['zh', 'cht'].includes(language) ? scope.row.questionSubcategory : scope.row.questionSubcategoryEn }}
            </template>
          </el-table-column>
          <el-table-column :label="$t('soi.common.createTime')" prop="createTime" width="150" sortable="custom" align="center" />
          <el-table-column :label="$t('soi.common.updateTime')" prop="lastUpdateTime" width="150" sortable="custom" align="center" />
          <el-table-column :label="$t('soi.common.operate')" width="160">
            <template slot-scope="scope">
              <el-button size="mini" @click="openEditQuestionDialog(scope.row)">
                {{ $t('soi.common.edit') }}
              </el-button>
              <el-button size="mini" @click="deleteCodingQuestion(scope.row.id)">
                {{ $t('soi.common.delete') }}
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <!--   Pagination tools   -->
        <div class="table-footer">
          <el-pagination
            :current-page.sync="pageRequest.page"
            :page-sizes="[10, 15, 20, 30, 50, 100]"
            :page-size.sync="pageRequest.rows"
            :total="total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </el-card>
      <!-- Add Coding question dialog -->
      <el-dialog
        :append-to-body="true"
        :visible.sync="addQuestionDialog"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        :title="$t('soi.codingPractice.addCodingQuestion')"
        top="5vh"
        width="70%"
        @close="resetForm('addCodingForm')"
      >
        <el-scrollbar style="height: 670px">
          <el-row type="flex" justify="space-around">
            <el-col :span="18">
              <el-form
                ref="addCodingForm"
                :model="addCodingForm"
                :rules="addCodingRules"
                :status-icon="true"
                label-width="230px"
              >
                <el-form-item :label="$t('soi.codingPractice.questionPrimaryCategory')" prop="questionCategory">
                  <el-select
                    v-model="addCodingForm.questionCategory"
                    :placeholder="$t('soi.codingPractice.questionCategoryTip')"
                    allow-create
                    filterable
                    default-first-option
                    style="width: 100%;"
                    @change="loadSubcategory($event, true); changeQuestionCategoryZh($event)"
                  >
                    <el-option
                      v-for="(item, index) in categoryOptions"
                      :key="index"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
                <el-form-item :label="$t('soi.codingPractice.questionPrimaryCategoryEn')" prop="questionCategoryEn">
                  <el-select
                    v-model="addCodingForm.questionCategoryEn"
                    :placeholder="$t('soi.codingPractice.questionCategoryTipEn')"
                    allow-create
                    filterable
                    default-first-option
                    style="width: 100%;"
                    @change="loadSubcategory($event, true); changeQuestionCategoryEn($event)"
                  >
                    <el-option
                      v-for="(item, index) in categoryOptionsEn"
                      :key="index"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
                <el-form-item :label="$t('soi.codingPractice.questionSubcategory')" prop="questionSubcategory">
                  <el-select
                    v-model="addCodingForm.questionSubcategory"
                    :placeholder="$t('soi.codingPractice.questionSubcategoryTip')"
                    style="width: 100%;"
                    allow-create
                    filterable
                    default-first-option
                    @change="changeQuestionSubcategoryZh($event)"
                  >
                    <el-option
                      v-for="(item, index) in subcategoryOptions"
                      :key="index"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
                <el-form-item :label="$t('soi.codingPractice.questionSubcategoryEn')" prop="questionSubcategoryEn">
                  <el-select
                    v-model="addCodingForm.questionSubcategoryEn"
                    :placeholder="$t('soi.codingPractice.questionSubcategoryTipEn')"
                    style="width: 100%;"
                    allow-create
                    filterable
                    default-first-option
                    @change="changeQuestionSubcategoryEn($event)"
                  >
                    <el-option
                      v-for="(item, index) in subcategoryOptionsEn"
                      :key="index"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
                <el-form-item :label="$t('soi.codingPractice.questionTitle')" prop="questionTitle">
                  <el-input
                    v-model="addCodingForm.questionTitle"
                    :placeholder="$t('soi.codingPractice.questionTitleTip')"
                    maxlength="300"
                    clearable
                  />
                </el-form-item>
                <el-form-item :label="$t('soi.codingPractice.questionTitleEn')" prop="questionTitleEn">
                  <el-input
                    v-model="addCodingForm.questionTitleEn"
                    :placeholder="$t('soi.codingPractice.questionTitleTipEn')"
                    maxlength="600"
                    clearable
                  />
                </el-form-item>
                <el-form-item :label="$t('soi.codingPractice.questionDescription')" prop="description">
                  <el-input
                    v-model="addCodingForm.description"
                    :placeholder="$t('soi.codingPractice.questionDescriptionTip')"
                    type="textarea"
                    rows="6"
                    maxlength="10000"
                    clearable
                  />
                </el-form-item>
                <el-form-item :label="$t('soi.codingPractice.questionDescriptionEn')" prop="descriptionEn">
                  <el-input
                    v-model="addCodingForm.descriptionEn"
                    :placeholder="$t('soi.codingPractice.questionDescriptionTipEn')"
                    type="textarea"
                    rows="6"
                    maxlength="10000"
                    clearable
                  />
                </el-form-item>
                <el-form-item :label="$t('soi.codingPractice.target')" prop="target">
                  <el-input
                    v-model="addCodingForm.target"
                    :placeholder="$t('soi.codingPractice.targetTip')"
                    type="textarea"
                    rows="6"
                    maxlength="10000"
                    clearable
                  />
                </el-form-item>
                <el-form-item :label="$t('soi.codingPractice.targetEn')" prop="targetEn">
                  <el-input
                    v-model="addCodingForm.targetEn"
                    :placeholder="$t('soi.codingPractice.targetTipEn')"
                    type="textarea"
                    rows="6"
                    maxlength="10000"
                    clearable
                  />
                </el-form-item>
                <el-form-item :label="$t('soi.codingPractice.trainingPurpose')" prop="trainingPurpose">
                  <el-input
                    v-model="addCodingForm.trainingPurpose"
                    :placeholder="$t('soi.codingPractice.trainingPurposeTip')"
                    type="textarea"
                    rows="6"
                    maxlength="10000"
                    clearable
                  />
                </el-form-item>
                <el-form-item :label="$t('soi.codingPractice.trainingPurposeEn')" prop="trainingPurposeEn">
                  <el-input
                    v-model="addCodingForm.trainingPurposeEn"
                    :placeholder="$t('soi.codingPractice.trainingPurposeTipEn')"
                    type="textarea"
                    rows="6"
                    maxlength="10000"
                    clearable
                  />
                </el-form-item>
                <el-form-item :label="$t('soi.codingPractice.apiInformation')" prop="apiInfo">
                  <el-input
                    v-model="addCodingForm.apiInfo"
                    :placeholder="$t('soi.codingPractice.apiInformationTip')"
                    type="textarea"
                    rows="6"
                    maxlength="10000"
                    clearable
                  />
                </el-form-item>
                <el-form-item :label="$t('soi.codingPractice.apiInformationEn')" prop="apiInfoEn">
                  <el-input
                    v-model="addCodingForm.apiInfoEn"
                    :placeholder="$t('soi.codingPractice.apiInformationTipEn')"
                    type="textarea"
                    rows="6"
                    maxlength="10000"
                    clearable
                  />
                </el-form-item>
                <el-form-item :label="$t('soi.codingPractice.databaseInformation')" prop="databaseInfo">
                  <el-input
                    v-model="addCodingForm.databaseInfo"
                    :placeholder="$t('soi.codingPractice.databaseInformationTip')"
                    type="textarea"
                    rows="6"
                    maxlength="10000"
                    clearable
                  />
                </el-form-item>
                <el-form-item :label="$t('soi.codingPractice.databaseInformationEn')" prop="databaseInfoEn">
                  <el-input
                    v-model="addCodingForm.databaseInfoEn"
                    :placeholder="$t('soi.codingPractice.databaseInformationTipEn')"
                    type="textarea"
                    rows="6"
                    maxlength="10000"
                    clearable
                  />
                </el-form-item>
                <el-form-item :label="$t('soi.codingPractice.supportedLanguages')" prop="supportedLanguages">
                  <el-checkbox-group v-model="addCodingForm.supportedLanguages">
                    <el-checkbox
                      v-for="(item, index) in languagesOptions"
                      :key="index"
                      :label="item.value"
                      name="supportedLanguages"
                    >
                      {{ item.label }}
                    </el-checkbox>
                  </el-checkbox-group>
                </el-form-item>
                <div
                  v-for="(item, index) in addCodingForm.supportedLanguages"
                  :key="index"
                >
                  <el-form-item
                    :label="`${$t('soi.codingPractice.initialCode')} - ${item}: `"
                    :prop="item"
                  >
                    <el-input
                      v-model="addCodingForm[item]"
                      :placeholder="`${$t('soi.codingPractice.initialCodeTip')} - ${item}`"
                      type="textarea"
                      rows="6"
                      maxlength="10000"
                      clearable
                    />
                  </el-form-item>
                  <el-form-item
                    :label="`${$t('soi.codingPractice.codeDescription')} - ${item}: `"
                    :prop="'cd_' + item"
                  >
                    <el-input
                      v-model="addCodingForm['cd_' + item]"
                      :placeholder="`${$t('soi.codingPractice.codeDescriptionTip')} - ${item}`"
                      type="textarea"
                      rows="6"
                      maxlength="10000"
                      clearable
                    />
                  </el-form-item>
                  <el-form-item
                    :label="`${$t('soi.codingPractice.codeDescriptionEn')} - ${item}: `"
                    :prop="'cd_en_' + item"
                  >
                    <el-input
                      v-model="addCodingForm['cd_en_' + item]"
                      :placeholder="`${$t('soi.codingPractice.codeDescriptionTipEn')} - ${item}`"
                      type="textarea"
                      rows="6"
                      maxlength="10000"
                      clearable
                    />
                  </el-form-item>
                  <el-form-item
                    :label="`${$t('soi.codingPractice.modelAnswer')} - ${item}: `"
                    :prop="'ma_' + item"
                  >
                    <el-input
                      v-model="addCodingForm['ma_' + item]"
                      :placeholder="`${$t('soi.codingPractice.modelAnswerTip')} - ${item}`"
                      type="textarea"
                      rows="6"
                      clearable
                    />
                  </el-form-item>
                </div>
              </el-form>
            </el-col>
          </el-row>
        </el-scrollbar>
        <span slot="footer" class="dialog-footer">
          <el-button size="small" @click="addQuestionDialog = false">{{ $t('soi.common.cancel') }}</el-button>
          <el-button type="primary" size="small" @click="addQuestion('addCodingForm')">{{ $t('soi.common.confirm') }}</el-button>
        </span>
      </el-dialog>

      <!--    Edit Coding question dialog   -->
      <el-dialog
        :append-to-body="true"
        :visible.sync="editQuestionDialog"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        :title="$t('soi.codingPractice.editCodingQuestion')"
        top="5vh"
        width="70%"
        @close="resetForm('editCodingForm')"
      >
        <el-scrollbar style="height: 670px">
          <el-row type="flex" justify="space-around">
            <el-col :span="18">
              <el-form
                ref="editCodingForm"
                :model="editCodingForm"
                :rules="editCodingRules"
                :status-icon="true"
                label-width="230px"
              >
                <el-form-item :label="$t('soi.codingPractice.questionPrimaryCategory')" prop="questionCategory">
                  <el-select
                    v-model="editCodingForm.questionCategory"
                    :placeholder="$t('soi.codingPractice.questionCategoryTip')"
                    allow-create
                    filterable
                    default-first-option
                    style="width: 100%;"
                    @change="loadSubcategory($event, true); changeUpdateQuestionCategoryZh($event)"
                  >
                    <el-option
                      v-for="(item, index) in categoryOptions"
                      :key="index"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
                <el-form-item :label="$t('soi.codingPractice.questionPrimaryCategoryEn')" prop="questionCategoryEn">
                  <el-select
                    v-model="editCodingForm.questionCategoryEn"
                    :placeholder="$t('soi.codingPractice.questionCategoryTipEn')"
                    allow-create
                    filterable
                    default-first-option
                    style="width: 100%;"
                    @change="loadSubcategory($event, true); changeUpdateQuestionCategoryEn($event)"
                  >
                    <el-option
                      v-for="(item, index) in categoryOptionsEn"
                      :key="index"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
                <el-form-item :label="$t('soi.codingPractice.questionSubcategory')" prop="questionSubcategory">
                  <el-select
                    v-model="editCodingForm.questionSubcategory"
                    :placeholder="$t('soi.codingPractice.questionSubcategoryTip')"
                    allow-create
                    filterable
                    style="width: 100%;"
                    default-first-option
                    @change="changeUpdateQuestionSubcategoryZh($event)"
                  >
                    <el-option
                      v-for="(item, index) in subcategoryOptions"
                      :key="index"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
                <el-form-item :label="$t('soi.codingPractice.questionSubcategoryEn')" prop="questionSubcategoryEn">
                  <el-select
                    v-model="editCodingForm.questionSubcategoryEn"
                    :placeholder="$t('soi.codingPractice.questionSubcategoryTipEn')"
                    allow-create
                    filterable
                    style="width: 100%;"
                    default-first-option
                    @change="changeUpdateQuestionSubcategoryEn($event)"
                  >
                    <el-option
                      v-for="(item, index) in subcategoryOptionsEn"
                      :key="index"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
                <el-form-item :label="$t('soi.codingPractice.questionTitle')" prop="questionTitle">
                  <el-input
                    v-model="editCodingForm.questionTitle"
                    :placeholder="$t('soi.codingPractice.questionTitleTip')"
                    maxlength="300"
                    clearable
                  />
                </el-form-item>
                <el-form-item :label="$t('soi.codingPractice.questionTitleEn')" prop="questionTitleEn">
                  <el-input
                    v-model="editCodingForm.questionTitleEn"
                    :placeholder="$t('soi.codingPractice.questionTitleTipEn')"
                    maxlength="600"
                    clearable
                  />
                </el-form-item>
                <el-form-item :label="$t('soi.codingPractice.questionDescription')" prop="description">
                  <el-input
                    v-model="editCodingForm.description"
                    :placeholder="$t('soi.codingPractice.questionDescriptionTip')"
                    type="textarea"
                    rows="6"
                    maxlength="10000"
                    clearable
                  />
                </el-form-item>
                <el-form-item :label="$t('soi.codingPractice.questionDescriptionEn')" prop="descriptionEn">
                  <el-input
                    v-model="editCodingForm.descriptionEn"
                    :placeholder="$t('soi.codingPractice.questionDescriptionTipEn')"
                    type="textarea"
                    rows="6"
                    maxlength="10000"
                    clearable
                  />
                </el-form-item>
                <el-form-item :label="$t('soi.codingPractice.target')" prop="target">
                  <el-input
                    v-model="editCodingForm.target"
                    :placeholder="$t('soi.codingPractice.targetTip')"
                    type="textarea"
                    rows="6"
                    maxlength="10000"
                    clearable
                  />
                </el-form-item>
                <el-form-item :label="$t('soi.codingPractice.targetEn')" prop="targetEn">
                  <el-input
                    v-model="editCodingForm.targetEn"
                    :placeholder="$t('soi.codingPractice.targetTipEn')"
                    type="textarea"
                    rows="6"
                    maxlength="10000"
                    clearable
                  />
                </el-form-item>
                <el-form-item :label="$t('soi.codingPractice.trainingPurpose')" prop="trainingPurpose">
                  <el-input
                    v-model="editCodingForm.trainingPurpose"
                    :placeholder="$t('soi.codingPractice.trainingPurposeTip')"
                    type="textarea"
                    rows="6"
                    maxlength="10000"
                    clearable
                  />
                </el-form-item>
                <el-form-item :label="$t('soi.codingPractice.trainingPurposeEn')" prop="trainingPurposeEn">
                  <el-input
                    v-model="editCodingForm.trainingPurposeEn"
                    :placeholder="$t('soi.codingPractice.trainingPurposeTipEn')"
                    type="textarea"
                    rows="6"
                    maxlength="10000"
                    clearable
                  />
                </el-form-item>
                <el-form-item :label="$t('soi.codingPractice.apiInformation')" prop="apiInfo">
                  <el-input
                    v-model="editCodingForm.apiInfo"
                    :placeholder="$t('soi.codingPractice.apiInformationTip')"
                    type="textarea"
                    rows="6"
                    maxlength="10000"
                    clearable
                  />
                </el-form-item>
                <el-form-item :label="$t('soi.codingPractice.apiInformationEn')" prop="apiInfoEn">
                  <el-input
                    v-model="editCodingForm.apiInfoEn"
                    :placeholder="$t('soi.codingPractice.apiInformationTipEn')"
                    type="textarea"
                    rows="6"
                    maxlength="10000"
                    clearable
                  />
                </el-form-item>
                <el-form-item :label="$t('soi.codingPractice.databaseInformation')" prop="databaseInfo">
                  <el-input
                    v-model="editCodingForm.databaseInfo"
                    :placeholder="$t('soi.codingPractice.databaseInformationTip')"
                    type="textarea"
                    rows="6"
                    maxlength="10000"
                    clearable
                  />
                </el-form-item>
                <el-form-item :label="$t('soi.codingPractice.databaseInformationEn')" prop="databaseInfoEn">
                  <el-input
                    v-model="editCodingForm.databaseInfoEn"
                    :placeholder="$t('soi.codingPractice.databaseInformationTipEn')"
                    type="textarea"
                    rows="6"
                    maxlength="10000"
                    clearable
                  />
                </el-form-item>
                <el-form-item :label="$t('soi.codingPractice.supportedLanguages')" prop="supportedLanguages">
                  <el-checkbox-group v-model="editCodingForm.supportedLanguages" placeholder="Coding question category">
                    <el-checkbox
                      v-for="(item, index) in languagesOptions"
                      :key="index"
                      :label="item.value"
                      name="supportedLanguages"
                    >
                      {{ item.label }}
                    </el-checkbox>
                  </el-checkbox-group>
                </el-form-item>
                <div v-for="(item) in editCodingForm.supportedLanguages" :key="item.id">
                  <el-form-item
                    :label="`${$t('soi.codingPractice.initialCode')} - ${item}: `"
                    :prop="item"
                  >
                    <el-input
                      v-model="editCodingForm[item]"
                      :placeholder="`${$t('soi.codingPractice.initialCodeTip')} - ${item}`"
                      type="textarea"
                      rows="6"
                      maxlength="10000"
                      clearable
                    />
                  </el-form-item>
                  <el-form-item
                    :label="`${$t('soi.codingPractice.codeDescription')} - ${item}: `"
                    :prop="'cd_' + item"
                  >
                    <el-input
                      v-model="editCodingForm['cd_' + item]"
                      :placeholder="`${$t('soi.codingPractice.codeDescriptionTip')} - ${item}`"
                      type="textarea"
                      rows="6"
                      clearable
                    />
                  </el-form-item>
                  <el-form-item
                    :label="`${$t('soi.codingPractice.codeDescriptionEn')} - ${item}: `"
                    :prop="'cd_en_' + item"
                  >
                    <el-input
                      v-model="editCodingForm['cd_en_' + item]"
                      :placeholder="`${$t('soi.codingPractice.codeDescriptionTipEn')} - ${item}`"
                      type="textarea"
                      rows="6"
                      clearable
                    />
                  </el-form-item>
                  <el-form-item
                    :label="`${$t('soi.codingPractice.modelAnswer')} - ${item}: `"
                    :prop="'ma_' + item"
                  >
                    <el-input
                      v-model="editCodingForm['ma_' + item]"
                      :placeholder="`${$t('soi.codingPractice.modelAnswerTip')} - ${item}`"
                      type="textarea"
                      rows="6"
                      clearable
                    />
                  </el-form-item>
                </div>
              </el-form>
            </el-col>
          </el-row>
        </el-scrollbar>
        <span slot="footer" class="dialog-footer">
          <el-button size="small" @click="editQuestionDialog = false">{{ $t('soi.common.cancel') }}</el-button>
          <el-button size="small" type="primary" @click="editQuestion('editCodingForm')">
            {{ $t('soi.common.confirm') }}
          </el-button>
        </span>
      </el-dialog>
    </customize-card>
  </div>
</template>
<script>
import CustomizeCard from '@/components/CustomizeCard/index.vue'
import {
  createQuestion, deleteQuestion,
  getCategory,
  getQuestionByPage,
  getSubcategory,
  updateQuestion
} from '@/api/practice/coding-practice'
import { mapGetters } from 'vuex'

export default {
  name: 'PracticeManagementCodingQuestionManagement',
  components: { CustomizeCard },
  data() {
    return {
      loading: false,
      questions: [], // Coding questions shown in the table.
      pageRequest: { // Paging query params.
        key: '', // Keywords(coding question title) for fuzzy search [not required].
        category: '', // Coding question category [not required].
        status: '', // Coding question status(0, 1 or null) [not required] <null: all, 0: disable, 1: enable>
        page: 1, // Current page number, default: 1 [required].
        rows: 15, // Display size per page, default: 10 [required].
        sortBy: 'createTime', // Sort field, default: createTime [not required].
        desc: true // Whether to sort in descending order, default: true [required].
      },
      addQuestionDialog: false, // Control the display of the add coding question dialog
      editQuestionDialog: false, // Control the display of the edit coding question dialog
      total: 0, // Coding questions total
      categoryOptions: [ // Coding question optional primary category
      ],
      categoryOptionsEn: [ // Coding question optional primary category
      ],
      subcategoryOptions: [ // Coding question optional subcategory
      ],
      subcategoryOptionsEn: [ // Coding question optional subcategory
      ],
      addCodingForm: { // Create coding question request params.
        creator: '', // Coding question creator(user id).
        questionTitle: '', // Coding question title.
        questionTitleEn: '', // Coding question title(en).
        questionCategory: '', // Coding question category.
        questionCategoryEn: '', // Coding question category(en).
        description: '', // Coding question description information.
        descriptionEn: '', // Coding question description information(en).
        modelAnswer: '', // Coding question model answer.
        code: '', // Initial code Json string in different languages
        supportedLanguages: [], // Coding question supported languages
        questionSubcategory: '', // Coding question subcategory.
        questionSubcategoryEn: '', // Coding question subcategory(en).
        apiInfo: '', // API description information
        apiInfoEn: '', // API description information(en).
        codeDescription: '', // Code description information
        codeDescriptionEn: '', // Code description information(en).
        target: '',
        targetEn: '',
        databaseInfo: '',
        databaseInfoEn: '',
        trainingPurpose: '',
        trainingPurposeEn: ''
      },
      editCodingForm: { // Edit coding question request params.
        id: '', // Coding question id.
        questionTitle: '', // Coding question title.
        questionTitleEn: '', // Coding question title(en).
        questionCategory: '', // Coding question category.
        questionCategoryEn: '', // Coding question category(en).
        description: '', // Coding question description information.
        descriptionEn: '', // Coding question description information(en).
        modelAnswer: '', // Coding question model answer.
        code: '', // Initial code Json string in different languages
        supportedLanguages: [], // Coding question supported languages
        questionSubcategory: '', // Coding question subcategory.
        questionSubcategoryEn: '', // Coding question subcategory(en).
        apiInfo: '', // API description information
        apiInfoEn: '', // API description information(en).
        codeDescription: '', // Code description information
        codeDescriptionEn: '', // Code description information(en).
        target: '',
        targetEn: '',
        databaseInfo: '',
        databaseInfoEn: '',
        trainingPurpose: '',
        trainingPurposeEn: ''
      },
      addCodingRules: { // Add coding question field validation rules
      },
      editCodingRules: { // Edit coding question field validation rules
      },
      languagesOptions: [ // Language options supported by programming questions
        { label: 'Java 8', value: 'java' },
        { label: 'C++', value: 'cpp' },
        { label: 'C', value: 'c' },
        { label: 'Python 3.7', value: 'python' },
        { label: 'JavaScript', value: 'javascript' },
        { label: 'Web', value: 'web' }
      ],
      groupOptions: []
    }
  },
  computed: {
    ...mapGetters(['userDetails', 'language'])
  },
  watch: {
    'addCodingForm.supportedLanguages'(newValue, oldValue) {
      // Add language
      if (newValue.length > oldValue.length) {
        // The following code will not be able to enter
        // this.addCodingForm[newValue[newValue.length - 1]] = ""
        this.$set(this.addCodingForm, newValue[newValue.length - 1], '')
        this.$set(this.addCodingForm, 'ma_' + newValue[newValue.length - 1], '')
        this.$set(this.addCodingForm, 'cd_' + newValue[newValue.length - 1], '')
        this.$set(this.addCodingForm, 'cd_en_' + newValue[newValue.length - 1], '')
        // Add verification rules
        this.addCodingRules[newValue[newValue.length - 1]] = [
          {
            required: true,
            message: `Initial code(Chinese) - ${newValue[newValue.length - 1]} is not empty`,
            trigger: 'change'
          }
        ]
      }
      // Delete language
      if (newValue.length < oldValue.length) {
        oldValue.forEach(item => {
          if (oldValue.indexOf(item) === -1) {
            this.addCodingForm[item] = ''
            this.addCodingForm['ma_' + item] = ''
            this.addCodingForm['cd_' + item] = ''
            this.addCodingForm['cd_en_' + item] = ''
          }
        })
      }
    },
    'editCodingForm.supportedLanguages'(newValue, oldValue) {
      // Add language
      if (newValue.length > oldValue.length) {
        // The following code will not be able to enter
        // this.addCodingForm[newValue[newValue.length - 1]] = ""
        if (!this.editCodingForm[newValue[newValue.length - 1]] || this.editCodingForm[newValue[newValue.length - 1]].length === 0) {
          this.$set(this.editCodingForm, newValue[newValue.length - 1], '')
        }
        if (!this.editCodingForm['ma_' + newValue[newValue.length - 1]] || this.editCodingForm[newValue[newValue.length - 1]].length === 0) {
          this.$set(this.editCodingForm, 'ma_' + newValue[newValue.length - 1], '')
        }
        if (!this.editCodingForm['cd_' + newValue[newValue.length - 1]] || this.editCodingForm[newValue[newValue.length - 1]].length === 0) {
          this.$set(this.editCodingForm, 'cd_' + newValue[newValue.length - 1], '')
        }
        if (!this.editCodingForm['cd_en_' + newValue[newValue.length - 1]] || this.editCodingForm[newValue[newValue.length - 1]].length === 0) {
          this.$set(this.editCodingForm, 'cd_en_' + newValue[newValue.length - 1], '')
        }
        // Add verification rules
        this.editCodingRules[newValue[newValue.length - 1]] = [
          { required: true, message: `Initial code - ${newValue[newValue.length - 1]} is not empty`, trigger: 'change' }
        ]
      }
      // Delete language
      if (newValue.length < oldValue.length) {
        oldValue.forEach(item => {
          if (oldValue.indexOf(item) === -1) {
            this.editCodingForm[item] = ''
            this.editCodingForm['ma_' + item] = ''
            this.editCodingForm['cd_' + item] = ''
            this.editCodingForm['cd_en_' + item] = ''
          }
        })
      }
    }
  },
  created() {
    // Initially loaded content of the page
    this.init()
  },
  mounted() {
  },
  methods: {
    init() {
      // Initialize the coding question list
      this.paginationQueryCodingQuestions()
      this.loadPrimaryCategory().then(response => {
        if (response) {
          const groupOptions = []
          response.zh.forEach((primary, i) => {
            groupOptions.push(
              {
                label: response.zh[i].label,
                labelEn: response.en[i].label,
                value: response.zh[i].value
              }
            )
          })
          this.groupOptions = groupOptions
          this.groupOptions.forEach((item, index) => {
            this.loadSubcategory(item.value, false).then(response => {
              if (response) {
                const options = []
                response.zh.forEach((subcategory, i) => {
                  options.push(
                    {
                      label: response.zh[i].label,
                      labelEn: response.en[i].label,
                      value: response.zh[i].label
                    }
                  )
                })
                this.groupOptions[index]['options'] = options
                this.$forceUpdate()
              } else {
                this.groupOptions[index]['options'] = []
              }
            }).catch(error => {
              console.log(error)
            })
          })
        }
      }).catch(error => {
        console.log(error)
      })
      this.initFormRules()
    },
    // 加载一级分类
    async loadPrimaryCategory() {
      this.loading = true
      const options = []
      const optionsEn = []
      await getCategory()
        .then(response => {
          if (response.data && response.data.length > 0) {
            const responseData = response.data
            for (const item of responseData) {
              options.push({
                label: item.name,
                value: item.id
              })
              optionsEn.push({
                label: item.nameEn,
                value: item.id
              })
            }
            this.categoryOptions = options
            this.categoryOptionsEn = optionsEn
            this.addCodingForm.questionCategory = options[0].value
            this.addCodingForm.questionCategoryEn = optionsEn[0].value
            this.editCodingForm.questionCategory = options[0].value
            this.editCodingForm.questionCategoryEn = optionsEn[0].value
            this.$nextTick(() => {
              this.loadSubcategory(options[0].value, true)
            })
          }
        })
        .finally(() => {
          this.loading = false
        })
      return { zh: options, en: optionsEn }
    },
    changeQuestionCategoryZh(e) {
      for (const zh of this.categoryOptions) {
        // 切换
        if (zh.value === e) {
          this.addCodingForm.questionCategoryEn = e
          return
        } else {
          // 新增
          for (const en of this.categoryOptionsEn) {
            if (en.value === this.addCodingForm.questionCategoryEn) {
              this.addCodingForm.questionCategoryEn = ''
            }
          }
        }
      }
    },
    changeUpdateQuestionCategoryZh(e) {
      for (const zh of this.categoryOptions) {
        // 切换
        if (zh.value === e) {
          this.editCodingForm.questionCategoryEn = e
          return
        } else {
          // 新增
          for (const en of this.categoryOptionsEn) {
            if (en.value === this.editCodingForm.questionCategoryEn) {
              this.editCodingForm.questionCategoryEn = ''
            }
          }
        }
      }
    },
    changeQuestionCategoryEn(e) {
      for (const en of this.categoryOptionsEn) {
        // 切换
        if (en.value === e) {
          this.addCodingForm.questionCategory = e
          return
        } else {
          // 新增
          for (const zh of this.categoryOptions) {
            if (zh.value === this.addCodingForm.questionCategory) {
              this.addCodingForm.questionCategory = ''
            }
          }
        }
      }
    },
    changeUpdateQuestionCategoryEn(e) {
      for (const en of this.categoryOptionsEn) {
        // 切换
        if (en.value === e) {
          this.editCodingForm.questionCategory = e
          return
        } else {
          // 新增
          for (const zh of this.categoryOptions) {
            if (zh.value === this.editCodingForm.questionCategory) {
              this.editCodingForm.questionCategory = ''
            }
          }
        }
      }
    },
    changeQuestionSubcategoryZh(e) {
      for (let i = 0; i < this.subcategoryOptions.length; i++) {
        // 切换
        if (this.subcategoryOptions[i].value === e) {
          this.addCodingForm.questionSubcategoryEn = this.subcategoryOptionsEn[i].value
          return
        } else {
          // 新增
          for (const en of this.subcategoryOptionsEn) {
            if (en.value === this.addCodingForm.questionSubcategoryEn) {
              this.addCodingForm.questionSubcategoryEn = ''
            }
          }
        }
      }
    },
    changeUpdateQuestionSubcategoryZh(e) {
      for (let i = 0; i < this.subcategoryOptions.length; i++) {
        // 切换
        if (this.subcategoryOptions[i].value === e) {
          this.editCodingForm.questionSubcategoryEn = this.subcategoryOptionsEn[i].value
          return
        } else {
          // 新增
          for (const en of this.subcategoryOptionsEn) {
            if (en.value === this.editCodingForm.questionSubcategoryEn) {
              this.editCodingForm.questionSubcategoryEn = ''
            }
          }
        }
      }
    },
    changeQuestionSubcategoryEn(e) {
      for (let i = 0; i < this.subcategoryOptionsEn.length; i++) {
        // 切换
        if (this.subcategoryOptionsEn[i].value === e) {
          this.addCodingForm.questionSubcategory = this.subcategoryOptions[i].value
          return
        } else {
          // 新增
          for (const zh of this.subcategoryOptions) {
            if (zh.value === this.addCodingForm.questionSubcategory) {
              this.addCodingForm.questionSubcategory = ''
            }
          }
        }
      }
    },
    changeUpdateQuestionSubcategoryEn(e) {
      for (let i = 0; i < this.subcategoryOptionsEn.length; i++) {
        // 切换
        if (this.subcategoryOptionsEn[i].value === e) {
          this.editCodingForm.questionSubcategory = this.subcategoryOptions[i].value
          return
        } else {
          // 新增
          for (const zh of this.subcategoryOptions) {
            if (zh.value === this.editCodingForm.questionSubcategory) {
              this.editCodingForm.questionSubcategory = ''
            }
          }
        }
      }
    },
    async loadSubcategory(id, flag) {
      const vue = this
      vue.loading = true
      const subcategory = []
      const subcategoryEn = []
      await getSubcategory({ categoryId: id })
        .then(response => {
          const responseData = response.data
          for (const item of responseData) {
            subcategory.push({
              label: item.name,
              value: item.name
            })
            subcategoryEn.push({
              label: item.nameEn,
              value: item.nameEn
            })
          }
          vue.subcategoryOptions = subcategory
          vue.subcategoryOptionsEn = subcategoryEn
          if (subcategory.length > 0) {
            if (flag) {
              vue.addCodingForm.questionSubcategory = subcategory[0].value
              vue.addCodingForm.questionSubcategoryEn = subcategoryEn[0].value
            }
            vue.editCodingForm.questionSubcategory = subcategory[0].value
            vue.editCodingForm.questionSubcategoryEn = subcategoryEn[0].value
          } else {
            vue.addCodingForm.questionSubcategory = null
            vue.addCodingForm.questionSubcategoryEn = null
            vue.editCodingForm.questionSubcategory = null
            vue.editCodingForm.questionSubcategoryEn = null
          }
        })
        .finally(() => {
          vue.loading = false
        })
      return { zh: subcategory, en: subcategoryEn }
    },
    // Pagination query coding question(s)
    paginationQueryCodingQuestions() {
      const vue = this
      this.loading = true
      getQuestionByPage(vue.pageRequest)
        .then(response => {
          vue.questions = response.data.items
          vue.total = response.data.total
        })
        .finally(() => {
          vue.loading = false
        })
    },
    // Initialize form validation rules
    initFormRules() {
      this.addCodingRules = {
        questionTitle: [
          { required: true, message: 'Question title is not empty(Chinese)', trigger: 'change' }
        ],
        questionTitleEn: [
          { required: true, message: 'Question title is not empty(English)', trigger: 'change' }
        ],
        questionCategory: [
          { required: true, message: 'Question category is not empty(Chinese)', trigger: 'change' }
        ],
        questionCategoryEn: [
          { required: true, message: 'Question category is not empty(English)', trigger: 'change' }
        ],
        questionSubcategory: [
          { required: true, message: 'Question subcategory is not empty(Chinese)', trigger: 'change' }
        ],
        questionSubcategoryEn: [
          { required: true, message: 'Question subcategory is not empty(English)', trigger: 'change' }
        ],
        target: [
          { required: true, message: 'Target is not empty(Chinese)', trigger: 'change' }
        ],
        targetEn: [
          { required: true, message: 'Target is not empty(English)', trigger: 'change' }
        ],
        trainingPurpose: [
          { required: true, message: 'Training purpose is not empty(Chinese)', trigger: 'change' }
        ],
        trainingPurposeEn: [
          { required: true, message: 'Training purpose is not empty(English)', trigger: 'change' }
        ],
        description: [
          { required: true, message: 'Question description is not empty(Chinese)', trigger: 'change' }
        ],
        descriptionEn: [
          { required: true, message: 'Question description is not empty(English)', trigger: 'change' }
        ],
        supportedLanguages: [
          { required: true, message: 'Question title is not empty', trigger: 'change' }
        ]
      }

      this.editCodingRules = {
        questionTitle: [
          { required: true, message: 'Question title is not empty(Chinese)', trigger: 'change' }
        ],
        questionTitleEn: [
          { required: true, message: 'Question title is not empty(English)', trigger: 'change' }
        ],
        questionCategory: [
          { required: true, message: 'Question category is not empty(Chinese)', trigger: 'change' }
        ],
        questionCategoryEn: [
          { required: true, message: 'Question category is not empty(English)', trigger: 'change' }
        ],
        questionSubcategory: [
          { required: true, message: 'Question subcategory is not empty(Chinese)', trigger: 'change' }
        ],
        questionSubcategoryEn: [
          { required: true, message: 'Question subcategory is not empty(English)', trigger: 'change' }
        ],
        target: [
          { required: true, message: 'Target is not empty(Chinese)', trigger: 'change' }
        ],
        targetEn: [
          { required: true, message: 'Target is not empty(English)', trigger: 'change' }
        ],
        trainingPurpose: [
          { required: true, message: 'Training purpose is not empty(Chinese)', trigger: 'change' }
        ],
        trainingPurposeEn: [
          { required: true, message: 'Training purpose is not empty(English)', trigger: 'change' }
        ],
        description: [
          { required: true, message: 'Question description is not empty(Chinese)', trigger: 'change' }
        ],
        descriptionEn: [
          { required: true, message: 'Question description is not empty(English)', trigger: 'change' }
        ],
        supportedLanguages: [
          { required: true, message: 'Question title is not empty', trigger: 'change' }
        ]
      }
    },
    // Open add coding question dialog
    openAddQuestionDialog() {
      this.addQuestionDialog = true
    },
    // Open edit coding question dialog
    openEditQuestionDialog(row) {
      this.editCodingForm.id = row.id
      this.editCodingForm.questionTitle = row.questionTitle
      this.editCodingForm.questionTitleEn = row.questionTitleEn
      this.editCodingForm.questionCategory = row.questionCategoryId
      this.editCodingForm.questionCategoryEn = row.questionCategoryId
      this.editCodingForm.questionSubcategory = row.questionSubcategory
      this.editCodingForm.questionSubcategoryEn = row.questionSubcategoryEn
      this.editCodingForm.apiInfo = row.apiInfo
      this.editCodingForm.apiInfoEn = row.apiInfoEn
      this.editCodingForm.description = row.description
      this.editCodingForm.descriptionEn = row.descriptionEn
      this.editCodingForm.target = row.target
      this.editCodingForm.targetEn = row.targetEn
      this.editCodingForm.trainingPurpose = row.trainingPurpose
      this.editCodingForm.trainingPurposeEn = row.trainingPurposeEn
      this.editCodingForm.databaseInfo = row.databaseInfo
      this.editCodingForm.databaseInfoEn = row.databaseInfoEn
      if (row.modelAnswer) this.editCodingForm.modelAnswer = JSON.parse(row.modelAnswer)
      if (row.code) this.editCodingForm.code = JSON.parse(row.code)
      if (row.codeDescription) this.editCodingForm.codeDescription = JSON.parse(row.codeDescription)
      if (row.codeDescriptionEn) this.editCodingForm.codeDescriptionEn = JSON.parse(row.codeDescriptionEn)
      this.editCodingForm.supportedLanguages = Object.keys(this.editCodingForm.code)
      for (const item of this.editCodingForm.supportedLanguages) {
        this.$set(this.editCodingForm, item, this.editCodingForm.code[item])
        this.$set(this.editCodingForm, 'ma_' + item, this.editCodingForm.modelAnswer['ma_' + item])
        this.$set(this.editCodingForm, 'cd_' + item, this.editCodingForm.codeDescription['cd_' + item])
        this.$set(this.editCodingForm, 'cd_en_' + item, this.editCodingForm.codeDescriptionEn['cd_en_' + item])
        this.editCodingRules[item] = [
          { required: true, message: `Initial code - ${item} is not empty`, trigger: 'change' }
        ]
      }
      this.editQuestionDialog = true
    },
    // Create Coding Question
    addQuestion(formName) {
      const vue = this
      // Verify the content of the form
      vue.$refs[formName].validate((valid) => {
        if (valid) {
          // Confirm creation coding question
          vue.$confirm(vue.$t('soi.codingPractice.addQuestionTip'), vue.$t('soi.common.tip'), {
            confirmButtonText: vue.$t('soi.common.confirm'),
            cancelButtonText: vue.$t('soi.common.cancel'),
            type: 'warning'
          }).then(() => {
            vue.loading = true
            // Call the interface to create a coding question
            const requestData = vue.handlerAddRequestData()
            createQuestion(requestData)
              .then(response => {
                vue.$message.success(response.message)
                vue.addQuestionDialog = false
                vue.paginationQueryCodingQuestions()
                vue.loadPrimaryCategory()
              })
              .finally(() => {
                vue.loading = false
              })
          }).catch(() => {
          })
        } else {
          return false
        }
      })
    },
    // Update Coding Question
    editQuestion(formName) {
      const vue = this
      // Verify the content of the form
      vue.$refs[formName].validate((valid) => {
        if (valid) {
          // Confirm creation coding question
          vue.$confirm(vue.$t('soi.codingPractice.updateQuestionTip'), vue.$t('soi.common.tip'), {
            confirmButtonText: vue.$t('soi.common.confirm'),
            cancelButtonText: vue.$t('soi.common.cancel'),
            type: 'warning'
          }).then(() => {
            vue.loading = true
            // Call the interface to create a coding question
            const requestData = vue.handlerUpdateRequestData()
            updateQuestion(requestData)
              .then(response => {
                vue.$message.success(response.message)
                vue.editQuestionDialog = false
                vue.paginationQueryCodingQuestions()
                vue.loadPrimaryCategory()
              })
              .finally(() => {
                vue.loading = false
              })
          }).catch(() => {
          })
        } else {
          return false
        }
      })
    },
    deleteCodingQuestion(id) {
      const vue = this
      // Confirm creation coding question
      vue.$confirm(vue.$t('soi.codingPractice.deleteQuestionTip'), vue.$t('soi.common.tip'), {
        confirmButtonText: vue.$t('soi.common.confirm'),
        cancelButtonText: vue.$t('soi.common.cancel'),
        type: 'warning'
      }).then(() => {
        vue.loading = true
        // Call the interface to create a coding question
        deleteQuestion({ ids: [id] })
          .then(response => {
            vue.$message.success(response.message)
            const deleteAfterPage = Math.ceil((vue.total - 1) / vue.pageRequest.rows)
            const currentPage = vue.pageRequest.page > deleteAfterPage ? deleteAfterPage : vue.pageRequest.page
            vue.pageRequest.page = currentPage < 1 ? 1 : currentPage
            vue.paginationQueryCodingQuestions()
          })
          .finally(() => {
            vue.loading = false
          })
      }).catch(() => {
      })
    },
    handlerAddRequestData() {
      const code = {}
      const modelAnswer = {}
      const codeDescription = {}
      const codeDescriptionEn = {}

      this.addCodingForm.supportedLanguages.forEach(item => {
        code[item] = this.addCodingForm[item]
        modelAnswer['ma_' + item] = this.addCodingForm['ma_' + item]
        codeDescription['cd_' + item] = this.addCodingForm['cd_' + item]
        codeDescriptionEn['cd_en_' + item] = this.addCodingForm['cd_en_' + item]
      })

      return {
        creator: this.userDetails.email,
        questionTitle: this.addCodingForm.questionTitle,
        questionTitleEn: this.addCodingForm.questionTitleEn,
        questionCategory: this.addCodingForm.questionCategory,
        questionCategoryEn: this.addCodingForm.questionCategoryEn,
        questionSubcategory: this.addCodingForm.questionSubcategory,
        questionSubcategoryEn: this.addCodingForm.questionSubcategoryEn,
        apiInfo: this.addCodingForm.apiInfo,
        apiInfoEn: this.addCodingForm.apiInfoEn,
        description: this.addCodingForm.description,
        descriptionEn: this.addCodingForm.descriptionEn,
        target: this.addCodingForm.target,
        targetEn: this.addCodingForm.targetEn,
        trainingPurpose: this.addCodingForm.trainingPurpose,
        trainingPurposeEn: this.addCodingForm.trainingPurposeEn,
        databaseInfo: this.addCodingForm.databaseInfo,
        databaseInfoEn: this.addCodingForm.databaseInfoEn,
        modelAnswer: JSON.stringify(modelAnswer),
        code: JSON.stringify(code),
        codeDescription: JSON.stringify(codeDescription),
        codeDescriptionEn: JSON.stringify(codeDescriptionEn)
      }
    },
    handlerUpdateRequestData() {
      const code = {}
      const modelAnswer = {}
      const codeDescription = {}
      const codeDescriptionEn = {}

      this.editCodingForm.supportedLanguages.forEach(item => {
        code[item] = this.editCodingForm[item]
        modelAnswer['ma_' + item] = this.editCodingForm['ma_' + item]
        codeDescription['cd_' + item] = this.editCodingForm['cd_' + item]
        codeDescriptionEn['cd_en_' + item] = this.editCodingForm['cd_en_' + item]
      })

      return {
        id: this.editCodingForm.id,
        questionTitle: this.editCodingForm.questionTitle,
        questionTitleEn: this.editCodingForm.questionTitleEn,
        questionCategory: this.editCodingForm.questionCategory,
        questionCategoryEn: this.editCodingForm.questionCategoryEn,
        questionSubcategory: this.editCodingForm.questionSubcategory,
        questionSubcategoryEn: this.editCodingForm.questionSubcategoryEn,
        apiInfo: this.editCodingForm.apiInfo,
        apiInfoEn: this.editCodingForm.apiInfoEn,
        description: this.editCodingForm.description,
        descriptionEn: this.editCodingForm.descriptionEn,
        target: this.editCodingForm.target,
        targetEn: this.editCodingForm.targetEn,
        trainingPurpose: this.editCodingForm.trainingPurpose,
        trainingPurposeEn: this.editCodingForm.trainingPurposeEn,
        databaseInfo: this.editCodingForm.databaseInfo,
        databaseInfoEn: this.editCodingForm.databaseInfoEn,
        modelAnswer: JSON.stringify(modelAnswer),
        code: JSON.stringify(code),
        codeDescription: JSON.stringify(codeDescription),
        codeDescriptionEn: JSON.stringify(codeDescriptionEn)
      }
    },
    // Reload data when page size changes
    handleSizeChange(val) {
      this.pageRequest.rows = val
      this.paginationQueryCodingQuestions()
    },
    // Reload data when current page changes
    handleCurrentChange(val) {
      this.pageRequest.page = val
      this.paginationQueryCodingQuestions()
    },
    // Reload data when sort changes
    handleSortChange(sort) {
      const { prop, order } = sort
      if (order === null) { // order is null means no sorting
        this.pageRequest.sortBy = null // If the backend does not sort, you need to set the sort field to null
      } else {
        this.pageRequest.sortBy = prop
      }
      this.pageRequest.desc = order === 'descending'
      this.paginationQueryCodingQuestions()
    },
    // Reset the form according to the ref name of the form
    resetForm(formName) {
      this.$refs[formName].resetFields()
    }
  }
}
</script>

<style lang="scss" scoped>
.practice-management-coding-question-management-container {
  .data-action-button-group {
    margin-bottom: 10px;
    display: flex;
    justify-content: space-between;
  }
}
</style>
