<template>
  <el-collapse v-model="activeNames">
    <el-collapse-item :title="$t('soi.workflowConfig.publicWorkflow')" name="1">
      <el-table
        :data="commonTableData"
        style="width: 100%"
        stripe
        highlight-current-row
      >
        <el-table-column type="index" label="#" width="50" />
        <el-table-column prop="name" :label="$t('soi.workflowConfig.name')" min-width="150" />
        <el-table-column prop="group_name" :label="$t('soi.workflowConfig.group')" min-width="150" />
        <el-table-column prop="create_date" :label="$t('soi.common.createDate')" min-width="150" />
        <el-table-column :label="$t('soi.common.operate')" width="220">
          <template slot-scope="scope">
            <el-button type="text" size="small" @click="viewData(scope.row.group_name,'1')">{{ $t("soi.common.view") }}</el-button>
            <el-button type="text" size="small" @click="downloadFile(scope.row,'0')">{{ $t("soi.common.download") }}</el-button>
            <el-button v-if="isAdmin" type="text" size="small" @click="updateData(scope.row)">{{ $t("soi.common.edit") }}</el-button>
            <el-button v-if="isAdmin" type="text" size="small" @click="handleDeleteClick(scope.row)">{{ $t("soi.common.delete") }}</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-collapse-item>
    <el-collapse-item v-if="!isAdmin" :title="$t('soi.workflowConfig.customWorkflow')" name="2">
      <el-table
        :data="tableData"
        style="width: 100%"
        stripe
        highlight-current-row
      >
        <el-table-column type="index" label="#" width="50" />
        <el-table-column prop="name" :label="$t('soi.workflowConfig.name')" min-width="150" />
        <el-table-column prop="group_name" :label="$t('soi.workflowConfig.group')" min-width="150" />
        <el-table-column prop="create_date" :label="$t('soi.common.createDate')" min-width="150" />
        <el-table-column :label="$t('soi.common.operate')" width="220">
          <template slot-scope="scope">
            <el-button type="text" size="small" @click="viewData(scope.row.group_name,'0')">{{ $t("soi.common.view") }}</el-button>
            <el-button type="text" size="small" @click="downloadFile(scope.row,'0')">{{ $t("soi.common.download") }}</el-button>
            <el-button type="text" size="small" @click="updateData(scope.row)">{{ $t("soi.common.edit") }}</el-button>
            <el-button type="text" size="small" @click="handleDeleteClick(scope.row)">{{ $t("soi.common.delete") }}</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-collapse-item>
  </el-collapse>
</template>

<script>
import { mapGetters } from 'vuex'
import { deleteDeployment, getWorkflow } from '@/api/system/workflow'
import { WORKFLOW_API_URL } from '@/contains'

export default {
  name: 'PracticeManagementWorkflowList',
  props: {
    module: {
      type: String,
      required: false,
      default: 'practice-management'
    }
  },
  data() {
    return {
      loading: false,
      activeNames: ['1', '2'],
      tableData: [],
      commonTableData: []
    }
  },
  computed: {
    ...mapGetters(['userDetails', 'roles']),
    isAdmin() {
      return this.roles.includes('administrator')
    }
  },
  created() {
    this.init()
  },
  methods: {
    init() {
      const vue = this
      vue.loading = true
      getWorkflow({ userID: this.userDetails.id })
        .then((res) => {
          vue.tableData = []
          vue.commonTableData = []
          if (res.data) {
            const list = res.data
            for (let i = 0; i < list.length; i++) {
              if (list[i].userId === 'admin001') {
                vue.commonTableData.push(list[i])
              } else {
                vue.tableData.push(list[i])
              }
            }
          }
        })
        .finally(() => {
          vue.loading = false
        })
    },
    viewData(tableName) {
      if (tableName === 'eKYC') {
        this.$router.push({ name: `${this.module}-e-kyc-workflow-details` })
      }
      if (tableName === 'insurance') {
        this.$router.push({ name: `${this.module}-insurance-workflow-details` })
      }
    },
    updateData(row) {
      localStorage.setItem('workflow-info', JSON.stringify(row))
      if (row.group_name === 'eKYC') {
        this.$router.push({ name: `${this.module}-manage-e-kyc-workflow` })
      } else {
        this.$router.push({ name: `${this.module}-manage-insurance-workflow` })
      }
    },
    handleDeleteClick(row) {
      const vue = this
      vue.$confirm(vue.$t('soi.workflowConfig.deleteTip'), vue.$t('soi.common.tip'), {
        confirmButtonText: vue.$t('soi.common.confirm'),
        cancelButtonText: vue.$t('soi.common.cancel'),
        type: 'warning'
      }).then(() => {
        vue.loading = true
        const requestData = {
          deploymentId: row.deployment_id,
          userID: this.userDetails.id
        }

        deleteDeployment(requestData)
          .then(() => {
            vue.init()
          })
          .finally(() => {
            vue.loading = false
          })
      })
    },
    downloadFile(row) {
      window.open(`${WORKFLOW_API_URL}/workflow/download?id=${row.id}`)
    }
  }
}
</script>
