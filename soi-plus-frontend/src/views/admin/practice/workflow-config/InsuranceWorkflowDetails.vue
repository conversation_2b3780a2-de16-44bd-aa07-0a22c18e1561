<template>
  <div v-loading.fullscreen.lock="loading" class="practice-management-e-kyc-workflow-details-container">
    <customize-card :title="$t('soi.router.eKYCWorkflowDetails')">
      <h3>{{ $t("soi.workflowConfig.insuranceClaimsProcessNodeList") }}</h3>
      <el-table
        class="params-table"
        :header-cell-style="{'background': '#109eae42', 'color': '#333'}"
        :data="tableDataNode"
      >
        <el-table-column type="index" label="#" />
        <el-table-column prop="api_name" :label="$t('soi.workflowConfig.name')" align="center" />
        <el-table-column prop="group" :label="$t('soi.workflowConfig.group')" align="center" />
        <el-table-column prop="api_url" :label="$t('soi.workflowConfig.apiUrl')" />
        <el-table-column prop="api_method" :label="$t('soi.workflowConfig.apiMethod')" align="center" />
      </el-table>
    </customize-card>
  </div>
</template>
<script>
import CustomizeCard from '@/components/CustomizeCard/index.vue'
import { mapGetters } from 'vuex'
import { getFlows } from '@/api/system/workflow'

export default {
  name: 'PracticeManagementEKYCWorkflowDetails',
  components: { CustomizeCard },
  data() {
    return {
      loading: false,
      tableDataNode: []
    }
  },
  computed: {
    ...mapGetters(['userDetails'])
  },
  mounted() {
    this.getAllWorkflowNode()
  },
  methods: {
    getAllWorkflowNode() {
      const _this = this
      _this.loading = true
      getFlows({ group: 'eKYC' })
        .then(response => {
          _this.tableDataNode = response.data
        })
        .finally(() => {
          _this.loading = false
        })
    }
  }
}
</script>
