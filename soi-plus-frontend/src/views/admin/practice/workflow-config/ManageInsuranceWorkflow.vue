<template>
  <div v-loading.fullscreen.lock="loading" class="practice-management-manage-insurance-container">
    <customize-card :title="$t('soi.router.manageInsuranceWorkflow')">
      <el-row>
        <el-col :span="12" style="padding: 0 10px;">
          <p>{{ $t("soi.workflowConfig.insuranceTip1") }}</p>
          <div v-html="$t('soi.workflowConfig.insuranceTip2')" />
        </el-col>
        <el-col :span="12" style="padding: 0 10px;">
          <el-form ref="form" :model="form" :rules="rules" label-width="150px">
            <el-form-item :label="$t('soi.workflowConfig.name')" prop="flowName">
              <el-input v-model="form.flowName" />
            </el-form-item>
            <el-form-item :label="$t('soi.workflowConfig.file')" prop="file">
              <el-upload
                ref="upload"
                action="/"
                :auto-upload="false"
                :show-file-list="true"
                :drag="true"
                :limit="1"
                accept=".bpmn"
                :on-exceed="handlerExceed"
                :on-success="handlerSuccess"
                :on-error="handlerError"
                :on-change="handlerChange"
                :file-list="fileList"
              >
                <i class="el-icon-upload" />
                <div class="el-upload__text">
                  <em>{{ $t("soi.workflowConfig.clickUploadTip") }}</em>
                </div>
                <div slot="tip" class="el-upload__tip">{{ $t("soi.workflowConfig.uploadTip") }}</div>
              </el-upload>
            </el-form-item>
            <div class="text-center">
              <el-button type="primary" style="width:100px;" size="mini" @click="submit()">{{ $t("soi.common.submit") }}</el-button>
            </div>
          </el-form>
        </el-col>
      </el-row>
    </customize-card>
  </div>
</template>
<script>
import CustomizeCard from '@/components/CustomizeCard/index.vue'
import { mapGetters } from 'vuex'
import { createOrUpdateDeploy } from '@/api/system/workflow'

export default {
  name: 'PracticeManagementManageInsuranceWorkflow',
  components: { CustomizeCard },
  data() {
    return {
      loading: false,
      form: {
        file: null,
        flowName: ''
      },
      rules: {
        flowName: [
          { required: true, message: this.$t('soi.workflowConfig.workflowNameError'), trigger: 'blur' }
        ],
        file: [
          { required: true, message: this.$t('soi.workflowConfig.fileError'), trigger: 'change' }
        ]
      },
      role: '',
      fileList: []
    }
  },
  computed: {
    ...mapGetters(['userDetails', 'roles']),
    isAdmin() {
      return this.roles.includes('administrator')
    }
  },
  created() {
    const workflowInfoStr = localStorage.getItem('workflow-info')
    if (workflowInfoStr) {
      const workflowInfo = JSON.parse(workflowInfoStr)
      this.form.flowName = workflowInfo.name
      this.form.id = workflowInfo.id
    }
  },
  methods: {
    submit() {
      const vue = this
      vue.$refs['form'].validate(valid => {
        if (valid) {
          vue.loading = true
          const requestData = new FormData()
          requestData.append('file', vue.form.file)
          let url = '/workflow/createDeploy'
          if (this.form.id) {
            url = '/workflow/updateDeploy'
            requestData.append('id', vue.form.id)
          } else {
            requestData.append('flowName', vue.form.flowName)
            requestData.append('ruleId', 2)
            requestData.append('userId', this.userDetails.id)
          }
          createOrUpdateDeploy(url, requestData)
            .then(() => {
              vue.loading = false
              this.$router.go(-1)
            }).finally(() => {
              vue.loading = false
            })
        }
      })
    },
    handlerChange(file, fileList) {
      const vue = this
      if (!file) {
        return
      }
      const isLt2M = file.size / 1024 / 1024 < 2
      if (!isLt2M) {
        this.$message.error(vue.$t('soi.workflowConfig.fileSizeError'))
        vue.fileList = []
        return isLt2M
      } else {
        vue.form.file = file.raw
        vue.fileList = fileList.slice(-1)
      }
    },
    handlerSuccess() {
      const vue = this
      vue.$alert(vue.$t('soi.workflowConfig.uploadSuccess'), vue.$t('soi.common.tip'), {
        confirmButtonText: vue.$t('soi.common.confirm'),
        callback: action => {}
      })
    },
    handlerError() {
      const vue = this
      vue.$alert(vue.$t('soi.workflowConfig.uploadFailed'), vue.$t('soi.common.tip'), {
        confirmButtonText: vue.$t('soi.common.confirm'),
        callback: action => {}
      })
    },
    handlerExceed() {
      const vue = this
      vue.$alert(
        vue.$t('soi.workflowConfig.fileNumberError'),
        vue.$t('soi.common.tip'),
        {
          confirmButtonText: vue.$t('soi.common.confirm'),
          callback: action => {}
        }
      )
    }
  }
}
</script>

<style lang="scss" scoped>
.practice-management-manage-insurance-container {
  font-size: 14px;
  .text-center {
    text-align: center;
  }
}
</style>
