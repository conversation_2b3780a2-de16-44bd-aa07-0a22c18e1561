<template>
  <div v-loading.fullscreen.lock="loading" class="practice-management-api-list">
    <customize-card :title="$t('soi.router.apiList')">
      <template #header-actions>
        <el-button size="mini" @click="handleImportApi">{{ $t('soi.apiList.importApi') }}</el-button>
        <el-button size="mini" @click="handleAddApi">{{ $t('soi.apiList.addApi') }}</el-button>
      </template>
      <el-table
        :data="tableData.slice((currentPage - 1) * pagesize, currentPage * pagesize)"
        style="width: 100%"
        stripe
        highlight-current-row
      >
        <el-table-column type="index" label="#" width="50" />
        <el-table-column prop="apiName" :label="$t('soi.apiList.apiName')" min-width="200">
          <template slot-scope="scope">
            {{ ['zh', 'cht'].includes(language) ? scope.row.apiName : scope.row.apiNameEn }}
          </template>
        </el-table-column>
        <el-table-column prop="description" :label="$t('soi.common.description')" min-width="200">
          <template slot-scope="scope">
            {{ ['zh', 'cht'].includes(language) ? scope.row.description : scope.row.descriptionEn }}
          </template>
        </el-table-column>
        <el-table-column prop="createDate" :label="$t('soi.common.createDate')" min-width="100" />
        <el-table-column :label="$t('soi.common.operate')" width="180">
          <template slot-scope="scope">
            <el-button type="text" size="small" @click="handleClick(scope.row)">{{ $t("soi.common.edit") }}</el-button>
            <el-button type="text" size="small" @click="handleDeleteClick(scope.row,scope.$index)">{{ $t("soi.common.delete") }}</el-button>
            <el-button type="text" size="small" @click="handleTryOutClick(scope.row)">{{ $t("soi.apiList.tryoutApi") }}</el-button>

          </template>
        </el-table-column>
      </el-table>
      <div class="table-footer">
        <el-pagination
          :current-page="currentPage"
          :page-sizes="[5, 10, 20, 40]"
          :page-size="pagesize"
          :total="tableData.length"
          layout="total, prev, pager, next"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </customize-card>
  </div>
</template>

<script>
import CustomizeCard from '@/components/CustomizeCard/index.vue'
import { deleteApi, getApiList } from '@/api/practice/api-market'
import { mapGetters } from 'vuex'

export default {
  name: 'PracticeManagementApiList',
  components: { CustomizeCard },
  data() {
    return {
      loading: false,
      classId: this.$route.params.classId,
      subclassId: this.$route.params.subclassId,
      tableData: [],
      currentPage: 1,
      pagesize: 10
    }
  },
  computed: {
    ...mapGetters([
      'userDetails',
      'language'
    ])
  },
  mounted() {
    this.init()
  },
  methods: {
    init() {
      const vue = this
      vue.loading = true
      getApiList({ self: '1', apiClassId: this.classId, userId: this.userDetails.id, apiSubClassId: this.subclassId })
        .then((res) => {
          vue.tableData = []
          if (res.data) {
            const list = res.data.reverse()
            let obj = {}
            for (let i = 0; i < list.length; i++) {
              obj = {}
              obj.apiName = list[i].apiInfo.apiName
              obj.apiNameEn = list[i].apiInfo.apiNameEn
              obj.apiId = list[i].apiInfo.apiId
              obj.targetUrl = list[i].apiInfo.targetUrl
              obj.description = list[i].apiInfo.description
              obj.descriptionEn = list[i].apiInfo.descriptionEn
              obj.createDate = list[i].apiInfo.createDate
              obj.access = list[i].access
              obj.policyId = list[i].apiInfo.policyId
              obj.fiName = list[i].apiInfo.fiName
              vue.tableData.push(obj)
            }
          }
        })
        .finally(() => {
          vue.loading = false
        })
    },
    handleClick(row) {
      localStorage.setItem('edit-api-info', JSON.stringify(row))
      this.$router.push({ name: 'practice-management-edit-api', params: { classId: this.classId, subclassId: this.subclassId, apiId: row.apiId }})
    },
    handleDeleteClick(row) {
      const vue = this
      vue.$confirm(vue.$t('soi.apiList.confirmDeleteTip'), vue.$t('soi.common.tip'), {
        confirmButtonText: vue.$t('soi.common.confirm'),
        cancelButtonText: vue.$t('soi.common.cancel'),
        type: 'warning'
      }).then(() => {
        vue.loading = true
        deleteApi({ apiIds: [row.apiId] })
          .then(() => {
            vue.$message({
              type: 'success',
              message: vue.$t('soi.apiList.deleteSuccess')
            })
            vue.init()
          })
          .finally(() => {
            vue.loading = false
          })
      }).catch(() => {})
    },
    handleAddApi() {
      this.$router.push({
        name: 'practice-management-add-api',
        query: { classId: this.classId, subclassId: this.subclassId }
      })
    },
    handleImportApi() {
      this.$router.push({
        name: 'practice-management-import-api',
        query: { classId: this.classId, subclassId: this.subclassId }
      })
    },
    handleTryOutClick(row) {
      localStorage.setItem('api-info', JSON.stringify(row))
      this.$router.push({
        name: 'practice-management-tryout-api',
        params: { apiId: row.apiId }
      })
    },
    handleSizeChange(size) {
      this.pagesize = size
    },
    handleCurrentChange(currentPage) {
      this.currentPage = currentPage
    }
  }
}
</script>
