<template>
  <div v-loading.fullscreen.lock="loading" class="practice-management-sandbox-management-container">
    <customize-card :title="$t('soi.router.sandboxManagement')" :show-back-btn="false">
      <el-form ref="searchForm" :model="searchForm" size="small" inline>
        <!-- 用户学生 -->
        <el-form-item :label="$t('soi.sandboxManagement.studentUser')" prop="userId">
          <el-select v-model="searchForm.userId" :placeholder="$t('soi.sandboxManagement.studentUser')">
            <el-option
              v-for="item in userOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>

        <!-- Sandbox ID -->
        <el-form-item :label="$t('soi.sandboxManagement.sandboxId')" prop="sandboxId">
          <el-input v-model="searchForm.sandboxId" :placeholder="$t('soi.sandboxManagement.sandboxId')" />
        </el-form-item>

        <!-- 操作按钮 -->
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="initData()">{{ $t('soi.common.search') }}</el-button>
          <el-button icon="el-icon-refresh" @click="resetForm('searchForm')">{{ $t('soi.common.reset') }}</el-button>
        </el-form-item>
      </el-form>

      <div class="data-action-button-group">
        <div>
          <el-button size="small" icon="el-icon-delete" type="danger" plain @click="deleteAllOut()">{{ $t('soi.sandboxManagement.deleteOut') }}</el-button>
          <el-button size="small" icon="el-icon-delete" type="danger" plain @click="deleteAllInside()">{{ $t('soi.sandboxManagement.deleteInside') }}</el-button>
        </div>

        <el-button icon="el-icon-refresh" size="small" circle @click="initData()" />
      </div>

      <el-table
        :data="tableData.slice((currentPage - 1) * pagesize, currentPage * pagesize)"
        stripe
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="expand">
          <template slot-scope="scope">
            <el-table :data="scope.row.tokenList" style="width: 100%" @selection-change="handleSelectionChangeChild">
              <el-table-column type="selection" width="55" />
              <el-table-column type="index" label="#" width="50" />
              <el-table-column prop="CustomerNumber" :label="$t('soi.sandboxManagement.customerNumber')" min-width="100" />
              <el-table-column prop="Loginname" :label="$t('soi.sandboxManagement.loginName')" min-width="100" />
              <el-table-column prop="LoginPwd" :label="$t('soi.sandboxManagement.loginPassword')" min-width="100" />
              <el-table-column prop="Token" :label="$t('soi.common.token')" min-width="300" />
            </el-table>
          </template>
        </el-table-column>
        <el-table-column type="selection" width="55" />
        <el-table-column type="index" label="#" width="50" />
        <el-table-column prop="email" :label="$t('soi.sandboxManagement.email')" min-width="100" />
        <el-table-column prop="sandboxId" :label="$t('soi.sandboxManagement.sandboxId')" min-width="200" />
      </el-table>
      <div class="table-footer">
        <el-pagination
          :current-page="currentPage"
          :page-sizes="[5, 10, 20, 40]"
          :page-size="pagesize"
          :total="tableData.length"
          layout="total, prev, pager, next"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </customize-card>
  </div>
</template>

<script>
import CustomizeCard from '@/components/CustomizeCard/index.vue'
import { deleteApiTokenByUserId, findUser, getSandboxList } from '@/api/practice/sandbox'

export default {
  name: 'PracticeManagementSandboxManagement',
  components: { CustomizeCard },
  data() {
    return {
      loading: false,
      searchForm: {
        userId: '',
        sandboxId: ''
      },
      tableData: [],
      tokenList: [],
      userOptions: [],
      currentPage: 1,
      pagesize: 20,
      multipleSelection: [],
      multipleSelectionChild: []
    }
  },
  mounted() {
    this.getSuList()
  },
  methods: {
    initData() {
      const vue = this
      vue.loading = true
      getSandboxList({ userId: vue.searchForm.userId, sandboxId: vue.searchForm.sandboxId })
        .then((res) => {
          vue.tableData = []
          if (res.data) {
            const list = res.data
            let obj = {}
            for (let i = 0; i < list.length; i++) {
              if (!list[i].userId) continue
              obj = {}
              for (let j = 0; j < vue.userOptions.length; j++) {
                if (vue.userOptions[j].value === list[i].userId) {
                  obj.email = vue.userOptions[j].label
                }
              }
              list[i].tokenList.forEach(item => {
                item.userId = list[i].userId
              })
              obj.userId = list[i].userId
              obj.sandboxId = list[i].sandboxId
              obj.tokenList = list[i].tokenList
              vue.tableData.push(obj)
            }
          }
        })
        .finally(() => {
          vue.loading = false
        })
    },
    getSuList() {
      const vue = this
      vue.loading = true
      const requestData = {
        email: '',
        company: '',
        role: '',
        startRegisterTime: '',
        endRegisterTime: '',
        pager: '',
        pageSize: ''
      }
      findUser(requestData)
        .then((res) => {
          if (res.data) {
            const list = res.data.items
            vue.userOptions = []
            for (let i = 0; i < list.length; i++) {
              if (list[i].email === '<EMAIL>') {
                console.log(list[i].email)
              }
              vue.userOptions.push({ 'value': list[i].id, 'label': list[i].email })
            }
            vue.initData()
          }
        })
        .finally(() => {
          vue.loading = false
        })
    },
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    handleSelectionChangeChild(val) {
      this.multipleSelectionChild = val
    },
    handleSizeChange: function(size) {
      this.pagesize = size
    },
    handleCurrentChange: function(currentPage) {
      this.currentPage = currentPage
    },
    // 通过ref属性重置表单
    resetForm(formName) {
      this.$refs[formName].resetFields()
    },
    // 外部批量删除
    deleteAllOut() {
      if (!this.multipleSelection.length) {
        this.$message.error(this.$t('soi.sandboxManagement.deleteUserTip'))
        return
      }
      this.$confirm(this.$t('soi.sandboxManagement.deleteTip'), this.$t('soi.common.warning'), {
        confirmButtonText: this.$t('soi.common.confirm'),
        cancelButtonText: this.$t('soi.common.cancel'),
        type: 'warning'
      }).then(() => {
        const arrOut = []
        this.multipleSelection.forEach(item => {
          item.tokenList.forEach(item2 => {
            arrOut.push({
              id: item2.id,
              userId: item.userId
            })
          })
        })

        this.loading = true
        deleteApiTokenByUserId(arrOut)
          .then((res) => {
            this.$message.success(res.message)
            this.initData()
          }).finally(() => {
            this.loading = false
          })
      }).catch(() => {})
    },
    // 内部批量删除
    deleteAllInside() {
      if (!this.multipleSelectionChild.length) {
        this.$message.error(this.$t('soi.sandboxManagement.deleteSandboxDataTip'))
        return
      }
      this.$confirm(this.$t('soi.sandboxManagement.deleteTip'), this.$t('soi.common.warning'), {
        confirmButtonText: this.$t('soi.common.confirm'),
        cancelButtonText: this.$t('soi.common.cancel'),
        type: 'warning'
      })
        .then(() => {
          const arrInside = []
          this.multipleSelectionChild.forEach(item => {
            arrInside.push({
              id: item.id,
              userId: item.userId
            })
          })

          this.loading = true
          deleteApiTokenByUserId(arrInside)
            .then((res) => {
              this.$message.success(res.message)
              this.initData()
            }).finally(() => {
              this.loading = false
            })
        })
        .catch(() => {})
    }
  }
}
</script>

<style lang="scss" scoped>
.practice-management-sandbox-management-container {
  .data-action-button-group {
    margin-bottom: 10px;
    display: flex;
    justify-content: space-between;
  }
}
</style>
