<template>
  <div v-loading.fullscreen.lock="loading" class="practice-management-document-list-container">
    <customize-card :title="$t('soi.router.documentList')">
      <template #header-actions>
        <el-button size="mini" @click="handlerCreateDocument()">{{ $t('soi.common.create') }}</el-button>
      </template>

      <el-table
        :data="documentList"
        :header-cell-style="{'background': '#109eae42', 'color': '#333'}"
        style="width: 100%"
        stripe
        highlight-current-row
      >
        <el-table-column type="index" label="#" width="50" />
        <el-table-column prop="docName" :label="$t('soi.domainPracticals.name')" min-width="100" align="center">
          <template slot-scope="scope">
            {{ ['zh', 'cht'].includes(language) ? scope.row.docName : scope.row.docNameEn }}
          </template>
        </el-table-column>
        <el-table-column :label="$t('soi.domainPracticals.document')" min-width="100" align="center">
          <template slot-scope="scope">
            <el-button v-if="['zh', 'cht'].includes(language) && scope.row.cnDocUrl" icon="el-icon-view" size="mini" @click="showDocument(scope.row)">
              {{ $t('soi.common.view') }}
            </el-button>
            <el-button v-if="['zh', 'cht'].includes(language) && scope.row.cnDocUrl" icon="el-icon-download" size="mini" @click="downloadDocument(scope.row)">
              {{ $t('soi.common.download') }}
            </el-button>

            <el-button v-if="['en'].includes(language) && scope.row.enDocUrl" icon="el-icon-view" size="mini" @click="showDocument(scope.row)">
              {{ $t('soi.common.view') }}
            </el-button>
            <el-button v-if="['en'].includes(language) && scope.row.enDocUrl" icon="el-icon-download" size="mini" @click="downloadDocument(scope.row)">
              {{ $t('soi.common.download') }}
            </el-button>
          </template>
        </el-table-column>
        <el-table-column :label="$t('soi.domainPracticals.video')" min-width="100" align="center">
          <template slot-scope="scope">
            <el-button v-if="['zh', 'cht'].includes(language) && scope.row.cnVideoUrl" icon="el-icon-view" size="mini" @click="showVideo(scope.row)">
              {{ $t('soi.common.view') }}
            </el-button>
            <el-button v-if="['zh', 'cht'].includes(language) && scope.row.cnVideoUrl" icon="el-icon-download" size="mini" @click="downloadVideo(scope.row)">
              {{ $t('soi.common.download') }}
            </el-button>

            <el-button v-if="['en'].includes(language) && scope.row.enVideoUrl" icon="el-icon-view" size="mini" @click="showVideo(scope.row)">
              {{ $t('soi.common.view') }}
            </el-button>
            <el-button v-if="['en'].includes(language) && scope.row.enVideoUrl" icon="el-icon-download" size="mini" @click="downloadVideo(scope.row)">
              {{ $t('soi.common.download') }}
            </el-button>
          </template>
        </el-table-column>

        <el-table-column :label="$t('soi.common.operate')" min-width="100" align="center">
          <template slot-scope="scope">
            <el-button size="mini" @click="handlerEditDocument(scope.row)">{{ $t('soi.common.edit') }}</el-button>
            <el-button size="mini" @click="handlerDeleteDocument(scope.row)">{{ $t('soi.common.delete') }}</el-button>
          </template>
        </el-table-column>
      </el-table>
    </customize-card>
  </div>
</template>
<script>
import CustomizeCard from '@/components/CustomizeCard/index.vue'
import { delRecord, getDocumentList } from '@/api/practice/document'
import { mapGetters } from 'vuex'
import { downloadMp4, downloadPdf } from '@/utils/download'

export default {
  name: 'PracticeManagementDocumentList',
  components: { CustomizeCard },
  data() {
    return {
      loading: false,
      classId: this.$route.params.classId,
      documentList: []
    }
  },
  computed: {
    ...mapGetters([
      'userDetails',
      'language'
    ])
  },
  mounted() {
    this.getData()
  },
  methods: {
    handlerCreateDocument() {
      localStorage.removeItem('document-data')
      this.$router.push({ name: 'practice-management-manage-document', params: { classId: this.classId }})
    },
    handlerEditDocument(row) {
      localStorage.setItem('document-data', JSON.stringify(row))
      this.$router.push({ name: 'practice-management-manage-document', params: { classId: this.classId }})
    },
    handlerDeleteDocument(data) {
      const vue = this
      this.$confirm(vue.$t('soi.documentManagement.deleteTip'), vue.$t('soi.common.tip'), {
        confirmButtonText: vue.$t('soi.common.confirm'),
        cancelButtonText: vue.$t('soi.common.cancel'),
        type: 'warning'
      })
        .then(() => {
          this.loading = true
          delRecord({ id: data.id })
            .then(res => {
              this.$message.success(res.message)
              this.getData()
            })
            .finally(() => {
              this.loading = false
            })
        })
        .catch(() => {})
    },
    getData() {
      const vue = this
      vue.loading = true
      getDocumentList({ classId: vue.classId, userId: vue.userDetails.email })
        .then(res => {
          vue.documentList = res.data
        })
        .finally(() => {
          vue.loading = false
        })
    },
    showDocument(row) {
      if (['cht', 'zh'].includes(this.language)) {
        if (!row.cnDocUrl.startsWith('https://')) {
          row.cnDocUrl = 'https://' + row.cnDocUrl
        }
        window.open(row.cnDocUrl)
      }
      if (['en'].includes(this.language)) {
        if (!row.enDocUrl.startsWith('https://')) {
          row.enDocUrl = 'https://' + row.enDocUrl
        }
        window.open(row.enDocUrl)
      }
    },
    downloadDocument(row) {
      if (['cht', 'zh'].includes(this.language)) {
        if (!row.cnDocUrl.startsWith('https://')) {
          row.cnDocUrl = 'https://' + row.cnDocUrl
        }
        downloadPdf(row.cnDocUrl, row.docName)
      }
      if (['en'].includes(this.language)) {
        if (!row.enDocUrl.startsWith('https://')) {
          row.enDocUrl = 'https://' + row.enDocUrl
        }
        downloadPdf(row.enDocUrl, row.docNameEn)
      }
    },
    showVideo(row) {
      if (['cht', 'zh'].includes(this.language)) {
        if (!row.cnVideoUrl.startsWith('https://')) {
          row.cnVideoUrl = 'https://' + row.cnVideoUrl
        }
        window.open(row.cnVideoUrl)
      }
      if (['en'].includes(this.language)) {
        if (!row.enVideoUrl.startsWith('https://')) {
          row.enVideoUrl = 'https://' + row.enVideoUrl
        }
        window.open(row.enVideoUrl)
      }
    },
    downloadVideo(row) {
      if (['cht', 'zh'].includes(this.language)) {
        if (!row.cnVideoUrl.startsWith('https://')) {
          row.cnVideoUrl = 'https://' + row.cnVideoUrl
        }

        downloadMp4(row.cnVideoUrl, row.docName)
      }
      if (['en'].includes(this.language)) {
        if (!row.enVideoUrl.startsWith('https://')) {
          row.enVideoUrl = 'https://' + row.enVideoUrl
        }
        downloadMp4(row.enVideoUrl, row.docNameEn)
      }
    }
  }
}
</script>
