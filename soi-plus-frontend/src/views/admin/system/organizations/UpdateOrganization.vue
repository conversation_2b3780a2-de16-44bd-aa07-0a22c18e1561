<template>
  <div class="update-organization-container">
    <organization-form
      :organization-form.sync="organization"
      :confirm-button-loading="confirmButtonLoading"
      @validated="updateOrganization()"
      @cancel="$emit('cancel')"
    />
  </div>
</template>

<script>
import OrganizationForm from '@/views/admin/system/organizations/OrganizationForm.vue'
import { getOrganizationDetails, updateOrganization } from '@/api/system/organization'

export default {
  name: 'UpdateOrganization',
  components: { OrganizationForm },
  props: {
    organizationId: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      confirmButtonLoading: false,
      organization: {},
      organizationOptions: []
    }
  },
  async mounted() {
    // 加载企业信息
    const { data } = await getOrganizationDetails(this.organizationId)
    this.organization = data
  },
  methods: {
    // 调用API更新企业
    updateOrganization() {
      this.confirmButtonLoading = true
      updateOrganization(this.organization)
        .then((res) => {
          this.$message.success(res.message)

          this.$emit('success')
        })
        .finally(() => {
          this.confirmButtonLoading = false
        })
    }
  }
}
</script>

<style scoped lang="scss">
.update-organization-container {
  padding: 0 20px;
}
</style>
