<template>
  <div v-loading="loading" class="app-container organization-management-container">
    <customize-card :title="$t('soi.router.organizationManagement')" :show-back-btn="false">

      <!-- 搜索表单 -->
      <el-form ref="searchForm" :model="searchForm" size="small" inline>
        <!-- 企业名称 -->
        <el-form-item :label="$t('soi.common.keywords')" prop="keywords">
          <el-input v-model="searchForm.keywords" :placeholder="$t('soi.organization.keywordsPlaceholder')" clearable />
        </el-form-item>

        <!-- 操作按钮 -->
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="getOrganizationList()">{{ $t('soi.common.search') }}</el-button>
          <el-button icon="el-icon-refresh" @click="resetForm('searchForm')">{{ $t('soi.common.reset') }}</el-button>
        </el-form-item>
      </el-form>

      <!-- 创建和批量删除按钮 -->
      <div class="data-action-button-group">
        <div>
          <el-button size="small" icon="el-icon-plus" type="primary" plain @click="openCreateOrganizationDialog()">{{ $t('soi.common.create') }}</el-button>
          <el-button size="small" icon="el-icon-delete" type="danger" plain :disabled="!multipleSelection.length" @click="batchDeleteOrganization()">{{ $t('soi.common.delete') }}</el-button>
        </div>

        <!-- 刷新按钮 -->
        <el-button icon="el-icon-refresh" size="small" circle @click="getOrganizationList()" />
      </div>

      <!-- 企业表格 -->
      <el-card shadow="never">
        <el-table v-loading="tableLoading" :data="organizationList" row-key="id" stripe :tree-props="{children: 'children', hasChildren: 'hasChildren'}" @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="55px" />
          <el-table-column type="index" label="#" align="center" />
          <el-table-column :label="$t('soi.organization.organizationId')" prop="organizationId" align="center" />
          <el-table-column :label="$t('soi.organization.organizationName')" prop="organizationName" align="center" />
          <el-table-column :label="$t('soi.organization.organizationAbbreviationCode')" prop="organizationAbbreviationCode" align="center" />
          <el-table-column :label="$t('soi.organization.organizationNature')" prop="organizationNature" align="center" />
          <el-table-column :label="$t('soi.common.createTime')" prop="createTime" align="center" />
          <el-table-column :label="$t('soi.common.operate')" align="center" width="193px">
            <template slot-scope="scope">
              <el-button type="text" size="mini" text icon="el-icon-edit" @click="openUpdateOrganizationDialog(scope.row.id)">{{ $t('soi.common.edit') }}</el-button>
              <el-button type="text" size="mini" text icon="el-icon-delete" @click="batchDeleteOrganization([scope.row.organizationId])">{{ $t('soi.common.delete') }}</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </customize-card>

    <!-- 创建企业的对话框 -->
    <el-dialog
      :title="$t('soi.organization.createOrganization')"
      :visible.sync="createOrganizationDialogVisible"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      width="60%"
    >
      <create-organization :key="createKey" @success="handlerCreateOrganizationSuccess()" @cancel="closeCreateOrganizationDialog()" />
    </el-dialog>

    <!-- 更新企业的对话框 -->
    <el-dialog
      :title="$t('soi.organization.editOrganization')"
      :visible.sync="updateOrganizationDialogVisible"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      width="60%"
    >
      <!-- key的作用: 重新刷新子组件 -->
      <update-organization
        :key="updateKey"
        :organization-id="updateOrganizationId"
        @success="handlerUpdateOrganizationSuccess()"
        @cancel="closeUpdateOrganizationDialog()"
      />
    </el-dialog>
  </div>
</template>

<script>
import CreateOrganization from '@/views/admin/system/organizations/CreateOrganization.vue'
import UpdateOrganization from '@/views/admin/system/organizations/UpdateOrganization.vue'
import { statusOptions } from '@/data'
import { batchDeleteOrganization, getOrganizationList } from '@/api/system/organization'
import CustomizeCard from '@/components/CustomizeCard/index.vue'

export default {
  name: 'OrganizationManagement',
  components: { CustomizeCard, UpdateOrganization, CreateOrganization },
  data() {
    return {
      // 页面loading
      loading: false,
      // 表格loading
      tableLoading: false,
      createKey: '',
      updateKey: '',
      // 状态选项
      statusOptions,
      // 分页查询请求参数
      searchForm: {
        keywords: ''
      },
      // 企业列表
      organizationList: [],
      // 创建企业的对话框标记：true 显示，false 隐藏
      createOrganizationDialogVisible: false,
      // 更新企业的对话框标记：true 显示，false 隐藏
      updateOrganizationDialogVisible: false,
      updateOrganizationId: '',
      multipleSelection: []
    }
  },
  mounted() {
    this.getOrganizationList()
  },
  methods: {
    // 删除企业
    batchDeleteOrganization(organizationIds) {
      this.$confirm(this.$t('soi.organization.deleteOrganizationPrompt'), this.$t('soi.common.tip'), {
        confirmButtonText: this.$t('soi.common.confirm'),
        cancelButtonText: this.$t('soi.common.cancel'),
        type: 'warning'
      }).then(() => {
        let deletedOrganizationIds = organizationIds

        if (!deletedOrganizationIds || !deletedOrganizationIds.length) {
          deletedOrganizationIds = this.multipleSelection.map((organization) => organization.organizationId)
        }

        this.loading = true
        batchDeleteOrganization(deletedOrganizationIds)
          .then((res) => {
            // 提示成功
            this.$message.success(res.message)

            // 重新加载企业列表
            this.getOrganizationList()
          })
          .finally(() => {
            this.loading = false
          })
      })
    },
    // 更新企业成功
    handlerUpdateOrganizationSuccess() {
      this.updateOrganizationDialogVisible = false
      this.getOrganizationList()
    },
    // 打开更新企业对话框
    openUpdateOrganizationDialog(organizationId) {
      this.updateOrganizationId = organizationId
      this.updateKey = String(new Date().getTime())
      this.updateOrganizationDialogVisible = true
    },
    // 关闭更新企业对话框
    closeUpdateOrganizationDialog() {
      this.updateOrganizationDialogVisible = false
    },
    // 创建企业成功
    handlerCreateOrganizationSuccess() {
      this.createOrganizationDialogVisible = false
      this.getOrganizationList()
    },
    // 调用API分页查询企业列表
    getOrganizationList() {
      this.tableLoading = true

      const requestData = { ...this.searchForm }

      if (this.searchForm.dateRange) {
        requestData.startTime = this.searchForm.dateRange[0]
        requestData.endTime = this.searchForm.dateRange[1]
      }

      delete requestData.dateRange

      getOrganizationList(requestData)
        .then((res) => {
          const { total, items: organizationList } = res.data

          this.organizationList = organizationList
          this.total = total
        })
        .finally(() => {
          this.tableLoading = false
        })
    },
    // 打开创建企业对话框
    openCreateOrganizationDialog() {
      this.createKey = String(new Date().getTime())
      this.createOrganizationDialogVisible = true
    },
    // 关闭创建企业对话框
    closeCreateOrganizationDialog() {
      this.createOrganizationDialogVisible = false
    },
    // 通过ref属性重置表单
    resetForm(formName) {
      this.$refs[formName].resetFields()
    },
    // 保存已选中的企业记录
    handleSelectionChange(val) {
      this.multipleSelection = val
    }
  }
}
</script>

<style scoped lang="scss">
.organization-management-container {
  .data-action-button-group {
    margin-bottom: 10px;
    display: flex;
    justify-content: space-between;
  }
}
</style>
