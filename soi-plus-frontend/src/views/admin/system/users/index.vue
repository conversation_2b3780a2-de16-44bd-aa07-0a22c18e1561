<template>
  <div v-loading="loading" class="app-container user-management-container">
    <customize-card :title="$t('soi.router.userManagement')" :show-back-btn="false">
      <!-- 搜索表单 -->
      <el-form ref="searchForm" :model="searchForm" size="small" inline>
        <!-- 邮箱 -->
        <el-form-item :label="$t('soi.common.keywords')" prop="keywords">
          <el-input v-model="searchForm.keywords" :placeholder="$t('soi.user.keywordsPlaceholder')" clearable />
        </el-form-item>

        <!-- 角色 -->
        <el-form-item :label="$t('soi.user.role')" prop="roleIds">
          <el-select v-model="searchForm.roleIds" :placeholder="$t('soi.user.rolePlaceholder')" clearable>
            <el-option v-for="item in roleOptions" :key="item.id" :label="item.roleName" :value="item.id" />
          </el-select>
        </el-form-item>

        <!-- 状态 -->
        <el-form-item :label="$t('soi.user.activeStatus')" prop="status">
          <el-select v-model="searchForm.status" :placeholder="$t('soi.user.activeStatusPlaceholder')" clearable>
            <el-option v-for="item in statusOptions" :key="item.value" :label="$t(item.label)" :value="item.value" />
          </el-select>
        </el-form-item>

        <!-- 黑名单 -->
        <el-form-item :label="$t('soi.user.isBlacklist')" prop="status">
          <el-select v-model="searchForm.blackList" :placeholder="$t('soi.user.isBlacklistPlaceholder')" clearable>
            <el-option v-for="item in blacklistOptions" :key="item.value" :label="$t(item.label)" :value="item.value" />
          </el-select>
        </el-form-item>

        <!-- 创建时间 -->
        <el-form-item :label="$t('soi.common.createDate')" prop="dateRange">
          <el-date-picker
            v-model="searchForm.dateRange"
            type="daterange"
            :range-separator="$t('soi.common.to')"
            value-format="yyyy-MM-dd HH:mm:ss"
            :start-placeholder="$t('soi.common.startDate')"
            :end-placeholder="$t('soi.common.endDate')"
            :default-time="['00:00:00', '23:59:59']"
            unlink-panels
          />
        </el-form-item>

        <!-- 操作按钮 -->
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="getUserList()">{{ $t('soi.common.search') }}</el-button>
          <el-button icon="el-icon-refresh" @click="resetForm('searchForm')">{{ $t('soi.common.reset') }}</el-button>
        </el-form-item>
      </el-form>

      <!-- 创建和批量删除按钮 -->
      <div class="data-action-button-group">
        <div>
          <el-button size="small" icon="el-icon-plus" type="primary" plain @click="openCreateUserDialog()">{{ $t('soi.common.create') }}</el-button>
          <el-button size="small" icon="el-icon-delete" type="danger" plain :disabled="!multipleSelection.length" @click="batchDeleteUser()">{{ $t('soi.common.delete') }}</el-button>
          <el-button size="small" icon="el-icon-upload2" type="warning" plain @click="openImportUserDialog()">{{ $t('soi.common.import') }}</el-button>
        </div>

        <el-button icon="el-icon-refresh" size="small" circle @click="getUserList()" />
      </div>

      <!-- 用户表格 -->
      <el-card shadow="never">
        <el-table v-loading="tableLoading" :data="userList" stripe @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="55px" />
          <el-table-column type="index" label="#" align="center" />
          <el-table-column :label="$t('soi.user.email')" prop="email" show-overflow-tooltip align="left" />
          <el-table-column :label="$t('soi.user.username')" prop="username" align="left" />
          <el-table-column :label="$t('soi.user.role')" prop="roles" align="left">
            <template slot-scope="scope">
              <el-tag v-for="role in scope.row.roles" :key="role" size="small" type="info" style="margin-right: 2px">
                {{ role }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column :label="$t('soi.user.isBlacklist')" prop="blackList" align="center">
            <template slot-scope="scope">
              <el-switch
                v-model="scope.row.blackList"
                active-color="#ff4949"
                :active-value="1"
                :inactive-value="0"
                :disabled="scope.row.switchStatusEnabled"
                @change="addBlacklist($event, scope.row)"
              />
            </template>
          </el-table-column>
          <el-table-column :label="$t('soi.user.activeStatus')" prop="status" align="center">
            <template slot-scope="scope">
              <span v-for="item in statusOptions" :key="item.value">
                <el-tag v-if="item.value === scope.row.status" size="small" :type="getStatusColor(scope.row.status)">
                  {{ $t(item.label) }}
                </el-tag>
              </span>
            </template>
          </el-table-column>
          <el-table-column :label="$t('soi.common.createTime')" prop="createTime" align="center" />
          <el-table-column :label="$t('soi.common.operate')" align="center" width="193px">
            <template slot-scope="scope">
              <el-button type="text" size="mini" text icon="el-icon-edit" @click="openUpdateUserDialog(scope.row.id)">{{ $t('soi.common.edit') }}</el-button>
              <el-button type="text" size="mini" text icon="el-icon-delete" @click="batchDeleteUser([scope.row.id])">{{ $t('soi.common.delete') }}</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>

      <div class="table-footer">
        <el-pagination
          :current-page.sync="searchForm.currentPage"
          :page-sizes="[20, 50, 100, 500, 1000]"
          :page-size.sync="searchForm.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="getUserList()"
          @current-change="getUserList()"
        />
      </div>
    </customize-card>

    <!-- 创建用户的对话框 -->
    <el-dialog
      :title="$t('soi.user.createUser')"
      :visible.sync="createUserDialogVisible"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      width="60%"
    >
      <create-user :key="createKey" @success="handlerCreateUserSuccess()" @cancel="closeCreateUserDialog()" />
    </el-dialog>

    <!-- 更新用户的对话框 -->
    <el-dialog
      :title="$t('soi.user.editUser')"
      :visible.sync="updateUserDialogVisible"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      width="60%"
    >
      <update-user :key="updateKey" :user-id="updateUserId" @success="handlerUpdateUserSuccess()" @cancel="closeUpdateUserDialog()" />
    </el-dialog>

    <!-- 批量导入用户 -->
    <el-dialog
      :title="$t('soi.user.importUser')"
      :visible.sync="importUserDialogVisible"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      width="1000px"
    >
      <div class="organization-select">
        <span style="margin-right: 20px">{{ $t('soi.user.pleaseSelectUserOrganization') }}</span>
        <el-select v-model="importUserForm.organizationId" :placeholder="$t('soi.user.organizationPlaceholder')" auto-complete="off" class="org-select">
          <el-option v-for="item in organizationOptions" :key="item.organizationId" :label="item.organizationName" :value="item.organizationId" />
        </el-select>
      </div>

      <div class="update-user-actions">
        <el-upload
          ref="importUser"
          action=""
          :file-list="importUserForm.files"
          :limit="1"
          name="file"
          :show-file-list="true"
          accept=".xlsx,.xls"
          list-type="text"
          :auto-upload="false"
          style="display: inline-block"
          :on-change="onChange"
          :before-remove="beforeRemove"
        >
          <el-button slot="trigger" type="primary" icon="el-icon-document" class="select-file">{{ $t('soi.user.selectFile') }}</el-button>
          <el-button type="primary" class="start-upload" icon="el-icon-upload2" :disabled="importUserForm.files.length === 0" @click="importUser()">{{ $t('soi.user.startImport') }}</el-button>
          <el-button type="primary" class="download-template" icon="el-icon-download" @click="downloadTemplateFile()">{{ $t('soi.user.downloadTemplate') }}</el-button>
        </el-upload>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { addBlacklist, batchDeleteUser, getUserList, importUser } from '@/api/system/user'
import { blacklistOptions, organizationOptions, roleOptions, statusOptions } from '@/data'
import CreateUser from '@/views/admin/system/users/CreateUser.vue'
import UpdateUser from '@/views/admin/system/users/UpdateUser.vue'
import { mapGetters } from 'vuex'
import CustomizeCard from '@/components/CustomizeCard/index.vue'
import { SOI_PLUS_CORE_API_URL } from '@/contains'

export default {
  name: 'UserManagement',
  components: { CustomizeCard, UpdateUser, CreateUser },
  data() {
    return {
      // 页面loading
      loading: false,
      // 表格loading
      tableLoading: false,
      createKey: '',
      updateKey: '',
      // 状态选项
      statusOptions,
      blacklistOptions,
      importUserForm: {
        organizationId: '',
        files: []
      },
      organizationOptions: [],
      total: 0,
      // 分页查询请求参数
      searchForm: {
        currentPage: 1,
        pageSize: 20,
        keywords: '',
        status: '',
        blackList: '',
        roleIds: '',
        dateRange: []
      },
      // 创建用户的对话框标记：true 显示，false 隐藏
      createUserDialogVisible: false,
      // 更新用户的对话框标记：true 显示，false 隐藏
      updateUserDialogVisible: false,
      importUserDialogVisible: false,
      updateUserId: '',
      // 已选中的用户记录
      multipleSelection: [],
      // 用户列表
      userList: [],
      // 角色列表
      roleOptions: []
    }
  },
  computed: {
    ...mapGetters(['language'])
  },
  async mounted() {
    // 页面初始化加载用户列表
    this.getUserList()

    // 加载角色列表
    this.roleOptions = await roleOptions()

    // 加载企业列表
    this.organizationOptions = await organizationOptions()
    this.importUserForm.organizationId = this.organizationOptions && this.organizationOptions.length ? this.organizationOptions[0].organizationId : ''
  },
  methods: {
    downloadTemplateFile() {
      window.open(
        `${SOI_PLUS_CORE_API_URL}/v1/system/template/download?fileName=user-import-template-${this.language}.xlsx`,
        '_self'
      )
    },
    onChange(file, fileList) {
      this.importUserForm.files = fileList
    },
    beforeRemove(file) {
      this.importUserForm.files.forEach((item, index) => {
        if (item === file) {
          this.importUserForm.files.splice(index, 1)
        }
      })
    },
    // 开始导入
    importUser() {
      if (!this.importUserForm.organizationId || !this.importUserForm.organizationId.length) {
        this.$message.error(this.$t('soi.user.pleaseSelectUserOrganization'))
        return
      }
      const formData = new FormData()
      this.importUserForm.files.forEach(item => {
        formData.append('file', item.raw)
      })
      formData.append('organizationId', this.importUserForm.organizationId)
      importUser(formData)
        .then((res) => {
          this.$message.success(res.message)
          this.importUserDialogVisible = false
          this.getUserList()
        })
    },
    // 打开导入用户对话款
    openImportUserDialog() {
      this.importUserDialogVisible = true
    },
    // 修改黑名单状态
    addBlacklist(blackList, user) {
      user.switchStatusEnabled = true
      addBlacklist({ blackList, id: user.id })
        .then((res) => {
          // 提示成功
          this.$message.success(res.message)
        })
        .finally(() => {
          user.switchStatusEnabled = false
        })
    },
    getStatusColor(value) {
      if (value === 0) {
        return 'danger'
      }
      return 'primary'
    },
    // 删除用户
    batchDeleteUser(userIds) {
      this.$confirm(this.$t('soi.user.deleteUserPrompt'), this.$t('soi.common.tip'), {
        confirmButtonText: this.$t('soi.common.confirm'),
        cancelButtonText: this.$t('soi.common.cancel'),
        type: 'warning'
      }).then(() => {
        let deletedUserIds = userIds

        if (!deletedUserIds || !deletedUserIds.length) {
          deletedUserIds = this.multipleSelection.map((user) => user.id)
        }

        this.loading = true
        batchDeleteUser(deletedUserIds)
          .then((res) => {
            // 提示成功
            this.$message.success(res.message)

            // 重新加载用户列表
            this.getUserList()
          })
          .finally(() => {
            this.loading = false
          })
      })
    },
    // 更新用户成功
    handlerUpdateUserSuccess() {
      this.updateUserDialogVisible = false
      this.getUserList()
    },
    // 打开更新用户对话框
    openUpdateUserDialog(userId) {
      // 暂存需要更新的用户信息，往子组件传
      this.updateUserId = userId
      this.updateKey = String(new Date().getTime())
      this.updateUserDialogVisible = true
    },
    // 关闭更新用户对话框
    closeUpdateUserDialog() {
      this.updateUserDialogVisible = false
    },
    // 创建用户成功
    handlerCreateUserSuccess() {
      this.createUserDialogVisible = false
      this.getUserList()
    },
    // 打开创建用户对话框
    openCreateUserDialog() {
      this.createKey = String(new Date().getTime())
      this.createUserDialogVisible = true
    },
    // 关闭创建用户对话框
    closeCreateUserDialog() {
      this.createUserDialogVisible = false
    },
    // 调用API分页查询用户列表
    getUserList() {
      this.tableLoading = true

      const requestData = { ...this.searchForm }

      if (this.searchForm.dateRange) {
        requestData.startTime = this.searchForm.dateRange[0]
        requestData.endTime = this.searchForm.dateRange[1]
      }

      delete requestData.dateRange

      getUserList(requestData)
        .then((res) => {
          const { total, items: userList } = res.data

          userList.forEach((_, index) => {
            userList[index].switchStatusEnabled = false
          })

          this.userList = userList
          this.total = total
        })
        .finally(() => {
          this.tableLoading = false
        })
    },
    // 通过ref属性重置表单
    resetForm(formName) {
      this.$refs[formName].resetFields()
    },
    // 保存已选中的用户记录
    handleSelectionChange(val) {
      this.multipleSelection = val
    }
  }
}
</script>
<style scoped lang="scss">
.user-management-container {
  .data-action-button-group {
    margin-bottom: 10px;
    display: flex;
    justify-content: space-between;
  }
  .role-tags:not(:last-child) {
    margin-right: 5px;
  }

  .update-user-actions,
  .organization-select {
    margin: 0 auto;
    width: 75%;
  }

  .organization-select {
    .org-select {
      margin-bottom: 20px;
    }
  }

  .update-user-actions {
    padding-bottom: 60px;
  }

  .download-template, .select-file, .start-upload {
    width: 220px;
    display: inline-block;
  }

  .select-file {
    margin: 0 10px 0 0;
  }
}
</style>
