<template>
  <div class="create-role-container">
    <role-form
      :role-form.sync="roleForm"
      :permission-options="permissionOptions"
      :confirm-button-loading="confirmButtonLoading"
      @validated="createRole()"
      @update-permission="updatePermission($event)"
      @cancel="$emit('cancel')"
    />
  </div>
</template>

<script>
import RoleForm from '@/views/admin/system/roles/RoleForm.vue'
import { createRole } from '@/api/system/role'
import { permissionOptions } from '@/data'

export default {
  name: 'CreateRole',
  components: { RoleForm },
  data() {
    return {
      confirmButtonLoading: false,
      roleForm: {
        roleName: '',
        remark: '',
        permissionIds: []
      },
      permissionOptions: []
    }
  },
  async mounted() {
    // 加载权限列表
    this.permissionOptions = await permissionOptions()
  },
  methods: {
    // 更新选中的权限
    updatePermission(permissionIds) {
      this.roleForm.permissionIds = permissionIds
    },
    // 调用API创建角色
    createRole() {
      this.confirmButtonLoading = true
      createRole(this.roleForm)
        .then((res) => {
          this.$message.success(res.message)

          this.$emit('success')
        })
        .finally(() => {
          this.confirmButtonLoading = false
        })
    }
  }
}
</script>

<style scoped lang="scss">
.create-role-container {
  padding: 0 20px;
}
</style>
