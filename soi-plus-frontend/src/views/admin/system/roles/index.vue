<template>
  <div v-loading="loading" class="app-container role-management-container">
    <customize-card :title="$t('soi.router.roleManagement')" :show-back-btn="false">
      <!-- 搜索表单 -->
      <el-form ref="searchForm" :model="searchForm" size="small" inline>
        <!-- 角色名称 -->
        <el-form-item :label="$t('soi.common.keywords')" prop="keywords">
          <el-input v-model="searchForm.keywords" :placeholder="$t('soi.role.keywordsPlaceholder')" clearable />
        </el-form-item>

        <!-- 创建时间 -->
        <el-form-item :label="$t('soi.common.createDate')" prop="dateRange">
          <el-date-picker
            v-model="searchForm.dateRange"
            type="daterange"
            :range-separator="$t('soi.common.to')"
            value-format="yyyy-MM-dd HH:mm:ss"
            :start-placeholder="$t('soi.common.startDate')"
            :end-placeholder="$t('soi.common.endDate')"
            :default-time="['00:00:00', '23:59:59']"
            unlink-panels
          />
        </el-form-item>

        <!-- 操作按钮 -->
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="getRoleList()">{{ $t('soi.common.search') }}</el-button>
          <el-button icon="el-icon-refresh" @click="resetForm('searchForm')">{{ $t('soi.common.reset') }}</el-button>
        </el-form-item>
      </el-form>

      <!-- 创建和批量删除按钮 -->
      <div class="data-action-button-group">
        <div>
          <el-button size="small" icon="el-icon-plus" type="primary" plain @click="openCreateRoleDialog()">{{ $t('soi.common.create') }}</el-button>
          <el-button size="small" icon="el-icon-delete" type="danger" plain :disabled="!multipleSelection.length" @click="batchDeleteRole()">{{ $t('soi.common.delete') }}</el-button>
        </div>

        <!-- 刷新按钮 -->
        <el-button icon="el-icon-refresh" size="small" circle @click="getRoleList()" />
      </div>

      <!-- 角色表格 -->
      <el-card shadow="never">
        <el-table v-loading="tableLoading" :data="roleList" stripe @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="55px" />
          <el-table-column type="index" label="#" align="center" />
          <el-table-column :label="$t('soi.role.roleName')" prop="roleName" show-overflow-tooltip align="left" />
          <el-table-column :label="$t('soi.role.remark')" prop="remark" show-overflow-tooltip align="left" />
          <el-table-column :label="$t('soi.common.createTime')" prop="createTime" align="center" />
          <el-table-column :label="$t('soi.common.operate')" align="center" width="193px">
            <template slot-scope="scope">
              <el-button type="text" size="mini" text icon="el-icon-edit" @click="openUpdateRoleDialog(scope.row.id)">{{ $t('soi.common.edit') }}</el-button>
              <el-button type="text" size="mini" text icon="el-icon-delete" @click="batchDeleteRole([scope.row.id])">{{ $t('soi.common.delete') }}</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>

      <div class="table-footer">
        <el-pagination
          :current-page.sync="searchForm.currentPage"
          :page-sizes="[20, 50, 100, 500, 1000]"
          :page-size.sync="searchForm.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="getRoleList()"
          @current-change="getRoleList()"
        />
      </div>
    </customize-card>

    <!-- 创建角色的对话框 -->
    <el-dialog
      :title="$t('soi.role.createRole')"
      :visible.sync="createRoleDialogVisible"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      width="60%"
    >
      <create-role :key="createKey" @success="handlerCreateRoleSuccess()" @cancel="closeCreateRoleDialog()" />
    </el-dialog>

    <!-- 更新角色的对话框 -->
    <el-dialog
      :title="$t('soi.role.editRole')"
      :visible.sync="updateRoleDialogVisible"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      width="60%"
    >
      <!-- key的作用: 重新刷新子组件 -->
      <update-role :key="updateKey" :role-id="updateRoleId" @success="handlerUpdateRoleSuccess()" @cancel="closeUpdateRoleDialog()" />
    </el-dialog>
  </div>
</template>

<script>
import CreateRole from '@/views/admin/system/roles/CreateRole.vue'
import UpdateRole from '@/views/admin/system/roles/UpdateRole.vue'
import { statusOptions } from '@/data'
import { batchDeleteRole, getRoleList } from '@/api/system/role'
import CustomizeCard from '@/components/CustomizeCard/index.vue'

export default {
  name: 'RoleManagement',
  components: { CustomizeCard, UpdateRole, CreateRole },
  data() {
    return {
      // 页面loading
      loading: false,
      // 表格loading
      tableLoading: false,
      createKey: '',
      updateKey: '',
      // 状态选项
      statusOptions,
      total: 0,
      // 分页查询请求参数
      searchForm: {
        currentPage: 1,
        pageSize: 20,
        keywords: '',
        dateRange: []
      },
      // 角色列表
      roleList: [],
      // 创建角色的对话框标记：true 显示，false 隐藏
      createRoleDialogVisible: false,
      // 更新角色的对话框标记：true 显示，false 隐藏
      updateRoleDialogVisible: false,
      updateRoleId: '',
      multipleSelection: []
    }
  },
  mounted() {
    this.getRoleList()
  },
  methods: {
    // 删除角色
    batchDeleteRole(roleIds) {
      this.$confirm(this.$t('soi.role.deleteRolePrompt'), this.$t('soi.common.tip'), {
        confirmButtonText: this.$t('soi.common.confirm'),
        cancelButtonText: this.$t('soi.common.cancel'),
        type: 'warning'
      }).then(() => {
        let deletedRoleIds = roleIds

        if (!deletedRoleIds || !deletedRoleIds.length) {
          deletedRoleIds = this.multipleSelection.map((role) => role.id)
        }

        this.loading = true
        batchDeleteRole(deletedRoleIds)
          .then((res) => {
            // 提示成功
            this.$message.success(res.message)

            // 重新加载角色列表
            this.getRoleList()
          })
          .finally(() => {
            this.loading = false
          })
      })
    },
    // 更新角色成功
    handlerUpdateRoleSuccess() {
      this.updateRoleDialogVisible = false
      this.getRoleList()
    },
    // 打开更新角色对话框
    openUpdateRoleDialog(roleId) {
      this.updateRoleId = roleId
      this.updateKey = String(new Date().getTime())
      this.updateRoleDialogVisible = true
    },
    // 关闭更新角色对话框
    closeUpdateRoleDialog() {
      this.updateRoleDialogVisible = false
    },
    // 创建角色成功
    handlerCreateRoleSuccess() {
      this.createRoleDialogVisible = false
      this.getRoleList()
    },
    // 调用API分页查询角色列表
    getRoleList() {
      this.tableLoading = true

      const requestData = { ...this.searchForm }

      if (this.searchForm.dateRange) {
        requestData.startTime = this.searchForm.dateRange[0]
        requestData.endTime = this.searchForm.dateRange[1]
      }

      delete requestData.dateRange

      getRoleList(requestData)
        .then((res) => {
          const { total, items: roleList } = res.data

          roleList.forEach((_, index) => {
            roleList[index].switchStatusEnabled = false
          })

          this.roleList = roleList
          this.total = total
        })
        .finally(() => {
          this.tableLoading = false
        })
    },
    // 打开创建角色对话框
    openCreateRoleDialog() {
      this.createKey = String(new Date().getTime())
      this.createRoleDialogVisible = true
    },
    // 关闭创建角色对话框
    closeCreateRoleDialog() {
      this.createRoleDialogVisible = false
    },
    // 通过ref属性重置表单
    resetForm(formName) {
      this.$refs[formName].resetFields()
    },
    // 保存已选中的角色记录
    handleSelectionChange(val) {
      this.multipleSelection = val
    }
  }
}
</script>

<style scoped lang="scss">
.role-management-container {
  .data-action-button-group {
    margin-bottom: 10px;
    display: flex;
    justify-content: space-between;
  }
}
</style>
