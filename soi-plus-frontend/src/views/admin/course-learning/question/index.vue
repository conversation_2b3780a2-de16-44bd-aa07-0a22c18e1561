<template>
  <div v-loading="loading" class="app-container question-container">
    <customize-card :title="$t('soi.router.question')" :show-back-btn="false">
      <!-- 搜索表单 -->
      <el-form ref="searchForm" :model="searchForm" size="small" inline>
        <!-- 题干内容 -->
        <el-form-item :label="$t('soi.common.keywords')" prop="keywords">
          <el-input v-model="searchForm.keywords" :placeholder="$t('soi.question.keywordsPlaceholder')" clearable />
        </el-form-item>

        <!-- 课程大分类列表 -->
        <el-form-item :label="$t('soi.courseCategory.courseCategory')" prop="courseCategoryId">
          <el-select
            v-model="searchForm.courseCategoryId"
            :placeholder="$t('soi.courseCategory.keywordsPlaceholder')"
            clearable
            @change="loadCurrentCourseList($event)"
          >
            <el-option v-for="item in courseCategoryOptions" :key="item.id" :label="item.courseCategory" :value="item.id" />
          </el-select>
        </el-form-item>

        <!-- 课程列表 -->
        <el-form-item :label="$t('soi.course.courseName')" prop="courseCode">
          <el-select
            v-model="searchForm.courseCode"
            :placeholder="$t('soi.course.keywordsPlaceholder')"
            clearable
            @change="loadCurrentSubjectList($event)"
          >
            <el-option v-for="item in currentCourseOptions" :key="item.id" :label="item.courseName" :value="item.courseCode" />
          </el-select>
        </el-form-item>

        <!-- 课题列表 -->
        <el-form-item :label="$t('soi.subject.subjectName')" prop="subjectCode">
          <el-select v-model="searchForm.subjectCode" :placeholder="$t('soi.subject.keywordsPlaceholder')" clearable>
            <el-option v-for="item in currentSubjectOptions" :key="item.id" :label="item.subjectName" :value="item.subjectCode" />
          </el-select>
        </el-form-item>

        <!-- 操作按钮 -->
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="getQuestionList()">{{ $t('soi.common.search') }}</el-button>
          <el-button icon="el-icon-refresh" @click="resetForm('searchForm')">{{ $t('soi.common.reset') }}</el-button>
        </el-form-item>
      </el-form>

      <!-- 创建和批量删除按钮 -->
      <div class="data-action-button-group">
        <div>
          <el-button size="small" icon="el-icon-plus" type="primary" plain @click="openCreateQuestionDialog()">{{ $t('soi.common.create') }}</el-button>
          <el-button size="small" icon="el-icon-delete" type="danger" plain :disabled="!multipleSelection.length" @click="batchDeleteQuestion()">{{ $t('soi.common.delete') }}</el-button>
        </div>

        <!-- 刷新按钮 -->
        <el-button icon="el-icon-refresh" size="small" circle @click="getQuestionList()" />
      </div>

      <!-- 试题练习表格 -->
      <el-card shadow="never">
        <el-table v-loading="tableLoading" :data="questionList" stripe @selection-change="handleSelectionChange">
          <el-table-column type="selection" width="55px" />
          <el-table-column type="index" label="#" align="center" />
          <el-table-column :label="$t('soi.courseCategory.courseCategory')" prop="courseCategory" align="left" />
          <el-table-column :label="$t('soi.course.courseName')" prop="courseName" align="left" />
          <el-table-column :label="$t('soi.subject.subjectName')" prop="subjectName" align="left" />
          <el-table-column :label="$t('soi.question.questionContent')" prop="questionContent" show-overflow-tooltip align="left" />
          <el-table-column :label="$t('soi.question.modelAnswer')" prop="questionModelanswer" show-overflow-tooltip align="left" />
          <el-table-column :label="$t('soi.common.creator')" prop="creator" align="center" />
          <el-table-column :label="$t('soi.common.updateBy')" prop="lastUpdateCreator" align="center" />
          <el-table-column :label="$t('soi.common.createTime')" prop="createTime" align="center" />
          <el-table-column :label="$t('soi.common.updateTime')" prop="lastUpdateTime" align="center" />
          <el-table-column :label="$t('soi.common.operate')" align="center" width="193px">
            <template slot-scope="scope">
              <el-button type="text" size="mini" text icon="el-icon-edit" @click="openUpdateQuestionDialog(scope.row.subjectCode,scope.row.id)">{{ $t('soi.common.edit') }}</el-button>
              <el-button type="text" size="mini" text icon="el-icon-delete" @click="batchDeleteQuestion([scope.row.id])">{{ $t('soi.common.delete') }}</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>

      <div class="table-footer">
        <el-pagination
          :current-page.sync="searchForm.currentPage"
          :page-sizes="[20, 50, 100, 500, 1000]"
          :page-size.sync="searchForm.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="getQuestionList()"
          @current-change="getQuestionList()"
        />
      </div>
    </customize-card>

    <!-- 创建试题练习的对话框 -->
    <el-dialog
      :title="$t('soi.question.createQuestion')"
      :visible.sync="createQuestionDialogVisible"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      width="60%"
    >
      <create-question :key="createKey" @success="handlerCreateQuestionSuccess()" @cancel="closeCreateQuestionDialog()" />
    </el-dialog>

    <!-- 更新试题练习的对话框 -->
    <el-dialog
      :title="$t('soi.question.editQuestion')"
      :visible.sync="updateQuestionDialogVisible"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      width="60%"
    >
      <!-- key的作用: 重新刷新子组件 -->
      <update-question :key="updateKey" :subject-code="updateSubjectCode" :question-id="updateQuestionId" @success="handlerUpdateQuestionSuccess()" @cancel="closeUpdateQuestionDialog()" />
    </el-dialog>
  </div>
</template>
<script>
import CustomizeCard from '@/components/CustomizeCard/index.vue'
import { batchDeleteQuestion, getQuestionList } from '@/api/system/question'
import { courseCategoryOptions, courseOptions, subjectOptions } from '@/data'
import CreateQuestion from '@/views/admin/course-learning/question/CreateQuestion.vue'
import UpdateQuestion from '@/views/admin/course-learning/question/UpdateQuestion.vue'

export default {
  name: 'Course',
  components: { UpdateQuestion, CreateQuestion, CustomizeCard },
  data() {
    return {
      // 页面loading
      loading: false,
      // 表格loading
      tableLoading: false,
      createKey: '',
      updateKey: '',
      detailKey: '',
      total: 0,
      // 分页查询请求参数
      searchForm: {
        currentPage: 1,
        pageSize: 20,
        keywords: '',
        courseCategoryId: '',
        courseCode: '',
        subjectCode: ''
      },
      // 试题练习列表
      questionList: [],
      // 创建试题练习的对话框标记：true 显示，false 隐藏
      createQuestionDialogVisible: false,
      // 更新试题练习的对话框标记：true 显示，false 隐藏
      updateQuestionDialogVisible: false,
      updateSubjectCode: '',
      updateQuestionId: '',
      multipleSelection: [],
      courseCategoryOptions: [],
      courseOptions: [],
      subjectOptions: [],
      currentCourseOptions: [],
      currentSubjectOptions: []
    }
  },
  async mounted() {
    // 页面初始化加载试题练习列表
    this.getQuestionList()

    // 加载课程大分类列表
    this.courseCategoryOptions = await courseCategoryOptions()
    // 加载课程列表
    this.courseOptions = await courseOptions()
    // 加载课题列表
    this.subjectOptions = await subjectOptions()
  },
  methods: {
    // 调用API分页查询试题练习列表
    getQuestionList() {
      this.tableLoading = true
      getQuestionList(this.searchForm)
        .then((res) => {
          const { total, items: questionList } = res.data

          this.questionList = questionList
          this.total = total
        })
        .finally(() => {
          this.tableLoading = false
        })
    },
    // 通过ref属性重置表单
    resetForm(formName) {
      this.$refs[formName].resetFields()
    },
    // 打开创建试题练习对话框
    openCreateQuestionDialog() {
      this.createKey = String(new Date().getTime())
      this.createQuestionDialogVisible = true
    },
    // 关闭创建试题练习对话框
    closeCreateQuestionDialog() {
      this.createQuestionDialogVisible = false
    },
    // 创建试题练习成功
    handlerCreateQuestionSuccess() {
      this.createQuestionDialogVisible = false
      this.getQuestionList()
    },
    // 打开更新试题练习对话框
    openUpdateQuestionDialog(subjectCode, id) {
      this.updateSubjectCode = subjectCode
      this.updateQuestionId = id
      this.updateKey = String(new Date().getTime())
      this.updateQuestionDialogVisible = true
    },
    // 关闭更新试题练习对话框
    closeUpdateQuestionDialog() {
      this.updateQuestionDialogVisible = false
    },
    // 更新试题练习成功
    handlerUpdateQuestionSuccess() {
      this.updateQuestionDialogVisible = false
      this.getQuestionList()
    },
    // 删除试题练习
    batchDeleteQuestion(questionIds) {
      this.$confirm(this.$t('soi.question.deleteQuestionPrompt'), this.$t('soi.common.tip'), {
        confirmButtonText: this.$t('soi.common.confirm'),
        cancelButtonText: this.$t('soi.common.cancel'),
        type: 'warning'
      }).then(() => {
        let deletedQuestionIds = questionIds

        if (!deletedQuestionIds || !deletedQuestionIds.length) {
          deletedQuestionIds = this.multipleSelection.map((course) => course.id)
        }

        this.loading = true
        batchDeleteQuestion(deletedQuestionIds)
          .then((res) => {
            // 提示成功
            this.$message.success(res.message)

            // 重新加载试题练习列表
            this.getQuestionList()
          })
          .finally(() => {
            this.loading = false
          })
      })
    },
    // 保存已选中的试题练习记录
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    // 切换课程大分类时，课程切换成与之对应的列表
    loadCurrentCourseList(id) {
      this.searchForm.courseCategoryId = id
      this.currentCourseOptions = this.courseOptions.filter(item => item.courseCategoryId === this.searchForm.courseCategoryId)
      this.searchForm.courseCode = ''
      this.currentSubjectOptions = []
      this.searchForm.subjectCode = ''
    },
    // 切换课程时，课题切换成与之对应的列表
    loadCurrentSubjectList(courseCode) {
      const currentCourse = this.currentCourseOptions.filter(item => item.courseCode === courseCode)
      if (currentCourse.length > 0) {
        const currentCourseId = currentCourse[0].id
        this.currentSubjectOptions = this.subjectOptions.filter(item => item.courseId === currentCourseId)
      } else {
        this.currentSubjectOptions = []
      }
      this.searchForm.subjectCode = ''
    }
  }
}
</script>

<style scoped lang="scss">
.question-container {
  .data-action-button-group {
    margin-bottom: 10px;
    display: flex;
    justify-content: space-between;
  }
}
</style>
