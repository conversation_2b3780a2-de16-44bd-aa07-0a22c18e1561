<template>
  <div class="create-question-container">
    <question-form
      v-if="show"
      :question-form.sync="questionForm"
      :course-category-options="courseCategoryOptions"
      :course-options="courseOptions"
      :subject-options="subjectOptions"
      :confirm-button-loading="confirmButtonLoading"
      flag="create"
      @validated="createQuestion()"
      @cancel="$emit('cancel')"
    />
  </div>
</template>

<script>
import QuestionForm from '@/views/admin/course-learning/question/QuestionForm.vue'
import { createQuestion } from '@/api/system/question'
import { courseCategoryOptions, courseOptions, subjectOptions } from '@/data'

export default {
  name: 'CreateQuestion',
  components: { QuestionForm },
  data() {
    return {
      show: false,
      confirmButtonLoading: false,
      questionForm: {
        courseCategoryId: '',
        courseCode: '',
        subjectCode: '',
        questionContent: '',
        questionModelanswer: ''
      },
      courseCategoryOptions: [],
      courseOptions: [],
      subjectOptions: []
    }
  },
  async mounted() {
    this.show = false
    // 加载课程大分类列表
    this.courseCategoryOptions = await courseCategoryOptions()
    // 加载课程列表
    this.courseOptions = await courseOptions()
    // 加载课题列表
    this.subjectOptions = await subjectOptions()
    this.show = true
  },
  methods: {
    // 调用API创建角色
    createQuestion() {
      this.confirmButtonLoading = true
      createQuestion(this.questionForm)
        .then((res) => {
          this.$message.success(res.message)

          this.$emit('success')
        })
        .finally(() => {
          this.confirmButtonLoading = false
        })
    }
  }
}
</script>

<style scoped lang="scss">
.create-question-container {
  padding: 0 20px;
}
</style>
