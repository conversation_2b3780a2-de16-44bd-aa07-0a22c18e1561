<template>
  <div class="create-course-category-container">
    <course-category-form
      :course-category-form.sync="courseCategoryForm"
      :confirm-button-loading="confirmButtonLoading"
      @validated="createCourseCategory()"
      @cancel="$emit('cancel')"
    />
  </div>
</template>

<script>
import CourseCategoryForm from '@/views/admin/course-learning/course-category/CourseCategoryForm.vue'
import { createCourseCategory } from '@/api/system/course-category'

export default {
  name: 'CreateCourseCategory',
  components: { CourseCategoryForm },
  data() {
    return {
      confirmButtonLoading: false,
      courseCategoryForm: {
        courseCategory: '',
        courseCategoryPicture: ''
      }
    }
  },
  methods: {
    // 调用API创建课程大分类
    createCourseCategory() {
      this.confirmButtonLoading = true
      createCourseCategory(this.courseCategoryForm)
        .then((res) => {
          this.$message.success(res.message)

          this.$emit('success')
        })
        .finally(() => {
          this.confirmButtonLoading = false
        })
    }
  }
}
</script>

<style scoped lang="scss">
.create-course-category-container {
  padding: 0 20px;
}
</style>
