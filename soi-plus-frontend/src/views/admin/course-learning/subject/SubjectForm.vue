<template>
  <!-- 课题表单 -->
  <el-form ref="subjectForm" :model="subjectForm" :rules="courseRules" size="small" label-width="220px" label-position="left" class="subject-form-container">
    <el-row :gutter="30">
      <el-col :span="24">
        <!-- 课程类型 -->
        <el-form-item :label="$t('soi.course.courseName')" prop="courseId">
          <el-select v-model="subjectForm.courseId" :placeholder="$t('soi.subject.courseNamePlaceholder')" clearable style="width: 100%;">
            <el-option v-for="item in courseOptions" :key="item.id" :label="item.courseName" :value="item.id" />
          </el-select>
        </el-form-item>
      </el-col>

      <!-- 课题名称 -->
      <el-col :span="24">
        <el-form-item :label="$t('soi.subject.subjectName')" prop="subjectName">
          <el-input v-model="subjectForm.subjectName" :placeholder="$t('soi.subject.subjectNamePlaceholder')" maxlength="64" clearable />
        </el-form-item>
      </el-col>

      <!-- 课题代码 -->
      <el-col :span="24">
        <el-form-item :label="$t('soi.subject.subjectCode')" prop="subjectCode">
          <el-input v-model="subjectForm.subjectCode" :placeholder="$t('soi.subject.subjectCodePlaceholder')" maxlength="64" clearable />
        </el-form-item>
      </el-col>

      <!-- 课题类型 -->
      <el-col :span="24">
        <el-form-item :label="$t('soi.subject.category')" prop="subjectCategory">
          <el-select v-model="subjectForm.subjectCategory" :placeholder="$t('soi.subject.categoryPlaceholder')" clearable style="width: 100%;">
            <el-option v-for="item in subjectCategoryOptions" :key="item.id" :label="$t(item.label)" :value="item.value" />
          </el-select>
        </el-form-item>
      </el-col>

      <!-- 课题时长 -->
      <el-col :span="24">
        <el-form-item :label="$t('soi.subject.subjectHours')" prop="subjectHours">
          <el-input v-model="subjectForm.subjectHours" :placeholder="$t('soi.subject.subjectHoursPlaceholder')" maxlength="64" clearable />
        </el-form-item>
      </el-col>

      <!-- 课题级别 -->
      <el-col :span="24">
        <el-form-item :label="$t('soi.subject.level')" prop="subjectLevel">
          <el-select v-model="subjectForm.subjectLevel" :placeholder="$t('soi.subject.levelPlaceholder')" clearable style="width: 100%;">
            <el-option v-for="item in subjectLevelOptions" :key="item.id" :label="$t(item.label)" :value="item.value" />
          </el-select>
        </el-form-item>
      </el-col>

      <!-- 课题目标 -->
      <el-col :span="24">
        <el-form-item :label="$t('soi.subject.objective')" prop="subjectObjective">
          <el-input v-model="subjectForm.subjectObjective" :placeholder="$t('soi.subject.objectivePlaceholder')" maxlength="64" clearable />
        </el-form-item>
      </el-col>

      <!-- 课题内容描述 -->
      <el-col :span="24">
        <el-form-item :label="$t('soi.subject.description')" prop="subjectDescription">
          <el-input
            v-model="subjectForm.subjectDescription"
            type="textarea"
            autosize
            maxlength="255"
            :placeholder="$t('soi.subject.descriptionPlaceholder')"
          />
        </el-form-item>
      </el-col>

      <!-- 课题图片 -->
      <el-col :span="24">
        <el-form-item ref="pictureUpload" :label="$t('soi.subject.pictureUpload')" prop="subjectPictureUrl">
          <el-image
            v-if="subjectForm.subjectPictureUrl"
            :src="subjectForm.subjectPictureUrl"
            class="avatar"
          />
          <el-upload
            ref="upload"
            class="avatar-uploader"
            :action="uploadUrl"
            :limit="1"
            accept=".jpg,.png"
            list-type="picture"
            :show-file-list="false"
            :on-success="handlePictureSuccess"
            :data="{ userID: userDetails.email }"
          >
            <el-button size="small" type="primary" class="upload-button">{{ $t('soi.common.upload') }}</el-button>
          </el-upload>
        </el-form-item>
      </el-col>

      <!-- 课题演示材料 -->
      <el-col :span="24">
        <el-form-item :label="$t('soi.subject.presentationMaterialUpload')" prop="materialsUrl">
          <el-upload
            ref="materialsUpload"
            :action="uploadUrl"
            :limit="1"
            :file-list="materialsFileList"
            :on-success="handleMaterialsSuccess"
            :data="{ userID: userDetails.email }"
            :on-preview="handlePreview"
          >
            <el-button size="small" type="primary" class="upload-button">{{ $t('soi.common.upload') }}</el-button>
          </el-upload>
        </el-form-item>
      </el-col>

      <!-- 课题视频 -->
      <el-col :span="24">
        <el-form-item :label="$t('soi.subject.videoLinkUpload')" prop="videoUrl">
          <el-upload
            ref="videoUpload"
            :action="uploadUrl"
            :limit="1"
            accept="video/*"
            :file-list="videoFileList"
            :on-success="handleVideoSuccess"
            :data="{ userID: userDetails.email }"
            :on-preview="handlePreview"
          >
            <el-button size="small" type="primary" class="upload-button">{{ $t('soi.common.upload') }}</el-button>
          </el-upload>
        </el-form-item>
      </el-col>

      <!-- 课题作业 -->
      <el-col :span="24">
        <el-form-item :label="$t('soi.subject.assignmentUpload')" prop="assignmentUrl">
          <el-upload
            ref="assignmentUpload"
            :action="uploadUrl"
            :limit="1"
            :file-list="assignmentFileList"
            :on-success="handleAssignmentSuccess"
            :data="{ userID: userDetails.email }"
            :on-preview="handlePreview"
          >
            <el-button size="small" type="primary" class="upload-button">{{ $t('soi.common.upload') }}</el-button>
          </el-upload>
        </el-form-item>
      </el-col>

      <el-col :span="24">
        <!-- 操作按钮 -->
        <el-form-item class="bottom-actions-button-group">
          <el-button @click="cancel()">{{ $t('soi.common.cancel') }}</el-button>
          <el-button type="primary" :loading="confirmButtonLoading" @click="validateForm('subjectForm')">
            {{ $t('soi.common.confirm') }}
          </el-button>
        </el-form-item>
      </el-col>
    </el-row>
  </el-form>
</template>

<script>
import { mapGetters } from 'vuex'
import { SOI_PLUS_BUSINESS_API_URL } from '@/contains'
import { subjectCategoryOptions, subjectLevelOptions } from '@/data'

export default {
  name: 'SubjectForm',
  props: {
    subjectForm: {
      type: Object,
      required: true
    },
    confirmButtonLoading: {
      type: Boolean,
      required: false,
      default: false
    },
    courseOptions: {
      type: Array,
      required: true
    }
  },
  data() {
    return {
      materialsFileList: [],
      videoFileList: [],
      assignmentFileList: [],
      subjectCategoryOptions,
      subjectLevelOptions,
      uploadUrl: SOI_PLUS_BUSINESS_API_URL + '/v1/practical/document/upload',
      // 校验规则
      courseRules: {
        courseId: [
          { required: true, trigger: 'change', message: this.$t('soi.course.courseNameIsRequired') }
        ],
        subjectCode: [
          { required: true, trigger: 'blur', message: this.$t('soi.subject.subjectCodeIsRequired') }
        ],
        subjectName: [
          { required: true, trigger: 'blur', message: this.$t('soi.subject.subjectNameIsRequired') }
        ],
        subjectCategory: [
          { required: true, trigger: 'change', message: this.$t('soi.subject.categoryIsRequired') }
        ],
        subjectHours: [
          { required: true, trigger: 'blur', message: this.$t('soi.subject.courseHoursIsRequired') }
        ],
        subjectLevel: [
          { required: true, trigger: 'change', message: this.$t('soi.subject.levelIsRequired') }
        ],
        subjectObjective: [
          { required: true, trigger: 'blur', message: this.$t('soi.subject.objectiveIsRequired') }
        ],
        subjectDescription: [
          { required: true, trigger: 'blur', message: this.$t('soi.subject.descriptionIsRequired') }
        ],
        subjectPictureUrl: [
          { required: true, trigger: 'change', message: this.$t('soi.subject.pictureUploadIsRequired') }
        ]
      }
    }
  },
  computed: {
    ...mapGetters([
      'userDetails'
    ])
  },
  mounted() {
    // 修改课题信息时初始化上传文件列表
    this.initUploadData()
  },
  methods: {
    initUploadData() {
      if (this.subjectForm.videoUrl) {
        const videoName = this.subjectForm.videoUrl.split('/').pop()
        const arr = []
        arr.push({ name: videoName, url: this.subjectForm.videoUrl })
        this.videoFileList = arr
      }
      if (this.subjectForm.materialsUrl) {
        const materialsName = this.subjectForm.materialsUrl.split('/').pop()
        const arr = []
        arr.push({ name: materialsName, url: this.subjectForm.materialsUrl })
        this.materialsFileList = arr
      }
      if (this.subjectForm.assignmentUrl) {
        const assignmentName = this.subjectForm.assignmentUrl.split('/').pop()
        const arr = []
        arr.push({ name: assignmentName, url: this.subjectForm.assignmentUrl })
        this.assignmentFileList = arr
        this.$refs.assignmentUpload.clearFiles()
      }
    },
    // 图片上传成功
    handlePictureSuccess(res) {
      this.subjectForm.subjectPictureUrl = res.data
      this.$refs.pictureUpload.clearValidate()
      this.$refs.upload.clearFiles()
    },
    // 演示材料上传成功
    handleMaterialsSuccess(res, file, fileList) {
      this.subjectForm.materialsUrl = res.data
      this.materialsFileList = fileList
      this.$refs.materialsUpload.clearFiles()
    },
    // 视频上传成功
    handleVideoSuccess(res, file, fileList) {
      this.subjectForm.videoUrl = res.data
      this.videoFileList = fileList
      this.$refs.videoUpload.clearFiles()
    },
    // 作业上传成功
    handleAssignmentSuccess(res, file, fileList) {
      this.subjectForm.assignmentUrl = res.data
      this.assignmentFileList = fileList
      this.$refs.assignmentUpload.clearFiles()
    },
    // 点击已上传成功的文件并打开
    handlePreview(file) {
      window.open(file.url)
    },
    // 验证表单
    validateForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          // 表单校验成功
          this.$emit('validated')
        }
      })
    },
    // 取消创建
    cancel() {
      this.$emit('cancel')
    }
  }
}
</script>

<style scoped lang="scss">
.subject-form-container{
  .bottom-actions-button-group {
    text-align: right;
  }
  .avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }
  .avatar-uploader .el-upload:hover {
    border-color: #409EFF;
  }
  .avatar {
    width: 180px;
    height: 100px;
    float: left;
    margin-right: 10px;
  }
  .upload-button{
    float: left;
  }
}
</style>

