<template>
  <div class="update-subject-container">
    <subject-form
      v-if="show"
      :subject-form.sync="subject"
      :course-options="courseOptions"
      :confirm-button-loading="confirmButtonLoading"
      @validated="updateSubject()"
      @cancel="$emit('cancel')"
    />
  </div>
</template>

<script>
import SubjectForm from '@/views/admin/course-learning/subject/SubjectForm.vue'
import { updateSubject, getSubjectDetails } from '@/api/system/subject'
import { courseOptions } from '@/data'

export default {
  name: 'UpdateSubject',
  components: { SubjectForm },
  props: {
    subjectCode: {
      type: String,
      required: true
    }
  },
  data() {
    return {
      show: false,
      confirmButtonLoading: false,
      subject: {},
      courseOptions: []
    }
  },
  async mounted() {
    this.show = false
    // 加载课题信息
    const { data } = await getSubjectDetails(this.subjectCode)
    this.subject = data
    // if (this.subject.subjectCategory === 'Business') {
    //   this.subject.subjectCategory = '1'
    // } else if (this.subject.subjectCategory === 'Technical') {
    //   this.subject.subjectCategory = '2'
    // }

    // 加载课程列表
    this.courseOptions = await courseOptions()
    this.show = true
  },
  methods: {
    // 调用API更新角色
    updateSubject() {
      this.confirmButtonLoading = true
      updateSubject(this.subject)
        .then((res) => {
          this.$message.success(res.message)

          this.$emit('success')
        })
        .finally(() => {
          this.confirmButtonLoading = false
        })
    }
  }
}
</script>

<style scoped lang="scss">
.update-subject-container {
  padding: 0 20px;
}
</style>
