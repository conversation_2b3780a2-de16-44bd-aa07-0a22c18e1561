<template>
  <div class="create-subject-container">
    <subject-form
      :subject-form.sync="subjectForm"
      :course-options="courseOptions"
      :confirm-button-loading="confirmButtonLoading"
      @validated="createSubject()"
      @cancel="$emit('cancel')"
    />
  </div>
</template>

<script>
import SubjectForm from '@/views/admin/course-learning/subject/SubjectForm.vue'
import { createSubject } from '@/api/system/subject'
import { courseOptions } from '@/data'

export default {
  name: 'CreateSubject',
  components: { SubjectForm },
  data() {
    return {
      confirmButtonLoading: false,
      subjectForm: {
        courseId: '',
        subjectCode: '',
        subjectName: '',
        subjectCategory: '',
        subjectHours: '',
        subjectLevel: '',
        subjectObjective: '',
        subjectDescription: '',
        subjectPictureUrl: '',
        materialsUrl: '',
        videoUrl: '',
        assignmentUrl: ''
      },
      courseOptions: []
    }
  },
  async mounted() {
    // 加载课程列表
    this.courseOptions = await courseOptions()
  },
  methods: {
    // 调用API创建角色
    createSubject() {
      this.confirmButtonLoading = true
      // if (this.subjectForm.subjectCategory === '1') {
      //   this.subjectForm.subjectCategory = 'Business'
      // } else if (this.subjectForm.subjectCategory === '2') {
      //   this.subjectForm.subjectCategory = 'Technical'
      // }
      console.log(this.subjectForm)
      createSubject(this.subjectForm)
        .then((res) => {
          this.$message.success(res.message)

          this.$emit('success')
        })
        .finally(() => {
          this.confirmButtonLoading = false
        })
    }
  }
}
</script>

<style scoped lang="scss">
.create-subject-container {
  padding: 0 20px;
}
</style>
