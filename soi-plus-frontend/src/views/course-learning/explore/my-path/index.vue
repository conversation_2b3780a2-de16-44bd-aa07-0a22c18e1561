<template>
  <div v-loading="loading" class="explore-my-path-container">
    <customize-card :title="$t('soi.courseLearning.myPath')">
      <el-tabs v-model="activeName" @tab-click="handleClick">
        <!-- My Path -->
        <el-tab-pane :label="$t('soi.courseLearning.myPath')" name="first">
          <div>
            <!-- 搜索表单 -->
            <el-form ref="myPathForm" :model="myPathForm" size="small" inline>
              <!-- 课题名称 -->
              <el-form-item :label="$t('soi.common.keywords')" prop="keywords">
                <el-input v-model="myPathForm.keywords" :placeholder="$t('soi.subject.keywordsPlaceholder')" clearable />
              </el-form-item>

              <!-- 课程大分类列表 -->
              <el-form-item :label="$t('soi.courseCategory.courseCategory')" prop="courseCategoryId">
                <el-select
                  v-model="myPathForm.courseCategoryId"
                  :placeholder="$t('soi.courseCategory.keywordsPlaceholder')"
                  clearable
                  @change="loadCurrentCourseList($event)"
                >
                  <el-option v-for="item in courseCategoryOptions" :key="item.id" :label="item.courseCategory" :value="item.id" />
                </el-select>
              </el-form-item>

              <!-- 课程列表 -->
              <el-form-item :label="$t('soi.course.courseName')" prop="courseCode">
                <el-select
                  v-model="myPathForm.courseCode"
                  :placeholder="$t('soi.course.keywordsPlaceholder')"
                  clearable
                >
                  <el-option v-for="item in currentCourseOptions" :key="item.id" :label="item.courseName" :value="item.courseCode" />
                </el-select>
              </el-form-item>

              <!-- 操作按钮 -->
              <el-form-item>
                <el-button type="primary" icon="el-icon-search" @click="getPathList()">{{ $t('soi.common.search') }}</el-button>
                <el-button icon="el-icon-refresh" @click="resetForm('myPathForm')">{{ $t('soi.common.reset') }}</el-button>
                <el-button icon="el-icon-delete" type="danger" :disabled="!multipleSelection.length" @click="batchDeletePath()">{{ $t('soi.common.delete') }}</el-button>
              </el-form-item>
            </el-form>

            <!-- 课程表格 -->
            <el-card shadow="never">
              <el-table v-loading="tableLoading" :data="pathList" stripe :header-cell-style="{'background': '#109eae42', 'color': '#333'}" @selection-change="handleSelectionChange">
                <el-table-column type="selection" :selectable="selectable" width="55px" />
                <el-table-column type="index" label="#" align="center" />
                <el-table-column :label="$t('soi.courseCategory.courseCategory')" prop="courseCategory" show-overflow-tooltip align="left" />
                <el-table-column :label="$t('soi.course.courseName')" prop="courseName" align="left" />
                <el-table-column :label="$t('soi.subject.subjectName')" prop="subjectName" align="left" />
                <el-table-column :label="$t('soi.common.createTime')" prop="createTime" align="center" />
                <el-table-column :label="$t('soi.common.creator')" prop="creator" align="center" />
                <el-table-column :label="$t('soi.courseLearning.completeStatus')" prop="completeStatus" align="center" />
                <el-table-column :label="$t('soi.courseLearning.firstStudyTime')" prop="firstStudyTime" align="center" />
                <el-table-column :label="$t('soi.common.operate')" align="center" width="193px">
                  <template slot-scope="scope">
                    <el-button type="text" size="mini" text @click="openDetailDialog(scope.row)">{{ $t('soi.common.details') }}</el-button>
                    <el-button v-if="scope.row.completeStatus === 'Pending'" type="text" size="mini" text icon="el-icon-delete" @click="batchDeletePath([scope.row.id])">{{ $t('soi.common.delete') }}</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-card>

            <div class="table-footer">
              <el-pagination
                :current-page.sync="myPathForm.currentPage"
                :page-sizes="[20, 50, 100, 500, 1000]"
                :page-size.sync="myPathForm.pageSize"
                layout="total, sizes, prev, pager, next, jumper"
                :total="total"
                @size-change="getPathList()"
                @current-change="getPathList()"
              />
            </div>

            <!-- 课题详情的对话框 -->
            <el-dialog
              :title="$t('soi.subject.subjectDetail')"
              :visible.sync="subjectDetailDialogVisible"
              :close-on-press-escape="false"
              :close-on-click-modal="false"
              width="50%"
              @close="closeDetailDialog"
            >
              <el-descriptions v-if="detailShow" class="margin-top" :column="1" border :label-style="{width:'30%'}">
                <!-- 课程名称 -->
                <el-descriptions-item :label="$t('soi.course.courseName')" prop="courseName">
                  {{ subjectDetail.courseName }}
                </el-descriptions-item>

                <!-- 课题名称 -->
                <el-descriptions-item :label="$t('soi.subject.subjectName')" prop="subjectName">
                  {{ subjectDetail.subjectName }}
                </el-descriptions-item>

                <!-- 课题类型 -->
                <el-descriptions-item :label="$t('soi.subject.category')" prop="subjectCategory">
                  {{ $t(subjectCategory) }}
                </el-descriptions-item>

                <!-- 课题时长 -->
                <el-descriptions-item :label="$t('soi.subject.subjectHours')" prop="subjectHours">
                  {{ subjectDetail.subjectHours }}
                </el-descriptions-item>

                <!-- 课题级别 -->
                <el-descriptions-item :label="$t('soi.subject.level')" prop="subjectLevel">
                  {{ $t(subjectLevel) }}
                </el-descriptions-item>

                <!-- 课题目标 -->
                <el-descriptions-item :label="$t('soi.subject.objective')" prop="subjectObjective">
                  {{ subjectDetail.subjectObjective }}
                </el-descriptions-item>

                <!-- 课题内容描述 -->
                <el-descriptions-item :label="$t('soi.subject.description')" prop="subjectDescription">
                  {{ subjectDetail.subjectDescription }}
                </el-descriptions-item>

                <!-- 课题图片 -->
                <el-descriptions-item :label="$t('soi.subject.picture')" prop="subjectPictureUrl">
                  <el-image
                    v-if="subjectDetail.subjectPictureUrl"
                    class="subjectPicture"
                    :src="subjectDetail.subjectPictureUrl"
                    style="width:180px;height: 100px"
                  />
                </el-descriptions-item>

                <!-- 课题演示材料 -->
                <el-descriptions-item :label="$t('soi.subject.presentationMaterial')" prop="materialsUrl">
                  <el-button v-if="subjectDetail.materialsUrl" size="mini" @click="viewDetail(subjectDetail.materialsUrl)">{{ $t('soi.common.view') }}</el-button>
                </el-descriptions-item>

                <!-- 课题视频 -->
                <el-descriptions-item :label="$t('soi.subject.video')" prop="videoUrl">
                  <el-button v-if="subjectDetail.videoUrl" size="mini" @click="viewVideo(subjectDetail)">{{ $t('soi.common.view') }}</el-button>
                </el-descriptions-item>

                <!-- 课题作业 -->
                <el-descriptions-item :label="$t('soi.subject.assignment')" prop="assignmentUrl">
                  <el-button v-if="subjectDetail.assignmentUrl" size="mini" @click="viewDetail(subjectDetail.assignmentUrl)">{{ $t('soi.common.view') }}</el-button>
                </el-descriptions-item>

                <!-- 创建时间 -->
                <el-descriptions-item :label="$t('soi.common.createTime')" prop="createTime">
                  {{ subjectDetail.createTime }}
                </el-descriptions-item>

                <!-- 更新时间 -->
                <el-descriptions-item :label="$t('soi.common.updateTime')" prop="lastUpdateTime">
                  {{ subjectDetail.lastUpdateTime }}
                </el-descriptions-item>

                <!-- 创建时间 -->
                <el-descriptions-item :label="$t('soi.common.creator')" prop="creator">
                  {{ subjectDetail.creator }}
                </el-descriptions-item>

                <!-- 更新时间 -->
                <el-descriptions-item :label="$t('soi.common.updateBy')" prop="lastUpdateCreator">
                  {{ subjectDetail.lastUpdateCreator }}
                </el-descriptions-item>
              </el-descriptions>
            </el-dialog>

            <el-dialog
              :title="$t('soi.courseLearning.video')"
              :visible.sync="videoDialogVisible"
              :close-on-press-escape="false"
              :close-on-click-modal="false"
              width="60%"
            >
              <video ref="videoPlayer" controls width="100%" autoplay @ended="onVideoEnded()">
                <source :src="videoData.videoUrl" type="video/mp4">
              </video>
            </el-dialog>
          </div>
        </el-tab-pane>
        <!-- Path Maintenance -->
        <el-tab-pane :label="$t('soi.courseLearning.pathMaintenance')" name="second">
          <!-- 用户基本信息 -->
          <el-descriptions :title="$t('soi.courseLearning.myPath')" :column="2">
            <el-descriptions-item :label="$t('soi.courseLearning.name')">{{ userDetails.username }}</el-descriptions-item>
            <el-descriptions-item :label="$t('soi.courseLearning.studentOrStaffNumber')">{{ userDetails.studentOrStaffNumber? userDetails.studentOrStaffNumber: '' }}</el-descriptions-item>
            <el-descriptions-item :label="$t('soi.courseLearning.job')">{{ userDetails.jobTitle? userDetails.jobTitle: '' }}</el-descriptions-item>
            <el-descriptions-item :label="$t('soi.courseLearning.role')">{{ userDetails.roles.join(',') }}</el-descriptions-item>
          </el-descriptions>

          <!-- 系统推荐表格 -->
          <el-card shadow="never" style="margin-top: 20px">
            <el-table
              v-loading="recommendTableLoading"
              :data="systemRecommendationList"
              stripe
              :header-cell-style="{'background': '#109eae42', 'color': '#333'}"
              :header-cell-class-name="cellClass"
              @selection-change="handleRecommendSelectionChange"
            >
              <el-table-column :label="$t('soi.course.courseName')" prop="courseName" align="left" />
              <el-table-column :label="$t('soi.subject.subjectName')" prop="subjectName" align="left" />
              <el-table-column :label="$t('soi.common.details')" align="left">
                <template slot-scope="scope">
                  <el-button type="text" size="mini" text @click="openRecommendDetailDialog(scope.row.subjectCode)">{{ $t('soi.common.view') }}</el-button>
                </template>
              </el-table-column>
              <el-table-column width="100" type="selection" />
            </el-table>

            <el-button v-if="systemRecommendationList.length" class="subscribe-btn" @click="subscribeCourse()">{{ $t('soi.courseLearning.subscribe') }}</el-button>

            <!-- 课题详情的对话框 -->
            <el-dialog
              :title="$t('soi.subject.subjectDetail')"
              :visible.sync="recommendDetailDialogVisible"
              :close-on-press-escape="false"
              :close-on-click-modal="false"
              width="50%"
            >
              <el-descriptions v-if="recommendDetailShow" class="margin-top" :column="1" border :label-style="{width:'30%'}">
                <!-- 课题名称 -->
                <el-descriptions-item :label="$t('soi.subject.subjectName')" prop="subjectName">
                  {{ recommendDetail.subjectName }}
                </el-descriptions-item>

                <!-- 所属课程 -->
                <el-descriptions-item :label="$t('soi.subject.courseName')" prop="courseName">
                  {{ recommendDetail.courseName }}
                </el-descriptions-item>

                <!-- 课题时长 -->
                <el-descriptions-item :label="$t('soi.subject.subjectHours')" prop="subjectHours">
                  {{ $t(recommendSubjectCategory) }}
                </el-descriptions-item>

                <!-- 课题级别 -->
                <el-descriptions-item :label="$t('soi.subject.level')" prop="subjectLevel">
                  {{ $t(recommendSubjectLevel) }}
                </el-descriptions-item>

                <!-- 课题目标 -->
                <el-descriptions-item :label="$t('soi.subject.objective')" prop="subjectObjective">
                  {{ recommendDetail.subjectObjective }}
                </el-descriptions-item>

                <!-- 课题内容描述 -->
                <el-descriptions-item :label="$t('soi.subject.description')" prop="subjectDescription">
                  {{ recommendDetail.subjectDescription }}
                </el-descriptions-item>
              </el-descriptions>
            </el-dialog>
          </el-card>
        </el-tab-pane>
      </el-tabs>
    </customize-card>
  </div>
</template>

<script>
import CustomizeCard from '@/components/CustomizeCard/index.vue'
import { courseCategoryOptions, courseOptions, subjectLevelOptions, subjectCategory } from '@/data'
import { getPathList, batchDeletePath, getSubjectRecommend, subscribeCourse, firstStudySubject, finishedStudySubject } from '@/api/course-learning/my-path'
import { mapGetters } from 'vuex'
import { getSubjectDetails } from '@/api/system/subject'

export default {
  name: 'CourseLearningMyPath',
  components: { CustomizeCard },
  data() {
    return {
      activeName: 'first',
      // 页面loading
      loading: false,
      // ‘我的学习路径’表格loading
      tableLoading: false,
      // 课题详情页面loading
      detailShow: false,
      total: 0,
      // ‘我的学习路径’分页查询请求参数
      myPathForm: {
        currentPage: 1,
        pageSize: 20,
        keywords: '',
        courseCategoryId: '',
        courseCode: ''
      },
      // ‘我的学习路径’列表
      pathList: [],
      multipleSelection: [],
      courseCategoryOptions: [],
      courseOptions: [],
      currentCourseOptions: [],
      // 课题详情的对话框标记：true 显示，false 隐藏
      subjectDetailDialogVisible: false,
      subjectDetail: {},
      // 系统推荐课程列表
      systemRecommendationList: [],
      // 系统推荐表格loading
      recommendTableLoading: false,
      recommendDetail: [],
      recommendDetailDialogVisible: false,
      recommendDetailShow: false,
      recommendMultipleSelection: [],
      videoDialogVisible: false,
      videoData: {},
      firstStudyTime: '',
      subjectLevel: '',
      recommendSubjectLevel: '',
      subjectCategory: '',
      recommendSubjectCategory: ''
    }
  },
  computed: {
    ...mapGetters(['userDetails'])
  },
  async mounted() {
    // 页面初始化加载‘我的学习路径’列表
    this.getPathList()

    // 加载课程大分类列表
    this.courseCategoryOptions = await courseCategoryOptions()
    // 加载课程列表
    this.courseOptions = await courseOptions()

    // 监听视频是否播放完成
    this.$watch('$refs.videoPlayer.currentTime', (newTime, oldTime) => {
      if (newTime >= this.$refs.videoPlayer.duration) {
        this.onVideoEnded()
      }
    })
  },
  methods: {
    handleClick(tab) {
      if (tab.name === 'second') {
        this.recommendTableLoading = true
        getSubjectRecommend()
          .then((res) => {
            this.systemRecommendationList = res.data
          })
          .finally(() => {
            this.recommendTableLoading = false
          })
      }
    },
    // 调用API分页查询‘我的学习路径’列表
    getPathList() {
      this.tableLoading = true
      getPathList(this.myPathForm)
        .then((res) => {
          const { total, items: pathList } = res.data

          this.pathList = pathList
          this.total = total
        })
        .finally(() => {
          this.tableLoading = false
        })
    },
    // 通过ref属性重置表单
    resetForm(formName) {
      this.$refs[formName].resetFields()
    },
    // 删除‘我的学习路径’
    batchDeletePath(pathIds) {
      this.$confirm(this.$t('soi.courseLearning.deletePathPrompt'), this.$t('soi.common.tip'), {
        confirmButtonText: this.$t('soi.common.confirm'),
        cancelButtonText: this.$t('soi.common.cancel'),
        type: 'warning'
      }).then(() => {
        let deletedPathIds = pathIds

        if (!deletedPathIds || !deletedPathIds.length) {
          deletedPathIds = this.multipleSelection.map((path) => path.id)
        }

        this.loading = true
        batchDeletePath(deletedPathIds)
          .then((res) => {
            // 提示成功
            this.$message.success(res.message)

            // 重新加载‘我的学习路径’列表
            this.getPathList()
          })
          .finally(() => {
            this.loading = false
          })
      })
    },
    // 切换课程大分类时，课程切换成与之对应的列表
    loadCurrentCourseList(id) {
      this.myPathForm.courseCategoryId = id
      this.currentCourseOptions = this.courseOptions.filter(item => item.courseCategoryId === this.myPathForm.courseCategoryId)
      this.myPathForm.courseCode = ''
    },
    // 保存已选中的课程记录
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    // 打开‘我的学习路径’详情对话框
    openDetailDialog(row) {
      this.subjectDetailDialogVisible = true
      this.firstStudyTime = row.firstStudyTime
      this.detailShow = false
      getSubjectDetails(row.subjectCode)
        .then((res) => {
          this.subjectDetail = res.data
          // 获取当前课题类型名称
          this.subjectCategory = subjectCategory[this.subjectDetail.subjectCategory]
          // 获取当前课题级别名称
          const currentSubjectLevel = subjectLevelOptions.filter(item => item.value === this.subjectDetail.subjectLevel)
          this.subjectLevel = currentSubjectLevel[0].label
        })
        .finally(() => {
          this.detailShow = true
        })
    },
    closeDetailDialog() {
      this.subjectDetailDialogVisible = false
      this.videoData = {}
      this.firstStudyTime = ''
      this.getPathList()
    },
    // 打开‘系统推荐’详情对话框
    openRecommendDetailDialog(subjectCode) {
      this.recommendDetailDialogVisible = true
      this.recommendDetailShow = false
      getSubjectDetails(subjectCode)
        .then((res) => {
          this.recommendDetail = res.data
          // 获取当前课题类型名称
          this.recommendSubjectCategory = subjectCategory[this.recommendDetail.subjectCategory]
          // 获取当前课题级别名称
          const currentSubjectLevel = subjectLevelOptions.filter(item => item.value === this.recommendDetail.subjectLevel)
          this.recommendSubjectLevel = currentSubjectLevel[0].label
        })
        .finally(() => {
          this.recommendDetailShow = true
        })
    },
    // 查看文件详细内容
    viewDetail(url) {
      window.open(url)
    },
    // 保存系统推荐已选中的课程记录
    handleRecommendSelectionChange(val) {
      this.recommendMultipleSelection = val
    },
    // 订阅系统推荐课程
    subscribeCourse() {
      const subscribeCourseList = this.recommendMultipleSelection.map((course) => {
        return {
          courseCode: course.courseCode,
          subjectCode: course.subjectCode,
          subscriptionStatus: 1
        }
      })
      this.loading = true
      subscribeCourse(subscribeCourseList)
        .then((res) => {
          // 提示成功
          this.$message.success(res.message)

          // 重新加载‘我的学习路径’列表
          this.getPathList()
          // 回到‘我的学习路径’列表页面
          this.activeName = 'first'
        })
        .finally(() => {
          this.loading = false
        })
    },
    // 点击view播放视频
    viewVideo(subjectDetail) {
      this.videoData = subjectDetail
      this.videoDialogVisible = true
      const data = {
        courseCode: this.videoData.courseCode,
        subjectCode: this.videoData.subjectCode
      }
      if (!this.firstStudyTime) {
        firstStudySubject(data)
      }
    },
    // 处理视频播放完成的逻辑
    onVideoEnded() {
      const data = {
        courseCode: this.videoData.courseCode,
        subjectCode: this.videoData.subjectCode
      }
      console.log()
      finishedStudySubject(data)
    },
    // 给复选框那一列添加 类名为 ‘disabledCheck’
    cellClass(row) {
      if (row.columnIndex === 3) {
        return 'disabledCheck'
      }
    },
    selectable(row, index) {
      return row.completeStatus !== 'Done'
    }
  }
}
</script>

<style scoped lang="scss">
.explore-my-path-container {
  .data-action-button {
    margin-bottom: 10px;
    display: flex;
    justify-content: space-between;
  }

  .subscribe-btn{
    float: right;
    margin:20px 0;
  }
}

/* 去掉全选按钮 */
::v-deep .el-table .disabledCheck .cell .el-checkbox__inner {
  display: none !important;
}

/* 修改全选框那部分内容 */
::v-deep .el-table .disabledCheck .cell::before {
  content: 'Select';
}
</style>
