<template>
  <div class="data-analysis-financial-data-characteristics-container">
    <customize-card :title="$t('soi.router.financialDataCharacteristics')">
      <el-row type="flex" justify="center">
        <el-col :span="20">
          <h1 class="api-title">{{ $t('soi.router.financialDataCharacteristics') }}</h1>
          <h4 class="tip-item">{{ $t('soi.financialDataCharacteristics.description1') }}</h4>
          <h4 class="tip-item">{{ $t('soi.financialDataCharacteristics.description2') }}</h4>
          <div class="image-item">
            <img width="60%" :src="require(`@/assets/images/data-characteristics1-${language}.png`)" alt="">
          </div>
          <h4 class="tip-item">{{ $t('soi.financialDataCharacteristics.description3') }}</h4>
          <h1 class="api-title">{{ $t('soi.financialDataCharacteristics.bankingServicesAndData') }}</h1>
          <h4 class="tip-item">{{ $t('soi.financialDataCharacteristics.description4') }}</h4>
          <h4 class="tip-item">{{ $t('soi.financialDataCharacteristics.description5') }}</h4>
          <div class="image-item">
            <img width="50%" :src="require(`@/assets/images/data-characteristics2-${language}.png`)" alt="">
          </div>
          <h4 class="tip-item">{{ $t('soi.financialDataCharacteristics.description6') }}</h4>
        </el-col>
      </el-row>
    </customize-card>
  </div>
</template>

<script>
import CustomizeCard from '@/components/CustomizeCard/index.vue'
import { mapGetters } from 'vuex'

export default {
  components: { CustomizeCard },
  computed: {
    ...mapGetters([
      'language'
    ])
  }
}
</script>

<style lang="scss" scoped>
.data-analysis-financial-data-characteristics-container {
  .api-title {
    margin: 30px 0;
    text-align: center;
  }

  .subtitle {
    margin: 20px 0;
  }

  .tip-item {
    margin: 10px 0 ;
    line-height: 2;
    font-size: 16px;
    font-weight: 400;
  }

  .image-item {
    margin: 60px auto;
    text-align: center;
  }
}
</style>
