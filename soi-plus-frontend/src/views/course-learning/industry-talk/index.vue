<template>
  <div class="maintenance-page">
    <h1>网站正在维护中</h1>
    <p>我们的工程师们正在努力修复问题，请稍后再试。</p>
  </div>
</template>
<!--<template>-->
<!--  <div v-loading="loading">-->
<!--    <customize-card title="行业讨论">-->
<!--      <div>-->
<!--        <el-select-->
<!--          v-model="defaultOption"-->
<!--          style="width: 200px;"-->
<!--          clearable-->
<!--        >-->
<!--          <el-option-->
<!--            v-for="item in statusOptions"-->
<!--            :key="item"-->
<!--            :label="item"-->
<!--            :value="item"-->
<!--          />-->
<!--        </el-select>-->
<!--        <el-input-->
<!--          v-model="keywords"-->
<!--          placeholder="关键字"-->
<!--          style="width: 200px; margin-left: 5px; margin-right: 5px"-->
<!--          clearable-->
<!--        />-->
<!--        &lt;!&ndash; 操作按钮 &ndash;&gt;-->
<!--        <el-button type="primary" icon="el-icon-search" @click="getStudentExerciseList()">-->
<!--          {{ $t('soi.common.search') }}-->
<!--        </el-button>-->
<!--        <el-button type="primary" @click="getStudentExerciseList()">-->
<!--          新建提问-->
<!--        </el-button>-->
<!--      </div>-->

<!--      &lt;!&ndash; 行业讨论表格 &ndash;&gt;-->
<!--      <el-card shadow="never" style="margin-top: 10px">-->
<!--        <el-table-->
<!--          v-loading="tableLoading"-->
<!--          :data="industryTalkList"-->
<!--          stripe-->
<!--          @selection-change="handleSelectionChange"-->
<!--        >-->
<!--          <el-table-column type="selection" width="50px" />-->
<!--          <el-table-column type="index" label="#" align="center" />-->
<!--          <el-table-column prop="title" :label="$t('worksheet.Title')" />-->
<!--          <el-table-column-->
<!--            sortable-->
<!--            prop="applicationDate"-->
<!--            :label="$t('worksheet.ApplicationDate')"-->
<!--            min-width="200"-->
<!--          />-->
<!--          <el-table-column-->
<!--            prop="status"-->
<!--            :label="$t('worksheet.Status')"-->
<!--            min-width="100"-->
<!--            :filters="[-->
<!--              {text:'Ready',value:'Ready'},-->
<!--              {text:'Processing',value:'Processing'},-->
<!--              {text:'Done',value:'Done'},-->
<!--              {text:'Error',value:'Error'},-->
<!--              {text:'Reload',value:'Reload'},-->
<!--              {text:'Close',value:'Close'}-->
<!--            ]"-->
<!--            :filter-method="filterTag"-->
<!--            filter-placement="bottom-end"-->
<!--          />-->
<!--          <el-table-column prop="comment" :label="$t('worksheet.Comment')" min-width="150" />-->
<!--          <el-table-column :label="$t('soi.common.operate')" min-width="70">-->
<!--            <template slot-scope="scope">-->
<!--              <el-button-->
<!--                type="text"-->
<!--                size="small"-->
<!--                @click="handleDetailClick(scope.row,'creatcommonworksheet')"-->
<!--              >{{ $t('worksheet.Detail') }}</el-button>-->
<!--              <el-button-->
<!--                type="text"-->
<!--                size="small"-->
<!--                text-->
<!--                icon="el-icon-delete"-->
<!--                @click="batchDeleteHomeworkList([scope.row.id])"-->
<!--              >{{ $t('soi.common.delete') }}-->
<!--              </el-button>-->
<!--            </template>-->
<!--          </el-table-column>-->
<!--        </el-table>-->
<!--        <div class="table-footer">-->
<!--          <el-pagination-->
<!--            :current-page.sync="currentPage"-->
<!--            :page-sizes="[20, 50, 100, 500, 1000]"-->
<!--            :page-size.sync="pageSize"-->
<!--            layout="total, sizes, prev, pager, next, jumper"-->
<!--            :total="total"-->
<!--            @size-change="getStudentExerciseList()"-->
<!--            @current-change="getStudentExerciseList()"-->
<!--          />-->
<!--        </div>-->
<!--      </el-card>-->
<!--    </customize-card>-->
<!--  </div>-->
<!--</template>-->

<script>

// import CustomizeCard from '@/components/CustomizeCard/index.vue'

export default {
  name: 'CourseLearningIndustryTalk',
  // components: { CustomizeCard },
  data() {
    return {
      // // 页面loading
      // loading: false,
      // // ‘行业讨论列表’表格loading
      // tableLoading: false,
      // // 下拉选择框内容可选值
      // statusOptions: [
      //   'All',
      //   'Ready',
      //   'Processing',
      //   'Done',
      //   'Error',
      //   'Reload',
      //   'Close'
      // ],
      // defaultOption: 'All',
      // keywords: '',
      // industryTalkList: [],
      // currentPage: 1,
      // pageSize: 20,
      // total: 0,
      // multipleSelection: []
    }
  },
  methods: {
    // filterTag(value, row) {
    //   return row.tag === value
    // },
    // // 保存已选中的记录
    // handleSelectionChange(val) {
    //   this.multipleSelection = val
    // }
  }
}
</script>
<style scoped>
.maintenance-page {
  text-align: center;
  padding: 20px;
  color: #333;
}
.filter-container {
  padding-bottom: 10px;

  .filter-item {
    display: inline-block;
    vertical-align: middle;
    margin-bottom: 10px;
  }
}
</style>
