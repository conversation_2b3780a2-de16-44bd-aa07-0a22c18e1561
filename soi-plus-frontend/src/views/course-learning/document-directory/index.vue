<template>
  <div class="document-directory">
    <div v-loading="loading" class="directory-content">
      <h2 class="title">
        {{
          title.reduce((previousValue, currentValue) => {
            return previousValue + ' - ' + this.$t(`${currentValue}`)
          }, this.$t('soi.courseLearning.courseLearningSpace'))
        }}
      </h2>
      <div class="back-container">
        <el-button size="small" @click="back()">{{ $t('soi.common.back') }}</el-button>
      </div>
      <transition name="fade-transform" mode="out-in">
        <el-row :key="currentParentId" :gutter="20" class="card-container">
          <el-col v-for="item in documents" :key="item.id" :span="6" :xs="12" :sm="8" :md="6">
            <el-card
              shadow="hover"
              class="menu-card-item"
              :style="getCardBackgroundStyle(item)"
              @click.native="handleCardClick(item)"
            >
              <div class="content">
                <span class="card-title">{{ item.name }}</span>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </transition>
    </div>
  </div>
</template>

<script>
import { getDocumentChildren } from '@/api/course-learning/document'
import { OFFICIAL_WEBSITE_URL } from '@/contains'

export default {
  name: 'DocumentDirectory',
  data() {
    return {
      loading: false,
      documents: [],
      currentParentId: '',
      title: []
    }
  },
  watch: {
    '$route': {
      handler: 'initFromRoute',
      immediate: true
    }
  },
  methods: {
    back() {
      this.$router.go(-1)
    },
    initFromRoute() {
      // 从路由路径确定当前目录ID
      const path = this.$route.path
      if (this.$route.query.parentId) {
        // 处理子目录的情况
        this.currentParentId = this.$route.query.parentId
      } else if (path === '/course-business-markdown') {
        this.currentParentId = 'learning-space-business'
        this.title = ['soi.router.courseBusiness']
      } else if (path === '/course-technology-markdown') {
        this.currentParentId = 'learning-space-technology'
        this.title = ['soi.router.courseTechnology']
      }

      this.fetchDocuments()
    },

    async fetchDocuments() {
      if (!this.currentParentId) return

      this.loading = true
      try {
        const response = await getDocumentChildren(this.currentParentId)
        if (response && response.data) {
          this.documents = response.data
        } else {
          this.documents = []
        }
      } catch (error) {
        console.error('Failed to fetch documents:', error)
        this.documents = []
      } finally {
        this.loading = false
      }
    },

    getCardBackgroundStyle(item) {
      // 根据文档类型返回不同的背景图片
      let backgroundImage = ''

      if (item.type === 'folder') {
        backgroundImage = 'document-center.jpg'
      } else if (item.type === 'document') {
        backgroundImage = 'learning-space-business.jpg'
      } else {
        backgroundImage = 'learning-space-technology.png'
      }

      // 如果文档有自己的图片，优先使用文档自己的图片
      if (item.image) {
        return { backgroundImage: `url(${item.image})` }
      }

      return { backgroundImage: `url(${require('@/assets/images/router/' + backgroundImage)})` }
    },

    handleCardClick(item) {
      if (item.type === 'folder') {
        // 在跳转前保存当前目录名称，以便在标题中显示
        this.$router.push({
          path: this.$route.path,
          query: {
            parentId: item.id
          }
        })
        this.title.push(item.name)
      } else if (item.type === 'document') {
        // 点击文档，跳转到文档查看页面
        // 如果文档有contentId，则传递contentId参数
        window.open(`${OFFICIAL_WEBSITE_URL}/course/markdown/${item.id}/${item.id}`, '_blank')
      }
    },

    formatDate(dateString) {
      if (!dateString) return ''

      const date = new Date(dateString)
      return date.toLocaleDateString()
    }
  }
}
</script>

<style lang="scss" scoped>
/* 增强版的过渡动画 */
.fade-transform-enter-active,
.fade-transform-leave-active {
  transition: all 0.8s ease;
}

.fade-transform-enter {
  opacity: 0;
  transform: translateX(-50px);
}

.fade-transform-leave-to {
  opacity: 0;
  transform: translateX(50px);
}

.document-directory {

  .title {
    text-align: center;
    padding-top: 20px;
    padding-bottom: 20px;
    font-size: 24px;
    font-weight: 500;
  }
  padding: 20px;

  .directory-header {
    margin-bottom: 20px;

    h2 {
      margin-bottom: 10px;
    }
  }

  .directory-content {
    min-height: 300px;

    .empty-directory {
      display: flex;
      justify-content: center;
      align-items: center;
      height: 300px;
    }

    .card-container {
      .menu-card-item {
        height: 200px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 20px;
        font-size: 18px;
        cursor: pointer;
        background: no-repeat center center;
        position: relative;
        background-size: 100% 100%;
        transition: all 0.3s;

        .content {
          position: absolute;
          left: 0;
          right: 0;
          top: 0;
          bottom: 0;
          line-height: 1.5;
          display: flex;
          flex-direction: column;
          align-items: center;
          justify-content: center;
          font-weight: 700;
          background: rgba(255, 255, 255, .75);

          .card-title {
            font-size: 18px;
            font-weight: 700;
            margin-bottom: 10px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 90%;
          }

          .card-icon {
            font-size: 32px;
            margin-bottom: 10px;

            .el-icon-folder {
              color: #409EFF;
            }

            .el-icon-document {
              color: #67C23A;
            }

            .el-icon-document-copy {
              color: #E6A23C;
            }
          }

          .card-date {
            font-size: 12px;
            color: #909399;
            position: absolute;
            bottom: 10px;
          }
        }
      }
    }
  }
  .back-container {
    text-align: right;
    margin-bottom: 10px;
  }
}
</style>
