<template>
  <div v-loading.fullscreen.lock="loading" class="data-visualization-data-preparation-container">
    <customize-card :title="$t('soi.router.dataPreparation')">
      <template slot="header-actions">
        <el-button size="mini" type="primary" @click="$router.push({ name: 'data-analysis-data-visualization-data-source' })">
          {{ $t('soi.router.dataSource') }}
        </el-button>
      </template>
      <div style="margin-bottom: 10px">
        <el-button :type="!isDataProcess ? 'primary' : 'default'" size="mini" @click="isDataProcess = false">{{ $t('soi.router.myDataTable') }}</el-button>
        <el-button :type="isDataProcess ? 'primary' : 'default'" size="mini" @click="isDataProcess = true">{{ $t('soi.router.dataPreparation') }}</el-button>
      </div>
      <el-card shadow="never">
        <data-visualization-data-table-form-content v-show="!isDataProcess" :is-default="false" />
        <el-tabs v-show="isDataProcess" v-model="activeName">
          <el-tab-pane label="Automatical data processing with one-click" name="auto">
            <el-row type="flex" justify="center">
              <el-col>
                <customize-upload class=" d-inline-block" :cache-key="'AUTO_RAW_DATA_FILE'" :action="uploadDataAction" @upload-success="handlerAutoProcessFileUploadSuccess($event)" />
                <el-button type="primary" class="download-template d-inline-block" @click="downloadTemplateFile">Download Template Config File</el-button>
                <br>
                <el-button type="primary" class="start-auto-process" @click="handlerAutoProcessData">Go</el-button>

                <el-form>
                  <el-form-item>
                    <div class="tip processed-data-tip">Processed Data</div>
                    <customize-table :cols="processedDataHeader" :file-list="processedData" :show-view="false" />
                  </el-form-item>
                </el-form>
              </el-col>
            </el-row>
          </el-tab-pane>
          <el-tab-pane label="Automatical data processing step by step" name="step">
            <el-row type="flex" justify="center">
              <el-col>
                <customize-step :active="active" @click-active="handlerClickActive($event)" />
                <el-card shadow="never">
                  <!-- upload data -->
                  <div v-show="active === 0" class="upload-data">
                    <el-form>
                      <el-form-item>
                        <customize-upload :action="uploadDataAction" :cache-key="'RAW_DATA_FILE'" @upload-success="handlerUploadDataSuccess($event)" />
                      </el-form-item>
                      <customize-turn-page :current="active" :previous="false" @switchPage="handlerSwitchPage($event)" />
                    </el-form>
                  </div>
                  <!-- data labeling -->
                  <div v-show="active === 1">
                    <p class="tip">The following non-numeric data needs to be one-hot: </p>
                    <el-form label-position="top">
                      <el-form-item label="View Column: ">
                        <el-select v-model="dataHeader" class="header-select" filterable @change="handlerChangeDataHeader">
                          <el-option
                            v-for="(item, index) in uploadResponseData.keys"
                            :key="index"
                            :label="item"
                            :value="item"
                          />
                        </el-select>
                        <el-button type="primary" icon="el-icon-view" @click="viewLabel">View</el-button>
                        <customize-dialog
                          :title="'Column Name: ' + dataHeader"
                          :dialog-visible="viewDataDialogVisible"
                          @update:dialogVisible="handlerUpdateViewDataDialogVisible($event)"
                          @confirm="handlerConfirm"
                        >
                          {{ dataValue }}
                        </customize-dialog>
                      </el-form-item>
                      <p class="tip">Select the data column that to be one-hot:</p>
                      <el-form-item label="Set Label Type: ">
                        <perfect-scrollbar class="label-ps" :options="{ wheelPropagation: false }">
                          <div v-for="(item, index) in dataLabelingForm.columns" :key="item.column" class="label-item">
                            <label class="header-select">Value
                              <el-select v-model="item.column" filterable @change="handlerChangeColumn(index)">
                                <el-option
                                  v-for="(i, indexInner) in uploadResponseData.keys"
                                  :key="indexInner"
                                  :label="i"
                                  :value="i"
                                  :disabled="usedColumns.indexOf(i) !== -1 && item.column !== i"
                                />
                              </el-select>
                            </label>
                            <label class="header-select">Label Type
                              <el-select v-model="item.labelType" @change="handlerChangeLabelType(index)">
                                <el-option
                                  v-for="(i, indexInner) in labelTypeOptions"
                                  :key="indexInner"
                                  :label="i"
                                  :value="i"
                                />
                              </el-select>
                            </label>
                            <el-button
                              :type="item.status ? 'success' : 'warning'"
                              icon="el-icon-collection-tag"
                              :disabled="item.labelType === 'Categorical'"
                              @click="handlerLabel(index)"
                            >
                              Label
                            </el-button>
                            <el-button
                              v-if="dataLabelingForm.columns.length !== 1"
                              type="danger"
                              icon="el-icon-delete"
                              @click="handlerDeleteColumn(index)"
                            >
                              Delete
                            </el-button>
                            <el-button
                              v-if="index === (dataLabelingForm.columns.length - 1) && index < (uploadResponseData.keys.length) - 1"
                              icon="el-icon-plus"
                              @click="handlerAddColumn"
                            >
                              Add
                            </el-button>
                          </div>
                        </perfect-scrollbar>
                        <customize-dialog
                          :title="'Column Name: ' + labeling.column"
                          :dialog-visible="labelingDialogVisible"
                          :cancel-button="true"
                          @update:dialogVisible="handlerUpdateLabelingDataDialogVisible($event)"
                          @confirm="handlerConfirmLabeling"
                        >
                          <el-form
                            v-for="value in labeling.values"
                            :key="value.key"
                            :model="value"
                            inline
                            label-width="280px"
                            style="text-align: center"
                          >
                            <el-form-item :label="value.key.toString()" prop="value">
                              <el-input-number
                                v-if="labeling.labelType !== 'Binary'"
                                v-model="value.value"
                                :min="0"
                                :step="1"
                                step-strictly
                                :precision="0"
                                :controls="false"
                              />
                              <el-select
                                v-if="labeling.labelType === 'Binary'"
                                v-model="value.value"
                              >
                                <el-option v-for="item in binaryOptions" :key="item" :label="item" :value="item" />
                              </el-select>
                            </el-form-item>
                          </el-form>
                        </customize-dialog>
                      </el-form-item>
                      <el-form-item>
                        <el-button type="primary" @click="submitLabeling">Submit</el-button>
                      </el-form-item>
                      <el-form-item>
                        <div class="tip">One Hot Data</div>
                        <customize-table :cols="labeledDataHeader" :file-list="labeledData" />
                      </el-form-item>
                      <customize-turn-page :current="active" @switchPage="handlerSwitchPage($event)" />
                    </el-form>
                  </div>
                  <!-- data cleansing -->
                  <div v-show="active === 2">
                    <el-form>
                      <el-form-item>
                        <el-radio-group v-model="cleansingFileType">
                          <el-radio :label="1">Use previous one-hot file</el-radio>
                          <el-radio :label="2">New upload one-hot file</el-radio>
                        </el-radio-group>
                      </el-form-item>
                      <el-form-item v-show="cleansingFileType === 1">
                        <div v-if="labeledData && labeledData[0] && labeledData[0].fileId">
                          <span class="file-label tip">One Hot: </span>
                          <span class="file-id">{{ labeledData[0].fileId }}</span>
                          <customize-view-data :file-id="labeledData[0].fileId" />
                        </div>
                        <div v-else>Please complete the previous step first.</div>
                      </el-form-item>
                      <el-form-item v-show="cleansingFileType === 2">
                        <customize-upload :action="uploadAction" :cache-key="'ONE_HOT_DATA_FILE'" @upload-success="handlerUploadLabelSuccess($event)" />
                      </el-form-item>
                      <el-form-item>
                        <div v-show="cleansingFileType === 1">
                          <div v-if="labeledData && labeledData[0] && labeledData[0].fileId">
                            <customize-mark-table
                              :file-id="labeledData[0].fileId"
                              title="Dirty Data"
                              button-value="View Dirty Data"
                            />
                          </div>
                        </div>
                        <div v-show="cleansingFileType === 2">
                          <div v-if="uploadLabelDataResponse.fileId">
                            <customize-mark-table
                              :file-id="uploadLabelDataResponse.fileId"
                              title="Dirty Data"
                              button-value="View Dirty Data"
                            />
                          </div>
                        </div>
                      </el-form-item>
                      <el-form-item>
                        <span class="start-data-cleansing tip">Start Data Cleansing with only one-click</span>
                        <el-button type="primary" @click="handlerCleansData">Clean</el-button>
                        <div class="node-tip">*Node: Data cleansing process will use the one-hot data.</div>
                      </el-form-item>
                      <el-form-item>
                        <div class="tip">Cleansed Data</div>
                        <customize-table :cols="cleansedDataHeader" :file-list="cleansedData" />
                      </el-form-item>
                      <customize-turn-page :current="active" @switchPage="handlerSwitchPage($event)" />
                    </el-form>
                  </div>
                  <!-- sample balancing -->
                  <div v-show="active === 3">
                    <el-form>
                      <el-form-item>
                        <el-radio-group v-model="sampleBalancingFileType">
                          <el-radio :label="1">Use previous cleansed file</el-radio>
                          <el-radio :label="2">New upload cleansed file</el-radio>
                        </el-radio-group>
                      </el-form-item>
                      <el-form-item v-show="sampleBalancingFileType === 1">
                        <div v-if="cleansedData && cleansedData[0] && cleansedData[0].fileId">
                          <span class="file-label tip">Cleansed Data: </span>
                          <span class="file-id">{{ cleansedData[0].fileId }}</span>
                          <customize-view-data :file-id="cleansedData[0].fileId" />
                        </div>
                        <div v-else>Please complete the previous step first.</div>
                      </el-form-item>
                      <el-form-item v-show="sampleBalancingFileType === 2">
                        <customize-upload :action="uploadAction" :cache-key="'CLEANSED_DATA_FILE'" @upload-success="handlerUploadCleansedSuccess($event)" />
                      </el-form-item>
                      <el-form-item label="Label Column Selection">
                        <el-select v-model="sampleBalancingHeader" class="feature-selection-label-select">
                          <el-option
                            v-for="(item, index) in sampleBalancingHeaderOptions"
                            :key="index"
                            :label="item"
                            :value="item"
                          />
                        </el-select>
                        <el-button type="primary" @click="handlerStartSampleBalancing()">Sample Balancing</el-button>
                        <div class="node-tip">*Node: Sample balance process will use the cleansed data that return at the
                          Data Cleansing page.
                        </div>
                      </el-form-item>
                      <el-form-item>
                        <div class="tip">Sample Balanced Data</div>
                        <customize-table :cols="sampleBalancingDataHeader" :file-list="sampleBalancingData" />
                      </el-form-item>
                      <customize-turn-page :current="active" @switchPage="handlerSwitchPage($event)" />
                    </el-form>
                  </div>
                  <!-- dimensionality reduction -->
                  <div v-show="active === 4">
                    <el-form>
                      <el-form-item>
                        <el-radio-group v-model="dimensionalityReductionFileType" @change="handlerChangeDimensionalityReductionFileType">
                          <el-radio :label="1">Use previous sample balanced file</el-radio>
                          <el-radio :label="2">New upload sample balanced file</el-radio>
                        </el-radio-group>
                      </el-form-item>
                      <el-form-item v-show="dimensionalityReductionFileType === 1">
                        <div v-if="sampleBalancingData && sampleBalancingData[0] && sampleBalancingData[0].fileId">
                          <span class="file-label tip">Sample Balanced Data: </span>
                          <span class="file-id">{{ sampleBalancingData[0].fileId }}</span>
                          <customize-view-data :file-id="sampleBalancingData[0].fileId" />
                        </div>
                        <div v-else>Please complete the previous step first.</div>
                      </el-form-item>
                      <el-form-item v-show="dimensionalityReductionFileType === 2">
                        <customize-upload :action="uploadAction" :cache-key="'BALANCED_DATA_FILE'" @upload-success="handlerUploadSampleBalancingSuccess($event)" />
                      </el-form-item>
                      <el-form-item label="Label Column Selection">
                        <el-select v-model="dimensionalityReductionHeader" class="feature-selection-label-select">
                          <el-option
                            v-for="(item, index) in dimensionalityReductionHeaderOptions"
                            :key="index"
                            :label="item"
                            :value="item"
                          />
                        </el-select>
                        <el-button type="primary" @click="handlerDimensionalityReduction()">Dimensionality Reduced</el-button>
                        <div class="node-tip">*Node: Dimensionality reduction process will use the sample balanced data that return at the
                          Sample Balance page.
                        </div>
                      </el-form-item>
                      <el-form-item v-if="dimensionalityReductionData && dimensionalityReductionData[0] && dimensionalityReductionData[0].heatmap">
                        <div class="tip feature-report">Heatmap</div>
                        <el-image
                          :src="dimensionalityReductionData[0].heatmap"
                        />
                      </el-form-item>
                      <el-form-item>
                        <div class="tip">Dimensionality Reduction Data</div>
                        <customize-table :cols="dimensionalityReductionDataHeader" :file-list="dimensionalityReductionData" />
                      </el-form-item>
                      <customize-turn-page :current="active" @switchPage="handlerSwitchPage($event)" />
                    </el-form>
                  </div>
                  <!-- pca -->
                  <div v-show="active === 5">
                    <el-form>
                      <el-form-item>
                        <el-radio-group v-model="pcaFileType">
                          <el-radio :label="1">Use previous dimensionality reduction file</el-radio>
                          <el-radio :label="2">New upload dimensionality reduction file</el-radio>
                        </el-radio-group>
                      </el-form-item>
                      <el-form-item v-show="pcaFileType === 1">
                        <div v-if="dimensionalityReductionData && dimensionalityReductionData[0] && dimensionalityReductionData[0].fileId">
                          <span class="file-label tip">Dimensionality Reduction Data: </span>
                          <span class="file-id">{{ dimensionalityReductionData[0].fileId }}</span>
                          <customize-view-data :file-id="dimensionalityReductionData[0].fileId" />
                        </div>
                        <div v-else>Please complete the previous step first.</div>
                      </el-form-item>
                      <el-form-item v-show="pcaFileType === 2">
                        <customize-upload
                          :action="uploadAction"
                          :cache-key="'DIMENSIONALITY_REDUCTION_DATA_FILE'"
                          @upload-success="handlerUploadDimensionalityReductionSuccess($event)"
                        />
                      </el-form-item>
                      <el-form-item>
                        <span class="tip margin-bottom-none start-data-cleansing">Start PCA with only one-click</span>
                        <el-button type="primary" class="start-data-cleansing" @click="handlerStartPCA()">Start PCA</el-button>
                        <div class="node-tip">*Node: PCA process will use the dimensionality reduction data that return at the Dimensionality Reduction page.</div>
                      </el-form-item>
                      <el-form-item v-if="pcaData[0] && pcaData[0].features">
                        <div class="tip principal-components-report">Principal components report</div>
                        <div class="node-tip">*P-value &lt; 5% suggests critical feature in general</div>
                        <el-table
                          :data="pcaData[0].features"
                          border
                          height="300"
                          :cell-style="cellStyle"
                        >
                          <el-table-column type="index" label="#" align="center" width="55" />
                          <el-table-column label="Principal" prop="principal" align="center" />
                          <el-table-column label="Criticality" prop="criticality" align="center" />
                          <el-table-column label="p-value(%)" prop="p-value" align="center" />
                        </el-table>
                      </el-form-item>
                      <el-form-item>
                        <div class="tip">PCA Data</div>
                        <customize-table :cols="pcaDataHeader" :file-list="pcaData" />
                      </el-form-item>
                      <customize-turn-page :current="active" :next="false" @switchPage="handlerSwitchPage($event)" />
                    </el-form>
                  </div>
                </el-card>
              </el-col>
            </el-row>
          </el-tab-pane>
        </el-tabs>
      </el-card>
    </customize-card>
  </div>
</template>

<script>
import CustomizeCard from '@/components/CustomizeCard/index.vue'
import CustomizeStep from '@/views/practice/data-analysis/data-preparation/components/CustomizeStep'
import CustomizeUpload from '@/views/practice/data-analysis/data-preparation/components/CustomizeUpload'
import CustomizeTurnPage from '@/views/practice/data-analysis/data-preparation/components/CustomizeTurnPage'
import CustomizeDialog from '@/views/practice/data-analysis/data-preparation/components/CustomizeDialog'
import CustomizeTable from '@/views/practice/data-analysis/data-preparation/components/CustomizeTable'
import CustomizeViewData from '@/views/practice/data-analysis/data-preparation/components/CustomizeViewData'
import CustomizeMarkTable from '@/views/practice/data-analysis/data-preparation/components/CustomizeMarkTable'
import { DATA_PREPARATION_API_URL, OFFICIAL_WEBSITE_URL } from '@/contains'
import {
  balancedDataSet,
  cleansing,
  dimensionalityReduction,
  label,
  oneKeyStart,
  pcaAnalysis
} from '@/api/practice/data-preparation'
import DataVisualizationDataTableFormContent
  from '@/views/practice/data-analysis/data-visualization/data-table/components/DataTableFormContent.vue'

export default {
  name: 'DataAnalysisDataPreparation',
  components: {
    DataVisualizationDataTableFormContent,
    CustomizeCard,
    CustomizeMarkTable,
    CustomizeViewData,
    CustomizeTable,
    CustomizeDialog,
    CustomizeTurnPage,
    CustomizeUpload,
    CustomizeStep
  },
  data() {
    const stringState = sessionStorage.getItem('DATA_PROCESSING_TOOLS_STATE')
    const state = stringState ? JSON.parse(stringState) : {}
    return {
      ...{
        // page
        isDataProcess: true,
        loading: false,
        activeName: 'auto',
        active: 0,
        // upload
        uploadDataAction: DATA_PREPARATION_API_URL + '/dp/file-upload/',
        uploadResponseData: {
          fileId: '',
          items: [],
          keys: []
        },
        // label
        dataLabelingForm: {
          columns: [],
          fileId: ''
        },
        usedColumns: [],
        labelingDialogVisible: false,
        viewDataDialogVisible: false,
        dataHeader: null,
        dataValue: [],
        labelTypeOptions: ['Natural', 'Binary', 'Categorical'],
        labeling: {},
        binaryOptions: [0, 1],
        labeledData: [],
        labeledDataHeader: [
          {
            label: 'Raw Data',
            prop: 'dataFileName'
          },
          {
            label: 'One Hot Data',
            prop: 'fileId'
          },
          {
            label: 'Status',
            prop: 'status'
          }
        ],
        // cleans
        cleansingFileType: null,
        templateFileUrl: OFFICIAL_WEBSITE_URL + '/document/soi-plus/data_preparation_template/data_process_template.xlsx',
        uploadAction: DATA_PREPARATION_API_URL + '/dp/file-upload-columns/',
        cleansedDataHeader: [
          {
            label: 'One Hot Data',
            prop: 'dataFileName'
          },
          {
            label: 'Cleansed Data',
            prop: 'fileId'
          },
          {
            label: 'Status',
            prop: 'status'
          }
        ],
        cleansedData: [],
        uploadLabelDataResponse: {},
        // feature selection
        dimensionalityReductionFileType: null,
        uploadCleansDataResponse: {},
        dimensionalityReductionHeader: null,
        dimensionalityReductionHeaderOptions: [],
        sampleBalancingHeader: null,
        sampleBalancingHeaderOptions: [],
        dimensionalityReductionData: [],
        dimensionalityReductionDataHeader: [
          {
            label: 'Sample Balanced Data',
            prop: 'dataFileName'
          },
          {
            label: 'Dimensionality Reduced Data',
            prop: 'fileId'
          },
          {
            label: 'Status',
            prop: 'status'
          }
        ],
        pcaFileType: null,
        uploadDimensionalityReductionDataResponse: {},
        pcaData: [],
        pcaDataHeader: [
          {
            label: 'Dimensionality Reduction Data',
            prop: 'dataFileName'
          },
          {
            label: 'PCA Data',
            prop: 'fileId'
          },
          {
            label: 'Status',
            prop: 'status'
          }
        ],
        // sample balancing
        sampleBalancingFileType: null,
        uploadPCADataResponse: {},
        sampleBalancingDataHeader: [
          {
            label: 'Cleansed Data',
            prop: 'dataFileName'
          },
          {
            label: 'Sample Balanced Data',
            prop: 'fileId'
          },
          {
            label: 'Status',
            prop: 'status'
          }
        ],
        sampleBalancingData: [],
        // training sample
        trainingSampleFileType: null,
        uploadSampleBalancingDataResponse: {},
        trainingSampleDataHeader: [
          {
            label: 'Sample Balancing Data',
            prop: 'dataFileName'
          },
          {
            label: 'Testing Sample Data',
            prop: 'testingLink'
          },
          {
            label: 'Training Sample Data',
            prop: 'trainingLink'
          },
          {
            label: 'Status',
            prop: 'status'
          }
        ],
        trainingSampleData: [],
        trainingPercent: 50,
        testingPercent: 50,
        uploadAutoProcessFileResponse: {},
        processedDataHeader: [
          {
            label: 'Raw Data',
            prop: 'rawDataName'
          },
          {
            label: 'Processed Data',
            prop: 'fileId'
          },
          {
            label: 'Status',
            prop: 'status'
          }
        ],
        processedData: []
      },
      ...state
    }
  },
  watch: {
    $data: {
      handler(newValue) {
        const stringState = sessionStorage.getItem('DATA_PROCESSING_TOOLS_STATE')
        const state = stringState ? JSON.parse(stringState) : {}
        sessionStorage.setItem('DATA_PROCESSING_TOOLS_STATE', JSON.stringify({ ...state, ...newValue }))
      },
      deep: true
    }
  },
  methods: {
    handlerSwitchPage(active) {
      // 清洗数据
      if (active === 2) {
        if (this.labeledData && this.labeledData.length > 0) {
          this.cleansingFileType = 1
        } else {
          this.cleansingFileType = 2
        }
      }
      // 样本平衡
      if (active === 3) {
        if (this.cleansedData && this.cleansedData.length > 0) {
          this.sampleBalancingFileType = 1
        } else {
          this.sampleBalancingFileType = 2
        }
        this.$nextTick(() => {
          this.handlerChangeDimensionalityReductionFileType()
        })
      }
      // 降维
      if (active === 4) {
        if (this.sampleBalancingData && this.sampleBalancingData.length > 0) {
          this.dimensionalityReductionFileType = 1
        } else {
          this.dimensionalityReductionFileType = 2
        }
      }
      // PCA
      if (active === 5) {
        if (this.dimensionalityReductionData && this.dimensionalityReductionData.length > 0) {
          this.pcaFileType = 1
        } else {
          this.pcaFileType = 2
        }
      }
      this.active = active
    },
    handlerUploadDataSuccess(data) {
      this.uploadResponseData = { ...data }
      if (data.items.length === 0) {
        return
      }
      this.uploadResponseData.keys = this.uploadResponseData.items.map(item => {
        return item.column
      })
      this.dataLabelingForm.fileId = data.fileId
      this.dataLabelingForm.columns = [{
        column: this.uploadResponseData.keys[0],
        values: this.getValues(this.uploadResponseData.keys[0]),
        labelType: this.labelTypeOptions[0],
        status: false
      }]
      this.dataHeader = this.uploadResponseData.keys[0]
      this.handlerChangeDataHeader(this.dataHeader)
      this.getUsed()
      this.labeledData = []
    },
    handlerUploadLabelSuccess(data) {
      this.uploadLabelDataResponse = data
    },
    handlerUploadCleansedSuccess(data) {
      this.sampleBalancingHeaderOptions = data.items
      this.sampleBalancingHeader = this.sampleBalancingHeaderOptions[0]
      this.uploadCleansDataResponse = data
    },
    handlerUploadDimensionalityReductionSuccess(data) {
      this.uploadDimensionalityReductionDataResponse = data
    },
    handlerUploadPCASuccess(data) {
      this.uploadPCADataResponse = data
    },
    handlerUploadSampleBalancingSuccess(data) {
      this.dimensionalityReductionHeaderOptions = data.items
      this.dimensionalityReductionHeader = this.dimensionalityReductionHeaderOptions[0]
      this.uploadSampleBalancingDataResponse = data
    },
    handlerAutoProcessFileUploadSuccess(data) {
      this.uploadAutoProcessFileResponse = data
      this.processedData = []
    },
    getUsed() {
      this.usedColumns = this.dataLabelingForm.columns.map(item => {
        return item.column
      })
    },
    downloadTemplateFile() {
      window.location.href = this.templateFileUrl
    },
    getValues(column) {
      const temp = []
      this.uploadResponseData.items.forEach((item, index) => {
        if (this.uploadResponseData.items[index].column === column) {
          for (let i = 0; i < this.uploadResponseData.items[index].value.length; i++) {
            temp.push({
              key: this.uploadResponseData.items[index].value[i],
              value: i
            })
          }
        }
      })
      return temp
    },
    handlerUpdateViewDataDialogVisible(value) {
      this.viewDataDialogVisible = value
    },
    handlerUpdateLabelingDataDialogVisible(value) {
      this.labelingDialogVisible = value
    },
    handlerChangeDataHeader(value) {
      this.uploadResponseData.items.forEach((item, index) => {
        if (item.column === value) {
          this.dataValue = this.uploadResponseData.items[index].value
        }
      })
    },
    handlerAutoProcessData() {
      if (!this.uploadAutoProcessFileResponse.fileId) {
        this.$message.error('Please upload the data file first.')
        return
      }
      const requestData = {
        fileId: this.uploadAutoProcessFileResponse.fileId,
        columns: this.uploadAutoProcessFileResponse.items
      }
      this.loading = true
      oneKeyStart(requestData)
        .then(response => {
          const { data } = response
          this.$message.success('Data Process Successfully!')
          this.processedData = [{
            rawDataName: this.uploadAutoProcessFileResponse.fileName,
            fileId: data,
            status: 'Successfully'
          }]
        })
        .finally(() => {
          this.loading = false
        })
    },
    handlerConfirm() {
      this.viewDataDialogVisible = false
    },
    handlerConfirmLabeling() {
      this.dataLabelingForm.columns[this.labeling.index] =
        {
          ...this.dataLabelingForm.columns[this.labeling.index],
          ...{
            ...this.labeling,
            status: true
          }
        }
      this.labelingDialogVisible = false
    },
    viewLabel() {
      if (!this.uploadResponseData.fileId) {
        this.$message.error('Please upload the data file first.')
        return
      }
      this.viewDataDialogVisible = true
    },
    handlerAddColumn() {
      const notUsed = this.uploadResponseData.keys.filter(item => {
        return this.usedColumns.indexOf(item) === -1
      })
      this.dataLabelingForm.columns.push({
        column: notUsed[0],
        values: this.getValues(notUsed[0]),
        labelType: this.labelTypeOptions[0],
        status: false
      })
      this.getUsed()
    },
    handlerDeleteColumn(i) {
      this.$confirm('Are you sure you want to delete it?', 'Tip', {
        confirmButtonText: 'Confirm',
        cancelButtonText: 'Cancel',
        type: 'warning'
      }).then(() => {
        this.dataLabelingForm.columns = this.dataLabelingForm.columns.filter((item, index) => {
          return index !== i
        })
        this.getUsed()
      }).catch(() => {})
    },
    handlerChangeColumn(index) {
      this.$set(this.dataLabelingForm.columns[index], 'values', this.getValues(this.dataLabelingForm.columns[index].column))
      this.$set(this.dataLabelingForm.columns[index], 'labelType', this.labelTypeOptions[0])
      this.$set(this.dataLabelingForm.columns[index], 'status', false)
      this.getUsed()
    },
    handlerLabel(index) {
      this.labeling = JSON.parse(JSON.stringify({
        ...this.dataLabelingForm.columns[index],
        index: index
      }))
      this.labelingDialogVisible = true
    },
    handlerChangeLabelType(index) {
      if (this.dataLabelingForm.columns[index].labelType === 'Binary') {
        for (let i = 0; i < this.dataLabelingForm.columns[index].values.length; i++) {
          this.dataLabelingForm.columns[index].values[i].value = i % 2
        }
      } else {
        for (let i = 0; i < this.dataLabelingForm.columns[index].values.length; i++) {
          this.dataLabelingForm.columns[index].values[i].value = i
        }
      }
    },
    submitLabeling() {
      if (!this.uploadResponseData.fileId) {
        this.$message.error('Please upload the data file first.')
        return
      }
      const requestData = JSON.parse(JSON.stringify(this.dataLabelingForm))
      for (const column of requestData.columns) {
        const temp = {}
        for (const value of column.values) {
          temp[value.key] = value.value
        }
        column.values = temp
      }
      this.loading = true
      label(requestData)
        .then(response => {
          const { data } = response
          this.$message.success('Data One Hot Successfully!')
          this.labeledData = [{
            dataFileName: this.uploadResponseData.fileId,
            fileId: data.fileId,
            status: 'Successfully'
          }]
        })
        .finally(() => {
          this.loading = false
        })
    },
    handlerCleansData() {
      let fileId = ''
      if (this.cleansingFileType === 1) {
        if (this.labeledData && this.labeledData[0] && this.labeledData[0].fileId) {
          fileId = this.labeledData[0].fileId
        } else {
          this.$message.error('Please complete the previous step first.')
          return
        }
      } else {
        if (this.uploadLabelDataResponse.fileId) {
          fileId = this.uploadLabelDataResponse.fileId
        } else {
          this.$message.error('Please upload the one-hot file first.')
          return
        }
      }
      this.loading = true
      const requestData = { fileId: fileId }
      cleansing(requestData)
        .then(response => {
          const { data } = response
          this.$message.success('Data Cleansing Successfully!')
          const fileName = this.cleansingFileType === 1 ? this.labeledData[0].fileId : this.uploadLabelDataResponse.fileName
          this.cleansedData = [{
            dataFileName: fileName,
            fileId: data.fileId,
            status: 'Successfully',
            columns: data.columns
          }]
          this.sampleBalancingHeaderOptions = this.cleansedData[0].columns
          this.sampleBalancingHeader = this.sampleBalancingHeaderOptions[0]
        })
        .finally(() => {
          this.loading = false
        })
    },
    handlerChangeDimensionalityReductionFileType() {
      if (this.dimensionalityReductionFileType === 1) {
        if (this.cleansedData && this.cleansedData[0]) {
          this.dimensionalityReductionHeaderOptions = this.cleansedData[0].columns
          this.dimensionalityReductionHeader = this.dimensionalityReductionHeaderOptions[0]
        } else {
          this.dimensionalityReductionHeaderOptions = []
          this.dimensionalityReductionHeader = null
        }
      }

      if (this.dimensionalityReductionFileType === 2) {
        if (this.uploadCleansDataResponse && this.uploadCleansDataResponse.items) {
          this.dimensionalityReductionHeaderOptions = this.uploadCleansDataResponse.items
          this.dimensionalityReductionHeader = this.dimensionalityReductionHeaderOptions[0]
        } else {
          this.dimensionalityReductionHeaderOptions = []
          this.dimensionalityReductionHeader = null
        }
      }
    },
    handlerDimensionalityReduction() {
      let fileId = null
      if (this.dimensionalityReductionFileType === 1) {
        if (this.sampleBalancingData[0] && this.sampleBalancingData[0].fileId) {
          fileId = this.sampleBalancingData[0].fileId
        } else {
          this.$message.error('Please complete the previous step first.')
          return
        }
      } else {
        if (this.uploadSampleBalancingDataResponse && this.uploadSampleBalancingDataResponse.fileId) {
          fileId = this.uploadSampleBalancingDataResponse.fileId
        } else {
          this.$message.error('Please upload the sample balanced file first.')
          return
        }
      }
      const requestData = {
        fileId: fileId,
        label: this.dimensionalityReductionHeader
      }
      this.loading = true
      dimensionalityReduction(requestData)
        .then(response => {
          const { data } = response
          let dataFileName = ''
          if (this.dimensionalityReductionFileType === 1) {
            dataFileName = this.sampleBalancingData[0].fileId
          }
          if (this.dimensionalityReductionFileType === 2) {
            dataFileName = this.uploadSampleBalancingDataResponse.fileName
          }
          this.dimensionalityReductionData = [
            {
              dataFileName: dataFileName,
              fileId: data.fileId,
              status: 'Successfully',
              columns: data.columns,
              heatmap: data.heatmap
            }
          ]
          this.$message.success('Data Dimensionality Reduction Successfully!')
        })
        .finally(() => {
          this.loading = false
        })
    },
    cellStyle({ row, column }) {
      if ((row[column.property] === '' || !row[column.property]) && column.label !== '#' && row[column.property] !== 0) {
        return { background: '#E4E7ED' }
      }
    },
    handlerStartPCA() {
      let fileId = null
      if (this.pcaFileType === 1) {
        if (this.dimensionalityReductionData[0] && this.dimensionalityReductionData[0].fileId) {
          fileId = this.dimensionalityReductionData[0].fileId
        } else {
          this.$message.error('Please complete the previous step first.')
          return
        }
      } else {
        if (this.uploadDimensionalityReductionDataResponse && this.uploadDimensionalityReductionDataResponse.fileId) {
          fileId = this.uploadDimensionalityReductionDataResponse.fileId
        } else {
          this.$message.error('Please upload the dimensionality reduction file first.')
          return
        }
      }
      this.loading = true
      pcaAnalysis({
        fileId: fileId
      })
        .then(response => {
          const { data } = response
          let dataFileName = null
          if (this.pcaFileType === 1) {
            dataFileName = this.dimensionalityReductionData[0].fileId
          }
          if (this.pcaFileType === 2) {
            dataFileName = this.uploadDimensionalityReductionDataResponse.fileName
          }
          this.pcaData = [
            {
              dataFileName: dataFileName,
              fileId: data.fileId,
              status: 'Successfully',
              columns: data.columns
            }
          ]
          this.$message.success('Principal Component Analysis Successfully!')
        })
        .finally(() => {
          this.loading = false
        })
    },
    handlerStartSampleBalancing() {
      let fileId = null
      if (this.sampleBalancingFileType === 1) {
        if (this.cleansedData[0] && this.cleansedData[0].fileId) {
          fileId = this.cleansedData[0].fileId
        } else {
          this.$message.error('Please complete the previous step first.')
          return
        }
      } else {
        if (this.uploadCleansDataResponse && this.uploadCleansDataResponse.fileId) {
          fileId = this.uploadCleansDataResponse.fileId
        } else {
          this.$message.error('Please upload the cleansed file first.')
          return
        }
      }
      this.loading = true
      balancedDataSet({ fileId: fileId, label: this.sampleBalancingHeader })
        .then(response => {
          const { data } = response
          let dataFileName = null
          if (this.sampleBalancingFileType === 1) {
            dataFileName = this.cleansedData[0].fileId
          }
          if (this.sampleBalancingFileType === 2) {
            dataFileName = this.uploadCleansDataResponse.fileName
          }
          this.sampleBalancingData = [
            {
              dataFileName: dataFileName,
              fileId: data.fileId,
              status: 'Successfully',
              columns: data.columns
            }
          ]
          this.$message.success('Sample Balance Successfully!')

          this.dimensionalityReductionHeaderOptions = this.sampleBalancingData[0].columns
          this.dimensionalityReductionHeader = this.dimensionalityReductionHeaderOptions[0]
        })
        .finally(() => {
          this.loading = false
        })
    },
    handlerTrainingPercentChange(value) {
      if ((!value && value !== 0) || value === '' || value.length === 0) {
        this.testingPercent = null
      } else {
        this.testingPercent = 100 - value
      }
    },
    handlerClickActive(step) {
      this.active = step
    }
  }
}
</script>

<style lang="scss">
.data-visualization-data-preparation-container {
  //width: 1600px;
  margin: 20px auto 0;

  .d-inline-block {
    display: inline-block;
    vertical-align: middle;
  }

  .download-template {
    margin-left: 40px;
  }

  .start-auto-process {
    margin-top: 20px;
  }

  .processed-data-tip {
    margin-top: 20px;
  }

  .tip {
    color: #409EFF;
    margin-bottom: 20px;
    &.margin-bottom-none {
      margin-bottom: 0;
    }
  }

  .header-select {
    margin-right: 20px;
    width: 350px;
  }

  .label-item {
    margin-bottom: 10px;
  }

  .file-label, .file-id {
    display: inline-block;
  }

  .file-label {
    margin-bottom: 0;
  }

  .file-id {
    margin: 0 20px 0 10px;
  }

  .start-data-cleansing, .feature-selection-label-select, .start-pca-tip {
    margin-right: 20px;
  }

  .node-tip {
    color: #707070;
  }

  .view-btn {
    margin-left: 10px;
  }

  .label-ps {
    max-height: 300px;
  }

  .feature-report, .principal-components-report, .start-pca-tip {
    margin-bottom: 0;
  }

  .start-pca-tip {
    display: inline-block;
  }

  .input-number {
    margin-bottom: 10px;
  }

  .percent {
    width: 350px;
  }

  .el-input.is-disabled .el-input__inner {
    text-align: center;
  }
  .labeling-ps {
    height: 500px;
  }
}
</style>
