<template>
  <div class="customize-dialog-container">
    <el-dialog
      :visible.sync="customizeDialogVisible"
      :title="title"
      :width="dialogWidth"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <perfect-scrollbar>
        <slot />
      </perfect-scrollbar>
      <span slot="footer" class="dialog-footer">
        <el-button v-if="cancelButton" @click="customizeDialogVisible = false">Cancel</el-button>
        <el-button type="primary" @click="handlerConfirm()">Confirm</el-button>
      </span>
    </el-dialog>
  </div>

</template>

<script>
export default {
  name: 'CustomizeDialog',
  props: {
    title: {
      type: String,
      required: false,
      default: ''
    },
    dialogVisible: {
      type: Boolean,
      required: true,
      default: false
    },
    cancelButton: {
      type: Boolean,
      required: false,
      default: false
    },
    maxWidth: {
      type: Number,
      required: false,
      default: 800
    }
  },
  data() {
    return {
      dialogWidth: null
    }
  },
  computed: {
    customizeDialogVisible: {
      set(visible) {
        this.$emit('update:dialogVisible', visible)
      },
      get() {
        return this.dialogVisible
      }
    }
  },
  watch: {
    screenWidth: {
      immediate: true,
      handler(newValue) {
        this.setDialogWidth(newValue)
      }
    }
  },
  mounted() {
    const vue = this
    window.onresize = () => {
      return (() => {
        vue.screenWidth = document.body.clientWidth
        this.setDialogWidth(document.body.clientWidth)
      })()
    }
    this.setDialogWidth(document.body.clientWidth)
  },
  methods: {
    setDialogWidth(value) {
      if (value - 100 > this.maxWidth) {
        this.dialogWidth = this.maxWidth + 'px'
      } else {
        this.dialogWidth = value - 20 + 'px'
      }
    },
    handlerConfirm() {
      this.$emit('confirm')
      this.customizeDialogVisible = false
    }
  }
}
</script>

<style lang="scss">
.customize-dialog-container {
  .el-dialog__header {
    background-color: #f6f7fa;
    border-radius: 5px;
  }
  .el-dialog__header {
    padding: 10px 20px;
  }
  .el-dialog__body {
    padding-bottom: 0;
  }
  .ps {
    height: 500px;
  }
}
</style>
