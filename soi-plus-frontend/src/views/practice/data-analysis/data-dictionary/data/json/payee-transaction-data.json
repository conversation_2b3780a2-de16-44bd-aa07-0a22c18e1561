{"zh": {"title": "支付交易数据", "data_table_description": ["该数据表包含了支付交易。例如支付交易中，收款人编号，支付金额等。", "可能用到的业务场景：查询支付交易详细信息。"], "data_table_type": "SimBank数据字典 - 支付交易数据", "headers": ["数据表描述", "列名", "数据类型", "长度", "描述", "备注"], "fields": [{"column_name": "ID", "data_type": "int", "length": 20, "remark": ["自增序号"], "description": []}, {"column_name": "PayeeCategoryID", "data_type": "<PERSON><PERSON><PERSON>", "length": 50, "remark": ["收款人的类别编号"], "description": []}, {"column_name": "PayeeCategory", "data_type": "<PERSON><PERSON><PERSON>", "length": 70, "remark": ["收款人类别名称"], "description": []}, {"column_name": "PayeeID", "data_type": "<PERSON><PERSON><PERSON>", "length": 50, "remark": ["收款方编号"], "description": ["例如，交水费的话，是给水利公司交。 这个按照服务大类分的话，就属于生活缴费，那么这个水利公司就会有一个id在系统中唯一标志在生活缴费这个大类下面的这一家水利公司。"]}, {"column_name": "PayeeNumber", "data_type": "<PERSON><PERSON><PERSON>", "length": 50, "remark": ["收款方的账户编号"], "description": ["例如，交水费的话，这里要添加您家在水利公司的账户编号。"]}, {"column_name": "CustomerNumber", "data_type": "<PERSON><PERSON><PERSON>", "length": 25, "remark": ["客户编号。该编号是系统自动生成的唯一标志银行客户。"], "description": []}, {"column_name": "SandBoxId", "data_type": "<PERSON><PERSON><PERSON>", "length": 50, "remark": ["沙盒编号"], "description": []}, {"column_name": "TransactionDealNumber", "data_type": "<PERSON><PERSON><PERSON>", "length": 50, "remark": ["交易流水号"], "description": []}, {"column_name": "CustomerAccountType", "data_type": "<PERSON><PERSON><PERSON>", "length": 4, "remark": ["客户输入的银行账户的类型"], "description": ["账户类型", "SAVI - 001结尾的储蓄账户", "CURR - 002结尾的活期账户", "CRED - 信用卡账户。"]}, {"column_name": "CustomerAccountNumber", "data_type": "<PERSON><PERSON><PERSON>", "length": 34, "remark": ["账户号码"], "description": ["客户银行账户。支持001结尾的储蓄账户；002结尾的活期账户和信用卡账户。"]}, {"column_name": "TransactionTime", "data_type": "decimal", "length": 20, "remark": ["交易时间"], "description": []}, {"column_name": "TransactionAmount", "data_type": "decimal", "length": 18, "remark": ["交易金额"], "description": []}, {"column_name": "TransactionCurrency", "data_type": "<PERSON><PERSON><PERSON>", "length": 3, "remark": ["交易货币类型。 默认支持：HKD"], "description": []}, {"column_name": "PaymentEffectiveDay", "data_type": "decimal", "length": 20, "remark": ["交易指定的日期"], "description": ["例如：您想要指定2天后自动交水费，那么就可以设定2天后的日期，系统会在2天后的那天自动扣款缴费。"]}, {"column_name": "Remarks", "data_type": "<PERSON><PERSON><PERSON>", "length": 140, "remark": ["备注信息"], "description": []}, {"column_name": "LastUpdateDate", "data_type": "decimal", "length": 20, "remark": ["记录更新时间"], "description": []}, {"column_name": "CreateDate", "data_type": "decimal", "length": 20, "remark": ["记录创建时间"], "description": []}, {"column_name": "Status", "data_type": "<PERSON><PERSON><PERSON>", "length": 4, "remark": ["交易状态"], "description": ["交易状态：", "EFCT：交易成功", "INEF：交易无效"]}]}, "en": {"title": "Payee Transaction Data", "data_table_description": ["This data table contains payment transactions. For example, in payment transactions, the payee's number, payment amount, etc.", "Possible business scenarios: querying payment transaction details."], "data_table_type": "SimBank Data Dictionary - Payee Transaction Data", "headers": ["Data table description", "Column Name", "Data Type", "Length", "Description", "Remark"], "fields": [{"column_name": "ID", "data_type": "int", "length": 20, "remark": ["Auto Incrementing"], "description": []}, {"column_name": "PayeeCategoryID", "data_type": "<PERSON><PERSON><PERSON>", "length": 50, "remark": ["Category id of payee"], "description": []}, {"column_name": "PayeeCategory", "data_type": "<PERSON><PERSON><PERSON>", "length": 70, "remark": ["Category name of payee"], "description": []}, {"column_name": "PayeeID", "data_type": "<PERSON><PERSON><PERSON>", "length": 50, "remark": ["A Payee ID that can uniquely label a Payee within its corresponding Payee category"], "description": []}, {"column_name": "PayeeNumber", "data_type": "<PERSON><PERSON><PERSON>", "length": 50, "remark": ["Payee number"], "description": []}, {"column_name": "CustomerNumber", "data_type": "<PERSON><PERSON><PERSON>", "length": 25, "remark": ["Branch code + runing squence that  was defined in system configeration table. Each bank customer has a unique customernumber."], "description": []}, {"column_name": "SandBoxId", "data_type": "<PERSON><PERSON><PERSON>", "length": 50, "remark": ["Used for sandbox allocation for each user"], "description": []}, {"column_name": "TransactionDealNumber", "data_type": "<PERSON><PERSON><PERSON>", "length": 50, "remark": ["Transaction deal number"], "description": []}, {"column_name": "CustomerAccountType", "data_type": "<PERSON><PERSON><PERSON>", "length": 4, "remark": ["The type of the account that the customer inputted in the payment."], "description": ["Possible values:", "SAVI - Saving Account", "CURR - Current Account", "CRED - Credit Card"]}, {"column_name": "CustomerAccountNumber", "data_type": "<PERSON><PERSON><PERSON>", "length": 34, "remark": ["The bank account that the customer inputted to make payment."], "description": []}, {"column_name": "TransactionTime", "data_type": "decimal", "length": 20, "remark": ["Transaction time"], "description": []}, {"column_name": "TransactionAmount", "data_type": "decimal", "length": 18, "remark": ["Transaction amount"], "description": []}, {"column_name": "TransactionCurrency", "data_type": "<PERSON><PERSON><PERSON>", "length": 3, "remark": ["Currency of the payment transacton"], "description": []}, {"column_name": "PaymentEffectiveDay", "data_type": "decimal", "length": 20, "remark": ["The day the payment is actually made successfully by your bank and it should be within 1 year from current time"], "description": []}, {"column_name": "Remarks", "data_type": "<PERSON><PERSON><PERSON>", "length": 140, "remark": ["Any remark that the customer made in the transaction."], "description": []}, {"column_name": "LastUpdateDate", "data_type": "decimal", "length": 20, "remark": ["The last update date of the record"], "description": []}, {"column_name": "CreateDate", "data_type": "decimal", "length": 20, "remark": ["Creation date of the record"], "description": []}, {"column_name": "Status", "data_type": "<PERSON><PERSON><PERSON>", "length": 4, "remark": ["Status of the transaction."], "description": ["Possible values:", "EFCT - effective", "INEF - ineffective"]}]}, "cht": {"title": "支付交易数据", "data_table_description": ["该数据表包含了支付交易。例如支付交易中，收款人编号，支付金额等。", "可能用到的业务场景：查询支付交易详细信息。"], "data_table_type": "SimBank数据字典 - 支付交易数据", "headers": ["数据表描述", "列名", "数据类型", "长度", "描述", "备注"], "fields": [{"column_name": "ID", "data_type": "int", "length": 20, "remark": ["自增序号"], "description": []}, {"column_name": "PayeeCategoryID", "data_type": "<PERSON><PERSON><PERSON>", "length": 50, "remark": ["收款人的类别编号"], "description": []}, {"column_name": "PayeeCategory", "data_type": "<PERSON><PERSON><PERSON>", "length": 70, "remark": ["收款人类别名称"], "description": []}, {"column_name": "PayeeID", "data_type": "<PERSON><PERSON><PERSON>", "length": 50, "remark": ["收款方编号"], "description": ["例如，交水费的话，是给水利公司交。 这个按照服务大类分的话，就属于生活缴费，那么这个水利公司就会有一个id在系统中唯一标志在生活缴费这个大类下面的这一家水利公司。"]}, {"column_name": "PayeeNumber", "data_type": "<PERSON><PERSON><PERSON>", "length": 50, "remark": ["收款方的账户编号"], "description": ["例如，交水费的话，这里要添加您家在水利公司的账户编号。"]}, {"column_name": "CustomerNumber", "data_type": "<PERSON><PERSON><PERSON>", "length": 25, "remark": ["客户编号。该编号是系统自动生成的唯一标志银行客户。"], "description": []}, {"column_name": "SandBoxId", "data_type": "<PERSON><PERSON><PERSON>", "length": 50, "remark": ["沙盒编号"], "description": []}, {"column_name": "TransactionDealNumber", "data_type": "<PERSON><PERSON><PERSON>", "length": 50, "remark": ["交易流水号"], "description": []}, {"column_name": "CustomerAccountType", "data_type": "<PERSON><PERSON><PERSON>", "length": 4, "remark": ["客户输入的银行账户的类型"], "description": ["账户类型", "SAVI - 001结尾的储蓄账户", "CURR - 002结尾的活期账户", "CRED - 信用卡账户。"]}, {"column_name": "CustomerAccountNumber", "data_type": "<PERSON><PERSON><PERSON>", "length": 34, "remark": ["账户号码"], "description": ["客户银行账户。支持001结尾的储蓄账户；002结尾的活期账户和信用卡账户。"]}, {"column_name": "TransactionTime", "data_type": "decimal", "length": 20, "remark": ["交易时间"], "description": []}, {"column_name": "TransactionAmount", "data_type": "decimal", "length": 18, "remark": ["交易金额"], "description": []}, {"column_name": "TransactionCurrency", "data_type": "<PERSON><PERSON><PERSON>", "length": 3, "remark": ["交易货币类型。 默认支持：HKD"], "description": []}, {"column_name": "PaymentEffectiveDay", "data_type": "decimal", "length": 20, "remark": ["交易指定的日期"], "description": ["例如：您想要指定2天后自动交水费，那么就可以设定2天后的日期，系统会在2天后的那天自动扣款缴费。"]}, {"column_name": "Remarks", "data_type": "<PERSON><PERSON><PERSON>", "length": 140, "remark": ["备注信息"], "description": []}, {"column_name": "LastUpdateDate", "data_type": "decimal", "length": 20, "remark": ["记录更新时间"], "description": []}, {"column_name": "CreateDate", "data_type": "decimal", "length": 20, "remark": ["记录创建时间"], "description": []}, {"column_name": "Status", "data_type": "<PERSON><PERSON><PERSON>", "length": 4, "remark": ["交易状态"], "description": ["交易状态：", "EFCT：交易成功", "INEF：交易无效"]}]}}