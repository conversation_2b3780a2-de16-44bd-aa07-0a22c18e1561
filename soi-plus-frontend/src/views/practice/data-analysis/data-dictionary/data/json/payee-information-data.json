{"zh": {"title": "收款人详细信息数据", "data_table_description": ["该数据表包含了收款人详细信息。 例如收款人联系方式，地址，收款账户信息等。 ", "可能用到的业务场景：查询收款人详细信息。"], "data_table_type": "SimBank数据字典 - 收款人详细信息数据", "headers": ["数据表描述", "列名", "数据类型", "长度", "描述", "备注"], "fields": [{"column_name": "ID", "data_type": "int", "length": 20, "remark": ["自增序号"], "description": []}, {"column_name": "PayeeID", "data_type": "<PERSON><PERSON><PERSON>", "length": 50, "remark": ["收款人编号"], "description": []}, {"column_name": "SubCategory", "data_type": "<PERSON><PERSON><PERSON>", "length": 70, "remark": ["收款人子分类"], "description": []}, {"column_name": "PayeeCategoryID", "data_type": "<PERSON><PERSON><PERSON>", "length": 50, "remark": ["收款人分类编号"], "description": []}, {"column_name": "PayeeName", "data_type": "<PERSON><PERSON><PERSON>", "length": 140, "remark": ["收款人名称"], "description": []}, {"column_name": "PhoneNumber", "data_type": "<PERSON><PERSON><PERSON>", "length": 34, "remark": ["收款人电话"], "description": []}, {"column_name": "PayeeAccountNumber", "data_type": "<PERSON><PERSON><PERSON>", "length": 34, "remark": ["收款人银行账户"], "description": []}, {"column_name": "LastUpdateDate", "data_type": "decimal", "length": 20, "remark": ["最后一次更新时间"], "description": []}, {"column_name": "CreateDate", "data_type": "decimal", "length": 20, "remark": ["创建时间"], "description": []}, {"column_name": "PayeeAddressFormat", "data_type": "<PERSON><PERSON><PERSON>", "length": 1, "remark": ["地址格式：", "S - 结构化格式", "F - 自由格式"], "description": []}, {"column_name": "PayeeDepartment", "data_type": "<PERSON><PERSON><PERSON>", "length": 70, "remark": ["收款部门"], "description": []}, {"column_name": "PayeeSubDepartment", "data_type": "<PERSON><PERSON><PERSON>", "length": 70, "remark": ["收款子部门"], "description": []}, {"column_name": "PayeeStreetName", "data_type": "<PERSON><PERSON><PERSON>", "length": 70, "remark": ["收款人所在街道名称"], "description": []}, {"column_name": "PayeeBuildingNumber", "data_type": "<PERSON><PERSON><PERSON>", "length": 16, "remark": ["收款人门牌号"], "description": []}, {"column_name": "PayeeBuildingName", "data_type": "<PERSON><PERSON><PERSON>", "length": 35, "remark": ["收款人楼宇名称"], "description": []}, {"column_name": "PayeeFloor", "data_type": "<PERSON><PERSON><PERSON>", "length": 70, "remark": ["收款人所在楼层"], "description": []}, {"column_name": "PayeePostBox", "data_type": "<PERSON><PERSON><PERSON>", "length": 16, "remark": ["收款人邮政信箱"], "description": []}, {"column_name": "PayeeRoom", "data_type": "<PERSON><PERSON><PERSON>", "length": 70, "remark": ["收款人房间号"], "description": []}, {"column_name": "PayeePostCode", "data_type": "<PERSON><PERSON><PERSON>", "length": 16, "remark": ["收款人邮政编码"], "description": []}, {"column_name": "PayeeTownName", "data_type": "<PERSON><PERSON><PERSON>", "length": 35, "remark": ["收款人城镇名称"], "description": []}, {"column_name": "PayeeTownLocationName", "data_type": "<PERSON><PERSON><PERSON>", "length": 35, "remark": ["收款人城镇位置名称"], "description": []}, {"column_name": "PayeeDistrictName", "data_type": "<PERSON><PERSON><PERSON>", "length": 35, "remark": ["收款人所在区县名称"], "description": []}, {"column_name": "PayeeCountrySubDivision", "data_type": "<PERSON><PERSON><PERSON>", "length": 35, "remark": ["收款人国家行政区划"], "description": []}, {"column_name": "PayeeCountry", "data_type": "<PERSON><PERSON><PERSON>", "length": 2, "remark": ["收款人所在国家"], "description": []}, {"column_name": "PayeeAddressLine1", "data_type": "<PERSON><PERSON><PERSON>", "length": 370, "remark": ["收款人地址1"], "description": []}, {"column_name": "PayeeAddressLine2", "data_type": "<PERSON><PERSON><PERSON>", "length": 70, "remark": ["收款人地址2"], "description": []}, {"column_name": "PayeeAddressLine3", "data_type": "<PERSON><PERSON><PERSON>", "length": 70, "remark": ["收款人地址3"], "description": []}, {"column_name": "PayeeAddressLine4", "data_type": "<PERSON><PERSON><PERSON>", "length": 70, "remark": ["收款人地址4"], "description": []}, {"column_name": "PayeeAddressLine5", "data_type": "<PERSON><PERSON><PERSON>", "length": 70, "remark": ["收款人地址5"], "description": []}, {"column_name": "PayeeAddressLine6", "data_type": "<PERSON><PERSON><PERSON>", "length": 70, "remark": ["收款人地址6"], "description": []}, {"column_name": "PayeeAddressLine7", "data_type": "<PERSON><PERSON><PERSON>", "length": 70, "remark": ["收款人地址7"], "description": []}]}, "en": {"title": "Payee Information Data", "data_table_description": ["This data table contains detailed information about the payee. For example, the recipient's contact information, address, account information, etc.", "Possible business scenarios: querying detailed information of the payee."], "data_table_type": "SimBank Data Dictionary - Payee Information Data", "headers": ["Data table description", "Column Name", "Data Type", "Length", "Description", "Remark"], "fields": [{"column_name": "ID", "data_type": "int", "length": 20, "remark": ["Auto Incrementing"], "description": []}, {"column_name": "PayeeID", "data_type": "<PERSON><PERSON><PERSON>", "length": 50, "remark": [" ", "A unique ID to identify a payee in a dedicated payee category."], "description": []}, {"column_name": "SubCategory", "data_type": "<PERSON><PERSON><PERSON>", "length": 70, "remark": ["Payee sub category"], "description": []}, {"column_name": "PayeeCategoryID", "data_type": "<PERSON><PERSON><PERSON>", "length": 50, "remark": ["A unique ID to identify a payee category, such as a school, a water company and so on."], "description": []}, {"column_name": "PayeeName", "data_type": "<PERSON><PERSON><PERSON>", "length": 140, "remark": ["Payee name"], "description": []}, {"column_name": "PhoneNumber", "data_type": "<PERSON><PERSON><PERSON>", "length": 34, "remark": ["A unique number or number & string to identify a user of a dedicated payee."], "description": []}, {"column_name": "PayeeAccountNumber", "data_type": "<PERSON><PERSON><PERSON>", "length": 34, "remark": ["Bank account number of payee"], "description": []}, {"column_name": "LastUpdateDate", "data_type": "decimal", "length": 20, "remark": ["The last update date of the record"], "description": []}, {"column_name": "CreateDate", "data_type": "decimal", "length": 20, "remark": ["Creation date of the record"], "description": []}, {"column_name": "PayeeAddressFormat", "data_type": "<PERSON><PERSON><PERSON>", "length": 1, "remark": ["Payee address format."], "description": ["Address Format:", "S - Structure format", "F - Free format."]}, {"column_name": "PayeeDepartment", "data_type": "<PERSON><PERSON><PERSON>", "length": 70, "remark": ["Payee department "], "description": []}, {"column_name": "PayeeSubDepartment", "data_type": "<PERSON><PERSON><PERSON>", "length": 70, "remark": ["Payee sub department"], "description": []}, {"column_name": "PayeeStreetName", "data_type": "<PERSON><PERSON><PERSON>", "length": 70, "remark": ["Payee street name"], "description": []}, {"column_name": "PayeeBuildingNumber", "data_type": "<PERSON><PERSON><PERSON>", "length": 16, "remark": ["Payee building number"], "description": []}, {"column_name": "PayeeBuildingName", "data_type": "<PERSON><PERSON><PERSON>", "length": 35, "remark": ["Payee building name"], "description": []}, {"column_name": "PayeeFloor", "data_type": "<PERSON><PERSON><PERSON>", "length": 70, "remark": ["Payee floor"], "description": []}, {"column_name": "PayeePostBox", "data_type": "<PERSON><PERSON><PERSON>", "length": 16, "remark": ["Payee post box"], "description": []}, {"column_name": "PayeeRoom", "data_type": "<PERSON><PERSON><PERSON>", "length": 70, "remark": ["Payee room"], "description": []}, {"column_name": "PayeePostCode", "data_type": "<PERSON><PERSON><PERSON>", "length": 16, "remark": ["Payee post code"], "description": []}, {"column_name": "PayeeTownName", "data_type": "<PERSON><PERSON><PERSON>", "length": 35, "remark": ["Payee town name "], "description": []}, {"column_name": "PayeeTownLocationName", "data_type": "<PERSON><PERSON><PERSON>", "length": 35, "remark": ["Payee town location name"], "description": []}, {"column_name": "PayeeDistrictName", "data_type": "<PERSON><PERSON><PERSON>", "length": 35, "remark": ["Payee district name"], "description": []}, {"column_name": "PayeeCountrySubDivision", "data_type": "<PERSON><PERSON><PERSON>", "length": 35, "remark": ["Payee country subdivision"], "description": []}, {"column_name": "PayeeCountry", "data_type": "<PERSON><PERSON><PERSON>", "length": 2, "remark": ["Payee country"], "description": []}, {"column_name": "PayeeAddressLine1", "data_type": "<PERSON><PERSON><PERSON>", "length": 370, "remark": ["Payee address line1"], "description": []}, {"column_name": "PayeeAddressLine2", "data_type": "<PERSON><PERSON><PERSON>", "length": 70, "remark": ["Payee address line2"], "description": []}, {"column_name": "PayeeAddressLine3", "data_type": "<PERSON><PERSON><PERSON>", "length": 70, "remark": ["Payee address line3"], "description": []}, {"column_name": "PayeeAddressLine4", "data_type": "<PERSON><PERSON><PERSON>", "length": 70, "remark": ["Payee address line4"], "description": []}, {"column_name": "PayeeAddressLine5", "data_type": "<PERSON><PERSON><PERSON>", "length": 70, "remark": ["Payee address line5"], "description": []}, {"column_name": "PayeeAddressLine6", "data_type": "<PERSON><PERSON><PERSON>", "length": 70, "remark": ["Payee address line6"], "description": []}, {"column_name": "PayeeAddressLine7", "data_type": "<PERSON><PERSON><PERSON>", "length": 70, "remark": ["Payee address line7"], "description": []}]}, "cht": {"title": "收款人详细信息数据", "data_table_description": ["该数据表包含了收款人详细信息。 例如收款人联系方式，地址，收款账户信息等。 ", "可能用到的业务场景：查询收款人详细信息。"], "data_table_type": "SimBank数据字典 - 收款人详细信息数据", "headers": ["数据表描述", "列名", "数据类型", "长度", "描述", "备注"], "fields": [{"column_name": "ID", "data_type": "int", "length": 20, "remark": ["自增序号"], "description": []}, {"column_name": "PayeeID", "data_type": "<PERSON><PERSON><PERSON>", "length": 50, "remark": ["收款人编号"], "description": []}, {"column_name": "SubCategory", "data_type": "<PERSON><PERSON><PERSON>", "length": 70, "remark": ["收款人子分类"], "description": []}, {"column_name": "PayeeCategoryID", "data_type": "<PERSON><PERSON><PERSON>", "length": 50, "remark": ["收款人分类编号"], "description": []}, {"column_name": "PayeeName", "data_type": "<PERSON><PERSON><PERSON>", "length": 140, "remark": ["收款人名称"], "description": []}, {"column_name": "PhoneNumber", "data_type": "<PERSON><PERSON><PERSON>", "length": 34, "remark": ["收款人电话"], "description": []}, {"column_name": "PayeeAccountNumber", "data_type": "<PERSON><PERSON><PERSON>", "length": 34, "remark": ["收款人银行账户"], "description": []}, {"column_name": "LastUpdateDate", "data_type": "decimal", "length": 20, "remark": ["最后一次更新时间"], "description": []}, {"column_name": "CreateDate", "data_type": "decimal", "length": 20, "remark": ["创建时间"], "description": []}, {"column_name": "PayeeAddressFormat", "data_type": "<PERSON><PERSON><PERSON>", "length": 1, "remark": ["地址格式：", "S - 结构化格式", "F - 自由格式"], "description": []}, {"column_name": "PayeeDepartment", "data_type": "<PERSON><PERSON><PERSON>", "length": 70, "remark": ["收款部门"], "description": []}, {"column_name": "PayeeSubDepartment", "data_type": "<PERSON><PERSON><PERSON>", "length": 70, "remark": ["收款子部门"], "description": []}, {"column_name": "PayeeStreetName", "data_type": "<PERSON><PERSON><PERSON>", "length": 70, "remark": ["收款人所在街道名称"], "description": []}, {"column_name": "PayeeBuildingNumber", "data_type": "<PERSON><PERSON><PERSON>", "length": 16, "remark": ["收款人门牌号"], "description": []}, {"column_name": "PayeeBuildingName", "data_type": "<PERSON><PERSON><PERSON>", "length": 35, "remark": ["收款人楼宇名称"], "description": []}, {"column_name": "PayeeFloor", "data_type": "<PERSON><PERSON><PERSON>", "length": 70, "remark": ["收款人所在楼层"], "description": []}, {"column_name": "PayeePostBox", "data_type": "<PERSON><PERSON><PERSON>", "length": 16, "remark": ["收款人邮政信箱"], "description": []}, {"column_name": "PayeeRoom", "data_type": "<PERSON><PERSON><PERSON>", "length": 70, "remark": ["收款人房间号"], "description": []}, {"column_name": "PayeePostCode", "data_type": "<PERSON><PERSON><PERSON>", "length": 16, "remark": ["收款人邮政编码"], "description": []}, {"column_name": "PayeeTownName", "data_type": "<PERSON><PERSON><PERSON>", "length": 35, "remark": ["收款人城镇名称"], "description": []}, {"column_name": "PayeeTownLocationName", "data_type": "<PERSON><PERSON><PERSON>", "length": 35, "remark": ["收款人城镇位置名称"], "description": []}, {"column_name": "PayeeDistrictName", "data_type": "<PERSON><PERSON><PERSON>", "length": 35, "remark": ["收款人所在区县名称"], "description": []}, {"column_name": "PayeeCountrySubDivision", "data_type": "<PERSON><PERSON><PERSON>", "length": 35, "remark": ["收款人国家行政区划"], "description": []}, {"column_name": "PayeeCountry", "data_type": "<PERSON><PERSON><PERSON>", "length": 2, "remark": ["收款人所在国家"], "description": []}, {"column_name": "PayeeAddressLine1", "data_type": "<PERSON><PERSON><PERSON>", "length": 370, "remark": ["收款人地址1"], "description": []}, {"column_name": "PayeeAddressLine2", "data_type": "<PERSON><PERSON><PERSON>", "length": 70, "remark": ["收款人地址2"], "description": []}, {"column_name": "PayeeAddressLine3", "data_type": "<PERSON><PERSON><PERSON>", "length": 70, "remark": ["收款人地址3"], "description": []}, {"column_name": "PayeeAddressLine4", "data_type": "<PERSON><PERSON><PERSON>", "length": 70, "remark": ["收款人地址4"], "description": []}, {"column_name": "PayeeAddressLine5", "data_type": "<PERSON><PERSON><PERSON>", "length": 70, "remark": ["收款人地址5"], "description": []}, {"column_name": "PayeeAddressLine6", "data_type": "<PERSON><PERSON><PERSON>", "length": 70, "remark": ["收款人地址6"], "description": []}, {"column_name": "PayeeAddressLine7", "data_type": "<PERSON><PERSON><PERSON>", "length": 70, "remark": ["收款人地址7"], "description": []}]}}