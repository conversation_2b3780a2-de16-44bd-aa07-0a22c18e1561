{"zh": {"title": "信用卡交易记录数据", "data_table_description": ["信用卡交易记录表是用于系统化存储信用卡每笔交易明细的结构化数据表，包含交易金额、时间、商户信息、类型等核心字段，以支持交易对账、风险监控、账单生成及争议处理等金融业务场景。"], "data_table_type": "SimBank数据字典 - 信用卡交易记录数据", "headers": ["数据表描述", "列名", "数据类型", "长度", "描述", "备注"], "fields": [{"column_name": "ID", "data_type": "int", "length": 11, "remark": ["主键自增"], "description": []}, {"column_name": "CreditCardNumber", "data_type": "<PERSON><PERSON><PERSON>", "length": 34, "remark": ["信用卡卡号"], "description": []}, {"column_name": "DealNumber", "data_type": "<PERSON><PERSON><PERSON>", "length": 20, "remark": ["信用卡交易编号"], "description": []}, {"column_name": "TransactionDate", "data_type": "decimal", "length": 20, "remark": ["信用卡交易时间"], "description": []}, {"column_name": "MerchantNumber", "data_type": "<PERSON><PERSON><PERSON>", "length": 35, "remark": ["信用卡商户编号"], "description": []}, {"column_name": "TransactionCcy", "data_type": "<PERSON><PERSON><PERSON>", "length": 3, "remark": ["信用卡交易货币类型"], "description": ["示例：\nTransaction Currency: USD"]}, {"column_name": "TransactionAmount", "data_type": "decimal", "length": 18, "remark": ["信用卡交易金额"], "description": ["示例：\nTransaction Amount: 100"]}, {"column_name": "BookingCcy", "data_type": "<PERSON><PERSON><PERSON>", "length": 3, "remark": ["信用卡交易中，发卡机构将原始交易货币（如USD）按汇率转换为持卡人账单或账户的基准货币类型（如HKD）"], "description": ["示例：\nBooking Currency: HKD"]}, {"column_name": "BookingAmount", "data_type": "decimal", "length": 18, "remark": ["指转换后的实际金额（以Booking Currency计价），包含汇率和可能的费用调整后的数值。"], "description": ["示例：\nBooking Amount: HKD 780 (按照发卡行的汇率换算)"]}, {"column_name": "AuthorizationNumber", "data_type": "<PERSON><PERSON><PERSON>", "length": 20, "remark": ["信用卡交易授权编号"], "description": []}, {"column_name": "TransactionType", "data_type": "int", "length": 11, "remark": ["信用卡交易类型"], "description": ["可能的值： \n1- repayment（还款）\n0- post（刷卡）"]}]}, "en": {"title": "Credit Card Transaction Details Data", "data_table_description": ["The Credit Card Transaction Detail Table is a structured data table designed to systematically store detailed records of credit card transactions. It includes core fields such as transaction amount, timestamp, merchant number, and transaction type to support financial operations like reconciliation, fraud detection, statement generation, and dispute resolution."], "data_table_type": "SimBank Data Dictionary - Credit Card Transaction Details Data", "headers": ["Data table description", "Column Name", "Data Type", "Length", "Description", "Remark"], "fields": [{"column_name": "ID", "data_type": "int", "length": 11, "remark": ["Auto Incrementing"], "description": []}, {"column_name": "CreditCardNumber", "data_type": "<PERSON><PERSON><PERSON>", "length": 34, "remark": ["Credit card number"], "description": []}, {"column_name": "DealNumber", "data_type": "<PERSON><PERSON><PERSON>", "length": 20, "remark": ["The unique identifier assigned to a specific  credit card transaction"], "description": []}, {"column_name": "TransactionDate", "data_type": "decimal", "length": 20, "remark": ["Transaction date of a credit card transaction"], "description": []}, {"column_name": "MerchantNumber", "data_type": "<PERSON><PERSON><PERSON>", "length": 35, "remark": ["The unique identifier assigned to a specific credit  card merchant"], "description": []}, {"column_name": "TransactionCcy", "data_type": "<PERSON><PERSON><PERSON>", "length": 3, "remark": ["Currency type of a credit card transaction"], "description": ["Example: Transaction Currency: USD"]}, {"column_name": "TransactionAmount", "data_type": "decimal", "length": 18, "remark": ["Transaction amount of a credit card transaction"], "description": ["Example: Transaction Amount: 100"]}, {"column_name": "BookingCcy", "data_type": "<PERSON><PERSON><PERSON>", "length": 3, "remark": ["Booking Currency refers to the currency type (e.g., HKD) into which a credit card transaction's original transaction currency (e.g., USD) is converted by the issuer for billing or record-keeping purposes."], "description": ["Example: Booking Currency: HKD"]}, {"column_name": "BookingAmount", "data_type": "decimal", "length": 18, "remark": ["Booking Amount refers to the converted monetary value (e.g., in HKD) of a credit card transaction, calculated using the issuer’s exchange rate, which reflects the final amount charged to the cardholder’s account in the booking currency."], "description": ["Example: Booking Amount: HKD 780 (converted at the issuer’s rate)."]}, {"column_name": "AuthorizationNumber", "data_type": "<PERSON><PERSON><PERSON>", "length": 20, "remark": ["Credit Card Authorization Number refers to the unique code generated by the card issuer during a transaction authorization process, confirming approval for the requested amount and serving as a reference for tracking or reconciling the transaction."], "description": ["Possible Values:\n1- repayment\n0- post"]}, {"column_name": "TransactionType", "data_type": "int", "length": 11, "remark": ["Credit card transaction type."], "description": []}]}, "cht": {"title": "信用卡交易记录数据", "data_table_description": ["信用卡交易记录表是用于系统化存储信用卡每笔交易明细的结构化数据表，包含交易金额、时间、商户信息、类型等核心字段，以支持交易对账、风险监控、账单生成及争议处理等金融业务场景。"], "data_table_type": "SimBank数据字典 - 信用卡交易记录数据", "headers": ["数据表描述", "列名", "数据类型", "长度", "描述", "备注"], "fields": [{"column_name": "ID", "data_type": "int", "length": 11, "remark": ["主键自增"], "description": []}, {"column_name": "CreditCardNumber", "data_type": "<PERSON><PERSON><PERSON>", "length": 34, "remark": ["信用卡卡号"], "description": []}, {"column_name": "DealNumber", "data_type": "<PERSON><PERSON><PERSON>", "length": 20, "remark": ["信用卡交易编号"], "description": []}, {"column_name": "TransactionDate", "data_type": "decimal", "length": 20, "remark": ["信用卡交易时间"], "description": []}, {"column_name": "MerchantNumber", "data_type": "<PERSON><PERSON><PERSON>", "length": 35, "remark": ["信用卡商户编号"], "description": []}, {"column_name": "TransactionCcy", "data_type": "<PERSON><PERSON><PERSON>", "length": 3, "remark": ["信用卡交易货币类型"], "description": ["示例：\nTransaction Currency: USD"]}, {"column_name": "TransactionAmount", "data_type": "decimal", "length": 18, "remark": ["信用卡交易金额"], "description": ["示例：\nTransaction Amount: 100"]}, {"column_name": "BookingCcy", "data_type": "<PERSON><PERSON><PERSON>", "length": 3, "remark": ["信用卡交易中，发卡机构将原始交易货币（如USD）按汇率转换为持卡人账单或账户的基准货币类型（如HKD）"], "description": ["示例：\nBooking Currency: HKD"]}, {"column_name": "BookingAmount", "data_type": "decimal", "length": 18, "remark": ["指转换后的实际金额（以Booking Currency计价），包含汇率和可能的费用调整后的数值。"], "description": ["示例：\nBooking Amount: HKD 780 (按照发卡行的汇率换算)"]}, {"column_name": "AuthorizationNumber", "data_type": "<PERSON><PERSON><PERSON>", "length": 20, "remark": ["信用卡交易授权编号"], "description": []}, {"column_name": "TransactionType", "data_type": "int", "length": 11, "remark": ["信用卡交易类型"], "description": ["可能的值： \n1- repayment（还款）\n0- post（刷卡）"]}]}}