{"zh": {"title": "外汇交易数据", "data_table_description": ["该数据表包含了外汇账户的交易记录，例如交易货币类型，交易金额等。", "可能用到的业务场景：查询外汇账户的交易记录。"], "data_table_type": "SimBank数据字典 - 外汇交易数据", "headers": ["数据表描述", "列名", "数据类型", "长度", "描述", "备注"], "fields": [{"column_name": "ID", "data_type": "int", "length": 11, "remark": ["自增序号"], "description": []}, {"column_name": "CountryCode", "data_type": "<PERSON><PERSON><PERSON>", "length": 2, "remark": ["国家代码，如HK"], "description": []}, {"column_name": "ClearingCode", "data_type": "<PERSON><PERSON><PERSON>", "length": 4, "remark": ["银行代码"], "description": []}, {"column_name": "BranchCode", "data_type": "<PERSON><PERSON><PERSON>", "length": 3, "remark": ["分行代码"], "description": []}, {"column_name": "SandBoxId", "data_type": "<PERSON><PERSON><PERSON>", "length": 50, "remark": ["沙盒编号"], "description": []}, {"column_name": "AccountNumber", "data_type": "<PERSON><PERSON><PERSON>", "length": 34, "remark": ["账户号码"], "description": []}, {"column_name": "RelAccountNumber", "data_type": "<PERSON><PERSON><PERSON>", "length": 34, "remark": ["与该外汇账户关联的借记账户。可能是活期账号或储蓄账号"], "description": []}, {"column_name": "TransactionType", "data_type": "<PERSON><PERSON><PERSON>", "length": 4, "remark": ["外汇交易类型"], "description": ["交易类型", "B-买入外币", "S-卖出外币"]}, {"column_name": "LocalCurrency", "data_type": "<PERSON><PERSON><PERSON>", "length": 3, "remark": ["本地货币类型"], "description": ["默认情况下，货币类型设置为HKD"]}, {"column_name": "ForeignCurrency", "data_type": "<PERSON><PERSON><PERSON>", "length": 3, "remark": [], "description": ["支持的外币类型:", "USD,CNY,AUD,CAD,CHF,GBP,JPY,NZD,SGD"]}, {"column_name": "ExchangeRate", "data_type": "decimal", "length": 18, "remark": ["汇率"], "description": []}, {"column_name": "ExchangeAmoutInLocalCurrency", "data_type": "decimal", "length": 18, "remark": ["兑换金额（以当地货币计算）。"], "description": ["例如，如果你想卖出100美元，那么需要把它兑换成当地货币（HKD），当地货币的兑换金额将是712 HKD"]}, {"column_name": "ExchangeAmoutInForeignCurrency", "data_type": "decimal", "length": 18, "remark": ["交易金额（以外币计算）"], "description": ["例如卖出100 USD，那么这里的金额就是100.表示交易100 USD"]}, {"column_name": "PrevBalInForeignCurrencyAccount", "data_type": "decimal", "length": 18, "remark": ["交易之前，账户余额（以外币计算）"], "description": ["例如，交易之前，账户剩余300 USD，那么这里的余额就是300"]}, {"column_name": "PostBalInForeignCurrencyAccount", "data_type": "decimal", "length": 18, "remark": ["交易之后，账户余额（以外币计算）"], "description": ["例如，交易之后，账户剩余200 USD，那么这里的余额就是200。"]}, {"column_name": "TransactionTime", "data_type": "decimal", "length": 14, "remark": ["交易时间"], "description": []}]}, "en": {"title": "Foreign Exchange Data", "data_table_description": ["This data table contains transaction records of foreign exchange accounts, such as transaction currency types, transaction amounts, etc.", "Possible business scenarios: querying transaction records of foreign exchange accounts."], "data_table_type": "SimBank Data Dictionary - Foreign Exchange Data", "headers": ["Data table description", "Column Name", "Data Type", "Length", "Description", "Remark"], "fields": [{"column_name": "ID", "data_type": "int", "length": 11, "remark": ["Auto Incrementing"], "description": []}, {"column_name": "CountryCode", "data_type": "<PERSON><PERSON><PERSON>", "length": 2, "remark": ["A country code is a short, standardized sequence of letters or numbers used to represent a specific country or geographic region. e.g., HK for Hong Kong."], "description": []}, {"column_name": "ClearingCode", "data_type": "<PERSON><PERSON><PERSON>", "length": 4, "remark": ["Clearing code is code for different bank. for example BBIC,HSBC eg."], "description": []}, {"column_name": "BranchCode", "data_type": "<PERSON><PERSON><PERSON>", "length": 3, "remark": ["Branch code is code for different branch of the same bank."], "description": []}, {"column_name": "SandBoxId", "data_type": "<PERSON><PERSON><PERSON>", "length": 50, "remark": ["Used for sandbox allocation for each user."], "description": []}, {"column_name": "AccountNumber", "data_type": "<PERSON><PERSON><PERSON>", "length": 34, "remark": ["The foreign exchange account number of the customer."], "description": []}, {"column_name": "RelAccountNumber", "data_type": "<PERSON><PERSON><PERSON>", "length": 34, "remark": ["The debit account ,maybe current account number or saving account number"], "description": []}, {"column_name": "TransactionType", "data_type": "<PERSON><PERSON><PERSON>", "length": 4, "remark": ["Type of the foreign exchange transaction."], "description": ["Possible values:", "B -Buy foreign currency", "S - Sell foreign currency"]}, {"column_name": "LocalCurrency", "data_type": "<PERSON><PERSON><PERSON>", "length": 3, "remark": ["Local currency type"], "description": ["By default, the local ccy is HKD."]}, {"column_name": "ForeignCurrency", "data_type": "<PERSON><PERSON><PERSON>", "length": 3, "remark": ["Foreign currency type"], "description": ["Supported foreign Currency Code:", "USD,CNY,AUD,CAD,CHF,GBP,JPY,NZD,SGD"]}, {"column_name": "ExchangeRate", "data_type": "decimal", "length": 18, "remark": ["Exchange rate "], "description": []}, {"column_name": "ExchangeAmoutInLocalCurrency", "data_type": "decimal", "length": 18, "remark": ["Exchange amount (calculated in local currency)"], "description": ["For example, if you want to sell 100 USD, then if you want to convert it into local currency (HKD), the exchagne amount in local currency will be 712 HKD"]}, {"column_name": "ExchangeAmoutInForeignCurrency", "data_type": "decimal", "length": 18, "remark": ["Transaction amount (in foreign currency)"], "description": ["For example, if you sell 100 USD, then the amount here is 100. It means that the transaction is 100 USD"]}, {"column_name": "PrevBalInForeignCurrencyAccount", "data_type": "decimal", "length": 18, "remark": ["Before the transaction, the account balance (in foreign currency)"], "description": ["For example, before the transaction, if the account has 300 USD left, the balance here is 300."]}, {"column_name": "PostBalInForeignCurrencyAccount", "data_type": "decimal", "length": 18, "remark": ["After the transaction, the account balance (in foreign currency)"], "description": ["For example, after the transaction, if the account has 200 USD left, the balance here is 200"]}, {"column_name": "TransactionTime", "data_type": "decimal", "length": 14, "remark": ["Transaction time"], "description": []}]}, "cht": {"title": "外汇交易数据", "data_table_description": ["该数据表包含了外汇账户的交易记录，例如交易货币类型，交易金额等。", "可能用到的业务场景：查询外汇账户的交易记录。"], "data_table_type": "SimBank数据字典 - 外汇交易数据", "headers": ["数据表描述", "列名", "数据类型", "长度", "描述", "备注"], "fields": [{"column_name": "ID", "data_type": "int", "length": 11, "remark": ["自增序号"], "description": []}, {"column_name": "CountryCode", "data_type": "<PERSON><PERSON><PERSON>", "length": 2, "remark": ["国家代码，如HK"], "description": []}, {"column_name": "ClearingCode", "data_type": "<PERSON><PERSON><PERSON>", "length": 4, "remark": ["银行代码"], "description": []}, {"column_name": "BranchCode", "data_type": "<PERSON><PERSON><PERSON>", "length": 3, "remark": ["分行代码"], "description": []}, {"column_name": "SandBoxId", "data_type": "<PERSON><PERSON><PERSON>", "length": 50, "remark": ["沙盒编号"], "description": []}, {"column_name": "AccountNumber", "data_type": "<PERSON><PERSON><PERSON>", "length": 34, "remark": ["账户号码"], "description": []}, {"column_name": "RelAccountNumber", "data_type": "<PERSON><PERSON><PERSON>", "length": 34, "remark": ["与该外汇账户关联的借记账户。可能是活期账号或储蓄账号"], "description": []}, {"column_name": "TransactionType", "data_type": "<PERSON><PERSON><PERSON>", "length": 4, "remark": ["外汇交易类型"], "description": ["交易类型", "B-买入外币", "S-卖出外币"]}, {"column_name": "LocalCurrency", "data_type": "<PERSON><PERSON><PERSON>", "length": 3, "remark": ["本地货币类型"], "description": ["默认情况下，货币类型设置为HKD"]}, {"column_name": "ForeignCurrency", "data_type": "<PERSON><PERSON><PERSON>", "length": 3, "remark": [], "description": ["支持的外币类型:", "USD,CNY,AUD,CAD,CHF,GBP,JPY,NZD,SGD"]}, {"column_name": "ExchangeRate", "data_type": "decimal", "length": 18, "remark": ["汇率"], "description": []}, {"column_name": "ExchangeAmoutInLocalCurrency", "data_type": "decimal", "length": 18, "remark": ["兑换金额（以当地货币计算）。"], "description": ["例如，如果你想卖出100美元，那么需要把它兑换成当地货币（HKD），当地货币的兑换金额将是712 HKD"]}, {"column_name": "ExchangeAmoutInForeignCurrency", "data_type": "decimal", "length": 18, "remark": ["交易金额（以外币计算）"], "description": ["例如卖出100 USD，那么这里的金额就是100.表示交易100 USD"]}, {"column_name": "PrevBalInForeignCurrencyAccount", "data_type": "decimal", "length": 18, "remark": ["交易之前，账户余额（以外币计算）"], "description": ["例如，交易之前，账户剩余300 USD，那么这里的余额就是300"]}, {"column_name": "PostBalInForeignCurrencyAccount", "data_type": "decimal", "length": 18, "remark": ["交易之后，账户余额（以外币计算）"], "description": ["例如，交易之后，账户剩余200 USD，那么这里的余额就是200。"]}, {"column_name": "TransactionTime", "data_type": "decimal", "length": 14, "remark": ["交易时间"], "description": []}]}}