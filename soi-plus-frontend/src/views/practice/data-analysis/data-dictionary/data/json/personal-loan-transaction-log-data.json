{"zh": {"title": "个人消费交易记录数据", "data_table_description": ["该数据表包含了个人贷款交易记录。例如银行放款记录，客户还款记录。", "可能用到的业务场景：查询贷款账户的交易记录。"], "data_table_type": "SimBank数据字典 - 个人消费交易记录数据", "headers": ["数据表描述", "列名", "数据类型", "长度", "描述", "备注"], "fields": [{"column_name": "ID", "data_type": "int", "length": 20, "remark": ["自增序号"], "description": []}, {"column_name": "CountryCode", "data_type": "<PERSON><PERSON><PERSON>", "length": 2, "remark": ["国家代码，如HK"], "description": []}, {"column_name": "ClearingCode", "data_type": "<PERSON><PERSON><PERSON>", "length": 20, "remark": ["银行代码"], "description": []}, {"column_name": "BranchCode", "data_type": "<PERSON><PERSON><PERSON>", "length": 20, "remark": ["分行代码"], "description": []}, {"column_name": "SandBoxId", "data_type": "<PERSON><PERSON><PERSON>", "length": 50, "remark": ["沙盒编号"], "description": []}, {"column_name": "AccountNumber", "data_type": "<PERSON><PERSON><PERSON>", "length": 34, "remark": ["贷款账号"], "description": []}, {"column_name": "ContractNumber", "data_type": "<PERSON><PERSON><PERSON>", "length": 50, "remark": ["贷款合同号"], "description": []}, {"column_name": "DebitAccountNumber", "data_type": "<PERSON><PERSON><PERSON>", "length": 34, "remark": ["与贷款账户关联的借记账户，用于偿还贷款。它可以是活期账户号码或储蓄账户号码"], "description": []}, {"column_name": "PhaseNo", "data_type": "<PERSON><PERSON><PERSON>", "length": 140, "remark": ["还款期数"], "description": []}, {"column_name": "DealNumber", "data_type": "<PERSON><PERSON><PERSON>", "length": 20, "remark": ["交易号"], "description": []}, {"column_name": "TransactionTime", "data_type": "decimal", "length": 20, "remark": ["交易时间"], "description": []}, {"column_name": "CurrencyCode", "data_type": "<PERSON><PERSON><PERSON>", "length": 3, "remark": ["货币类型"], "description": []}, {"column_name": "TranAmt", "data_type": "decimal", "length": 18, "remark": ["交易金额"], "description": []}, {"column_name": "TranType", "data_type": "<PERSON><PERSON><PERSON>", "length": 3, "remark": ["交易类型"], "description": ["交易类型", "BRW--贷款", "PMT --还款"]}, {"column_name": "CreationTime", "data_type": "decimal", "length": 20, "remark": ["最后一次更新时间"], "description": []}, {"column_name": "LastUpdateTime", "data_type": "decimal", "length": 20, "remark": ["创建时间"], "description": []}]}, "en": {"title": "Personal Loan Transaction Log Data", "data_table_description": ["This data table contains records of personal loan transactions, such as bank disbursement records and customer repayment records.", "Possible business use case: Querying transaction records for a loan account."], "data_table_type": "SimBank Data Dictionary - Personal Loan Transaction Log Data", "headers": ["Data table description", "Column Name", "Data Type", "Length", "Description", "Remark"], "fields": [{"column_name": "ID", "data_type": "int", "length": 20, "remark": ["Auto Incrementing"], "description": []}, {"column_name": "CountryCode", "data_type": "<PERSON><PERSON><PERSON>", "length": 2, "remark": ["A country code is a short, standardized sequence of letters or numbers used to represent a specific country or geographic region. e.g., HK for Hong Kong"], "description": []}, {"column_name": "ClearingCode", "data_type": "<PERSON><PERSON><PERSON>", "length": 20, "remark": ["Clearing code is code for different bank. for example BBIC,HSBC eg."], "description": []}, {"column_name": "BranchCode", "data_type": "<PERSON><PERSON><PERSON>", "length": 20, "remark": ["Branch code is code for different branch of the same bank"], "description": []}, {"column_name": "SandBoxId", "data_type": "<PERSON><PERSON><PERSON>", "length": 50, "remark": ["Used for sandbox allocation for each user"], "description": []}, {"column_name": "AccountNumber", "data_type": "<PERSON><PERSON><PERSON>", "length": 34, "remark": ["A unique number used to identify a loan account"], "description": []}, {"column_name": "ContractNumber", "data_type": "<PERSON><PERSON><PERSON>", "length": 50, "remark": ["A unique number used to identify a personal loan contract"], "description": []}, {"column_name": "DebitAccountNumber", "data_type": "<PERSON><PERSON><PERSON>", "length": 34, "remark": ["The debit account that is associated with the loan account to make loan repayment. It can be a current account number or a saving account number"], "description": []}, {"column_name": "PhaseNo", "data_type": "<PERSON><PERSON><PERSON>", "length": 140, "remark": ["Number of repayment periods of the loan"], "description": []}, {"column_name": "DealNumber", "data_type": "<PERSON><PERSON><PERSON>", "length": 20, "remark": ["Deal number of the transaction"], "description": []}, {"column_name": "TransactionTime", "data_type": "decimal", "length": 20, "remark": ["Transaction time"], "description": []}, {"column_name": "CurrencyCode", "data_type": "<PERSON><PERSON><PERSON>", "length": 3, "remark": ["Currency code of the loan transaction"], "description": []}, {"column_name": "TranAmt", "data_type": "decimal", "length": 18, "remark": ["Transaction amount"], "description": []}, {"column_name": "TranType", "data_type": "<PERSON><PERSON><PERSON>", "length": 3, "remark": ["Transaction type"], "description": ["Possible values:", "BRW--Borrowing", "PMT --Payment"]}, {"column_name": "CreationTime", "data_type": "decimal", "length": 20, "remark": ["Creation date of the record"], "description": []}, {"column_name": "LastUpdateTime", "data_type": "decimal", "length": 20, "remark": ["The last update date of the record"], "description": []}]}, "cht": {"title": "个人消费交易记录数据", "data_table_description": ["该数据表包含了个人贷款交易记录。例如银行放款记录，客户还款记录。", "可能用到的业务场景：查询贷款账户的交易记录。"], "data_table_type": "SimBank数据字典 - 个人消费交易记录数据", "headers": ["数据表描述", "列名", "数据类型", "长度", "描述", "备注"], "fields": [{"column_name": "ID", "data_type": "int", "length": 20, "remark": ["自增序号"], "description": []}, {"column_name": "CountryCode", "data_type": "<PERSON><PERSON><PERSON>", "length": 2, "remark": ["国家代码，如HK"], "description": []}, {"column_name": "ClearingCode", "data_type": "<PERSON><PERSON><PERSON>", "length": 20, "remark": ["银行代码"], "description": []}, {"column_name": "BranchCode", "data_type": "<PERSON><PERSON><PERSON>", "length": 20, "remark": ["分行代码"], "description": []}, {"column_name": "SandBoxId", "data_type": "<PERSON><PERSON><PERSON>", "length": 50, "remark": ["沙盒编号"], "description": []}, {"column_name": "AccountNumber", "data_type": "<PERSON><PERSON><PERSON>", "length": 34, "remark": ["贷款账号"], "description": []}, {"column_name": "ContractNumber", "data_type": "<PERSON><PERSON><PERSON>", "length": 50, "remark": ["贷款合同号"], "description": []}, {"column_name": "DebitAccountNumber", "data_type": "<PERSON><PERSON><PERSON>", "length": 34, "remark": ["与贷款账户关联的借记账户，用于偿还贷款。它可以是活期账户号码或储蓄账户号码"], "description": []}, {"column_name": "PhaseNo", "data_type": "<PERSON><PERSON><PERSON>", "length": 140, "remark": ["还款期数"], "description": []}, {"column_name": "DealNumber", "data_type": "<PERSON><PERSON><PERSON>", "length": 20, "remark": ["交易号"], "description": []}, {"column_name": "TransactionTime", "data_type": "decimal", "length": 20, "remark": ["交易时间"], "description": []}, {"column_name": "CurrencyCode", "data_type": "<PERSON><PERSON><PERSON>", "length": 3, "remark": ["货币类型"], "description": []}, {"column_name": "TranAmt", "data_type": "decimal", "length": 18, "remark": ["交易金额"], "description": []}, {"column_name": "TranType", "data_type": "<PERSON><PERSON><PERSON>", "length": 3, "remark": ["交易类型"], "description": ["交易类型", "BRW--贷款", "PMT --还款"]}, {"column_name": "CreationTime", "data_type": "decimal", "length": 20, "remark": ["最后一次更新时间"], "description": []}, {"column_name": "LastUpdateTime", "data_type": "decimal", "length": 20, "remark": ["创建时间"], "description": []}]}}