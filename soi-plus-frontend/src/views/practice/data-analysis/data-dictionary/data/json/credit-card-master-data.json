{"zh": {"title": "信用卡账户数据", "data_table_description": ["信用卡账户表是用于系统化记录和管理信用卡核心属性及动态状态的结构化数据表，包含发卡信息、有效期、额度管理、账户状态等关键字段，以支持交易处理、风险监控及客户服务等功能。"], "data_table_type": "SimBank数据字典 - 信用卡账户数据", "headers": ["数据表描述", "列名", "数据类型", "长度", "描述", "备注"], "fields": [{"column_name": "ID", "data_type": "int", "length": 11, "remark": ["主键自增"], "description": []}, {"column_name": "CountryCode", "data_type": "<PERSON><PERSON><PERSON>", "length": 2, "remark": ["国家代码"], "description": []}, {"column_name": "ClearingCode", "data_type": "<PERSON><PERSON><PERSON>", "length": 20, "remark": ["银行代码"], "description": []}, {"column_name": "BranchCode", "data_type": "<PERSON><PERSON><PERSON>", "length": 20, "remark": ["分行代码"], "description": []}, {"column_name": "SandBoxId", "data_type": "<PERSON><PERSON><PERSON>", "length": 50, "remark": ["沙箱ID"], "description": []}, {"column_name": "CreditCardNumber", "data_type": "<PERSON><PERSON><PERSON>", "length": 34, "remark": ["信用卡卡号"], "description": []}, {"column_name": "<PERSON><PERSON><PERSON><PERSON>", "data_type": "<PERSON><PERSON><PERSON>", "length": 140, "remark": ["持有人"], "description": []}, {"column_name": "CreditCardType", "data_type": "<PERSON><PERSON><PERSON>", "length": 1, "remark": ["信用卡类型"], "description": ["可能的值： \nV-Visa"]}, {"column_name": "IssuanceDate", "data_type": "decimal", "length": 20, "remark": ["信用卡发卡日期"], "description": []}, {"column_name": "ExpiryDate", "data_type": "decimal", "length": 20, "remark": ["信用卡到期日期"], "description": []}, {"column_name": "VerificationCode", "data_type": "<PERSON><PERSON><PERSON>", "length": 3, "remark": ["校验码"], "description": []}, {"column_name": "CreditCardStatus", "data_type": "<PERSON><PERSON><PERSON>", "length": 20, "remark": ["信用卡状态"], "description": ["可能的值： \nP-Pending Activation(待激活）\nA-Active;（激活）\nC-cancel;  （注销）\nL-loss;         （挂失）\nH-Hold;        （临时冻结）\nS-Supervisor Approval （主管批准）"]}, {"column_name": "ReportLossDate", "data_type": "decimal", "length": 20, "remark": ["信用卡挂失日期"], "description": []}, {"column_name": "ReportCancelDate", "data_type": "decimal", "length": 20, "remark": ["信用卡注销日期"], "description": []}, {"column_name": "CreditCardLimitApprovedDate", "data_type": "decimal", "length": 20, "remark": ["信用卡额度批准日期"], "description": []}, {"column_name": "NextLimitReviewDate", "data_type": "decimal", "length": 20, "remark": ["信用卡下一次额度审核日期"], "description": []}, {"column_name": "LimitApprovalStatus", "data_type": "<PERSON><PERSON><PERSON>", "length": 4, "remark": ["信用卡额度审批状态"], "description": ["可能的值： \nP-Pending Approval（待审批） \nA-Approved; （审批通过）\nR-Rejected （审批拒绝）"]}, {"column_name": "ApprovedLimit", "data_type": "decimal", "length": 18, "remark": ["信用卡授权额度"], "description": []}, {"column_name": "CashAdvanceLimit", "data_type": "decimal", "length": 18, "remark": ["信用卡提现额度"], "description": []}, {"column_name": "UsedLimit", "data_type": "decimal", "length": 18, "remark": ["信用卡已用额度"], "description": []}, {"column_name": "AvailableLimit", "data_type": "decimal", "length": 18, "remark": ["信用卡可用额度"], "description": []}, {"column_name": "AssociatedCusNum", "data_type": "<PERSON><PERSON><PERSON>", "length": 34, "remark": ["信用卡关联客户编号"], "description": []}, {"column_name": "RepaymentCycle", "data_type": "<PERSON><PERSON><PERSON>", "length": 5, "remark": ["信用卡还款周期"], "description": []}, {"column_name": "PaymentPassword", "data_type": "<PERSON><PERSON><PERSON>", "length": 140, "remark": ["信用卡支付密码"], "description": []}, {"column_name": "RewardPoint", "data_type": "decimal", "length": 18, "remark": ["信用卡累计有效积分"], "description": []}, {"column_name": "CreateDate", "data_type": "decimal", "length": 20, "remark": ["信用卡开户时间"], "description": []}, {"column_name": "Remarks", "data_type": "<PERSON><PERSON><PERSON>", "length": 255, "remark": ["备注"], "description": []}]}, "en": {"title": "Credit Card Master Data", "data_table_description": ["Credit card master table is a structured data table designed to systematically record and manage core attributes and dynamic statuses of credit cards. It includes key fields such as issuance details, validity periods, credit limit management, and account statuses to support transaction processing, risk monitoring, and customer service operations."], "data_table_type": "SimBank Data Dictionary - Credit Card Master Data", "headers": ["Data table description", "Column Name", "Data Type", "Length", "Description", "Remark"], "fields": [{"column_name": "ID", "data_type": "int", "length": 11, "remark": ["Auto Incrementing"], "description": []}, {"column_name": "CountryCode", "data_type": "<PERSON><PERSON><PERSON>", "length": 2, "remark": ["A country code is a short, standardized sequence of letters or numbers used to represent a specific country or geographic region. e.g., HK for Hong Kong"], "description": []}, {"column_name": "ClearingCode", "data_type": "<PERSON><PERSON><PERSON>", "length": 20, "remark": ["Clearing code is code for different bank. for example BBIC,HSBC eg."], "description": []}, {"column_name": "BranchCode", "data_type": "<PERSON><PERSON><PERSON>", "length": 20, "remark": ["Branch code is code for different branch of the same bank"], "description": []}, {"column_name": "SandBoxId", "data_type": "<PERSON><PERSON><PERSON>", "length": 50, "remark": ["Used for sandbox allocation for each user"], "description": []}, {"column_name": "CreditCardNumber", "data_type": "<PERSON><PERSON><PERSON>", "length": 34, "remark": ["Credit card number"], "description": []}, {"column_name": "<PERSON><PERSON><PERSON><PERSON>", "data_type": "<PERSON><PERSON><PERSON>", "length": 140, "remark": ["Holder name of a credit card"], "description": []}, {"column_name": "CreditCardType", "data_type": "<PERSON><PERSON><PERSON>", "length": 1, "remark": ["Credit card type. "], "description": ["Possible Values:\nV-Visa"]}, {"column_name": "IssuanceDate", "data_type": "decimal", "length": 20, "remark": ["Credit Card Issuance Date refers to the specific date on which a credit card is formally approved and disbursed to the borrower."], "description": []}, {"column_name": "ExpiryDate", "data_type": "decimal", "length": 20, "remark": ["Credit Expiry Date refers to the specific date on which a credit card ceases to be valid or available for use."], "description": []}, {"column_name": "VerificationCode", "data_type": "<PERSON><PERSON><PERSON>", "length": 3, "remark": ["Credit Card Verification Code refers to the security feature ( 3-digit number in this case) on a credit card used to authenticate transactions, usually found on the back (or front for Amex) of the card."], "description": []}, {"column_name": "CreditCardStatus", "data_type": "<PERSON><PERSON><PERSON>", "length": 20, "remark": ["Credit Card Status."], "description": ["Possible Values:\nP-Pending Activation\nA-Active;\nC-cancel;\nL-loss;\nH-Hold;\nS-Supervisor Approval"]}, {"column_name": "ReportLossDate", "data_type": "decimal", "length": 20, "remark": ["Report Loss Date refers to the specific date on which a cardholder officially notifies their financial institution (e.g., bank or credit card issuer) that their card is lost, stolen, or compromised, initiating fraud protection measures and liability limitations."], "description": []}, {"column_name": "ReportCancelDate", "data_type": "decimal", "length": 20, "remark": ["Credit Card Report Cancel Date refers to the specific date on which a cardholder formally reports the cancellation of their credit card to the issuer, after which the card becomes inactive and the account closure process is initiated."], "description": []}, {"column_name": "CreditCardLimitApprovedDate", "data_type": "decimal", "length": 20, "remark": ["Credit Card Limit Approved Date refers to the specific date on which a credit card issuer formally approves and sets the maximum spending amount (credit limit) for a cardholder, enabling the use of the card within the authorized limit."], "description": []}, {"column_name": "NextLimitReviewDate", "data_type": "decimal", "length": 20, "remark": ["Credit Card Next Limit Review Date refers to the specific date on which the card issuer plans to reassess the cardholder’s creditworthiness and potentially adjust their approved credit limit."], "description": []}, {"column_name": "LimitApprovalStatus", "data_type": "<PERSON><PERSON><PERSON>", "length": 4, "remark": ["Credit Card Limit Approval Status refers to the current state (e.g., approved, pending, rejected) of a cardholder’s credit limit request or adjustment, as determined by the issuer based on eligibility and risk assessment."], "description": ["Possible Values:\nP-Pending Approval; \nA-Approved;\nR-Rejected"]}, {"column_name": "ApprovedLimit", "data_type": "decimal", "length": 18, "remark": ["Credit Card Approved Limit refers to the maximum amount of credit authorized by the issuer for a cardholder to spend, as determined by factors such as creditworthiness, income, and risk assessment."], "description": []}, {"column_name": "CashAdvanceLimit", "data_type": "decimal", "length": 18, "remark": ["Credit Card Cash Advance Limit refers to the maximum amount a cardholder can withdraw as cash (e.g., via ATMs or bank transactions) using their credit card, typically set lower than the total approved credit limit and subject to fees or higher interest rates."], "description": []}, {"column_name": "UsedLimit", "data_type": "decimal", "length": 18, "remark": ["Credit Card Used Limit refers to the portion of the approved credit limit that a cardholder has already spent or utilized."], "description": []}, {"column_name": "AvailableLimit", "data_type": "decimal", "length": 18, "remark": ["Credit Card Available Limit refers to the remaining amount of credit accessible to the cardholder for spending or cash advances."], "description": []}, {"column_name": "AssociatedCusNum", "data_type": "<PERSON><PERSON><PERSON>", "length": 34, "remark": ["Branch code + runing squence that  was defined in system configeration table. Each bank customer has a unique customernumber"], "description": []}, {"column_name": "RepaymentCycle", "data_type": "<PERSON><PERSON><PERSON>", "length": 5, "remark": ["Credit Card Repayment Cycle refers to the recurring period (e.g., monthly) during which a cardholder must repay outstanding balances or minimum payments on their credit card account."], "description": []}, {"column_name": "PaymentPassword", "data_type": "<PERSON><PERSON><PERSON>", "length": 140, "remark": ["Credit Card Payment Password refers to a security credential (e.g., a numeric PIN, biometric authentication, or one-time code) set by the cardholder to authorize transactions, ensuring secure access to the card account and preventing unauthorized use."], "description": []}, {"column_name": "RewardPoint", "data_type": "decimal", "length": 18, "remark": ["Credit Card Reward Point refers to the units earned by a cardholder through eligible transactions or promotions, which can be redeemed for benefits (e.g., cashback, travel miles, gift cards) under the terms of the issuer’s rewards program."], "description": []}, {"column_name": "CreateDate", "data_type": "decimal", "length": 20, "remark": ["Credit Card Create Date refers to the date on which the credit card account was formally established or recorded in the issuer’s system"], "description": []}, {"column_name": "Remarks", "data_type": "<PERSON><PERSON><PERSON>", "length": 255, "remark": ["Remarks info."], "description": []}]}, "cht": {"title": "信用卡帳戶數據", "data_table_description": ["信用卡帳戶表是用於系統化記錄和管理信用卡核心屬性及動態狀態的結構化資料表，包含發卡資訊、有效期、額度管理、帳戶狀態等關鍵字段，以支援交易處理、風險監控及客戶服務等功能。"], "data_table_type": "SimBank数据字典 - 信用卡帳戶數據", "headers": ["数据表描述", "列名", "数据类型", "长度", "描述", "备注"], "fields": [{"column_name": "ID", "data_type": "int", "length": 11, "remark": ["主鍵自增"], "description": []}, {"column_name": "CountryCode", "data_type": "<PERSON><PERSON><PERSON>", "length": 2, "remark": ["國家代碼"], "description": []}, {"column_name": "ClearingCode", "data_type": "<PERSON><PERSON><PERSON>", "length": 20, "remark": ["銀行代碼"], "description": []}, {"column_name": "BranchCode", "data_type": "<PERSON><PERSON><PERSON>", "length": 20, "remark": ["分行代碼"], "description": []}, {"column_name": "SandBoxId", "data_type": "<PERSON><PERSON><PERSON>", "length": 50, "remark": ["沙箱ID"], "description": []}, {"column_name": "CreditCardNumber", "data_type": "<PERSON><PERSON><PERSON>", "length": 34, "remark": ["信用卡卡號"], "description": []}, {"column_name": "<PERSON><PERSON><PERSON><PERSON>", "data_type": "<PERSON><PERSON><PERSON>", "length": 140, "remark": ["持有人"], "description": []}, {"column_name": "CreditCardType", "data_type": "<PERSON><PERSON><PERSON>", "length": 1, "remark": ["信用卡型別"], "description": ["可能的值： \nV-Visa"]}, {"column_name": "IssuanceDate", "data_type": "decimal", "length": 20, "remark": ["信用卡發卡日期"], "description": []}, {"column_name": "ExpiryDate", "data_type": "decimal", "length": 20, "remark": ["信用卡到期日"], "description": []}, {"column_name": "VerificationCode", "data_type": "<PERSON><PERSON><PERSON>", "length": 3, "remark": ["校驗碼"], "description": []}, {"column_name": "CreditCardStatus", "data_type": "<PERSON><PERSON><PERSON>", "length": 20, "remark": ["信用卡狀態"], "description": ["可能的值： \nP-Pending Activation(待啟動）\nA-Active-lossnA-Active-（激活）\nA-cm （掛失）\nH-Hold; （暫時凍結）\nS-Supervisor Approval （主管核准）"]}, {"column_name": "ReportLossDate", "data_type": "decimal", "length": 20, "remark": ["信用卡掛失日期"], "description": []}, {"column_name": "ReportCancelDate", "data_type": "decimal", "length": 20, "remark": ["信用卡登出日期"], "description": []}, {"column_name": "CreditCardLimitApprovedDate", "data_type": "decimal", "length": 20, "remark": ["信用卡額度核准日期"], "description": []}, {"column_name": "NextLimitReviewDate", "data_type": "decimal", "length": 20, "remark": ["信用卡下次額度審核日期"], "description": []}, {"column_name": "LimitApprovalStatus", "data_type": "<PERSON><PERSON><PERSON>", "length": 4, "remark": ["信用卡額度審批狀態"], "description": ["可能的值： \nP-Pending Approval（待審） \nReject; （審核拒絕）"]}, {"column_name": "ApprovedLimit", "data_type": "decimal", "length": 18, "remark": ["信用卡授權額度"], "description": []}, {"column_name": "CashAdvanceLimit", "data_type": "decimal", "length": 18, "remark": ["信用卡提領額度"], "description": []}, {"column_name": "UsedLimit", "data_type": "decimal", "length": 18, "remark": ["信用卡已用額度"], "description": []}, {"column_name": "AvailableLimit", "data_type": "decimal", "length": 18, "remark": ["信用卡可用額度"], "description": []}, {"column_name": "AssociatedCusNum", "data_type": "<PERSON><PERSON><PERSON>", "length": 34, "remark": ["信用卡關聯客戶編號"], "description": []}, {"column_name": "RepaymentCycle", "data_type": "<PERSON><PERSON><PERSON>", "length": 5, "remark": ["信用卡還款週期"], "description": []}, {"column_name": "PaymentPassword", "data_type": "<PERSON><PERSON><PERSON>", "length": 140, "remark": ["信用卡付款密碼"], "description": []}, {"column_name": "RewardPoint", "data_type": "decimal", "length": 18, "remark": ["信用卡累積有效積分"], "description": []}, {"column_name": "CreateDate", "data_type": "decimal", "length": 20, "remark": ["信用卡開戶時間"], "description": []}, {"column_name": "Remarks", "data_type": "<PERSON><PERSON><PERSON>", "length": 255, "remark": ["備註"], "description": []}]}}