{"zh": {"title": "股票持仓数据", "data_table_description": ["该数据表包含了股票账户的所有参数信息，例如账户号码，开户行，账户状态等。", "可能用到的业务场景：查询银行客户个人账户信息，通过账户号码查询个人信息。"], "data_table_type": "SimBank数据字典 - 股票持仓数据", "headers": ["数据表描述", "列名", "数据类型", "长度", "描述", "备注"], "fields": [{"column_name": "ID", "data_type": "int", "length": 20, "remark": ["自增序号"], "description": []}, {"column_name": "AccountNumber", "data_type": "<PERSON><PERSON><PERSON>", "length": 34, "remark": ["股票账户"], "description": []}, {"column_name": "StockCode", "data_type": "<PERSON><PERSON><PERSON>", "length": 10, "remark": ["股票代码"], "description": []}, {"column_name": "SharesHoldingNo", "data_type": "decimal", "length": 18, "remark": ["总持有股份"], "description": []}, {"column_name": "AvailableShare", "data_type": "decimal", "length": 18, "remark": ["可交易份额"], "description": []}, {"column_name": "AveragePrice", "data_type": "decimal", "length": 18, "remark": ["平均价格"], "description": []}, {"column_name": "<PERSON><PERSON><PERSON><PERSON>", "data_type": "<PERSON><PERSON><PERSON>", "length": 3, "remark": ["货币类型"], "description": []}, {"column_name": "LastUpdateDate", "data_type": "decimal", "length": 20, "remark": ["最近更新时间"], "description": []}]}, "en": {"title": "Stock Holdings Data", "data_table_description": ["This data table contains all parameter information of the stock account, such as account number, opening bank, account status, etc.", "Possible business scenarios: Querying personal account information of bank customers and checking demographic information through account numbers."], "data_table_type": "SimBank Data Dictionary - Stock Holdings Data", "headers": ["Data table description", "Column Name", "Data Type", "Length", "Description", "Remark"], "fields": [{"column_name": "ID", "data_type": "int", "length": 20, "remark": ["Auto Incrementing"], "description": []}, {"column_name": "AccountNumber", "data_type": "<PERSON><PERSON><PERSON>", "length": 34, "remark": ["the stocktrading account number"], "description": []}, {"column_name": "StockCode", "data_type": "<PERSON><PERSON><PERSON>", "length": 10, "remark": ["Stock code"], "description": []}, {"column_name": "SharesHoldingNo", "data_type": "decimal", "length": 18, "remark": ["Holding shares of a  stock"], "description": []}, {"column_name": "AvailableShare", "data_type": "decimal", "length": 18, "remark": ["Available shares of a stock"], "description": []}, {"column_name": "AveragePrice", "data_type": "decimal", "length": 18, "remark": ["The average price of a holding stock"], "description": []}, {"column_name": "<PERSON><PERSON><PERSON><PERSON>", "data_type": "<PERSON><PERSON><PERSON>", "length": 3, "remark": ["Currency code of the holding stocks"], "description": []}, {"column_name": "LastUpdateDate", "data_type": "decimal", "length": 20, "remark": ["Latet update of the record"], "description": []}]}, "cht": {"title": "股票持仓数据", "data_table_description": ["该数据表包含了股票账户的所有参数信息，例如账户号码，开户行，账户状态等。", "可能用到的业务场景：查询银行客户个人账户信息，通过账户号码查询个人信息。"], "data_table_type": "SimBank数据字典 - 股票持仓数据", "headers": ["数据表描述", "列名", "数据类型", "长度", "描述", "备注"], "fields": [{"column_name": "ID", "data_type": "int", "length": 20, "remark": ["自增序号"], "description": []}, {"column_name": "AccountNumber", "data_type": "<PERSON><PERSON><PERSON>", "length": 34, "remark": ["股票账户"], "description": []}, {"column_name": "StockCode", "data_type": "<PERSON><PERSON><PERSON>", "length": 10, "remark": ["股票代码"], "description": []}, {"column_name": "SharesHoldingNo", "data_type": "decimal", "length": 18, "remark": ["总持有股份"], "description": []}, {"column_name": "AvailableShare", "data_type": "decimal", "length": 18, "remark": ["可交易份额"], "description": []}, {"column_name": "AveragePrice", "data_type": "decimal", "length": 18, "remark": ["平均价格"], "description": []}, {"column_name": "<PERSON><PERSON><PERSON><PERSON>", "data_type": "<PERSON><PERSON><PERSON>", "length": 3, "remark": ["货币类型"], "description": []}, {"column_name": "LastUpdateDate", "data_type": "decimal", "length": 20, "remark": ["最近更新时间"], "description": []}]}}