<template>
  <div class="data-dictionary-subcategory-container">
    <customize-card :title="`${$t('soi.router.dataDictionarySubcategory')} - ${$t(categoryName)}`">
      <el-row :gutter="40">
        <el-col v-for="(item, index) in dataDictionary" :key="index" :span="8" :xs="24">

          <el-card shadow="hover" class="card-list">
            <el-row type="flex" align="middle">
              <el-col :span="6" class="card-img">
                <img :src="dataImage" alt="image">
              </el-col>
              <el-col :span="16" :offset="2">
                <h2>{{ item.tables[0][language].title }}</h2>
                <el-tooltip effect="light" placement="top-start">
                  <div slot="content">
                    <div style="width: 400px" v-text="item.tables[0][language].data_table_description.join('').replaceAll('<b>', '').replaceAll('</b>', '')" />
                  </div>
                  <p v-text="item.tables[0][language].data_table_description.join('').replaceAll('<b>', '').replaceAll('</b>', '')" />
                </el-tooltip>
                <el-button type="primary" size="small" @click="viewDataDictionary(index)">{{ $t("soi.common.view") }}</el-button>
              </el-col>
            </el-row>
          </el-card>

        </el-col>
      </el-row>
    </customize-card>
  </div>
</template>

<script>
import CustomizeCard from '@/components/CustomizeCard/index.vue'

import { mapGetters } from 'vuex'

import { findCategoryNameById, findSubcategoryById } from '@/views/practice/data-analysis/data-dictionary/data'

export default {
  name: 'DataDictionarySubcategory',
  components: { CustomizeCard },
  data() {
    return {
      dataImage: require(`@/assets/images/data.png`),
      id: this.$route.params.id
    }
  },
  computed: {
    ...mapGetters([
      'language'
    ]),
    dataDictionary() {
      return findSubcategoryById(this.id)
    },
    categoryName() {
      return findCategoryNameById(this.id)
    }
  },
  methods: {
    viewDataDictionary(index) {
      this.$router.push({ name: 'data-analysis-data-dictionary-details', params: { id: this.id, index }})
    }
  }
}
</script>

<style lang="scss" scoped>
.data-dictionary-subcategory-container {
  .card-list {
    position: relative;
    height: 205px;
    margin-bottom: 20px;
    border: 1px solid #ccc !important;
    box-shadow: 5px 5px 5px #ccc;
    .card-img {
      text-align: center;
      img {
        width: 100%;
        height: auto;
        vertical-align: middle;
      }
    }
    h2 {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      line-height: 1.1;
    }
    p {
      height: 34px;
      font-size: 14px;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      line-clamp: 2;
      -webkit-box-orient: vertical;
    }
  }
}
</style>
