<template>
  <div class="data-dictionary-details-container">
    <customize-card :title="`${$t('soi.router.dataDictionaryDetails')} - ${$t(categoryName)}`" :body-style="{ padding: '10px' }">
      <div v-if="item.length > 1">
        <el-button v-for="(table, index) in item" :key="index" :type="index === tablesIndex ? 'primary' : ''" class="table-btn" @click="switchTable(index)">{{ table[language].title }}</el-button>
      </div>
      <table>
        <tr class="header-title">
          <td width="330" align="center" valign="middle"><b>{{ item[tablesIndex][language].headers[0] }}</b></td>
          <td colspan="5" valign="middle">
            <span
              v-for="(i, index) in item[tablesIndex][language].data_table_description"
              :key="index"
              class="description"
              v-html="i"
            />
          </td>
        </tr>
        <tr class="header-type">
          <td colspan="6" align="center" valign="middle">{{ item[tablesIndex][language].data_table_type }}</td>
        </tr>
        <tr class="headers fields">
          <td width="20" align="center" valign="middle">#</td>
          <td width="166" align="center" valign="middle">{{ item[tablesIndex][language].headers[1] }}</td>
          <td width="165" align="center" valign="middle">{{ item[tablesIndex][language].headers[2] }}</td>
          <td width="495" align="center" valign="middle">{{ item[tablesIndex][language].headers[3] }}</td>
          <td width="495" align="center" valign="middle">{{ item[tablesIndex][language].headers[4] }}</td>
          <td width="495" align="center" valign="middle">{{ item[tablesIndex][language].headers[5] }}</td>
        </tr>
        <tr v-for="(field, j) in item[tablesIndex][language].fields" :key="j" class="fields">
          <td width="50" align="center" valign="middle">{{ j + 1 }}</td>
          <td width="330" align="center" valign="middle">{{ field.column_name }}</td>
          <td width="166" align="center" valign="middle">{{ field.data_type }}</td>
          <td width="165" align="center" valign="middle">{{ field.length }}</td>
          <td width="495" valign="middle">
            <span
              v-for="(r, k) in field.remark"
              :key="k"
              class="field-remark"
            >{{ r }}</span>
          </td>
          <td width="495" valign="middle">
            <span
              v-for="(d, l) in field.description"
              :key="l"
              class="field-description"
            >{{ d }}</span>
          </td>
        </tr>
      </table>
    </customize-card>
  </div>
</template>

<script>
import CustomizeCard from '@/components/CustomizeCard/index.vue'
import { findCategoryNameById, findDataDictionaryDetails } from '@/views/practice/data-analysis/data-dictionary/data'

export default {
  components: { CustomizeCard },
  data() {
    return {
      tablesIndex: 0,
      id: this.$route.params.id
    }
  },
  computed: {
    language() {
      return this.$store.state.app.language
    },
    item() {
      const id = this.$route.params.id
      const index = this.$route.params.index
      return findDataDictionaryDetails(id, index)
    },
    categoryName() {
      return findCategoryNameById(this.id)
    }
  },
  methods: {
    back() {
      this.$emit('showPage')
    },
    switchTable(index) {
      this.tablesIndex = index
    }
  }
}
</script>

<style lang="scss" scoped>
.data-dictionary-details-container {

  .header-title {
    background: #b7dee8;
  }

  table {
    tr:not(.header-type) {
      td {
        font-size: 11pt;
        font-family: Segoe UI,SegoeUI,Helvetica Neue,Helvetica,Arial,sans-serif;
        padding: 10px 0;
      }
    }
  }

  .header-type {
    text-align: center;
    background: #a6a6a6;
    color: #fff;
    font-size: 16pt !important;
    font-weight: 700;
    line-height: 1.5;
    font-family: Segoe UI,SegoeUI,Helvetica Neue,Helvetica,Arial,sans-serif;
  }

  .description {
    line-height: 1.5;
  }

  .field-remark, .field-description {
    line-height: 1.5;
    display: block;
  }

  .header-title {
    .description {
      display: block;
    }
  }

  .headers {
    td {
      font-weight: 700;
    }
  }

  .fields {
    td {
      background: #dce6f1;
      border-bottom: .5pt dashed windowtext;
    }
  }

  .table-btn {
    margin-bottom: 10px;
  }
}
</style>
