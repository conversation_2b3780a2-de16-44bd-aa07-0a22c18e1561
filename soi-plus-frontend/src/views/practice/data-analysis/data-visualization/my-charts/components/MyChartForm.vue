<template>
  <div
    v-loading.fullscreen.lock="loading"
    :element-loading-text="$t('soi.dataVisualization.loadingText')"
    class="data-visualization-my-chart-form-container charts"
  >
    <customize-card :title="$t('soi.dataVisualization.charts')">
      <el-tabs v-model="activeTag" type="card" @tab-click="handleClick">
        <el-tab-pane label="Charts" name="first">
          <el-row>
            <div class="itxst">
              <el-col :span="4" class="div-left">
                <div class="text-title">
                  <el-cascader
                    v-model="value"
                    :placeholder="$t('soi.dataVisualization.selectModel')"
                    :options="list"
                    :props="{ expandTrigger: 'hover' }"
                    style="width: 100%;"
                    @change="handleChange"
                  />
                  <el-input
                    v-model="key"
                    :placeholder="$t('soi.dataVisualization.searchField')"
                    style="margin-top: 15px"
                    clearable
                  >
                    <i slot="prefix" class="el-input__icon el-icon-search" />
                  </el-input>
                </div>
                <div style="height: 800px;overflow-y: auto">
                  <draggable
                    v-model="fieldInfoList"
                    :group="{name: 'field',pull: 'clone',put: false}"
                    v-bind="{sort: false}"
                    :move="onMove"
                    @end="End"
                  >
                    <div v-for="(item,index) in fieldInfoList" :key="index">
                      <el-tag v-if="item.type === 'DATABASE'" type="danger" style="margin-bottom: 4px;cursor: pointer">
                        {{ item.databaseName }}
                      </el-tag>
                      <el-tag
                        v-if="item.type === 'TABLE'"
                        type="warning"
                        style="margin-bottom: 4px;cursor: pointer;margin-left: 20px"
                      >
                        {{ item.tableName }}
                      </el-tag>
                      <div v-if="item.type !== 'TABLE' && item.type !== 'DATABASE'">
                        <!-- 根据搜索的关键字显示与隐藏 -->
                        <el-tag
                          v-if="!key || key.length === 0 || item.columnName.toUpperCase().indexOf(key.toUpperCase()) !== -1"
                          style="margin-bottom: 4px;cursor: pointer;margin-left: 40px"
                        >
                          <svg-icon v-if="MYSQLStringTypeList.indexOf(item.type) !== -1" icon-class="string" />
                          <svg-icon v-if="MYSQLNumberTypeList.indexOf(item.type) !== -1" icon-class="number" />
                          <svg-icon v-if="MYSQLDateTypeList.indexOf(item.type) !== -1" icon-class="date" />
                          {{ item.columnName }}
                        </el-tag>
                      </div>
                    </div>
                  </draggable>
                </div>
              </el-col>
              <el-col :span="16" class="div-bottom">
                <div style="width: 100%;border-bottom: 1px #ccc solid">
                  <div class="scrollbar" style="overflow-x: auto">
                    <div style="width: max-content;min-width: 100%">
                      <draggable
                        v-model="dimensions"
                        v-bind="{sort: false}"
                        :group="{name: 'field',pull: true,put: true}"
                        style="display: block;width: 100%"
                        @end="End"
                        @add="addDimensions"
                      >
                        <transition-group style="display: block;min-height: 66px;width: 100%;padding: 17px 0 0 10px">
                          <el-tag
                            v-for="(item,index) in dimensions"
                            :key="index + '_dimensions'"
                            :class="item.id === -1 ? 'tag tag-tip' : 'tags'"
                            :style="'text-align: center;color: ' + (item.id === -1 ? '#aaaaaa' : '')"
                            :closable="!(item.id === -1)"
                            @close="cancelSelectTag('dimensions',index)"
                          >
                            {{ item.id === -1 ? $t('soi.dataVisualization.dimensionsTip') : `${item.columnName}(${item.tableName})` }}
                          </el-tag>
                        </transition-group>
                      </draggable>
                    </div>
                  </div>
                </div>
                <div>
                  <div class="scrollbar" style="overflow-x: auto;">
                    <div style="width: max-content;min-width: 100%">
                      <draggable
                        v-model="measures"
                        v-bind="{sort: false}"
                        :group="{name: 'field',pull: true,put: true}"
                        style="display: inline-block;width: 100%"
                        @end="End"
                        @add="addMeasures"
                      >
                        <transition-group style="display: block;min-height: 66px;width: 100%;padding: 17px 0 0 10px">
                          <el-tag
                            v-for="(item,index) in measures"
                            :key="index + '_measures'"
                            :class="item.id === -1 ? 'tag tag-tip' : 'tags'"
                            :style="'text-align: center;color: ' + (item.id === -1 ? '#aaaaaa' : '')"
                            :closable="!(item.id === -1)"
                            @close="cancelSelectTag('measures',index)"
                          >
                            {{ item.id === -1 ? $t('soi.dataVisualization.measuresTip') : `${item.columnName}(${item.tableName})` }}
                          </el-tag>
                        </transition-group>
                      </draggable>
                    </div>
                  </div>
                </div>
              </el-col>
            </div>
            <el-col :span="4" style="border-right: 1px solid #cccccc;height: 132px">
              <div style="border-top: 1px solid #cccccc;">
                <div class="text-center">
                  <el-button
                    type="primary"
                    size="small"
                    style="margin-top: 25px;margin-bottom: 10px"
                    @click="handlerCreateDataTable()"
                  >
                    {{ $t("soi.dataVisualization.createDataTable") }}
                  </el-button>
                  <p style="color: #707070;padding: 0 10px">{{ $t('soi.dataVisualization.createTip') }}</p>
                </div>
              </div>
            </el-col>
            <el-col :span="16" class="div-chart">
              <div v-show="showtable" style="padding: 10px">
                <el-table
                  :header-cell-style="{'background': '#109eae', 'color': '#333'}"
                  :data="chartData"
                  tooltip-effect="dark"
                  border
                  height="750"
                  style="width: 100%;height: 730px;overflow-y: auto"
                >
                  <el-table-column
                    v-for="(data, index) in chartParameters"
                    :key="index"
                    :prop="data.value"
                    :label="data.label"
                    sortable
                  />
                </el-table>
              </div>
              <div
                v-show="!showtable && chartOptions.length === 0"
                id="myChart"
                style="margin-top: 22px"
                class="myChart"
              />
              <div style="position: relative">
                <i
                  class="el-icon-download"
                  style="position: absolute; top: 5px; right: 15px; width: 15px; height: 15px; cursor: pointer;z-index: 10"
                  @click="downloadImage()"
                />
                <el-row
                  v-show="!showtable && chartOptions.length > 0"
                  id="charts"
                  style="margin-top: 22px"
                  class="myChart"
                >
                  <el-col :span="chartOptions.length > 2 ? 12 : 24">
                    <div id="chart1" :style="`height: ${chartsHeight}px`" />
                  </el-col>
                  <el-col :span="chartOptions.length > 2 ? 12 : 24">
                    <div id="chart2" :style="`height: ${chartsHeight}px`" />
                  </el-col>
                  <el-col v-show="chartOptions.length >= 3" :span="chartOptions.length > 2 ? 12 : 24">
                    <div id="chart3" :style="`height: ${chartsHeight}px`" />
                  </el-col>
                  <el-col v-show="chartOptions.length === 4" :span="chartOptions.length > 2 ? 12 : 24">
                    <div id="chart4" :style="`height: ${chartsHeight}px`" />
                  </el-col>
                </el-row>
              </div>
            </el-col>
            <el-col :span="4" class="div-right">
              <div class="right-div">
                <p style="text-align: center">{{ $t('soi.dataVisualization.color') }}</p>
                <div class="color-tag">
                  <draggable
                    v-model="arr2"
                    v-bind="{sort: false}"
                    :group="{name: 'field',pull: false,put: true}"
                    @add="AddColor"
                  >
                    <transition-group>
                      <el-tag
                        v-for="(item, index) in arr2"
                        :key="index + '_color'"
                        :style="'position: relative;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;width: 100%;text-align: center;color: ' + (item.id === -1 ? '#aaaaaa' : '')"
                        :closable="!(item.id === -1)"
                        @close="closeColor"
                      >
                        {{ item.id === -1 ? $t('soi.dataVisualization.colorTip') : `${item.columnName}(${item.tableName})` }}
                      </el-tag>
                    </transition-group>
                  </draggable>
                </div>
                <div style="margin:20px 0;">
                  <el-button plain type="primary" style="width: 100%" @click="showFilter()">
                    {{ $t('soi.dataVisualization.filter') }} <span v-show="filters.length > 0"> ({{ filters.length }})</span>
                  </el-button>
                  <el-dialog
                    :title="$t('soi.dataVisualization.filter')"
                    :visible.sync="filterDialogVisible"
                    :close-on-click-modal="false"
                    :close-on-press-escape="false"
                    :show-close="false"
                    width="50%"
                    center
                  >
                    <div style="height: 350px;overflow-y: auto">
                      <div v-for="(item,index) in columns" v-show="dataShow" :key="index" style="margin-bottom: 5px">
                        <div v-if="MYSQLNoDateTypeList.indexOf(item.type) !== -1" style="margin-left: 40px">
                          <el-tag type="info">{{ item.name }}</el-tag>
                          <div v-for="(filter,a) in item.filters" :key="a" style="display: inline-block">
                            <div v-if="filter.matter.length > 0" style="display: inline-block">
                              <el-tag
                                v-if="a === 0"
                                style="margin: 0 5px 5px 0"
                                closable
                                @close="cleanFilter(item,item.name,a)"
                              >
                                {{ item.name + ' ' + filter.condition + ' ' + filter.matter }}
                              </el-tag>
                              <el-tag
                                v-else
                                style="margin: 0 5px 5px 0"
                                closable
                                @close="cleanFilter(item,item.name,a)"
                              >
                                {{ filter.andOr + ' ' + item.name + ' ' + filter.condition + ' ' + filter.matter }}
                              </el-tag>
                            </div>
                            <div v-else style="display: inline-block">
                              <el-select
                                v-if="a > 0"
                                v-model="filterAndOr"
                                placeholder=""
                                size="mini"
                                style="width: 80px"
                              >
                                <el-option label="AND" value="AND" />
                                <el-option label="OR" value="OR" />
                              </el-select>
                              <el-select v-model="filterCondition" placeholder="" size="mini" style="width: 120px">
                                <el-option
                                  v-for="item1 in computeConditions"
                                  :key="item1.value"
                                  :label="item1.label"
                                  :value="item1.value"
                                />
                              </el-select>
                              <el-input
                                v-show="filterCondition !== 'IS NULL' && filterCondition !== 'IS NOT NULL'"
                                v-if="MYSQLNumberTypeList.indexOf(item.type) !== -1"
                                v-model="filterMatter"
                                oninput="
                                      value = value.replace(/[^\d.]/g, '');
                                      value = value.replace(/\.{2,}/g, '.');
                                      value = value.replace('.', '$#$').replace(/\./g, '').replace('$#$', '.');"
                                clearable
                                maxlength="50"
                                size="mini"
                                style="width: 170px"
                                placeholder=""
                              />
                              <el-select
                                v-show="filterCondition !== 'IS NULL' && filterCondition !== 'IS NOT NULL' && !showInput"
                                v-else
                                v-model="filterMatter"
                                filterable
                                clearable
                                size="mini"
                                style="width: 170px"
                                placeholder=""
                              >
                                <el-option
                                  v-for="item1 in filterGroups"
                                  :key="item1"
                                  :label="item1"
                                  :value="item1"
                                />
                              </el-select>
                              <el-input
                                v-show="filterCondition !== 'IS NULL' && filterCondition !== 'IS NOT NULL' && showInput"
                                v-model="filterMatter"
                                clearable
                                size="mini"
                                style="width: 170px"
                                placeholder=""
                              />
                              <i
                                v-show="!addOk"
                                v-if="item.type !== 'DATABASE' || item.type !== 'TABLE'"
                                style="cursor: pointer"
                                class="el-icon-circle-close"
                                @click="closeCondition(item, item.name)"
                              />
                              <i
                                v-show="!addOk"
                                v-if="item.type !== 'DATABASE' || item.type !== 'TABLE'"
                                style="cursor: pointer"
                                class="el-icon-circle-check"
                                @click="okCondition(item, item.name,item.type)"
                              />
                            </div>
                          </div>
                          <i
                            v-show="addOk"
                            v-if="item.type !== 'DATABASE' || item.type !== 'TABLE'"
                            style="cursor: pointer"
                            class="el-icon-circle-plus-outline"
                            @click="addCondition(item.databaseName,item.tableName,item.name)"
                          />
                        </div>
                        <div v-if="MYSQLDateTypeList.indexOf(item.type) !== -1" style="margin-left: 40px">
                          <el-tag type="info">{{ item.name }}</el-tag>
                          <div v-for="(filter,b) in item.filters" :key="b" style="display: inline-block">
                            <div v-if="filter.date.length > 0" style="display: inline-block">
                              <el-tag
                                v-if="b === 0"
                                style="margin: 0 5px 5px 0"
                                closable
                                @close="cleanFilter(item,item.name,b)"
                              >{{ filter.date }}
                              </el-tag>
                              <el-tag
                                v-else
                                style="margin: 0 5px 5px 0"
                                closable
                                @close="cleanFilter(item,item.name,b)"
                              >
                                {{ filter.andOr + ' ' + filter.date }}
                              </el-tag>
                            </div>
                            <div v-else style="display: inline-block">
                              <el-select
                                v-if="b > 0"
                                v-model="filterAndOr"
                                placeholder=""
                                size="mini"
                                style="width: 80px"
                              >
                                <el-option label="AND" value="AND" />
                                <el-option label="OR" value="OR" />
                              </el-select>
                              <el-date-picker
                                v-model="filterDate"
                                style="width: 260px"
                                size="mini"
                                type="daterange"
                                unlink-panels
                                range-separator="-"
                                start-placeholder="Start Date"
                                end-placeholder="End Date"
                              />
                              <i
                                v-show="!addOk"
                                v-if="item.type !== 'DATABASE' || item.type !== 'TABLE'"
                                style="cursor: pointer"
                                class="el-icon-circle-close"
                                @click="closeCondition(item,item.name)"
                              />
                              <i
                                v-show="!addOk"
                                v-if="item.type !== 'DATABASE' || item.type !== 'TABLE'"
                                style="cursor: pointer"
                                class="el-icon-circle-check"
                                @click="okCondition(item,item.name,item.type)"
                              />
                            </div>
                          </div>
                          <i
                            v-show="addOk"
                            v-if="item.type !== 'DATABASE' || item.type !== 'TABLE'"
                            style="cursor: pointer"
                            class="el-icon-circle-plus-outline"
                            @click="addCondition(item.databaseName,item.tableName,item.name)"
                          />
                        </div>
                        <div v-if="item.type === 'DATABASE'">
                          <el-tag type="danger">{{ item.databaseName }}</el-tag>
                        </div>
                        <div v-if="item.type === 'TABLE'" style="margin-left: 20px">
                          <el-tag type="warning">{{ item.tableName }}</el-tag>
                        </div>
                      </div>
                    </div>
                    <span slot="footer" class="dialog-footer">
                      <el-button @click="closeFilterDialogVisible()">{{ $t('soi.common.clear') }}</el-button>
                      <el-button type="primary" @click="okFilterDialogVisible()">{{ $t('soi.common.confirm') }}</el-button>
                    </span>
                  </el-dialog>
                </div>
                <div class="right-text"><p style="margin-bottom: 0">{{ $t('soi.dataVisualization.figure') }}</p></div>
                <el-row style="background-color: #F7F7F7;">
                  <el-col :span="6" style="text-align: center">
                    <div class="div-icon">
                      <div v-if="!tableClick">
                        <svg-icon icon-class="charts-table" class="icon-item" />
                      </div>
                      <div v-else @click="switchCharts('table')">
                        <svg-icon icon-class="charts-table-color" class="icon-item" />
                      </div>
                    </div>
                  </el-col>
                  <el-col :span="6" style="text-align: center">
                    <div class="div-icon">
                      <div v-if="!barClick">
                        <svg-icon icon-class="charts-bar" class="icon-item" />
                      </div>
                      <div v-else @click="switchCharts('bar')">
                        <svg-icon icon-class="charts-bar-color" class="icon-item" />
                      </div>
                    </div>
                  </el-col>
                  <el-col :span="6" style="text-align: center">
                    <div class="div-icon">
                      <div v-if="!lineClick">
                        <svg-icon icon-class="charts-line" class="icon-item" />
                      </div>
                      <div v-else @click="switchCharts('line')">
                        <svg-icon icon-class="charts-line-color" class="icon-item" />
                      </div>
                    </div>
                  </el-col>
                  <el-col :span="6" style="text-align: center">
                    <div class="div-icon">
                      <div v-if="!pieClick">
                        <svg-icon icon-class="charts-pie" class="icon-item" />
                      </div>
                      <div v-else @click="switchCharts('pie')">
                        <svg-icon icon-class="charts-pie-color" class="icon-item" />
                      </div>
                    </div>
                  </el-col>
                  <el-col :span="6" style="text-align: center">
                    <div class="div-icon">
                      <div v-if="!heatmapClick">
                        <svg-icon icon-class="heatmap" class="icon-item" />
                      </div>
                      <div v-else @click="switchCharts('heatmap')">
                        <svg-icon icon-class="heatmap-color" class="icon-item" />
                      </div>
                    </div>
                  </el-col>
                </el-row>
                <div v-if="firstEdit === 1 && createOrEdit === 'edit'" style="margin:20px 0;">
                  <el-button
                    v-if="createOrEdit === 'create'"
                    plain
                    type="primary"
                    style="width: 100%"
                    @click="saveChart()"
                  >
                    {{ $t('soi.dataVisualization.saveChart') }}
                  </el-button>
                  <el-button v-else plain type="primary" style="width: 100%" @click="editChart()">
                    {{ $t('soi.dataVisualization.updateChart') }}
                  </el-button>
                </div>
                <div v-else style="margin:20px 0;">
                  <el-button
                    v-if="createOrEdit === 'create'"
                    :disabled="saveButton"
                    plain
                    type="primary"
                    style="width: 100%"
                    @click="saveChart()"
                  >
                    {{ $t('soi.dataVisualization.saveChart') }}
                  </el-button>
                  <el-button v-else :disabled="saveButton" plain type="primary" style="width: 100%" @click="editChart()">
                    {{ $t('soi.dataVisualization.updateChart') }}
                  </el-button>
                </div>
                <div style="margin:20px 0;">
                  <el-tabs v-model="activeName" stretch>
                    <el-tab-pane :label="$t('soi.dataVisualization.aggregate')" name="Aggregate">
                      <el-radio-group v-model="resultFunction" size="mini">
                        <div v-for="(item,index) in resultOptions" :key="index" style="margin-bottom: 5px">
                          <el-radio
                            :key="item.value"
                            :disabled="(item.value === 'typeCount' && !((dimensions.length === 1 && dimensions[0].id !== -1 && measures[0].id === -1) || (measures.length === 1 && measures[0].id !== -1 && dimensions[0].id === -1))) ||
                              ((item.value === 'categoryCount') && !((dimensions.length === 1 && dimensions[0].id !== -1 && measures.length === 1) || (measures.length === 1 && measures[0].id !== -1 && dimensions.length === 1) && arr2[0].id === -1)) || (item.value === 'categoryCount' && arr2[0].id !== -1) || (item.value === 'typeCount' && arr2[0].id !== -1)"
                            :label="item.value"
                            :value="item.value"
                            border
                          >
                            {{ $t(`soi.dataVisualization.${item.label}`) }}
                          </el-radio>
                        </div>
                      </el-radio-group>
                    </el-tab-pane>
                    <el-tab-pane :label="$t('soi.dataVisualization.style')" name="Style">
                      <div id="chart-content" style="display: none">
                        <div class="charts-style" style="width: 100%;height: 355px;overflow-x: auto;overflow-y: auto">
                          <p class="charts-style-title">{{ $t('soi.dataVisualization.title') }}</p>
                          <el-input
                            v-model="chartsTitle"
                            :placeholder="$t('soi.dataVisualization.setChartsTitle')"
                            size="mini"
                            style="margin-bottom: 10px;width: 95%"
                            @change="changeChartsTitle()"
                          />
                          <div v-if="chartsTitle.length > 0">
                            <p class="charts-style-title">{{ $t('soi.dataVisualization.titlePosition') }}</p><br>
                            <p class="charts-style-title" style="width: 70px;color: #707070">{{ $t('soi.dataVisualization.top') }}
                              (%)</p>
                            <el-input
                              v-model="titleTop"
                              placeholder="0-100"
                              size="mini"
                              style="width: 100px"
                              @change="changeTitleTop()"
                            />
                            <br>
                            <p class="charts-style-title" style="width: 70px;color: #707070">
                              {{ $t('soi.dataVisualization.left') }}
                              (%)</p>
                            <el-input
                              v-model="titleLeft"
                              placeholder="0-100"
                              size="mini"
                              style="width: 100px"
                              @change="changeTitleLeft()"
                            />
                            <br>
                            <p class="charts-style-title">{{ $t('soi.dataVisualization.titleFont') }}</p><br>
                            <el-select
                              v-model="titleFont"
                              size="mini"
                              placeholder=""
                              style="width: 140px"
                              @change="changeTitleFont()"
                            >
                              <el-option
                                v-for="item in fontList"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                              />
                            </el-select>
                            <br>
                            <p class="charts-style-title" style="margin-top: 10px">
                              {{ $t('soi.dataVisualization.titleFontSize') }}</p><br>
                            <el-select
                              v-model="titleSize"
                              size="mini"
                              placeholder=""
                              style="width: 85px"
                              @change="changeTitleSize()"
                            >
                              <el-option v-for="item in labelFontSizeList" :key="item" :label="item" :value="item" />
                            </el-select>
                            <div class="block" style="padding-top: 10px">
                              <p class="charts-style-title">{{ $t('soi.dataVisualization.titleFontColor') }}</p><br>
                              <el-color-picker v-model="titleColor" size="mini" @change="changeTitleColor()" />
                            </div>
                          </div>
                          <p class="charts-style-title">{{ $t('soi.dataVisualization.backgroundColor') }}</p><br>
                          <el-color-picker v-model="backgroundColor" size="mini" @change="changeBackgroundColor()" />
                          <br>
                          <div v-if="chartType !== 'heatmap'">
                            <p class="charts-style-title" style="margin-top: 5px;margin-bottom: 5px">
                              {{ $t('soi.dataVisualization.showLabel') }}</p><br>
                            <el-switch
                              v-model="labelShow"
                              active-color="#13ce66"
                              style="margin-bottom: 5px"
                              inactive-color="#cccccc"
                              @change="changeLabelShow()"
                            />
                            <br>
                            <div id="label-attr" style="display: none">
                              <p class="charts-style-title">{{ $t('soi.dataVisualization.labelPosition') }}</p><br>
                              <el-select
                                v-model="labelPosition"
                                size="mini"
                                placeholder=""
                                style="width: 150px"
                                @change="changeLabelPosition()"
                              >
                                <el-option
                                  v-for="item in labelPositionList"
                                  :key="item.value"
                                  :label="item.label"
                                  :value="item.value"
                                />
                              </el-select>
                              <br>
                              <p class="charts-style-title" style="margin-top: 10px">
                                {{ $t('soi.dataVisualization.labelFont') }}
                              </p>
                              <br>
                              <el-select
                                v-model="labelFont"
                                size="mini"
                                placeholder=""
                                style="width: 140px;margin-bottom: 7px"
                                @change="changeLabelFont()"
                              >
                                <el-option
                                  v-for="item in fontList"
                                  :key="item.value"
                                  :label="item.label"
                                  :value="item.value"
                                />
                              </el-select>
                              <br>
                              <p class="charts-style-title">{{ $t('soi.dataVisualization.labelFontSize') }}</p><br>
                              <el-select
                                v-model="labelFontSize"
                                size="mini"
                                placeholder=""
                                style="width: 85px"
                                @change="changeLabelFontSize()"
                              >
                                <el-option v-for="item in labelFontSizeList" :key="item" :label="item" :value="item" />
                              </el-select>
                              <div class="block" style="padding-top: 10px">
                                <p class="charts-style-title">{{ $t('soi.dataVisualization.labelFontColor') }}</p><br>
                                <el-color-picker v-model="labelFontColor" size="mini" @change="changeLabelFontColor()" />
                              </div>
                            </div>
                            <p class="charts-style-title" style="margin-top: 5px;margin-bottom: 5px">
                              {{ $t('soi.dataVisualization.showLegend') }}</p><br>
                            <el-switch
                              v-model="legendShow"
                              style="margin-bottom: 5px"
                              active-color="#13ce66"
                              inactive-color="#cccccc"
                              @change="changeLegendShow()"
                            />
                            <br>
                            <div v-if="legendShow">
                              <p class="charts-style-title" style="margin-top: 5px">
                                {{ $t('soi.dataVisualization.legendOrient') }}
                              </p>
                              <br>
                              <el-select
                                v-model="legendOrient"
                                size="mini"
                                placeholder=""
                                style="width: 110px"
                                @change="changeLegendOrient()"
                              >
                                <el-option label="Horizontal" value="horizontal" />
                                <el-option label="Vertical" value="vertical" />
                              </el-select>
                              <br>
                              <p class="charts-style-title" style="margin-top: 5px">
                                {{ $t('soi.dataVisualization.legendPosition') }}
                              </p>
                              <br>
                              <p class="charts-style-title" style="width: 70px;color: #707070">
                                {{ $t('soi.dataVisualization.top') }}(%)
                              </p>
                              <el-input
                                v-model="legendTop"
                                placeholder="0-100"
                                size="mini"
                                style="width: 100px"
                                @change="changeLegendTop()"
                              />
                              <br>
                              <p class="charts-style-title" style="width: 70px;color: #707070">
                                {{ $t('soi.dataVisualization.left') }}</p>
                              <el-select
                                v-model="legendLeft"
                                size="mini"
                                placeholder=""
                                style="width: 100px"
                                @change="changeLegendLeft()"
                              >
                                <el-option
                                  v-for="item in legendLeftList"
                                  :key="item.value"
                                  :label="item.label"
                                  :value="item.value"
                                />
                              </el-select>
                              <br>
                              <p class="charts-style-title" style="margin-top: 5px">
                                {{ $t('soi.dataVisualization.legendFont') }}
                              </p>
                              <br>
                              <el-select
                                v-model="legendFont"
                                size="mini"
                                placeholder=""
                                style="width: 140px"
                                @change="changeLegendFont()"
                              >
                                <el-option
                                  v-for="item in fontList"
                                  :key="item.value"
                                  :label="item.label"
                                  :value="item.value"
                                />
                              </el-select>
                              <br>
                              <p class="charts-style-title" style="margin-top: 5px">
                                {{ $t('soi.dataVisualization.legendFontSize') }}</p><br>
                              <el-select
                                v-model="legendFontSize"
                                size="mini"
                                placeholder=""
                                style="width: 85px"
                                @change="changeLegendFontSize()"
                              >
                                <el-option
                                  v-for="item in labelFontSizeList"
                                  :key="item"
                                  :label="item"
                                  :value="item"
                                />
                              </el-select>
                              <br>
                              <p class="charts-style-title" style="margin-top: 5px">
                                {{ $t('soi.dataVisualization.legendFontColor') }}</p><br>
                              <el-color-picker v-model="legendFontColor" size="mini" @change="changeLegendFontColor()" />
                              <br>
                            </div>
                          </div>
                          <div v-if="chartType !== 'pie'">
                            <p class="charts-style-title">{{ $t('soi.dataVisualization.chartMargins') }}</p><br>
                            <p class="charts-style-title" style="color: #707070">{{ $t('soi.dataVisualization.chartLeftMargin') }}(%)</p><br>
                            <el-input
                              v-model="chartLeft"
                              placeholder="0-100"
                              size="mini"
                              style="width: 100px"
                              @change="changeChartLeft()"
                            />
                            <br>
                            <p class="charts-style-title" style="color: #707070">{{ $t('soi.dataVisualization.chartRightMargin') }}(%)</p><br>
                            <el-input
                              v-model="chartRight"
                              placeholder="0-100"
                              size="mini"
                              style="width: 100px"
                              @change="changeChartRight()"
                            />
                            <br>
                            <p class="charts-style-title" style="color: #707070">{{ $t('soi.dataVisualization.chartTopMargin') }}(%)</p><br>
                            <el-input
                              v-model="chartTop"
                              placeholder="0-100"
                              size="mini"
                              style="width: 100px"
                              @change="changeChartTop()"
                            />
                            <br>
                            <p class="charts-style-title" style="color: #707070">{{ $t('soi.dataVisualization.chartBottomMargin') }}(%)</p><br>
                            <el-input
                              v-model="chartBottom"
                              placeholder="0-100"
                              size="mini"
                              style="width: 100px"
                              @change="changeChartBottom()"
                            />
                            <br>
                          </div>
                          <div v-if="chartType === 'heatmap'">
                            <p class="charts-style-title">{{ $t('soi.dataVisualization.excessiveColor') }}</p><br>
                            <p class="charts-style-title" style="color: #707070">{{ $t('soi.dataVisualization.startColor') }}</p><br>
                            <el-color-picker v-model="startColor" size="mini" @change="changeStartColor()" />
                            <br>
                            <p class="charts-style-title" style="color: #707070">{{ $t('soi.dataVisualization.endColor') }}</p><br>
                            <el-color-picker v-model="endColor" size="mini" @change="changeEndColor()" />
                            <br>

                            <p class="charts-style-title">{{ $t('soi.dataVisualization.legend') }}</p><br>
                            <p class="charts-style-title" style="color: #707070">{{ $t('soi.dataVisualization.legendDirection') }}</p>
                            <el-select
                              v-model="heatmapOrient"
                              size="mini"
                              placeholder=""
                              style="width: 120px;margin-bottom: 7px"
                              @change="changeHeatmapDirection()"
                            >
                              <el-option
                                v-for="item in barDirectionList"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                              />
                            </el-select>
                            <br>
                            <p class="charts-style-title" style="color: #707070">{{ $t('soi.dataVisualization.legendLeftMargins') }}(%)</p><br>
                            <el-input
                              v-model="heatmapLeft"
                              placeholder="0-100"
                              size="mini"
                              style="width: 100px"
                              @change="changHeatmapLeft()"
                            />
                            <br>
                            <p class="charts-style-title" style="color: #707070">{{ $t('soi.dataVisualization.legendBottomMargins') }}(%)</p><br>
                            <el-input
                              v-model="heatmapBottom"
                              placeholder="0-100"
                              size="mini"
                              style="width: 100px"
                              @change="changHeatmapBottom()"
                            />
                            <br>
                          </div>
                          <div v-if="chartType === 'bar'">
                            <p class="charts-style-title">{{ $t('soi.dataVisualization.barDirection') }}</p><br>
                            <el-select
                              v-model="barDirection"
                              size="mini"
                              placeholder=""
                              style="width: 120px;margin-bottom: 7px"
                              @change="changeBarDirection()"
                            >
                              <el-option
                                v-for="item in barDirectionList"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                              />
                            </el-select>
                            <br>
                          </div>
                          <div v-if="chartType === 'pie'">
                            <p class="charts-style-title">{{ $t('soi.dataVisualization.pieType') }}</p><br>
                            <el-select
                              v-model="pieType"
                              size="mini"
                              placeholder=""
                              style="width: 120px;margin-bottom: 7px"
                              @change="changePieChartsType()"
                            >
                              <el-option
                                v-for="item in pieTypeList"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                              />
                            </el-select>
                            <br>
                            <div
                              v-if="lineOption.series[0].data && lineOption.series[0].data.length > 0"
                              style="padding-top: 8px"
                            >
                              <p class="charts-style-title">{{ $t('soi.dataVisualization.categoryColor') }}</p><br>
                              <div style="margin-left: 5px">
                                <el-radio-group v-model="pieName" size="mini">
                                  <el-radio
                                    v-for="(item,index) in lineOption.series[0].data"
                                    :key="index"
                                    :label="item.name"
                                    @click.native.prevent="handleCancel2(item.name,index)"
                                  >
                                    {{ item.name }}
                                  </el-radio>
                                </el-radio-group>
                              </div>
                              <el-color-picker
                                v-model="pieColor"
                                style="margin-top: 5px"
                                size="mini"
                                @change="changePieChartColor()"
                              />
                            </div>
                          </div>
                          <div v-if="chartType === 'line'">
                            <p class="charts-style-title">{{ $t('soi.dataVisualization.lineType') }}</p><br>
                            <el-select
                              v-model="chartsType"
                              size="mini"
                              placeholder=""
                              style="width: 100px"
                              @change="changeChartsType()"
                            >
                              <el-option
                                v-for="item in lineTypeList"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                              />
                            </el-select>
                            <br>
                            <p class="charts-style-title" style="margin-top: 7px">
                              {{ $t('soi.dataVisualization.connectionSymbol') }}</p><br>
                            <el-select
                              v-model="symbolName"
                              size="mini"
                              placeholder=""
                              style="width: 130px"
                              @change="changeSymbol()"
                            >
                              <el-option
                                v-for="item in symbolNameList"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                              />
                            </el-select>
                            <br>
                            <p class="charts-style-title" style="margin-top: 7px">{{ $t('soi.dataVisualization.lineStyle') }}</p>
                            <br>
                            <el-select
                              v-model="lineStyleType"
                              size="mini"
                              placeholder=""
                              style="width: 130px"
                              @change="changeLineStyleType()"
                            >
                              <el-option
                                v-for="item in lineStyleTypeList"
                                :key="item.value"
                                :label="item.label"
                                :value="item.value"
                              />
                            </el-select>
                          </div>
                          <div v-if="chartType === 'line' || chartType === 'bar'">
                            <div v-if="lineOption.series && lineOption.series.length > 0" style="padding-top: 8px">
                              <p class="charts-style-title">{{ $t('soi.dataVisualization.categoryColor') }}</p><br>
                              <div style="margin-left: 5px">
                                <el-radio-group v-model="lineName" size="mini">
                                  <el-radio
                                    v-for="(item,index) in lineOption.series"
                                    :key="index"
                                    :label="item.name"
                                    @click.native.prevent="handleCancel(item.name, index)"
                                  >
                                    {{ item.name }}
                                  </el-radio>
                                </el-radio-group>
                              </div>
                              <el-color-picker
                                v-model="lineColor"
                                style="margin-top: 5px"
                                size="mini"
                                @change="changeLineChartColor()"
                              />
                            </div>
                            <p class="charts-style-title" style="margin-top: 5px">{{ $t('soi.dataVisualization.stackTotal') }}</p>
                            <br>
                            <el-switch
                              v-model="chartStack"
                              style="margin-bottom: 5px"
                              active-color="#13ce66"
                              inactive-color="#cccccc"
                              @change="changeChartStack()"
                            />
                            <br>
                            <p class="charts-style-title" style="margin-top: 5px">{{ $t('soi.dataVisualization.showZoom') }}</p>
                            <br>
                            <el-switch
                              v-model="dataZoomShow"
                              style="margin-bottom: 5px"
                              active-color="#13ce66"
                              inactive-color="#cccccc"
                              @change="changeDataZoomShow()"
                            />
                            <br>
                            <div v-if="dataZoomShow">
                              <p class="charts-style-title" style="margin-top: 5px">
                                {{ $t('soi.dataVisualization.zoomFontColor') }}</p><br>
                              <el-color-picker
                                v-model="dataZoomFontColor"
                                size="mini"
                                @change="changeDataZoomFontColor()"
                              />
                              <br>
                            </div>
                            <p class="charts-style-title" style="margin-top: 5px">{{ $t('soi.dataVisualization.showXAxis') }}</p>
                            <br>
                            <el-switch
                              v-model="XAxisShow"
                              style="margin-bottom: 5px"
                              active-color="#13ce66"
                              inactive-color="#cccccc"
                              @change="changeXAxisShow()"
                            />
                            <br>
                            <div>
                              <p class="charts-style-title" style="margin-top: 5px">
                                {{ $t('soi.dataVisualization.xAxisColor') }}
                              </p>
                              <br>
                              <el-color-picker v-model="XAxisColor" size="mini" @change="changeXAxisColor()" />
                              <br>
                              <p class="charts-style-title" style="margin-top: 5px">
                                {{ $t('soi.dataVisualization.showAllLabelsOnXAxis') }}</p>
                              <br>
                              <el-switch
                                v-model="ShowLabelsOnXaxis"
                                style="margin-bottom: 5px"
                                active-color="#13ce66"
                                inactive-color="#cccccc"
                                @change="ShowAllLabelsOnXaxis()"
                              />
                              <br>
                              <p class="charts-style-title" style="margin-top: 5px">{{ $t('soi.dataVisualization.rotate') }}</p>
                              <br>
                              <el-select
                                v-model="Rotate"
                                size="mini"
                                placeholder=""
                                style="width: 130px"
                                @change="changeRotate()"
                              >
                                <el-option
                                  v-for="item in changeRotateList"
                                  :key="item.value"
                                  :label="item.label"
                                  :value="item.value"
                                />
                              </el-select>
                              <br>
                            </div>
                            <p class="charts-style-title" style="margin-top: 5px">{{ $t('soi.dataVisualization.showYAxis') }}</p>
                            <br>
                            <el-switch
                              v-model="YAxisShow"
                              style="margin-bottom: 5px"
                              active-color="#13ce66"
                              inactive-color="#cccccc"
                              @change="changeYAxisShow()"
                            />
                            <br>
                            <div>
                              <p class="charts-style-title" style="margin-top: 5px">
                                {{ $t('soi.dataVisualization.yAxisColor') }}
                              </p>
                              <br>
                              <el-color-picker v-model="YAxisColor" size="mini" @change="changeYAxisColor()" />
                              <br>
                            </div>
                            <p class="charts-style-title" style="margin-top: 5px">
                              {{ $t('soi.dataVisualization.showSplitLine') }}</p>
                            <br>
                            <el-switch
                              v-model="SplitLine"
                              style="margin-bottom: 5px"
                              active-color="#13ce66"
                              inactive-color="#cccccc"
                              @change="changeShowSplitLine()"
                            />
                          </div>
                        </div>
                      </div>
                    </el-tab-pane>
                    <el-tab-pane :label="$t('soi.dataVisualization.order')" name="Order">
                      <div v-show="orderShow" style="height: 400px;overflow-x: auto;overflow-y: auto">
                        <div v-for="(item,index) in orderList" :key="index">
                          <div v-if="resultFunction === 'sum'">
                            <p v-if="item.aggregate" :key="index" style="color: #707070;display: inline-block; font-size: 12px">
                              <span class="sum" style="display: inline-block">{{ resultFunction }}</span>
                              {{ `( ${item.name.columnName}(${item.name.tableName}) )` }}
                            </p>
                          </div>
                          <div v-if="resultFunction === 'avg'">
                            <p v-if="item.aggregate" :key="index" style="color: #707070;display: inline-block; font-size: 12px">
                              <span class="avg" style="display: inline-block">{{ resultFunction }}</span>
                              {{ `( ${item.name.columnName}(${item.name.tableName}) )` }}
                            </p>
                          </div>
                          <div v-if="resultFunction === 'count'">
                            <p v-if="item.aggregate" :key="index" style="color: #707070;display: inline-block; font-size: 12px">
                              <span class="count" style="display: inline-block">{{ resultFunction }}</span>
                              {{ `( ${item.name.columnName}(${item.name.tableName}) )` }}
                            </p>
                          </div>
                          <div v-if="resultFunction === 'typeCount' || resultFunction === 'categoryCount'">
                            <p v-if="item.aggregate" :key="index" style="color: #707070;display: inline-block; font-size: 12px">
                              {{ `${item.name.columnName}(${item.name.tableName})` }}
                            </p>
                          </div>
                          <el-radio-group v-if="item.aggregate" v-model="item.orderType">
                            <el-radio :label="0">{{ $t('soi.dataVisualization.noSorting') }}</el-radio>
                            <el-radio :label="1">{{ $t('soi.dataVisualization.ascending') }}</el-radio>
                            <el-radio :label="2">{{ $t('soi.dataVisualization.descending') }}</el-radio>
                          </el-radio-group>
                        </div>
                      </div>
                    </el-tab-pane>
                    <el-tab-pane
                      :disabled="dimensions.length > 1"
                      :label="$t('soi.dataVisualization.bucketing')"
                      style="height: 390px; overflow-y: auto"
                      class="bucketing"
                    >
                      <!-- bucketing -->
                      <div v-for="(dimension,index) in dimensions" :key="index" style="margin-bottom: 5px">
                        <el-button
                          v-if="index > 0 || dimension.type"
                          :disabled="resultFunction === 'typeCount' || resultFunction === 'categoryCount'"
                          size="mini"
                          @click="openBucketingDialog(dimension)"
                        >
                          <svg-icon v-if="MYSQLStringTypeList.indexOf(dimension.type) !== -1" icon-class="string" />
                          <svg-icon v-if="MYSQLNumberTypeList.indexOf(dimension.type) !== -1" icon-class="number" />
                          <svg-icon v-if="MYSQLDateTypeList.indexOf(dimension.type) !== -1" icon-class="date" />
                          {{ dimension.columnName }}
                        </el-button>
                      </div>
                    </el-tab-pane>
                    <el-dialog
                      :title="$t('soi.dataVisualization.bucketing') + `(${bucketingField.columnName})`"
                      :visible.sync="bucketingDialog"
                      :close-on-click-modal="false"
                      :close-on-press-escape="false"
                      width="40%"
                      @closed="bucketingDefault()"
                    >
                      <!--字符串类型-->
                      <div
                        v-if="MYSQLStringTypeList.indexOf(bucketingField.type) !== -1"
                        style="width: 450px;margin: 0 auto"
                      >
                        <div style="margin-bottom: 20px">
                          {{ $t('soi.dataVisualization.bucketingType') }}{{ bucketingInfo.end }}
                        </div>
                        <!-- 快捷键 -->
                        <div class="hot-key" style="margin-bottom: 10px">
                          <span>{{ $t('soi.dataVisualization.quickGroup') }}：</span>
                          <el-input-number
                            v-model="groupSize"
                            :min="100"
                            :max="10000"
                            :precision="0"
                            :controls="false"
                            :placeholder="$t('soi.dataVisualization.quickGroupTip')"
                            size="mini"
                          />
                          <el-button size="mini" @click="stringGroupHotKey()">{{ $t('soi.common.confirm') }}</el-button>
                        </div>
                        <div style="height: 300px;overflow-y: auto;" class="bucketing-item">
                          <div v-for="(item, index) in stringData.rule" :key="index" style="margin-bottom: 10px">
                            <el-input-number
                              v-model="item.string[0]"
                              :controls="false"
                              :precision="0"
                              :min="1"
                              :max="Number(bucketingInfo.end)"
                              :placeholder="$t('soi.dataVisualization.bucketingStart')"
                              style="width: 150px"
                              size="mini"
                            />
                            <el-input-number
                              v-model="item.string[1]"
                              :controls="false"
                              :precision="0"
                              :min="1"
                              :max="Number(bucketingInfo.end)"
                              :placeholder="$t('soi.dataVisualization.bucketingEnd')"
                              style="width: 150px"
                              size="mini"
                            />
                            <el-button
                              v-if="index === stringData.rule.length - 1"
                              :disabled="!stringData.rule[index].string[0] || !stringData.rule[index].string[1]"
                              size="mini"
                              @click="addDateSegment('string')"
                            >+
                            </el-button>
                            <el-button
                              v-if="index < stringData.rule.length - 1 || stringData.rule.length > 1"
                              size="mini"
                              @click="deleteDateSegment(index, 'string')"
                            >-
                            </el-button>
                          </div>
                        </div>
                      </div>
                      <!--数字类型-->
                      <div
                        v-if="MYSQLNumberTypeList.indexOf(bucketingField.type) !== -1"
                        style="width: 450px;margin: 0 auto"
                      >
                        <div style="margin-bottom: 20px">
                          {{ $t('soi.dataVisualization.bucketingNumber') }} {{ bucketingInfo.start }} - {{ bucketingInfo.end }}
                        </div>
                        <div style="height: 300px;overflow-y: auto;" class="bucketing-item">
                          <div v-for="(item, index) in numberData.rule" :key="index" style="margin-bottom: 10px">
                            <el-input-number
                              v-model="item.number[0]"
                              :controls="false"
                              :min="Number(bucketingInfo.start)"
                              :max="Number(bucketingInfo.end)"
                              :placeholder="$t('soi.dataVisualization.bucketingStart')"
                              style="width: 150px"
                              size="mini"
                            />
                            <el-input-number
                              v-model="item.number[1]"
                              :controls="false"
                              :min="Number(bucketingInfo.start)"
                              :max="Number(bucketingInfo.end)"
                              :placeholder="$t('soi.dataVisualization.bucketingEnd')"
                              style="width: 150px"
                              size="mini"
                            />
                            <el-button
                              v-if="index === numberData.rule.length - 1"
                              :disabled="(numberData.rule[index].number[0] === null || numberData.rule[index].number[0] === undefined) || numberData.rule[index].number[1] === null || numberData.rule[index].number[1] === undefined"
                              size="mini"
                              @click="addDateSegment('number')"
                            >+
                            </el-button>
                            <el-button
                              v-if="index < numberData.rule.length - 1 || numberData.rule.length > 1"
                              size="mini"
                              @click="deleteDateSegment(index, 'number')"
                            >-
                            </el-button>
                          </div>
                        </div>
                      </div>
                      <!--日期类型-->
                      <div
                        v-if="MYSQLDateTypeList.indexOf(bucketingField.type) !== -1"
                        style="width: 480px;margin: 0 auto"
                      >
                        <div
                          v-if="bucketingInfo.type !== 'TIME' && bucketingInfo.type !== 'YEAR'"
                          style="margin-bottom: 10px"
                        >
                          {{ $t('soi.dataVisualization.bucketingDate') }} {{ bucketingInfo.start }} - {{ bucketingInfo.end }}
                        </div>
                        <div v-if="bucketingInfo.type === 'TIME'" style="margin-bottom: 10px">
                          {{ $t('soi.dataVisualization.bucketingDate') }} {{ bucketingInfo.start }} - {{ bucketingInfo.end }}
                        </div>
                        <div v-if="bucketingInfo.type === 'YEAR'" style="margin-bottom: 10px">
                          {{ $t('soi.dataVisualization.bucketingDate') }} {{ bucketingInfo.start }} - {{ bucketingInfo.end }}
                        </div>
                        <!-- 快捷键 -->
                        <div class="hot-key" style="margin-bottom: 10px">
                          <el-button
                            v-if="bucketingField && bucketingField.type !== 'TIME' && bucketingField.type !== 'YEAR'"
                            type="primary"
                            size="mini"
                            @click="buckets(1)"
                          >
                            {{ $t('soi.dataVisualization.bucketByYear') }}
                          </el-button>
                          <el-button
                            v-if="bucketingField && bucketingField.type !== 'TIME' && bucketingField.type !== 'YEAR'"
                            type="primary"
                            size="mini"
                            @click="buckets(2)"
                          >
                            {{ $t('soi.dataVisualization.bucketByQuarterly') }}
                          </el-button>
                          <el-button
                            v-if="bucketingField && bucketingField.type !== 'TIME' && bucketingField.type !== 'YEAR'"
                            type="primary"
                            size="mini"
                            @click="buckets(3)"
                          >
                            {{ $t('soi.dataVisualization.bucketByMonth') }}
                          </el-button>
                        </div>
                        <div style="height: 300px;overflow-y: auto;" class="bucketing-item">
                          <div v-if="bucketingInfo.type !== 'TIME'">
                            <div
                              v-for="(item,index) in dateTimeData.rule"
                              :key="index"
                              style="margin-bottom: 10px"
                            >
                              <el-date-picker
                                v-if="bucketingInfo.type !== 'TIME' && bucketingInfo.type !== 'YEAR'"
                                v-model="item.date"
                                :clearable="false"
                                :default-time="['00:00:00', '23:59:59']"
                                :start-placeholder="$t('soi.dataVisualization.bucketingStart')"
                                :picker-options="pickerOptions"
                                :end-placeholder="$t('soi.dataVisualization.bucketingEnd')"
                                unlink-panels
                                value-format="timestamp"
                                size="mini"
                                type="daterange"
                                range-separator="To"
                              />
                              <div v-if="bucketingInfo.type === 'YEAR'" style="display: inline-block">
                                <el-date-picker
                                  v-model="item.date[0]"
                                  :clearable="false"
                                  :picker-options="pickerOptions"
                                  :placeholder="$t('soi.dataVisualization.bucketingStart')"
                                  style="width: 150px"
                                  size="mini"
                                  value-format="timestamp"
                                  type="year"
                                />
                                <span style="font-size: 12px;margin: 0 15px">To</span>
                                <el-date-picker
                                  v-model="item.date[1]"
                                  :clearable="false"
                                  :picker-options="pickerOptions"
                                  :placeholder="$t('soi.dataVisualization.bucketingEnd')"
                                  style="width: 150px"
                                  size="mini"
                                  type="year"
                                  value-format="timestamp"
                                />
                              </div>
                              <el-button
                                v-if="index === dateTimeData.rule.length - 1"
                                :disabled="dateTimeData.rule[index].date.length === 0"
                                size="mini"
                                @click="addDateSegment('date')"
                              >
                                +
                              </el-button>
                              <el-button
                                v-if="index < dateTimeData.rule.length - 1 || dateTimeData.rule.length > 1"
                                size="mini"
                                @click="deleteDateSegment(index, 'date')"
                              >-
                              </el-button>
                            </div>
                          </div>
                          <div v-if="bucketingInfo.type === 'TIME'">
                            <div
                              v-for="(item, index) in rule"

                              :key="index"
                              style="margin-bottom: 10px"
                            >
                              <el-time-picker
                                v-model="item.date"
                                :clearable="false"
                                :start-placeholder="$t('soi.dataVisualization.bucketingStart')"
                                :end-placeholder="$t('soi.dataVisualization.bucketingEnd')"
                                size="mini"
                                is-range
                                value-format="HH:mm:ss"
                                range-separator="To"
                              />
                              <el-button
                                v-if="index === rule.length - 1"
                                :disabled="rule[index].date.length === 0"
                                size="mini"
                                @click="addDateSegment('date')"
                              >+
                              </el-button>
                              <el-button
                                v-if="index < rule.length - 1 || rule.length > 1"
                                size="mini"
                                @click="deleteDateSegment(index, 'date')"
                              >-
                              </el-button>
                            </div>
                          </div>
                        </div>
                      </div>
                      <span slot="footer" class="dialog-footer">
                        <el-button @click="bucketingDialog = false">{{ $t('soi.common.cancel') }}</el-button>
                        <el-button @click="clearBucketing()">{{ $t('soi.common.clear') }}</el-button>
                        <el-button
                          v-if="MYSQLDateTypeList.indexOf(bucketingField.type) !== -1"
                          type="primary"
                          @click="addBucketingCondition('date')"
                        >{{ $t('soi.common.confirm') }}</el-button>
                        <el-button
                          v-if="MYSQLNumberTypeList.indexOf(bucketingField.type) !== -1"
                          type="primary"
                          @click="addBucketingCondition('number')"
                        >{{ $t('soi.common.confirm') }}</el-button>
                        <el-button
                          v-if="MYSQLStringTypeList.indexOf(bucketingField.type) !== -1"
                          type="primary"
                          @click="addBucketingCondition('string')"
                        >{{ $t('soi.common.confirm') }}</el-button>
                      </span>
                    </el-dialog>
                  </el-tabs>
                </div>
              </div>
            </el-col>
            <div class="color-dialog">
              <el-dialog
                :visible.sync="colorDialog"
                :close-on-click-modal="false"
                :close-on-press-escape="false"
                :show-close="false"
                style="margin-top: 8vh;margin-right: 45px"
                width="15%"
              >
                <div style="height: 200px;overflow-y: auto">
                  <el-form ref="form" label-position="left">
                    <div v-for="(item,index) in colorCategory" :key="index">
                      <el-form-item :label="item.name" label-width="180px">
                        <el-color-picker v-model="item.color" size="mini" />
                      </el-form-item>
                    </div>
                  </el-form>
                </div>
                <span slot="footer" class="dialog-footer">
                  <el-button size="mini" @click="cancelColor">{{ $t('soi.common.cancel') }}</el-button>
                  <el-button type="primary" size="mini" @click="colorDialog = false">
                    {{ $t('soi.common.confirm') }}
                  </el-button>
                </span>
              </el-dialog>
            </div>
          </el-row>
        </el-tab-pane>
        <el-tab-pane label="Data" name="second">
          <el-table
            :data="previewData"
            height="800"
            style="width: 100%"
          >
            <el-table-column
              v-for="(item, index) in previewKeys"
              :key="index"
              :render-header="labelHead"
              :prop="item"
              :label="item"
            />
          </el-table>
        </el-tab-pane>
      </el-tabs>
    </customize-card>
  </div>
</template>

<script>
import draggable from 'vuedraggable'
import Html2Canvas from 'html2canvas'
import elementResizeDetectorMaker from 'element-resize-detector'
import CustomizeCard from '@/components/CustomizeCard/index.vue'
import {
  getChartInfo,
  getColumns,
  getData,
  getDataTableList,
  getPreviewData,
  saveChart,
  updateChart,
  getEnum,
  getBucketingField
} from '@/api/practice/data-visualization'
import { mapGetters } from 'vuex'

export default {
  name: 'DataVisualizationMyChartForm',
  components: {
    draggable,
    CustomizeCard
  },
  data() {
    const than = this
    return {
      arr2: [
        { id: -1, databaseName: '', tableName: '', columnName: '', type: '' }
      ],
      loading: false,
      chartId: this.$route.query.chartId,
      dataTableName: this.$route.query.dataTableName,
      tableName: '',
      previewKeys: [],
      previewData: [],
      columns: [],
      search: '',
      dimensions: [{ id: -1, columnName: '' }],
      measures: [{ id: -1, columnName: '' }],
      // 字段信息
      fieldInfoList: [],
      lineOption: {},
      barOption: {},
      pieOption: {},
      groupSize: undefined,
      showtable: false,
      myChart: null,
      chart1: null,
      chart2: null,
      chart3: null,
      chart4: null,
      activeTag: 'first',
      computeConditions: [
        { value: '>', label: '>' },
        { value: '<', label: '<' },
        { value: '>=', label: '>=' },
        { value: '<=', label: '<=' },
        { value: '!=', label: '!=' },
        { value: '=', label: '=' },
        { value: 'IS NULL', label: 'IS NULL' },
        { value: 'IS NOT NULL', label: 'IS NOT NULL' }
      ],
      filterCondition: '>',
      filterMatter: '',
      filterAndOr: 'AND',
      filterDialogVisible: false,
      colorDialogVisible: false,
      innerDateDialogVisible: false,
      innerDialogVisible: false,
      requestChartsData: {
        'aggregators': [],
        'filters': [],
        'groups': [],
        'orders': [],
        'chartType': '',
        'color': '',
        'bucketings': []
      },
      resultFunction: 'sum',
      resultOptions: [
        {
          label: 'sum',
          value: 'sum'
        },
        {
          label: 'count',
          value: 'count'
        },
        {
          label: 'avg',
          value: 'avg'
        },
        {
          label: 'typeCount',
          value: 'categoryCount'
        },
        {
          label: 'childTypeCount',
          value: 'typeCount'
        }
      ],
      // 聚合
      aggregators: [],
      // 过滤
      filters: [],
      // 分组
      groups: [],
      // 排序
      orders: [],
      // 选中的Color
      colors: [],
      // 选中的Color(groups)
      selectColors: [],
      filterList: [],
      filterName: '',
      filterDate: '',
      table: {},
      filterType: '',
      key: '', // 搜索的关键字
      chartType: '',
      chartsType: '1',
      lineTypeList: [
        {
          label: 'Line',
          value: '1'
        },
        {
          label: 'Area',
          value: '2'
        },
        {
          label: 'Smooth',
          value: '3'
        }
      ],
      // 标题
      chartsTitle: '',
      titleTop: '0',
      titleLeft: '0',
      titleColor: '#000',
      titleFont: 'sans-serif',
      titleSize: '18',
      backgroundColor: '#fff',
      // 折线图颜色
      lineColor: '',
      labelShow: false,
      labelPosition: 'top',
      positionList: [
        {
          label: 'Top',
          value: 'top'
        },
        {
          label: 'Left',
          value: 'left'
        },
        {
          label: 'Right',
          value: 'right'
        },
        {
          label: 'Bottom',
          value: 'bottom'
        }
      ],
      labelFont: 'sans-serif',
      fontList: [
        {
          label: 'sans-serif',
          value: 'sans-serif'
        },
        {
          label: 'serif',
          value: 'serif'
        },
        {
          label: 'monospace',
          value: 'monospace'
        },
        {
          label: 'Arial',
          value: 'Arial'
        },
        {
          label: 'Courier New',
          value: 'Courier New'
        },
        {
          label: 'Microsoft YaHei',
          value: 'Microsoft YaHei'
        }
      ],
      labelFontSize: '12',
      labelFontSizeList: [8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25, 26, 27, 28, 29, 30, 40, 50, 60],
      labelFontColor: '',
      lineName: '',
      symbolName: 'emptyCircle',
      symbolNameList: [
        {
          label: 'Empty Circle',
          value: 'emptyCircle'
        },
        {
          label: 'Circle',
          value: 'circle'
        },
        {
          label: 'Rect',
          value: 'rect'
        },
        {
          label: 'Round Rect',
          value: 'roundRect'
        },
        {
          label: 'Triangle',
          value: 'triangle'
        },
        {
          label: 'Diamond',
          value: 'diamond'
        },
        {
          label: 'Pin',
          value: 'pin'
        },
        {
          label: 'Arrow',
          value: 'arrow'
        },
        {
          label: 'None',
          value: 'none'
        }
      ],
      lineStyleType: 'solid',
      theme: 'ligth',
      lineStyleTypeList: [
        {
          label: 'Solid',
          value: 'solid'
        },
        {
          label: 'Dashed',
          value: 'dashed'
        },
        {
          label: 'Dotted',
          value: 'dotted'
        }
      ],
      legendShow: true,
      legendTop: '0',
      legendLeft: 'center',
      legendFont: 'sans-serif',
      legendFontSize: '12',
      legendFontColor: '#333',
      XAxisShow: true,
      YAxisShow: true,
      XAxisColor: '#333',
      YAxisColor: '#333',
      startColor: '#C7DBFF',
      endColor: '#5291FF',
      chartLeft: 10,
      chartRight: 10,
      chartTop: 8,
      chartBottom: 20,
      heatmapOrient: 'horizontal',
      heatmapLeft: 40,
      heatmapBottom: 5,
      legendLeftList: [
        {
          label: 'Left',
          value: 'left'
        },
        {
          label: 'Center',
          value: 'center'
        },
        {
          label: 'Right',
          value: 'right'
        }
      ],
      legendOrient: 'horizontal',
      dataZoomShow: false,
      dataZoomFontColor: '#333',
      chartStack: false,
      pieType: '1',
      pieTypeList: [
        {
          label: 'Style One',
          value: '1'
        },
        {
          label: 'Style Two',
          value: '2'
        },
        {
          label: 'Style Three',
          value: '3'
        }
      ],
      labelPositionList: [],
      lineOrBarPosition: [
        {
          label: 'Top',
          value: 'top'
        },
        {
          label: 'Left',
          value: 'left'
        },
        {
          label: 'Right',
          value: 'right'
        },
        {
          label: 'Bottom',
          value: 'bottom'
        },
        {
          label: 'Inside',
          value: 'inside'
        },
        {
          label: 'InsideLeft',
          value: 'insideLeft'
        },
        {
          label: 'InsideRight',
          value: 'insideRight'
        },
        {
          label: 'InsideTop',
          value: 'insideTop'
        },
        {
          label: 'InsideBottom',
          value: 'insideBottom '
        },
        {
          label: 'InsideTopLeft',
          value: 'insideTopLeft '
        },
        {
          label: 'InsideBottomLeft',
          value: 'insideBottomLeft'
        },
        {
          label: 'InsideTopRight',
          value: 'insideTopRight'
        },
        {
          label: 'InsideBottomRight',
          value: 'insideBottomRight'
        }
      ],
      piePosition: [
        {
          label: 'Outside',
          value: 'outside'
        },
        {
          label: 'Inside',
          value: 'inside'
        },
        {
          label: 'Inner',
          value: 'inner'
        },
        {
          label: 'Center',
          value: 'center'
        }
      ],
      //  备份初始样式
      initOption: {},
      top: 0,
      left: 0,
      tagName: '',
      arr: [0],
      dataShow: true,
      AndOr: 'AND',
      addOk: true,
      activeName: 'Aggregate',
      tableData: [],
      tableParameters: [],
      currentPage: 1,
      pagesize: 10,
      payloadList: [],
      activePage: 'Charts',
      orderList: [],
      orderShow: true,
      barDirectionList: [
        {
          label: 'Horizontal',
          value: 'horizontal'
        },
        {
          label: 'Vertical',
          value: 'vertical'
        }
      ],
      barDirection: 'vertical',
      chartData: [],
      chartParameters: [],
      createOrEdit: '',
      chartName: '',
      orderFlag: false,
      dataModel: {},
      list: [],
      viewId: '',
      MYSQLDataTypeList: ['TINYINT', 'SMALLINT', 'MEDIUMINT', 'INT', 'BIGINT', 'FLOAT', 'DOUBLE', 'DECIMAL', 'YEAR', 'TIMESTAMP', 'TIME', 'DATE', 'DATETIME', 'SET', 'ENUM', 'BLOB', 'TINYTEXT', 'MEDIUMTEXT', 'TEXT', 'LONGTEXT', 'VARCHAR', 'CHAR'],
      MYSQLNumberTypeList: ['TINYINT', 'SMALLINT', 'MEDIUMINT', 'INT', 'BIGINT', 'FLOAT', 'DOUBLE', 'DECIMAL', 'SINT'],
      MYSQLDateTypeList: ['YEAR', 'TIMESTAMP', 'TIME', 'DATE', 'DATETIME', 'SDATE'],
      MYSQLStringTypeList: ['SET', 'ENUM', 'VARCHAR', 'CHAR', 'TINYTEXT', 'MEDIUMTEXT', 'TEXT', 'LONGTEXT', 'SVARCHAR'],
      MYSQLNoNumberTypeList: ['YEAR', 'TIMESTAMP', 'TIME', 'DATE', 'DATETIME', 'SET', 'ENUM', 'BLOB', 'TINYTEXT', 'MEDIUMTEXT', 'TEXT', 'LONGTEXT', 'VARCHAR', 'CHAR', 'SDATE', 'SVARCHAR'],
      MYSQLNoDateTypeList: ['TINYINT', 'SMALLINT', 'MEDIUMINT', 'INT', 'BIGINT', 'FLOAT', 'DOUBLE', 'DECIMAL', 'SET', 'ENUM', 'BLOB', 'TINYTEXT', 'MEDIUMTEXT', 'TEXT', 'LONGTEXT', 'VARCHAR', 'CHAR', 'SVARCHAR', 'SINT'],
      MYSQLNoStringTypeList: ['TINYINT', 'SMALLINT', 'MEDIUMINT', 'INT', 'BIGINT', 'FLOAT', 'DOUBLE', 'DECIMAL', 'YEAR', 'TIMESTAMP', 'TIME', 'DATE', 'DATETIME', 'SDATE', 'SINT'],
      value: [],
      DataSourceList: [],
      saveButton: true,
      tableClick: false,
      barClick: false,
      lineClick: false,
      pieClick: false,
      heatmapClick: false,
      tempcolumns: [],
      editGetColumns: false,
      closeLoading: false,
      defaultStyleFlag: false,
      ShowLabelsOnXaxis: false,
      Rotate: 0,
      changeRotateList: [
        { label: 0, value: 0 },
        { label: 5, value: 5 },
        { label: 10, value: 10 },
        { label: 15, value: 15 },
        { label: 30, value: 30 },
        { label: 45, value: 45 },
        { label: 60, value: 60 },
        { label: 90, value: 90 },
        { label: -5, value: -5 },
        { label: -10, value: -10 },
        { label: -15, value: -15 },
        { label: -30, value: -30 },
        { label: -45, value: -45 },
        { label: -60, value: -60 },
        { label: -90, value: -90 }
      ],
      SplitLine: true,
      isSingleTable: false,
      colorDialog: false,
      colorCategory: [],
      echartsDefaultColors: ['#c23531', '#2f4554', '#61a0a8', '#d48265', '#91c7ae', '#749f83', '#ca8622', '#bda29a', '#6e7074', '#546570', '#c4ccd3'],
      editOption: -1,
      clearStyleCount: 0,
      clearStyleFlag: true,
      firstEdit: 0,
      dataZoom: [
        {
          type: 'slider',
          textStyle: {
            color: '#333'
          },
          show: true,
          bottom: '10',
          start: 0,
          end: 100
        }
      ],
      // bucketing
      bucketingDialog: false, // 分桶对话框
      bucketingField: { // 分桶的字段信息
      },
      bucketingInfo: {},
      // 日期类型条件
      bucketingDateTime: [],
      bucketings: [],
      dateTimeData: {
        fieldName: '',
        tableName: '',
        databaseName: '',
        aggregate: 'sum',
        dataType: '',
        rule: [
          {
            date: []
          }
        ]
      },
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() < new Date(than.bucketingInfo.start).getTime() - 3600 * 8 * 1000 || time.getTime() >= new Date(than.bucketingInfo.end).getTime() + 3600 * 1000 * 16 - 1
        }
      },
      rule: [
        {
          date: [new Date(), new Date()]
        }
      ],
      numberData: {
        fieldName: '',
        tableName: '',
        databaseName: '',
        aggregate: 'sum',
        dataType: '',
        rule: [
          {
            number: []
          }
        ]
      },
      stringData: {
        fieldName: '',
        tableName: '',
        databaseName: '',
        aggregate: 'sum',
        dataType: '',
        rule: [
          {
            string: []
          }
        ]
      },
      startFlag: '',
      pieName: '', // 饼图的所有图例名称,为了修改饼图的颜色
      pieColor: '',
      filterGroups: [],
      showInput: false,
      chartsOne: {},
      chartsTwo: {},
      chartsThree: {},
      chartsFour: {},
      chartOptions: [],
      styles: ['light', 'dark']
    }
  },
  computed: {
    ...mapGetters(['userDetails']),
    chartsHeight() {
      return 746 / 2
    }
  },
  watch: {
    dimensions: {
      handler() {
        if (this.dimensions.length === 1) {
          this.myChart = this.$echarts.init(document.getElementById('myChart'))
        }
        if (this.dimensions.length === 0) {
          this.dimensions.push({ id: -1 })
        }
        this.isClickChart()
        this.saveButton = true
        this.chartType = ''
        this.getGroupsData()
        if (this.orderFlag || this.createOrEdit === 'create') {
          this.getOrderList()
        }
      },
      deep: true
    },
    measures: {
      handler() {
        if (this.measures.length === 0) {
          this.measures.push({ id: -1 })
        }
        this.isClickChart()
        this.saveButton = true
        this.chartType = ''
        this.getAggregatorsData()
        if (this.orderFlag || this.createOrEdit === 'create') {
          this.getOrderList()
        }
        this.orderFlag = true
      },
      deep: true
    },
    colors: {
      handler() {
        this.getColorData()
      },
      deep: true
    },
    resultFunction: {
      handler(newValue) {
        if (newValue === 'categoryCount') {
          this.tableClick = true
          this.barClick = true
          this.lineClick = true
          this.pieClick = true
          this.bucketings = []
        } else {
          this.isClickChart()
        }
        this.getAggregatorsData()
        this.orderListHandler()
      },
      deep: true
    },
    filterMatter: {
      handler() {
        this.formatMath()
      }
    },
    labelShow: {
      handler() {
        this.showLabelAttr()
      }
    },
    chartType: {
      handler() {
        if (this.chartType.length > 0 && this.chartType !== 'table') {
          document.getElementById('chart-content').style.display = 'inline'
          if (this.createOrEdit === 'create') {
            if (this.chartType === 'line') {
              this.labelPositionList = this.lineOrBarPosition
              this.labelShow = false
              this.legendOrient = 'horizontal'
              this.legendTop = '0'
              this.legendLeft = 'center'
            }
            if (this.chartType === 'bar') {
              this.labelPositionList = this.lineOrBarPosition
              this.labelShow = false
              this.legendOrient = 'horizontal'
              this.legendTop = '0'
              this.legendLeft = 'center'
            }
            if (this.chartType === 'pie') {
              this.labelPositionList = this.piePosition
              this.labelShow = true
              this.legendOrient = 'vertical'
              this.legendTop = '5'
              this.legendLeft = 'right'
            }
          } else {
            if (this.chartType === 'line') {
              this.labelPositionList = this.lineOrBarPosition
            }
            if (this.chartType === 'bar') {
              this.labelPositionList = this.lineOrBarPosition
            }
            if (this.chartType === 'pie') {
              this.labelPositionList = this.piePosition
            }
          }
        } else {
          document.getElementById('chart-content').style.display = 'none'
        }
      }
    },
    titleTop: {
      handler() {
        this.titleTop = this.titleTop.replace(/[^\d]/g, '')
      }
    },
    titleLeft: {
      handler() {
        this.titleLeft = this.titleLeft.replace(/[^\d]/g, '')
      }
    },
    legendTop: {
      handler() {
        this.legendTop = this.legendTop.replace(/[^\d]/g, '')
      }
    },
    orderList: {
      handler() {
        this.orderListHandler()
      },
      deep: true
    },
    filterCondition: {
      handler() {
        if (this.filterCondition === 'IS NULL' || this.filterCondition === 'IS NOT NULL') {
          this.filterMatter = ' '
        } else {
          this.filterMatter = ''
        }
      }
    },
    arr2: {
      handler() {
        this.isClickChart()
        this.saveButton = true
      }
    }
  },
  async mounted() {
    if (this.chartId) {
      this.createOrEdit = 'edit'
      this.initCharts()
      await this.getModelList()
      this.initChartsData()
    } else {
      this.createOrEdit = 'create'
      this.getModelList()
      this.initCharts()
    }
    // 监听窗口的变化，实时调用 echarts的 resize事件
    window.onresize = () => {
      this.myChart.resize()
      this.chart1.resize()
      this.chart2.resize()
      this.chart3.resize()
      this.chart4.resize()
    }
    // 监听myChart大小变化
    const erd = elementResizeDetectorMaker()
    const _this = this
    erd.listenTo(document.getElementById('myChart'), element => {
      _this.$nextTick(() => {
        this.myChart.resize()
      })
    })
    erd.listenTo(document.getElementById('chart1'), element => {
      _this.$nextTick(() => {
        this.chart1.resize()
      })
    })
    erd.listenTo(document.getElementById('chart2'), element => {
      _this.$nextTick(() => {
        this.chart2.resize()
      })
    })
    erd.listenTo(document.getElementById('chart3'), element => {
      _this.$nextTick(() => {
        this.chart3.resize()
      })
    })
    erd.listenTo(document.getElementById('chart4'), element => {
      _this.$nextTick(() => {
        this.chart4.resize()
      })
    })
  },
  methods: {
    initChartsData() {
      const vue = this
      vue.loading = true

      getChartInfo(vue.chartId)
        .then((res) => {
          const config = JSON.parse(res.data.config)
          vue.isSingleTable = config.isSingleTable
          vue.showtable = config.showtable
          vue.arr2 = config.arr2
          vue.colorCategory = config.colorCategory
          vue.bucketings = config.bucketings
          vue.editChartConfig(config.style, config.aggregation, config.dimensions, config.measures, config.orderList, config.requestChartsData)
          vue.tempcolumns = config.columns
          vue.chartName = res.data.name
          for (let i = 0; i < vue.DataSourceList.length; i++) {
            if (res.data.viewName === vue.DataSourceList[i].view.name) {
              vue.value = [vue.DataSourceList[i].source.name, vue.DataSourceList[i].view.id]
              vue.viewId = res.data.viewId
            }
          }
          vue.editOption = config.option
          vue.getColumns()
          vue.initLineData(config.requestChartsData, config.chartType)
        })
        .finally(() => {
          vue.loading = false
        })
    },
    getdata() {
      this.tableData = this.payloadList.slice((this.currentPage - 1) * this.pagesize, this.pagesize * this.currentPage)
    },
    // 取消选中的标签
    cancelSelectTag(type, index) {
      if (type === 'dimensions') {
        for (let i = 0; i < this.bucketings.length; i++) {
          if (this.dimensions[index].columnName === this.bucketings[i].fieldName) {
            this.bucketings.splice(i, 1)
          }
        }
        this.dimensions.splice(index, 1)
      } else if (type === 'measures') {
        this.measures.splice(index, 1)
      }
    },
    // 初始化echarts
    initCharts() {
      this.myChart = this.$echarts.init(document.getElementById('myChart'))
      this.chart1 = this.$echarts.init(document.getElementById('chart1'), this.styles[0])
      this.chart2 = this.$echarts.init(document.getElementById('chart2'), this.styles[0])
      this.chart3 = this.$echarts.init(document.getElementById('chart3'), this.styles[0])
      this.chart4 = this.$echarts.init(document.getElementById('chart4'), this.styles[0])
    },
    downloadImage() {
      this.transformSvg('charts').then(res => {
        if (res) {
          this.downloadFile(res, 'echarts.png')
        }
      })
    },
    downloadFile(content, fileName) { // 下载base64图片
      const base64ToBlob = function(code) {
        const parts = code.split(';base64,')
        const contentType = parts[0].split(':')[1]
        const raw = window.atob(parts[1])
        const rawLength = raw.length
        const uInt8Array = new Uint8Array(rawLength)
        for (let i = 0; i < rawLength; ++i) {
          uInt8Array[i] = raw.charCodeAt(i)
        }
        return new Blob([uInt8Array], {
          type: contentType
        })
      }
      const aLink = document.createElement('a')
      const blob = base64ToBlob(content)
      const evt = document.createEvent('HTMLEvents')
      evt.initEvent('click', true, true)
      aLink.download = fileName
      aLink.href = URL.createObjectURL(blob)
      aLink.click()
    },
    switchCharts(type) {
      // 设置请求参数
      this.requestChartsData.aggregators = this.aggregators
      this.requestChartsData.filters = this.filters
      this.requestChartsData.groups = this.groups
      this.requestChartsData.orders = this.orders
      this.requestChartsData.bucketings = this.bucketings
      if (this.arr2[0].id !== -1) {
        // 请求带 color
        if (this.isSingleTable) {
          // 单表
          this.requestChartsData.color = this.arr2[0].columnName
        } else {
          // 多表
          this.requestChartsData.color = this.arr2[0].databaseName + '$' + this.arr2[0].columnName + '(' + this.arr2[0].tableName + ')'
        }
      } else {
        // 请求不带color
        this.requestChartsData.color = ''
      }
      switch (type) {
        case 'line':
          if ((this.dimensions.length === 1 && this.dimensions[0].id !== -1) && this.measures[0].id === -1 && this.resultFunction === 'categoryCount') {
            // 选择一个 Dimensions
            const lineRequestChartsData = this.requestChartsData
            lineRequestChartsData.groups = []
            if (this.isSingleTable) {
              lineRequestChartsData.aggregators = [{ column: this.dimensions[0].columnName, func: this.resultFunction }]
            } else {
              lineRequestChartsData.aggregators = [{
                column: this.dimensions[0].databaseName + '$' + this.dimensions[0].columnName + '(' + this.dimensions[0].tableName + ')',
                func: this.resultFunction
              }]
            }
          }
          this.initLineData(this.requestChartsData, type)
          break
        case 'bar':
          if ((this.dimensions.length === 1 && this.dimensions[0].id !== -1) && this.measures[0].id === -1 && this.resultFunction === 'categoryCount') {
            // 选择一个 Dimensions
            const barRequestChartsData = this.requestChartsData
            barRequestChartsData.groups = []
            if (this.isSingleTable) {
              barRequestChartsData.aggregators = [{ column: this.dimensions[0].columnName, func: this.resultFunction }]
            } else {
              barRequestChartsData.aggregators = [{
                column: this.dimensions[0].databaseName + '$' + this.dimensions[0].columnName + '(' + this.dimensions[0].tableName + ')',
                func: this.resultFunction
              }]
            }
          }
          this.initLineData(this.requestChartsData, type)
          break
        case 'pie':
          if ((this.dimensions.length === 1 && this.dimensions[0].id !== -1) && this.measures[0].id === -1) {
            // 选择一个 Dimensions
            const pieRequestChartsData = this.requestChartsData
            pieRequestChartsData.groups = []
            if (this.isSingleTable) {
              pieRequestChartsData.aggregators = [{ column: this.dimensions[0].columnName, func: this.resultFunction }]
            } else {
              pieRequestChartsData.aggregators = [{
                column: this.dimensions[0].databaseName + '$' + this.dimensions[0].columnName + '(' + this.dimensions[0].tableName + ')',
                func: this.resultFunction
              }]
            }
            this.initLineData(pieRequestChartsData, type)
          } else if (this.measures.length === 1 && this.measures[0].id !== -1) {
            this.initLineData(this.requestChartsData, type)
          }
          break
        case 'heatmap':
          if ((this.dimensions.length === 1 && this.dimensions[0].id !== -1) && this.measures[0].id === -1) {
            // 选择一个 Dimensions
            const heatmapRequestChartsData = this.requestChartsData
            heatmapRequestChartsData.groups = []
            if (this.isSingleTable) {
              heatmapRequestChartsData.aggregators = [{
                column: this.dimensions[0].columnName,
                func: this.resultFunction
              }]
            } else {
              heatmapRequestChartsData.aggregators = [{
                column: this.dimensions[0].databaseName + '$' + this.dimensions[0].columnName + '(' + this.dimensions[0].tableName + ')',
                func: this.resultFunction
              }]
            }
            this.initLineData(heatmapRequestChartsData, type)
          } else if (this.measures.length === 1 && this.measures[0].id !== -1) {
            this.initLineData(this.requestChartsData, type)
          }
          break
        case 'table':
          if ((this.dimensions.length === 1 && this.dimensions[0].id !== -1) && this.measures[0].id === -1) {
            var tableRequestChartsData = this.requestChartsData
            tableRequestChartsData.groups = []
            if (this.isSingleTable) {
              tableRequestChartsData.aggregators = [{
                column: this.dimensions[0].columnName,
                func: this.resultFunction
              }]
            } else {
              tableRequestChartsData.aggregators = [{
                column: this.dimensions[0].databaseName + '$' + this.dimensions[0].columnName + '(' + this.dimensions[0].tableName + ')',
                func: this.resultFunction
              }]
            }
            this.initLineData(tableRequestChartsData, type)
          } else {
            this.initLineData(this.requestChartsData, type)
          }
          break
        default:
          break
      }
    },
    initLineData(requestChartsData, type) {
      if (this.clearStyleCount === 1 && this.clearStyleFlag && this.chartType !== 'table' && type !== 'table') {
        this.$confirm(this.$t('soi.dataVisualization.clearStyleTip'), this.$t('soi.common.tip'), {
          confirmButtonText: this.$t('soi.common.confirm'),
          cancelButtonText: this.$t('soi.common.cancel'),
          type: this.$t('soi.common.warning')
        }).then(() => {
          this.chartOptions = []
          this.clearStyleFlag = false
          this.myChart.clear()
          this.chart1.clear()
          this.chart2.clear()
          this.chart3.clear()
          this.chart4.clear()
          this.initLineData2(requestChartsData, type)
        }).catch(() => {
          return
        })
      } else {
        this.myChart.clear()
        this.chart1.clear()
        this.chart2.clear()
        this.chart3.clear()
        this.chart4.clear()
        this.initLineData2(requestChartsData, type)
      }
    },
    initLineData2(requestChartsData, type) {
      this.clearStyleCount = 1
      const vue = this
      requestChartsData.chartType = type

      for (let i = 0; i < vue.bucketings.length; i++) {
        vue.bucketings[i].aggregate = this.resultFunction
      }
      vue.loading = true
      getData(vue.viewId, requestChartsData)
        .then((res) => {
          vue.firstEdit += 1
          // 自定义样式恢复默认值
          if (vue.editOption === -1) {
            vue.defaultData()
          }
          // 不带color的图
          if (res.data && res.data.length > 0 && vue.arr2.length === 1 && vue.arr2[0].id === -1) {
            // 设置保存按钮为可点状态
            vue.saveButton = false
            const data = res.data
            // 图表类型为表格
            if (type === 'table') {
              vue.chartParameters = []
              for (const key in data[0]) {
                // 单张表的统计
                if (vue.isSingleTable) {
                  vue.chartParameters.push({ label: key, value: key })
                } else {
                  // 多张表的统计
                  if (key.indexOf('sum(') !== -1 || key.indexOf('count(') !== -1 || key.indexOf('avg(') !== -1) {
                    // 处理聚合字段的名称
                    let temp = key.substring(key.indexOf('(') + 1, key.indexOf('$') + 1)
                    temp = key.replace(temp, '')
                    vue.chartParameters.push({ label: temp, value: key })
                  } else {
                    // 处理非聚合字段
                    vue.chartParameters.push({ label: key.substring(key.indexOf('$') + 1), value: key })
                  }
                }
              }
              vue.chartData = data
              // 展示表格，隐藏ecahrts图表
              vue.showtable = true
            } else if (type === 'pie') {
              // 如果统计图为饼图，隐藏表格显示echarts统计图
              vue.showtable = false
              const line = [] // series.data中的数据
              const keys = [] // 返回对象的所有key
              const names = [] // legend.data中的数据（图例）
              for (const key in data[0]) {
                keys.push(key)
              }
              let lastKsy = ''
              let showName = ''
              for (let i = 0; i < keys.length; i++) {
                if (keys[i].indexOf('sum(') !== -1 || keys[i].indexOf('count(') !== -1 || keys[i].indexOf('avg(') !== -1) {
                  lastKsy = keys[i]
                } else {
                  showName += keys[i] + ' '
                }
              }
              showName = showName.substring(showName.indexOf('$') + 1)
              for (let i = 0; i < data.length; i++) {
                let name = ''
                let value = ''
                for (const key in data[i]) {
                  if (key.indexOf(lastKsy) < 0) {
                    name += data[i][key] + '  '
                  } else {
                    value = data[i][key]
                  }
                }
                line.push({
                  'name': name.replace('sum(', '').replace('avg(', '').replace('count(', '').replace(')', ''),
                  'value': value
                })
                names.push(name.replace('sum(', '').replace('avg(', '').replace('count(', '').replace(')', ''))
              }
              let pieOption
              // 判断是更新图表，还是新增图标
              if (vue.editOption === -1) {
                pieOption = {
                  title: {
                    text: '',
                    top: '',
                    left: '',
                    textStyle: {}
                  },
                  label: {},
                  backgroundColor: '#fff',
                  tooltip: {
                    trigger: 'item',
                    formatter: '{a} <br/>{b} : {c} ({d}%)'
                  },
                  legend: {
                    type: 'scroll',
                    orient: 'vertical', // 垂直
                    left: 'right',
                    right: '30px',
                    top: '5%',
                    textStyle: {
                      color: '#333',
                      fontFamily: 'sans-serif',
                      fontSize: '12'
                    },
                    data: []
                  },
                  toolbox: {
                    feature: {
                      saveAsImage: {}
                    },
                    bottom: 'auto'
                  },
                  series: [
                    {
                      name: [],
                      type: 'pie',
                      radius: [0, '50%'],
                      center: ['40%', '50%'],
                      data: [],
                      label: {
                        normal: {
                          show: true,
                          position: 'top'
                        }
                      },
                      emphasis: {
                        itemStyle: {
                          shadowBlur: 10,
                          shadowOffsetX: 0,
                          shadowColor: 'rgba(0, 0, 0, 0.5)'
                        }
                      }
                    }
                  ]
                }
              } else {
                pieOption = vue.editOption
              }
              // 给pie图的配置中添加生成的数据
              pieOption.legend.data = names
              pieOption.series[0].name = showName
              pieOption.series[0].data = line
              vue.lineOption = pieOption
              vue.editOption = -1
            } else {
              // 不是表格，也不是pie（line和bar图）
              vue.showtable = false

              let barOrLineOption
              if (vue.editOption === -1) {
                barOrLineOption = {
                  title: {
                    text: '',
                    top: '',
                    left: '',
                    textStyle: {}
                  },
                  backgroundColor: '',
                  tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                      type: 'cross',
                      label: {
                        backgroundColor: '#6a7985'
                      }
                    }
                  },
                  legend: {
                    data: [],
                    orient: 'horizontal',
                    type: 'scroll',
                    textStyle: {
                      color: '#333',
                      fontFamily: 'sans-serif',
                      fontSize: '12'
                    }
                  },
                  toolbox: {
                    feature: {
                      saveAsImage: {}
                    },
                    show: true
                  },
                  grid: {
                    left: '10%',
                    right: '10%',
                    bottom: '20%',
                    top: '8%',
                    backgroundColor: ''
                  },
                  xAxis: [{
                    name: '',
                    data: [],
                    type: 'category',
                    axisLine: {
                      lineStyle: {
                        color: '#333'
                      }
                    },
                    axisTick: {
                      show: true
                    },
                    axisLabel: {
                      interval: 'auto',
                      rotate: 0,
                      margin: 18
                    },
                    splitLine: {
                      show: false
                    }
                  }],
                  yAxis: [
                    {
                      name: '',
                      data: [],
                      type: 'value',
                      axisLine: {
                        lineStyle: {
                          color: '#333'
                        }
                      },
                      axisTick: {
                        show: true
                      },
                      splitLine: {
                        show: true
                      }
                    }
                  ],
                  series: [
                    {
                      name: '',
                      type: '',
                      symbol: 'emptyCircle',
                      data: [],
                      stack: '',
                      lineStyle: {
                        type: 'solid'
                      },
                      label: {
                        normal: {
                          show: false,
                          position: 'top'
                        }
                      }
                    }
                  ]
                }
              } else {
                barOrLineOption = vue.editOption
              }
              if (vue.dimensions.length > 1) {
                // 多个维度
                const options = []
                barOrLineOption.toolbox.show = false
                for (let i = 0; i < data.length; i++) {
                  options.push(vue.handlerMoreLineOrBar(type, data[i], JSON.parse(JSON.stringify(barOrLineOption))))
                }
                vue.chartOptions = options
                if (vue.chartOptions[0]) vue.chart1.setOption(vue.chartOptions[0])
                if (vue.chartOptions[1]) vue.chart2.setOption(vue.chartOptions[1])
                if (vue.chartOptions[2]) vue.chart3.setOption(vue.chartOptions[2])
                if (vue.chartOptions[3]) vue.chart4.setOption(vue.chartOptions[3])
                vue.lineOption = vue.chartOptions[0]
              } else {
                // 单个维度
                vue.lineOption = vue.handlerMoreLineOrBar(type, data, barOrLineOption)
              }
              vue.editOption = -1
            }

            vue.chartType = type
            // 随机生成颜色
            if (type === 'pie') {
              vue.setEchartColor()
              vue.labelShow = true
              vue.legendOrient = 'vertical'
            }
            // 备份
            vue.initOption = JSON.stringify(vue.lineOption)
            vue.myChart.setOption(vue.lineOption)
          } else {
            if (type === 'bar' || type === 'line') {
              // 带color的图表
              vue.showtable = false
              if ((res.data && res.data.length > 0) || (res.data.xdata && res.data.xdata.length > 0)) {
                const datas = res.data
                let barOrLineByColorOption
                if (vue.editOption === -1) {
                  barOrLineByColorOption = {
                    title: {
                      text: '',
                      top: '',
                      left: '',
                      textStyle: {}
                    },
                    backgroundColor: '#fff',
                    tooltip: {
                      trigger: 'axis',
                      axisPointer: {
                        type: 'cross',
                        label: {
                          backgroundColor: ''
                        }
                      }
                    },
                    legend: {
                      data: [],
                      orient: 'horizontal',
                      type: 'scroll',
                      textStyle: {
                        color: '#333',
                        fontFamily: 'sans-serif',
                        fontSize: '12'
                      }
                    },
                    toolbox: {
                      feature: {
                        saveAsImage: {}
                      },
                      show: true
                    },
                    grid: {
                      left: '10%',
                      right: '10%',
                      bottom: '20%',
                      top: '8%',
                      backgroundColor: ''
                    },
                    xAxis: [{
                      name: '',
                      data: [],
                      type: 'category',
                      axisLine: {
                        lineStyle: {
                          color: '#333'
                        }
                      },
                      axisTick: {
                        show: true
                      },
                      axisLabel: {
                        interval: 'auto',
                        rotate: 0,
                        margin: 18
                      },
                      splitLine: {
                        show: false
                      }
                    }],
                    yAxis: [
                      {
                        name: '',
                        type: 'value',
                        data: [],
                        axisLine: {
                          lineStyle: {
                            color: '#333'
                          }
                        },
                        axisTick: {
                          show: true
                        },
                        splitLine: {
                          show: true
                        }
                      }
                    ],
                    series: [
                      {
                        name: '',
                        type: '',
                        symbol: 'emptyCircle',
                        data: [],
                        stack: '',
                        lineStyle: {
                          type: 'solid'
                        },
                        color: '',
                        label: {
                          normal: {
                            show: false,
                            position: 'top'
                          }
                        }
                      }
                    ]
                  }
                } else {
                  barOrLineByColorOption = vue.editOption
                }
                if (vue.dimensions.length > 1) {
                  // 多个维度
                  const options = []
                  barOrLineByColorOption.toolbox.show = false
                  for (let i = 0; i < datas.length; i++) {
                    options.push(vue.handlerColorMoreLineOrBar(type, datas[i], JSON.parse(JSON.stringify(barOrLineByColorOption))))
                  }
                  vue.chartOptions = options
                  if (vue.chartOptions[0]) vue.chart1.setOption(vue.chartOptions[0])
                  if (vue.chartOptions[1]) vue.chart2.setOption(vue.chartOptions[1])
                  if (vue.chartOptions[2]) vue.chart3.setOption(vue.chartOptions[2])
                  if (vue.chartOptions[3]) vue.chart4.setOption(vue.chartOptions[3])
                  vue.lineOption = vue.chartOptions[0]
                } else {
                  // 单个维度
                  vue.lineOption = vue.handlerColorMoreLineOrBar(type, datas, barOrLineByColorOption)
                  vue.initOption = JSON.stringify(vue.lineOption)
                  vue.myChart.setOption(vue.lineOption)
                }
                vue.chartType = type
                vue.editOption = -1
                vue.saveButton = false
              } else {
                vue.$alert(vue.$t('soi.common.noData'), vue.$t('soi.common.success'))
                if (type === 'table') {
                  vue.chartData = []
                  vue.chartParameters = []
                }
                vue.saveButton = true
              }
            } else if (type === 'heatmap') {
              if (res.data && res.data.xdata && res.data.xdata.length > 0) {
                const data = res.data
                const xStatisticsData = data.xdata
                const yStatisticsData = []
                const allData = []
                data.ydata.forEach(item => {
                  yStatisticsData.push(item.colorName)
                })
                const statisticsData = []
                for (let i = 0; i < data.ydata.length; i++) {
                  for (let j = 0; j < data.ydata[i].metric.length; j++) {
                    statisticsData.push([j, i, data.ydata[i].metric[j]])
                    allData.push(data.ydata[i].metric[j])
                  }
                }
                allData.sort(function(a, b) {
                  return a - b
                })
                const min = allData[0]
                const max = allData[allData.length - 1]

                let heatmapByColorOption
                if (vue.editOption === -1) {
                  heatmapByColorOption = {
                    title: {
                      text: '',
                      top: '',
                      left: '',
                      textStyle: {}
                    },
                    tooltip: {
                      position: 'top'
                    },
                    backgroundColor: '#fff',
                    animation: false,
                    toolbox: {
                      feature: {
                        saveAsImage: {}
                      }
                    },
                    grid: {
                      left: '10%',
                      right: '10%',
                      top: '8%',
                      bottom: '20%',
                      backgroundColor: '#000000'
                    },
                    xAxis: {
                      type: 'category',
                      data: [],
                      splitArea: {
                        show: true
                      }
                    },
                    yAxis: {
                      type: 'category',
                      data: [],
                      splitArea: {
                        show: true
                      }
                    },
                    visualMap: {
                      min: 0,
                      max: 0,
                      calculable: true,
                      orient: 'horizontal',
                      left: '40%',
                      bottom: '5%',
                      inRange: {
                        color: ['#C7DBFF', '#5291FF']
                      }
                    },
                    series: [{
                      name: '',
                      type: 'heatmap',
                      data: [],
                      label: {
                        show: true
                      },
                      emphasis: {
                        itemStyle: {
                          shadowBlur: 10,
                          shadowColor: 'rgba(0, 0, 0, 0.5)'
                        }
                      }
                    }]
                  }
                } else {
                  heatmapByColorOption = vue.editOption
                }
                heatmapByColorOption.series[0].name = `${vue.measures[0].columnName}(${vue.resultFunction})`
                heatmapByColorOption.xAxis.data = xStatisticsData
                heatmapByColorOption.yAxis.data = yStatisticsData
                heatmapByColorOption.series[0].data = statisticsData
                heatmapByColorOption.visualMap.min = min
                heatmapByColorOption.visualMap.max = max
                vue.lineOption = heatmapByColorOption
                vue.initOption = JSON.stringify(vue.lineOption)
                vue.chartType = type
                vue.myChart.setOption(vue.lineOption)
                vue.editOption = -1
                vue.saveButton = false
              } else {
                vue.$alert(vue.$t('soi.common.noData'), vue.$t('soi.common.success'))
                if (type === 'table') {
                  vue.chartData = []
                  vue.chartParameters = []
                }
                vue.saveButton = true
              }
            }
          }
        })
        .catch(() => {
          if (type === 'table') {
            vue.chartData = []
            vue.chartParameters = []
          }
          vue.saveButton = true
        })
        .finally(() => {
          vue.loading = false
        })
    },
    handlerColorMoreLineOrBar(type, data, barOrLineByColorOption) {
      const vue = this
      const keys = []
      const xdata = []
      const seriesName = []
      const seriesData = []
      for (let i = 0; i < data.ydata.length; i++) {
        keys.push(data.ydata[i].colorName)
        seriesName.push(data.ydata[i].colorName)
        seriesData.push(data.ydata[i].metric)
      }
      for (let i = 0; i < data.xdata.length; i++) {
        xdata.push(data.xdata[i])
      }
      barOrLineByColorOption.legend.data = keys
      if (type === 'bar' && vue.barDirection === 'horizontal' && vue.editOption !== -1) {
        barOrLineByColorOption.yAxis[0].data = xdata
      } else {
        barOrLineByColorOption.xAxis[0].data = xdata
      }
      for (let i = 0; i < seriesName.length; i++) {
        const series0 = {
          name: barOrLineByColorOption.series[0].name,
          type: barOrLineByColorOption.series[0].type,
          symbol: barOrLineByColorOption.series[0].symbol,
          data: barOrLineByColorOption.series[0].data,
          stack: barOrLineByColorOption.series[0].stack,
          lineStyle: barOrLineByColorOption.series[0].lineStyle,
          label: barOrLineByColorOption.series[0].label,
          color: barOrLineByColorOption.series[0].color
        }
        barOrLineByColorOption.series.push(series0)
        barOrLineByColorOption.series[i + 1].name = seriesName[i]
        barOrLineByColorOption.series[i + 1].type = type
        barOrLineByColorOption.series[i + 1].data = seriesData[i]
      }
      barOrLineByColorOption.series.splice(0, 1)
      // 自定义颜色
      for (let i = 0; i < barOrLineByColorOption.series.length; i++) {
        for (let j = 0; j < vue.colorCategory.length; j++) {
          if (barOrLineByColorOption.series[i].name === vue.colorCategory[j].name) {
            barOrLineByColorOption.series[i].color = vue.colorCategory[j].color
          }
        }
      }
      return barOrLineByColorOption
    },
    handlerMoreLineOrBar(type, data, barOrLineOption) {
      const vue = this
      let line = []
      let xline = []
      const keys = []
      let xname = ''
      const seriesData = []
      for (let key in data[0]) {
        key = key.substring(key.indexOf('$') + 1)
        keys.push(key.replace('sum(', '').replace('avg(', '').replace('count(', '').replace(')', '') + ' ')
      }
      for (let w = 0; w < keys.length; w++) {
        for (let i = 0; i < data.length; i++) {
          const temp = Object.values(data[i])
          line.push(temp[w])
        }
        if (w === 0) {
          xname = keys[w]
          xline = line
        } else {
          seriesData.push({
            seriesName: (keys[w]),
            lines: (line)
          })
        }
        line = []
      }
      barOrLineOption.xAxis.name = xname
      if (type === 'bar' && vue.barDirection === 'horizontal' && vue.editOption !== -1) {
        barOrLineOption.yAxis[0].data = xline
      } else {
        barOrLineOption.xAxis[0].data = xline
      }

      barOrLineOption.legend.data = keys.slice(1, keys.length)

      for (let i = 0; i < seriesData.length; i++) {
        const series0 = {
          name: barOrLineOption.series[0].name,
          type: barOrLineOption.series[0].type,
          symbol: barOrLineOption.series[0].symbol,
          data: barOrLineOption.series[0].data,
          stack: barOrLineOption.series[0].stack,
          lineStyle: barOrLineOption.series[0].lineStyle,
          label: barOrLineOption.series[0].label
        }
        barOrLineOption.series.push(series0)
        barOrLineOption.series[i + 1].name = seriesData[i].seriesName
        barOrLineOption.series[i + 1].type = type
        barOrLineOption.series[i + 1].data = seriesData[i].lines
      }
      barOrLineOption.series.splice(0, 1)
      for (let i = 0; i < barOrLineOption.series.length; i++) {
        for (let j = 0; j < vue.colorCategory.length; j++) {
          if (barOrLineOption.series[i].name === vue.colorCategory[j].name) {
            barOrLineOption.series[i].color = vue.colorCategory[j].color
          }
        }
      }
      return barOrLineOption
    },
    showFilter() {
      this.filterDialogVisible = true
    },
    getGroupsData() {
      this.groups = []
      if (!(this.dimensions.length === 1 && this.dimensions[0].id === -1)) {
        for (let i = 0; i < this.dimensions.length; i++) {
          if (this.isSingleTable) {
            this.groups.push({
              'children': [],
              'column': `${this.dimensions[i].columnName}`
            })
          } else {
            this.groups.push({
              'children': [],
              'column': `${this.dimensions[i].databaseName}$${this.dimensions[i].columnName}(${this.dimensions[i].tableName})`
            })
          }
        }
      }
    },
    transformSvg(nodeId) {
      return new Promise(resolve => {
        Html2Canvas(document.querySelector(`#${nodeId}`)).then(canvas => {
          let imgUrl = null
          imgUrl = canvas.toDataURL()
          resolve(imgUrl)
        })
      })
    },
    getColorData() {
      this.selectColors = []
      for (let i = 0; i < this.colors.length; i++) {
        this.selectColors.push({
          'children': [],
          'column': this.colors[i]
        })
      }
    },
    getAggregatorsData() {
      this.aggregators = []
      if (this.isSingleTable) {
        //  單表
        if (!(this.measures.length === 1 && this.measures[0].id === -1)) {
          for (let i = 0; i < this.measures.length; i++) {
            this.aggregators.push({
              'column': `${this.measures[i].columnName}`,
              'func': this.resultFunction
            })
          }
        }
      } else {
        // 多表
        if (!(this.measures.length === 1 && this.measures[0].id === -1)) {
          for (let i = 0; i < this.measures.length; i++) {
            this.aggregators.push({
              'column': `${this.measures[i].databaseName}$${this.measures[i].columnName}(${this.measures[i].tableName})`,
              'func': this.resultFunction
            })
          }
        }
      }
    },
    async getModelList() {
      const vue = this
      vue.loading = true
      const requestData = {
        page: 0,
        pageSize: 0,
        userId: this.userDetails.id
      }
      const response = await getDataTableList(requestData)
      vue.list = response.data.items
      vue.DataSourceList = response.data.items
      if (response.data.length <= 0) {
        vue.$message.success(vue.$t('soi.common.noData'))
      } else {
        // 获取数据源
        const tempTreeDataTable = []
        const treeDataTable = []
        for (let i = 0; i < vue.list.length; i++) {
          if (tempTreeDataTable.indexOf(vue.list[i].source.name) === -1) {
            tempTreeDataTable.push(vue.list[i].source.name)
          }
          treeDataTable.push({
            label: vue.list[i].view.name,
            value: vue.list[i].view.id,
            parent: vue.list[i].source.name
          })
        }

        for (let i = 0; i < tempTreeDataTable.length; i++) {
          treeDataTable.push(
            {
              label: tempTreeDataTable[i],
              value: tempTreeDataTable[i],
              parent: '',
              children: []
            }
          )
        }
        vue.list = vue.toTree(treeDataTable)
        if (vue.createOrEdit === 'create') {
          if (vue.dataTableName) {
            for (let i = 0; i < response.data.items.length; i++) {
              if (vue.dataTableName === response.data.items[i].view.id) {
                vue.value = [response.data.items[i].source.name, response.data.items[i].view.id]
                vue.viewId = vue.value[1]
                vue.getColumns()
              }
            }
          } else {
            vue.value = [response.data.items[0].source.name, response.data.items[0].view.id]
            vue.viewId = vue.value[1]
            vue.getColumns()
          }
        }
      }
    },
    toTree(data) {
      const map = {}
      data.forEach(function(item) {
        map[item.value] = item
      })
      const val = []
      data.forEach(function(item) {
        const parent = map[item.parent]
        if (parent) {
          parent.children.push(item)
        } else {
          val.push(item)
        }
      })
      return val
    },
    labelHead: function(h, { column, $index }) {
      const label = column.label
      let l
      const f = 9
      if (label.indexOf('$') !== -1) {
        const labelArr = label.split('$')
        l = labelArr[0].length > labelArr[1].length ? labelArr[0].length : labelArr[1].length
        if (l < 10) {
          column.minWidth = 100
        } else {
          column.minWidth = f * (2 + l)// 加上一个文字长度
        }
        return h(
          'div', // 创建最外层的标签可随意
          [
            h('span', { // 创建第一个元素的标签可随意
              attrs: { type: 'text' }
            }, [labelArr[0]]),
            h('p', { // 创建第二个元素的标签可随意
              attrs: { type: 'text' }, // 给分割的某个元素单独加样式
              class: 'table-head header-cell',
              style: { width: '100%', color: '#707070' }
            }, [labelArr[1] || ''])
          ]
        )
      } else {
        l = column.label.length
        if (l < 10) {
          column.minWidth = 100
        } else {
          column.minWidth = f * (2 + l)// 加上一个文字长度
        }
        return h('div',
          {
            class: 'table-head header-cell',
            style: { width: '100%' }
          },
          [column.label])
      }
    },
    getColumns() {
      const vue = this
      vue.loading = true

      getColumns(vue.viewId, vue.userDetails.id)
        .then((response) => {
          if (vue.createOrEdit === 'create' || vue.closeLoading) {
            vue.loading = false
          }
          vue.closeLoading = true
          if (vue.createOrEdit === 'create' || vue.editGetColumns) {
            vue.dimensions = [{ id: -1, columnName: '' }]
            vue.measures = [{ id: -1, columnName: '' }]
            vue.defaultData()
            vue.myChart.clear()
            vue.chart1.clear()
            vue.chart2.clear()
            vue.chart3.clear()
            vue.chart4.clear()
            vue.resultFunction = 'sum'
            vue.tableData = []
            vue.chartParameters = []
            vue.arr2 = [{ id: -1, databaseName: '', tableName: '', columnName: '', type: '' }]
          }
          vue.dataModel = response.data
          vue.fieldInfoList = JSON.parse(vue.dataModel.view.model)
          vue.isSingleTable = false
          // 判断是不是单表
          if (vue.fieldInfoList.length === 1) {
            if (vue.fieldInfoList[0].tableInfoList.length === 1) {
              vue.isSingleTable = true
            }
          }
          const tempFieldInfoList = []
          // 获取字段列表
          for (let i = 0; i < vue.fieldInfoList.length; i++) {
            tempFieldInfoList.push({ databaseName: vue.fieldInfoList[i].db, type: 'DATABASE', filters: [] })
            for (let j = 0; j < vue.fieldInfoList[i].tableInfoList.length; j++) {
              tempFieldInfoList.push({
                tableName: vue.fieldInfoList[i].tableInfoList[j].tableName,
                type: 'TABLE',
                filters: []
              })
              for (let x = 0; x < vue.fieldInfoList[i].tableInfoList[j].column.length; x++) {
                vue.fieldInfoList[i].tableInfoList[j].column[x]['databaseName'] = vue.fieldInfoList[i].db
                vue.fieldInfoList[i].tableInfoList[j].column[x]['tableName'] = vue.fieldInfoList[i].tableInfoList[j].tableName
                vue.fieldInfoList[i].tableInfoList[j].column[x]['filters'] = []
                vue.fieldInfoList[i].tableInfoList[j].column[x]['name'] = vue.fieldInfoList[i].tableInfoList[j].column[x].columnName
                tempFieldInfoList.push(vue.fieldInfoList[i].tableInfoList[j].column[x])
              }
            }
          }
          vue.fieldInfoList = tempFieldInfoList
          vue.columns = tempFieldInfoList
          if (vue.createOrEdit === 'edit') {
            for (let i = 0; i < vue.columns.length; i++) {
              for (let j = 0; j < vue.tempcolumns.length; j++) {
                if (vue.columns[i].name === vue.tempcolumns[j].name) {
                  vue.columns[i].filters = vue.tempcolumns[j].filters
                }
              }
            }
            vue.saveButton = true
            vue.okFilterDialogVisible()
            vue.editGetColumns = true
          }
        })
        .finally(() => {
          vue.loading = false
        })
    },
    closeFilterDialogVisible() {
      for (let i = 0; i < this.columns.length; i++) {
        this.columns[i].filters = []
      }
      this.addOk = true
      this.filterMatter = ''
      this.filterCondition = '>'
      this.dataShow = false
      this.dataShow = true
    },
    okFilterDialogVisible() {
      if (!this.addOk) {
        this.$message({ message: 'Filter incomplete', type: 'warning' })
        return
      }
      this.filters = []
      // 生成过滤条件
      if (this.isSingleTable) {
        for (let i = 0; i < this.columns.length; i++) {
          if (this.columns[i].filters.length > 0) {
            let tempFilter = ''
            if (this.MYSQLNoDateTypeList.indexOf(this.columns[i].type) !== -1) {
              for (let j = 0; j < this.columns[i].filters.length; j++) {
                if (j === 0) {
                  if (this.columns[i].filters[j].condition === 'IS NULL' || this.columns[i].filters[j].condition === 'IS NOT NULL') {
                    tempFilter += '`' + this.columns[i].name + '`' + ' ' + this.columns[i].filters[j].condition + ' '
                  } else {
                    tempFilter += '`' + this.columns[i].name + '`' + ' ' + this.columns[i].filters[j].condition + " '" + this.columns[i].filters[j].matter + "' "
                  }
                } else {
                  if (this.columns[i].filters[j].condition === 'IS NULL' || this.columns[i].filters[j].condition === 'IS NOT NULL') {
                    tempFilter += this.columns[i].filters[j].andOr + ' ' + '`' + this.columns[i].name + '`' + ' ' + this.columns[i].filters[j].condition + ' '
                  } else {
                    tempFilter += this.columns[i].filters[j].andOr + ' ' + '`' + this.columns[i].name + '`' + ' ' + this.columns[i].filters[j].condition + " '" + this.columns[i].filters[j].matter + "' "
                  }
                }
              }
              this.filters.push(
                {
                  'column': this.columns[i].name,
                  'condition': tempFilter
                }
              )
            } else {
              for (let j = 0; j < this.columns[i].filters.length; j++) {
                if (j === 0) {
                  tempFilter += this.columns[i].filters[j].date + ' '
                } else {
                  tempFilter += this.columns[i].filters[j].andOr + ' ' + this.columns[i].filters[j].date + ' '
                }
              }
              this.filters.push(
                {
                  'column': this.columns[i].name,
                  'condition': tempFilter
                }
              )
            }
          }
        }
      } else {
        for (let i = 0; i < this.columns.length; i++) {
          if (this.columns[i].filters.length > 0) {
            let tempFilter = ''
            if (this.MYSQLNoDateTypeList.indexOf(this.columns[i].type) !== -1) {
              for (let j = 0; j < this.columns[i].filters.length; j++) {
                if (this.columns[i].filters[j].matter.length > 0) {
                  if (j === 0) {
                    if (this.columns[i].filters[j].condition === 'IS NULL' || this.columns[i].filters[j].condition === 'IS NOT NULL') {
                      tempFilter += '`' + this.columns[i].databaseName + '$' + this.columns[i].name + '(' + this.columns[i].tableName + ')' + '`' + ' ' + this.columns[i].filters[j].condition + ' '
                    } else {
                      tempFilter += '`' + this.columns[i].databaseName + '$' + this.columns[i].name + '(' + this.columns[i].tableName + ')' + '`' + ' ' + this.columns[i].filters[j].condition + " '" + this.columns[i].filters[j].matter + "' "
                    }
                  } else {
                    if (this.columns[i].filters[j].condition === 'IS NULL' || this.columns[i].filters[j].condition === 'IS NOT NULL') {
                      tempFilter += this.columns[i].filters[j].andOr + ' ' + '`' + this.columns[i].databaseName + '$' + this.columns[i].name + '(' + this.columns[i].tableName + ')' + '`' + ' ' + this.columns[i].filters[j].condition + ' '
                    } else {
                      tempFilter += this.columns[i].filters[j].andOr + ' ' + '`' + this.columns[i].databaseName + '$' + this.columns[i].name + '(' + this.columns[i].tableName + ')' + '`' + ' ' + this.columns[i].filters[j].condition + " '" + this.columns[i].filters[j].matter + "' "
                    }
                  }
                }
              }
              if (tempFilter.length > 0) {
                this.filters.push(
                  {
                    'column': this.columns[i].databaseName + '$' + this.columns[i].name + '(' + this.columns[i].tableName + ')',
                    'condition': tempFilter
                  }
                )
              }
            } else {
              for (let j = 0; j < this.columns[i].filters.length; j++) {
                if (this.columns[i].filters[j].date.length > 0) {
                  if (j === 0) {
                    tempFilter += this.columns[i].filters[j].date + ' '
                  } else {
                    tempFilter += this.columns[i].filters[j].andOr + ' ' + this.columns[i].filters[j].date + ' '
                  }
                }
              }
              if (tempFilter.length > 0) {
                const reg = new RegExp(this.columns[i].name, 'g')
                tempFilter = tempFilter.replace(reg, this.columns[i].databaseName + '$' + this.columns[i].name + '(' + this.columns[i].tableName + ')')
                this.filters.push(
                  {
                    'column': this.columns[i].databaseName + '$' + this.columns[i].name + '(' + this.columns[i].tableName + ')',
                    'condition': tempFilter
                  }
                )
              }
            }
          }
        }
      }
      if (this.chartType.length > 0) {
        // this.switchCharts(this.chartType)
      }
      this.filterDialogVisible = false
    },
    getConfig() {
      let config = {}
      const tempChartsItemColor = []

      if (this.chartType === 'bar' || this.chartType === 'line') {
        for (let i = 0; i < this.lineOption.series.length; i++) {
          tempChartsItemColor.push({ name: this.lineOption.series[i].name, color: this.lineOption.series[i].color })
        }
      }
      this.colorCategory = tempChartsItemColor
      if (this.chartType === 'table') {
        // this.lineOption = {}
      } else if (this.chartType === 'pie') {
        // 删除pie数据部分
        this.lineOption.legend.data = []
        for (let i = 0; i < this.lineOption.series.length; i++) {
          this.lineOption.series[i].name = ''
          this.lineOption.series[i].data = []
        }
      } else if (this.chartType === 'heatmap') {
        this.lineOption.xAxis.data = []
        this.lineOption.yAxis.data = []
        this.lineOption.series[0].data = []
      } else {
        // 删除bar和line数据部分
        this.lineOption.legend.data = []
        this.lineOption.xAxis.name = ''
        this.lineOption.xAxis.data = []
        for (let i = 0; i < this.lineOption.series.length; i++) {
          this.lineOption.series[i].name = ''
          this.lineOption.series[i].data = []
          this.lineOption.series[i].type = ''
        }
        this.lineOption.series = [this.lineOption.series[0]]
        if (this.chartType === 'bar' && this.barDirection === 'horizontal') {
          this.lineOption.yAxis.data = []
        }
      }
      const tempColumns = []
      for (let i = 0; i < this.columns.length; i++) {
        if (this.columns[i].filters.length > 0) {
          tempColumns.push(this.columns[i])
        }
      }
      config = {
        chartsItemColor: this.chartsItemColor,
        isSingleTable: this.isSingleTable,
        option: this.lineOption,
        aggregation: this.resultFunction,
        dimensions: this.dimensions,
        measures: this.measures,
        requestChartsData: this.requestChartsData,
        orderList: this.orderList,
        columns: tempColumns,
        showtable: this.showtable,
        chartType: this.chartType,
        bucketings: this.bucketings,
        // color
        arr2: this.arr2,
        colorCategory: this.colorCategory,
        style: {
          chartType: this.chartType,
          chartsType: this.chartsType,
          chartsTitle: this.chartsTitle,
          titleTop: this.titleTop,
          titleLeft: this.titleLeft,
          titleColor: this.titleColor,
          titleFont: this.titleFont,
          titleSize: this.titleSize,
          backgroundColor: this.backgroundColor,
          lineColor: '',
          labelShow: this.labelShow,
          labelPosition: this.labelPosition,
          labelFont: this.labelFont,
          labelFontSize: this.labelFontSize,
          labelFontColor: this.labelFontColor,
          symbolName: this.symbolName,
          lineStyleType: this.lineStyleType,
          legendShow: this.legendShow,
          legendTop: this.legendTop,
          legendLeft: this.legendLeft,
          legendFont: this.legendFont,
          legendFontSize: this.legendFontSize,
          legendFontColor: this.legendFontColor,
          XAxisShow: this.XAxisShow,
          YAxisShow: this.YAxisShow,
          XAxisColor: this.XAxisColor,
          YAxisColor: this.YAxisColor,
          legendOrient: this.legendOrient,
          dataZoomShow: this.dataZoomShow,
          dataZoomFontColor: this.dataZoomFontColor,
          pieType: this.pieType,
          chartStack: this.chartStack,
          barDirection: this.barDirection,
          ShowLabelsOnXaxis: this.ShowLabelsOnXaxis,
          Rotate: this.Rotate,
          SplitLine: this.SplitLine,
          startColor: this.startColor,
          endColor: this.endColor,
          chartLeft: this.chartLeft,
          chartRight: this.chartRight,
          chartTop: this.chartTop,
          chartBottom: this.chartBottom,
          heatmapOrient: this.heatmapOrient,
          heatmapLeft: this.heatmapLeft,
          heatmapBottom: this.heatmapBottom
        }
      }
      return config
    },
    saveChart() {
      const vue = this
      if (vue.lineOption !== {}) {
        this.$prompt(vue.$t('soi.dataVisualization.chartNameTip'), vue.$t('soi.common.tip'), {
          confirmButtonText: vue.$t('soi.common.confirm'),
          cancelButtonText: vue.$t('soi.common.cancel'),
          inputPattern: /^[a-zA-Z0-9_\-]{1,100}$/,
          inputValue: '',
          inputErrorMessage: vue.$t('soi.dataVisualization.chartNameError')
        }).then(({ value }) => {
          vue.loading = true
          const requester = {
            'config': JSON.stringify(vue.getConfig()),
            'viewId': vue.viewId,
            'name': value,
            'userId': vue.userDetails.id,
            'viewParam': JSON.stringify(vue.requestChartsData)
          }

          saveChart(requester)
            .then(() => {
              vue.saveButton = true

              vue.$message({
                type: 'success',
                message: vue.$t('soi.common.success')
              })
            })
            .finally(() => {
              vue.loading = false
            })
        })
      }
    },
    handleChange(value) {
      this.viewId = value[1]
      this.getColumns()
    },
    editChart() {
      const vue = this
      if (vue.lineOption !== {}) {
        this.$prompt(vue.$t('soi.dataVisualization.chartNameTip'), vue.$t('soi.common.tip'), {
          confirmButtonText: vue.$t('soi.common.confirm'),
          cancelButtonText: vue.$t('soi.common.cancel'),
          inputPattern: /^[a-zA-Z0-9_\-]{1,100}$/,
          inputValue: vue.chartName,
          inputErrorMessage: vue.$t('soi.dataVisualization.chartNameError')
        }).then(({ value }) => {
          vue.loading = true
          const requester = {
            'config': JSON.stringify(vue.getConfig()),
            'name': value,
            'viewParam': JSON.stringify(vue.requestChartsData),
            'viewId': vue.viewId
          }

          updateChart(vue.chartId, vue.userDetails.id, requester)
            .then(() => {
              vue.$message({
                type: 'success',
                message: vue.$t('soi.common.success')
              })
            })
            .finally(() => {
              vue.loading = false
            })
        })
      }
    },
    formatMath() {
      if (this.MYSQLNumberTypeList.indexOf(this.filterType) !== -1) {
        this.filterMatter = this.filterMatter.replace(/[^\d.]/g, '') // 清除"数字"和"."以外的字符
        this.filterMatter = this.filterMatter.replace(/^\./g, '') // 验证第一个字符是数字而不是字符
        this.filterMatter = this.filterMatter.replace(/\.{2,}/g, '.') // 只保留第一个.清除多余的
        this.filterMatter = this.filterMatter.replace('.', '$#$').replace(/\./g, '').replace('$#$', '.')
        this.filterMatter = this.filterMatter.replace(/^(\-)*(\d+)\.(\d\d).*$/, '$1$2.$3') // 只能输入两个小数
      }
    },
    // 修改标题
    changeChartsTitle() {
      if (this.isMultipleCharts()) {
        this.chartOptions[0].title.text = this.chartsTitle
        this.createMultipleCharts()
      } else {
        this.lineOption.title.text = this.chartsTitle
        this.createCharts()
      }
    },
    // 修改标题位置
    changeTitleTop() {
      if (this.isMultipleCharts()) {
        this.chartOptions[0].title.top = this.titleTop + '%'
        this.createMultipleCharts()
      } else {
        this.lineOption.title.top = this.titleTop + '%'
        this.createCharts()
      }
    },
    changeTitleLeft() {
      if (this.isMultipleCharts()) {
        this.chartOptions[0].title.left = this.titleLeft + '%'
        this.createMultipleCharts()
      } else {
        this.lineOption.title.left = this.titleLeft + '%'
        this.createCharts()
      }
    },
    // 标题字体
    changeTitleFont() {
      if (this.isMultipleCharts()) {
        this.chartOptions[0].title.textStyle['fontFamily'] = this.titleFont
        this.createMultipleCharts()
      } else {
        this.lineOption.title.textStyle['fontFamily'] = this.titleFont
        this.createCharts()
      }
    },
    // 标题大小
    changeTitleSize() {
      if (this.isMultipleCharts()) {
        this.chartOptions[0].title.textStyle['fontSize'] = this.titleSize
        this.createMultipleCharts()
      } else {
        this.lineOption.title.textStyle['fontSize'] = this.titleSize
        this.createCharts()
      }
    },
    // 标题颜色
    changeTitleColor() {
      if (this.isMultipleCharts()) {
        this.chartOptions[0].title.textStyle['color'] = this.titleColor
        this.createMultipleCharts()
      } else {
        this.lineOption.title.textStyle['color'] = this.titleColor
        this.createCharts()
      }
    },

    changeStartColor() {
      this.lineOption.visualMap.inRange.color[0] = this.startColor
      this.createCharts()
    },

    changeEndColor() {
      this.lineOption.visualMap.inRange.color[1] = this.endColor
      this.createCharts()
    },
    changeHeatmapDirection() {
      this.lineOption.visualMap.orient = this.heatmapOrient
      this.createCharts()
    },
    changHeatmapLeft() {
      this.lineOption.visualMap.left = this.heatmapLeft + '%'
      this.createCharts()
    },
    changHeatmapBottom() {
      this.lineOption.visualMap.bottom = this.heatmapBottom + '%'
      this.createCharts()
    },
    changeChartLeft() {
      if (this.isMultipleCharts()) {
        for (const item of this.chartOptions) {
          item.grid.left = this.chartLeft + '%'
        }
        this.createMultipleCharts()
      } else {
        this.lineOption.grid.left = this.chartLeft + '%'
        this.createCharts()
      }
    },
    changeChartRight() {
      if (this.isMultipleCharts()) {
        for (const item of this.chartOptions) {
          item.grid.right = this.chartRight + '%'
        }
        this.createMultipleCharts()
      } else {
        this.lineOption.grid.right = this.chartRight + '%'
        this.createCharts()
      }
    },
    changeChartTop() {
      if (this.isMultipleCharts()) {
        for (const item of this.chartOptions) {
          item.grid.top = this.chartTop + '%'
        }
        this.createMultipleCharts()
      } else {
        this.lineOption.grid.top = this.chartTop + '%'
        this.createCharts()
      }
    },
    changeChartBottom() {
      if (this.isMultipleCharts()) {
        for (const item of this.chartOptions) {
          item.grid.bottom = this.chartBottom + '%'
        }
        this.createMultipleCharts()
      } else {
        this.lineOption.grid.bottom = this.chartBottom + '%'
        this.createCharts()
      }
    },
    // 是否为多个图表
    isMultipleCharts() {
      return this.chartOptions.length > 1 && (this.chartType === 'line' || this.chartType === 'bar')
    },
    // 修改图标样式
    createMultipleCharts() {
      if (this.chart1 && this.chartOptions.length >= 1) {
        this.chart1.clear()
        this.chart1.setOption(this.chartOptions[0])
      }
      if (this.chart2 && this.chartOptions.length >= 2) {
        this.chart2.clear()
        this.chart2.setOption(this.chartOptions[1])
      }
      if (this.chart3 && this.chartOptions.length >= 3) {
        this.chart3.clear()
        this.chart3.setOption(this.chartOptions[2])
      }
      if (this.chart4 && this.chartOptions.length >= 4) {
        this.chart4.clear()
        this.chart4.setOption(this.chartOptions[3])
      }
    },
    // 修改背景颜色
    changeBackgroundColor() {
      if (this.isMultipleCharts()) {
        for (const item of this.chartOptions) {
          item.backgroundColor = this.backgroundColor
        }
        this.createMultipleCharts()
      } else {
        this.lineOption.backgroundColor = this.backgroundColor
        this.createCharts()
      }
    },
    // 修改折线图的样式
    changeChartsType() {
      if (this.isMultipleCharts()) {
        if (this.chartsType === '1') {
          for (const item of this.chartOptions) {
            for (let i = 0; i < item.series.length; i++) {
              delete item.series[i]['smooth']
              delete item.series[i]['areaStyle']
            }
          }
          this.createMultipleCharts()
        }
        if (this.chartsType === '2') {
          for (const item of this.chartOptions) {
            for (let i = 0; i < item.series.length; i++) {
              delete item.series[i]['smooth']
              item.series[i]['areaStyle'] = {}
            }
          }
          this.createMultipleCharts()
        }
        if (this.chartsType === '3') {
          for (const item of this.chartOptions) {
            for (let i = 0; i < item.series.length; i++) {
              delete item.series[i]['areaStyle']
              item.series[i]['smooth'] = true
            }
          }
          this.createMultipleCharts()
        }
      } else {
        if (this.chartsType === '1') {
          for (let i = 0; i < this.lineOption.series.length; i++) {
            delete this.lineOption.series[i]['smooth']
            delete this.lineOption.series[i]['areaStyle']
          }
          this.createCharts()
        }
        if (this.chartsType === '2') {
          for (let i = 0; i < this.lineOption.series.length; i++) {
            delete this.lineOption.series[i]['smooth']
            this.lineOption.series[i]['areaStyle'] = {}
          }
          this.createCharts()
        }
        if (this.chartsType === '3') {
          for (let i = 0; i < this.lineOption.series.length; i++) {
            delete this.lineOption.series[i]['areaStyle']
            this.lineOption.series[i]['smooth'] = true
          }
          this.createCharts()
        }
      }
    },
    // 折现拐角图形
    changeSymbol() {
      if (this.isMultipleCharts()) {
        for (const item of this.chartOptions) {
          for (let i = 0; i < item.series.length; i++) {
            item.series[i].symbol = this.symbolName
          }
        }
        this.createMultipleCharts()
      } else {
        for (let i = 0; i < this.lineOption.series.length; i++) {
          this.lineOption.series[i].symbol = this.symbolName
        }
        this.createCharts()
      }
    },
    // 折线类型
    changeLineStyleType() {
      if (this.isMultipleCharts()) {
        for (const item of this.chartOptions) {
          for (let i = 0; i < item.series.length; i++) {
            item.series[i].lineStyle.type = this.lineStyleType
          }
        }
        this.createMultipleCharts()
      } else {
        for (let i = 0; i < this.lineOption.series.length; i++) {
          this.lineOption.series[i].lineStyle.type = this.lineStyleType
        }
        this.createCharts()
      }
    },
    // 修改折线图的线条颜色
    changeLineChartColor() {
      if (this.isMultipleCharts()) {
        if (this.lineName !== '' || this.lineName !== null) {
          for (const item of this.chartOptions) {
            for (let i = 0; i < item.series.length; i++) {
              if (this.lineName === item.series[i].name) {
                item.series[i]['color'] = this.lineColor
              }
            }
          }
          this.createMultipleCharts()
        }
      } else {
        if (this.lineName !== '' || this.lineName !== null) {
          for (let i = 0; i < this.lineOption.series.length; i++) {
            if (this.lineName === this.lineOption.series[i].name) {
              this.lineOption.series[i]['color'] = this.lineColor
            }
          }
          this.createCharts()
        }
      }
    },
    // 修改饼图的颜色
    changePieChartColor() {
      if (this.pieName !== '' || this.pieName !== null) {
        for (let i = 0; i < this.lineOption.series[0].data.length; i++) {
          if (this.pieName === this.lineOption.series[0].data[i].name) {
            this.lineOption.series[0].data[i]['itemStyle'] = { color: this.pieColor }
          }
        }
        this.createCharts()
      }
    },
    // 是否显示 Data Zoom
    changeDataZoomShow() {
      if (this.isMultipleCharts()) {
        for (const item of this.chartOptions) {
          if (this.dataZoomShow) {
            item['dataZoom'] = this.dataZoom
          } else {
            delete item['dataZoom']
          }
        }
        this.createMultipleCharts()
      } else {
        if (this.dataZoomShow) {
          this.lineOption['dataZoom'] = this.dataZoom
        } else {
          delete this.lineOption['dataZoom']
        }
        this.createCharts()
      }
    },
    // Data Zoom Font Color
    changeDataZoomFontColor() {
      if (this.isMultipleCharts()) {
        for (const item of this.chartOptions) {
          for (let i = 0; i < item.dataZoom.length; i++) {
            item.dataZoom[i].textStyle.color = this.dataZoomFontColor
          }
        }
        this.createMultipleCharts()
      } else {
        for (let i = 0; i < this.lineOption.dataZoom.length; i++) {
          this.lineOption.dataZoom[i].textStyle.color = this.dataZoomFontColor
        }
        this.createCharts()
      }
    },
    // 是否显示 X-Axis
    changeXAxisShow() {
      if (this.isMultipleCharts()) {
        for (const item of this.chartOptions) {
          if (this.XAxisShow) {
            for (let i = 0; i < item.xAxis.length; i++) {
              item.xAxis[i].axisLine['show'] = true
              item.xAxis[i].axisTick.show = true
            }
          } else {
            for (let i = 0; i < item.xAxis.length; i++) {
              item.xAxis[i].axisLine['show'] = false
              item.xAxis[i].axisTick.show = false
            }
          }
        }
        this.createMultipleCharts()
      } else {
        if (this.XAxisShow) {
          for (let i = 0; i < this.lineOption.xAxis.length; i++) {
            this.lineOption.xAxis[i].axisLine['show'] = true
            this.lineOption.xAxis[i].axisTick.show = true
          }
        } else {
          for (let i = 0; i < this.lineOption.xAxis.length; i++) {
            this.lineOption.xAxis[i].axisLine['show'] = false
            this.lineOption.xAxis[i].axisTick.show = false
          }
        }
        this.createCharts()
      }
    },
    // 显示所有x轴标签
    ShowAllLabelsOnXaxis() {
      if (this.isMultipleCharts()) {
        for (const item of this.chartOptions) {
          if (this.ShowLabelsOnXaxis) {
            for (let i = 0; i < item.xAxis.length; i++) {
              item.xAxis[i].axisLabel.interval = 0
            }
          } else {
            for (let i = 0; i < item.xAxis.length; i++) {
              item.xAxis[i].axisLabel.interval = 'auto'
            }
          }
        }
        this.createMultipleCharts()
      } else {
        if (this.ShowLabelsOnXaxis) {
          for (let i = 0; i < this.lineOption.xAxis.length; i++) {
            this.lineOption.xAxis[i].axisLabel.interval = 0
          }
        } else {
          for (let i = 0; i < this.lineOption.xAxis.length; i++) {
            this.lineOption.xAxis[i].axisLabel.interval = 'auto'
          }
        }
        this.createCharts()
      }
    },
    changeRotate() {
      if (this.isMultipleCharts()) {
        for (const item of this.chartOptions) {
          for (let i = 0; i < item.xAxis.length; i++) {
            item.xAxis[i].axisLabel.rotate = this.Rotate
          }
        }
        this.createMultipleCharts()
      } else {
        for (let i = 0; i < this.lineOption.xAxis.length; i++) {
          this.lineOption.xAxis[i].axisLabel.rotate = this.Rotate
        }
        this.createCharts()
      }
    },
    // X-Axis 颜色
    changeXAxisColor() {
      if (this.isMultipleCharts()) {
        for (const item of this.chartOptions) {
          for (let i = 0; i < item.xAxis.length; i++) {
            item.xAxis[i].axisLine.lineStyle.color = this.XAxisColor
          }
        }
        this.createMultipleCharts()
      } else {
        for (let i = 0; i < this.lineOption.xAxis.length; i++) {
          this.lineOption.xAxis[i].axisLine.lineStyle.color = this.XAxisColor
        }
        this.createCharts()
      }
    },
    // 是否显示 Y-Axis
    changeYAxisShow() {
      if (this.isMultipleCharts()) {
        for (const item of this.chartOptions) {
          if (this.YAxisShow) {
            for (let i = 0; i < item.yAxis.length; i++) {
              item.yAxis[i].axisLine['show'] = true
              item.yAxis[i].axisTick.show = true
            }
          } else {
            for (let i = 0; i < item.yAxis.length; i++) {
              item.yAxis[i].axisLine['show'] = false
              item.yAxis[i].axisTick.show = false
            }
          }
        }
        this.createMultipleCharts()
      } else {
        if (this.YAxisShow) {
          for (let i = 0; i < this.lineOption.yAxis.length; i++) {
            this.lineOption.yAxis[i].axisLine['show'] = true
            this.lineOption.yAxis[i].axisTick.show = true
          }
        } else {
          for (let i = 0; i < this.lineOption.yAxis.length; i++) {
            this.lineOption.yAxis[i].axisLine['show'] = false
            this.lineOption.yAxis[i].axisTick.show = false
          }
        }
        this.createCharts()
      }
    },
    // Y-Axis 颜色
    changeYAxisColor() {
      if (this.isMultipleCharts()) {
        for (const item of this.chartOptions) {
          for (let i = 0; i < item.yAxis.length; i++) {
            item.yAxis[i].axisLine.lineStyle.color = this.YAxisColor
          }
        }
        this.createMultipleCharts()
      } else {
        for (let i = 0; i < this.lineOption.yAxis.length; i++) {
          this.lineOption.yAxis[i].axisLine.lineStyle.color = this.YAxisColor
        }
        this.createCharts()
      }
    },
    changeShowSplitLine() {
      if (this.isMultipleCharts()) {
        for (const item of this.chartOptions) {
          if (this.barDirection === 'horizontal') {
            for (let i = 0; i < item.yAxis.length; i++) {
              item.yAxis[i].splitLine.show = false
            }
            for (let i = 0; i < item.xAxis.length; i++) {
              item.xAxis[i].splitLine.show = this.SplitLine
            }
          } else {
            for (let i = 0; i < item.yAxis.length; i++) {
              item.yAxis[i].splitLine.show = this.SplitLine
            }
            for (let i = 0; i < this.lineOption.xAxis.length; i++) {
              item.xAxis[i].splitLine.show = false
            }
          }
        }
        this.createMultipleCharts()
      } else {
        if (this.barDirection === 'horizontal') {
          for (let i = 0; i < this.lineOption.yAxis.length; i++) {
            this.lineOption.yAxis[i].splitLine.show = false
          }
          for (let i = 0; i < this.lineOption.xAxis.length; i++) {
            this.lineOption.xAxis[i].splitLine.show = this.SplitLine
          }
        } else {
          for (let i = 0; i < this.lineOption.yAxis.length; i++) {
            this.lineOption.yAxis[i].splitLine.show = this.SplitLine
          }
          for (let i = 0; i < this.lineOption.xAxis.length; i++) {
            this.lineOption.xAxis[i].splitLine.show = false
          }
        }
        this.createCharts()
      }
    },
    // 柱状图是否堆叠切换
    changeChartStack() {
      if (this.isMultipleCharts()) {
        for (const item of this.chartOptions) {
          if (this.chartStack) {
            for (let i = 0; i < item.series.length; i++) {
              item.series[i].stack = 'stack'
            }
            if (this.chartStack && this.barDirection === 'horizontal') {
              delete item['dataZoom']
              this.dataZoomShow = false
            }
          } else {
            for (let i = 0; i < item.series.length; i++) {
              delete item.series[i].stack
            }
          }
        }
        this.createMultipleCharts()
      } else {
        if (this.chartStack) {
          for (let i = 0; i < this.lineOption.series.length; i++) {
            this.lineOption.series[i].stack = 'stack'
          }
          if (this.chartStack && this.barDirection === 'horizontal') {
            delete this.lineOption['dataZoom']
            this.dataZoomShow = false
          }
        } else {
          for (let i = 0; i < this.lineOption.series.length; i++) {
            delete this.lineOption.series[i].stack
          }
        }
        this.createCharts()
      }
    },
    // 修改柱状图方向
    changeBarDirection() {
      if (this.isMultipleCharts()) {
        for (const item of this.chartOptions) {
          const xAxisName = item.xAxis[0].name
          const xAxisData = item.xAxis[0].data
          const xAxisType = item.xAxis[0].type
          const yAxisName = item.yAxis[0].name
          const yAxisData = item.yAxis[0].data
          const yAxisType = item.yAxis[0].type

          item.xAxis[0].name = yAxisName
          item.xAxis[0].data = yAxisData
          item.xAxis[0].type = yAxisType

          item.yAxis[0].name = xAxisName
          item.yAxis[0].data = xAxisData
          item.yAxis[0].type = xAxisType

          if (this.chartStack && this.barDirection === 'horizontal') {
            delete item['dataZoom']
            this.dataZoomShow = false
          }

          if (this.barDirection === 'horizontal') {
            this.SplitLine = true
            for (let i = 0; i < item.yAxis.length; i++) {
              item.yAxis[i].splitLine.show = false
            }
            for (let i = 0; i < item.xAxis.length; i++) {
              item.xAxis[i].splitLine.show = true
            }
          } else {
            this.SplitLine = true
            for (let i = 0; i < item.yAxis.length; i++) {
              item.yAxis[i].splitLine.show = true
            }
            for (let i = 0; i < this.lineOption.xAxis.length; i++) {
              item.xAxis[i].splitLine.show = false
            }
          }
        }
        this.createMultipleCharts()
      } else {
        const xAxisName = this.lineOption.xAxis[0].name
        const xAxisData = this.lineOption.xAxis[0].data
        const xAxisType = this.lineOption.xAxis[0].type
        const yAxisName = this.lineOption.yAxis[0].name
        const yAxisData = this.lineOption.yAxis[0].data
        const yAxisType = this.lineOption.yAxis[0].type

        this.lineOption.xAxis[0].name = yAxisName
        this.lineOption.xAxis[0].data = yAxisData
        this.lineOption.xAxis[0].type = yAxisType

        this.lineOption.yAxis[0].name = xAxisName
        this.lineOption.yAxis[0].data = xAxisData
        this.lineOption.yAxis[0].type = xAxisType

        if (this.chartStack && this.barDirection === 'horizontal') {
          delete this.lineOption['dataZoom']
          this.dataZoomShow = false
        }

        if (this.barDirection === 'horizontal') {
          this.SplitLine = true
          for (let i = 0; i < this.lineOption.yAxis.length; i++) {
            this.lineOption.yAxis[i].splitLine.show = false
          }
          for (let i = 0; i < this.lineOption.xAxis.length; i++) {
            this.lineOption.xAxis[i].splitLine.show = true
          }
        } else {
          this.SplitLine = true
          for (let i = 0; i < this.lineOption.yAxis.length; i++) {
            this.lineOption.yAxis[i].splitLine.show = true
          }
          for (let i = 0; i < this.lineOption.xAxis.length; i++) {
            this.lineOption.xAxis[i].splitLine.show = false
          }
        }
        this.createCharts()
      }
    },
    // 是否显示 label
    changeLabelShow() {
      if (this.isMultipleCharts()) {
        if (this.labelShow) {
          for (const item of this.chartOptions) {
            for (let i = 0; i < item.series.length; i++) {
              item.series[i].label.normal.show = true
            }
          }
          this.createMultipleCharts()
        } else {
          for (const item of this.chartOptions) {
            for (let i = 0; i < item.series.length; i++) {
              item.series[i].label.normal.show = false
            }
          }
          this.createMultipleCharts()
        }
      } else {
        if (this.labelShow) {
          for (let i = 0; i < this.lineOption.series.length; i++) {
            this.lineOption.series[i].label.normal.show = true
          }
          this.createCharts()
        } else {
          for (let i = 0; i < this.lineOption.series.length; i++) {
            this.lineOption.series[i].label.normal.show = false
          }
          this.createCharts()
        }
      }
    },
    // label显示位置
    changeLabelPosition() {
      if (this.isMultipleCharts()) {
        for (const item of this.chartOptions) {
          for (let i = 0; i < item.series.length; i++) {
            item.series[i].label.normal.position = this.labelPosition
          }
        }
        this.createMultipleCharts()
      } else {
        for (let i = 0; i < this.lineOption.series.length; i++) {
          this.lineOption.series[i].label.normal.position = this.labelPosition
        }
        this.createCharts()
      }
    },
    // 是否显示 Legend
    changeLegendShow() {
      if (this.isMultipleCharts()) {
        for (const item of this.chartOptions) {
          item.legend['show'] = this.legendShow
        }
        this.createMultipleCharts()
      } else {
        this.lineOption.legend['show'] = this.legendShow
        this.createCharts()
      }
    },
    // Legend 的位置
    changeLegendTop() {
      if (this.isMultipleCharts()) {
        for (const item of this.chartOptions) {
          item.legend.top = this.legendTop + '%'
        }
        this.createMultipleCharts()
      } else {
        this.lineOption.legend.top = this.legendTop + '%'
        this.createCharts()
      }
    },
    changeLegendLeft() {
      if (this.isMultipleCharts()) {
        for (const item of this.chartOptions) {
          item.legend.left = this.legendLeft
        }
        this.createMultipleCharts()
      } else {
        this.lineOption.legend.left = this.legendLeft
        this.createCharts()
      }
    },
    // Legend Orient
    changeLegendOrient() {
      if (this.isMultipleCharts()) {
        for (const item of this.chartOptions) {
          item.legend.orient = this.legendOrient
          if (this.legendOrient === 'horizontal') {
            this.legendLeft = 'left'
            item.legend.left = 'left'
          } else {
            this.legendLeft = 'right'
            item.legend.left = 'right'
          }
        }
        this.createMultipleCharts()
      } else {
        this.lineOption.legend.orient = this.legendOrient
        if (this.legendOrient === 'horizontal') {
          this.legendLeft = 'left'
          this.lineOption.legend.left = 'left'
        } else {
          this.legendLeft = 'right'
          this.lineOption.legend.left = 'right'
        }
        this.createCharts()
      }
    },
    // Legend 字体
    changeLegendFont() {
      if (this.isMultipleCharts()) {
        for (const item of this.chartOptions) {
          item.legend.textStyle.fontFamily = this.legendFont
        }
        this.createMultipleCharts()
      } else {
        this.lineOption.legend.textStyle.fontFamily = this.legendFont
        this.createCharts()
      }
    },
    // Legend 字体大小
    changeLegendFontSize() {
      if (this.isMultipleCharts()) {
        for (const item of this.chartOptions) {
          item.legend.textStyle.fontSize = this.legendFontSize
        }
        this.createMultipleCharts()
      } else {
        this.lineOption.legend.textStyle.fontSize = this.legendFontSize
        this.createCharts()
      }
    },
    // Legend 字体颜色
    changeLegendFontColor() {
      if (this.isMultipleCharts()) {
        for (const item of this.chartOptions) {
          item.legend.textStyle.color = this.legendFontColor
        }
        this.createMultipleCharts()
      } else {
        this.lineOption.legend.textStyle.color = this.legendFontColor
        this.createCharts()
      }
      this.lineOption.legend.textStyle.color = this.legendFontColor
      this.createCharts()
    },
    // label 字体
    changeLabelFont() {
      if (this.isMultipleCharts()) {
        for (const item of this.chartOptions) {
          for (let i = 0; i < item.series.length; i++) {
            item.series[i].label.normal.fontFamily = this.labelFont
          }
        }
        this.createMultipleCharts()
      } else {
        for (let i = 0; i < this.lineOption.series.length; i++) {
          this.lineOption.series[i].label.normal.fontFamily = this.labelFont
        }
        this.createCharts()
      }
    },
    // label 字体
    changeLabelFontColor() {
      if (this.isMultipleCharts()) {
        for (const item of this.chartOptions) {
          for (let i = 0; i < item.series.length; i++) {
            item.series[i].label.normal.color = this.labelFontColor
          }
        }
        this.createMultipleCharts()
      } else {
        for (let i = 0; i < this.lineOption.series.length; i++) {
          this.lineOption.series[i].label.normal.color = this.labelFontColor
        }
        this.createCharts()
      }
    },
    // label 大小
    changeLabelFontSize() {
      if (this.isMultipleCharts()) {
        for (const item of this.chartOptions) {
          for (let i = 0; i < item.series.length; i++) {
            item.series[i].label.normal.fontSize = this.labelFontSize
          }
        }
        this.createMultipleCharts()
      } else {
        for (let i = 0; i < this.lineOption.series.length; i++) {
          this.lineOption.series[i].label.normal.fontSize = this.labelFontSize
        }
        this.createCharts()
      }
    },
    // 饼图样式
    changePieChartsType() {
      if (this.pieType === '1') {
        for (let i = 0; i < this.lineOption.series.length; i++) {
          delete this.lineOption.series[i]['roseType']
          this.lineOption.series[i]['radius'] = ['0', '50%']
        }
      }
      if (this.pieType === '2') {
        for (let i = 0; i < this.lineOption.series.length; i++) {
          this.lineOption.series[i]['roseType'] = 'radius'
          this.lineOption.series[i]['radius'] = ['0', '50%']
        }
      }
      if (this.pieType === '3') {
        for (let i = 0; i < this.lineOption.series.length; i++) {
          delete this.lineOption.series[i]['roseType']
          this.lineOption.series[i]['radius'] = ['40%', '50%']
        }
      }
      this.createCharts()
    },
    handlerCreateDataTable() {
      this.$router.push({ name: 'data-analysis-data-visualization-data-table-form' })
    },
    createCharts() {
      this.myChart.clear()
      this.myChart.setOption(this.lineOption)
    },
    showLabelAttr() {
      if (this.labelShow === true) {
        document.getElementById('label-attr').style.display = 'inline'
      } else {
        document.getElementById('label-attr').style.display = 'none'
      }
    },
    defaultData() {
      // 生成统计图类型
      // this.chartType = '';
      // 折线统计图类型
      this.chartsType = '1'
      // 统计图标题
      this.chartsTitle = ''
      // 统计图标题在Y轴百分比
      this.titleTop = '0'
      // 统计图标题在X轴百分比
      this.titleLeft = '0'
      // 统计图标题颜色
      this.titleColor = '#000'
      // 统计图标题字体
      this.titleFont = 'sans-serif'
      // 统计图标题字体大小
      this.titleSize = '18'
      // 统计图标背景颜色
      this.backgroundColor = '#fff'
      // 折线或条形统计图的内容颜色
      this.lineColor = ''
      // 是否展示标签
      this.labelShow = false
      // 标签位置
      this.labelPosition = 'top'
      // 标签字体
      this.labelFont = 'sans-serif'
      // 标签字体大小
      this.labelFontSize = '12'
      // 标签字体颜色
      this.labelFontColor = ''
      // 折线或条形统计图某个内容名称
      this.lineName = ''
      // 折线图拐角样式
      this.symbolName = 'emptyCircle'
      // 折线图线条样式
      this.lineStyleType = 'solid'
      // 是否展示legend
      this.legendShow = true
      // legend在Y轴百分比
      this.legendTop = '0'
      // legend在X轴的位置
      this.legendLeft = 'center'
      // legend字体
      this.legendFont = 'sans-serif'
      // legend字体大小
      this.legendFontSize = '12'
      // legend字体颜色
      this.legendFontColor = '#333'
      // 是否展示X轴
      this.XAxisShow = true
      // 是否展示Y轴
      this.YAxisShow = true
      // X轴颜色
      this.XAxisColor = '#333'
      // Y轴颜色
      this.YAxisColor = '#333'
      // legend方向
      this.legendOrient = 'horizontal'
      // 是否展示缩放工具
      this.dataZoomShow = false
      // 缩放工具字体颜色
      this.dataZoomFontColor = '#333'
      // 饼图的样式
      this.pieType = '1'
      // 折线或条形是否让数据累加显示
      this.chartStack = false
      // 柱状图方向
      this.barDirection = 'vertical'
      this.ShowLabelsOnXaxis = false
      this.Rotate = 0
      this.SplitLine = true
      this.startColor = '#C7DBFF'
      this.endColor = '#5291FF'
      this.chartLeft = 10
      this.chartRight = 10
      this.chartTop = 8
      this.chartBottom = 20
      this.heatmapOrient = 'horizontal'
      this.heatmapLeft = 40
      this.heatmapBottom = 5
    },
    handleClick(tab) {
      if (tab.name === 'second' && this.previewData.length === 0) {
        this.loading = true
        const requestData = {
          sourceId: this.dataModel.view.sourceId,
          sql: this.dataModel.view.sql
        }
        getPreviewData(requestData)
          .then((res) => {
            this.previewData = res.data
            this.previewKeys = Object.keys(this.previewData[0])
          })
          .finally(() => {
            this.loading = false
          })
      }
    },
    addCondition(databaseName, tableName, name) {
      const requestData = {
        sourceId: this.dataModel.view.sourceId,
        databaseName: databaseName,
        tableName: tableName,
        fieldName: name
      }
      this.loading = true
      getEnum(requestData)
        .then((res) => {
          this.filterGroups = res.data

          for (let i = 0; i < this.columns.length; i++) {
            if (this.columns[i].name === name && this.columns[i].tableName === tableName && this.columns[i].databaseName === databaseName) {
              if (this.MYSQLNoDateTypeList.indexOf(this.columns[i].type) !== -1) {
                this.columns[i].filters.push(
                  {
                    condition: '>',
                    matter: '',
                    andOr: ''
                  }
                )
              } else {
                this.columns[i].filters.push(
                  {
                    andOr: '',
                    date: ''
                  }
                )
              }
            }
          }
          this.dataShow = false
          this.dataShow = true
          this.addOk = !this.addOk
        })
        .finally(() => {
          this.loading = false
        })
    },
    okCondition(item, name, type) {
      if (this.MYSQLNoDateTypeList.indexOf(type) !== -1) {
        if (this.filterMatter.length > 0) {
          for (let i = 0; i < this.columns.length; i++) {
            if (this.columns[i].name === name && this.columns[i].tableName === item.tableName && this.columns[i].databaseName === item.databaseName) {
              this.columns[i].filters[this.columns[i].filters.length - 1].condition = this.filterCondition
              this.columns[i].filters[this.columns[i].filters.length - 1].matter = this.filterMatter
              this.columns[i].filters[this.columns[i].filters.length - 1].andOr = this.filterAndOr
            }
          }
          this.dataShow = false
          this.dataShow = true

          this.filterCondition = '>'
          this.filterMatter = ''
          this.filterAndOr = 'AND'

          this.addOk = !this.addOk
        } else {
          this.$message({ message: this.$t('soi.dataVisualization.filterConditionNotNull'), type: 'warning' })
        }
      } else {
        if (this.filterDate.length > 0) {
          for (let i = 0; i < this.columns.length; i++) {
            if (this.columns[i].name === name && this.columns[i].tableName === item.tableName && this.columns[i].databaseName === item.databaseName) {
              this.columns[i].filters[this.columns[i].filters.length - 1].andOr = this.filterAndOr
              this.columns[i].filters[this.columns[i].filters.length - 1].date = '`' + name + '`' + " > '" + this.$moment.parseZone(this.filterDate[0]).local().format('YYYY-MM-DD') + "' AND " + '`' + name + '`' + " < '" + this.$moment.parseZone(this.filterDate[1]).local().format('YYYY-MM-DD') + "'"
            }
          }
          this.dataShow = false
          this.dataShow = true

          this.filterDate = ''
          this.filterAndOr = 'AND'

          this.addOk = !this.addOk
        } else {
          this.$message({ message: this.$t('soi.dataVisualization.FilterConditionNotNull'), type: 'warning' })
        }
      }
    },
    closeCondition(item, name) {
      for (let i = 0; i < this.columns.length; i++) {
        if (this.columns[i].name === name && this.columns[i].tableName === item.tableName && this.columns[i].databaseName === item.databaseName) {
          this.columns[i].filters.splice(this.columns[i].filters.length - 1, 1)
        }
      }

      this.filterMatter = ''
      this.filterDate = ''

      this.dataShow = false
      this.dataShow = true

      this.addOk = !this.addOk
    },
    cleanFilter(item, name, index) {
      for (let i = 0; i < this.columns.length; i++) {
        if (this.columns[i].name === name && this.columns[i].tableName === item.tableName && this.columns[i].databaseName === item.databaseName) {
          this.columns[i].filters.splice(index, 1)
        }
      }
      this.dataShow = false
      this.dataShow = true
    },
    clearStyle() {
      this.defaultData()
      this.myChart.clear()
      this.myChart.setOption(JSON.parse(this.initOption))
    },
    getOrderList() {
      const temp = []
      if (!(this.dimensions.length === 1 && this.dimensions[0].id === -1)) {
        for (let i = 0; i < this.dimensions.length; i++) {
          temp.push({
            'name': this.dimensions[i],
            'orderType': 0,
            'aggregate': false
          })
        }
      }
      if (!(this.measures.length === 1 && this.measures[0].id === -1)) {
        for (let i = 0; i < this.measures.length; i++) {
          temp.push({
            'name': this.measures[i],
            'orderType': 0, // 0默认,1升序,2降序
            'aggregate': true
          })
        }
      }
      this.orderList = temp
    },
    handleCancel(value, index) {
      this.lineName = value === this.lineName ? '' : value

      if (this.lineName.length > 0) {
        this.lineColor = this.lineOption.series[index]['color']
      }
    },
    handleCancel2(value, index) {
      this.pieName = value === this.pieName ? '' : value
      if (this.pieName.length > 0 && this.lineOption.series[0].data[index].itemStyle) {
        this.pieColor = this.lineOption.series[0].data[index].itemStyle.color
      } else {
        this.pieColor = ''
      }
    },
    handleColors() {
      let color = ''
      const r = Math.floor(Math.random() * 256)
      const g = Math.floor(Math.random() * 256)
      const b = Math.floor(Math.random() * 256)
      color = `rgb(${r},${g},${b})`
      return color
    },
    setEchartColor() {
      if (this.lineOption.series[0].data.length % 11 === 1 && this.lineOption.series[0].data.length > 1) {
        const color = this.handleColors()
        this.lineOption.series[0].data[this.lineOption.series[0].data.length - 1]['itemStyle'] = { color: color }
      }
    },
    editChartConfig(style, aggregation, dimensions, measures, orderList, requestChartsData) {
      this.requestChartsData = requestChartsData
      this.orderList = orderList
      this.dimensions = dimensions
      this.measures = measures
      // this.columns = columns
      // 聚合类型
      this.resultFunction = aggregation
      // 生成统计图类型
      this.chartType = style.chartType
      // 折线统计图类型
      this.chartsType = style.chartsType
      // 统计图标题
      this.chartsTitle = style.chartsTitle
      // 统计图标题在Y轴百分比
      this.titleTop = style.titleTop
      // 统计图标题在X轴百分比
      this.titleLeft = style.titleLeft
      // 统计图标题颜色
      this.titleColor = style.titleColor
      // 统计图标题字体
      this.titleFont = style.titleFont
      // 统计图标题字体大小
      this.titleSize = style.titleSize
      // 统计图标背景颜色
      this.backgroundColor = style.backgroundColor
      // 折线或条形统计图的内容颜色
      this.lineColor = style.lineColor
      // 是否展示标签
      this.labelShow = style.labelShow
      // 标签位置
      this.labelPosition = style.labelPosition
      // 标签字体
      this.labelFont = style.labelFont
      // 标签字体大小
      this.labelFontSize = style.labelFontSize
      // 标签字体颜色
      this.labelFontColor = style.labelFontColor
      // 折线或条形统计图某个内容名称
      this.lineName = style.lineName
      // 折线图拐角样式
      this.symbolName = style.symbolName
      // 折线图线条样式
      this.lineStyleType = style.lineStyleType
      // 是否展示legend
      this.legendShow = style.legendShow
      // legend在Y轴百分比
      this.legendTop = style.legendTop
      // legend在X轴的位置
      this.legendLeft = style.legendLeft
      // legend字体
      this.legendFont = style.legendFont
      // legend字体大小
      this.legendFontSize = style.legendFontSize
      // legend字体颜色
      this.legendFontColor = style.legendFontColor
      // 是否展示X轴
      this.XAxisShow = style.XAxisShow
      // 是否展示Y轴
      this.YAxisShow = style.YAxisShow
      // X轴颜色
      this.XAxisColor = style.XAxisColor
      // Y轴颜色
      this.YAxisColor = style.YAxisColor
      // legend方向
      this.legendOrient = style.legendOrient
      // 是否展示缩放工具
      this.dataZoomShow = style.dataZoomShow
      // 缩放工具字体颜色
      this.dataZoomFontColor = style.dataZoomFontColor
      // 饼图的样式
      this.pieType = style.pieType
      // 折线或条形是否让数据累加显示
      this.chartStack = style.chartStack
      // 柱状图方向
      this.barDirection = style.barDirection
      this.ShowLabelsOnXaxis = style.ShowLabelsOnXaxis
      this.Rotate = style.Rotate
      this.SplitLine = style.SplitLine

      this.startColor = style.startColor
      this.endColor = style.endColor
      this.chartLeft = style.chartLeft
      this.chartRight = style.chartRight
      this.chartTop = style.chartTop
      this.chartBottom = style.chartBottom
      this.heatmapLeft = style.heatmapLeft
      this.heatmapBottom = style.heatmapBottom
    },
    orderListHandler() {
      this.orders = []
      for (let i = 0; i < this.orderList.length; i++) {
        if (this.orderList[i].orderType === 1 && this.orderList[i].aggregate) {
          if (this.isSingleTable) {
            this.orders.push({
              'aliasColumn': '',
              'column': this.resultFunction + '(' + this.orderList[i].name.columnName + ')',
              'direction': 'ASC'
            })
          } else {
            this.orders.push({
              'aliasColumn': '',
              'column': this.resultFunction + '(' + this.orderList[i].name.databaseName + '$' + this.orderList[i].name.columnName + '(' + this.orderList[i].name.tableName + '))',
              'direction': 'ASC'
            })
          }
        } else if (this.orderList[i].orderType === 1 && !this.orderList[i].aggregate) {
          if (this.isSingleTable) {
            this.orders.push({
              'aliasColumn': '',
              'column': this.orderList[i].name.columnName,
              'direction': 'ASC'
            })
          } else {
            this.orders.push({
              'aliasColumn': '',
              'column': this.orderList[i].name.databaseName + '$' + this.orderList[i].name.columnName + '(' + this.orderList[i].name.tableName + ')',
              'direction': 'ASC'
            })
          }
        }
        if (this.orderList[i].orderType === 2 && this.orderList[i].aggregate) {
          if (this.isSingleTable) {
            this.orders.push({
              'aliasColumn': '',
              'column': this.resultFunction + '(' + this.orderList[i].name.columnName + ')',
              'direction': 'DESC'
            })
          } else {
            this.orders.push({
              'aliasColumn': '',
              'column': this.resultFunction + '(' + this.orderList[i].name.databaseName + '$' + this.orderList[i].name.columnName + '(' + this.orderList[i].name.tableName + '))',
              'direction': 'DESC'
            })
          }
        } else if (this.orderList[i].orderType === 2 && !this.orderList[i].aggregate) {
          if (this.isSingleTable) {
            this.orders.push({
              'aliasColumn': '',
              'column': this.orderList[i].name.columnName,
              'direction': 'DESC'
            })
          } else {
            this.orders.push({
              'aliasColumn': '',
              'column': this.orderList[i].name.databaseName + '$' + this.orderList[i].name.columnName + '(' + this.orderList[i].name.tableName + ')',
              'direction': 'DESC'
            })
          }
        }
      }
    },
    isClickChart() {
      if ((this.dimensions.length === 1 && this.dimensions[0].id === -1) && (this.measures.length === 1 && this.measures[0].id === -1)) {
        this.tableClick = false
        this.barClick = false
        this.lineClick = false
        this.pieClick = false
        this.heatmapClick = false
      }
      this.tableClick = (((this.dimensions[0].id && this.dimensions[0].id !== -1) && this.dimensions.length === 1) || (this.dimensions[0].id && this.measures[0].id !== -1)) && this.arr2[0].id === -1
      if ((this.dimensions.length < 5 && this.dimensions[0].id !== -1) && this.measures[0].id !== -1) {
        this.barClick = true
        this.lineClick = true
      } else {
        this.barClick = false
        this.lineClick = false
      }
      // 1. dimensions 0,measures 1;
      // 2. dimensions more,measures 1;
      // 3. measures 0,dimensions 1;
      // 4. no color;
      this.pieClick = this.dimensions[0].id !== -1 && this.measures.length === 1 && this.measures[0].id !== -1 && this.arr2[0].id === -1

      if (this.resultFunction === 'typeCount' && !((this.dimensions.length === 1 && this.dimensions[0].id !== -1 && this.measures[0].id === -1) || (this.measures.length === 1 && this.measures[0].id !== -1 && this.dimensions[0].id === -1))) {
        this.resultFunction = 'sum'
      }
      if (this.resultFunction === 'categoryCount' && !((this.dimensions.length === 1 && this.dimensions[0].id !== -1 && this.measures.length === 1) || (this.measures.length === 1 && this.measures[0].id !== -1 && this.dimensions.length === 1))) {
        this.resultFunction = 'sum'
      }
      if ((this.resultFunction === 'typeCount' || this.resultFunction === 'categoryCount') && this.arr2[0].id !== -1) {
        this.resultFunction = 'sum'
      }
      if (this.measures.length === 1 && this.measures[0].id !== -1 && this.dimensions.length === 1 && this.dimensions[0].id !== -1) {
        this.tableClick = true
      }

      if ((this.dimensions.length === 1 && this.dimensions[0].id !== -1) && (this.measures.length === 1 && this.measures[0].id !== -1) && this.arr2[0].id !== -1) {
        this.heatmapClick = true
      } else {
        this.heatmapClick = false
      }

      if (this.arr2[0].id !== -1) {
        this.tableClick = false
      }
    },
    End(e) {
      // 去重
      this.dimensions = Array.from(new Set(this.dimensions))
      this.measures = Array.from(new Set(this.measures))
    },
    closeColor() {
      this.arr2 = [
        { id: -1, databaseName: '', tableName: '', columnName: '', type: '' }
      ]
    },
    cancelColor() {
      this.colorDialog = false
    },
    AddColor(e) {
      this.arr2 = [this.arr2[e.newIndex]]
    },
    addMeasures() {
      for (let i = 0; i < this.measures.length; i++) {
        if (this.measures[i].id === -1) {
          this.measures.splice(i, 1)
        }
      }
    },
    addDimensions() {
      for (let i = 0; i < this.dimensions.length; i++) {
        if (this.dimensions[i].id === -1) {
          this.dimensions.splice(i, 1)
        }
      }
    },
    onMove(e) {
      if (e.relatedContext.element.type === 'TABLE') return false
      if (e.relatedContext.element.type === 'DATABASE') return false
      if (e.draggedContext.element.type === 'TABLE') return false
      if (e.draggedContext.element.type === 'DATABASE') return false
    },
    openBucketingDialog(bucketingField) {
      this.bucketingField = JSON.parse(JSON.stringify(bucketingField))
      this.bucketingField['datasourceId'] = this.dataModel.view.sourceId
      this.bucketingField['sql'] = this.dataModel.view.sql

      this.dateTimeData.databaseName = bucketingField.databaseName
      this.dateTimeData.tableName = bucketingField.tableName
      this.dateTimeData.fieldName = bucketingField.columnName

      this.numberData.databaseName = bucketingField.databaseName
      this.numberData.tableName = bucketingField.tableName
      this.numberData.fieldName = bucketingField.columnName

      this.stringData.databaseName = bucketingField.databaseName
      this.stringData.tableName = bucketingField.tableName
      this.stringData.fieldName = bucketingField.columnName

      this.getBucketingInfoApi()
    },
    // 获取分桶字段信息
    getBucketingInfoApi() {
      const vue = this
      const buck = localStorage.getItem('bucketings')
      if (buck) {
        const tempBuck = JSON.parse(buck)
        for (const item of tempBuck) {
          if (
            item.databaseName === vue.bucketingField.databaseName &&
            item.tableName === vue.bucketingField.tableName &&
            item.fieldName === vue.bucketingField.columnName
          ) {
            this.bucketingInfo = item.bucketingInfo
            this.initBucketingDialog()
            this.bucketingDialog = true
            return
          }
        }
      }

      vue.loading = true
      delete vue.bucketingField.filters
      delete vue.bucketingField.name

      getBucketingField(vue.bucketingField)
        .then((res) => {
          if (res.data) {
            this.bucketingInfo = res.data

            // 添加本地缓存
            const buck = localStorage.getItem('bucketings')
            if (buck) {
              const tempBuck = JSON.parse(buck)
              tempBuck.push(
                {
                  fieldName: this.bucketingField.columnName,
                  databaseName: this.bucketingField.databaseName,
                  tableName: this.bucketingField.tableName,
                  bucketingInfo: this.bucketingInfo
                }
              )
              localStorage.setItem('bucketings', JSON.stringify(tempBuck))
            } else {
              localStorage.setItem('bucketings', JSON.stringify([
                {
                  fieldName: this.bucketingField.columnName,
                  databaseName: this.bucketingField.databaseName,
                  tableName: this.bucketingField.tableName,
                  bucketingInfo: this.bucketingInfo
                }
              ]))
            }

            this.initBucketingDialog()
            this.bucketingDialog = true
          }
        })
        .finally(() => {
          vue.loading = false
        })
    },
    initBucketingDialog() {
      this.dateTimeData.dataType = this.bucketingInfo.type
      this.numberData.dataType = this.bucketingInfo.type
      this.stringData.dataType = this.bucketingInfo.type

      if (
        this.bucketingInfo.type === 'SDATE' ||
        this.bucketingInfo.type === 'DATETIME' ||
        this.bucketingInfo.type === 'TIMESTAMP'
      ) {
        this.bucketingInfo.start = this.$moment(this.bucketingInfo.start).format('YYYY-MM-DD').toString()
        this.bucketingInfo.end = this.$moment(this.bucketingInfo.end).format('YYYY-MM-DD').toString()
        this.dateTimeData.rule[0] = {
          date: [new Date(this.bucketingInfo.start).getTime(), new Date(this.bucketingInfo.end).getTime()]
        }
      }
      if (this.bucketingInfo.type === 'DATE') {
        this.bucketingInfo.start = this.bucketingInfo.start.toString()
        this.bucketingInfo.end = this.bucketingInfo.end.toString()
        this.dateTimeData.rule[0] = {
          date: [new Date(this.bucketingInfo.start).getTime(), new Date(this.bucketingInfo.end).getTime()]
        }
      }

      if (this.bucketingInfo.type === 'TIME') {
        this.bucketingInfo.start = this.$moment(Number(this.bucketingInfo.start)).format('HH:mm:ss').toString()
        this.bucketingInfo.end = this.$moment(Number(this.bucketingInfo.end)).format('HH:mm:ss').toString()
        this.dateTimeData.rule[0] = {
          date: ['00:00:00', '23:59:59']
        }
      }
      if (this.bucketingInfo.type === 'YEAR') {
        this.bucketingInfo.start = this.$moment(this.bucketingInfo.start).format('yyyy').toString()
        this.bucketingInfo.end = this.$moment(this.bucketingInfo.end).format('yyyy').toString()
        this.dateTimeData.rule[0] = {
          date: [new Date(this.bucketingInfo.start).getTime(), new Date(this.bucketingInfo.end).getTime()]
        }
      }

      if (this.MYSQLNumberTypeList.indexOf(this.bucketingInfo.type) !== -1) {
        this.numberData.rule[0].number = [this.bucketingInfo.start, this.bucketingInfo.end]
      }

      if (this.MYSQLStringTypeList.indexOf(this.bucketingInfo.type) !== -1) {
        this.stringData.rule[0].string = [1, this.bucketingInfo.end]
      }

      for (let i = 0; i < this.bucketings.length; i++) {
        if (
          this.bucketings[i].fieldName === this.stringData.fieldName &&
          this.bucketings[i].databaseName === this.stringData.databaseName &&
          this.bucketings[i].tableName === this.stringData.tableName
        ) {
          this.stringData = JSON.parse(JSON.stringify(this.bucketings[i]))
          break
        }
      }

      for (let i = 0; i < this.bucketings.length; i++) {
        if (
          this.bucketings[i].fieldName === this.numberData.fieldName &&
          this.bucketings[i].databaseName === this.numberData.databaseName &&
          this.bucketings[i].tableName === this.numberData.tableName
        ) {
          this.numberData = JSON.parse(JSON.stringify(this.bucketings[i]))
          break
        }
      }

      for (let i = 0; i < this.bucketings.length; i++) {
        if (
          this.bucketings[i].fieldName === this.dateTimeData.fieldName &&
          this.bucketings[i].databaseName === this.dateTimeData.databaseName &&
          this.bucketings[i].tableName === this.dateTimeData.tableName
        ) {
          this.dateTimeData = JSON.parse(JSON.stringify(this.bucketings[i]))
          if (this.bucketingField.type === 'TIME') {
            this.rule = this.bucketings[i].rule
          }
          break
        }
      }
    },
    // 添加分桶的时间段
    addDateSegment(type) {
      if (type === 'string') {
        this.stringData.rule.push({
          string: [1, this.bucketingInfo.end]
        })
      }

      if (type === 'number') {
        this.numberData.rule.push({
          number: [this.bucketingInfo.start, this.bucketingInfo.end]
        })
      }
      if (type === 'date') {
        if (this.bucketingInfo.type === 'TIME') {
          this.rule.push({
            date: ['00:00:00', '23:59:59']
          })
        } else {
          this.dateTimeData.rule.push({
            date: [new Date(this.bucketingInfo.start).getTime(), new Date(this.bucketingInfo.end).getTime()]
          })
        }
      }
    },
    deleteDateSegment(index, type) {
      if (type === 'string') {
        this.stringData.rule.splice(index, 1)
      }
      if (type === 'number') {
        this.numberData.rule.splice(index, 1)
      }
      if (type === 'date') {
        if (this.bucketingInfo.type === 'TIME') {
          this.rule.splice(index, 1)
        } else {
          this.dateTimeData.rule.splice(index, 1)
        }
      }
    },
    // 关闭分桶对话框之前清空表单数据
    bucketingDefault() {
      this.dateTimeData = {
        fieldName: '',
        tableName: '',
        databaseName: '',
        aggregate: 'sum',
        dataType: '',
        rule: [
          {
            date: [new Date(this.bucketingInfo.start).getTime(), new Date(this.bucketingInfo.end).getTime()]
          }
        ]
      }
      this.rule = [
        {
          date: ['00:00:00', '23:59:59']
        }
      ]
      this.numberData = {
        fieldName: '',
        tableName: '',
        databaseName: '',
        aggregate: 'sum',
        dataType: '',
        rule: [
          {
            number: [this.bucketingInfo.start, this.bucketingInfo.end]
          }
        ]
      }
      this.stringData = {
        fieldName: '',
        tableName: '',
        databaseName: '',
        aggregate: 'sum',
        dataType: '',
        rule: [
          {
            string: [1, this.bucketingInfo.end]
          }
        ]
      }
    },
    // 添加分桶规则
    addBucketingCondition(type) {
      if (type === 'string') {
        for (let i = 0; i < this.stringData.rule.length; i++) {
          if (!this.stringData.rule[i].string[0] || !this.stringData.rule[i].string[1]) {
            this.$message.warning(this.$t('soi.dataVisualization.perfectTip'))
            return
          }
        }
        for (let i = 0; i < this.bucketings.length; i++) {
          if (
            this.bucketings[i].fieldName === this.stringData.fieldName &&
            this.bucketings[i].databaseName === this.stringData.databaseName &&
            this.bucketings[i].tableName === this.stringData.tableName
          ) {
            this.bucketings[i] = this.stringData
            this.bucketingDialog = false
            return
          }
        }
        this.bucketings.push(
          JSON.parse(JSON.stringify(this.stringData))
        )
      }
      if (type === 'number') {
        for (let i = 0; i < this.numberData.rule.length; i++) {
          if (this.numberData.rule[i].number[0] === null || this.numberData.rule[i].number[0] === undefined || this.numberData.rule[i].number[1] === null || this.numberData.rule[i].number[1] === undefined) {
            this.$message.warning(this.$t('soi.dataVisualization.perfectTip'))
            return
          }
        }
        for (let i = 0; i < this.bucketings.length; i++) {
          if (
            this.bucketings[i].fieldName === this.numberData.fieldName &&
            this.bucketings[i].databaseName === this.numberData.databaseName &&
            this.bucketings[i].tableName === this.numberData.tableName
          ) {
            this.bucketings[i] = this.numberData
            this.bucketingDialog = false
            return
          }
        }
        this.bucketings.push(
          JSON.parse(JSON.stringify(this.numberData))
        )
      }
      if (type === 'date') {
        if (this.bucketingInfo.type === 'TIME') {
          this.dateTimeData.rule = JSON.parse(JSON.stringify(this.rule))
        }
        for (let i = 0; i < this.dateTimeData.rule.length; i++) {
          if (
            this.dateTimeData.rule[i].date.length !== 2 ||
            !this.dateTimeData.rule[i].date[0] ||
            this.dateTimeData.rule[i].date[0].length <= 0 ||
            !this.dateTimeData.rule[i].date[1] ||
            this.dateTimeData.rule[i].date[1].length <= 0
          ) {
            this.$message.warning(this.$t('soi.dataVisualization.perfectTip'))
            return
          }
        }
        if (this.bucketingInfo.type === 'YEAR') {
          for (let i = 0; i < this.dateTimeData.rule.length; i++) {
            if (this.judgingYear(this.$moment(this.dateTimeData.rule[i].date[1]).format('yyyy'))) {
              this.dateTimeData.rule[i].date[1] = this.dateTimeData.rule[i].date[1] + 3600 * 1000 * 24 * 366 - 1
            } else {
              this.dateTimeData.rule[i].date[1] = this.dateTimeData.rule[i].date[1] + 3600 * 1000 * 24 * 365 - 1
            }
          }
        }
        for (let i = 0; i < this.bucketings.length; i++) {
          if (
            this.bucketings[i].fieldName === this.dateTimeData.fieldName &&
            this.bucketings[i].databaseName === this.dateTimeData.databaseName &&
            this.bucketings[i].tableName === this.dateTimeData.tableName
          ) {
            this.bucketings[i] = this.dateTimeData
            this.bucketingDialog = false
            return
          }
        }
        this.bucketings.push(
          JSON.parse(JSON.stringify(this.dateTimeData))
        )
      }
      this.bucketingDialog = false
    },
    // 清空分桶规则
    clearBucketing() {
      for (let i = 0; i < this.bucketings.length; i++) {
        if (
          this.bucketings[i].fieldName === this.stringData.fieldName &&
          this.bucketings[i].databaseName === this.stringData.databaseName &&
          this.bucketings[i].tableName === this.stringData.tableName
        ) {
          this.bucketings.splice(i, 1)
        }
      }
      this.bucketingDefault()
      this.bucketingDialog = false
    },
    // 判断年份是闰年还是平年（year: type Number）
    judgingYear(year) {
      return year % 4 === 0 && year % 100 !== 0 || year % 400 === 0
    },
    // 快捷分桶（字符串类型）
    stringGroupHotKey() {
      const rule = []
      for (let i = 0; (i + 1) * this.groupSize <= this.bucketingInfo.end; i++) {
        rule.push({
          string: [i * this.groupSize + 1, (i + 1) * this.groupSize]
        })
      }
      this.stringData.rule = rule
      if (rule[rule.length - 1].string[1] < this.bucketingInfo.end) {
        rule.push({
          string: [rule[rule.length - 1].string[1] + 1, this.bucketingInfo.end]
        })
        this.stringData.rule = rule
      }
    },
    // 快捷分桶（时间类型）
    buckets(type) {
      const rule = []
      // 按年分桶
      if (type === 1) {
        const start = new Date(this.bucketingInfo.start).getFullYear()
        const end = new Date(this.bucketingInfo.end).getFullYear()
        for (let i = start; i <= end; i++) {
          // 按年分在同一年
          if (start === end) {
            rule.push({
              date: [new Date(this.bucketingInfo.start).getTime(), new Date(this.bucketingInfo.end).getTime()]
            })
            continue
          }
          // 第一年
          if (i === start) {
            rule.push({
              date: [new Date(this.bucketingInfo.start).getTime(), new Date(i, 11, 31, 23, 59, 59).getTime()]
            })
            continue
          }
          // 最后一年
          if (i === end) {
            rule.push({
              date: [new Date(i, 0, 1, 0, 0, 0).getTime(), new Date(this.bucketingInfo.end).getTime()]
            })
            continue
          }
          // 不是第一年也不是最后一年
          rule.push({
            date: [new Date(i, 0, 1, 0, 0, 0).getTime(), new Date(i, 11, 31, 23, 59, 59).getTime()]
          })
        }
        this.dateTimeData.rule = rule
        return
      }
      // 按季度分桶
      if (type === 2) {
        const start = new Date(this.bucketingInfo.start)
        const end = new Date(this.bucketingInfo.end)
        const startYear = start.getFullYear()
        const endYear = end.getFullYear()
        const startMonth = start.getMonth()
        const endMonth = end.getMonth()
        for (let i = startYear; i <= endYear; i++) {
          // 开始和结束为同一年
          if (startYear === endYear) {
            let s = Math.floor(startMonth / 3) * 3
            const e = Math.floor(endMonth / 3) * 3
            // 开始月份和结束月份为同一个季度
            if (s === e) {
              rule.push({
                date: [new Date(this.bucketingInfo.start).getTime(), new Date(this.bucketingInfo.end).getTime()]
              })
              continue
            }
            const fist = true
            for (s; s <= e; s += 3) {
              // 开始的季度
              if (fist) {
                rule.push({
                  date: [new Date(this.bucketingInfo.start).getTime(), new Date(i, s + 2, s + 2 === 5 || s + 2 === 8 ? 30 : 31, 23, 59, 59).getTime()]
                })
                continue
              }
              // 结束的季度
              if (s === e) {
                rule.push({
                  date: [new Date(i, s, 1, 0, 0, 0).getTime(), new Date(this.bucketingInfo.end).getTime()]
                })
                continue
              }
              // 不是开始也不是结束的季度
              rule.push({
                date: [new Date(i, s, 1, 0, 0, 0).getTime(), new Date(i, s + 2, s + 2 === 5 || s + 2 === 8 ? 30 : 31, 23, 59, 59).getTime()]
              })
            }
            continue
          }
          // 开始和结束不是同一年，开始的年份
          if (i === startYear) {
            let j = Math.floor(startMonth / 3) * 3
            let fist = true
            for (j; j <= 9; j += 3) {
              // 开始的季度
              if (fist) {
                rule.push({
                  date: [new Date(this.bucketingInfo.start).getTime(), new Date(i, j + 2, j + 2 === 5 || j + 2 === 8 ? 30 : 31, 23, 59, 59).getTime()]
                })
                fist = false
                continue
              }
              rule.push({
                date: [new Date(i, j, 1, 0, 0, 0).getTime(), new Date(i, j + 2, j + 2 === 5 || j + 2 === 8 ? 30 : 31, 23, 59, 59).getTime()]
              })
            }
            continue
          }
          // 开始和结束不是同一年，结束的年份
          if (i === endYear) {
            const x = Math.floor(endMonth / 3) * 3
            for (let j = 0; j <= x; j += 3) {
              if (j === x) {
                // 结束的季度
                rule.push({
                  date: [new Date(i, j, 1, 0, 0, 0).getTime(), new Date(this.bucketingInfo.end).getTime()]
                })
                continue
              }
              rule.push({
                date: [new Date(i, j, 1, 0, 0, 0).getTime(), new Date(i, j + 2, j + 2 === 5 || j + 2 === 8 ? 30 : 31, 23, 59, 59).getTime()]
              })
            }
            continue
          }
          for (let j = 0; j <= 9; j += 3) {
            rule.push({
              date: [new Date(i, j, 1, 0, 0, 0).getTime(), new Date(i, j + 2, j + 2 === 5 || j + 2 === 8 ? 30 : 31, 23, 59, 59).getTime()]
            })
          }
        }
        this.dateTimeData.rule = rule
        return
      }
      // 按月分桶
      if (type === 3) {
        const start = new Date(this.bucketingInfo.start)
        const end = new Date(this.bucketingInfo.end)
        const startYear = start.getFullYear()
        const endYear = end.getFullYear()
        const startMonth = start.getMonth()
        const endMonth = end.getMonth()
        for (let i = startYear; i <= endYear; i++) {
          let day
          if (this.judgingYear(i)) {
            day = 29
          } else {
            day = 28
          }
          // 开始和结束在同一年
          if (startYear === endYear) {
            for (let j = startMonth; j < endMonth; j++) {
              // 开始和结束在同一个月
              if (startMonth === endMonth) {
                rule.push({
                  date: [new Date(this.bucketingInfo.start).getTime(), new Date(this.bucketingInfo.end).getTime()]
                })
                continue
              }
              // 开始和结束不在同一个月，开始月份
              if (j === startMonth) {
                rule.push({
                  date: [new Date(this.bucketingInfo.start).getTime(), new Date(i, j, ([0, 4, 6, 7, 9, 11].indexOf(j) === -1) ? (j === 1 ? day : 30) : 31, 23, 59, 59).getTime()]
                })
                continue
              }
              // 开始和结束不在同一个月，结束月份
              if (j === endMonth) {
                rule.push({
                  date: [new Date(i, j, 1, 0, 0, 0).getTime(), new Date(this.bucketingInfo.end).getTime()]
                })
                continue
              }
              rule.push({
                date: [new Date(i, j, 1, 0, 0, 0).getTime(), new Date(i, j, ([0, 4, 6, 7, 9, 11].indexOf(j) === -1) ? (j === 1 ? day : 30) : 31, 23, 59, 59).getTime()]
              })
            }
            continue
          }
          // 开始和结束不在一年，开始的年份
          if (i === startYear) {
            for (let j = startMonth; j < 12; j++) {
              // 开始的月份
              if (j === startMonth) {
                rule.push({
                  date: [new Date(this.bucketingInfo.start).getTime(), new Date(i, j, ([0, 4, 6, 7, 9, 11].indexOf(j) === -1) ? (j === 1 ? day : 30) : 31, 23, 59, 59).getTime()]
                })
                continue
              }
              rule.push({
                date: [new Date(i, j, 1, 0, 0, 0).getTime(), new Date(i, j, ([0, 4, 6, 7, 9, 11].indexOf(j) === -1) ? (j === 1 ? day : 30) : 31, 23, 59, 59).getTime()]
              })
            }
            continue
          }
          // 开始和结束不在一年，结束的年份
          if (i === endYear) {
            for (let j = 0; j <= endMonth; j++) {
              // 结束的月份
              if (j === endMonth) {
                rule.push({
                  date: [new Date(i, j, 1, 0, 0, 0).getTime(), new Date(this.bucketingInfo.end).getTime()]
                })
                continue
              }
              rule.push({
                date: [new Date(i, j, 1, 0, 0, 0).getTime(), new Date(i, j, ([0, 4, 6, 7, 9, 11].indexOf(j) === -1) ? (j === 1 ? day : 30) : 31, 23, 59, 59).getTime()]
              })
            }
            continue
          }
          for (let j = 0; j < 12; j++) {
            rule.push({
              date: [new Date(i, j, 1, 0, 0, 0).getTime(), new Date(i, j, ([0, 4, 6, 7, 9, 11].indexOf(j) === -1) ? (j === 1 ? day : 30) : 31, 23, 59, 59).getTime()]
            })
          }
        }
        this.dateTimeData.rule = rule
      }
    }
  }

}
</script>

<style scoped>
.charts .text-title.table-name {
  max-height: 95px;
  overflow-y: auto;
  word-break: break-all;
}

.charts .text-title {
  font-size: 15px;
  margin: 15px 0 15px;
}

.charts .myChart {
  width: 100%;
  height: 778px;
  padding: 10px 10px;
  margin: 0 auto;
}

.charts .div-left {
  border: 1px solid #cccccc;
  height: 934px;
  padding: 0 10px 10px;
}

.charts .div-y {
  padding: 0 10px 0;
  border-top: 1px solid #cccccc;
  border-right: 1px solid #cccccc;
  height: 75px;
}

.charts .div-chart {
  border-right: 1px solid #cccccc;
  border-top: 1px solid #cccccc;
  border-bottom: 1px solid #cccccc;
}

.charts .div-right {
  border-top: 1px solid #cccccc;
  border-right: 1px solid #cccccc;
  border-bottom: 1px solid #cccccc;
}

.charts .div-right .right-div {
  width: 100%;
  height: 800px;
  padding: 10px 10px 0 10px
}

.charts .div-bottom {
  border-top: 1px solid #cccccc;
  border-right: 1px solid #cccccc;
  height: 132px
}

.charts .X-axis {
  margin-bottom: 10px;
  display: inline-block;
  margin-right: 10px;
}

.charts .div-icon {
  width: 48px;
  margin: 10px auto 10px;
  display: inline-block;
  cursor: pointer;
}

.charts .tags {
  margin: 0 10px 5px 0;
}

.charts .text-title2 {
  font-size: 16px;
  color: #707070;
  text-align: center;
}

.charts .right-text {
  font-size: 18px;
  margin-bottom: 8px;
  text-align: center;
}

.charts .icon-item {
  width: 30px;
  height: 30px;
}

.charts .charts-style-title {
  font-size: 12px;
  display: inline-block;
  color: #109eae;
  margin-bottom: 5px;
}

.charts .contextmenu {
  margin: 0;
  background: #fff;
  z-index: 3000;
  position: absolute;
  list-style-type: none;
  padding: 5px 0;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 400;
  color: #333;
  box-shadow: 2px 2px 3px 0 rgba(0, 0, 0, 0.3);
}

.charts .contextmenu li {
  margin: 0;
  padding: 2px 16px;
  cursor: pointer;
}

.charts .contextmenu ul li :hover {
  background: #000;
}

.charts .scrollbar::-webkit-scrollbar {
  height: 7px;
}

.charts .scrollbar::-webkit-scrollbar-thumb {
  background-color: #999;
  border-radius: 5px;
}

.charts .scrollbar::-webkit-scrollbar-thumb:hover {
  background-color: #666;
}

.charts .scrollbar::-webkit-scrollbar-track {
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
  background: #fff;
}

.charts .sum {
  color: #F56C6C;
}

.charts .count {
  color: #67C23A;
}

.charts .avg {
  color: #E6A23C;
}
</style>

<style>
.charts .el-checkbox.is-bordered + .el-checkbox.is-bordered {
  margin-left: 0;
  margin-right: 5px;
}

.charts .el-input__inner {
  margin-bottom: 0;
}

.charts .el-checkbox.is-bordered.el-checkbox--medium {
  height: auto;
}

.charts .el-checkbox.is-bordered.el-checkbox--medium .el-checkbox__label {
  word-break: break-all;
  white-space: normal;
}

.charts .el-tag--medium {
  word-break: break-all;
  white-space: nowrap;
}

.charts .el-dropdown-menu--mini .el-dropdown-menu__item {
  line-height: 16px;
  font-size: 12px;
}

.charts .is-top {
  z-index: 2000;
}

.charts .el-radio__label {
  font-size: 12px;
}

.charts .color-dialog .el-dialog {
  margin: 0;
  float: right;
}

.charts .el-form-item {
  margin: 0;
}

.charts .el-form-item--medium .el-form-item__content, .el-form-item--medium .el-form-item__label {
  line-height: 30px;
}

.charts .tag-tip.el-tag {
  background-color: #ffffff;
  border: none;
  font-size: 16px;
  letter-spacing: 3px;
  font-style: italic;
}

.charts .color-tag .el-tag .el-tag__close {
  position: absolute;
  top: 6px;
  right: 2px;
}

.charts .el-date-editor .el-range-separator {
  width: 10%;
}

.charts .bucketing .el-radio {
  margin-right: 0;
}

.charts .bucketing-label label {
  margin-bottom: 0;
}

.charts .item {
  margin-top: 10px;
  margin-right: 40px;
}
</style>

<style lang="scss">
.charts {
  .el-tabs__item {
    padding: 0 10px;
    height: 35px;
    line-height: 35px;
    font-size: 12px;
  }

  .text-center {
    text-align: center;
    font-size: 14px;
  }
}
</style>
