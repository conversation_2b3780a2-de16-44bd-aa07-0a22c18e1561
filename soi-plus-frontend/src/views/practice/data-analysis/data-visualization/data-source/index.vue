<template>
  <div v-loading.fullscreen.lock="loading" class="data-visualization-data-source-container">
    <customize-card :title="`${$t('soi.router.dataVisualization')} - ${$t('soi.router.dataSource')}`">
      <template slot="header-actions">
        <el-button
          v-loading.fullscreen.lock="syncDataLoading"
          size="mini"
          class="actions"
          :element-loading-text="$t('soi.dataVisualization.syncDataLoading')"
          element-loading-background="#FFF"
          @click="synchronizeSandboxData()"
        >{{ $t('soi.dataVisualization.synchronizeSandboxData') }}</el-button>
        <el-button size="mini" class="actions" @click="selectDataTypeVisible = true">{{ $t('soi.dataVisualization.createDataSource') }}</el-button>
      </template>
      <div class="data-visualization-menu">
        <el-button size="mini" type="primary" @click="$router.push({ name: 'data-analysis-data-visualization-data-table' })">
          {{ $t('soi.router.dataTable') }}
        </el-button>
        <el-button size="mini" type="primary" @click="$router.push({ name: 'data-analysis-data-visualization-my-charts' })">
          {{ $t('soi.router.myCharts') }}
        </el-button>
      </div>
      <el-table
        :data="category"
        row-key="id"
        :show-header="false"
        :expand-row-keys="['PublicDataSource','CustomDataSource']"
        :row-class-name="tableRowClassName"
        :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
      >
        <el-table-column :label="$t('soi.dataVisualization.name')" prop="name" />
        <el-table-column type="expand">
          <template slot-scope="k">
            <el-table
              v-if="k.row.id === 'PublicDataSource'"
              :data="publicDataSource"
              :header-cell-style="{'background': '#109eae42', 'color': '#333'}"
              tooltip-effect="dark"
            >
              <el-table-column :label="$t('soi.dataVisualization.name')" prop="name" />
              <el-table-column :label="$t('soi.common.operate')" align="center" width="300">
                <template slot-scope="scope">
                  <el-button
                    v-if="scope.row.publicSource"
                    size="mini"
                    @click="handlerCreateDataTable(scope.row)"
                  >
                    {{ $t('soi.dataVisualization.createDataTable') }}
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
            <el-table
              v-if="k.row.id === 'CustomDataSource'"
              :data="customDataSource"
              :header-cell-style="{'background': '#109eae42', 'color': '#333'}"
              tooltip-effect="dark"
            >
              <el-table-column :label="$t('soi.dataVisualization.name')" prop="name" />
              <el-table-column :label="$t('soi.common.description')" prop="description" />
              <el-table-column :label="$t('soi.common.updateTime')" prop="updateTime" />
              <el-table-column :label="$t('soi.common.operate')" align="center" width="300">
                <template slot-scope="scope">
                  <el-button v-if="!scope.row.publicSource && scope.row.type" size="mini" @click="editDataSource(scope.row)">
                    {{ $t("soi.common.edit") }}
                  </el-button>
                  <el-button v-if="!scope.row.publicSource && !scope.row.type" size="mini" @click="reimport(scope.row)">
                    {{ $t("soi.dataVisualization.reimport") }}
                  </el-button>
                  <el-button
                    v-if="scope.row.type && !scope.row.publicSource"
                    size="mini"
                    @click="deleteDatasource(scope.row)"
                  >{{ $t("soi.common.delete") }}
                  </el-button>
                  <el-button
                    v-if="!scope.row.publicSource"
                    size="mini"
                    @click="handlerCreateDataTable(scope.row)"
                  >
                    {{ $t('soi.dataVisualization.createDataTable') }}
                  </el-button>
                </template>
              </el-table-column>
            </el-table>
          </template>
        </el-table-column>
      </el-table>

      <!-- 选择创建方式 -->
      <el-dialog
        :title="$t('soi.dataVisualization.selectDataSourceType')"
        :visible.sync="selectDataTypeVisible"
        width="400px"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
      >
        <el-row :gutter="24">
          <el-col :span="12">
            <el-card shadow="hover">
              <div style="padding:10px;text-align:center;height: 65px;word-break: break-word;cursor: pointer;" @click="showImportDialog('mysql')">{{ $t("soi.dataVisualization.addMysqlConnect") }}</div>
            </el-card>
          </el-col>
          <el-col :span="12">
            <el-card shadow="hover">
              <div style="padding:10px;text-align:center;height: 65px;word-break: break-word;cursor: pointer;" @click="showImportDialog('file')">{{ $t("soi.dataVisualization.importCsvFile") }}</div>
            </el-card>
          </el-col>
        </el-row>
        <div slot="footer" class="dialog-footer">
          <el-button size="small" @click="selectDataTypeVisible = false">{{ $t('soi.common.cancel') }}</el-button>
        </div>
      </el-dialog>

      <!-- 创建数据源 -->
      <el-dialog
        :title="$t('soi.dataVisualization.createDataSource')"
        :visible.sync="createDataSourceDialog"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        @close="resetForm('DBForm')"
      >
        <el-form ref="DBForm" :model="form" :rules="DBFormRules" style="padding: 0 100px;" label-width="180px">
          <el-form-item :label="$t('soi.dataVisualization.dataSourceType')" prop="DBType">
            <el-select v-model="form.DBType" style="width:100%">
              <el-option
                v-for="item in databaseTypeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item :label="$t('soi.dataVisualization.dataSourceHost')" prop="DBHost">
            <el-input v-model="form.DBHost" autocomplete="off" />
          </el-form-item>
          <el-form-item :label="$t('soi.dataVisualization.dataSourcePort')" prop="DBPort">
            <el-input v-model="form.DBPort" autocomplete="off" />
          </el-form-item>
          <el-form-item :label="$t('soi.dataVisualization.dataSourceUsername')" prop="DBUsername">
            <el-input v-model="form.DBUsername" autocomplete="off" />
          </el-form-item>
          <el-form-item :label="$t('soi.dataVisualization.dataSourcePassword')" prop="DBPassword">
            <el-input v-model="form.DBPassword" type="password" autocomplete="off" show-password />
          </el-form-item>
          <el-form-item :label="$t('soi.dataVisualization.dataSourceDescription')" prop="DBDescription">
            <el-input v-model="form.DBDescription" type="textarea" maxlenght="255" />
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button size="small" @click="createDataSourceDialog = false">{{ $t('soi.common.cancel') }}</el-button>
          <el-button size="small" type="primary" @click="testDB()">{{ $t('soi.dataVisualization.testConnect') }}</el-button>
          <el-button size="small" type="primary" @click="addDB()">{{ $t('soi.common.confirm') }}</el-button>
        </div>
      </el-dialog>

      <el-dialog
        :title="$t('soi.dataVisualization.editDataSource')"
        :visible.sync="dialogEditVisible"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        @close="resetForm('DBForm')"
      >
        <el-form ref="DBForm" :model="form" :rules="DBFormRules" style="padding: 0 100px;" label-width="180px">
          <el-form-item :label="$t('soi.dataVisualization.dataSourceType')" prop="DBType">
            <el-select v-model="form.DBType" style="width:100%">
              <el-option
                v-for="item in databaseTypeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item :label="$t('soi.dataVisualization.dataSourceHost')" prop="DBHost">
            <el-input v-model="form.DBHost" autocomplete="off" />
          </el-form-item>
          <el-form-item :label="$t('soi.dataVisualization.dataSourcePort')" prop="DBPort">
            <el-input v-model="form.DBPort" autocomplete="off" />
          </el-form-item>
          <el-form-item :label="$t('soi.dataVisualization.dataSourceUsername')" prop="DBUsername">
            <el-input v-model="form.DBUsername" autocomplete="off" />
          </el-form-item>
          <el-form-item :label="$t('soi.dataVisualization.dataSourcePassword')" prop="DBPassword">
            <el-input v-model="form.DBPassword" type="password" autocomplete="off" show-password />
          </el-form-item>
          <el-form-item :label="$t('soi.dataVisualization.dataSourceDescription')" prop="DBDescription">
            <el-input v-model="form.DBDescription" type="textarea" maxlenght="255" />
          </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer">
          <el-button size="small" @click="dialogEditVisible = false">{{ $t('soi.common.cancel') }}</el-button>
          <el-button size="small" type="primary" @click="testDB()">{{ $t('soi.dataVisualization.testConnect') }}</el-button>
          <el-button size="small" type="primary" @click="updateDB()">{{ $t('soi.common.confirm') }}</el-button>
        </div>
      </el-dialog>

      <el-dialog
        :title="$t('soi.dataVisualization.createDatabase')"
        :visible.sync="createDatabaseDialog"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        width="400px"
      >
        <el-alert
          :title="$t('soi.dataImport.createDatabaseTip')"
          type="success"
          :closable="false"
          style="margin-bottom:20px;"
        />
        <el-form ref="databaseForm" :model="databaseForm" :rules="databaseFormRules" label-width="130px">
          <el-form-item :label="$t('soi.dataImport.databaseName')" prop="databaseName">
            <el-input v-model="databaseForm.databaseName" />
          </el-form-item>
          <el-form-item>
            <el-button size="small" @click="createDatabaseDialog = false">{{ $t("soi.common.cancel") }}</el-button>
            <el-button type="primary" size="small" @click="createDatabase('databaseForm')">{{ $t("soi.common.confirm") }}</el-button>
          </el-form-item>
        </el-form>
      </el-dialog>
    </customize-card>
  </div>
</template>

<script>
import CustomizeCard from '@/components/CustomizeCard/index.vue'
import {
  addDataSource,
  deleteDataSource,
  getDataSourceInfo,
  getDataSourceList,
  testDataSourceCollect,
  updateDataSource
} from '@/api/practice/data-visualization'
import { mapGetters } from 'vuex'
import { databaseTypeOptions } from '@/data'
import { synchronizeData } from '@/api/practice/sandbox'
import { createDatabase } from '@/api/practice/data-import'

export default {
  name: 'DataVisualizationDataSource',
  components: { CustomizeCard },
  data() {
    const validateDBName = (rule, value, callback) => {
      const a = /^[a-z0-9_]*$/g

      if (!a.test(value)) {
        callback(new Error(this.$t('soi.dataImport.databaseNameFormatError')))
      } else {
        callback()
      }
    }
    return {
      loading: false,
      syncDataLoading: false,
      createDatabaseDialog: false,
      createDataSourceDialog: false,
      selectDataTypeVisible: false,
      dialogEditVisible: false,
      databaseTypeOptions,
      pageType: null,
      datasourceId: null,
      databaseForm: {
        databaseName: ''
      },
      databaseFormRules: {
        databaseName: [
          { required: true, validator: validateDBName, trigger: 'blur' },
          { min: 5, max: 30, message: this.$t('soi.dataImport.databaseNameLengthError'), trigger: 'blur' }
        ]
      },
      form: {
        name: '',
        DBType: 'MySQL',
        DBHost: '',
        DBPort: '',
        DBName: '',
        DBDescription: '',
        DBUsername: '',
        DBPassword: ''
      },
      category: [
        {
          id: 'PublicDataSource',
          name: this.$t('soi.dataVisualization.publicDataSource'),
          children: []
        },
        {
          id: 'CustomDataSource',
          name: this.$t('soi.dataVisualization.customDataSource'),
          children: []
        }
      ],
      publicDataSource: [],
      customDataSource: [],
      DBFormRules: {
        DBType: [
          { required: true, message: this.$t('soi.dataVisualization.dataSourceTypeIsRequired'), trigger: 'change' }
        ],
        DBHost: [
          { required: true, message: this.$t('soi.dataVisualization.dataSourceSourceHostIsRequired'), trigger: 'blur' }
        ],
        DBPort: [
          { required: true, message: this.$t('soi.dataVisualization.dataSourcePortIsRequired'), trigger: 'blur' }
        ],
        DBUsername: [
          { required: true, message: this.$t('soi.dataVisualization.dataSourceUsernameIsRequired'), trigger: 'blur' }
        ],
        DBPassword: [
          { required: true, message: this.$t('soi.dataVisualization.dataSourcePasswordIsRequired'), trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    ...mapGetters(['userDetails'])
  },
  created() {
    this.init()
  },
  methods: {
    init() {
      const vue = this
      vue.loading = true
      getDataSourceList({ userId: this.userDetails.id })
        .then((res) => {
          const { data } = res
          this.publicDataSource = []
          this.customDataSource = []
          for (const item of data) {
            if (!item.publicSource) {
              item.updateTime = new Date(item.updateTime).toLocaleString()
              vue.customDataSource.push(item)
            } else {
              vue.publicDataSource.push(item)
            }
          }
        })
        .finally(() => {
          vue.loading = false
        })
    },
    reimport() {
      this.$router.push({ name: 'data-analysis-data-import-import-data' })
    },
    showImportDialog(type) {
      this.selectDataTypeVisible = false
      if (type === 'file') {
        this.$router.push({ name: 'data-analysis-data-import-import-data' })
      } else {
        this.openCreateDataSourceDialog()
      }
    },
    createDatabase(formName) {
      const vue = this
      this.$refs[formName].validate(valid => {
        if (valid) {
          vue.loading = true
          var requestData = new FormData()
          requestData.append('userId', this.userDetails.id)
          requestData.append('dbName', vue.databaseForm.databaseName)
          createDatabase(requestData)
            .then(() => {
              vue.$message({
                showClose: true,
                message: vue.$t('soi.dataImport.createDatabaseSuccess'),
                type: 'success'
              })
              vue.isExistDB = true
            })
            .finally(() => {
              vue.loading = false
            })
        }
      })
    },
    testDB() {
      const vue = this
      this.$refs['DBForm'].validate((valid) => {
        if (valid) {
          vue.loading = true
          const requestData = {
            'username': vue.form.DBUsername,
            'password': vue.form.DBPassword,
            'url': `jdbc:mysql://${vue.form.DBHost}:${vue.form.DBPort}?useUnicode=true&characterEncoding=UTF-8&serverTimezone=Asia/Shanghai`
          }
          testDataSourceCollect(requestData)
            .then((res) => {
              vue.$message({
                type: 'success',
                message: res.message
              })
            })
            .finally(() => {
              vue.loading = false
            })
        }
      })
    },
    addDB() {
      this.pageType = 'create'
      const vue = this
      this.$refs['DBForm'].validate((valid) => {
        if (valid) {
          this.$prompt(vue.$t('soi.dataVisualization.dataSourceNameTip'), vue.$t('soi.common.tip'), {
            confirmButtonText: vue.$t('soi.common.confirm'),
            cancelButtonText: vue.$t('soi.common.cancel'),
            inputPattern: /^[a-zA-Z0-9_\-]{1,100}$/,
            inputValue: '',
            inputErrorMessage: vue.$t('soi.dataVisualization.chartNameError')
          }).then(({ value }) => {
            vue.loading = true
            const requestData = {
              'name': value,
              'description': vue.form.DBDescription,
              'config': {
                'username': vue.form.DBUsername,
                'password': vue.form.DBPassword,
                'url': `jdbc:mysql://${vue.form.DBHost}:${vue.form.DBPort}?useUnicode=true&characterEncoding=UTF-8&serverTimezone=Asia/Shanghai`
              },
              'userId': this.userDetails.id
            }

            addDataSource(requestData)
              .then((res) => {
                vue.$message({
                  type: 'success',
                  message: res.message
                })

                vue.init()
              })
              .finally(() => {
                vue.loading = false
                vue.createDataSourceDialog = false
              })
          })
        }
      })
    },
    updateDB() {
      const vue = this
      this.$refs['DBForm'].validate((valid) => {
        if (valid) {
          this.$prompt(vue.$t('soi.dataVisualization.dataSourceNameTip'), vue.$t('soi.common.tip'), {
            confirmButtonText: vue.$t('soi.common.confirm'),
            cancelButtonText: vue.$t('soi.common.cancel'),
            inputPattern: /^[a-zA-Z0-9_\-]{1,100}$/,
            inputValue: vue.form.name,
            inputErrorMessage: vue.$t('soi.dataVisualization.chartNameError')
          }).then(({ value }) => {
            vue.loading = true
            const requestData = {
              'id': vue.datasourceId,
              'name': value,
              'description': vue.form.DBDescription,
              'config': JSON.stringify({
                'username': vue.form.DBUsername,
                'password': vue.form.DBPassword,
                'url': `jdbc:mysql://${vue.form.DBHost}:${vue.form.DBPort}?useUnicode=true&characterEncoding=UTF-8&serverTimezone=Asia/Shanghai`
              }),
              'userId': vue.userDetails.id
            }

            updateDataSource(requestData)
              .then((res) => {
                vue.$message({
                  type: 'success',
                  message: res.message
                })

                vue.init()
              })
              .finally(() => {
                vue.loading = false
                vue.dialogEditVisible = false
              })
          })
        }
      })
    },
    deleteDatasource(row) {
      const vue = this
      this.$confirm(vue.$t('soi.dataVisualization.deleteDataSourceTip'), vue.$t('soi.common.tip'), {
        confirmButtonText: vue.$t('soi.common.confirm'),
        cancelButtonText: vue.$t('soi.common.cancel'),
        type: 'warning'
      }).then(() => {
        vue.loading = true
        deleteDataSource(row.id, this.userDetails.id)
          .then((res) => {
            vue.$message({
              type: 'success',
              message: res.message
            })

            vue.init()
          })
          .finally(() => {
            vue.loading = false
          })
      })
    },
    handlerCreateDataTable(row) {
      this.$router.push({ name: 'data-analysis-data-visualization-data-table-form', query: { sourceId: row.id }})
    },
    synchronizeSandboxData() {
      const vue = this
      let isExistDB = true
      vue.customDataSource.forEach((item) => {
        if (!item.publicSource && !item.type) {
          isExistDB = false
          vue.syncDataLoading = true
          synchronizeData(vue.userDetails.id, item.name)
            .then((res) => {
              vue.$message({
                type: 'success',
                message: res.message
              })
            })
            .finally(() => {
              vue.syncDataLoading = false
            })
        }
      })
      if (isExistDB) {
        vue.$confirm(vue.$t('soi.dataVisualization.hasNoDataSourceTip'), vue.$t('soi.common.tip'), {
          confirmButtonText: vue.$t('soi.common.confirm'),
          cancelButtonText: vue.$t('soi.common.cancel'),
          type: 'warning'
        }).then(() => {
          vue.createDatabaseDialog = true
        }).catch(() => {})
      }
    },
    openCreateDataSourceDialog() {
      this.createDataSourceDialog = true
    },
    editDataSource(row) {
      this.pageType = 'update'
      const vue = this
      vue.loading = true

      getDataSourceInfo(row.id, this.userDetails.id)
        .then((res) => {
          const result = res.data
          let jdbcUrl = JSON.parse(result.config).url.substr(13)
          if (jdbcUrl.indexOf('?') !== -1) {
            // 去除url参数
            jdbcUrl = jdbcUrl.substr(0, jdbcUrl.indexOf('?'))
          }
          // 获取host
          const urlAndPortName = jdbcUrl.split(':')
          vue.form.DBHost = urlAndPortName[0]
          // 获取port 和 databaseName
          vue.form.DBPort = urlAndPortName[1]
          vue.datasourceId = result.id
          vue.form.DBUsername = JSON.parse(result.config).username
          vue.form.DBPassword = JSON.parse(result.config).password
          vue.form.name = result.name
        })
        .finally(() => {
          vue.loading = false
          vue.dialogEditVisible = true
        })
    },
    tableRowClassName({ row }) {
      if (row.id === 'PublicDataSource') {
        return 'public-datasource'
      } else if (row.id === 'CustomDataSource') {
        return 'custom-datasource'
      }
      return ''
    },
    // 通过ref属性重置表单
    resetForm(formName) {
      this.$refs[formName].resetFields()
    }
  }
}
</script>

<style lang="scss" scoped>
.data-visualization-menu {
  text-align: right;
  margin-bottom: 10px;
}
</style>

<style>
.el-table .public-datasource {
  background: oldlace;
  color: #000000;
}

.el-table .custom-datasource {
  background: #f0f9eb;
  color: #000000;
}
</style>
