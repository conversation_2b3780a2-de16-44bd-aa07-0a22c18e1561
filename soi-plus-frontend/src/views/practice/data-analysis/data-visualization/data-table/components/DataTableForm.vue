<template>
  <div class="data-visualization-data-table-form-container">
    <customize-card :title="$t('soi.dataVisualization.createDataTable')">
      <data-visualization-data-table-form-content />
    </customize-card>
  </div>
</template>
<script>
import CustomizeCard from '@/components/CustomizeCard/index.vue'
import DataVisualizationDataTableFormContent
  from '@/views/practice/data-analysis/data-visualization/data-table/components/DataTableFormContent.vue'

export default {
  name: 'DataVisualizationDataTableForm',
  components: { DataVisualizationDataTableFormContent, CustomizeCard },
  data() {
    return {
    }
  }
}
</script>
