<template>
  <div v-loading="loading" class="data-visualization-foreign-exchange-data-enquiry-container">
    <customize-card :title="$t('soi.router.foreignExchangeDataEnquiry')">
      <!-- 搜索表单 -->
      <el-form ref="searchForm" :model="searchForm" size="small" label-width="130px" label-position="left">
        <el-row :gutter="20">
          <!--          <el-col :span="8">
            &lt;!&ndash; 数据表名称 &ndash;&gt;
            <el-form-item :label="$t('soi.foreignExchangeEnquiry.tableName')" prop="tableName">
              <el-select v-model="searchForm.tableName" :placeholder="$t('soi.foreignExchangeEnquiry.tableNamePlaceholder')" clearable class="search-form-item">
                <el-option
                  v-for="item in tableNameList"
                  :key="item"
                  :label="item"
                  :value="item"
                />
              </el-select>
            </el-form-item>
          </el-col>-->
          <el-col :span="8">
            <!-- 创建时间范围 -->
            <el-form-item :label="$t('soi.common.createDate')" prop="dateRange">
              <el-date-picker
                v-model="searchForm.dateRange"
                type="daterange"
                :range-separator="$t('soi.common.to')"
                value-format="timestamp"
                :start-placeholder="$t('soi.common.startDate')"
                :end-placeholder="$t('soi.common.endDate')"
                :default-time="['00:00:00', '23:59:59']"
                unlink-panels
                class="search-form-item"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <!-- 国家代码 -->
            <el-form-item :label="$t('soi.foreignExchangeEnquiry.countryCode')" prop="countryCode">
              <el-input v-model="searchForm.countryCode" :placeholder="$t('soi.foreignExchangeEnquiry.countryCodePlaceholder')" clearable class="search-form-item" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <!-- 银行编号 -->
            <el-form-item :label="$t('soi.foreignExchangeEnquiry.clearingCode')" prop="clearingCode">
              <el-input v-model="searchForm.clearingCode" :placeholder="$t('soi.foreignExchangeEnquiry.clearingCodePlaceholder')" clearable class="search-form-item" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <!-- 分行代码 -->
            <el-form-item :label="$t('soi.foreignExchangeEnquiry.branchCode')" prop="branchCode">
              <el-input v-model="searchForm.branchCode" :placeholder="$t('soi.foreignExchangeEnquiry.branchCodePlaceholder')" clearable class="search-form-item" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <!-- 账号 -->
            <el-form-item :label="$t('soi.foreignExchangeEnquiry.accountNumber')" prop="accountNumber">
              <el-input v-model="searchForm.accountNumber" :placeholder="$t('soi.foreignExchangeEnquiry.accountNumberPlaceholder')" clearable class="search-form-item" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <!-- 操作按钮 -->
            <el-button type="primary" icon="el-icon-search" size="small" @click="getForeignExchangeList()">{{ $t('soi.common.search') }}</el-button>
            <el-button icon="el-icon-refresh" size="small" @click="resetForm('searchForm')">{{ $t('soi.common.reset') }}</el-button>
          </el-col>
        </el-row>
      </el-form>

      <!-- 创建和批量删除按钮 -->
      <div class="data-action-button-group">
        <div />

        <!-- 导出按钮 -->
        <el-button icon="el-icon-download" size="small" circle @click="exportList()" />
        <!-- 刷新按钮 -->
        <el-button icon="el-icon-refresh" size="small" circle @click="getForeignExchangeList()" />
      </div>

      <!-- 外汇数据表格 -->
      <el-card shadow="never">
        <el-table v-loading="tableLoading" :data="foreignExchangeList" stripe>
          <el-table-column type="index" label="#" align="center" />
          <el-table-column prop="countryCode" :label="$t('soi.foreignExchangeEnquiry.countryCode')" align="center" width="120" />
          <el-table-column prop="clearingCode" :label="$t('soi.foreignExchangeEnquiry.clearingCode')" align="center" width="120" />
          <el-table-column prop="branchCode" :label="$t('soi.foreignExchangeEnquiry.branchCode')" align="center" width="120" />
          <el-table-column prop="accountNumber" :label="$t('soi.foreignExchangeEnquiry.accountNumber')" align="center" width="210" />
          <el-table-column prop="localCurrency" :label="$t('soi.foreignExchangeEnquiry.localCurrency')" align="center" width="140" />
          <el-table-column prop="foreignCurrency" :label="$t('soi.foreignExchangeEnquiry.foreignCurrency')" align="center" width="140" />
          <el-table-column prop="exchangeRate" :label="$t('soi.foreignExchangeEnquiry.exchangeRate')" align="center" width="140" />
          <el-table-column prop="exchangeAmoutInLocalCurrency" :label="$t('soi.foreignExchangeEnquiry.exchangeAmoutInLocalCurrency')" align="center" width="300" />
          <el-table-column prop="exchangeAmoutInForeignCurrency" :label="$t('soi.foreignExchangeEnquiry.exchangeAmoutInForeignCurrency')" align="center" width="300" />
          <el-table-column prop="transactionTime" :label="$t('soi.foreignExchangeEnquiry.transactionTime')" align="center" width="150">
            <template slot-scope="scope">
              {{ moment(Number(scope.row.transactionTime)).format('YYYY-MM-DD HH:mm:ss') }}
            </template>
          </el-table-column>
          <el-table-column prop="transactionType" :label="$t('soi.foreignExchangeEnquiry.transactionType')" align="center" width="140" />
          <el-table-column :label="$t('soi.common.operate')" align="center" fixed="right" width="150">
            <template slot-scope="scope">
              <el-button size="mini" type="text" icon="el-icon-edit" @click="openEditDialog(scope.row)">{{ $t('soi.common.edit') }}</el-button>
              <el-button size="mini" type="text" icon="el-icon-view" @click="openViewDialog(scope.row)">{{ $t('soi.common.view') }}</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>

      <div class="table-footer">
        <el-pagination
          :current-page.sync="searchForm.page"
          :page-sizes="[20, 50, 100, 500, 1000]"
          :page-size.sync="searchForm.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="getForeignExchangeList()"
          @current-change="getForeignExchangeList()"
        />
      </div>
    </customize-card>

    <!-- 查看的对话框 -->
    <el-dialog
      :title="$t('soi.foreignExchangeView.title')"
      :visible.sync="viewDialogVisible"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      width="60%"
      top="50px"
    >
      <descriptions :view-row="viewRow" />
    </el-dialog>

    <!-- 更新用户的对话框 -->
    <el-dialog
      :title="$t('soi.foreignExchangeUpdate.title')"
      :visible.sync="updateDialogVisible"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      width="60%"
    >
      <customize-form
        ref="customizeForm"
        form-ref="creditCardDataForm"
        :model="updateData"
        :form-item-config="formConfig"
        :rules="{}"
        label-width="275px"
        label-position="left"
      />

      <span slot="footer" class="dialog-footer">
        <el-button @click="updateDialogVisible = false"> {{ $t('soi.common.cancel') }}</el-button>
        <el-button type="primary" :loading="updateButtonLoading" @click="handlerUpdateData()">{{ $t('soi.common.confirm') }}</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import CustomizeCard from '@/components/CustomizeCard/index.vue'
import { getTableList, updateForeignExchange } from '@/api/practice/data-creation'
import { enquiryForeignExchangeByPage } from '@/api/practice/data-enquiry'
import moment from 'moment'
import Descriptions from '@/views/practice/data-analysis/data-enquiry/components/Descriptions.vue'
import CustomizeForm from '@/components/CustomizeForm/CustomizeForm.vue'
import { formConfig } from '@/views/practice/data-analysis/data-enquiry/foreign-exchange/form-config'
import { mapGetters } from 'vuex'
import { exportForeignExchangeList } from '@/api/practice/date-export'

export default {
  name: 'ForeignExchangeDataEnquiry',
  components: { CustomizeForm, Descriptions, CustomizeCard },
  data() {
    return {
      loading: false,
      moment,
      loadingCount: 0,
      // 表格loading
      tableLoading: false,
      formConfig,
      updateDialogVisible: false,
      updateButtonLoading: false,
      updateData: {},
      createKey: '',
      updateKey: '',
      total: 0,
      viewDialogVisible: false,
      viewRow: [],
      // 分页查询请求参数
      searchForm: {
        page: 1,
        pageSize: 20,
        dateRange: [],
        tableName: '',
        fromCreateDate: '',
        toCreateDate: '',
        countryCode: '',
        clearingCode: '',
        branchCode: '',
        accountNumber: '',
        userId: ''
      },
      // 外汇数据列表
      foreignExchangeList: [],
      tableNameList: [],
      // 更新外汇数据的对话框标记：true 显示，false 隐藏
      updateForeignExchangeDialogVisible: false,
      updateForeignExchangeId: '',
      multipleSelection: []
    }
  },
  computed: {
    ...mapGetters(['userDetails'])
  },
  mounted() {
    this.initTableName()
  },
  methods: {
    openEditDialog(row) {
      this.updateData = JSON.parse(JSON.stringify({ ...row, tableName: this.searchForm.tableName }))
      this.updateDialogVisible = true
    },
    handlerUpdateData() {
      this.$refs.customizeForm.validate((valid, formData) => {
        if (valid) {
          this.updateButtonLoading = true
          updateForeignExchange(formData)
            .then((res) => {
              this.$message.success(res.message)
              this.getForeignExchangeList()
              this.updateDialogVisible = false
            })
            .finally(() => {
              this.updateButtonLoading = false
            })
        }
      })
    },
    // 打开查看对话框
    openViewDialog(row) {
      const keyJson = formConfig.map(item => item.prop)
      keyJson.unshift('id')
      this.viewRow = keyJson.map(key => {
        const value = row[key]
        if ((key.toLowerCase().includes('date') || key.toLowerCase().includes('time')) && !isNaN(value)) {
          const formattedValue = moment(Number(value)).format('YYYY-MM-DD HH:mm:ss')
          return { key: this.$t(`soi.dataEnquiryDetails.${key}`), value: formattedValue }
        } else {
          return { key: this.$t(`soi.dataEnquiryDetails.${key}`), value: value }
        }
      })
      this.viewDialogVisible = true
    },
    getForeignExchangeList() {
      this.tableLoading = true

      const requestData = { ...this.searchForm, userId: this.userDetails.id }

      if (this.searchForm.dateRange) {
        requestData.fromCreateDate = this.searchForm.dateRange[0]
        requestData.toCreateDate = this.searchForm.dateRange[1]
      }

      enquiryForeignExchangeByPage(requestData)
        .then((res) => {
          const { totalCount, foreignExchangeList } = res.data

          this.foreignExchangeList = foreignExchangeList
          this.total = totalCount
        })
        .finally(() => {
          this.tableLoading = false
        })
    },
    openUpdateForeignExchangeDialog(id) {},
    initTableName() {
      const _this = this
      _this.startLoading()
      getTableList({ type: 'Fex' })
        .then(function(res) {
          _this.tableNameList = res.data
          if (_this.tableNameList && _this.tableNameList.length) {
            _this.searchForm.tableName = _this.tableNameList[0]
            _this.getForeignExchangeList()
          }
        })
        .catch(() => {
          _this.tableNameList = []
        })
        .finally(() => {
          _this.endLoading()
        })
    },
    startLoading() {
      this.loading = true

      this.loadingCount++
    },
    endLoading() {
      this.loadingCount--

      if (this.loadingCount <= 0) {
        setTimeout(() => {
          this.loading = false
        }, 100)
      }
    },
    // 通过ref属性重置表单
    resetForm(formName) {
      this.$refs[formName].resetFields()
    },
    exportList() {
      const requestData = { ...this.searchForm, userId: this.userDetails.id }

      if (this.searchForm.dateRange.length) {
        requestData.fromCreateDate = this.searchForm.dateRange[0]
        requestData.toCreateDate = this.searchForm.dateRange[1]
      }
      exportForeignExchangeList(requestData)
    }
  }
}
</script>

<style scoped lang="scss">
.data-visualization-foreign-exchange-data-enquiry-container {
  .data-action-button-group {
    margin-bottom: 10px;
    display: flex;
    justify-content: right;
  }

  .search-form-item {
    width: 100%;
  }
}
</style>
