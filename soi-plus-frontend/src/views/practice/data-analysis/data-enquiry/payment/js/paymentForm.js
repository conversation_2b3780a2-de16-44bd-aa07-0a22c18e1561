export const paymentFormJson = () => {
  return [
    {
      'label': 'soi.paymentUpdate.payeeCategoryID',
      'placeholder': 'soi.paymentUpdate.payeeCategoryIDPlaceholder',
      'prop': 'payeeCategoryID',
      'component': 'el-input',
      'span': 12,
      'clearable': true
    },
    {
      'label': 'soi.paymentUpdate.payeeCategory',
      'placeholder': 'soi.paymentUpdate.payeeCategoryPlaceholder',
      'prop': 'payeeCategory',
      'component': 'el-input',
      'span': 12,
      'clearable': true
    },
    {
      'label': 'soi.paymentUpdate.payeeID',
      'placeholder': 'soi.paymentUpdate.payeeIDPlaceholder',
      'prop': 'payeeID',
      'component': 'el-input',
      'span': 12,
      'clearable': true
    },
    {
      'label': 'soi.paymentUpdate.payeeNumber',
      'placeholder': 'soi.paymentUpdate.payeeNumberPlaceholder',
      'prop': 'payeeNumber',
      'component': 'el-input',
      'span': 12,
      'clearable': true
    },
    {
      'label': 'soi.paymentUpdate.transactionDealNumber',
      'placeholder': 'soi.paymentUpdate.transactionDealNumberPlaceholder',
      'prop': 'transactionDealNumber',
      'component': 'el-input',
      'span': 12,
      'clearable': true
    },
    {
      'label': 'soi.paymentUpdate.customerAccountType',
      'placeholder': 'soi.paymentUpdate.customerAccountTypePlaceholder',
      'prop': 'customerAccountType',
      'component': 'el-input',
      'span': 12,
      'clearable': true
    },
    {
      'label': 'soi.paymentUpdate.customerAccountNumber',
      'placeholder': 'soi.paymentUpdate.customerAccountNumberPlaceholder',
      'prop': 'customerAccountNumber',
      'component': 'el-input',
      'span': 12,
      'clearable': true
    },
    {
      'label': 'soi.paymentUpdate.transactionTime',
      'placeholder': 'soi.paymentUpdate.transactionTimePlaceholder',
      'prop': 'transactionTime',
      'component': 'el-date-picker',
      'clearable': true,
      'type': 'datetime',
      'format': 'yyyy-MM-dd HH:mm:ss',
      'valueFormat': 'timestamp',
      'span': 12,
      'width': '100%'
    },
    {
      'label': 'soi.paymentUpdate.transactionAmount',
      'placeholder': 'soi.paymentUpdate.transactionAmountPlaceholder',
      'prop': 'transactionAmount',
      'component': 'el-input-number',
      'width': '100%',
      'span': 12,
      'min': 0,
      'step': 0.00001,
      'step-strictly': true,
      'controls': false,
      'precision': 5,
      'clearable': true
    },
    {
      'label': 'soi.paymentUpdate.transactionCurrency',
      'placeholder': 'soi.paymentUpdate.transactionCurrencyPlaceholder',
      'prop': 'transactionCurrency',
      'component': 'el-input',
      'span': 12,
      'clearable': true
    },
    {
      'label': 'soi.paymentUpdate.remarks',
      'placeholder': 'soi.paymentUpdate.remarksPlaceholder',
      'prop': 'remarks',
      'component': 'el-input',
      'span': 12,
      'clearable': true
    },
    {
      'label': 'soi.paymentUpdate.lastUpdateDate',
      'placeholder': 'soi.paymentUpdate.lastUpdateDatePlaceholder',
      'prop': 'lastUpdateDate',
      'component': 'el-date-picker',
      'clearable': true,
      'type': 'datetime',
      'format': 'yyyy-MM-dd HH:mm:ss',
      'valueFormat': 'timestamp',
      'span': 12,
      'width': '100%'
    },
    {
      'label': 'soi.paymentUpdate.createDate',
      'placeholder': 'soi.paymentUpdate.createDatePlaceholder',
      'prop': 'createDate',
      'component': 'el-date-picker',
      'clearable': true,
      'type': 'datetime',
      'format': 'yyyy-MM-dd HH:mm:ss',
      'valueFormat': 'timestamp',
      'span': 12,
      'width': '100%'
    },
    {
      'label': 'soi.paymentUpdate.status',
      'placeholder': 'soi.paymentUpdate.statusPlaceholder',
      'prop': 'status',
      'component': 'el-input',
      'span': 12,
      'clearable': true
    }
  ]
}
export const paymentFormRules = {}
