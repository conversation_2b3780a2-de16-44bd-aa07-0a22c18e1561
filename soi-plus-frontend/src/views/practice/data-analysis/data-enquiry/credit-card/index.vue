<template>
  <div v-loading="loading" class="data-visualization-credit-card-data-enquiry-container">
    <customize-card :title="$t('soi.router.creditCardDataEnquiry')">
      <!-- 搜索表单 -->
      <el-form ref="searchForm" :rules="formRules" :model="searchForm" size="small" label-width="190px" label-position="left">
        <el-row :gutter="20">
          <!--          <el-col :span="8">
            &lt;!&ndash; 数据表名称 &ndash;&gt;
            <el-form-item :label="$t('soi.creditCardEnquiry.tableName')" prop="tableName">
              <el-select v-model="searchForm.tableName" :placeholder="$t('soi.creditCardEnquiry.tableNamePlaceholder')" clearable class="search-form-item">
                <el-option
                  v-for="item in tableNameList"
                  :key="item"
                  :label="item"
                  :value="item"
                />
              </el-select>
            </el-form-item>
          </el-col>-->
          <el-col :span="8">
            <!-- 创建时间范围 -->
            <el-form-item :label="$t('soi.creditCardEnquiry.createDateRange')" prop="dateRange">
              <el-date-picker
                v-model="searchForm.dateRange"
                type="daterange"
                :range-separator="$t('soi.common.to')"
                value-format="timestamp"
                :start-placeholder="$t('soi.common.startDate')"
                :end-placeholder="$t('soi.common.endDate')"
                :default-time="['00:00:00', '23:59:59']"
                unlink-panels
                class="search-form-item"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <!-- 国家代码 -->
            <el-form-item :label="$t('soi.creditCardEnquiry.countryCode')" prop="countryCode">
              <el-input v-model="searchForm.countryCode" :placeholder="$t('soi.creditCardEnquiry.countryCodePlaceholder')" clearable class="search-form-item" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <!-- 银行编号 -->
            <el-form-item :label="$t('soi.creditCardEnquiry.clearingCode')" prop="clearingCode">
              <el-input v-model="searchForm.clearingCode" :placeholder="$t('soi.creditCardEnquiry.clearingCodePlaceholder')" clearable class="search-form-item" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <!-- 分行代码 -->
            <el-form-item :label="$t('soi.creditCardEnquiry.branchCode')" prop="branchCode">
              <el-input v-model="searchForm.branchCode" :placeholder="$t('soi.creditCardEnquiry.branchCodePlaceholder')" clearable class="search-form-item" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <!-- 起始交易金额 -->
            <el-form-item :label="$t('soi.creditCardEnquiry.fromTransactionAmount')" prop="fromTransactionAmount">
              <el-input v-model="searchForm.fromTransactionAmount" :placeholder="$t('soi.creditCardEnquiry.fromTransactionAmountPlaceholder')" clearable class="search-form-item" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <!-- 终止交易金额 -->
            <el-form-item :label="$t('soi.creditCardEnquiry.toTransactionAmount')" prop="toTransactionAmount">
              <el-input v-model="searchForm.toTransactionAmount" :placeholder="$t('soi.creditCardEnquiry.toTransactionAmountPlaceholder')" clearable class="search-form-item" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <!-- 信用卡号 -->
            <el-form-item :label="$t('soi.creditCardEnquiry.creditCardNumber')" prop="creditCardNumber">
              <el-input v-model="searchForm.creditCardNumber" :placeholder="$t('soi.creditCardEnquiry.creditCardNumberPlaceholder')" clearable class="search-form-item" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-button type="primary" icon="el-icon-search" size="small" @click="getCreditCardList()">{{ $t('soi.common.search') }}</el-button>
            <el-button icon="el-icon-refresh" size="small" @click="resetForm('searchForm')">{{ $t('soi.common.reset') }}</el-button>
          </el-col>
        </el-row>
      </el-form>

      <!-- 创建和批量删除按钮 -->
      <div class="data-action-button-group">
        <div />
        <!-- 导出按钮 -->
        <el-button icon="el-icon-download" size="small" circle @click="exportList()" />
        <!-- 刷新按钮 -->
        <el-button icon="el-icon-refresh" size="small" circle @click="getCreditCardList()" />
      </div>

      <!-- 信用卡数据表格 -->
      <el-card shadow="never">
        <el-table v-loading="tableLoading" :data="creditCardList" stripe>
          <el-table-column type="index" label="#" align="center" />
          <el-table-column prop="countryCode" :label="$t('soi.creditCardEnquiry.countryCode')" align="center" width="120" />
          <el-table-column prop="clearingCode" :label="$t('soi.creditCardEnquiry.clearingCode')" align="center" width="120" />
          <el-table-column prop="branchCode" :label="$t('soi.creditCardEnquiry.branchCode')" align="center" width="120" />
          <el-table-column prop="creditCardNumber" :label="$t('soi.creditCardEnquiry.creditCardNumber')" align="center" width="155" />
          <el-table-column prop="creditCardType" :label="$t('soi.creditCardEnquiry.creditCardType')" align="center" width="135" />
          <el-table-column prop="dealNumber" :label="$t('soi.creditCardEnquiry.dealNumber')" align="center" width="155" />
          <el-table-column prop="merchantName" :label="$t('soi.creditCardEnquiry.merchantName')" align="center" width="180" />
          <el-table-column prop="merchantNumber" :label="$t('soi.creditCardEnquiry.merchantNumber')" align="center" width="210" />
          <el-table-column prop="transactionAmount" :label="$t('soi.creditCardEnquiry.transactionAmount')" align="center" width="155" />
          <el-table-column prop="transactionCcy" :label="$t('soi.creditCardEnquiry.transactionCcy')" align="center" width="130" />
          <el-table-column prop="transactionTime" :label="$t('soi.creditCardEnquiry.transactionTime')" align="center" width="150">
            <template slot-scope="scope">
              {{ moment(Number(scope.row.transactionTime)).format('YYYY-MM-DD HH:mm:ss') }}
            </template>
          </el-table-column>
          <el-table-column prop="transactionType" :label="$t('soi.creditCardEnquiry.transactionType')" align="center" width="140" />
          <el-table-column :label="$t('soi.common.operate')" align="center" fixed="right" width="150">
            <template slot-scope="scope">
              <el-button size="mini" type="text" icon="el-icon-edit" @click="openEditDialog(scope.row)">{{ $t('soi.common.edit') }}</el-button>
              <el-button size="mini" type="text" icon="el-icon-view" @click="openViewDialog(scope.row)">{{ $t('soi.common.view') }}</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>

      <div class="table-footer">
        <el-pagination
          :current-page.sync="searchForm.page"
          :page-sizes="[20, 50, 100, 500, 1000]"
          :page-size.sync="searchForm.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="getCreditCardList()"
          @current-change="getCreditCardList()"
        />
      </div>
    </customize-card>

    <!-- 查看的对话框 -->
    <el-dialog
      :title="$t('soi.creditCardView.title')"
      :visible.sync="viewDialogVisible"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      width="60%"
      top="50px"
    >
      <descriptions :view-row="viewRow" />
    </el-dialog>

    <!-- 更新用户的对话框 -->
    <el-dialog
      :title="$t('soi.creditCardUpdate.title')"
      :visible.sync="updateDialogVisible"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      width="60%"
    >
      <customize-form
        ref="customizeForm"
        form-ref="creditCardDataForm"
        :model="updateData"
        :form-item-config="formConfig"
        :rules="{}"
        label-width="160px"
        label-position="left"
      />

      <span slot="footer" class="dialog-footer">
        <el-button @click="updateDialogVisible = false"> {{ $t('soi.common.cancel') }}</el-button>
        <el-button type="primary" :loading="updateButtonLoading" @click="handlerUpdateData()">{{ $t('soi.common.confirm') }}</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import CustomizeCard from '@/components/CustomizeCard/index.vue'
import { getTableList, updateCreditCard } from '@/api/practice/data-creation'
import { enquiryCreditCardByPage } from '@/api/practice/data-enquiry'
import moment from 'moment'
import Descriptions from '@/views/practice/data-analysis/data-enquiry/components/Descriptions.vue'
import CustomizeForm from '@/components/CustomizeForm/CustomizeForm.vue'
import { formConfig } from '@/views/practice/data-analysis/data-enquiry/credit-card/form-config'
import { mapGetters } from 'vuex'
import { exportCreditCardList } from '@/api/practice/date-export'

export default {
  name: 'CreditCardDataEnquiry',
  components: { CustomizeForm, Descriptions, CustomizeCard },
  data() {
    return {
      // 页面loading
      loading: false,
      moment,
      formConfig,
      loadingCount: 0,
      // 表格loading
      tableLoading: false,
      viewDialogVisible: false,
      updateButtonLoading: false,
      updateDialogVisible: false,
      viewRow: [],
      createKey: '',
      updateKey: '',
      total: 0,
      // 分页查询请求参数
      searchForm: {
        page: 1,
        pageSize: 20,
        dateRange: [],
        tableName: '',
        fromCreateDate: '',
        toCreateDate: '',
        countryCode: '',
        clearingCode: '',
        branchCode: '',
        creditCardNumber: '',
        fromTransactionAmount: '',
        toTransactionAmount: '',
        userId: ''
      },
      // 信用卡数据列表
      creditCardList: [],
      tableNameList: [],
      // 更新信用卡数据的对话框标记：true 显示，false 隐藏
      updateCreditCardDialogVisible: false,
      updateCreditCardId: '',
      updateData: {},
      formRules: {
        toTransactionAmount: [
          { validator: this.fromAndToValidator('fromTransactionAmount', 'toTransactionAmount'), trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    ...mapGetters(['userDetails'])
  },
  mounted() {
    this.initTableName()
  },
  methods: {
    openEditDialog(row) {
      this.updateData = JSON.parse(JSON.stringify({ ...row, tableName: this.searchForm.tableName }))
      this.updateDialogVisible = true
    },
    // 打开查看对话框
    openViewDialog(row) {
      const keyJson = formConfig.map(item => item.prop)
      keyJson.unshift('id')
      this.viewRow = keyJson.map(key => {
        const value = row[key]
        if (key.toLowerCase().includes('date') && !isNaN(value)) {
          const formattedValue = moment(Number(value)).format('YYYY-MM-DD HH:mm:ss')
          return { key: this.$t(`soi.dataEnquiryDetails.${key}`), value: formattedValue }
        } else {
          return { key: this.$t(`soi.dataEnquiryDetails.${key}`), value: value }
        }
      })
      this.viewDialogVisible = true
    },
    handlerUpdateData() {
      this.$refs.customizeForm.validate((valid, formData) => {
        if (valid) {
          this.updateButtonLoading = true
          updateCreditCard(formData)
            .then((res) => {
              this.$message.success(res.message)
              this.getCreditCardList()
              this.updateDialogVisible = false
            })
            .finally(() => {
              this.updateButtonLoading = false
            })
        }
      })
    },
    getCreditCardList() {
      this.$refs['searchForm'].validate((valid) => {
        if (valid) {
          this.tableLoading = true

          const requestData = { ...this.searchForm, userId: this.userDetails.id }

          if (this.searchForm.dateRange) {
            requestData.fromCreateDate = this.searchForm.dateRange[0]
            requestData.toCreateDate = this.searchForm.dateRange[1]
          }

          enquiryCreditCardByPage(requestData)
            .then((res) => {
              const { totalCount, creditcardTransactionDetailList } = res.data

              this.creditCardList = creditcardTransactionDetailList
              this.total = totalCount
            })
            .finally(() => {
              this.tableLoading = false
            })
        } else {
          return false
        }
      })
    },
    initTableName() {
      const _this = this
      _this.startLoading()
      getTableList({ type: 'CreditCard' })
        .then(function(res) {
          _this.tableNameList = res.data
          if (_this.tableNameList && _this.tableNameList.length) {
            _this.searchForm.tableName = _this.tableNameList[0]
            _this.getCreditCardList()
          }
        })
        .catch(() => {
          _this.tableNameList = []
        })
        .finally(() => {
          _this.endLoading()
        })
    },
    startLoading() {
      this.loading = true

      this.loadingCount++
    },
    endLoading() {
      this.loadingCount--

      if (this.loadingCount <= 0) {
        setTimeout(() => {
          this.loading = false
        }, 100)
      }
    },
    openUpdateCreditCardDialog(id) {},
    // 通过ref属性重置表单
    resetForm(formName) {
      this.$refs[formName].resetFields()
    },
    exportList() {
      const requestData = { ...this.searchForm, userId: this.userDetails.id }
      if (this.searchForm.dateRange.length) {
        requestData.fromCreateDate = this.searchForm.dateRange[0]
        requestData.toCreateDate = this.searchForm.dateRange[1]
      }
      exportCreditCardList(requestData)
    },
    fromAndToValidator(fromField, toField) {
      return (rule, value, callback) => {
        if (this.searchForm[toField] && this.searchForm[fromField] &&
          (Number(this.searchForm[toField]) < Number(this.searchForm[fromField]))
        ) {
          callback(
            new Error(
              this.$t(
                'soi.creditCardEnquiry.numberGreaterThanPlaceholder',
                {
                  form: this.$t(`soi.creditCardEnquiry.${fromField}`),
                  to: this.$t(`soi.creditCardEnquiry.${toField}`)
                }
              )
            )
          )
        } else {
          callback()
        }
      }
    }
  }
}
</script>

<style scoped lang="scss">
.data-visualization-credit-card-data-enquiry-container {
  .data-action-button-group {
    margin-bottom: 10px;
    display: flex;
    justify-content: right;
  }

  .search-form-item {
    width: 100%;
  }
}
</style>
