export const formConfig = [
  {
    'label': 'soi.creditCardUpdate.countryCode',
    'placeholder': 'soi.creditCardUpdate.countryCodePlaceholder',
    'prop': 'countryCode',
    'component': 'el-input',
    'span': 12,
    'width': '100%',
    'disabled': true,
    'clearable': true
  },
  {
    'label': 'soi.creditCardUpdate.clearingCode',
    'placeholder': 'soi.creditCardUpdate.clearingCodePlaceholder',
    'prop': 'clearingCode',
    'component': 'el-input',
    'span': 12,
    'width': '100%',
    'disabled': true,
    'clearable': true
  },
  {
    'label': 'soi.creditCardUpdate.branchCode',
    'placeholder': 'soi.creditCardUpdate.branchCodePlaceholder',
    'prop': 'branchCode',
    'component': 'el-input',
    'span': 12,
    'width': '100%',
    'disabled': true,
    'clearable': true
  },
  {
    'label': 'soi.creditCardUpdate.creditCardNumber',
    'placeholder': 'soi.creditCardUpdate.creditCardNumberPlaceholder',
    'prop': 'creditCardNumber',
    'component': 'el-input',
    'span': 12,
    'width': '100%',
    'disabled': true,
    'clearable': true
  },
  {
    'label': 'soi.creditCardUpdate.creditCardType',
    'placeholder': 'soi.creditCardUpdate.creditCardTypePlaceholder',
    'prop': 'creditCardType',
    'component': 'el-input',
    'span': 12,
    'width': '100%',
    'disabled': true,
    'clearable': true
  },
  {
    'label': 'soi.creditCardUpdate.dealNumber',
    'placeholder': 'soi.creditCardUpdate.dealNumberPlaceholder',
    'prop': 'dealNumber',
    'component': 'el-input',
    'span': 12,
    'width': '100%',
    'disabled': true,
    'clearable': true
  },
  {
    'label': 'soi.creditCardUpdate.transactionTime',
    'placeholder': 'soi.creditCardUpdate.transactionTimePlaceholder',
    'prop': 'transactionTime',
    'component': 'el-date-picker',
    'type': 'datetime',
    'span': 12,
    'value-format': 'timestamp',
    'width': '100%',
    'clearable': true
  },
  {
    'label': 'soi.creditCardUpdate.merchantName',
    'placeholder': 'soi.creditCardUpdate.merchantNamePlaceholder',
    'prop': 'merchantName',
    'component': 'el-input',
    'span': 12,
    'width': '100%',
    'disabled': true,
    'clearable': true
  },
  {
    'label': 'soi.creditCardUpdate.merchantNumber',
    'placeholder': 'soi.creditCardUpdate.merchantNumberPlaceholder',
    'prop': 'merchantNumber',
    'component': 'el-input',
    'span': 12,
    'width': '100%',
    'disabled': true,
    'clearable': true
  },
  {
    'label': 'soi.creditCardUpdate.transactionCcy',
    'placeholder': 'soi.creditCardUpdate.transactionCcyPlaceholder',
    'prop': 'transactionCcy',
    'component': 'el-input',
    'span': 12,
    'width': '100%',
    'clearable': true
  },
  {
    'label': 'soi.creditCardUpdate.transactionAmount',
    'placeholder': 'soi.creditCardUpdate.transactionAmountPlaceholder',
    'prop': 'transactionAmount',
    'component': 'el-input-number',
    'min': 0,
    'step': 0.00001,
    'step-strictly': true,
    'controls': false,
    'precision': 5,
    'span': 12,
    'width': '100%',
    'clearable': true
  },
  {
    'label': 'soi.creditCardUpdate.transactionType',
    'placeholder': 'soi.creditCardUpdate.transactionTypePlaceholder',
    'prop': 'transactionType',
    'component': 'el-input',
    'span': 12,
    'width': '100%',
    'clearable': true
  }
]
