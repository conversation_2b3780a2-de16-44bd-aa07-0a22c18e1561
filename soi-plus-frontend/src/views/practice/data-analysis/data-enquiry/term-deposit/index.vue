<template>
  <div class="data-visualization-term-deposit-data-enquiry-container">
    <customize-card :title="$t('soi.router.termDepositDataEnquiry')">
      <!-- 搜索表单 -->
      <el-form
        ref="searchForm"
        :model="searchForm"
        :rules="searchFormRules"
        size="small"
        label-position="left"
        label-width="210px"
      >
        <el-row :gutter="20">
          <!--          <el-col :span="8">
            &lt;!&ndash; 表名<TableName> &ndash;&gt;
            <el-form-item label="Table Name" prop="tableName">
              <el-select
                v-model="searchForm.tableName"
                class="input"
              >
                <el-option
                  v-for="item in customerTableList"
                  :key="item"
                  :label="item"
                  :value="item"
                />
              </el-select>
            </el-form-item>
          </el-col>-->
          <el-col :span="8">
            <!-- 创建时间<CreationDate> -->
            <el-form-item :label="$t('soi.termDepositEnquiry.createDate')" prop="createDate">
              <el-date-picker
                v-model="searchForm.createDate"
                type="daterange"
                value-format="timestamp"
                range-separator="To"
                unlink-panels
                :start-placeholder="$t('soi.termDepositEnquiry.fromCreateDate')"
                :end-placeholder="$t('soi.termDepositEnquiry.toCreateDate')"
                :default-time="['00:00:00', '23:59:59']"
                class="date-picker"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <!-- 存款号码<DepositNumber> -->
            <el-form-item :label="$t('soi.termDepositEnquiry.depositNumber')" prop="depositNumber">
              <el-input v-model="searchForm.depositNumber" clearable class="input" :placeholder="$t('soi.termDepositEnquiry.depositNumberPlaceholder')" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <!-- 帐号<AccountNumber> -->
            <el-form-item :label="$t('soi.termDepositEnquiry.accountNumber')" prop="accountNumber">
              <el-input v-model="searchForm.accountNumber" clearable class="input" :placeholder="$t('soi.termDepositEnquiry.accountNumberPlaceholder')" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <!-- fromTransactionAmount<fromTransactionAmount> -->
            <el-form-item :label="$t('soi.termDepositEnquiry.fromTransactionAmount')" prop="fromTransactionAmount">
              <el-input v-model="searchForm.fromTransactionAmount" clearable class="input" :placeholder="$t('soi.termDepositEnquiry.fromTransactionAmountPlaceholder')" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <!-- fromTransactionAmount<fromTransactionAmount> -->
            <el-form-item :label="$t('soi.termDepositEnquiry.toTransactionAmount')" prop="toTransactionAmount">
              <el-input v-model="searchForm.toTransactionAmount" clearable class="input" :placeholder="$t('soi.termDepositEnquiry.toTransactionAmountPlaceholder')" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <!-- 操作按钮 -->
            <el-button type="primary" icon="el-icon-search" size="small" @click="getDataList()">{{
              $t('soi.common.search')
            }}
            </el-button>
            <el-button icon="el-icon-refresh" size="small" @click="resetForm('searchForm')">{{
              $t('soi.common.reset')
            }}
            </el-button>
          </el-col>
        </el-row>
      </el-form>

      <!-- 操作按钮 -->
      <div class="data-action-button-group">
        <!-- 新增按钮 -->
        <!-- <el-button icon="el-icon-plus" size="small" circle /> -->
        <!-- 导出按钮 -->
        <el-button icon="el-icon-download" size="small" circle @click="exportList()" />
        <!-- 刷新按钮 -->
        <el-button icon="el-icon-refresh" size="small" circle @click="getDataList()" />
      </div>

      <!-- 表格 -->
      <el-card shadow="never">
        <el-table
          v-loading="tableLoading"
          :data="dataList"
          stripe
          style="width: 100%"
        >
          <el-table-column type="index" label="#" align="center" />
          <el-table-column :label="$t('soi.termDepositEnquiry.accountNumber')" prop="accountNumber" align="center" width="230" />
          <el-table-column :label="$t('soi.termDepositEnquiry.depositNumber')" prop="depositNumber" align="center" width="150" />
          <el-table-column :label="$t('soi.termDepositEnquiry.depositAmount')" prop="depositAmount" align="center" width="150" />
          <el-table-column :label="$t('soi.termDepositEnquiry.termPeriod')" prop="termPeriod" align="center" width="150" />
          <el-table-column :label="$t('soi.termDepositEnquiry.termInterestRate')" prop="termInterestRate" align="center" width="150" />
          <el-table-column :label="$t('soi.termDepositEnquiry.maturityDate')" prop="maturityDate" align="center" width="150">
            <template slot-scope="scope">
              <span>{{ moment(Number(scope.row.maturityDate)).format('YYYY-MM-DD HH:mm:ss') }}</span>
            </template>
          </el-table-column>
          <el-table-column :label="$t('soi.termDepositEnquiry.maturityInterest')" prop="maturityInterest" align="center" width="150" />
          <el-table-column :label="$t('soi.termDepositEnquiry.maturityAmount')" prop="maturityAmount" align="center" width="150" />
          <el-table-column :label="$t('soi.termDepositEnquiry.maturityStatus')" prop="maturityStatus" align="center" width="150" />
          <el-table-column :label="$t('soi.termDepositEnquiry.createDate')" prop="createDate" align="center" width="150">
            <template slot-scope="scope">
              <span>{{ moment(Number(scope.row.createDate)).format('YYYY-MM-DD HH:mm:ss') }}</span>
            </template>
          </el-table-column>
          <el-table-column :label="$t('soi.termDepositEnquiry.systemDate')" prop="systemDate" align="center" width="150">
            <template slot-scope="scope">
              <span>{{ moment(Number(scope.row.systemDate)).format('YYYY-MM-DD HH:mm:ss') }}</span>
            </template>
          </el-table-column>

          <el-table-column :label="$t('soi.common.operate')" align="center" width="150" fixed="right">
            <template slot-scope="scope">
              <el-button type="text" size="mini" text icon="el-icon-edit" @click="openUpdateDialog(scope.row)">
                {{ $t('soi.common.edit') }}
              </el-button>
              <el-button type="text" size="mini" text icon="el-icon-view" @click="openViewDialog(scope.row)">
                {{ $t('soi.common.view') }}
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-card>

      <div class="table-footer">
        <el-pagination
          :current-page.sync="searchForm.page"
          :page-sizes="[20, 50, 100, 500, 1000]"
          :page-size.sync="searchForm.pageSize"
          layout="total, sizes, prev, pager, next, jumper"
          :total="total"
          @size-change="getDataList()"
          @current-change="getDataList()"
        />
      </div>
    </customize-card>

    <!-- 更新的对话框 -->
    <el-dialog
      :title="$t('soi.termDepositUpdate.title')"
      :visible.sync="updateDialogVisible"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      width="60%"
    >
      <customize-form
        ref="form"
        form-ref="termDepositForm"
        :model="termDepositForm"
        :form-item-config="termDepositFormJson"
        :rules="termDepositFormRules"
        label-width="250px"
        label-position="left"
        style="max-height: 600px; overflow-y: auto"
      />
      <span slot="footer" class="dialog-footer">
        <el-button @click="closeUpdateDialog">{{ $t('soi.common.cancel') }}</el-button>
        <el-button type="primary" :loading="termDepositUpdateLoading" @click="handlerUpdateSuccess">{{ $t('soi.common.confirm') }}</el-button>
      </span>
    </el-dialog>

    <!-- 查看的对话框 -->
    <el-dialog
      :title="$t('soi.termDepositView.title')"
      :visible.sync="viewDialogVisible"
      :close-on-press-escape="false"
      :close-on-click-modal="false"
      width="60%"
      top="50px"
    >
      <descriptions :view-row="viewRow" title="CustomerDataView" />
    </el-dialog>
  </div>
</template>

<script>
import CustomizeCard from '@/components/CustomizeCard/index.vue'
import Descriptions from '@/views/practice/data-analysis/data-enquiry/components/Descriptions.vue'
import { mapGetters } from 'vuex'
import { getTableList, updateTermDeposit } from '@/api/practice/data-creation'
import { enquiryTermDepositByPage } from '@/api/practice/data-enquiry'
import moment from 'moment'
import {
  termDepositFormJson,
  termDepositFormRules
} from '@/views/practice/data-analysis/data-enquiry/term-deposit/js/termDepositForm'
import CustomizeForm from '@/components/CustomizeForm/CustomizeForm.vue'
import { exportTermDepositList } from '@/api/practice/date-export'

export default {
  name: 'TermDepositDataEnquiry',
  components: { CustomizeForm, Descriptions, CustomizeCard },
  data() {
    return {
      // 页面loading
      loading: true,
      moment,
      // 表格loading
      tableLoading: false,
      updateKey: '',
      total: 0,
      // 分页查询请求参数
      searchForm: {
        page: 1,
        pageSize: 20,
        keywords: '',
        createDate: [],
        depositNumber: '',
        accountNumber: '',
        fromTransactionAmount: null,
        toTransactionAmount: null,
        tableName: ''
      },
      searchFormRules: {
        tableName: [
          { required: true, message: 'Please select one table name.', trigger: 'change' }
        ],
        toTransactionAmount: [
          { validator: this.fromAndToValidator('fromTransactionAmount', 'toTransactionAmount'), trigger: 'blur' }
        ]
      },
      // 更新的对话框标记：true 显示，false 隐藏
      // 更新的对话框标记：true 显示，false 隐藏
      updateDialogVisible: false,
      termDepositForm: {},
      termDepositFormJson: [],
      termDepositFormRules,
      termDepositUpdateLoading: false,
      // 查看的对话框标记：true 显示，false 隐藏
      viewDialogVisible: false,
      viewRow: [],
      // 列表
      dataList: [],
      customerTableList: []
    }
  },
  computed: {
    ...mapGetters(['language', 'userDetails'])
  },
  async mounted() {
    const _this = this
    getTableList({ type: 'TermDeposit' })
      .then(function(res) {
        _this.customerTableList = res.data
        if (_this.customerTableList && _this.customerTableList.length > 0) {
          _this.searchForm.tableName = _this.customerTableList[0]
        }
      })
      .then(() => {
        // 页面初始化加载列表
        this.getDataList()
        this.termDepositFormJson = termDepositFormJson()
      }).finally(() => {
        this.loading = false
      })
  },
  methods: {
    // 更新成功
    handlerUpdateSuccess() {
      this.$refs.form.validate((valid, formData) => {
        if (valid) {
          this.termDepositUpdateLoading = true
          updateTermDeposit(formData)
            .then(res => {
              this.$message.success(res.message)
            })
            .finally(() => {
              this.termDepositUpdateLoading = false
              this.updateDialogVisible = false
              this.getDataList()
            })
        } else {
          return false
        }
      })
    },
    // 打开更新对话框
    openUpdateDialog(row) {
      // 暂存需要更新的信息，往子组件传
      this.termDepositForm = JSON.parse(JSON.stringify({
        ...row,
        'tableName': this.searchForm.tableName
      }))
      this.updateDialogVisible = true
    },
    // 关闭更新对话框
    closeUpdateDialog() {
      this.updateDialogVisible = false
    },
    // 打开查看对话框
    openViewDialog(row) {
      const keyJson = this.termDepositFormJson.map(item => item.prop)
      keyJson.unshift('id')
      this.viewRow = keyJson.map(key => {
        const value = row[key]
        if (key.toLowerCase().includes('date') && !isNaN(value)) {
          const formattedValue = moment(Number(value)).format('YYYY-MM-DD HH:mm:ss')
          return { key: this.$t(`soi.dataEnquiryDetails.${key}`), value: formattedValue }
        } else {
          return { key: this.$t(`soi.dataEnquiryDetails.${key}`), value: value }
        }
      })
      this.viewDialogVisible = true
    },
    // 调用API分页查询列表
    getDataList() {
      this.$refs['searchForm'].validate((valid) => {
        if (valid) {
          this.tableLoading = true

          const requestData = {
            ...this.searchForm,
            'userId': this.userDetails.id,
            'fromCreateDate': this.searchForm.createDate.length ? this.searchForm.createDate[0] : '',
            'toCreateDate': this.searchForm.createDate.length ? this.searchForm.createDate[1] : ''
          }

          delete requestData.createDate

          enquiryTermDepositByPage(requestData)
            .then((res) => {
              const { totalCount, termDepositDetailList: dataList } = res.data
              this.dataList = dataList
              this.total = totalCount
            })
            .finally(() => {
              this.tableLoading = false
            })
        } else {
          return false
        }
      })
    },
    // 通过ref属性重置表单
    resetForm(formName) {
      this.$refs[formName].resetFields()
    },
    exportList() {
      const requestData = {
        ...this.searchForm,
        'userId': this.userDetails.id,
        'fromCreateDate': this.searchForm.createDate.length ? this.searchForm.createDate[0] : '',
        'toCreateDate': this.searchForm.createDate.length ? this.searchForm.createDate[1] : ''
      }

      delete requestData.createDate
      exportTermDepositList(requestData)
    },
    fromAndToValidator(fromField, toField) {
      return (rule, value, callback) => {
        if (this.searchForm[toField] && this.searchForm[fromField] &&
          (Number(this.searchForm[toField]) < Number(this.searchForm[fromField]))
        ) {
          callback(
            new Error(
              this.$t(
                'soi.termDepositEnquiry.numberGreaterThanPlaceholder',
                {
                  form: this.$t(`soi.termDepositEnquiry.${fromField}`),
                  to: this.$t(`soi.termDepositEnquiry.${toField}`)
                }
              )
            )
          )
        } else {
          callback()
        }
      }
    }
  }
}
</script>
<style scoped lang="scss">
.date-picker, .select, .input {
  width: 220px;
}

.data-action-button-group {
  display: flex;
  margin-bottom: 10px;
  justify-content: right;
}
</style>

