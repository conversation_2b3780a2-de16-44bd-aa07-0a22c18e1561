export const termDepositFormJson = () => {
  return [
    {
      'label': 'soi.termDepositUpdate.accountNumber',
      'placeholder': 'soi.termDepositUpdate.accountNumberPlaceholder',
      'prop': 'accountNumber',
      'component': 'el-input',
      'span': 12,
      'clearable': true
    },
    {
      'label': 'soi.termDepositUpdate.depositNumber',
      'placeholder': 'soi.termDepositUpdate.depositNumberPlaceholder',
      'prop': 'depositNumber',
      'component': 'el-input',
      'span': 12,
      'clearable': true
    },
    {
      'label': 'soi.termDepositUpdate.depositAmount',
      'placeholder': 'soi.termDepositUpdate.depositAmountPlaceholder',
      'prop': 'depositAmount',
      'component': 'el-input-number',
      'width': '100%',
      'span': 12,
      'min': 0,
      'step': 0.00001,
      'step-strictly': true,
      'controls': false,
      'precision': 5,
      'clearable': true
    },
    {
      'label': 'soi.termDepositUpdate.termPeriod',
      'placeholder': 'soi.termDepositUpdate.termPeriodPlaceholder',
      'prop': 'termPeriod',
      'component': 'el-input',
      'span': 12,
      'clearable': true
    },
    {
      'label': 'soi.termDepositUpdate.termInterestRate',
      'placeholder': 'soi.termDepositUpdate.termInterestRatePlaceholder',
      'prop': 'termInterestRate',
      'component': 'el-input-number',
      'width': '100%',
      'span': 12,
      'min': 0,
      'step': 0.00001,
      'step-strictly': true,
      'controls': false,
      'precision': 5,
      'clearable': true
    },
    {
      'label': 'soi.termDepositUpdate.maturityDate',
      'placeholder': 'soi.termDepositUpdate.maturityDatePlaceholder',
      'prop': 'maturityDate',
      'component': 'el-date-picker',
      'clearable': true,
      'type': 'datetime',
      'format': 'yyyy-MM-dd HH:mm:ss',
      'valueFormat': 'timestamp',
      'span': 12,
      'width': '100%'
    },
    {
      'label': 'soi.termDepositUpdate.maturityInterest',
      'placeholder': 'soi.termDepositUpdate.maturityInterestPlaceholder',
      'prop': 'maturityInterest',
      'component': 'el-input-number',
      'width': '100%',
      'span': 12,
      'min': 0,
      'step': 0.00001,
      'step-strictly': true,
      'controls': false,
      'precision': 5,
      'clearable': true
    },
    {
      'label': 'soi.termDepositUpdate.maturityAmount',
      'placeholder': 'soi.termDepositUpdate.maturityAmountPlaceholder',
      'prop': 'maturityAmount',
      'component': 'el-input-number',
      'width': '100%',
      'span': 12,
      'min': 0,
      'step': 0.00001,
      'step-strictly': true,
      'controls': false,
      'precision': 5,
      'clearable': true
    },
    {
      'label': 'soi.termDepositUpdate.maturityStatus',
      'placeholder': 'soi.termDepositUpdate.maturityStatusPlaceholder',
      'prop': 'maturityStatus',
      'component': 'el-input',
      'span': 12,
      'clearable': true
    },
    {
      'label': 'soi.termDepositUpdate.createDate',
      'placeholder': 'soi.termDepositUpdate.createDatePlaceholder',
      'prop': 'createDate',
      'component': 'el-date-picker',
      'clearable': true,
      'type': 'datetime',
      'format': 'yyyy-MM-dd HH:mm:ss',
      'valueFormat': 'timestamp',
      'span': 12,
      'width': '100%'
    },
    {
      'label': 'soi.termDepositUpdate.lastUpdatedDate',
      'placeholder': 'soi.termDepositUpdate.lastUpdatedDatePlaceholder',
      'prop': 'lastUpdatedDate',
      'component': 'el-date-picker',
      'clearable': true,
      'type': 'datetime',
      'format': 'yyyy-MM-dd HH:mm:ss',
      'valueFormat': 'timestamp',
      'span': 12,
      'width': '100%'
    },
    {
      'label': 'soi.termDepositUpdate.systemDate',
      'placeholder': 'soi.termDepositUpdate.systemDatePlaceholder',
      'prop': 'systemDate',
      'component': 'el-date-picker',
      'clearable': true,
      'type': 'date',
      'format': 'yyyy-MM-dd',
      'valueFormat': 'timestamp',
      'span': 12,
      'width': '100%'
    }
  ]
}
export const termDepositFormRules = {}
