<template>
  <div class="data-analysis-data-analysis-use-case-category">
    <customize-card :title="$t('soi.router.dataAnalysisUseCase')">
      <el-row :gutter="40" class="panel-group">
        <el-col v-for="item in categoryDataFiltering()" :key="item.id" :span="8" :xs="24">
          <div class="card-panel" @click="handlerClickCategory(item.id)">
            <div class="card-panel-icon-wrapper icon-people">
              <svg-icon icon-class="docs" class-name="card-panel-icon" />
            </div>
            <div class="card-panel-description">
              <div class="card-panel-text">{{ $t(item.name) }}</div>
            </div>
          </div>
        </el-col>
      </el-row>
    </customize-card>
  </div>
</template>

<script>
import CustomizeCard from '@/components/CustomizeCard/index.vue'
import { category } from '@/views/practice/data-analysis/data-analysis-use-case/data/category'

export default {
  name: 'DataAnalysisUseCaseCategory',
  components: { CustomizeCard },
  data() {
    return {
      category
    }
  },
  methods: {
    handlerClickCategory(categoryId) {
      this.$router.push({ name: 'data-analysis-use-case-subcategory', params: { categoryId }})
    },
    categoryDataFiltering() {
      return this.category.filter(item =>
        process.env.VUE_APP_TEMPORARY_PERMISSION !== 'cuhk' ||
        process.env.VUE_APP_TEMPORARY_PERMISSION !== 'hsu' ||
        item.id !== '2'
      )
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
.panel-group {
  .card-panel-col {
    margin-bottom: 32px;
  }

  .card-panel {
    height: 108px;
    cursor: pointer;
    font-size: 12px;
    position: relative;
    color: #666;
    background: #fff;
    box-shadow: 4px 4px 40px rgba(0, 0, 0, 0.05);
    border-color: rgba(0, 0, 0, 0.05);

    &:hover {
      .card-panel-icon-wrapper {
        color: #fff;
      }

      .icon-people {
        background: #40c9c6;
      }

      .icon-message {
        background: #36a3f7;
      }

      .icon-money {
        background: #f4516c;
      }

      .icon-shopping {
        background: #34bfa3;
      }
    }

    .icon-people {
      color: #40c9c6;
    }

    .icon-message {
      color: #36a3f7;
    }

    .icon-money {
      color: #f4516c;
    }

    .icon-shopping {
      color: #34bfa3;
    }

    .card-panel-icon-wrapper {
      float: left;
      margin: 14px 0 0 14px;
      padding: 16px;
      transition: all 0.38s ease-out;
      border-radius: 6px;
    }

    .card-panel-icon {
      float: left;
      font-size: 48px;
    }

    .card-panel-description {
      font-weight: bold;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;

      .card-panel-text {
        line-height: 108px;
        color: rgba(0, 0, 0, 0.45);
        font-size: 16px;
        margin-left: 20px;
        word-break: break-all;
      }

      .card-panel-num {
        font-size: 20px;
      }
    }
  }
}

.card_list {
  position: relative;
  height: 195px;
  border: 1px solid #ccc !important;
  box-shadow: 5px 5px 5px #ccc;

  .card_img {
    text-align: center;

    img {
      width: 80%;
      height: auto;
      vertical-align: middle;
    }
  }

  h2 {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    line-height: 1.5;
  }

  p {
    height: 48px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
  }
}
</style>
<style>
.data-analysis-data-analysis-use-case-category {
  background-color: #fff;
}

.data-analysis-data-analysis-use-case-category .searchbox {
  width: 300px;
  float: right;
  margin: 0;
}

.data-analysis-data-analysis-use-case-category .el-input--medium .el-input__inner {
  margin: 0;
}

.data-analysis-data-analysis-use-case-category .show-pwd {
  cursor: pointer;
}

.doc-icon {
  color: #109eae;
  font-size: 60px;
  margin: 20px 20px 0;
}

.fontSize14 {
  font-size: 14px;
}

.noBorder {
  border: none;
}

.noBorder button {
  margin-bottom: 10px !important;
  width: 113px;
}
</style>
