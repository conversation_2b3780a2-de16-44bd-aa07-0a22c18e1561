<template>
  <div v-loading.fullscreen.lock="loading" class="data-analysis-data-analysis-use-case-subcategory">
    <customize-card :title="`${$t('soi.router.dataAnalysisUseCaseSubcategory')} - ${$t(categoryName)}`">
      <el-row v-if="subcategoryList.length > 0" :gutter="20">
        <el-col v-for="(item, index) in subcategoryList" :key="index" :span="6" :xs="24" style="padding:10px;">
          <el-card shadow="hover" class="card-list" style="height:100%">
            <el-row type="flex" align="middle" style="height:100%">
              <el-col :span="6" class="card-img">
                <img :src="dataImage" alt="image">
              </el-col>
              <el-col :span="16" :offset="2">
                <h2>{{ ['zh', 'cht'].includes(language) ? item.className : item.classNameEn }}</h2>
                <el-button type="primary" size="small" @click="handlerClickSubcategory(item)">{{ $t("soi.common.view") }}</el-button>
              </el-col>
            </el-row>
          </el-card>
        </el-col>
      </el-row>
      <div v-else class="text-center">{{ $t('soi.common.noData') }}</div>
    </customize-card>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'
import CustomizeCard from '@/components/CustomizeCard/index.vue'
import { getUserCaseSubcategory } from '@/api/practice/user-case'
import { findCategoryNameById } from '@/views/practice/data-analysis/data-analysis-use-case/data'

export default {
  name: 'DataAnalysisUseCaseSubcategory',
  components: { CustomizeCard },
  data() {
    return {
      loading: false,
      categoryId: this.$route.params.categoryId,
      dataImage: require(`@/assets/images/doc.png`),
      subcategoryList: []
    }
  },
  computed: {
    ...mapGetters([
      'language'
    ]),
    categoryName() {
      return findCategoryNameById(this.categoryId)
    }
  },
  mounted() {
    this.loadUserCaseSubcategory()
  },
  methods: {
    handlerClickSubcategory(row) {
      this.$router.push({ name: 'data-analysis-use-case-list', params: { categoryId: this.categoryId, subcategoryId: row.id }})
    },
    loadUserCaseSubcategory() {
      const vue = this
      switch (this.categoryId) {
        case '1':
          vue.getCase('Data Analysis Case')
          break
        case '2':
          vue.getCase('Data Analysis Case Insurance')
          break
        case '3':
          vue.getCase('Data Analysis Case Stock')
          break
        default:
          vue.getCase('Data Analysis Case')
          break
      }
    },
    getCase(groupName) {
      const vue = this
      vue.loading = true
      getUserCaseSubcategory({ group: groupName })
        .then((res) => {
          vue.subcategoryList = res.data
        })
        .finally(() => {
          vue.loading = false
        })
    }
  }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>

.data-analysis-data-analysis-use-case-subcategory {
  img {
    width:60px;
    height: 60px;
  }
  .text-center {
    text-align: center;
  }
}

.card-list {
  position: relative;
  height: 195px;
  border: 1px solid #ccc !important;
  box-shadow: 5px 5px 5px #ccc;
  .card-img {
    text-align: center;
    img {
      width: 100%;
      height: auto;
      vertical-align: middle;
    }
  }
  h2 {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    line-height: 1.5;
    margin-top: 0;
    font-weight: 400;
  }
  p {
    height: 48px;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
  }
}
</style>
