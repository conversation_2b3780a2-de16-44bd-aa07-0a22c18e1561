<template>
  <div v-loading.fullscreen.lock="loading" class="data-import-data-view-container">
    <customize-card :title="`${$t('soi.dataImport.previewData')} (${tableName})`">
      <el-alert
        :title="$t('soi.dataImport.only50')"
        type="success"
      />
      <el-table
        :data="columnTableData.slice((currentPage-1)*pagesize,currentPage*pagesize)"
        style="width: 100%"
        stripe
        highlight-current-row
        :header-cell-style="{'background': '#BEEEDE', 'color': '#333'}"
      >
        <el-table-column
          v-for="(data, index) in columnTableDataParameters"
          :key="index"
          :prop="data"
          :label="data"
          :render-header="labelHead"
        />
      </el-table>
      <div class="table-footer">
        <el-pagination
          :current-page="currentPage"
          :page-sizes="[5, 10, 20, 40]"
          :page-size="pagesize"
          :total="columnTableData.length"
          layout="total, prev, pager, next"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </customize-card>
  </div>
</template>

<script>
import CustomizeCard from '@/components/CustomizeCard/index.vue'
import { getTableData } from '@/api/practice/data-import'
import { mapGetters } from 'vuex'

export default {
  name: 'DataImportDataView',
  components: { CustomizeCard },
  data() {
    return {
      loading: false,
      publicSource: this.$route.params.publicSource,
      tableName: this.$route.params.tableName,
      currentPage: 1,
      pagesize: 10,
      columnTableData: [],
      columnTableDataParameters: []
    }
  },
  computed: {
    ...mapGetters(['userDetails'])
  },
  mounted() {
    this.getTableData()
  },
  methods: {
    getTableData() {
      const vue = this
      vue.loading = true
      getTableData({ userId: this.userDetails.id, tableName: this.tableName, publicSource: this.publicSource })
        .then((res) => {
          if (res.data && res.data.length) {
            for (const key in res.data[0]) {
              vue.columnTableDataParameters.push(key)
            }

            vue.columnTableData = res.data
          }
        }).finally(() => {
          vue.loading = false
        })
    },
    handleSizeChange: function(size) {
      this.pagesize = size
    },
    handleCurrentChange: function(currentPage) {
      this.currentPage = currentPage
    },
    labelHead: function(h, { column, $index }) {
      const label = column.label
      let l
      const f = 9
      if (label.indexOf('$') !== -1) {
        const labelArr = label.split('$')
        l = labelArr[0].length > labelArr[1].length ? labelArr[0].length : labelArr[1].length
        if (l < 10) {
          column.minWidth = 100
        } else {
          column.minWidth = f * (2 + l)// 加上一个文字长度
        }
        return h(
          'div', // 创建最外层的标签可随意
          [
            h('span', { // 创建第一个元素的标签可随意
              attrs: { type: 'text' }
            }, [labelArr[0]]),
            h('p', { // 创建第二个元素的标签可随意
              attrs: { type: 'text' }, // 给分割的某个元素单独加样式
              class: 'table-head header-cell',
              style: { width: '100%', color: '#707070' }
            }, [labelArr[1] || ''])
          ]
        )
      } else {
        l = column.label.length
        if (l < 10) {
          column.minWidth = 100
        } else {
          column.minWidth = f * (2 + l)// 加上一个文字长度
        }
        return h('div',
          {
            class: 'table-head header-cell',
            style: { width: '100%' }
          },
          [column.label])
      }
    }
  }
}
</script>
