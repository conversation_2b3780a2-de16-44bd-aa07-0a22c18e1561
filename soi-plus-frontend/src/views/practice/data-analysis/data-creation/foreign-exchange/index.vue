<template>
  <div v-loading.fullscreen.lock="loading" class="data-visualization-data-creation-container">
    <customize-card :title="$t('soi.router.foreignExchangeDataCreation')">
      <!--Foreign Exchange-->
      <el-form
        ref="foreignExchangeForm"
        :rules="foreignExchangeFormRules"
        :model="foreignExchangeForm"
        label-position="left"
        label-width="160px"
      >
        <el-form-item :label="$t('soi.foreignExchangeCreation.accountNumber')">
          <el-radio-group v-model="foreignExchangeFormSelectType">
            <el-radio-button label="1">{{ $t('soi.foreignExchangeCreation.accountNumber') }}</el-radio-button>
            <el-radio-button label="2">{{ $t('soi.foreignExchangeCreation.linkToDatatable') }}</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item
          v-if="foreignExchangeFormSelectType === '1'"
          prop="branchFr"
        >
          <el-input
            v-model="foreignExchangeForm.branchFr"
            :placeholder="$t('soi.foreignExchangeCreation.transactionAccountPlaceholder')"
          />
        </el-form-item>
        <el-form-item v-if="foreignExchangeFormSelectType === '2'">
          <el-upload
            :action="dataHost+'/file/upload'"
            :before-remove="beforeRemove"
            :limit="1"
            :on-change="exportData"
            :on-exceed="handleExceed"
            :on-success="foreignExchangeFormFileListSuccess"
            :file-list="foreignExchangeFormFileList"
            class="upload-demo"
            accept=".csv,.xls,.xlsx"
          >
            <el-button size="small" type="primary" @click="downloadTemplate('ForeignExchangeAccountNumber.xlsx')">
              {{ $t('soi.foreignExchangeCreation.downloadAccountTemplate') }}
            </el-button>
            <el-button size="small" type="primary">{{ $t('soi.foreignExchangeCreation.upload') }}</el-button>
            <span slot="tip" class="el-upload__tip">{{ $t('soi.foreignExchangeCreation.uploadFileRule') }}</span>
          </el-upload>
        </el-form-item>
        <el-form-item :label="$t('soi.foreignExchangeCreation.channel')" prop="channelID">
          <el-select v-model="foreignExchangeForm.channelID" style="width:100%;" placeholder="">
            <el-option v-for="item in channelList" :key="item.id" :value="item.id" :label="item.name" />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('soi.foreignExchangeCreation.transactionType')">
          <el-select v-model="foreignExchangeForm.transType" style="width:100%;" placeholder="">
            <el-option label="Buy" value="Buy" />
            <el-option label="Sell" value="Sell" />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('soi.foreignExchangeCreation.transactionCcy')">

          <el-row
            v-for="(data,index) in foreignExchangeForm.transCcyList"
            :key="index"
            :gutter="10"
            :style="{ margin: index > 0 ? '20px 0 0 0' : '0' }"
          >
            <el-col :span="6">
              <el-form-item
                :label="'0'+(index+1)"
                :prop="'transCcyList.' + index + '.ccy'"
                :rules="{required: true, message: 'Please select a ccy', trigger: 'change'}"
                label-width="40px"
              >
                <el-select v-model="data.ccy" style="width:100%;">
                  <el-option v-for="item in fexCcyList" :key="item" :value="item" :label="item" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="10">
              <el-form-item
                :prop="'transCcyList.' + index + '.amountFr'"
                :rules="{required: true, validator: validateLimitValue, trigger: 'blur'}"
                :label="$t('soi.foreignExchangeCreation.transactionAmountFrom')"
                label-width="200px"
              >
                <el-input
                  v-model="data.amountFr"
                  style="margin:0;"
                  :placeholder="$t('soi.foreignExchangeCreation.transactionAmountFromPlaceholder')"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item
                :prop="'transCcyList.' + index + '.amountTo'"
                :rules="[
                  {required: true, validator: validateLimitValue, trigger: 'blur'},
                  {required: true, validator: (rule, value, callback) => validateAmount(rule, value, callback, data.amountFr,'transactionDateFrom','transactionAmountTo'), trigger: 'blur'}
                ]"
                :label="$t('soi.foreignExchangeCreation.transactionAmountTo')"
                label-width="40px"
              >
                <el-input
                  v-model="data.amountTo"
                  style="margin:0;"
                  :placeholder="$t('soi.foreignExchangeCreation.transactionAmountToPlaceholder')"
                />
              </el-form-item>
            </el-col>
            <el-col :span="2">
              <el-button
                v-if="index === 0"
                type="primary"
                @click="addForeignExchangeFormCondition()"
              >{{
                $t('soi.common.add')
              }}
              </el-button>
              <el-button
                v-if="index > 0"
                type="primary"
                @click="handleDeleteForeignExchangeForm(index)"
              >{{ $t('soi.common.delete') }}
              </el-button>
            </el-col>
          </el-row>
        </el-form-item>
        <el-form-item :label="$t('soi.foreignExchangeCreation.transactionDate')">
          <el-col :span="11">
            <el-form-item
              :label="$t('soi.foreignExchangeCreation.transactionDateFrom')"
              prop="transactionFromDate"
              label-width="60px"
            >
              <el-date-picker
                v-model="foreignExchangeForm.transactionFromDate"
                type="datetime"
                value-format="timestamp"
                style="width: 60%;"
                :placeholder="$t('soi.foreignExchangeCreation.transactionDateFromPlaceholder')"
                :picker-options="pickerOptionsStart"
              />
            </el-form-item>
          </el-col>
          <el-col :span="2" class="line" style="text-align: center;">-</el-col>
          <el-col :span="11">
            <el-form-item
              :label="$t('soi.foreignExchangeCreation.transactionDateTo')"
              prop="transactionToDate"
              label-width="60px"
            >
              <el-date-picker
                v-model="foreignExchangeForm.transactionToDate"
                type="datetime"
                value-format="timestamp"
                style="width: 60%;"
                :placeholder="$t('soi.foreignExchangeCreation.transactionDateToPlaceholder')"
                :picker-options="pickerOptionsEnd"
              />
            </el-form-item>
          </el-col>
        </el-form-item>
        <el-form-item :label="$t('soi.foreignExchangeCreation.frequency')" prop="frequency">
          <el-select
            v-model="foreignExchangeForm.frequency"
            style="width:100%;"
            :placeholder="$t('soi.foreignExchangeCreation.frequencyPlaceholder')"
          >
            <el-option v-for="item in frequencyList" :key="item" :value="item" :label="item" />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('soi.foreignExchangeCreation.frequencyRate')" prop="frequencyRate">
          <el-input
            v-model="foreignExchangeForm.frequencyRate"
            :placeholder="$t('soi.foreignExchangeCreation.frequencyRatePlaceholder')"
          />
        </el-form-item>
        <el-form-item :label="$t('soi.foreignExchangeCreation.totalTransaction')" prop="total">
          <el-input-number
            v-model="foreignExchangeForm.total"
            :max="1000"
            :min="1"
            style="width: 100%;"
            :placeholder="$t('soi.foreignExchangeCreation.totalTransactionPlaceholder')"
          />
        </el-form-item>
        <el-form-item label="Fex Table: " prop="fexTable" hidden="hidden">
          <el-select
            v-model="foreignExchangeForm.fexTable"
            filterable
            allow-create
            style="width:100%;"
            default-first-option
            placeholder=""
          >
            <el-option
              v-for="item in fexTableNameList"
              :key="item"
              :label="item"
              :value="item"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="Transfer Table: " prop="transferTable" hidden="hidden">
          <el-select
            v-model="foreignExchangeForm.transferTable"
            filterable
            allow-create
            style="width:100%;"
            default-first-option
            placeholder=""
          >
            <el-option
              v-for="item in transferTableNameList"
              :key="item"
              :label="item"
              :value="item"
            />
          </el-select>
        </el-form-item>
        <div style="text-align:center;">
          <el-button type="primary" @click="submitForm()">{{ $t('soi.common.submit') }}</el-button>
          <el-button @click="resetForm()">{{ $t('soi.common.reset') }}</el-button>
        </div>
      </el-form>
    </customize-card>
  </div>
</template>
<script>
import CustomizeCard from '@/components/CustomizeCard/index.vue'
import { DATA_CREATION_API_URL } from '@/contains'
import {
  foreignExchangeGenerate,
  getAllFrequencyList,
  getChannelList,
  getFexCcyList,
  getTableList
} from '@/api/practice/data-creation'
import { downloadTemplate } from '@/api/practice/document'
import { mapGetters } from 'vuex'

export default {
  name: 'ForeignExchangeDataCreation',
  components: { CustomizeCard },
  data() {
    const validateLimitValue = (rule, value, callback) => {
      let a
      a = a = /^[0-9]\d*$/g

      if (!a.test(value)) {
        callback(new Error(this.$t('soi.common.invalidNumber')))
      } else {
        callback()
      }
    }
    const startDate = new Date()
    startDate.setHours(0, 0, 0, 0) // 设置时、分、秒、毫秒为 0
    const endDate = new Date()
    endDate.setHours(23, 59, 59, 0) // 设置时、分、秒、毫秒为 0
    return {
      loading: true,
      validateLimitValue: validateLimitValue,
      dataHost: DATA_CREATION_API_URL,
      foreignExchangeFormSelectType: '1',
      foreignExchangeFormFileList: [],
      channelList: [],
      fexCcyList: [],
      frequencyList: [],
      transferTableNameList: [],
      fexTableNameList: [],
      foreignExchangeForm: {
        dataSource: '1',
        branchFr: '',
        branchFrLink: '',
        transType: 'Buy',
        transCcyList: [
          {
            ccy: 'USD',
            settleAccType: 'Saving',
            amountFr: '100',
            amountTo: '1000'
          }
        ],
        transactionFromDate: startDate.getTime(),
        transactionToDate: endDate.getTime(),
        total: '1000',
        channelID: 1,
        frequency: 'Weekly',
        frequencyRate: '2',
        fexTable: 'fex_transaction',
        transferTable: 'transfer_transaction',
        userId: ''
      },
      foreignExchangeFormRules: {
        branchFr: [{
          required: true,
          message: this.$t('soi.foreignExchangeCreation.transactionAccountPlaceholder'),
          trigger: 'blur'
        }],
        transactionFromDate: [{
          required: true,
          message: this.$t('soi.foreignExchangeCreation.transactionDateFromPlaceholder'),
          trigger: 'change'
        }],
        transactionToDate: [{
          required: true,
          message: this.$t('soi.foreignExchangeCreation.transactionDateToPlaceholder'),
          trigger: 'change'
        }],
        frequency: [{
          required: true,
          message: this.$t('soi.foreignExchangeCreation.frequencyPlaceholder'),
          trigger: 'change'
        }],
        frequencyRate: [{
          required: true,
          message: this.$t('soi.foreignExchangeCreation.frequencyRatePlaceholder'),
          trigger: 'blur'
        }],
        total: [{
          required: true,
          message: this.$t('soi.foreignExchangeCreation.totalTransactionPlaceholder'),
          trigger: 'blur'
        }],
        fexTable: [{ required: true, message: 'Please input the fex table name.', trigger: 'change' }],
        transferTable: [{ required: true, message: 'Please input the transfer table name.', trigger: 'change' }]
      },
      pickerOptionsStart: {
        disabledDate: time => {
          const beginDateVal = this.foreignExchangeForm.transactionToDate
          if (beginDateVal) {
            return (
              time.getTime() > new Date(beginDateVal).getTime() - 24 * 60 * 60 * 1000
            )
          }
        }
      },
      pickerOptionsEnd: {
        disabledDate: time => {
          const beginDateVal = this.foreignExchangeForm.transactionFromDate
          if (beginDateVal) {
            return (
              time.getTime() < new Date(beginDateVal).getTime() + 24 * 60 * 60 * 1000
            )
          }
        }
      }
    }
  },
  computed: {
    ...mapGetters(['userDetails'])
  },
  watch: {
    'foreignExchangeFormSelectType'(newValue) {
      if (newValue === '2') {
        this.$refs['foreignExchangeForm'].clearValidate(['branchFr'])
      }
    }
  },
  mounted() {
    this.initSelectOptions()
    this.initTableName()
    this.loading = false
  },
  methods: {
    initTableName() {
      const _this = this
      getTableList({ type: 'Fex' })
        .then(function(res) {
          _this.fexTableNameList = res.data
        })
        .catch(() => {
          _this.fexTableNameList = []
        })
      getTableList({ type: 'Transfer' })
        .then(function(res) {
          _this.transferTableNameList = res.data
        })
        .catch(() => {
          _this.transferTableNameList = []
        })
    },
    initSelectOptions() {
      const _this = this
      getChannelList()
        .then(function(res) {
          _this.channelList = res.data.filter(item => item.id === 1)
        })
        .catch(() => {
          _this.channelList = []
        })
      getFexCcyList()
        .then(function(res) {
          _this.fexCcyList = res.data
        })
        .catch(() => {
          _this.fexCcyList = []
        })
      getAllFrequencyList()
        .then(function(res) {
          _this.frequencyList = res.data
        })
        .catch(() => {
          _this.frequencyList = []
        })
    },
    handleExceed(files, fileList) {
      this.$message.warning(this.$t('soi.foreignExchangeCreation.handleExceedMessage', {
        thisTimeChoose: files.length,
        totallyChoose: files.length + fileList.length
      }))
    },
    beforeRemove(file, fileList) {
      return this.$confirm(this.$t('soi.foreignExchangeCreation.beforeRemoveMessage', { fileName: file.name }))
    },
    exportData(file, fileList) {
      if (!file) {
        return
      }
      const isLt2M = file.size / 1024 / 1024 < 5
      if (!isLt2M) {
        this.$message.error(this.$t('soi.foreignExchangeCreation.uploadFileRule'))
        fileList = []
        return isLt2M
      }
    },
    foreignExchangeFormFileListSuccess(res, file, fileList) {
      if (res.code !== 20000) {
        this.$alert(res.message)
        this.foreignExchangeFormFileList = []
        return
      }
      this.foreignExchangeForm.branchFrLink = res.data
    },
    addForeignExchangeFormCondition() {
      var vue = this
      vue.foreignExchangeForm.transCcyList.push(
        {
          ccy: 'USD',
          settleAccType: 'Saving',
          amountFr: '',
          amountTo: ''
        }
      )
    },
    handleDeleteForeignExchangeForm(index) {
      this.foreignExchangeForm.transCcyList.splice(index, 1)
    },
    submitForm() {
      this.$refs['foreignExchangeForm'].validate((valid) => {
        const _this = this
        if (valid) {
          _this.loading = true
          _this.foreignExchangeFormSelectType === '1' ? _this.foreignExchangeForm.branchFrLink = '' : _this.foreignExchangeForm.branchFr = ''
          _this.foreignExchangeForm.userId = _this.userDetails.id
          foreignExchangeGenerate(_this.foreignExchangeForm)
            .then(function(res) {
              _this.$alert(res.message)
            })
            .finally(() => {
              _this.loading = false
            })
        } else {
          return false
        }
      })
    },
    resetForm() {
      const startDate = new Date()
      startDate.setHours(0, 0, 0, 0) // 设置时、分、秒、毫秒为 0
      const endDate = new Date()
      endDate.setHours(23, 59, 59, 0) // 设置时、分、秒、毫秒为 0
      this.foreignExchangeForm = {
        dataSource: '1',
        branchFr: '',
        branchFrLink: '',
        transType: 'Buy',
        transCcyList: [
          {
            ccy: 'USD',
            settleAccType: 'Saving',
            amountFr: '',
            amountTo: ''
          }
        ],
        transactionFromDate: startDate.getTime(),
        transactionToDate: endDate.getTime(),
        total: '1000',
        channelID: 1,
        frequency: 'Weekly',
        frequencyRate: '',
        fexTable: 'fex_transaction',
        transferTable: 'transfer_transaction',
        userId: ''
      }
    },
    downloadTemplate,
    validateAmount(rule, value, callback, amountFrValue, amountFr, amountTo) {
      if (value === '' || value === null) {
        callback(new Error(this.$t('soi.common.invalidNumber')))
      } else if (Number(value) < Number(amountFrValue)) {
        callback(new Error(
          this.$t(
            'soi.foreignExchangeCreation.numberGreaterThanPlaceholder',
            {
              form: this.$t(`soi.foreignExchangeCreation.${amountFr}`),
              to: this.$t(`soi.foreignExchangeCreation.${amountTo}`)
            }
          )
        ))
      } else {
        callback()
      }
    }
  }
}
</script>
<style scoped>
.maintenance-page {
  text-align: center;
  padding: 20px;
  color: #333;
}
</style>
