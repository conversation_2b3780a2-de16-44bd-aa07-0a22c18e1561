<template>
  <div v-loading.fullscreen.lock="loading" class="data-visualization-data-creation-container">
    <customize-card :title="$t('soi.router.customerDataCreation')">
      <el-form ref="productForm" :model="productForm" label-position="left" label-width="220px">
        <el-form-item :label="$t('soi.customerCreation.creationType') ">
          <el-radio-group v-model="creationType">
            <el-radio-button label="1">{{ $t('soi.customerCreation.singleCreation') }}</el-radio-button>
            <el-radio-button label="2">{{ $t('soi.customerCreation.bulkCreation') }}</el-radio-button>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <single-creation v-if="creationType==='1'" @changeLoadingStatus="changeLoadingStatus" />
      <bulk-creation v-if="creationType==='2'" @changeLoadingStatus="changeLoadingStatus" />
    </customize-card>
  </div>
</template>
<script>
import CustomizeCard from '@/components/CustomizeCard/index.vue'
import BulkCreation from '@/views/practice/data-analysis/data-creation/customer/components/BulkCreation.vue'
import SingleCreation from '@/views/practice/data-analysis/data-creation/customer/components/SingleCreation.vue'

export default {
  name: 'DepositDataCreation',
  components: { SingleCreation, BulkCreation, CustomizeCard },
  data() {
    return {
      loading: true,
      productForm: {
        type: 'Saving'
      },
      dataType: '3',
      creationType: '1'
    }
  },
  methods: {
    changeLoadingStatus(loading) {
      this.loading = loading
    }
  }
}
</script>
<style scoped>
.maintenance-page {
  text-align: center;
  padding: 20px;
  color: #333;
}
</style>
