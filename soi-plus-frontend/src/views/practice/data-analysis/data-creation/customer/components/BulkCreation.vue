<template>
  <el-form
    ref="CustomerForm"
    :model="customerForm"
    :rules="customerFormRules"
    label-position="left"
    label-width="220px"
    class="bulk-creation-form"
  >
    <!-- 国家代码<CountryCode> -->
    <el-form-item :label="$t('soi.customerBulkCreation.countryCode')" prop="countryCode">
      <el-input
        v-model="customerForm.countryCode"
        :placeholder="$t('soi.customerBulkCreation.countryCodePlaceholder')"
        class="input"
        maxlength="2"
      />
    </el-form-item>
    <!-- 清算代码<ClearingCode> -->
    <el-form-item :label="$t('soi.customerBulkCreation.clearingCode')" prop="clearingCode">
      <el-input v-model="customerForm.clearingCode" :placeholder="$t('soi.customerBulkCreation.clearingCodePlaceholder')" class="input" />
    </el-form-item>
    <!-- 分行编号<BranchCode> -->
    <el-form-item :label="$t('soi.customerBulkCreation.branchCode')">
      <!-- Form -->
      <el-col :span="11">
        <el-form-item :label="$t('soi.customerBulkCreation.branchNumberFrom')" prop="branchNumberFrom" label-width="50px">
          <el-input
            v-model="customerForm.branchNumberFrom"
            :placeholder="$t('soi.customerBulkCreation.branchNumberFromPlaceholder')"
            class="input"
            type="number"
            :disabled="true"
          />
        </el-form-item>
      </el-col>
      <el-col :span="2" class="line" style="text-align: center;">-</el-col>
      <!-- To -->
      <el-col :span="11">
        <el-form-item :label="$t('soi.customerBulkCreation.branchNumberTo')" prop="branchNumberTo" label-width="50px">
          <el-input
            v-model="customerForm.branchNumberTo"
            :placeholder="$t('soi.customerBulkCreation.branchNumberToPlaceholder')"
            class="input"
            type="number"
            :disabled="true"
          />
        </el-form-item>
      </el-col>
    </el-form-item>
    <!-- 年龄阶层<AgeGroup> -->
    <el-form-item :label="$t('soi.customerBulkCreation.ageGroup')" prop="ageGroup">
      <div class="el-form-item-inner">
        <el-input
          :value="customerForm.ageGroup.map(item => `${item.select}: ${item.input}`).join(', ')"
          class="input el-form-item-inner-input"
          :placeholder="$t('soi.customerBulkCreation.ageGroupPlaceholder')"
          readonly
        />
        <el-button
          type="primary"
          @click="openDynamicDataDialog(
            $t('soi.customerBulkCreation.ageGroup'),
            customerForm.ageGroup,
            $t('soi.customerBulkCreation.ageGroup'),
            customerFormRules.ageGroup,
            $t('soi.customerBulkCreation.ageGroupPlaceholder'),
            customerFormRules.ageGroupInput,
            $t('soi.customerBulkCreation.ageGroupPercentagePlaceholder'),
            6,
            'ageGroup',
            ageGroupOptions
          )"
        >
          {{ $t('soi.common.add') }}
        </el-button>
      </div>
    </el-form-item>
    <!-- 性别<Gender> -->
    <el-form-item prop="gender" :label="$t('soi.customerBulkCreation.gender')">
      <div class="el-form-item-inner">
        <el-input
          :value="customerForm.gender.map(item => `${item.select}: ${item.input}`).join(', ')"
          class="input el-form-item-inner-input"
          :placeholder="$t('soi.customerBulkCreation.genderPlaceholder')"
          readonly
        />
        <el-button
          type="primary"
          @click="openDynamicDataDialog(
            $t('soi.customerBulkCreation.gender'),
            customerForm.gender,
            $t('soi.customerBulkCreation.gender'),
            customerFormRules.gender,
            $t('soi.customerBulkCreation.genderPlaceholder'),
            customerFormRules.genderInput,
            $t('soi.customerBulkCreation.genderPercentagePlaceholder'),
            7,
            'gender',
            genderOptions
          )"
        >
          {{ $t('soi.common.add') }}
        </el-button>
      </div>
    </el-form-item>
    <!-- 国籍<Nationality> -->
    <el-form-item :label="$t('soi.customerBulkCreation.nationality')" prop="nationality1">
      <div class="el-form-item-inner">
        <el-input
          :value="customerForm.nationality1.map(item => `${item.select}: ${item.input}`).join(', ')"
          class="input el-form-item-inner-input"
          :placeholder="$t('soi.customerBulkCreation.nationalityPlaceholder')"
          readonly
        />
        <el-button
          type="primary"
          @click="openDynamicDataDialog(
            $t('soi.customerBulkCreation.nationality'),
            customerForm.nationality1,
            $t('soi.customerBulkCreation.nationality'),
            customerFormRules.nationality1,
            $t('soi.customerBulkCreation.nationalityPlaceholder'),
            customerFormRules.nationality1Input,
            $t('soi.customerBulkCreation.nationalityPercentagePlaceholder'),
            7,
            'nationality1',
            nationality
          )"
        >
          {{ $t('soi.common.add') }}
        </el-button>
      </div>
    </el-form-item>
    <!-- 永久居留身份<PermanentResidenceStatus> -->
    <el-form-item :label="$t('soi.customerBulkCreation.permanentResidenceStatus')" prop="permanentResidenceStatus">
      <div class="el-form-item-inner">
        <el-input
          :value="customerForm.permanentResidenceStatus.map(item => `${item.select}: ${item.input}`).join(', ')"
          class="input el-form-item-inner-input"
          :placeholder="$t('soi.customerBulkCreation.permanentResidenceStatusPlaceholder')"
          readonly
        />
        <el-button
          type="primary"
          @click="openDynamicDataDialog(
            $t('soi.customerBulkCreation.permanentResidenceStatus'),
            customerForm.permanentResidenceStatus,
            $t('soi.customerBulkCreation.permanentResidenceStatus'),
            customerFormRules.permanentResidenceStatus,
            $t('soi.customerBulkCreation.permanentResidenceStatusPlaceholder'),
            customerFormRules.permanentResidenceStatusInput,
            $t('soi.customerBulkCreation.permanentResidenceStatusPercentagePlaceholder'),
            7,
            'permanentResidenceStatus',
            permanentResidenceStatusOptions
          )"
        >
          {{ $t('soi.common.add') }}
        </el-button>
      </div>
    </el-form-item>
    <!-- 婚姻状况<MaritalStatus> -->
    <el-form-item prop="maritalStatus" :label="$t('soi.customerBulkCreation.maritalStatus')">
      <div class="el-form-item-inner">
        <el-input
          :value="customerForm.maritalStatus.map(item => `${item.select}: ${item.input}`).join(', ')"
          class="input el-form-item-inner-input"
          :placeholder="$t('soi.customerBulkCreation.maritalStatusPlaceholder')"
          readonly
        />
        <el-button
          type="primary"
          @click="openDynamicDataDialog(
            $t('soi.customerBulkCreation.maritalStatus'),
            customerForm.maritalStatus,
            $t('soi.customerBulkCreation.maritalStatus'),
            customerFormRules.maritalStatus,
            $t('soi.customerBulkCreation.maritalStatusPlaceholder'),
            customerFormRules.maritalStatusInput,
            $t('soi.customerBulkCreation.maritalStatusPercentagePlaceholder'),
            7,
            'maritalStatus',
            maritalStatusOptions
          )"
        >
          {{ $t('soi.common.add') }}
        </el-button>
      </div>
    </el-form-item>
    <!-- 教育<Education> -->
    <el-form-item prop="education" :label="$t('soi.customerBulkCreation.education')">
      <div class="el-form-item-inner">
        <el-input
          :value="customerForm.education.map(item => `${item.select}: ${item.input}`).join(', ')"
          class="input el-form-item-inner-input"
          :placeholder="$t('soi.customerBulkCreation.educationPlaceholder')"
          readonly
        />
        <el-button
          type="primary"
          @click="openDynamicDataDialog(
            $t('soi.customerBulkCreation.education'),
            customerForm.education,
            $t('soi.customerBulkCreation.education'),
            customerFormRules.education,
            $t('soi.customerBulkCreation.educationPlaceholder'),
            customerFormRules.educationInput,
            $t('soi.customerBulkCreation.educationPercentagePlaceholder'),
            7,
            'education',
            educationOptions
          )"
        >
          {{ $t('soi.common.add') }}
        </el-button>
      </div>
    </el-form-item>
    <!-- Accommodation<Accommodation> -->
    <el-form-item prop="accommodation" :label="$t('soi.customerBulkCreation.accommodation')">
      <div class="el-form-item-inner">
        <el-input
          :value="customerForm.accommodation.map(item => `${item.select}: ${item.input}`).join(', ')"
          class="input el-form-item-inner-input"
          :placeholder="$t('soi.customerBulkCreation.accommodationPlaceholder')"
          readonly
        />
        <el-button
          type="primary"
          @click="openDynamicDataDialog(
            $t('soi.customerBulkCreation.accommodation'),
            customerForm.accommodation,
            $t('soi.customerBulkCreation.accommodation'),
            customerFormRules.accommodation,
            $t('soi.customerBulkCreation.accommodationPlaceholder'),
            customerFormRules.accommodationInput,
            $t('soi.customerBulkCreation.accommodationPercentagePlaceholder'),
            7,
            'accommodation',
            accommodation
          )"
        >
          {{ $t('soi.common.add') }}
        </el-button>
      </div>
    </el-form-item>
    <!-- 地区<Region> -->
    <el-form-item :label="$t('soi.customerBulkCreation.region')" prop="region">
      <div class="el-form-item-inner">
        <el-input
          :value="customerForm.region.map(item => `${item.select}: ${item.input}`).join(', ')"
          class="input el-form-item-inner-input"
          :placeholder="$t('soi.customerBulkCreation.regionPlaceholder')"
          readonly
        />
        <el-button
          type="primary"
          @click="openDynamicDataDialog(
            $t('soi.customerBulkCreation.region'),
            customerForm.region,
            $t('soi.customerBulkCreation.region'),
            customerFormRules.region,
            $t('soi.customerBulkCreation.regionPlaceholder'),
            customerFormRules.regionInput,
            $t('soi.customerBulkCreation.regionPercentagePlaceholder'),
            7,
            'region',
            residentialRegionName
          )"
        >
          {{ $t('soi.common.add') }}
        </el-button>
      </div>
    </el-form-item>
    <!-- 出生国家<CountryOfBirth> -->
    <el-form-item :label="$t('soi.customerBulkCreation.countryOfBirth')" prop="countryOfBirth">
      <el-select v-model="customerForm.countryOfBirth" multiple class="select" :placeholder="$t('soi.customerBulkCreation.countryOfBirthPlaceholder')">
        <el-option
          v-for="item in countryOfBirthList"
          :key="item"
          :label="item"
          :value="item"
        />
      </el-select>
    </el-form-item>
    <!-- 创建时间<CreationDate> -->
    <el-form-item :label="$t('soi.customerBulkCreation.createDate')" prop="createDate">
      <el-date-picker
        v-model="customerForm.createDate"
        type="daterange"
        value-format="timestamp"
        range-separator="-"
        unlink-panels
        :start-placeholder="$t('soi.customerBulkCreation.fromCreateDate')"
        :end-placeholder="$t('soi.customerBulkCreation.toCreateDate')"
        :default-time="['00:00:00', '23:59:59']"
      />
    </el-form-item>
    <!-- 首选方法<ContactPreferredMethod> -->
    <el-form-item :label="$t('soi.customerBulkCreation.contactPreferredMethod')" prop="contactPreferredMethod">
      <div class="el-form-item-inner">
        <el-input
          :value="customerForm.contactPreferredMethod.map(item => `${item.select}: ${item.input}`).join(', ')"
          class="input el-form-item-inner-input"
          :placeholder="$t('soi.customerBulkCreation.contactPreferredMethodPlaceholder')"
          readonly
        />
        <el-button
          type="primary"
          @click="openDynamicDataDialog(
            $t('soi.customerBulkCreation.contactPreferredMethod'),
            customerForm.contactPreferredMethod,
            $t('soi.customerBulkCreation.contactPreferredMethod'),
            customerFormRules.contactPreferredMethod,
            $t('soi.customerBulkCreation.contactPreferredMethodPlaceholder'),
            customerFormRules.contactPreferredMethodInput,
            $t('soi.customerBulkCreation.contactPreferredMethodPercentagePlaceholder'),
            7,
            'contactPreferredMethod',
            contactPreferredMethod
          )"
        >
          {{ $t('soi.common.add') }}
        </el-button>
      </div>
    </el-form-item>
    <!-- 之前的语言<PreLanguage> -->
    <el-form-item :label="$t('soi.customerBulkCreation.preLanguage')" prop="preLanguage1">
      <div class="el-form-item-inner">
        <el-input
          :value="customerForm.preLanguage1.map(item => `${item.select}: ${item.input}`).join(', ')"
          class="input el-form-item-inner-input"
          :placeholder="$t('soi.customerBulkCreation.preLanguagePlaceholder')"
          readonly
        />
        <el-button
          type="primary"
          @click="openDynamicDataDialog(
            $t('soi.customerBulkCreation.preLanguage'),
            customerForm.preLanguage1,
            $t('soi.customerBulkCreation.preLanguage'),
            customerFormRules.preLanguage1,
            $t('soi.customerBulkCreation.preLanguagePlaceholder'),
            customerFormRules.preLanguage1Input,
            $t('soi.customerBulkCreation.preLanguagePercentagePlaceholder'),
            7,
            'preLanguage1',
            preLanguage1
          )"
        >
          {{ $t('soi.common.add') }}
        </el-button>
      </div>
    </el-form-item>
    <!-- 敏感状态<SensitiveStatus> -->
    <el-form-item :label="$t('soi.customerBulkCreation.sensitiveStatus')" prop="sensitiveStatus">
      <div class="el-form-item-inner">
        <el-input
          :value="customerForm.sensitiveStatus.map(item => `${item.select}: ${item.input}`).join(', ')"
          class="input el-form-item-inner-input"
          :placeholder="$t('soi.customerBulkCreation.sensitiveStatusPlaceholder')"
          readonly
        />
        <el-button
          type="primary"
          @click="openDynamicDataDialog(
            $t('soi.customerBulkCreation.sensitiveStatus'),
            customerForm.sensitiveStatus,
            $t('soi.customerBulkCreation.sensitiveStatus'),
            customerFormRules.sensitiveStatus,
            $t('soi.customerBulkCreation.sensitiveStatusPlaceholder'),
            customerFormRules.sensitiveStatusInput,
            $t('soi.customerBulkCreation.sensitiveStatusPercentagePlaceholder'),
            7,
            'sensitiveStatus',
            permanentResidenceStatusOptions
          )"
        >
          {{ $t('soi.common.add') }}
        </el-button>
      </div>
    </el-form-item>
    <!-- 家庭收入<HouseholdIncome> -->
    <el-form-item :label="$t('soi.customerBulkCreation.householdIncome')" prop="householdIncome">
      <div class="el-form-item-inner">
        <el-input
          :value="customerForm.householdIncome.map(item => `${item.fr} - ${item.to} : ${item.input}`).join(', ')"
          class="input el-form-item-inner-input"
          :placeholder="$t('soi.customerBulkCreation.householdIncomePlaceholder')"
          readonly
        />
        <el-button
          type="primary"
          @click="openIncomeDynamicDataDialog(
            $t('soi.customerBulkCreation.householdIncome'),
            customerForm.householdIncome,
            customerFormRules.householdIncomeAmountFrom,
            $t('soi.customerBulkCreation.householdIncomeAmountFromPlaceholder'),
            customerFormRules.householdIncomeAmountTo,
            $t('soi.customerBulkCreation.householdIncomeAmountToPlaceholder'),
            customerFormRules.householdIncomeInput,
            $t('soi.customerBulkCreation.householdIncomePercentagePlaceholder'),
            3,
            'householdIncome'
          )"
        >
          {{ $t('soi.common.add') }}
        </el-button>
      </div>
    </el-form-item>
    <!-- 就业状况<EmploymentStatus> -->
    <el-form-item prop="employmentStatus" :label="$t('soi.customerBulkCreation.employmentStatus')">
      <div class="el-form-item-inner">
        <el-input
          :value="customerForm.employmentStatus.map(item => `${item.select}: ${item.input}`).join(', ')"
          class="input el-form-item-inner-input"
          :placeholder="$t('soi.customerBulkCreation.employmentStatusPlaceholder')"
          readonly
        />
        <el-button
          type="primary"
          @click="openDynamicDataDialog(
            $t('soi.customerBulkCreation.employmentStatus'),
            customerForm.employmentStatus,
            $t('soi.customerBulkCreation.employmentStatus'),
            customerFormRules.employmentStatus,
            $t('soi.customerBulkCreation.employmentStatusPlaceholder'),
            customerFormRules.employmentStatusInput,
            $t('soi.customerBulkCreation.employmentStatusPercentagePlaceholder'),
            7,
            'employmentStatus',
            employmentStatusOptions
          )"
        >
          {{ $t('soi.common.add') }}
        </el-button>
      </div>
    </el-form-item>
    <!-- 职业<Occupation> -->
    <el-form-item :label="$t('soi.customerBulkCreation.occupation')" prop="occupation">
      <el-select
        v-model="customerForm.occupation"
        multiple
        class="select"
        :placeholder="$t('soi.customerBulkCreation.occupationPlaceholder')"
      >
        <el-option
          v-for="item in occupationOptions"
          :key="item"
          :label="item"
          :value="item"
        />
      </el-select>
    </el-form-item>
    <!-- 每月收入<MonthlyIncome> -->
    <el-form-item :label="$t('soi.customerBulkCreation.monthlyIncome')" prop="monthlyIncome">
      <div class="el-form-item-inner">
        <el-input
          :value="customerForm.monthlyIncome.map(item => `${item.fr} - ${item.to} : ${item.input}`).join(', ')"
          class="input el-form-item-inner-input"
          :placeholder="$t('soi.customerBulkCreation.monthlyIncomePlaceholder')"
          readonly
        />
        <el-button
          type="primary"
          @click="openIncomeDynamicDataDialog(
            $t('soi.customerBulkCreation.monthlyIncome'),
            customerForm.monthlyIncome,
            customerFormRules.monthlyIncomeFrom,
            $t('soi.customerBulkCreation.monthlyIncomeFromPlaceholder'),
            customerFormRules.monthlyIncomeTo,
            $t('soi.customerBulkCreation.monthlyIncomeToPlaceholder'),
            customerFormRules.monthlyIncomeInput,
            $t('soi.customerBulkCreation.monthlyIncomePercentagePlaceholder'),
            3,
            'monthlyIncome'
          )"
        >
          {{ $t('soi.common.add') }}
        </el-button>
      </div>
    </el-form-item>
    <!-- 其他每月收入<OtherMonthlyIncome> -->
    <el-form-item :label="$t('soi.customerBulkCreation.otherMonthlyIncome')">
      <!-- Form -->
      <el-col :span="11">
        <el-form-item :label="$t('soi.customerBulkCreation.otherMonthlyIncomeFrom')" prop="otherMonthlyIncomeFrom" label-width="50px">
          <el-input v-model="customerForm.otherMonthlyIncomeFrom" :placeholder="$t('soi.customerBulkCreation.otherMonthlyIncomeFromPlaceholder')" class="input" type="number" />
        </el-form-item>
      </el-col>
      <el-col :span="2" class="line" style="text-align: center;">-</el-col>
      <!-- To -->
      <el-col :span="11">
        <el-form-item :label="$t('soi.customerBulkCreation.otherMonthlyIncomeTo')" prop="otherMonthlyIncomeTo" label-width="50px">
          <el-input v-model="customerForm.otherMonthlyIncomeTo" :placeholder="$t('soi.customerBulkCreation.otherMonthlyIncomeToPlaceholder')" class="input" type="number" />
        </el-form-item>
      </el-col>
    </el-form-item>
    <!-- 雇主行业<EmployerIndustry> -->
    <el-form-item :label="$t('soi.customerBulkCreation.employerIndustry')" prop="employerIndustry">
      <el-select v-model="customerForm.employerIndustry" multiple class="select" :placeholder="$t('soi.customerBulkCreation.employerIndustryPlaceholder')">
        <el-option
          v-for="item in employerIndustry"
          :key="item"
          :label="item"
          :value="item"
        />
      </el-select>
    </el-form-item>
    <!-- 客户状态<CustomerStatus> -->
    <el-form-item prop="customerStatus" :label="$t('soi.customerBulkCreation.customerStatus')">
      <el-select v-model="customerForm.customerStatus" class="select" :placeholder="$t('soi.customerBulkCreation.customerStatusPlaceholder')">
        <el-option
          v-for="item in customerStatusOptions"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </el-form-item>
    <!-- 总数量<TotalCustomers> -->
    <el-form-item :label="$t('soi.customerBulkCreation.totalCustomers')" prop="total">
      <el-input v-model="customerForm.total" :placeholder="$t('soi.customerBulkCreation.totalCustomersPlaceholder')" class="input" type="number" />
    </el-form-item>
    <!-- 表名<TableName> -->
    <el-form-item :label="$t('soi.customerBulkCreation.tableName')" prop="tableName" hidden="hidden">
      <el-select
        v-model="customerForm.tableName"
        filterable
        allow-create
        class="input"
        default-first-option
        :placeholder="$t('soi.customerBulkCreation.tableNamePlaceholder')"
      >
        <el-option
          v-for="item in customerTableList"
          :key="item"
          :label="item"
          :value="item"
        />
      </el-select>
    </el-form-item>
    <el-form-item :label="$t('soi.customerBulkCreation.accountDataCreation')" />
    <el-form-item :label="$t('soi.customerBulkCreation.accountType')" prop="accountType">
      <el-checkbox-group v-model="customerForm.accountType">
        <el-checkbox
          v-for="(item, index) in accountTypeOptions"
          :key="index"
          :disabled="(item.value === '001' || item.value === '002')"
          :label="item.value"
          name="accountType"
        >
          {{ item.label }}
        </el-checkbox>
      </el-checkbox-group>
    </el-form-item>
    <el-form-item v-if="customerForm.accountType.includes('003')" :label="$t('soi.customerBulkCreation.fexAccountCurrencyCode')" prop="currencyCode">
      <el-select v-model="customerForm.currencyCode" class="select" :placeholder="$t('soi.customerBulkCreation.fexAccountCurrencyCodePlaceholder')">
        <el-option
          v-for="(item, index) in currencyTypeOptions"
          :key="index"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </el-form-item>
    <div style="text-align:center;">
      <el-button type="primary" @click="submitForm">{{ $t('soi.common.submit') }}</el-button>
      <el-button @click="resetForm('CustomerForm')">{{ $t('soi.common.reset') }}</el-button>
    </div>
    <dynamic-data
      :visible="dialog.visible"
      :title="dialog.title"
      :label="dialog.label"
      :placeholder="dialog.placeholder"
      :max-size="dialog.maxSize"
      :form="dialog.form"
      :data-list="dialog.form.dataList"
      :select-rule="dialog.selectRule"
      :select-placeholder="dialog.selectPlaceholder"
      :input-rule="dialog.inputRule"
      :input-placeholder="dialog.inputPlaceholder"
      :type="dialog.type"
      :candidate-list="dialog.candidateList"
      @dialog-form-submitted="handleDialogFormSubmission"
      @dialog-closed="handleDialogClosed"
    />

    <income-dynamic-data
      :visible="incomeDialog.visible"
      :title="incomeDialog.title"
      :max-size="incomeDialog.maxSize"
      :form="incomeDialog.form"
      :fr-rule="incomeDialog.frRule"
      :fr-placeholder="incomeDialog.frPlaceholder"
      :to-rule="incomeDialog.toRule"
      :to-placeholder="incomeDialog.toPlaceholder"
      :input-rule="incomeDialog.inputRule"
      :input-placeholder="incomeDialog.inputPlaceholder"
      :data-list="incomeDialog.form.dataList"
      :type="incomeDialog.type"
      @dialog-form-submitted="handleIncomeDialogFormSubmission"
      @dialog-closed="handleIncomeDialogClosed"
    />
  </el-form>
</template>
<script>
import {
  bulkGenerate,
  getCustomerOccupationList,
  getTableList,
  optionalGenerate
} from '@/api/practice/data-creation'
import {
  ageGroupOptions,
  customerStatusOptions,
  genderOptions,
  permanentResidenceStatusOptions,
  maritalStatusOptions,
  educationOptions,
  employmentStatusOptions,
  accountTypeOptions,
  currencyTypeOptions,
  accountTableMap, getAccommodationLabelByValue
} from '@/views/practice/data-analysis/data-creation/customer/js'
import DynamicData from '@/views/practice/data-analysis/data-creation/customer/components/DynamicData.vue'
import IncomeDynamicData from '@/views/practice/data-analysis/data-creation/customer/components/IncomeDynamicData.vue'
import { mapGetters } from 'vuex'

export default {
  name: 'BulkCreation',
  components: { DynamicData, IncomeDynamicData },
  data() {
    return {
      customerForm: {
        countryCode: '',
        clearingCode: '0001',
        branchNumberFrom: 177,
        branchNumberTo: 199,
        ageGroup: [],
        gender: [],
        nationality1: [],
        permanentResidenceStatus: [],
        maritalStatus: [],
        education: [],
        accommodation: [],
        region: [],
        countryOfBirth: [],
        createDate: [],
        contactPreferredMethod: [],
        preLanguage1: [],
        sensitiveStatus: [],
        householdIncome: [],
        employmentStatus: [],
        occupation: [],
        monthlyIncome: [],
        otherMonthlyIncomeFrom: null,
        otherMonthlyIncomeTo: null,
        employerIndustry: [],
        customerStatus: '',
        total: null,
        tableName: '',
        userId: '',
        accountType: ['001', '002'],
        currencyCode: '',
        accountTotal: 1,
        accountTable: '',
        relAccountTable: ''
      },
      customerStatusOptions,
      ageGroupOptions,
      genderOptions,
      permanentResidenceStatusOptions,
      maritalStatusOptions,
      educationOptions,
      employmentStatusOptions,
      accountTypeOptions,
      currencyTypeOptions,
      accountTableMap,
      customerFormRules: {
        countryCode: [
          { required: true, message: this.$t('soi.customerBulkCreation.countryCodePlaceholder'), trigger: 'blur' }
        ],
        clearingCode: [
          { required: true, message: this.$t('soi.customerBulkCreation.clearingCodePlaceholder'), trigger: 'blur' }
        ],
        branchNumberFrom: [
          { required: true, message: this.$t('soi.customerBulkCreation.branchNumberFromPlaceholder'), trigger: 'blur' },
          { pattern: /^[1-9]\d*$/, message: this.$t('soi.common.invalidNumber'), trigger: 'blur' }
        ],
        branchNumberTo: [
          { required: true, message: this.$t('soi.customerBulkCreation.branchNumberToPlaceholder'), trigger: 'blur' },
          { pattern: /^[1-9]\d*$/, message: this.$t('soi.common.invalidNumber'), trigger: 'blur' }
        ],
        ageGroup: [
          { required: true, message: this.$t('soi.customerBulkCreation.ageGroupPlaceholder'), trigger: 'change' }
        ],
        ageGroupInput: [
          { required: true, message: this.$t('soi.customerBulkCreation.ageGroupPercentagePlaceholder'), trigger: 'blur' }
        ],
        gender: [
          { required: true, message: this.$t('soi.customerBulkCreation.genderPlaceholder'), trigger: 'change' }
        ],
        genderInput: [
          { required: true, message: this.$t('soi.customerBulkCreation.genderPercentagePlaceholder'), trigger: 'blur' }
        ],
        nationality1: [
          { required: true, message: this.$t('soi.customerBulkCreation.nationalityPlaceholder'), trigger: 'change' }
        ],
        nationality1Input: [
          { required: true, message: this.$t('soi.customerBulkCreation.nationalityPercentagePlaceholder'), trigger: 'blur' }
        ],
        permanentResidenceStatus: [
          { required: true, message: this.$t('soi.customerBulkCreation.permanentResidenceStatusPlaceholder'), trigger: 'change' }
        ],
        permanentResidenceStatusInput: [
          { required: true, message: this.$t('soi.customerBulkCreation.permanentResidenceStatusPercentagePlaceholder'), trigger: 'blur' }
        ],
        maritalStatus: [
          { required: true, message: this.$t('soi.customerBulkCreation.maritalStatusPlaceholder'), trigger: 'change' }
        ],
        maritalStatusInput: [
          { required: true, message: this.$t('soi.customerBulkCreation.maritalStatusPercentagePlaceholder'), trigger: 'blur' }
        ],
        education: [
          { required: true, message: this.$t('soi.customerBulkCreation.educationPlaceholder'), trigger: 'change' }
        ],
        educationInput: [
          { required: true, message: this.$t('soi.customerBulkCreation.educationPercentagePlaceholder'), trigger: 'blur' }
        ],
        accommodation: [
          { required: true, message: this.$t('soi.customerBulkCreation.accommodationPlaceholder'), trigger: 'change' }
        ],
        accommodationInput: [
          { required: true, message: this.$t('soi.customerBulkCreation.accommodationPercentagePlaceholder'), trigger: 'blur' }
        ],
        region: [
          { required: true, message: this.$t('soi.customerBulkCreation.regionPlaceholder'), trigger: 'change' }
        ],
        regionInput: [
          { required: true, message: this.$t('soi.customerBulkCreation.regionPercentagePlaceholder'), trigger: 'blur' }
        ],
        countryOfBirth: [
          { required: true, message: this.$t('soi.customerBulkCreation.countryOfBirthPlaceholder'), trigger: 'change' }
        ],
        createDate: [
          { required: true, message: this.$t('soi.customerBulkCreation.createDate'), trigger: 'blur' }
        ],
        contactPreferredMethod: [
          { required: true, message: this.$t('soi.customerBulkCreation.contactPreferredMethodPlaceholder'), trigger: 'change' }
        ],
        contactPreferredMethodInput: [
          { required: true, message: this.$t('soi.customerBulkCreation.contactPreferredMethodPercentagePlaceholder'), trigger: 'blur' }
        ],
        preLanguage1: [
          { required: true, message: this.$t('soi.customerBulkCreation.preLanguagePlaceholder'), trigger: 'change' }
        ],
        preLanguage1Input: [
          { required: true, message: this.$t('soi.customerBulkCreation.preLanguagePercentagePlaceholder'), trigger: 'blur' }
        ],
        sensitiveStatus: [
          { required: true, message: this.$t('soi.customerBulkCreation.sensitiveStatusPlaceholder'), trigger: 'change' }
        ],
        sensitiveStatusInput: [
          { required: true, message: this.$t('soi.customerBulkCreation.sensitiveStatusPercentagePlaceholder'), trigger: 'blur' }
        ],
        householdIncomeAmountFrom: [
          { required: true, message: this.$t('soi.customerBulkCreation.householdIncomeAmountFromPlaceholder'), trigger: 'blur' },
          { pattern: /^[1-9]\d*$/, message: this.$t('soi.common.invalidNumber'), trigger: 'blur' }
        ],
        householdIncomeAmountTo: [
          { required: true, message: this.$t('soi.customerBulkCreation.householdIncomeAmountToPlaceholder'), trigger: 'blur' },
          { pattern: /^[1-9]\d*$/, message: this.$t('soi.common.invalidNumber'), trigger: 'blur' }
        ],
        householdIncomeInput: [
          { required: true, message: this.$t('soi.customerBulkCreation.householdIncomePercentagePlaceholder'), trigger: 'blur' }
        ],
        employmentStatus: [
          { required: true, message: this.$t('soi.customerBulkCreation.employmentStatusPlaceholder'), trigger: 'change' }
        ],
        employmentStatusInput: [
          { required: true, message: this.$t('soi.customerBulkCreation.employmentStatusPercentagePlaceholder'), trigger: 'blur' }
        ],
        occupation: [
          { required: true, message: this.$t('soi.customerBulkCreation.occupationPlaceholder'), trigger: 'change' }
        ],
        monthlyIncome: [
          { required: true, message: this.$t('soi.customerBulkCreation.monthlyIncomePlaceholder'), trigger: 'change' }
        ],
        householdIncome: [
          { required: true, message: this.$t('soi.customerBulkCreation.householdIncomePlaceholder'), trigger: 'change' }
        ],
        monthlyIncomeFrom: [
          { required: true, message: this.$t('soi.customerBulkCreation.monthlyIncomeFromPlaceholder'), trigger: 'blur' },
          { pattern: /^[1-9]\d*$/, message: this.$t('soi.common.invalidNumber'), trigger: 'blur' }
        ],
        monthlyIncomeTo: [
          { required: true, message: this.$t('soi.customerBulkCreation.monthlyIncomeToPlaceholder'), trigger: 'blur' },
          { pattern: /^[1-9]\d*$/, message: this.$t('soi.common.invalidNumber'), trigger: 'blur' }
        ],
        monthlyIncomeInput: [
          { required: true, message: this.$t('soi.customerBulkCreation.monthlyIncomePercentagePlaceholder'), trigger: 'blur' }
        ],
        otherMonthlyIncomeFrom: [
          { required: true, message: this.$t('soi.customerBulkCreation.otherMonthlyIncomeFromPlaceholder'), trigger: 'blur' },
          { pattern: /^[1-9]\d*$/, message: this.$t('soi.common.invalidNumber'), trigger: 'blur' }
        ],
        otherMonthlyIncomeTo: [
          { required: true, message: this.$t('soi.customerBulkCreation.otherMonthlyIncomeToPlaceholder'), trigger: 'blur' },
          { pattern: /^[1-9]\d*$/, message: this.$t('soi.common.invalidNumber'), trigger: 'blur' },
          { validator: this.fromAndToValidator('otherMonthlyIncomeFrom', 'otherMonthlyIncomeTo'), trigger: 'blur' }
        ],
        employerIndustry: [
          { required: true, message: this.$t('soi.customerBulkCreation.employerIndustryPlaceholder'), trigger: 'change' }
        ],
        customerStatus: [
          { required: true, message: this.$t('soi.customerBulkCreation.customerStatusPlaceholder'), trigger: 'change' }
        ],
        total: [
          { required: true, message: this.$t('soi.customerBulkCreation.totalCustomersPlaceholder'), trigger: 'blur' },
          { pattern: /^[1-9]\d*$/, message: this.$t('soi.common.invalidNumber'), trigger: 'blur' }
        ],
        tableName: [
          { required: true, message: this.$t('soi.customerBulkCreation.tableNamePlaceholder'), trigger: 'change' }
        ],
        accountType: [
          { required: true, message: this.$t('soi.customerBulkCreation.accountTypePlaceholder'), trigger: 'change' }
        ],
        currencyCode: [
          { required: true, message: this.$t('soi.customerBulkCreation.currencyCodePlaceholder'), trigger: 'change' }
        ]
      },
      accommodation: [],
      countryOfBirthList: [],
      customerIDType: [],
      employerIndustry: [],
      nationality: [],
      occupationOptions: [],
      contactPreferredMethod: [],
      preLanguage1: [],
      customerTableList: [],
      residentialRegionName: [],
      dialog: {
        visible: false,
        title: '',
        form: {
          dataList: []
        },
        label: '',
        selectRule: [],
        selectPlaceholder: '',
        inputRule: [],
        inputPlaceholder: '',
        maxSize: null,
        type: '',
        candidateList: []
      },
      incomeDialog: {
        visible: false,
        title: '',
        form: {
          dataList: []
        },
        frRule: [],
        frPlaceholder: '',
        toRule: [],
        toPlaceholder: '',
        inputRule: [],
        inputPlaceholder: '',
        maxSize: null,
        type: ''
      }
    }
  },
  computed: {
    ...mapGetters(['userDetails'])
  },
  mounted() {
    const _this = this
    _this.$emit('changeLoadingStatus', true)
    optionalGenerate().then(function(res) {
      _this.accommodation = res.data.accommodation.map(item => ({
        label: getAccommodationLabelByValue(item),
        value: item
      }))
      _this.countryOfBirthList = res.data.countryOfBirth
      _this.customerIDType = res.data.customerIDType
      _this.employerIndustry = res.data.employerIndustry
      _this.nationality = res.data.nationality.map(item => ({
        label: item,
        value: item
      }))
      _this.contactPreferredMethod = res.data.contactPreferredMethod.map(item => ({
        label: item,
        value: item
      }))
      _this.preLanguage1 = res.data.preLanguage1.map(item => ({
        label: item,
        value: item
      }))
      _this.residentialRegionName = res.data.residentialRegionName.map(item => ({
        label: item,
        value: item
      }))

      getCustomerOccupationList()
        .then(function(res) {
          _this.occupationOptions = res.data
        })
      getTableList({ type: 'Customer' })
        .then(function(res) {
          _this.customerTableList = res.data
          if (_this.customerTableList && _this.customerTableList.length > 0) {
            _this.customerForm.tableName = _this.customerTableList[0]
          }
        })
    }).finally(() => {
      _this.$emit('changeLoadingStatus', false)
    })
  },
  methods: {
    submitForm() {
      this.$refs['CustomerForm'].validate((valid) => {
        if (valid) {
          const _this = this
          _this.$emit('changeLoadingStatus', true)
          const requestData = this.getCustomerRequestData()
          bulkGenerate(requestData)
            .then(function(res) {
              _this.$message.success(res.message)
            })
            .finally(() => {
              _this.$emit('changeLoadingStatus', false)
            })
        } else {
          return false
        }
      })
    },
    getCustomerRequestData() {
      return {
        ...this.customerForm,
        branchNumber: {
          'fr': this.customerForm.branchNumberFrom,
          'to': this.customerForm.branchNumberTo
        },
        ageGroup: this.generateOptionalValue(this.customerForm.ageGroup),
        gender: this.generateOptionalValue(this.customerForm.gender),
        nationality1: this.generateOptionalValue(this.customerForm.nationality1),
        permanentResidenceStatus: this.generateOptionalValue(this.customerForm.permanentResidenceStatus),
        maritalStatus: this.generateOptionalValue(this.customerForm.maritalStatus),
        education: this.generateOptionalValue(this.customerForm.education),
        accommodation: this.generateOptionalValue(this.customerForm.accommodation),
        region: this.generateOptionalValue(this.customerForm.region),
        createDate: {
          'fr': this.customerForm.createDate[0],
          'to': this.customerForm.createDate[1]
        },
        contactPreferredMethod: this.generateOptionalValue(this.customerForm.contactPreferredMethod),
        preLanguage1: this.generateOptionalValue(this.customerForm.preLanguage1),
        sensitiveStatus: this.generateOptionalValue(this.customerForm.sensitiveStatus),
        employmentStatus: this.generateOptionalValue(this.customerForm.employmentStatus),
        monthlyIncome: this.generateIncomeValue(this.customerForm.monthlyIncome),
        householdIncome: this.generateIncomeValue(this.customerForm.householdIncome),
        otherMonthlyIncome: {
          'fr': this.customerForm.otherMonthlyIncomeFrom,
          'to': this.customerForm.otherMonthlyIncomeTo
        },
        userId: this.userDetails.id,
        accountList: this.customerForm.accountType.map(item => {
          return {
            accountTable: this.accountTableMap.get(item),
            accountType: item,
            creationDate: {
              fr: this.customerForm.createDate[0],
              to: this.customerForm.createDate[1]
            },
            currencyCode: item === '003' ? this.customerForm.currencyCode : 'HKD',
            customerTable: this.customerForm.tableName,
            relAccountTable: item !== '001' && item !== '002' ? 'savingaccountmaster' : '',
            total: this.customerForm.accountTotal,
            userId: this.userDetails.id
          }
        })
      }
    },
    generateOptionalValue(dataList) {
      const result = {}
      const fields = ['first', 'second', 'third', 'fourth', 'fifth', 'sixth', 'seventh']
      for (let i = 0; i < fields.length; i++) {
        const fieldName = fields[i]
        if (i < dataList.length) {
          result[fieldName] = dataList[i].select
          result[`${fieldName}Ratio`] = dataList[i].input
        } else {
          result[fieldName] = ''
          result[`${fieldName}Ratio`] = 0
        }
      }
      return result
    },
    resetForm(formName) {
      this.$refs[formName].resetFields()
    },
    openDynamicDataDialog(title, dataList, label, selectRule,
      selectPlaceholder, inputRule, inputPlaceholder, maxSize, type, candidateList
    ) {
      this.dialog = {
        visible: true,
        title: title,
        form: {
          dataList: dataList.length !== 0 ? dataList : [{ select: '', input: null }]
        },
        label: label,
        selectRule: selectRule,
        selectPlaceholder: selectPlaceholder,
        inputRule: inputRule,
        inputPlaceholder: inputPlaceholder,
        maxSize: maxSize,
        type: type,
        candidateList: candidateList
      }
    },
    handleDialogFormSubmission(dataList, type) {
      this.customerForm[type] = dataList
      this.dialog.visible = false
    },
    handleDialogClosed() {
      this.dialog.visible = false
    },
    openIncomeDynamicDataDialog(title, dataList, frRule, frPlaceholder, toRule, toPlaceholder, inputRule, inputPlaceholder, maxSize, type) {
      this.incomeDialog = {
        visible: true,
        title: title,
        form: {
          dataList: dataList.length !== 0 ? dataList : [{ form: '', to: '', input: null }]
        },
        frRule: frRule,
        frPlaceholder: frPlaceholder,
        toRule: toRule,
        toPlaceholder: toPlaceholder,
        inputRule: inputRule,
        inputPlaceholder: inputPlaceholder,
        maxSize: maxSize,
        type: type
      }
    },
    handleIncomeDialogFormSubmission(dataList, type) {
      this.customerForm[type] = dataList
      this.incomeDialog.visible = false
    },
    handleIncomeDialogClosed() {
      this.incomeDialog.visible = false
    },
    generateIncomeValue(dataList) {
      const result = {}
      const fields = ['first', 'second', 'third']
      for (let i = 0; i < fields.length; i++) {
        const fieldName = fields[i]
        if (i < dataList.length) {
          result[fieldName] = {
            fr: dataList[i].fr,
            to: dataList[i].to
          }
          result[`${fieldName}Ratio`] = dataList[i].input
        } else {
          result[fieldName] = {}
          result[`${fieldName}Ratio`] = 0
        }
      }
      return result
    },
    fromAndToValidator(fromField, toField) {
      return (rule, value, callback) => {
        if (Number.parseInt(this.customerForm[toField]) < Number.parseInt(this.customerForm[fromField])) {
          callback(
            new Error(
              this.$t(
                'soi.customerBulkCreation.numberGreaterThanPlaceholder',
                {
                  form: this.$t(`soi.customerBulkCreation.${fromField}`),
                  to: this.$t(`soi.customerBulkCreation.${toField}`)
                }
              )
            )
          )
        } else {
          callback()
        }
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.bulk-creation-form {
  .el-form-item-inner {
    display: flex;
    align-content: center;
    justify-content: center;

    &-input {
      margin-right: 10px
    }
  }

  .full-width {
    width: 100%;
  }

  .mr-10 {
    margin-right: 10px;
  }

  .mx-10 {
    margin: 0 10px;
  }
}
</style>
