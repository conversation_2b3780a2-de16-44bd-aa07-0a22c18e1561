<template>
  <div v-loading.fullscreen.lock="loading" class="data-visualization-data-creation-container">
    <customize-card :title="$t('soi.router.dataCreation')">
      <el-form ref="Productform" :model="Productform" label-position="left" label-width="160px">
        <el-form-item label="Data Type: ">
          <el-radio-group v-model="datatype">
            <el-radio-button label="3">Customer</el-radio-button>
            <!--            <el-radio-button label="2">Transaction</el-radio-button>-->
            <!--            <el-radio-button label="1">Product</el-radio-button>-->
          </el-radio-group>
        </el-form-item>
      </el-form>
      <el-form v-show="datatype === '1'" ref="AccountForm" :model="AccountForm" :rules="AccountFormRules" label-position="left" label-width="160px">
        <el-form-item label="Account Type" prop="accountType">
          <el-select
            v-model="AccountForm.accountType"
            class="select"
            @change="handlerChangeAccountType"
          >
            <el-option
              v-for="(item, index) in accountTypeOptions"
              :key="index"
              :label="item.label"
              :value="item.value"
              :disabled="(!relAccountTableList || relAccountTableList.length === 0) && index > 1"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="Currency Code" prop="currencyCode">
          <el-select v-model="AccountForm.currencyCode" class="select">
            <el-option
              v-for="(item, index) in currencyTypeOptions"
              :key="index"
              :disabled="item.value !== 'HDK' && AccountForm.accountType !== '003'"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="Create Date">
          <el-col :span="11">
            <el-form-item label="From" prop="createDateFrom" label-width="50px">
              <el-date-picker
                v-model="AccountForm.createDateFrom"
                value-format="timestamp"
                placeholder="Start Date"
                class="date-select"
              />
            </el-form-item>
          </el-col>
          <el-col :span="2" class="line" style="text-align: center;">-</el-col>
          <el-col :span="11">
            <el-form-item label="To" prop="createDateTo" label-width="50px">
              <el-date-picker
                v-model="AccountForm.createDateTo"
                value-format="timestamp"
                placeholder="End Date"
                class="date-select"
              />
            </el-form-item>
          </el-col>
        </el-form-item>
        <el-form-item label="Customer Table" prop="customerTable">
          <el-select
            v-model="AccountForm.customerTable"
            class="input"
            default-first-option
            placeholder=""
          >
            <el-option
              v-for="item in customerTableList"
              :key="item"
              :label="item"
              :value="item"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="Account Table" prop="accountTable">
          <el-select
            v-model="AccountForm.accountTable"
            filterable
            allow-create
            class="input"
            default-first-option
            placeholder=""
          >
            <el-option
              v-for="item in accountTableList"
              :key="item"
              :label="item"
              :value="item"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          v-if="AccountForm.accountType !== '001' && AccountForm.accountType !== '002'"
          label="Rel Account Table"
          prop="relAccountTable"
        >
          <el-select
            v-model="AccountForm.relAccountTable"
            class="input"
            default-first-option
            placeholder=""
          >
            <el-option
              v-for="item in relAccountTableList"
              :key="item"
              :label="item"
              :value="item"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="Total" prop="total">
          <el-input v-model="AccountForm.total" placeholder="Total" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="submitForm('AccountForm')">Submit</el-button>
        </el-form-item>
      </el-form>
      <el-form v-show="datatype === '2'" ref="Transactionform" :model="Transactionform" label-position="left" label-width="160px">
        <el-form-item label="Transaction Type: ">
          <el-select v-model="Transactionform.type" style="width:100%;" placeholder="" @change="refreshValidate">
            <el-option label="Transfer" value="Transfer" />
            <el-option label="Payment" value="Payment" />
            <el-option label="Credit Card" value="Credit Card" />
            <el-option label="Term Deposit" value="Term Deposit" />
            <el-option label="Foreign Exchange" value="Foreign Exchange" />
            <el-option label="Stock Trading" value="Stock Trading" />
            <el-option label="Fund Trading" value="Fund Trading" />
          </el-select>
        </el-form-item>
        <el-form-item label="Data Source: ">
          <el-select v-model="Transactionform.dataSource" style="width:100%;" placeholder="">
            <el-option label="10W" value="2" />
            <el-option label="NEW" value="1" />
          </el-select>
        </el-form-item>
      </el-form>
      <!--Transfer-->
      <el-form v-show="Transactionform.type === 'Transfer' && datatype === '2'" ref="Transferform" :rules="Transferformrules" :model="Transferform" label-position="left" label-width="160px">
        <el-form-item label="Channel: " prop="channelID">
          <el-select v-model="Transferform.channelID" style="width:100%;" placeholder="">
            <el-option v-for="item in channellist" :key="item.id" :value="item.id" :label="item.name" />
          </el-select>
        </el-form-item>
        <el-form-item label="Transfer From Type: ">
          <el-radio-group v-model="TransferformSelectType1">
            <el-radio-button label="1">Branch Number</el-radio-button>
            <el-radio-button label="2">Link to Datatable(Branch)</el-radio-button>
            <el-radio-button label="3">Account Number</el-radio-button>
            <el-radio-button label="4">Link to Datatable(Account)</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="TransferformSelectType1 == '1'" label="Transfer From: " prop="branchFr">
          <el-input v-model="Transferform.branchFr" placeholder="" />
        </el-form-item>
        <el-form-item v-if="TransferformSelectType1 == '2'" label="Transfer From: ">
          <el-upload
            :action="datahost+'/file/upload'"
            :before-remove="beforeRemove"
            :limit="1"
            :on-change="exportData"
            :on-exceed="handleExceed"
            :on-success="branchFrListSuccess"
            :file-list="branchFrList"
            class="upload-demo"
            accept=".csv,.xls,.xlsx"
          >
            <el-button size="small" type="primary">Upload</el-button>
            <span slot="tip" class="el-upload__tip">only upload csv/xls/xlsx files, not exceed 5MB.</span>
          </el-upload>
        </el-form-item>
        <el-form-item v-if="TransferformSelectType1 == '3'" label="Transfer From: " prop="accountNumberFr">
          <el-input v-model="Transferform.accountNumberFr" placeholder="" />
        </el-form-item>
        <el-form-item v-if="TransferformSelectType1 == '4'" label="Transfer From: ">
          <el-upload
            :action="datahost+'/file/upload'"
            :before-remove="beforeRemove"
            :limit="1"
            :on-change="exportData"
            :on-exceed="handleExceed"
            :on-success="accountFrListSuccess"
            :file-list="accountFrList"
            class="upload-demo"
            accept=".csv,.xls,.xlsx"
          >
            <el-button size="small" type="primary">Upload</el-button>
            <span slot="tip" class="el-upload__tip">only upload csv/xls/xlsx files, not exceed 5MB.</span>
          </el-upload>
        </el-form-item>
        <el-form-item label="Account Type From: " prop="accountTypeFr">
          <el-select v-model="Transferform.accountTypeFr" style="width:100%;" placeholder="">
            <el-option label="Saving" value="Saving" />
            <el-option label="Current" value="Current" />
          </el-select>
        </el-form-item>
        <el-form-item label="Transfer To Type: ">
          <el-radio-group v-model="TransferformSelectType2">
            <el-radio-button label="1">Branch Number</el-radio-button>
            <el-radio-button label="2">Link to Datatable(Branch)</el-radio-button>
            <el-radio-button label="3">Account Number</el-radio-button>
            <el-radio-button label="4">Link to Datatable(Account)</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="TransferformSelectType2 == '1'" label="Transfer To: " prop="branchTo">
          <el-input v-model="Transferform.branchTo" placeholder="" />
        </el-form-item>
        <el-form-item v-if="TransferformSelectType2 == '2'" label="Transfer To: ">
          <el-upload
            :action="datahost+'/file/upload'"
            :before-remove="beforeRemove"
            :limit="1"
            :on-change="exportData"
            :on-exceed="handleExceed"
            :on-success="branchToListSuccess"
            :file-list="branchToList"
            class="upload-demo"
            accept=".csv,.xls,.xlsx"
          >
            <el-button size="small" type="primary">Upload</el-button>
            <span slot="tip" class="el-upload__tip">only upload csv/xls/xlsx files, not exceed 5MB.</span>
          </el-upload>
        </el-form-item>
        <el-form-item v-if="TransferformSelectType2 == '3'" label="Transfer To: " prop="accountNumberTo">
          <el-input v-model="Transferform.accountNumberTo" placeholder="" />
        </el-form-item>
        <el-form-item v-if="TransferformSelectType2 == '4'" label="Transfer To: ">
          <el-upload
            :action="datahost+'/file/upload'"
            :before-remove="beforeRemove"
            :limit="1"
            :on-change="exportData"
            :on-exceed="handleExceed"
            :on-success="accountToListSuccess"
            :file-list="accountToList"
            class="upload-demo"
            accept=".csv,.xls,.xlsx"
          >
            <el-button size="small" type="primary">Upload</el-button>
            <span slot="tip" class="el-upload__tip">only upload csv/xls/xlsx files, not exceed 5MB.</span>
          </el-upload>
        </el-form-item>
        <el-form-item label="Account Type To: " prop="accountTypeTo">
          <el-select v-model="Transferform.accountTypeTo" style="width:100%;" placeholder="">
            <el-option label="Saving" value="Saving" />
            <el-option label="Current" value="Current" />
          </el-select>
        </el-form-item>
        <el-form-item label="Transfer Amount: ">
          <el-col :span="11">
            <el-form-item :rules="{required: true, validator: validateLimitValue, trigger: 'blur'}" label="From:" prop="amountFr" label-width="60px">
              <el-input v-model="Transferform.amountFr" />
            </el-form-item>
          </el-col>
          <el-col :span="2" class="line" style="text-align: center;">-</el-col>
          <el-col :span="11">
            <el-form-item :rules="{required: true, validator: validateLimitValue, trigger: 'blur'}" label="To:" prop="amountFr" label-width="60px">
              <el-input v-model="Transferform.amountTo" />
            </el-form-item>
          </el-col>
        </el-form-item>
        <el-form-item label="Transaction Date: ">
          <el-col :span="11">
            <el-form-item label="From: " prop="transactionFromDate" label-width="60px">
              <el-date-picker v-model="Transferform.transactionFromDate" type="date" value-format="timestamp" style="width: 60%;" placeholder="Start Date" />
            </el-form-item>
          </el-col>
          <el-col :span="2" class="line" style="text-align: center;">-</el-col>
          <el-col :span="11">
            <el-form-item label="To: " prop="transactionToDate" label-width="60px">
              <el-date-picker v-model="Transferform.transactionToDate" type="date" value-format="timestamp" style="width: 60%;" placeholder="End Date" />
            </el-form-item>
          </el-col>
        </el-form-item>
        <el-form-item label="Frequency: " prop="frequency">
          <el-select v-model="Transferform.frequency" style="width:100%;" placeholder="">
            <el-option v-for="item in frequencylist" :key="item" :value="item" :label="item" />
          </el-select>
        </el-form-item>
        <el-form-item label="Frequency rate: " prop="frequencyRate">
          <el-input v-model="Transferform.frequencyRate" />
        </el-form-item>
        <el-form-item label="Total Transaction: " prop="total">
          <el-input v-model="Transferform.total" />
        </el-form-item>
        <el-form-item label="Table Name: " prop="tableName">
          <el-select
            v-model="Transferform.tableName"
            filterable
            allow-create
            style="width:100%;"
            default-first-option
            placeholder=""
          >
            <el-option
              v-for="item in tableNamelist"
              :key="item"
              :label="item"
              :value="item"
            />
          </el-select>
        </el-form-item>
        <div style="text-align:center;">
          <el-button type="primary" @click="submitForm('Transferform')">Submit</el-button>
          <el-button @click="resetForm('Transferform')">Reset</el-button>
        </div>
      </el-form>
      <!--Payment-->
      <el-form v-show="Transactionform.type === 'Payment' && datatype === '2'" ref="Paymentform" :rules="Paymentformrules" :model="Paymentform" label-position="left" label-width="160px">
        <el-form-item label="Account Type From: " prop="accountTypeFr">
          <el-select v-model="Paymentform.accountTypeFr" style="width:100%;" placeholder="">
            <el-option label="Saving" value="Saving" />
            <el-option label="Current" value="Current" />
          </el-select>
        </el-form-item>
        <el-form-item label="Channel: " prop="channelID">
          <el-select v-model="Paymentform.channelID" style="width:100%;" placeholder="">
            <el-option v-for="item in channellist" :key="item.id" :value="item.id" :label="item.name" />
          </el-select>
        </el-form-item>
        <el-form-item label="Move Money From Type: ">
          <el-radio-group v-model="PaymentformSelectType">
            <el-radio-button label="1">Branch Number</el-radio-button>
            <el-radio-button label="2">Link to Datatable</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="PaymentformSelectType == '1'" label-width="160px" label="Move Money From: " prop="moveFr">
          <el-input v-model="Paymentform.moveFr" placeholder="" />
        </el-form-item>
        <el-form-item v-if="PaymentformSelectType == '2'" label-width="160px" label="Move Money From: ">
          <el-upload
            :action="datahost+'/file/upload'"
            :before-remove="beforeRemove"
            :limit="1"
            :on-change="exportData"
            :on-exceed="handleExceed"
            :on-success="PaymentformFileListSuccess"
            :file-list="PaymentformFileList"
            class="upload-demo"
            accept=".csv,.xls,.xlsx"
          >
            <el-button size="small" type="primary">Upload</el-button>
            <span slot="tip" class="el-upload__tip">only upload csv/xls/xlsx files, not exceed 5MB.</span>
          </el-upload>
        </el-form-item>
        <el-form-item label="Move Money To: ">
          <el-button type="primary" size="mini" @click="addPaymentformCondition()">Add</el-button>
          <el-row v-for="(data,index) in Paymentform.moveToList" :key="index" :gutter="20" style="margin:0 0 20px 0;">
            <el-col :span="9">
              <el-form-item :label="'Industry'+(index+1)" :prop="'moveToList.' + index + '.payeeCategoryID'" :rules="{required: true, message: 'Please select a industry', trigger: 'change'}" label-width="95px">
                <el-select v-model="data.payeeCategoryID" style="width:100%;">
                  <el-option v-for="item in paymentindustrylist" :key="item.payeeCategoryID" :value="item.payeeCategoryID" :label="item.payeeCategory" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="9">
              <el-form-item :prop="'moveToList.' + index + '.amountFr'" :rules="{required: true, validator: validateLimitValue, trigger: 'blur'}" label="Amount From: " label-width="120px">
                <el-input v-model="data.amountFr" style="margin:0;" />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item :prop="'moveToList.' + index + '.amountTo'" :rules="{required: true, validator: validateLimitValue, trigger: 'blur'}" label="To" label-width="50px">
                <el-input v-model="data.amountTo" style="margin:0;" />
              </el-form-item>
            </el-col>
            <div v-if="index > 0" class="text-center">
              <el-button type="primary" style="width:100px;margin-top:20px;" size="mini" @click="handleDeletePaymentform(index)">Delete</el-button>
            </div>
          </el-row>
        </el-form-item>
        <el-form-item label="Transaction Date: ">
          <el-col :span="11">
            <el-form-item label="From: " prop="transactionFromDate" label-width="60px">
              <el-date-picker v-model="Paymentform.transactionFromDate" type="date" value-format="timestamp" style="width: 60%;" placeholder="Start Date" />
            </el-form-item>
          </el-col>
          <el-col :span="2" class="line" style="text-align: center;">-</el-col>
          <el-col :span="11">
            <el-form-item label="To: " prop="transactionToDate" label-width="60px">
              <el-date-picker v-model="Paymentform.transactionToDate" type="date" value-format="timestamp" style="width: 60%;" placeholder="End Date" />
            </el-form-item>
          </el-col>
        </el-form-item>
        <el-form-item label="Frequency: " prop="frequency">
          <el-select v-model="Paymentform.frequency" style="width:100%;" placeholder="">
            <el-option v-for="item in frequencylist" :key="item" :value="item" :label="item" />
          </el-select>
        </el-form-item>
        <el-form-item label="Frequency rate: " prop="frequencyRate">
          <el-input v-model="Paymentform.frequencyRate" />
        </el-form-item>
        <el-form-item label="Total Transaction: " prop="total">
          <el-input v-model="Paymentform.total" />
        </el-form-item>
        <el-form-item label="Payment Table: " prop="paymentTable">
          <el-select
            v-model="Paymentform.paymentTable"
            filterable
            allow-create
            style="width:100%;"
            default-first-option
            placeholder=""
          >
            <el-option
              v-for="item in tableNamelist"
              :key="item"
              :label="item"
              :value="item"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="Transfer Table: " prop="transferTable">
          <el-select
            v-model="Paymentform.transferTable"
            filterable
            allow-create
            style="width:100%;"
            default-first-option
            placeholder=""
          >
            <el-option
              v-for="item in tableNamelist"
              :key="item"
              :label="item"
              :value="item"
            />
          </el-select>
        </el-form-item>
        <div style="text-align:center;">
          <el-button type="primary" @click="submitForm('Paymentform')">Submit</el-button>
          <el-button @click="resetForm('Paymentform')">Reset</el-button>
        </div>
      </el-form>
      <!--Credit Card-->
      <el-form v-show="Transactionform.type === 'Credit Card' && datatype === '2'" ref="CreditCardform" :rules="CreditCardformrules" :model="CreditCardform" label-position="left" label-width="160px">
        <el-form-item label="Transaction By Type: ">
          <el-radio-group v-model="CreditCardformSelectType">
            <el-radio-button label="1">Branch Number</el-radio-button>
            <el-radio-button label="2">Link to Datatable</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="CreditCardformSelectType == '1'" label-width="160px" label="Transaction By: ">
          <p>Card Sequence number </p>
          <el-col :span="11">
            <el-form-item :rules="{required: true, validator: validateLimitValue, trigger: 'blur'}" label="From:" prop="sequenceFr" label-width="60px">
              <el-input v-model="CreditCardform.sequenceFr" />
            </el-form-item>
          </el-col>
          <el-col :span="2" class="line" style="text-align: center;">-</el-col>
          <el-col :span="11">
            <el-form-item :rules="{required: true, validator: validateLimitValue, trigger: 'blur'}" label="To:" prop="sequenceTo" label-width="60px">
              <el-input v-model="CreditCardform.sequenceTo" />
            </el-form-item>
          </el-col>
        </el-form-item>
        <el-form-item v-if="CreditCardformSelectType == '2'" label-width="160px" label="Transaction By: ">
          <p>Card Sequence number </p>
          <el-upload
            :action="datahost+'/file/upload'"
            :before-remove="beforeRemove"
            :limit="1"
            :on-change="exportData"
            :on-exceed="handleExceed"
            :on-success="CreditCardformFileListSuccess"
            :file-list="CreditCardformFileList"
            class="upload-demo"
            accept=".csv,.xls,.xlsx"
          >
            <el-button size="small" type="primary">Upload</el-button>
            <span slot="tip" class="el-upload__tip">only upload csv/xls/xlsx files, not exceed 5MB.</span>
          </el-upload>
        </el-form-item>
        <el-form-item label="Transaction To: ">
          <el-button type="primary" size="mini" @click="addCreditCardformCondition()">Add</el-button>
          <el-row v-for="(data,index) in CreditCardform.transToList" :key="index" :gutter="20" style="margin:0 0 20px 0;">
            <el-col :span="9">
              <el-form-item :label="'Industry'+(index+1)" :prop="'transToList.' + index + '.categoryname'" :rules="{required: true, message: 'Please select a industry', trigger: 'change'}" label-width="95px">
                <el-select v-model="data.categoryname" style="width:100%;">
                  <el-option v-for="item in creditcardindustrylist" :key="item" :value="item" :label="item" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="9">
              <el-form-item :prop="'transToList.' + index + '.amountFr'" :rules="{required: true, validator: validateLimitValue, trigger: 'blur'}" label="Amount From: " label-width="120px">
                <el-input v-model="data.amountFr" style="margin:0;" />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item :prop="'transToList.' + index + '.amountTo'" :rules="{required: true, validator: validateLimitValue, trigger: 'blur'}" label="To" label-width="50px">
                <el-input v-model="data.amountTo" style="margin:0;" />
              </el-form-item>
            </el-col>
            <div v-if="index > 0" class="text-center">
              <el-button type="primary" style="width:100px;margin-top:20px;" size="mini" @click="handleDeleteCreditCardform(index)">Delete</el-button>
            </div>
          </el-row>
        </el-form-item>
        <el-form-item label="Transaction Date: ">
          <el-col :span="11">
            <el-form-item label="From: " prop="transactionFromDate" label-width="60px">
              <el-date-picker v-model="CreditCardform.transactionFromDate" type="date" value-format="timestamp" style="width: 60%;" placeholder="Start Date" />
            </el-form-item>
          </el-col>
          <el-col :span="2" class="line" style="text-align: center;">-</el-col>
          <el-col :span="11">
            <el-form-item label="To: " prop="transactionToDate" label-width="60px">
              <el-date-picker v-model="CreditCardform.transactionToDate" type="date" value-format="timestamp" style="width: 60%;" placeholder="End Date" />
            </el-form-item>
          </el-col>
        </el-form-item>
        <el-form-item label="Frequency: " prop="frequency">
          <el-select v-model="CreditCardform.frequency" style="width:100%;" placeholder="">
            <el-option v-for="item in frequencylist" :key="item" :value="item" :label="item" />
          </el-select>
        </el-form-item>
        <el-form-item label="Frequency rate: " prop="frequencyRate">
          <el-input v-model="CreditCardform.frequencyRate" />
        </el-form-item>
        <el-form-item label="Total Transaction: " prop="total">
          <el-input v-model="CreditCardform.total" />
        </el-form-item>
        <el-form-item label="Table Name: " prop="tableName">
          <el-select
            v-model="CreditCardform.tableName"
            filterable
            allow-create
            style="width:100%;"
            default-first-option
            placeholder=""
          >
            <el-option
              v-for="item in tableNamelist"
              :key="item"
              :label="item"
              :value="item"
            />
          </el-select>
        </el-form-item>
        <div style="text-align:center;">
          <el-button type="primary" @click="submitForm('CreditCardform')">Submit</el-button>
          <el-button @click="resetForm('CreditCardform')">Reset</el-button>
        </div>
      </el-form>
      <!--Term Deposit-->
      <el-form v-show="Transactionform.type === 'Term Deposit' && datatype === '2'" ref="TermDepositform" :rules="TermDepositformrules" :model="TermDepositform" label-position="left" label-width="160px">
        <el-form-item label="Transaction Account Type: ">
          <el-radio-group v-model="TermDepositformSelectType">
            <el-radio-button label="1">Branch Number</el-radio-button>
            <el-radio-button label="2">Link to Datatable</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="TermDepositformSelectType == '1'" label-width="160px" label="Transaction Account: " prop="branchFr">
          <label style="color: #606266;width: 120px;">Branch Number:</label>
          <el-input v-model="TermDepositform.branchFr" style="width:225px;" />
        </el-form-item>
        <el-form-item v-if="TermDepositformSelectType == '2'" label-width="160px" label="Transaction Account: ">
          <el-upload
            :action="datahost+'/file/upload'"
            :before-remove="beforeRemove"
            :limit="1"
            :on-change="exportData"
            :on-exceed="handleExceed"
            :on-success="TermDepositformFileListSuccess"
            :file-list="TermDepositformFileList"
            class="upload-demo"
            accept=".csv,.xls,.xlsx"
          >
            <el-button size="small" type="primary">Upload</el-button>
            <span slot="tip" class="el-upload__tip">only upload csv/xls/xlsx files, not exceed 5MB.</span>
          </el-upload>
        </el-form-item>
        <el-form-item label="Channel: " prop="channelID">
          <el-select v-model="TermDepositform.channelID" style="width:100%;" placeholder="">
            <el-option v-for="item in channellist" :key="item.id" :value="item.id" :label="item.name" />
          </el-select>
        </el-form-item>
        <el-form-item label="Transaction Details: ">
          <el-button type="primary" size="mini" @click="addTermDepositformCondition()">Add</el-button>
          <el-row v-for="(data,index) in TermDepositform.transToList" :key="index" :gutter="10" style="margin:0 0 30px 0;">
            <el-col :span="6">
              <el-form-item :label="'Period'+(index+1)" :prop="'transToList.' + index + '.TermPeriod'" :rules="{required: true, message: 'Please select a period', trigger: 'change'}" label-width="80px">
                <el-select v-model="data.TermPeriod" style="width:100%;">
                  <el-option v-for="item in termdepositperiodlist" :key="item" :value="item" :label="item" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item :prop="'transToList.' + index + '.amountFr'" :rules="{required: true, validator: validateLimitValue, trigger: 'blur'}" label="Amount From: " label-width="120px">
                <el-input v-model="data.amountFr" style="margin:0;" />
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item :prop="'transToList.' + index + '.amountTo'" :rules="{required: true, validator: validateLimitValue, trigger: 'blur'}" label="To" label-width="40px">
                <el-input v-model="data.amountTo" style="margin:0;" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :prop="'transToList.' + index + '.settleAccType'" :rules="{required: true, message: 'Please select a account type', trigger: 'change'}" label="Settlement Account: " label-width="160px">
                <el-select v-model="data.settleAccType" style="width:100%;" placeholder="">
                  <el-option label="Saving" value="Saving" />
                  <el-option label="Current" value="Current" />
                </el-select>
              </el-form-item>
            </el-col>
            <div v-if="index > 0" class="text-center">
              <el-button type="primary" style="width:100px;margin-top:20px;" size="mini" @click="handleDeleteTermDepositform(index)">Delete</el-button>
            </div>
          </el-row>
        </el-form-item>
        <el-form-item label="Transaction Date: ">
          <el-col :span="11">
            <el-form-item label="From: " prop="transactionFromDate" label-width="60px">
              <el-date-picker v-model="TermDepositform.transactionFromDate" type="date" value-format="timestamp" style="width: 60%;" placeholder="Start Date" />
            </el-form-item>
          </el-col>
          <el-col :span="2" class="line" style="text-align: center;">-</el-col>
          <el-col :span="11">
            <el-form-item label="To: " prop="transactionToDate" label-width="60px">
              <el-date-picker v-model="TermDepositform.transactionToDate" type="date" value-format="timestamp" style="width: 60%;" placeholder="End Date" />
            </el-form-item>
          </el-col>
        </el-form-item>
        <el-form-item label="Frequency: " prop="frequency">
          <el-select v-model="TermDepositform.frequency" style="width:100%;" placeholder="">
            <el-option v-for="item in frequencylist" :key="item" :value="item" :label="item" />
          </el-select>
        </el-form-item>
        <el-form-item label="Frequency rate: " prop="frequencyRate">
          <el-input v-model="TermDepositform.frequencyRate" />
        </el-form-item>
        <el-form-item label="Total Transaction: " prop="total">
          <el-input v-model="TermDepositform.total" />
        </el-form-item>
        <el-form-item label="Term Table: " prop="termTable">
          <el-select
            v-model="TermDepositform.termTable"
            filterable
            allow-create
            style="width:100%;"
            default-first-option
            placeholder=""
          >
            <el-option
              v-for="item in tableNamelist"
              :key="item"
              :label="item"
              :value="item"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="Transfer Table: " prop="transferTable">
          <el-select
            v-model="TermDepositform.transferTable"
            filterable
            allow-create
            style="width:100%;"
            default-first-option
            placeholder=""
          >
            <el-option
              v-for="item in tableNamelist"
              :key="item"
              :label="item"
              :value="item"
            />
          </el-select>
        </el-form-item>
        <div style="text-align:center;">
          <el-button type="primary" @click="submitForm('TermDepositform')">Submit</el-button>
          <el-button @click="resetForm('TermDepositform')">Reset</el-button>
        </div>
      </el-form>
      <!--Foreign Exchange-->
      <el-form v-show="Transactionform.type === 'Foreign Exchange' && datatype === '2'" ref="ForeignExchangeform" :rules="ForeignExchangeformrules" :model="ForeignExchangeform" label-position="left" label-width="160px">
        <el-form-item label="Transaction Account Type: ">
          <el-radio-group v-model="ForeignExchangeformSelectType">
            <el-radio-button label="1">Branch Number</el-radio-button>
            <el-radio-button label="2">Link to Datatable</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="ForeignExchangeformSelectType == '1'" label-width="160px" label="Transaction Account: " prop="branchFr">
          <label style="color: #606266;width: 120px;">Branch Number:</label>
          <el-input v-model="ForeignExchangeform.branchFr" style="width:225px;" />
        </el-form-item>
        <el-form-item v-if="ForeignExchangeformSelectType == '2'" label-width="160px" label="Transaction Account: ">
          <el-upload
            :action="datahost+'/file/upload'"
            :before-remove="beforeRemove"
            :limit="1"
            :on-change="exportData"
            :on-exceed="handleExceed"
            :on-success="ForeignExchangeformFileListSuccess"
            :file-list="ForeignExchangeformFileList"
            class="upload-demo"
            accept=".csv,.xls,.xlsx"
          >
            <el-button size="small" type="primary">Upload</el-button>
            <span slot="tip" class="el-upload__tip">only upload csv/xls/xlsx files, not exceed 5MB.</span>
          </el-upload>
        </el-form-item>
        <el-form-item label="Channel: " prop="channelID">
          <el-select v-model="ForeignExchangeform.channelID" style="width:100%;" placeholder="">
            <el-option v-for="item in channellist" :key="item.id" :value="item.id" :label="item.name" />
          </el-select>
        </el-form-item>
        <el-form-item label="Transaction Type: ">
          <el-select v-model="ForeignExchangeform.transType" style="width:100%;" placeholder="">
            <el-option label="Buy" value="Buy" />
            <el-option label="Sell" value="Sell" />
          </el-select>
        </el-form-item>
        <el-form-item label="Transaction Ccy: ">
          <el-button type="primary" size="mini" @click="addForeignExchangeformCondition()">Add</el-button>
          <el-row v-for="(data,index) in ForeignExchangeform.transCcyList" :key="index" :gutter="10" style="margin:0 0 30px 0;">
            <el-col :span="6">
              <el-form-item :label="'0'+(index+1)" :prop="'transCcyList.' + index + '.Ccy'" :rules="{required: true, message: 'Please select a ccy', trigger: 'change'}" label-width="40px">
                <el-select v-model="data.Ccy" style="width:100%;">
                  <el-option v-for="item in fexccylist" :key="item" :value="item" :label="item" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item :prop="'transCcyList.' + index + '.amountFr'" :rules="{required: true, validator: validateLimitValue, trigger: 'blur'}" label="Amount From: " label-width="120px">
                <el-input v-model="data.amountFr" style="margin:0;" />
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item :prop="'transCcyList.' + index + '.amountTo'" :rules="{required: true, validator: validateLimitValue, trigger: 'blur'}" label="To" label-width="40px">
                <el-input v-model="data.amountTo" style="margin:0;" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :prop="'transCcyList.' + index + '.settleAccType'" :rules="{required: true, message: 'Please select a account type', trigger: 'change'}" label="Settlement Account: " label-width="160px">
                <el-select v-model="data.settleAccType" style="width:100%;" placeholder="">
                  <el-option label="Saving" value="Saving" />
                  <el-option label="Current" value="Current" />
                </el-select>
              </el-form-item>
            </el-col>
            <div v-if="index > 0" class="text-center">
              <el-button type="primary" style="width:100px;margin-top:30px;" size="mini" @click="handleDeleteForeignExchangeform(index)">Delete</el-button>
            </div>
          </el-row>
        </el-form-item>
        <el-form-item label="Transaction Date: ">
          <el-col :span="11">
            <el-form-item label="From: " prop="transactionFromDate" label-width="60px">
              <el-date-picker v-model="ForeignExchangeform.transactionFromDate" type="date" value-format="timestamp" style="width: 60%;" placeholder="Start Date" />
            </el-form-item>
          </el-col>
          <el-col :span="2" class="line" style="text-align: center;">-</el-col>
          <el-col :span="11">
            <el-form-item label="To: " prop="transactionToDate" label-width="60px">
              <el-date-picker v-model="ForeignExchangeform.transactionToDate" type="date" value-format="timestamp" style="width: 60%;" placeholder="End Date" />
            </el-form-item>
          </el-col>
        </el-form-item>
        <el-form-item label="Frequency: " prop="frequency">
          <el-select v-model="ForeignExchangeform.frequency" style="width:100%;" placeholder="">
            <el-option v-for="item in frequencylist" :key="item" :value="item" :label="item" />
          </el-select>
        </el-form-item>
        <el-form-item label="Frequency rate: " prop="frequencyRate">
          <el-input v-model="ForeignExchangeform.frequencyRate" />
        </el-form-item>
        <el-form-item label="Total Transaction: " prop="total">
          <el-input v-model="ForeignExchangeform.total" />
        </el-form-item>
        <el-form-item label="Fex Table: " prop="fexTable">
          <el-select
            v-model="ForeignExchangeform.fexTable"
            filterable
            allow-create
            style="width:100%;"
            default-first-option
            placeholder=""
          >
            <el-option
              v-for="item in tableNamelist"
              :key="item"
              :label="item"
              :value="item"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="Transfer Table: " prop="transferTable">
          <el-select
            v-model="ForeignExchangeform.transferTable"
            filterable
            allow-create
            style="width:100%;"
            default-first-option
            placeholder=""
          >
            <el-option
              v-for="item in tableNamelist"
              :key="item"
              :label="item"
              :value="item"
            />
          </el-select>
        </el-form-item>
        <div style="text-align:center;">
          <el-button type="primary" @click="submitForm('ForeignExchangeform')">Submit</el-button>
          <el-button @click="resetForm('ForeignExchangeform')">Reset</el-button>
        </div>
      </el-form>
      <!--Stock Trading-->
      <el-form v-show="Transactionform.type === 'Stock Trading' && datatype === '2'" ref="StockTradingform" :rules="StockTradingformrules" :model="StockTradingform" label-position="left" label-width="160px">
        <el-form-item label="Account Type From: " prop="accountTypeFr">
          <el-select v-model="StockTradingform.accountTypeFr" style="width:100%;" placeholder="">
            <el-option label="Saving" value="Saving" />
            <el-option label="Current" value="Current" />
          </el-select>
        </el-form-item>
        <el-form-item label="Channel: " prop="channelID">
          <el-select v-model="StockTradingform.channelID" style="width:100%;" placeholder="">
            <el-option v-for="item in channellist" :key="item.id" :value="item.id" :label="item.name" />
          </el-select>
        </el-form-item>
        <el-form-item label="Transaction Account Type: ">
          <el-radio-group v-model="StockTradingformSelectType">
            <el-radio-button label="1">Branch Number</el-radio-button>
            <el-radio-button label="2">Link to Datatable</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="StockTradingformSelectType == '1'" label-width="160px" label="Transaction Account: " prop="branchFr">
          <label style="color: #606266;width: 120px;">Branch Number:</label>
          <el-input v-model="StockTradingform.branchFr" style="width:225px;" />
        </el-form-item>
        <el-form-item v-if="StockTradingformSelectType == '2'" label-width="160px" label="Transaction Account: ">
          <el-upload
            :action="datahost+'/file/upload'"
            :before-remove="beforeRemove"
            :limit="1"
            :on-change="exportData"
            :on-exceed="handleExceed"
            :on-success="StockTradingformFileListSuccess"
            :file-list="StockTradingformFileList"
            class="upload-demo"
            accept=".csv,.xls,.xlsx"
          >
            <el-button size="small" type="primary">Upload</el-button>
            <span slot="tip" class="el-upload__tip">only upload csv/xls/xlsx files, not exceed 5MB.</span>
          </el-upload>
        </el-form-item>
        <el-form-item label="Transaction Type: ">
          <el-select v-model="StockTradingform.transType" style="width:100%;" placeholder="">
            <el-option label="Buy" value="Buy" />
            <el-option label="Sell" value="Sell" />
          </el-select>
        </el-form-item>
        <el-form-item label="Transaction Details: ">
          <el-button type="primary" size="mini" @click="addStockTradingformCondition()">Add</el-button>
          <el-row :gutter="10">
            <el-col :span="6">Order Type</el-col>
            <el-col :span="6">Stock Code</el-col>
            <el-col :span="4">No. of lots</el-col>
            <el-col :span="7">Settlement Account</el-col>
          </el-row>
          <el-row v-for="(data,index) in StockTradingform.transToList" :key="index" :gutter="10" style="margin:0 0 30px 0;">
            <el-col :span="6">
              <el-form-item :label="'0'+(index+1)" :prop="'transToList.' + index + '.orderType'" :rules="{required: true, message: 'Please select a order type', trigger: 'change'}" label-width="40px">
                <el-select v-model="data.orderType" style="width:100%;">
                  <el-option v-for="item in stockorderTypelist" :key="item" :value="item" :label="item" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label-width="0px">
                <el-upload
                  :action="datahost+'/file/upload'"
                  :before-remove="beforeRemove"
                  :limit="1"
                  :on-change="exportData"
                  :on-exceed="handleExceed"
                  :on-success="StockTradingformFileList2Success"
                  :file-list="StockTradingformFileList2[index]"
                  class="upload-demo"
                  accept=".csv,.xls,.xlsx"
                >
                  <el-button size="small" type="primary" @click="setStockTradingformFileIndex(index)">Link to Datatable</el-button>
                </el-upload>
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item :prop="'transToList.' + index + '.no'" :rules="{required: true, validator: validateLimitValue, trigger: 'blur'}" label-width="0px">
                <el-input v-model="data.no" style="margin:0;" />
              </el-form-item>
            </el-col>
            <el-col :span="7">
              <el-form-item :prop="'transToList.' + index + '.settleAccType'" :rules="{required: true, message: 'Please select a account type', trigger: 'change'}" label-width="0px">
                <el-select v-model="data.settleAccType" style="width:100%;" placeholder="">
                  <el-option label="Saving" value="Saving" />
                  <el-option label="Current" value="Current" />
                </el-select>
              </el-form-item>
            </el-col>
            <div v-if="index > 0" class="text-center">
              <el-button type="primary" style="width:100px;margin-top:30px;" size="mini" @click="handleDeleteStockTradingform(index)">Delete</el-button>
            </div>
          </el-row>
        </el-form-item>
        <el-form-item label="Transaction Date: ">
          <el-col :span="11">
            <el-form-item label="From: " prop="transactionFromDate" label-width="60px">
              <el-date-picker v-model="StockTradingform.transactionFromDate" type="date" value-format="timestamp" style="width: 60%;" placeholder="Start Date" />
            </el-form-item>
          </el-col>
          <el-col :span="2" class="line" style="text-align: center;">-</el-col>
          <el-col :span="11">
            <el-form-item label="To: " prop="transactionToDate" label-width="60px">
              <el-date-picker v-model="StockTradingform.transactionToDate" type="date" value-format="timestamp" style="width: 60%;" placeholder="End Date" />
            </el-form-item>
          </el-col>
        </el-form-item>
        <el-form-item label="Frequency: " prop="frequency">
          <el-select v-model="StockTradingform.frequency" style="width:100%;" placeholder="">
            <el-option v-for="item in frequencylist" :key="item" :value="item" :label="item" />
          </el-select>
        </el-form-item>
        <el-form-item label="Frequency rate: " prop="frequencyRate">
          <el-input v-model="StockTradingform.frequencyRate" />
        </el-form-item>
        <el-form-item label="Total Transaction: " prop="total">
          <el-input v-model="StockTradingform.total" />
        </el-form-item>
        <el-form-item label="Stock Table: " prop="stockTable">
          <el-select
            v-model="StockTradingform.stockTable"
            filterable
            allow-create
            style="width:100%;"
            default-first-option
            placeholder=""
          >
            <el-option
              v-for="item in tableNamelist"
              :key="item"
              :label="item"
              :value="item"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="Transfer Table: " prop="transferTable">
          <el-select
            v-model="StockTradingform.transferTable"
            filterable
            allow-create
            style="width:100%;"
            default-first-option
            placeholder=""
          >
            <el-option
              v-for="item in tableNamelist"
              :key="item"
              :label="item"
              :value="item"
            />
          </el-select>
        </el-form-item>
        <div style="text-align:center;">
          <el-button type="primary" @click="submitForm('StockTradingform')">Submit</el-button>
          <el-button @click="resetForm('StockTradingform')">Reset</el-button>
        </div>
      </el-form>
      <!--Fund Trading-->
      <el-form v-show="Transactionform.type === 'Fund Trading' && datatype === '2'" ref="FundTradingform" :rules="FundTradingformrules" :model="FundTradingform" label-position="left" label-width="160px">
        <el-form-item label="Account Type From: " prop="accountTypeFr">
          <el-select v-model="FundTradingform.accountTypeFr" style="width:100%;" placeholder="">
            <el-option label="Saving" value="Saving" />
            <el-option label="Current" value="Current" />
          </el-select>
        </el-form-item>
        <el-form-item label="Channel: " prop="channelID">
          <el-select v-model="FundTradingform.channelID" style="width:100%;" placeholder="">
            <el-option v-for="item in channellist" :key="item.id" :value="item.id" :label="item.name" />
          </el-select>
        </el-form-item>
        <el-form-item label="Transaction Account Type: ">
          <el-radio-group v-model="FundTradingformSelectType">
            <el-radio-button label="1">Branch Number</el-radio-button>
            <el-radio-button label="2">Link to Datatable</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="FundTradingformSelectType == '1'" label-width="160px" label="Transaction Account: " prop="branchFr">
          <label style="color: #606266;width: 120px;">Branch Number:</label>
          <el-input v-model="FundTradingform.branchFr" style="width:225px;" />
        </el-form-item>
        <el-form-item v-if="FundTradingformSelectType == '2'" label-width="160px" label="Transaction Account: ">
          <el-upload
            :action="datahost+'/file/upload'"
            :before-remove="beforeRemove"
            :limit="1"
            :on-change="exportData"
            :on-exceed="handleExceed"
            :on-success="FundTradingformFileListSuccess"
            :file-list="FundTradingformFileList"
            class="upload-demo"
            accept=".csv,.xls,.xlsx"
          >
            <el-button size="small" type="primary">Upload</el-button>
            <span slot="tip" class="el-upload__tip">only upload csv/xls/xlsx files, not exceed 5MB.</span>
          </el-upload>
        </el-form-item>
        <el-form-item label="Transaction Type: ">
          <el-select v-model="FundTradingform.transType" style="width:100%;" placeholder="">
            <el-option label="Buy" value="Buy" />
            <el-option label="Sell" value="Sell" />
          </el-select>
        </el-form-item>
        <el-form-item label="Transaction Details: ">
          <el-button type="primary" size="mini" @click="addFundTradingformCondition()">Add</el-button>
          <el-row v-for="(data,index) in FundTradingform.transToList" :key="index" :gutter="10" style="margin:0 0 30px 0;">
            <el-col :span="24">
              <el-radio-group v-model="data.type">
                <el-radio-button label="1">Fund Code</el-radio-button>
                <el-radio-button label="2">Currency</el-radio-button>
                <el-radio-button label="3">Fund Type</el-radio-button>
                <el-radio-button label="4">Region</el-radio-button>
              </el-radio-group>
            </el-col>
            <el-col :span="6">
              <el-form-item :label="'0'+(index+1)" label-width="40px">
                <div v-show="data.type == '1'">
                  <el-upload
                    :action="datahost+'/file/upload'"
                    :before-remove="beforeRemove"
                    :limit="1"
                    :on-change="exportData"
                    :on-exceed="handleExceed"
                    :on-success="FundTradingformFileList2Success"
                    :file-list="FundTradingformFileList2[index]"
                    class="upload-demo"
                    accept=".csv,.xls,.xlsx"
                  >
                    <el-button size="small" type="primary" @click="setFundTradingformFileIndex(index)">Link to Datatable</el-button>
                  </el-upload>
                </div>
                <div v-show="data.type == '2'">
                  <el-select v-model="data.ccy" style="width:100%;" placeholder="">
                    <el-option v-for="item in fundcurrencylist" :key="item" :value="item" :label="item" />
                  </el-select>
                </div>
                <div v-show="data.type == '3'">
                  <el-select v-model="data.fundType" style="width:100%;" placeholder="">
                    <el-option v-for="item in fundtypelist" :key="item" :value="item" :label="item" />
                  </el-select>
                </div>
                <div v-show="data.type == '4'">
                  <el-select v-model="data.region" style="width:100%;" placeholder="">
                    <el-option v-for="item in fundregionlist" :key="item" :value="item" :label="item" />
                  </el-select>
                </div>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item :prop="'transToList.' + index + '.amountFr'" :rules="{required: true, validator: validateLimitValue, trigger: 'blur'}" label="Amount From: " label-width="120px">
                <el-input v-model="data.amountFr" style="margin:0;" />
              </el-form-item>
            </el-col>
            <el-col :span="4">
              <el-form-item :prop="'transToList.' + index + '.amountTo'" :rules="{required: true, validator: validateLimitValue, trigger: 'blur'}" label="To" label-width="40px">
                <el-input v-model="data.amountTo" style="margin:0;" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item :prop="'transToList.' + index + '.settleAccType'" :rules="{required: true, message: 'Please select a account type', trigger: 'change'}" label="Settlement Account: " label-width="160px">
                <el-select v-model="data.settleAccType" style="width:100%;" placeholder="">
                  <el-option label="Saving" value="Saving" />
                  <el-option label="Current" value="Current" />
                </el-select>
              </el-form-item>
            </el-col>
            <div v-if="index > 0" class="text-center">
              <el-button type="primary" style="width:100px;margin-top:30px;" size="mini" @click="handleDeleteFundTradingform(index)">Delete</el-button>
            </div>
          </el-row>
        </el-form-item>
        <el-form-item label="Transaction Date: ">
          <el-col :span="11">
            <el-form-item label="From: " prop="transactionFromDate" label-width="60px">
              <el-date-picker v-model="FundTradingform.transactionFromDate" type="date" value-format="timestamp" style="width: 60%;" placeholder="Start Date" />
            </el-form-item>
          </el-col>
          <el-col :span="2" class="line" style="text-align: center;">-</el-col>
          <el-col :span="11">
            <el-form-item label="To: " prop="transactionToDate" label-width="60px">
              <el-date-picker v-model="FundTradingform.transactionToDate" type="date" value-format="timestamp" style="width: 60%;" placeholder="End Date" />
            </el-form-item>
          </el-col>
        </el-form-item>
        <el-form-item label="Frequency: " prop="frequency">
          <el-select v-model="FundTradingform.frequency" style="width:100%;" placeholder="">
            <el-option v-for="item in frequencylist" :key="item" :value="item" :label="item" />
          </el-select>
        </el-form-item>
        <el-form-item label="Frequency rate: " prop="frequencyRate">
          <el-input v-model="FundTradingform.frequencyRate" />
        </el-form-item>
        <el-form-item label="Total Transaction: " prop="total">
          <el-input v-model="FundTradingform.total" />
        </el-form-item>
        <el-form-item label="Fund Table: " prop="fundTable">
          <el-select
            v-model="FundTradingform.fundTable"
            filterable
            allow-create
            style="width:100%;"
            default-first-option
            placeholder=""
          >
            <el-option
              v-for="item in tableNamelist"
              :key="item"
              :label="item"
              :value="item"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="Transfer Table: " prop="transferTable">
          <el-select
            v-model="FundTradingform.transferTable"
            filterable
            allow-create
            style="width:100%;"
            default-first-option
            placeholder=""
          >
            <el-option
              v-for="item in tableNamelist"
              :key="item"
              :label="item"
              :value="item"
            />
          </el-select>
        </el-form-item>
        <div style="text-align:center;">
          <el-button type="primary" @click="submitForm('FundTradingform')">Submit</el-button>
          <el-button @click="resetForm('FundTradingform')">Reset</el-button>
        </div>
      </el-form>
      <el-form v-show="datatype === '3'" ref="CustomerForm" :model="customerForm" :rules="customerFormRules" label-position="left" label-width="220px">
        <!-- 职业<occupation> -->
        <el-form-item label="Occupation" prop="occupation">
          <el-select
            v-model="customerForm.occupation"
            multiple
            collapse-tags
            placeholder="Please select an occupation, support multiple selection"
            class="select"
          >
            <el-option
              v-for="item in occupationOptions"
              :key="item"
              :label="item"
              :value="item"
            />
          </el-select>
        </el-form-item>
        <!-- 国籍<Nationality> -->
        <el-form-item label="Nationality" prop="nationality">
          <el-select v-model="customerForm.nationality" class="select">
            <el-option
              v-for="item in nationalityOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <!-- 婚姻状况<MaritalStatus> -->
        <el-form-item prop="maritalStatus">
          <template #label>
            Marital Status
            <el-tooltip effect="dark" placement="top">
              <div slot="content" class="tooltip-content">
                <p>Marital Status:</p>
                <div>
                  <dl>
                    <dt>M - Married</dt>
                    <dt>S - Single</dt>
                    <dt>D - Divorced</dt>
                    <dt>W - Widow</dt>
                  </dl>
                </div>
              </div>
              <i class="el-icon-question help-icon" />
            </el-tooltip>
          </template>
          <el-select v-model="customerForm.maritalStatus" class="select">
            <el-option
              v-for="item in maritalStatusOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <!-- 永久居留身份<PermanentResidenceStatus> -->
        <el-form-item label="Permanent Residence Status" prop="permanentResidenceStatus">
          <el-select v-model="customerForm.permanentResidenceStatus" class="select">
            <el-option
              v-for="item in permanentResidenceStatusOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <!-- 年龄阶层<AgeGroup> -->
        <el-form-item label="Age Group" prop="ageGroup">
          <el-select v-model="customerForm.ageGroup" class="select">
            <el-option
              v-for="item in ageGroupOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <!-- 性别<Gender> -->
        <el-form-item prop="gender">
          <template #label>
            Gender
            <el-tooltip effect="dark" placement="top">
              <div slot="content" class="tooltip-content">
                <p>Gender:</p>
                <div>
                  <dl>
                    <dt>F - Femal</dt>
                    <dt>M - Male</dt>
                  </dl>
                </div>
              </div>
              <i class="el-icon-question help-icon" />
            </el-tooltip>
          </template>
          <el-select v-model="customerForm.gender" class="select">
            <el-option
              v-for="item in genderOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <!-- 教育<Education> -->
        <el-form-item prop="education">
          <template #label>
            Education
            <el-tooltip effect="dark" placement="top">
              <div slot="content" class="tooltip-content">
                <p>Education:</p>
                <div>
                  <dl>
                    <dt>U - University</dt>
                    <dt>M - Master</dt>
                    <dt>S - Secondary</dt>
                    <dt>P - Post Secondary</dt>
                    <dt>X - Others</dt>
                  </dl>
                </div>
              </div>
              <i class="el-icon-question help-icon" />
            </el-tooltip>
          </template>
          <el-select v-model="customerForm.education" class="select">
            <el-option
              v-for="item in educationOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <!-- 居住国家代码<Residence County Code> -->
        <el-form-item label="Residence County Code" prop="residenceCountyCode">
          <el-select v-model="customerForm.residenceCountyCode" class="select">
            <el-option
              v-for="item in residenceCountyCodeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <!-- Region -->
        <el-form-item label="Region" prop="region">
          <el-select v-model="customerForm.region" class="select">
            <el-option
              v-for="item in regionOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <!-- District -->
        <el-form-item label="District" prop="district">
          <el-select v-model="customerForm.district" class="select">
            <el-option
              v-for="item in districtOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <!-- 就业状况<EmploymentStatus> -->
        <el-form-item prop="employmentStatus">
          <template #label>
            Employment Status
            <el-tooltip effect="dark" placement="top">
              <div slot="content" class="tooltip-content">
                <p>Employment Status:</p>
                <div>
                  <dl>
                    <dt>J - Primary/Junior</dt>
                    <dt>F - Full time</dt>
                    <dt>P - Part time</dt>
                    <dt>T - Temporary</dt>
                    <dt>S - Self employed</dt>
                    <dt>U - Unemployed</dt>
                    <dt>R - Retired</dt>
                  </dl>
                </div>
              </div>
              <i class="el-icon-question help-icon" />
            </el-tooltip>
          </template>
          <el-select v-model="customerForm.employmentStatus" class="select">
            <el-option
              v-for="item in employmentStatusOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <!-- 月收入<MonthlyIncome> -->
        <el-form-item label="Monthly Income Amount">
          <!-- Form -->
          <el-col :span="11">
            <el-form-item label="From" prop="monthlyIncomeAmountFrom" label-width="50px">
              <el-input v-model="customerForm.monthlyIncomeAmountFrom" placeholder="From" class="input" />
            </el-form-item>
          </el-col>
          <el-col :span="2" class="line" style="text-align: center;">-</el-col>
          <!-- To -->
          <el-col :span="11">
            <el-form-item label="To" prop="monthlyIncomeAmountTo" label-width="50px">
              <el-input v-model="customerForm.monthlyIncomeAmountTo" placeholder="To" class="input" />
            </el-form-item>
          </el-col>
        </el-form-item>
        <!-- 家庭收入<HouseholdIncome> -->
        <el-form-item label="Household Income Amount">
          <!-- Form -->
          <el-col :span="11">
            <el-form-item label="From" prop="householdIncomeAmountFrom" label-width="50px">
              <el-input v-model="customerForm.householdIncomeAmountFrom" placeholder="From" class="input" />
            </el-form-item>
          </el-col>
          <el-col :span="2" class="line" style="text-align: center;">-</el-col>
          <!-- To -->
          <el-col :span="11">
            <el-form-item label="To" prop="householdIncomeAmountTo" label-width="50px">
              <el-input v-model="customerForm.householdIncomeAmountTo" placeholder="To" class="input" />
            </el-form-item>
          </el-col>
        </el-form-item>
        <el-form-item label="Creation Date" prop="createDate">
          <el-date-picker
            v-model="customerForm.createDate"
            type="daterange"
            value-format="timestamp"
            range-separator="-"
            unlink-panels
            start-placeholder="Start Date"
            end-placeholder="End Date"
          />
        </el-form-item>
        <!-- 出生国家<CountryOfBirth> -->
        <el-form-item label="Country Of Birth" prop="countryOfBirth">
          <el-select v-model="customerForm.countryOfBirth" class="select">
            <el-option
              v-for="item in countryOfBirthOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <!-- 分行编号<BranchNumber> -->
        <el-form-item label="Branch Number">
          <!-- Form -->
          <el-col :span="11">
            <el-form-item label="From" prop="branchNumberFrom" label-width="50px">
              <el-input v-model="customerForm.branchNumberFrom" placeholder="From" class="input" />
            </el-form-item>
          </el-col>
          <el-col :span="2" class="line" style="text-align: center;">-</el-col>
          <!-- To -->
          <el-col :span="11">
            <el-form-item label="To" prop="branchNumberTo" label-width="50px">
              <el-input v-model="customerForm.branchNumberTo" placeholder="To" class="input" />
            </el-form-item>
          </el-col>
        </el-form-item>
        <!-- 电子邮件地址后缀类型<EmailAddressSuffixType> -->
        <el-form-item label="Email Address Suffix Type" prop="emailSuffix">
          <el-select v-model="customerForm.emailSuffix" class="select">
            <el-option
              v-for="item in emailSuffixTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <!-- 客户状态<CustomerStatus> -->
        <el-form-item prop="customerStatus">
          <template #label>
            Customer Status
            <el-tooltip effect="dark" placement="top">
              <div slot="content" class="tooltip-content">
                <p>Customer Status:</p>
                <div>
                  <dl>
                    <dt>Null - Others</dt>
                    <dt>S - Staff</dt>
                    <dt>R - Relative of staff</dt>
                    <dt>P - Spouse of staff</dt>
                    <dt>D - Bank director</dt>
                  </dl>
                </div>
              </div>
              <i class="el-icon-question help-icon" />
            </el-tooltip>
          </template>
          <el-select v-model="customerForm.customerStatus" class="select">
            <el-option
              v-for="item in customerStatusOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <!-- 自日期起的家庭住址<HomeAddressSinceDate> -->
        <el-form-item label="Home Address Since Date" prop="homeAddressSinceDate">
          <el-date-picker
            v-model="customerForm.homeAddressSinceDate"
            type="daterange"
            value-format="timestamp"
            range-separator="-"
            unlink-panels
            start-placeholder="Start Date"
            end-placeholder="End Date"
          />
        </el-form-item>
        <!-- 首选口语<PreferredSpokenLanguage> -->
        <el-form-item prop="preferredSpokenLanguage">
          <template #label>
            Preferred Spoken Language
            <el-tooltip effect="dark" placement="top">
              <div slot="content" class="tooltip-content">
                <p>Preferred Spoken Language:</p>
                <div>
                  <dl>
                    <dt>E - English</dt>
                    <dt>C - Cantonese</dt>
                    <dt>P - Mandarin</dt>
                  </dl>
                </div>
              </div>
              <i class="el-icon-question help-icon" />
            </el-tooltip>
          </template>
          <el-select v-model="customerForm.preferredSpokenLanguage" class="select">
            <el-option
              v-for="item in preferredSpokenLanguageOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <!-- 生成的总数<total> -->
        <el-form-item label="Total Customers" prop="total">
          <el-input v-model="customerForm.total" placeholder="Integer greater than zero" class="input" />
        </el-form-item>
        <!-- 表名<TableName> -->
        <el-form-item label="Table Name" prop="tableName">
          <el-select
            v-model="customerForm.tableName"
            filterable
            allow-create
            class="input"
            default-first-option
            placeholder=""
          >
            <el-option
              v-for="item in customerTableList"
              :key="item"
              :label="item"
              :value="item"
            />
          </el-select>
        </el-form-item>
        <div style="text-align:center;">
          <el-button type="primary" @click="submitForm('CustomerForm')">Submit</el-button>
          <el-button @click="resetForm('CustomerForm')">Reset</el-button>
        </div>
      </el-form>
      <el-dialog
        :visible.sync="showdialog"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        title="data.subject"
        width="1000px"
        append-to-body
        destroy-on-close
      />
    </customize-card>
  </div>
</template>

<script>
import {
  accountTypeOptions,
  ageGroupOptions,
  countryOfBirthOptions,
  currencyTypeOptions,
  customerStatusOptions,
  educationOptions,
  emailSuffixTypeOptions,
  employmentStatusOptions,
  genderOptions,
  getAllProvince,
  getDistrictByCountryNameAndRegionName,
  getRegionByProvinceName,
  maritalStatusOptions,
  nationalityOptions,
  permanentResidenceStatusOptions,
  preferredSpokenLanguageOptions,
  residenceCountyCodeOptions
} from './js/index'
import CustomizeCard from '@/components/CustomizeCard/index.vue'
import {
  accountGenerate,
  creditCardGenerate,
  customerGenerate,
  depositGenerate,
  foreignExchangeGenerate,
  fundGenerate,
  getAllFrequencyList,
  getChannelList,
  getCreditCardIndustryList,
  getCustomerOccupationList,
  getFexCcyList,
  getFundCcyList,
  getFundRegionList,
  getFundTypeList,
  getPaymentIndustryList,
  getStockOrderTypeList,
  getTableList,
  getTermDepositPeriodPeriod,
  paymentGenerate,
  stockGenerate,
  termDepositGenerate
} from '@/api/practice/data-creation'
import { DATA_CREATION_API_URL } from '@/contains'

export default {
  name: 'DataAnalysisDataCreation',
  components: { CustomizeCard },
  data() {
    const validateLimitValue = (rule, value, callback) => {
      let a
      a = a = /^[0-9]\d*$/g

      if (!a.test(value)) {
        callback(new Error('Invalid number'))
      } else {
        callback()
      }
    }
    return {
      validateLimitValue: validateLimitValue,
      datahost: DATA_CREATION_API_URL,
      activeName: 'second',
      loading: false,
      showdialog: false,
      datatype: '3',
      FundTradingformSelectType: '1',
      PaymentformSelectType: '1',
      CreditCardformSelectType: '1',
      TermDepositformSelectType: '1',
      ForeignExchangeformSelectType: '1',
      StockTradingformSelectType: '1',
      TransferformSelectType1: '1',
      TransferformSelectType2: '1',
      PaymentformFileList: [],
      branchFrList: [],
      branchToList: [],
      accountFrList: [],
      accountToList: [],
      CreditCardformFileList: [],
      TermDepositformFileList: [],
      ForeignExchangeformFileList: [],
      StockTradingformFileList: [],
      StockTradingformFileList2: [[]],
      FundTradingformFileList: [],
      FundTradingformFileList2: [[]],
      stockorderTypelist: [],
      fexccylist: [],
      termdepositperiodlist: [],
      creditcardindustrylist: [],
      paymentindustrylist: [],
      frequencylist: [],
      tableNamelist: [],
      channellist: [],
      fundtypelist: [],
      fundcurrencylist: [],
      fundregionlist: [],
      Productform: {
        type: 'Saving'
      },
      pickerOptions: {
        disabledDate(time) {
          const startTime = new Date('2017-01-01 00:00:00').getTime()
          const endTime = new Date('2021-12-31 23:59:59').getTime()
          return time.getTime() < startTime || time.getTime() > endTime
        }
      },
      AccountForm: {
        accountType: '001',
        currencyCode: 'HKD',
        createDateFrom: '',
        createDateTo: '',
        total: 100,
        customerTable: '',
        accountTable: '',
        relAccountTable: ''
      },
      Transactionform: {
        type: 'Transfer',
        dataSource: '2'
      },
      Transferform: {
        accountTypeFr: 'Saving',
        accountTypeTo: 'Current',
        branchFr: '130',
        accountNumberFr: '',
        branchTo: '131',
        accountNumberTo: '',
        branchFrList: '',
        branchToList: '',
        accountFrList: '',
        accountToList: '',
        amountFr: '100',
        amountTo: '1000',
        transactionFromDate: new Date().getTime(),
        transactionToDate: new Date().getTime(),
        frequency: 'Weekly',
        frequencyRate: '2',
        total: '1000',
        channelID: 1,
        tableName: 'transfer_log1'
      },
      Transferformrules: {
        accountTypeFr: [{ required: true, message: 'Please select the account type from.', trigger: 'change' }],
        accountTypeTo: [{ required: true, message: 'Please select the account type to.', trigger: 'change' }],
        branchFr: [{ required: true, message: 'Please input the tranfer from branch numbers.', trigger: 'blur' }],
        branchTo: [{ required: true, message: 'Please input the tranfer to branch numbers.', trigger: 'blur' }],
        accountNumberFr: [{ required: true, message: 'Please input the tranfer from account numbers.', trigger: 'blur' }],
        accountNumberTo: [{ required: true, message: 'Please input the tranfer to account numbers.', trigger: 'blur' }],
        transactionFromDate: [{ required: true, message: 'Please input the transaction from date.', trigger: 'change' }],
        transactionToDate: [{ required: true, message: 'Please input the transaction to date.', trigger: 'change' }],
        frequency: [{ required: true, message: 'Please select the frequency.', trigger: 'change' }],
        frequencyRate: [{ required: true, message: 'Please input the frequency rate.', trigger: 'blur' }],
        total: [{ required: true, message: 'Please input the total transaction.', trigger: 'blur' }],
        tableName: [{ required: true, message: 'Please input the table name.', trigger: 'change' }]
      },
      Paymentform: {
        accountTypeFr: 'Saving',
        moveFr: '130',
        moveFrLink: '',
        moveToList: [
          {
            payeeCategoryID: '003',
            amountFr: '100',
            amountTo: '1000'
          }
        ],
        transactionFromDate: new Date().getTime(),
        transactionToDate: new Date().getTime(),
        total: '1000',
        channelID: 1,
        frequency: 'Weekly',
        frequencyRate: '2',
        paymentTable: 'payment_log',
        transferTable: 'transfer_log1'
      },
      Paymentformrules: {
        accountTypeFr: [{ required: true, message: 'Please select the account type from.', trigger: 'change' }],
        moveFr: [{ required: true, message: 'Please input the move money from.', trigger: 'blur' }],
        transactionFromDate: [{ required: true, message: 'Please input the transaction from date.', trigger: 'change' }],
        transactionToDate: [{ required: true, message: 'Please input the transaction to date.', trigger: 'change' }],
        frequency: [{ required: true, message: 'Please input the frequency.', trigger: 'change' }],
        frequencyRate: [{ required: true, message: 'Please input the frequency rate.', trigger: 'blur' }],
        total: [{ required: true, message: 'Please input the total transaction.', trigger: 'blur' }],
        paymentTable: [{ required: true, message: 'Please input the payment table.', trigger: 'change' }],
        transferTable: [{ required: true, message: 'Please input the transfer table.', trigger: 'change' }]
      },
      CreditCardform: {
        sequenceFr: '263849',
        sequenceTo: '263949',
        sequenceFrLink: '',
        transToList: [
          {
            categoryname: 'Beauty',
            amountFr: '100',
            amountTo: '1000'
          }
        ],
        transactionFromDate: new Date().getTime(),
        transactionToDate: new Date().getTime(),
        total: '1000',
        frequency: 'Weekly',
        frequencyRate: '2',
        tableName: 'creditcard_log'
      },
      CreditCardformrules: {
        transactionFromDate: [{ required: true, message: 'Please input the transaction from date.', trigger: 'change' }],
        transactionToDate: [{ required: true, message: 'Please input the transaction to date.', trigger: 'change' }],
        frequency: [{ required: true, message: 'Please input the frequency.', trigger: 'change' }],
        frequencyRate: [{ required: true, message: 'Please input the frequency rate.', trigger: 'blur' }],
        total: [{ required: true, message: 'Please input the total transaction.', trigger: 'blur' }],
        tableName: [{ required: true, message: 'Please input the table name.', trigger: 'change' }]
      },
      TermDepositform: {
        branchFr: '130',
        branchFrLink: '',
        transToList: [
          {
            TermPeriod: '1week',
            settleAccType: 'Saving',
            amountFr: '100',
            amountTo: '1000'
          }
        ],
        transactionFromDate: new Date().getTime(),
        transactionToDate: new Date().getTime(),
        total: '1000',
        channelID: 1,
        frequency: 'Weekly',
        frequencyRate: '2',
        termTable: 'termdeposit_log',
        transferTable: 'transfer_log1'
      },
      TermDepositformrules: {
        branchFr: [{ required: true, message: 'Please input the branch number.', trigger: 'blur' }],
        transactionFromDate: [{ required: true, message: 'Please input the transaction from date.', trigger: 'change' }],
        transactionToDate: [{ required: true, message: 'Please input the transaction to date.', trigger: 'change' }],
        frequency: [{ required: true, message: 'Please input the frequency.', trigger: 'change' }],
        frequencyRate: [{ required: true, message: 'Please input the frequency rate.', trigger: 'blur' }],
        total: [{ required: true, message: 'Please input the total transaction.', trigger: 'blur' }],
        termTable: [{ required: true, message: 'Please input the term table name.', trigger: 'change' }],
        transferTable: [{ required: true, message: 'Please input the tansfer table name.', trigger: 'change' }]
      },
      ForeignExchangeform: {
        branchFr: '130',
        branchFrLink: '',
        transType: 'Buy',
        transCcyList: [
          {
            Ccy: 'USD',
            settleAccType: 'Saving',
            amountFr: '100',
            amountTo: '1000'
          }
        ],
        transactionFromDate: new Date().getTime(),
        transactionToDate: new Date().getTime(),
        total: '1000',
        channelID: 1,
        frequency: 'Weekly',
        frequencyRate: '2',
        fexTable: 'fex_log',
        transferTable: 'transfer_log1'
      },
      ForeignExchangeformrules: {
        branchFr: [{ required: true, message: 'Please input the branch number.', trigger: 'blur' }],
        transactionFromDate: [{ required: true, message: 'Please input the transaction from date.', trigger: 'change' }],
        transactionToDate: [{ required: true, message: 'Please input the transaction to date.', trigger: 'change' }],
        frequency: [{ required: true, message: 'Please input the frequency.', trigger: 'change' }],
        frequencyRate: [{ required: true, message: 'Please input the frequency rate.', trigger: 'blur' }],
        total: [{ required: true, message: 'Please input the total transaction.', trigger: 'blur' }],
        fexTable: [{ required: true, message: 'Please input the fex table name.', trigger: 'change' }],
        transferTable: [{ required: true, message: 'Please input the transfer table name.', trigger: 'change' }]
      },
      StockTradingform: {
        accountTypeFr: 'Saving',
        branchFr: '130',
        branchFrLink: '',
        transType: 'Buy',
        transToList: [
          {
            orderType: 'Fix',
            settleAccType: 'Saving',
            no: '100',
            stockLink: ''
          }
        ],
        transactionFromDate: new Date().getTime(),
        transactionToDate: new Date().getTime(),
        total: '1000',
        channelID: 1,
        frequency: 'Weekly',
        frequencyRate: '2',
        stockTable: 'stock_log',
        transferTable: 'transfer_log1'
      },
      StockTradingformrules: {
        branchFr: [{ required: true, message: 'Please input the branch number.', trigger: 'blur' }],
        transactionFromDate: [{ required: true, message: 'Please input the transaction from date.', trigger: 'change' }],
        transactionToDate: [{ required: true, message: 'Please input the transaction to date.', trigger: 'change' }],
        frequency: [{ required: true, message: 'Please input the frequency.', trigger: 'change' }],
        frequencyRate: [{ required: true, message: 'Please input the frequency rate.', trigger: 'blur' }],
        total: [{ required: true, message: 'Please input the total transaction.', trigger: 'blur' }],
        stockTable: [{ required: true, message: 'Please input the stock table name.', trigger: 'change' }],
        transferTable: [{ required: true, message: 'Please input the transfer table name.', trigger: 'change' }]
      },
      FundTradingform: {
        accountTypeFr: 'Saving',
        branchFr: '130',
        branchFrLink: '',
        transType: 'Buy',
        transToList: [
          {
            settleAccType: 'Saving',
            amountFr: '100',
            amountTo: '1000',
            fundLink: '',
            type: '1',
            ccy: 'USD',
            fundType: 'Equity Funds',
            region: 'Europe'
          }
        ],
        transactionFromDate: new Date().getTime(),
        transactionToDate: new Date().getTime(),
        total: '1000',
        channelID: 1,
        frequency: 'Weekly',
        frequencyRate: '2',
        fundTable: 'fund_log',
        transferTable: 'transfer_log1'
      },
      FundTradingformrules: {
        branchFr: [{ required: true, message: 'Please input the branch number.', trigger: 'blur' }],
        transactionFromDate: [{ required: true, message: 'Please input the transaction from date.', trigger: 'change' }],
        transactionToDate: [{ required: true, message: 'Please input the transaction to date.', trigger: 'change' }],
        frequency: [{ required: true, message: 'Please input the frequency.', trigger: 'change' }],
        frequencyRate: [{ required: true, message: 'Please input the frequency rate.', trigger: 'blur' }],
        total: [{ required: true, message: 'Please input the total transaction.', trigger: 'blur' }],
        fundTable: [{ required: true, message: 'Please input the fund table name.', trigger: 'change' }],
        transferTable: [{ required: true, message: 'Please input the transfer table name.', trigger: 'change' }]
      },
      customerForm: {
        occupation: ['Advertising'],
        nationality: 'HK',
        maritalStatus: 'M',
        permanentResidenceStatus: 'Y',
        ageGroup: '26-35',
        gender: 'F',
        education: 'U',
        residenceCountyCode: 'HK',
        region: '',
        district: '',
        employmentStatus: 'F',
        monthlyIncomeAmountFrom: '',
        monthlyIncomeAmountTo: '',
        householdIncomeAmountFrom: '',
        householdIncomeAmountTo: '',
        customerNumbers: '',
        createDate: [],
        countryOfBirth: 'HK',
        branchNumberFrom: '',
        branchNumberTo: '',
        emailSuffix: 'gmail.com',
        customerStatus: null,
        homeAddressSinceDate: [],
        preferredSpokenLanguage: 'C',
        total: 1000,
        tableName: 'customer'
      },
      customerFormRules: {
        occupation: [
          { required: true, message: 'Please select one or more occupations.', trigger: 'change' }
        ],
        monthlyIncomeAmountFrom: [
          { required: true, message: 'Please input the monthly income amount from.', trigger: 'blur' },
          { pattern: /^[1-9]\d*$/, message: 'Invalid number.', trigger: 'blur' }
        ],
        monthlyIncomeAmountTo: [
          { required: true, message: 'Please input the monthly income amount to.', trigger: 'blur' },
          { pattern: /^[1-9]\d*$/, message: 'Invalid number.', trigger: 'blur' }
        ],
        householdIncomeAmountFrom: [
          { required: true, message: 'Please input the household income amount from.', trigger: 'blur' },
          { pattern: /^[1-9]\d*$/, message: 'Invalid number.', trigger: 'blur' }
        ],
        householdIncomeAmountTo: [
          { required: true, message: 'Please input the household income amount to.', trigger: 'blur' },
          { pattern: /^[1-9]\d*$/, message: 'Invalid number.', trigger: 'blur' }
        ],
        customerNumbers: [
          { required: true, message: 'Please input the customer numbers.', trigger: 'blur' },
          { pattern: /^[1-9]\d*$/, message: 'Invalid number.', trigger: 'blur' }
        ],
        createDate: [
          { required: true, message: 'Please select creation date.', trigger: 'blur' }
        ],
        branchNumberFrom: [
          { required: true, message: 'Please input the branch number from.', trigger: 'blur' },
          { pattern: /^[1-9]\d*$/, message: 'Invalid number.', trigger: 'blur' }
        ],
        branchNumberTo: [
          { required: true, message: 'Please input the branch number to.', trigger: 'blur' },
          { pattern: /^[1-9]\d*$/, message: 'Invalid number.', trigger: 'blur' }
        ],
        homeAddressSinceDate: [
          { required: true, message: 'Please select home address since date.', trigger: 'blur' }
        ],
        total: [
          { required: true, message: 'Please input the total number.', trigger: 'blur' },
          { pattern: /^[1-9]\d*$/, message: 'Invalid number.', trigger: 'blur' }
        ],
        tableName: [
          { required: true, message: 'Please input the table name.', trigger: 'blur' }
        ]
      },
      occupationOptions: [],
      nationalityOptions,
      educationOptions,
      emailSuffixTypeOptions,
      employmentStatusOptions,
      ageGroupOptions,
      customerStatusOptions,
      maritalStatusOptions,
      genderOptions,
      accountTypeOptions,
      currencyTypeOptions,
      permanentResidenceStatusOptions,
      preferredSpokenLanguageOptions,
      residenceCountyCodeOptions,
      countryOfBirthOptions,
      regionOptions: [],
      districtOptions: [],
      AccountFormRules: {
        total: [
          { required: true, message: 'Please input the account numbers', trigger: 'blur' },
          { pattern: /^[0-9]\d*$/g, message: 'Invalid account numbers', trigger: 'blur' }
        ],
        createDateFrom: [
          { required: true, message: 'Please select the create date', trigger: 'blur' }
        ],
        createDateTo: [
          { required: true, message: 'Please select the end data', trigger: 'blur' }
        ]
      },
      relAccountTableList: [],
      accountTableList: [],
      customerTableList: []
    }
  },
  computed: {
  },
  watch: {
    'customerForm.residenceCountyCode'(newVal) {
      this.setRegionOptions(newVal)
    },
    'customerForm.region'(newVal) {
      this.setDistrictOptions(this.customerForm.residenceCountyCode, newVal)
    }
  },
  created() {
    this.initTableNameList('Customer')
    this.initTableNameList('001')
    this.initTableNameList('001,002')
  },
  mounted() {
    this.initTableName()
    this.initOccupationOptions()
    this.initSelectOptions()
    this.setRegionOptions(this.customerForm.residenceCountyCode)
  },
  methods: {
    handlerChangeAccountType(value) {
      this.initTableNameList(value)
    },
    initTableNameList(type) {
      const vue = this
      getTableList({ type })
        .then(function(res) {
          if (type === 'Customer') {
            vue.customerTableList = res.data
            if (vue.customerTableList && vue.customerTableList.length > 0) {
              vue.AccountForm.customerTable = vue.customerTableList[0]
            }
          } else if (type === '001,002') {
            vue.relAccountTableList = res.data
            if (vue.relAccountTableList && vue.relAccountTableList.length > 0) {
              vue.AccountForm.relAccountTable = vue.relAccountTableList[0]
            }
          } else {
            vue.accountTableList = res.data
            if (vue.accountTableList && vue.accountTableList.length > 0) {
              vue.AccountForm.accountTable = vue.accountTableList[0]
            } else {
              vue.AccountForm.accountTable = ''
            }
          }
        })
        .finally(() => {
          vue.loading = false
        })
    },
    initTableName() {
      const vue = this
      getTableList()
        .then(function(res) {
          vue.tableNamelist = res.data
        })
        .catch(() => {
          vue.tableNamelist = []
        })
        .finally(() => {
          vue.loading = false
        })
    },
    initOccupationOptions() {
      const vue = this
      getCustomerOccupationList()
        .then(function(res) {
          vue.tableNamelist = []
          vue.occupationOptions = res.data
        })
        .finally(() => {
          vue.loading = false
        })
    },
    initSelectOptions() {
      const vue = this
      getAllFrequencyList()
        .then(function(res) {
          vue.frequencylist = res.data
        })
        .catch(() => {
          vue.frequencylist = []
        })
        .finally(() => {
          vue.loading = false
        })
      getPaymentIndustryList()
        .then(function(res) {
          vue.paymentindustrylist = res.data
        })
        .catch(() => {
          vue.paymentindustrylist = []
        })
        .finally(() => {
          vue.loading = false
        })
      getCreditCardIndustryList()
        .then(function(res) {
          vue.creditcardindustrylist = res.data
        })
        .catch(() => {
          vue.creditcardindustrylist = []
        })
        .finally(() => {
          vue.loading = false
        })
      getTermDepositPeriodPeriod()
        .then(function(res) {
          vue.termdepositperiodlist = res.data
        })
        .catch(() => {
          vue.termdepositperiodlist = []
        })
        .finally(() => {
          vue.loading = false
        })
      getFexCcyList()
        .then(function(res) {
          vue.fexccylist = res.data
        })
        .catch(() => {
          vue.fexccylist = []
        })
        .finally(() => {
          vue.loading = false
        })
      getStockOrderTypeList()
        .then(function(res) {
          vue.stockorderTypelist = res.data
        })
        .catch(() => {
          vue.stockorderTypelist = []
        })
        .finally(() => {
          vue.loading = false
        })
      getChannelList()
        .then(function(res) {
          vue.channellist = res.data
        })
        .catch(() => {
          vue.channellist = []
        })
        .finally(() => {
          vue.loading = false
        })
      getFundTypeList()
        .then(function(res) {
          vue.fundtypelist = res.data
        })
        .catch(() => {
          vue.fundtypelist = []
        })
        .finally(() => {
          vue.loading = false
        })
      getFundCcyList()
        .then(function(res) {
          vue.fundcurrencylist = res.data
        })
        .catch(() => {
          vue.fundcurrencylist = []
        })
        .finally(() => {
          vue.loading = false
        })
      getFundRegionList()
        .then(function(res) {
          vue.fundregionlist = res.data
        })
        .catch(() => {
          vue.fundregionlist = []
        })
        .finally(() => {
          vue.loading = false
        })
    },
    submitForm(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          switch (formName) {
            case 'Transferform':
              this.submitTransferform()
              break
            case 'Paymentform':
              this.submitPaymentform()
              break
            case 'CreditCardform':
              this.submitCreditCardform()
              break
            case 'TermDepositform':
              this.submitTermDepositform()
              break
            case 'ForeignExchangeform':
              this.submitForeignExchangeform()
              break
            case 'StockTradingform':
              this.submitStockTradingform()
              break
            case 'FundTradingform':
              this.submitFundTradingform()
              break
            case 'CustomerForm':
              this.submitCustomerForm()
              break
            case 'AccountForm':
              this.submitAccountForm()
              break
            default:
              break
          }
        } else {
          console.log('error submit!!')
          return false
        }
      })
    },
    submitAccountForm() {
      const requestData = {
        accountTable: this.AccountForm.accountTable,
        accountType: this.AccountForm.accountType,
        creationDate: {
          fr: this.AccountForm.createDateFrom,
          to: this.AccountForm.createDateTo
        },
        currencyCode: this.AccountForm.currencyCode,
        customerTable: this.AccountForm.customerTable,
        relAccountTable: (this.AccountForm.accountType === '001' || this.AccountForm.accountType === '002') ? '' : this.AccountForm.relAccountTable,
        total: this.AccountForm.total
      }
      const vue = this
      this.loading = true
      accountGenerate(requestData)
        .then(function(res) {
          vue.$alert(res.message)
          vue.initTableNameList('001,002')
        })
        .finally(() => {
          vue.loading = false
        })
    },
    submitTransferform() {
      var vue = this
      var requestData = vue.Transferform
      requestData.dataSource = vue.Transactionform.dataSource
      console.log('requestData', requestData)
      vue.TransferformSelectType1 === '1' ? requestData.branchFrList = '' : requestData.branchFr = ''
      vue.TransferformSelectType2 === '1' ? requestData.branchToList = '' : requestData.branchTo = ''
      vue.TransferformSelectType1 === '3' ? requestData.accountFrList = '' : requestData.accountNumberFr = ''
      vue.TransferformSelectType2 === '3' ? requestData.accountToList = '' : requestData.accountNumberTo = ''
      vue.loading = true
      depositGenerate(requestData)
        .then(function(res) {
          vue.$alert(res.message)
        })
        .finally(() => {
          vue.loading = false
        })
    },
    submitPaymentform() {
      var vue = this
      var requestData = vue.Paymentform
      requestData.dataSource = vue.Transactionform.dataSource
      vue.PaymentformSelectType === '1' ? requestData.moveFrLink = '' : requestData.moveFr = ''
      vue.loading = true
      paymentGenerate(requestData)
        .then(function(res) {
          vue.$alert(res.message)
        })
        .finally(() => {
          vue.loading = false
        })
    },
    submitCreditCardform() {
      var vue = this
      var requestData = vue.CreditCardform
      requestData.dataSource = vue.Transactionform.dataSource
      vue.CreditCardformSelectType === '1' ? requestData.sequenceFrLink = '' : requestData.sequenceFr = ''
      vue.CreditCardformSelectType === '1' ? requestData.sequenceFrLink = '' : requestData.sequenceTo = ''
      vue.loading = true
      creditCardGenerate(requestData)
        .then(function(res) {
          vue.$alert(res.message)
        })
        .finally(() => {
          vue.loading = false
        })
    },
    submitTermDepositform() {
      var vue = this
      var requestData = vue.TermDepositform
      requestData.dataSource = vue.Transactionform.dataSource
      vue.TermDepositformSelectType === '1' ? requestData.branchFrLink = '' : requestData.branchFr = ''
      vue.loading = true
      termDepositGenerate(requestData)
        .then(function(res) {
          vue.$alert(res.message)
        })
        .finally(() => {
          vue.loading = false
        })
    },
    submitForeignExchangeform() {
      var vue = this
      var requestData = vue.ForeignExchangeform
      requestData.dataSource = vue.Transactionform.dataSource
      vue.ForeignExchangeformSelectType === '1' ? requestData.branchFrLink = '' : requestData.branchFr = ''
      vue.loading = true
      foreignExchangeGenerate(requestData)
        .then(function(res) {
          vue.$alert(res.message)
        })
        .finally(() => {
          vue.loading = false
        })
    },
    submitStockTradingform() {
      var vue = this
      var requestData = vue.StockTradingform
      requestData.dataSource = vue.Transactionform.dataSource
      vue.StockTradingformSelectType === '1' ? requestData.branchFrLink = '' : requestData.branchFr = ''
      vue.loading = true
      stockGenerate(requestData)
        .then(function(res) {
          vue.$alert(res.message)
        })
        .finally(() => {
          vue.loading = false
        })
    },
    submitFundTradingform() {
      var vue = this
      var requestData = vue.FundTradingform
      requestData.dataSource = vue.Transactionform.dataSource
      vue.FundTradingformSelectType === '1' ? requestData.branchFrLink = '' : requestData.branchFr = ''
      requestData.transToList.forEach((item, index) => {
        switch (item.type) {
          case '1':
            item.ccy = ''
            item.fundType = ''
            item.region = ''
            break
          case '2':
            item.fundLink = ''
            item.fundType = ''
            item.region = ''
            break
          case '3':
            item.fundLink = ''
            item.ccy = ''
            item.region = ''
            break
          case '4':
            item.fundLink = ''
            item.ccy = ''
            item.fundType = ''
            break
          default:
            break
        }
      })
      vue.loading = true
      fundGenerate(requestData)
        .then(function(res) {
          vue.$alert(res.message)
        })
        .finally(() => {
          vue.loading = false
        })
    },
    resetForm(formName) {
      if (formName === 'CustomerForm') {
        this.$refs[formName].resetFields()
        this.setRegionOptions(this.customerForm.residenceCountyCode)
      }
      if (formName === 'Transferform') {
        this.Transferform = {
          accountTypeFr: 'Saving',
          accountTypeTo: 'Current',
          branchFr: '',
          branchTo: '',
          branchFrList: '',
          branchToList: '',
          amountFr: '',
          amountTo: '',
          effectiveDate: new Date().getTime(),
          frequency: 'Weekly',
          frequencyRate: '',
          total: '',
          channelID: 1,
          tableName: ''
        }
      } else if (formName === 'Paymentform') {
        this.Paymentform = {
          accountTypeFr: 'Saving',
          moveFr: '',
          moveFrLink: '',
          moveToList: [
            {
              payeeCategoryID: '003',
              amountFr: '',
              amountTo: ''
            }
          ],
          transactionFromDate: new Date().getTime(),
          total: '',
          channelID: 1,
          frequency: 'Weekly',
          frequencyRate: '',
          paymentTable: '',
          transferTable: ''
        }
      } else if (formName === 'CreditCardform') {
        this.CreditCardform = {
          sequenceFr: '',
          sequenceTo: '',
          sequenceFrLink: '',
          transToList: [
            {
              categoryname: 'Beauty',
              amountFr: '',
              amountTo: ''
            }
          ],
          transactionFromDate: new Date().getTime(),
          total: '',
          frequency: 'Weekly',
          frequencyRate: '',
          tableName: ''
        }
      } else if (formName === 'TermDepositform') {
        this.TermDepositform = {
          branchFr: '',
          branchFrLink: '',
          transToList: [
            {
              TermPeriod: '1week',
              settleAccType: 'Saving',
              amountFr: '',
              amountTo: ''
            }
          ],
          transactionFromDate: new Date().getTime(),
          total: '',
          channelID: 1,
          frequency: 'Weekly',
          frequencyRate: '',
          termTable: '',
          transferTable: ''
        }
      } else if (formName === 'ForeignExchangeform') {
        this.ForeignExchangeform = {
          branchFr: '',
          branchFrLink: '',
          transType: 'Buy',
          transCcyList: [
            {
              Ccy: 'USD',
              settleAccType: 'Saving',
              amountFr: '',
              amountTo: ''
            }
          ],
          transactionFromDate: new Date().getTime(),
          total: '',
          channelID: 1,
          frequency: 'Weekly',
          frequencyRate: '',
          fexTable: '',
          transferTable: ''
        }
      } else if (formName === 'StockTradingform') {
        this.StockTradingform = {
          accountTypeFr: 'Saving',
          branchFr: '',
          branchFrLink: '',
          transType: 'Buy',
          transToList: [
            {
              orderType: 'Fix',
              settleAccType: 'Saving',
              no: '',
              stockLink: ''
            }
          ],
          transactionFromDate: new Date().getTime(),
          total: '',
          channelID: 1,
          frequency: 'Weekly',
          frequencyRate: '',
          stockTable: '',
          transferTable: ''
        }
      } else if (formName === 'FundTradingform') {
        this.FundTradingform = {
          accountTypeFr: 'Saving',
          branchFr: '',
          branchFrLink: '',
          transType: 'Buy',
          transToList: [
            {
              settleAccType: 'Saving',
              amountFr: '',
              amountTo: '',
              fundLink: '',
              type: '1',
              ccy: 'USD',
              fundType: 'Equity Funds',
              region: 'Europe'
            }
          ],
          transactionFromDate: new Date().getTime(),
          total: '',
          channelID: 1,
          frequency: 'Weekly',
          frequencyRate: '',
          fundTable: '',
          transferTable: ''
        }
      }
    },
    handleDeletePaymentform(index) {
      this.Paymentform.moveToList.splice(index, 1)
    },
    addPaymentformCondition() {
      var vue = this
      vue.Paymentform.moveToList.push(
        {
          payeeCategoryID: '',
          amountFr: '',
          amountTo: ''
        }
      )
    },
    handleDeleteCreditCardform(index) {
      this.CreditCardform.transToList.splice(index, 1)
    },
    addCreditCardformCondition() {
      var vue = this
      vue.CreditCardform.transToList.push(
        {
          categoryname: 'Beauty',
          amountFr: '',
          amountTo: ''
        }
      )
    },
    handleDeleteTermDepositform(index) {
      this.TermDepositform.transToList.splice(index, 1)
    },
    addTermDepositformCondition() {
      var vue = this
      vue.TermDepositform.transToList.push(
        {
          TermPeriod: '001',
          settleAccType: 'Saving',
          amountFr: '',
          amountTo: ''
        }
      )
    },
    handleDeleteForeignExchangeform(index) {
      this.ForeignExchangeform.transCcyList.splice(index, 1)
    },
    addForeignExchangeformCondition() {
      var vue = this
      vue.ForeignExchangeform.transCcyList.push(
        {
          Ccy: 'USD',
          settleAccType: 'Saving',
          amountFr: '',
          amountTo: ''
        }
      )
    },
    handleDeleteStockTradingform(index) {
      this.StockTradingform.transToList.splice(index, 1)
    },
    addStockTradingformCondition() {
      var vue = this
      vue.StockTradingform.transToList.push(
        {
          orderType: 'Fix',
          settleAccType: 'Saving',
          no: '',
          stockLink: ''
        }
      )
    },
    handleDeleteFundTradingform(index) {
      this.FundTradingform.transToList.splice(index, 1)
    },
    addFundTradingformCondition() {
      var vue = this
      vue.FundTradingform.transToList.push(
        {
          settleAccType: 'Saving',
          amountFr: '',
          amountTo: '',
          fundLink: '',
          type: '1',
          ccy: 'USD',
          fundType: 'Equity Funds',
          region: 'Europe'
        }
      )
    },
    branchFrListSuccess(res, file, fileList) {
      if (res.code !== 0) {
        this.$alert(res.desc)
        this.branchFrList = []
        return
      }
      this.Transferform.branchFrList = res.data
    },
    accountFrListSuccess(res, file, fileList) {
      if (res.code !== 0) {
        this.$alert(res.desc)
        this.accountFrList = []
        return
      }
      this.Transferform.accountFrList = res.data
    },
    branchToListSuccess(res, file, fileList) {
      if (res.code !== 0) {
        this.$alert(res.desc)
        this.branchToList = []
        return
      }
      this.Transferform.branchToList = res.data
    },
    accountToListSuccess(res, file, fileList) {
      if (res.code !== 0) {
        this.$alert(res.desc)
        this.accountToList = []
        return
      }
      this.Transferform.accountToList = res.data
    },
    PaymentformFileListSuccess(res, file, fileList) {
      if (res.code !== 0) {
        this.$alert(res.desc)
        this.PaymentformFileList = []
        return
      }
      this.Paymentform.moveFrLink = res.data
    },
    CreditCardformFileListSuccess(res, file, fileList) {
      if (res.code !== 0) {
        this.$alert(res.desc)
        this.CreditCardformFileList = []
        return
      }
      this.CreditCardform.sequenceFrLink = res.data
    },
    TermDepositformFileListSuccess(res, file, fileList) {
      if (res.code !== 0) {
        this.$alert(res.desc)
        this.TermDepositformFileList = []
        return
      }
      this.TermDepositform.branchFrLink = res.data
    },
    ForeignExchangeformFileListSuccess(res, file, fileList) {
      if (res.code !== 0) {
        this.$alert(res.desc)
        this.ForeignExchangeformFileList = []
        return
      }
      this.ForeignExchangeform.branchFrLink = res.data
    },
    setStockTradingformFileIndex(index) {
      this.StockTradingformFileIndex = index
    },
    StockTradingformFileListSuccess(res, file, fileList) {
      if (res.code !== 0) {
        this.$alert(res.desc)
        this.StockTradingformFileList = []
        return
      }
      this.StockTradingform.branchFrLink = res.data
    },
    StockTradingformFileList2Success(res, file, fileList) {
      if (res.code !== 0) {
        this.$alert(res.desc)
        this.StockTradingformFileList2 = []
        return
      }
      this.StockTradingform.transToList[this.StockTradingformFileIndex].stockLink = res.data
      console.log(this.StockTradingform.transToList[this.StockTradingformFileIndex].stockLink)
    },
    setFundTradingformFileIndex(index) {
      this.FundTradingformFileIndex = index
    },
    FundTradingformFileListSuccess(res, file, fileList) {
      if (res.code !== 0) {
        this.$alert(res.desc)
        this.FundTradingformFileList = []
        return
      }
      this.FundTradingform.branchFrLink = res.data
    },
    FundTradingformFileList2Success(res, file, fileList) {
      if (res.code !== 0) {
        this.$alert(res.desc)
        this.FundTradingformFileList2 = []
        return
      }
      this.FundTradingform.transToList[this.FundTradingformFileIndex].fundLink = res.data
      console.log(this.FundTradingform.transToList[this.FundTradingformFileIndex].fundLink)
    },
    handleExceed(files, fileList) {
      this.$message.warning(`Currently restricted to select 1 file, this time chose ${files.length} files，and totally chose ${files.length + fileList.length} files`)
    },
    beforeRemove(file, fileList) {
      return this.$confirm(`Confirm to remove ${file.name}?`)
    },
    exportData(file, fileList) {
      if (!file) {
        return
      }
      const isLt2M = file.size / 1024 / 1024 < 5
      if (!isLt2M) {
        this.$message.error('only upload csv/xls/xlsx files, not exceed 5MB.')
        fileList = []
        return isLt2M
      }
    },
    refreshValidate() {
      for (const val of document.getElementsByClassName('el-form-item__error')) {
        val.innerHTML = ''
      }
      var vue = this
      var formName = 'Transferform'
      switch (vue.Transactionform.type) {
        case 'Transfer':
          formName = 'Transferform'
          break
        case 'Payment':
          formName = 'Paymentform'
          break
        case 'Credit Card':
          formName = 'CreditCardform'
          break
        case 'Term Deposit':
          formName = 'TermDepositform'
          break
        case 'Foreign Exchange':
          formName = 'ForeignExchangeform'
          break
        case 'Stock Trading':
          formName = 'StockTradingform'
          break
        case 'Fund Trading':
          formName = 'FundTradingform'
          break
        default:
          break
      }
      this.$refs[formName].validate()
    },
    setRegionOptions(countryCode) {
      if (countryCode === 'HK') {
        this.regionOptions = getRegionByProvinceName('香港特别行政区')
      } else {
        this.regionOptions = getAllProvince()
      }
      console.log(this.regionOptions)
      this.customerForm.region = this.regionOptions[0].value
      this.setDistrictOptions(countryCode, this.regionOptions[0].value)
    },
    setDistrictOptions(countryCode, regionName) {
      if (countryCode === 'HK') {
        this.districtOptions = getDistrictByCountryNameAndRegionName('香港特别行政区', regionName)
      } else {
        this.districtOptions = getRegionByProvinceName(regionName)
      }
      this.customerForm.district = this.districtOptions[0].value
    },
    submitCustomerForm() {
      const vue = this
      this.$refs['CustomerForm'].validate((valid) => {
        if (valid) {
          vue.loading = true
          const requestData = vue.getCustomerRequestData()
          console.log(requestData)
          customerGenerate(requestData)
            .then(function(res) {
              vue.$alert(res.message)
            })
            .finally(() => {
              vue.loading = false
            })
        } else {
          return false
        }
      })
    },
    getCustomerRequestData() {
      return {
        'occupation': this.customerForm.occupation,
        'nationality': this.customerForm.nationality,
        'maritalStatus ': this.customerForm.maritalStatus,
        'permanentResidenceStatus': this.customerForm.permanentResidenceStatus,
        'ageGroup': this.customerForm.ageGroup,
        'gender': this.customerForm.gender,
        'education': this.customerForm.education,
        'residenceCountyCode  ': this.customerForm.residenceCountyCode,
        'region': this.customerForm.region,
        'district': this.customerForm.district,
        'monthlyIncome': {
          'fr': this.customerForm.monthlyIncomeAmountFrom,
          'to': this.customerForm.monthlyIncomeAmountTo
        },
        'householdIncome': {
          'fr': this.customerForm.householdIncomeAmountFrom,
          'to': this.customerForm.householdIncomeAmountTo
        },
        'employmentStatus': this.customerForm.employmentStatus,
        'homeAddressSinceDate': {
          'fr': this.customerForm.homeAddressSinceDate[0],
          'to': this.customerForm.homeAddressSinceDate[1]
        },
        'preferredSpokenLanguage': this.customerForm.preferredSpokenLanguage,
        'emailSuffix': this.customerForm.emailSuffix,
        'createDate': {
          'fr': this.customerForm.createDate[0],
          'to': this.customerForm.createDate[1]
        },
        'countryOfBirth': this.customerForm.countryOfBirth,
        'branchNumber': {
          'fr': this.customerForm.branchNumberFrom,
          'to': this.customerForm.branchNumberTo
        },
        'customerStatus': this.customerForm.customerStatus,
        'total': this.customerForm.total,
        'tableName': this.customerForm.tableName
      }
    }
  }
}
</script>

<style>
@import url('./css/main.css');
.ellipsis{
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  line-height: 1.4;
}
.generatData .el-input--medium .el-input__inner {
  margin: 0;
}
.el-date-editor .el-range-separator {
  width: 8%;
}
</style>
