.learning-course{
  text-align: center;
  padding: 50px;
}
.class-img img{
  width: 400px;
  height: 250px;
  opacity: 0.5;
}
.class-img>div>div>a{
  display: block;
  color: #FFF;
}
.class-img>div>div{
  height: 100px;
  line-height: 100px;
  border: 1px solid;
  margin: 0 50px;
  position: relative;
  top: -50px;
  color: #FFF;
  background-color: #4472c4;
}
.class-img>div>div.second{
  background-color: #ed7d31;
}
.class-img>div>div.third{
  background-color: #548235;
}
.class-img>div>div.fourth{
  background-color: #3b3838;
}
.intro01{
  width:1200px;
  margin: 50px auto;
  text-align: left;
}
.intro01>div{
  margin: 50px -20px;
}
.intro01>div img{
  width: 300px;
}
.intro01>div>div>div{
  padding-top: 30px;
}
.img-list li{
  display: inline-block;
  width: 15%;
  height: 250px;
}
.img-list li img{
  width: 100%;
}
.img-list li + li{
  margin-left:5%;
}
.intro2{
  width: 1100px;
  text-align: center;
  margin: 0 auto !important;
  line-height: 150px;
}
.course-class{
  width: 1000px;
  margin: 0px auto -100px auto!important;
}
.course-class img{
  width: 100%;
  height: 200px;
  border-bottom: 1px solid #EBEEF5;
  cursor: pointer;
}
.course-class>div{
  margin-bottom: 50px;
  display: inline-block;
  float: none;
}
.learning-course h4{
  margin: 50px auto;
  width: 1000px;
  line-height: 35px;
}
.learning-course .intro2 h4{
  margin: 100px 0 0 0;
  width: 1000px;
}
.Industry-class{
  width: 1200px;
  margin: 0px auto -100px auto!important;
}
.Industry-class img{
  width: 100%;
  height: 200px;
  cursor: pointer;
}
.Industry-class>div{
  margin-bottom: 50px;
}
.Industry-class .el-card{
  border: none;
  height: 337px;
}
.Industry-class.course-catalog .el-card{
  height: 320px;
}
.Industry-class.course-list .el-card{
  height: 285px;
}
.Industry-class.course-detail .el-card{
  height: 230px;
}
.Industry-class.course-detail img{
  height: 140px;
}
.Industry-class .duration{
  float: right;
  pointer-events: none;
  font-size: 10px;
  position: relative;
  top: -40px;
  color: #FFF;
  text-shadow: 1px 1px 3px #000;
}
.pending{
  font-size: 64px;
  text-align: center;
  margin-top: 50px;
  color: #108b99;
  opacity: 0.5;
}
.learning-course .el-divider--horizontal{
  clear: both;
  width: 1200px;
  margin: 50px auto;
}

.inline-input {
  width: 200px;
  display: inline-block;
}
.input, .select {
  width: 100%;
}
