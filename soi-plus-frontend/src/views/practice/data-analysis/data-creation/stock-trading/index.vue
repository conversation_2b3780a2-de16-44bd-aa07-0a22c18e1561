<template>
  <div v-loading.fullscreen.lock="loading" class="data-visualization-data-creation-container">
    <customize-card :title="$t('soi.router.stockTradingDataCreation')">
      <!--Stock Trading-->
      <el-form
        ref="stockTradingForm"
        :rules="stockTradingFormRules"
        :model="stockTradingForm"
        label-position="left"
        label-width="160px"
      >
        <el-form-item :label="$t('soi.stockCreation.dataSource')" hidden="hidden">
          <el-select
            v-model="stockTradingForm.dataSource"
            style="width:100%;"
            :placeholder="$t('soi.stockCreation.dataSourcePlaceholder')"
          >
            <el-option label="10W" value="2" />
            <el-option label="Migration" value="1" />
          </el-select>
        </el-form-item>
        <!--        <el-form-item label="Account Type From: " prop="accountTypeFr">-->
        <!--          <el-select v-model="stockTradingForm.accountTypeFr" style="width:100%;" placeholder="">-->
        <!--            <el-option label="Saving" value="Saving" />-->
        <!--            <el-option label="Current" value="Current" />-->
        <!--          </el-select>-->
        <!--        </el-form-item>-->
        <el-form-item :label="$t('soi.stockCreation.channel')" prop="channelID">
          <el-select
            v-model="stockTradingForm.channelID"
            style="width:100%;"
            :placeholder="$t('soi.stockCreation.channel')"
          >
            <el-option v-for="item in channelList" :key="item.id" :value="item.id" :label="item.name" />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('soi.stockCreation.accountNumber')">
          <el-radio-group v-model="stockTradingFormSelectType">
            <el-radio-button label="1">{{ $t('soi.stockCreation.accountNumber') }}</el-radio-button>
            <el-radio-button label="2">{{ $t('soi.stockCreation.linkToDatatable') }}</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item
          v-if="stockTradingFormSelectType === '1'"
          prop="branchFr"
        >
          <el-input
            v-model="stockTradingForm.branchFr"
            :placeholder="$t('soi.stockCreation.transactionAccountPlaceholder')"
          />
        </el-form-item>
        <el-form-item v-if="stockTradingFormSelectType === '2'">
          <el-upload
            :action="dataHost+'/file/upload'"
            :before-remove="beforeRemove"
            :limit="1"
            :on-change="exportData"
            :on-exceed="handleExceed"
            :on-success="stockTradingFormFileListSuccess"
            :file-list="stockTradingFormFileList"
            class="upload-demo"
            accept=".csv,.xls,.xlsx"
          >
            <el-button size="small" type="primary" @click="downloadTemplate('StockAccountTemplate.xlsx')">
              {{ $t('soi.stockCreation.downloadAccountTemplate') }}
            </el-button>
            <el-button size="small" type="primary">{{ $t('soi.stockCreation.upload') }}</el-button>
            <span slot="tip" class="el-upload__tip">{{ $t('soi.stockCreation.uploadFileRule') }}</span>
          </el-upload>
        </el-form-item>
        <el-form-item :label="$t('soi.stockCreation.transactionType')">
          <el-select v-model="stockTradingForm.transType" style="width:100%;" placeholder="">
            <el-option label="Buy" value="Buy" />
            <el-option label="Sell" value="Sell" />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('soi.stockCreation.transactionDetails')">
          <el-button
            type="primary"
            icon="el-icon-download"
            size="mini"
            @click="exportAvailableStockCode('hasData')"
          >
            {{ $t('soi.stockCreation.stockCodeList') }}
          </el-button>
          <el-button
            type="primary"
            icon="el-icon-download"
            size="mini"
            @click="exportAvailableStockCode('')"
          >
            {{ $t('soi.stockCreation.stockCodeTemplate') }}
          </el-button>
          <el-row
            v-for="(data,index) in stockTradingForm.transToList"
            :key="index"
            :gutter="10"
            :style="{ margin: index > 0 ? '20px 0 0 0' : '0' }"
          >
            <el-col :span="11">
              <el-form-item label-width="90px" :label="$t('soi.stockCreation.stockCode')">
                <el-upload
                  :action="dataHost+'/file/upload'"
                  :before-remove="beforeRemove"
                  :limit="1"
                  :on-change="exportData"
                  :on-exceed="handleExceed"
                  :on-success="stockTradingFormFileList2Success"
                  :file-list="stockTradingFormFileList2[index]"
                  class="upload-demo"
                  accept=".csv,.xls,.xlsx"
                >
                  <el-button size="small" type="primary" @click="setStockTradingFormFileIndex(index)">
                    {{ $t('soi.stockCreation.batchUploadStockCode') }}
                  </el-button>
                </el-upload>
              </el-form-item>
            </el-col>
            <el-col :span="11">
              <el-form-item
                :prop="'transToList.' + index + '.no'"
                :rules="{required: true, validator: validateLimitValue, trigger: 'blur'}"
                label-width="50px"
                :label="$t('soi.stockCreation.noOfLots')"
              >
                <el-input
                  v-model="data.no"
                  style="margin:0;"
                  :placeholder="$t('soi.stockCreation.noOfLotsPlaceholder')"
                />
              </el-form-item>
            </el-col>
            <el-col :span="2">
              <el-button
                v-if="index === 0"
                type="primary"
                @click="addStockTradingFormCondition()"
              >{{
                $t('soi.common.add')
              }}
              </el-button>
              <el-button
                v-if="index > 0"
                type="primary"
                @click="handleDeleteStockTradingForm(index)"
              >{{ $t('soi.common.delete') }}
              </el-button>
            </el-col>
          </el-row>
        </el-form-item>
        <el-form-item :label="$t('soi.stockCreation.transactionDate')">
          <el-col :span="11">
            <el-form-item
              :label="$t('soi.stockCreation.transactionDateFrom')"
              prop="transactionFromDate"
              label-width="60px"
            >
              <el-date-picker
                v-model="stockTradingForm.transactionFromDate"
                type="datetime"
                value-format="timestamp"
                style="width: 60%;"
                :placeholder="$t('soi.stockCreation.transactionDateFromPlaceholder')"
                :picker-options="pickerOptionsStart"
              />
            </el-form-item>
          </el-col>
          <el-col :span="2" class="line" style="text-align: center;">-</el-col>
          <el-col :span="11">
            <el-form-item
              :label="$t('soi.stockCreation.transactionDateTo')"
              prop="transactionToDate"
              label-width="60px"
            >
              <el-date-picker
                v-model="stockTradingForm.transactionToDate"
                type="datetime"
                value-format="timestamp"
                style="width: 60%;"
                :placeholder="$t('soi.stockCreation.transactionDateToPlaceholder')"
                :picker-options="pickerOptionsEnd"
              />
            </el-form-item>
          </el-col>
        </el-form-item>
        <el-form-item :label="$t('soi.stockCreation.frequency')" prop="frequency">
          <el-select
            v-model="stockTradingForm.frequency"
            style="width:100%;"
            :placeholder="$t('soi.stockCreation.frequencyPlaceholder')"
          >
            <el-option v-for="item in frequencyList" :key="item" :value="item" :label="item" />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('soi.stockCreation.frequencyRate')" prop="frequencyRate">
          <el-input
            v-model="stockTradingForm.frequencyRate"
            :placeholder="$t('soi.stockCreation.frequencyRatePlaceholder')"
          />
        </el-form-item>
        <el-form-item :label="$t('soi.stockCreation.totalTransaction')" prop="total">
          <el-input-number
            v-model="stockTradingForm.total"
            :max="1000"
            :min="1"
            style="width: 100%;"
            :placeholder="$t('soi.stockCreation.totalTransactionPlaceholder')"
          />
        </el-form-item>
        <el-form-item label="Stock Table: " prop="stockTable" hidden="hidden">
          <el-select
            v-model="stockTradingForm.stockTable"
            filterable
            allow-create
            style="width:100%;"
            default-first-option
            placeholder=""
          >
            <el-option
              v-for="item in stockTableNameList"
              :key="item"
              :label="item"
              :value="item"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="Transfer Table: " prop="transferTable" hidden="hidden">
          <el-select
            v-model="stockTradingForm.transferTable"
            filterable
            allow-create
            style="width:100%;"
            default-first-option
            placeholder=""
          >
            <el-option
              v-for="item in tableNameList"
              :key="item"
              :label="item"
              :value="item"
            />
          </el-select>
        </el-form-item>
        <div style="text-align:center;">
          <el-button type="primary" @click="submitForm()">{{ $t('soi.common.submit') }}</el-button>
          <el-button @click="resetForm()">{{ $t('soi.common.reset') }}</el-button>
        </div>
      </el-form>
    </customize-card>
  </div>
</template>
<script>
import CustomizeCard from '@/components/CustomizeCard/index.vue'
import {
  getAllFrequencyList,
  getChannelList,
  getStockOrderTypeList,
  getTableList,
  stockGenerate
} from '@/api/practice/data-creation'
import { DATA_CREATION_API_URL } from '@/contains'
import { downloadTemplate } from '@/api/practice/document'
import { mapGetters } from 'vuex'
import { exportAvailableStockCode } from '@/api/practice/date-export'

export default {
  name: 'StockTradingDataCreation',
  components: { CustomizeCard },
  data() {
    const validateLimitValue = (rule, value, callback) => {
      let a
      a = a = /^[0-9]\d*$/g

      if (!a.test(value)) {
        callback(new Error(this.$t('soi.common.invalidNumber')))
      } else {
        callback()
      }
    }
    const startDate = new Date()
    startDate.setHours(0, 0, 0, 0) // 设置时、分、秒、毫秒为 0
    const endDate = new Date()
    endDate.setHours(23, 59, 59, 0) // 设置时、分、秒、毫秒为 0
    return {
      validateLimitValue: validateLimitValue,
      dataHost: DATA_CREATION_API_URL,
      loading: true,
      stockTradingFormSelectType: '1',
      stockTradingFormFileIndex: null,
      channelList: [],
      frequencyList: [],
      stockTableNameList: [],
      transferTableNameList: [],
      stockTradingFormFileList: [],
      stockTradingFormFileList2: [],
      stockOrderTypeList: [],
      tableNameList: [],
      stockTradingForm: {
        dataSource: '1',
        accountTypeFr: 'Current',
        branchFr: '',
        branchFrLink: '',
        transType: 'Buy',
        transToList: [
          {
            orderType: 'Fix',
            settleAccType: 'Saving',
            no: '100',
            stockLink: ''
          }
        ],
        transactionFromDate: startDate.getTime(),
        transactionToDate: endDate.getTime(),
        total: '1000',
        channelID: 1,
        frequency: 'Weekly',
        frequencyRate: '2',
        stockTable: 'stock_transaction',
        transferTable: 'transfer_transaction',
        userId: ''
      },
      stockTradingFormRules: {
        branchFr: [{
          required: true,
          message: this.$t('soi.stockCreation.transactionAccountPlaceholder'),
          trigger: 'blur'
        }],
        transactionFromDate: [{
          required: true,
          message: this.$t('soi.stockCreation.transactionDateFromPlaceholder'),
          trigger: 'change'
        }],
        transactionToDate: [{
          required: true,
          message: this.$t('soi.stockCreation.transactionDateToPlaceholder'),
          trigger: 'change'
        }],
        frequency: [{ required: true, message: this.$t('soi.stockCreation.frequencyPlaceholder'), trigger: 'change' }],
        frequencyRate: [{
          required: true,
          message: this.$t('soi.stockCreation.frequencyRatePlaceholder'),
          trigger: 'blur'
        }],
        total: [{ required: true, message: this.$t('soi.stockCreation.totalTransactionPlaceholder'), trigger: 'blur' }],
        stockTable: [{ required: true, message: 'Please input the stock table name.', trigger: 'change' }],
        transferTable: [{ required: true, message: 'Please input the transfer table name.', trigger: 'change' }]
      },
      pickerOptionsStart: {
        disabledDate: time => {
          const beginDateVal = this.stockTradingForm.transactionToDate
          if (beginDateVal) {
            return (
              time.getTime() > new Date(beginDateVal).getTime() - 24 * 60 * 60 * 1000
            )
          }
        }
      },
      pickerOptionsEnd: {
        disabledDate: time => {
          const beginDateVal = this.stockTradingForm.transactionFromDate
          if (beginDateVal) {
            return (
              time.getTime() < new Date(beginDateVal).getTime() + 24 * 60 * 60 * 1000
            )
          }
        }
      }
    }
  },
  computed: {
    ...mapGetters(['userDetails'])
  },
  watch: {
    'stockTradingFormSelectType'(newValue) {
      if (newValue === '2') {
        this.$refs['stockTradingForm'].clearValidate(['branchFr'])
      }
    }
  },
  mounted() {
    this.initSelectOptions()
    this.initTableName()
    this.loading = false
  },
  methods: {
    initTableName() {
      const _this = this
      getTableList({ type: 'Stock' })
        .then(function(res) {
          _this.stockTableNameList = res.data
        })
        .catch(() => {
          _this.stockTableNameList = []
        })
      getTableList({ type: 'Transfer' })
        .then(function(res) {
          _this.transferTableNameList = res.data
        })
        .catch(() => {
          _this.transferTableNameList = []
        })
    },
    initSelectOptions() {
      const _this = this
      getAllFrequencyList()
        .then(function(res) {
          _this.frequencyList = res.data
        })
        .catch(() => {
          _this.frequencyList = []
        })
      getChannelList()
        .then(function(res) {
          _this.channelList = res.data.filter(item => item.id === 1)
        })
        .catch(() => {
          _this.channelList = []
        })
      getStockOrderTypeList()
        .then(function(res) {
          _this.stockOrderTypeList = res.data
        })
        .catch(() => {
          _this.stockOrderTypeList = []
        })
    },
    handleExceed(files, fileList) {
      this.$message.warning(this.$t('soi.stockCreation.handleExceedMessage', {
        thisTimeChoose: files.length,
        totallyChoose: files.length + fileList.length
      }))
    },
    beforeRemove(file, fileList) {
      return this.$confirm(this.$t('soi.stockCreation.beforeRemoveMessage', { fileName: file.name }))
    },
    exportData(file, fileList) {
      if (!file) {
        return
      }
      const isLt2M = file.size / 1024 / 1024 < 5
      if (!isLt2M) {
        this.$message.error(this.$t('soi.stockCreation.uploadFileRule'))
        fileList = []
        return isLt2M
      }
    },
    stockTradingFormFileListSuccess(res, file, fileList) {
      if (res.code !== 20000) {
        this.$alert(res.message)
        this.stockTradingFormFileList = []
        return
      }
      this.stockTradingForm.branchFrLink = res.data
    },
    stockTradingFormFileList2Success(res, file, fileList) {
      if (res.code !== 20000) {
        this.$alert(res.message)
        this.stockTradingFormFileList2 = []
        return
      }
      this.stockTradingForm.transToList[this.stockTradingFormFileIndex].stockLink = res.data
    },
    addStockTradingFormCondition() {
      var _this = this
      _this.stockTradingForm.transToList.push(
        {
          orderType: 'Fix',
          settleAccType: 'Saving',
          no: '',
          stockLink: ''
        }
      )
    },
    setStockTradingFormFileIndex(index) {
      this.stockTradingFormFileIndex = index
    },
    handleDeleteStockTradingForm(index) {
      this.stockTradingForm.transToList.splice(index, 1)
    },
    submitForm() {
      this.$refs['stockTradingForm'].validate((valid) => {
        const _this = this
        if (valid) {
          _this.loading = true
          _this.stockTradingFormSelectType === '1' ? _this.stockTradingForm.branchFrLink = '' : _this.stockTradingForm.branchFr = ''
          _this.stockTradingForm.userId = _this.userDetails.id
          stockGenerate(_this.stockTradingForm)
            .then(function(res) {
              _this.$alert(res.message)
            })
            .finally(() => {
              _this.loading = false
            })
        } else {
          return false
        }
      })
    },
    resetForm() {
      const startDate = new Date()
      startDate.setHours(0, 0, 0, 0) // 设置时、分、秒、毫秒为 0
      const endDate = new Date()
      endDate.setHours(23, 59, 59, 0) // 设置时、分、秒、毫秒为 0
      this.stockTradingForm = {
        dataSource: '1',
        accountTypeFr: 'Current',
        branchFr: '',
        branchFrLink: '',
        transType: 'Buy',
        transToList: [
          {
            orderType: 'Fix',
            settleAccType: 'Saving',
            no: '',
            stockLink: ''
          }
        ],
        transactionFromDate: startDate.getTime(),
        transactionToDate: endDate.getTime(),
        total: '1000',
        channelID: 1,
        frequency: 'Weekly',
        frequencyRate: '',
        stockTable: 'stock_transaction',
        transferTable: 'transfer_transaction',
        userId: ''
      }
    },
    downloadTemplate,
    exportAvailableStockCode
  }
}
</script>
<style scoped>
.maintenance-page {
  text-align: center;
  padding: 20px;
  color: #333;
}
</style>
