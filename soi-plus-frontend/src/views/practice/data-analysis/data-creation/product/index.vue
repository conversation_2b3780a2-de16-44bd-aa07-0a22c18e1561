<template>
  <div v-loading.fullscreen.lock="loading" class="data-visualization-data-creation-container">
    <customize-card :title="$t('soi.router.productDataCreation')">
      <el-form ref="accountForm" :model="accountForm" :rules="accountFormRules" label-position="left" label-width="160px">
        <el-form-item label="Account Type" prop="accountType">
          <el-select
            v-model="accountForm.accountType"
            class="select"
            @change="handlerChangeAccountType"
          >
            <el-option
              v-for="(item, index) in accountTypeOptions"
              :key="index"
              :label="item.label"
              :value="item.value"
              :disabled="(!relAccountTableList || relAccountTableList.length === 0) && index > 1"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="Currency Code" prop="currencyCode">
          <el-select v-model="accountForm.currencyCode" class="select">
            <el-option
              v-for="(item, index) in currencyTypeOptions"
              :key="index"
              :disabled="item.value !== 'HDK' && accountForm.accountType !== '003'"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="Create Date">
          <el-col :span="11">
            <el-form-item label="From" prop="createDateFrom" label-width="50px">
              <el-date-picker
                v-model="accountForm.createDateFrom"
                type="datetime"
                value-format="timestamp"
                placeholder="Start Date"
                class="date-select"
              />
            </el-form-item>
          </el-col>
          <el-col :span="2" class="line" style="text-align: center;">-</el-col>
          <el-col :span="11">
            <el-form-item label="To" prop="createDateTo" label-width="50px">
              <el-date-picker
                v-model="accountForm.createDateTo"
                type="datetime"
                value-format="timestamp"
                placeholder="End Date"
                class="date-select"
              />
            </el-form-item>
          </el-col>
        </el-form-item>
        <el-form-item label="Customer Table" prop="customerTable">
          <el-select
            v-model="accountForm.customerTable"
            class="input"
            default-first-option
            placeholder=""
          >
            <el-option
              v-for="item in customerTableList"
              :key="item"
              :label="item"
              :value="item"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="Account Table" prop="accountTable">
          <el-select
            v-model="accountForm.accountTable"
            filterable
            allow-create
            class="input"
            default-first-option
            placeholder=""
          >
            <el-option
              v-for="item in accountTableList"
              :key="item"
              :label="item"
              :value="item"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          v-if="accountForm.accountType !== '001' && accountForm.accountType !== '002'"
          label="Rel Account Table"
          prop="relAccountTable"
        >
          <el-select
            v-model="accountForm.relAccountTable"
            class="input"
            default-first-option
            placeholder=""
          >
            <el-option
              v-for="item in relAccountTableList"
              :key="item"
              :label="item"
              :value="item"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="Total" prop="total">
          <el-input v-model="accountForm.total" placeholder="Total" />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="submitForm('accountForm')">Submit</el-button>
        </el-form-item>
      </el-form>
    </customize-card>
  </div>
</template>
<script>
import { accountGenerate, getCustomerOccupationList, getTableList } from '@/api/practice/data-creation'
import {
  accountTypeOptions,
  currencyTypeOptions
} from '@/views/practice/data-analysis/data-creation/customer/js'
import CustomizeCard from '@/components/CustomizeCard/index.vue'
export default {
  name: 'ProductDataCreation',
  components: { CustomizeCard },
  data() {
    const startDate = new Date()
    startDate.setHours(0, 0, 0, 0) // 设置时、分、秒、毫秒为 0
    const endDate = new Date()
    endDate.setHours(23, 59, 59, 0) // 设置时、分、秒、毫秒为 0
    return {
      loading: true,
      accountForm: {
        accountType: '001',
        currencyCode: 'HKD',
        createDateFrom: startDate.getTime(),
        createDateTo: endDate.getTime(),
        total: 100,
        customerTable: '',
        accountTable: '',
        relAccountTable: ''
      },
      accountTypeOptions,
      currencyTypeOptions,
      accountFormRules: {
        total: [
          { required: true, message: 'Please input the account numbers', trigger: 'blur' },
          { pattern: /^[0-9]\d*$/g, message: 'Invalid account numbers', trigger: 'blur' }
        ],
        createDateFrom: [
          { required: true, message: 'Please select the create date', trigger: 'blur' }
        ],
        createDateTo: [
          { required: true, message: 'Please select the end data', trigger: 'blur' }
        ]
      },
      relAccountTableList: [],
      accountTableList: [],
      customerTableList: []
    }
  },
  created() {
    this.initTableNameList('Customer')
    this.initTableNameList('001')
    this.initTableNameList('001,002')
  },
  mounted() {
    const _this = this
    getCustomerOccupationList()
      .then(function(res) {
        _this.occupationOptions = res.data
      })
      .finally(() => {
        _this.loading = false
      })
  },
  methods: {
    handlerChangeAccountType(value) {
      this.initTableNameList(value)
    },
    initTableNameList(type) {
      const _this = this
      getTableList({ type })
        .then(function(res) {
          if (type === 'Customer') {
            _this.customerTableList = res.data
            if (_this.customerTableList && _this.customerTableList.length > 0) {
              _this.accountForm.customerTable = _this.customerTableList[0]
            }
          } else if (type === '001,002') {
            _this.relAccountTableList = res.data
            if (_this.relAccountTableList && _this.relAccountTableList.length > 0) {
              _this.accountForm.relAccountTable = _this.relAccountTableList[0]
            }
          } else {
            _this.accountTableList = res.data
            if (_this.accountTableList && _this.accountTableList.length > 0) {
              _this.accountForm.accountTable = _this.accountTableList[0]
            } else {
              _this.accountForm.accountTable = ''
            }
          }
        })
        .finally(() => {
          _this.loading = false
        })
    },
    submitForm() {
      this.$refs['accountForm'].validate((valid) => {
        if (valid) {
          const requestData = {
            accountTable: this.accountForm.accountTable,
            accountType: this.accountForm.accountType,
            creationDate: {
              fr: this.accountForm.createDateFrom,
              to: this.accountForm.createDateTo
            },
            currencyCode: this.accountForm.currencyCode,
            customerTable: this.accountForm.customerTable,
            relAccountTable: (this.accountForm.accountType === '001' || this.accountForm.accountType === '002') ? '' : this.accountForm.relAccountTable,
            total: this.accountForm.total
          }
          const _this = this
          this.loading = true
          accountGenerate(requestData)
            .then(function(res) {
              _this.$alert(res.message)
            })
            .finally(() => {
              _this.loading = false
            })
        } else {
          return false
        }
      })
    }
  }
}
</script>
<style scoped>
@import url('../customer/css/main.css');
.maintenance-page {
  text-align: center;
  padding: 20px;
  color: #333;
}
</style>
