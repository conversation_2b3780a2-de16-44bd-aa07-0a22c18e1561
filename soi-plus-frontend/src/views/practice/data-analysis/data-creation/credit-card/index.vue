<template>
  <div v-loading.fullscreen.lock="loading" class="data-visualization-data-creation-container">
    <customize-card :title="$t('soi.router.creditCardDataCreation')">
      <!--Credit Card-->
      <el-form
        ref="creditCardForm"
        :rules="creditCardFormRules"
        :model="creditCardForm"
        label-position="left"
        label-width="140px"
      >
        <el-form-item :label="$t('soi.creditCardCreation.dataSource')" hidden="hidden">
          <el-select
            v-model="creditCardForm.dataSource"
            style="width:100%;"
            :placeholder="$t('soi.creditCardCreation.dataSourcePlaceholder')"
          >
            <el-option label="10W" value="2" />
            <el-option label="Migration" value="1" />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('soi.creditCardCreation.transactionByType')">
          <el-radio-group v-model="creditCardFormSelectType">
            <el-radio-button label="1">{{ $t('soi.creditCardCreation.creditCardNumber') }}</el-radio-button>
            <el-radio-button label="2">{{ $t('soi.creditCardCreation.linkToDatatable') }}</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="creditCardFormSelectType === '1'">
          <el-form-item prop="creditCardNumber">
            <el-input
              v-model="creditCardForm.creditCardNumber"
              :placeholder="$t('soi.creditCardCreation.creditCardNumberPlaceholder')"
            />
          </el-form-item>
        </el-form-item>
        <el-form-item v-if="creditCardFormSelectType === '2'">
          <el-upload
            :action="dataHost+'/file/upload'"
            :before-remove="beforeRemove"
            :limit="1"
            :on-change="exportData"
            :on-exceed="handleExceed"
            :on-success="creditCardFormFileListSuccess"
            :file-list="creditCardFormFileList"
            class="upload-demo"
            accept=".csv,.xls,.xlsx"
          >
            <el-button size="small" type="primary" @click="downloadTemplate('data-creation-credit-card-number.xlsx')">
              {{ $t('soi.creditCardCreation.downloadCreditCardNumberTemplate') }}
            </el-button>
            <el-button size="small" type="primary">{{ $t('soi.creditCardCreation.upload') }}</el-button>
            <span slot="tip" class="el-upload__tip">{{ $t('soi.creditCardCreation.uploadFileRule') }}</span>
          </el-upload>
        </el-form-item>
        <el-form-item :label="$t('soi.creditCardCreation.transactionTo')">
          <el-row
            v-for="(data,index) in creditCardForm.transToList"
            :key="index"
            :gutter="20"
            :style="{ margin: index > 0 ? '20px 0 0 0' : '0' }"
          >
            <el-col :span="6">
              <el-form-item
                :label="$t('soi.creditCardCreation.industry')+(index+1)"
                :prop="'transToList.' + index + '.categoryname'"
                :rules="{required: true, message: 'Please select a industry', trigger: 'change'}"
                label-width="80px"
              >
                <el-select v-model="data.categoryname" style="width:100%;">
                  <el-option v-for="item in creditCardIndustryList" :key="item" :value="item" :label="item" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="10">
              <el-form-item
                :prop="'transToList.' + index + '.amountFr'"
                :rules="{required: true, validator: validateLimitValue, trigger: 'blur'}"
                :label="$t('soi.creditCardCreation.transactionAmountFrom')"
                label-width="200px"
              >
                <el-input
                  v-model="data.amountFr"
                  style="margin:0;"
                  :placeholder="$t('soi.creditCardCreation.transactionAmountFromPlaceholder')"
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item
                :prop="'transToList.' + index + '.amountTo'"
                :rules="[
                  {required: true, validator: validateLimitValue, trigger: 'blur'},
                  {required: true, validator: (rule, value, callback) => validateAmount(rule, value, callback, data.amountFr,'transactionDateFrom','transactionAmountTo'), trigger: 'blur'}
                ]"
                :label="$t('soi.creditCardCreation.transactionAmountTo')"
                label-width="40px"
              >
                <el-input
                  v-model="data.amountTo"
                  style="margin:0;"
                  :placeholder="$t('soi.creditCardCreation.transactionAmountToPlaceholder')"
                />
              </el-form-item>
            </el-col>
            <el-col :span="2">
              <el-button
                v-if="index === 0"
                type="primary"
                @click="addCreditCardFormCondition()"
              >{{ $t('soi.common.add') }}
              </el-button>
              <el-button
                v-if="index > 0"
                type="primary"
                @click="handleDeleteCreditCardForm(index)"
              >{{ $t('soi.common.delete') }}
              </el-button>
            </el-col>
          </el-row>
        </el-form-item>
        <el-form-item :label="$t('soi.creditCardCreation.transactionDate')">
          <el-col :span="11">
            <el-form-item
              :label="$t('soi.creditCardCreation.transactionDateFrom')"
              prop="transactionFromDate"
              label-width="60px"
            >
              <el-date-picker
                v-model="creditCardForm.transactionFromDate"
                type="datetime"
                value-format="timestamp"
                style="width: 60%;"
                :placeholder="$t('soi.creditCardCreation.transactionDateFromPlaceholder')"
                :picker-options="pickerOptionsStart"
              />
            </el-form-item>
          </el-col>
          <el-col :span="2" class="line" style="text-align: center;">-</el-col>
          <el-col :span="11">
            <el-form-item
              :label="$t('soi.creditCardCreation.transactionDateTo')"
              prop="transactionToDate"
              label-width="60px"
            >
              <el-date-picker
                v-model="creditCardForm.transactionToDate"
                type="datetime"
                value-format="timestamp"
                style="width: 60%;"
                :placeholder="$t('soi.creditCardCreation.transactionDateToPlaceholder')"
                :picker-options="pickerOptionsEnd"
              />
            </el-form-item>
          </el-col>
        </el-form-item>
        <el-form-item :label="$t('soi.creditCardCreation.frequency')" prop="frequency">
          <el-select
            v-model="creditCardForm.frequency"
            style="width:100%;"
            :placeholder="$t('soi.creditCardCreation.frequencyPlaceholder')"
          >
            <el-option v-for="item in frequencyList" :key="item" :value="item" :label="item" />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('soi.creditCardCreation.frequencyRate')" prop="frequencyRate">
          <el-input
            v-model="creditCardForm.frequencyRate"
            :placeholder="$t('soi.creditCardCreation.frequencyRatePlaceholder')"
          />
        </el-form-item>
        <el-form-item :label="$t('soi.creditCardCreation.totalTransaction')" prop="total">
          <el-input-number
            v-model="creditCardForm.total"
            :max="1000"
            :min="1"
            style="width: 100%;"
            :placeholder="$t('soi.creditCardCreation.totalTransactionPlaceholder')"
          />
        </el-form-item>
        <el-form-item :label="$t('soi.creditCardCreation.tableName')" prop="tableName" hidden="hidden">
          <el-select
            v-model="creditCardForm.tableName"
            filterable
            allow-create
            style="width:100%;"
            default-first-option
            :placeholder="$t('soi.creditCardCreation.tableNamePlaceholder')"
          >
            <el-option
              v-for="item in tableNameList"
              :key="item"
              :label="item"
              :value="item"
            />
          </el-select>
        </el-form-item>
        <div style="text-align:center;">
          <el-button type="primary" @click="submitForm()">{{ $t('soi.common.submit') }}</el-button>
          <el-button @click="resetForm()">{{ $t('soi.common.reset') }}</el-button>
        </div>
      </el-form>
    </customize-card>
  </div>
</template>
<script>
import CustomizeCard from '@/components/CustomizeCard/index.vue'
import { DATA_CREATION_API_URL } from '@/contains'
import {
  creditCardGenerate,
  getAllFrequencyList,
  getCreditCardIndustryList,
  getTableList
} from '@/api/practice/data-creation'
import { downloadTemplate } from '@/api/practice/document'
import { mapGetters } from 'vuex'

export default {
  name: 'CreditCardDataCreation',
  components: { CustomizeCard },
  data() {
    const validateLimitValue = (rule, value, callback) => {
      let a
      a = a = /^[0-9]\d*$/g

      if (!a.test(value)) {
        callback(new Error(this.$t('soi.common.invalidNumber')))
      } else {
        callback()
      }
    }
    const startDate = new Date()
    startDate.setHours(0, 0, 0, 0) // 设置时、分、秒、毫秒为 0
    const endDate = new Date()
    endDate.setHours(23, 59, 59, 0) // 设置时、分、秒、毫秒为 0
    return {
      loading: true,
      dataHost: DATA_CREATION_API_URL,
      validateLimitValue: validateLimitValue,
      creditCardFormSelectType: '1',
      creditCardFormFileList: [],
      creditCardIndustryList: [],
      frequencyList: [],
      tableNameList: [],
      creditCardForm: {
        dataSource: '1',
        creditCardNumber: '',
        sequenceFrLink: '',
        transToList: [
          {
            categoryname: 'Clothing-Retailers',
            amountFr: '100',
            amountTo: '1000'
          }
        ],
        transactionFromDate: startDate.getTime(),
        transactionToDate: endDate.getTime(),
        total: '1000',
        frequency: 'Weekly',
        frequencyRate: '2',
        tableName: 'creditcard_transaction',
        userId: ''
      },
      creditCardFormRules: {
        transactionFromDate: [{
          required: true,
          message: this.$t('soi.creditCardCreation.transactionDateFromPlaceholder'),
          trigger: 'change'
        }],
        transactionToDate: [{
          required: true,
          message: this.$t('soi.creditCardCreation.transactionDateToPlaceholder'),
          trigger: 'change'
        }],
        frequency: [{
          required: true,
          message: this.$t('soi.creditCardCreation.frequencyPlaceholder'),
          trigger: 'change'
        }],
        frequencyRate: [{
          required: true,
          message: this.$t('soi.creditCardCreation.frequencyRatePlaceholder'),
          trigger: 'blur'
        }],
        creditCardNumber: [{
          required: true,
          message: this.$t('soi.creditCardCreation.creditCardNumberPlaceholder'),
          trigger: 'blur'
        }],
        total: [{ required: true, message: 'Please input the total transaction.', trigger: 'blur' }],
        tableName: [{ required: true, message: 'Please input the table name.', trigger: 'change' }]
      },
      pickerOptionsStart: {
        disabledDate: time => {
          const beginDateVal = this.creditCardForm.transactionToDate
          if (beginDateVal) {
            return (
              time.getTime() > new Date(beginDateVal).getTime() - 24 * 60 * 60 * 1000
            )
          }
        }
      },
      pickerOptionsEnd: {
        disabledDate: time => {
          const beginDateVal = this.creditCardForm.transactionFromDate
          if (beginDateVal) {
            return (
              time.getTime() < new Date(beginDateVal).getTime() + 24 * 60 * 60 * 1000
            )
          }
        }
      }
    }
  },
  computed: {
    ...mapGetters(['userDetails'])
  },
  mounted() {
    this.initTableName()
    this.initSelectOptions()
    this.loading = false
  },
  methods: {
    initSelectOptions() {
      const _this = this
      getAllFrequencyList()
        .then(function(res) {
          _this.frequencyList = res.data
        })
        .catch(() => {
          _this.frequencyList = []
        })
      getCreditCardIndustryList()
        .then(function(res) {
          _this.creditCardIndustryList = res.data
        })
        .catch(() => {
          _this.creditCardIndustryList = []
        })
    },
    initTableName() {
      const _this = this
      getTableList({ type: 'CreditCard' })
        .then(function(res) {
          _this.tableNameList = res.data
        })
        .catch(() => {
          _this.tableNameList = []
        })
    },
    handleExceed(files, fileList) {
      this.$message.warning(this.$t('soi.creditCardCreation.handleExceedMessage', {
        thisTimeChoose: files.length,
        totallyChoose: files.length + fileList.length
      }))
    },
    beforeRemove(file, fileList) {
      return this.$confirm(this.$t('soi.creditCardCreation.beforeRemoveMessage', { fileName: file.name }))
    },
    exportData(file, fileList) {
      if (!file) {
        return
      }
      const isLt2M = file.size / 1024 / 1024 < 5
      if (!isLt2M) {
        this.$message.error(this.$t('soi.creditCardCreation.uploadFileRule'))
        fileList = []
        return isLt2M
      }
    },
    creditCardFormFileListSuccess(res, file, fileList) {
      if (res.code !== 20000) {
        this.$alert(res.message)
        this.creditCardFormFileList = []
        return
      }
      this.creditCardForm.sequenceFrLink = res.data
    },
    addCreditCardFormCondition() {
      var _this = this
      _this.creditCardForm.transToList.push(
        {
          categoryname: 'Clothing-Retailers',
          amountFr: '',
          amountTo: ''
        }
      )
    },
    handleDeleteCreditCardForm(index) {
      this.creditCardForm.transToList.splice(index, 1)
    },
    submitForm() {
      this.$refs['creditCardForm'].validate((valid) => {
        if (valid) {
          const _this = this
          _this.creditCardFormSelectType === '1' ? _this.creditCardForm.sequenceFrLink = '' : _this.creditCardForm.creditCardNumber = ''
          _this.creditCardForm.userId = _this.userDetails.id
          _this.loading = true
          creditCardGenerate(_this.creditCardForm)
            .then(function(res) {
              _this.$alert(res.message)
            })
            .finally(() => {
              _this.loading = false
            })
        } else {
          return false
        }
      })
    },
    resetForm() {
      const startDate = new Date()
      startDate.setHours(0, 0, 0, 0) // 设置时、分、秒、毫秒为 0
      const endDate = new Date()
      endDate.setHours(23, 59, 59, 0) // 设置时、分、秒、毫秒为 0
      this.creditCardForm = {
        dataSource: '1',
        creditCardNumber: '',
        sequenceFrLink: '',
        transToList: [
          {
            categoryname: 'Clothing-Retailers',
            amountFr: '',
            amountTo: ''
          }
        ],
        transactionFromDate: startDate.getTime(),
        transactionToDate: endDate.getTime(),
        total: '1000',
        frequency: 'Weekly',
        frequencyRate: '',
        tableName: 'creditcard_transaction',
        userId: ''
      }
    },
    downloadTemplate,
    validateAmount(rule, value, callback, amountFrValue, amountFr, amountTo) {
      if (value === '' || value === null) {
        callback(new Error(this.$t('soi.creditCardCreation.transactionAmountToPlaceholder')))
      } else if (Number(value) < Number(amountFrValue)) {
        callback(new Error(
          this.$t(
            'soi.creditCardCreation.numberGreaterThanPlaceholder',
            {
              form: this.$t(`soi.creditCardCreation.${amountFr}`),
              to: this.$t(`soi.creditCardCreation.${amountTo}`)
            }
          )
        ))
      } else {
        callback()
      }
    }
  }
}
</script>
<style scoped>
.maintenance-page {
  text-align: center;
  padding: 20px;
  color: #333;
}
</style>
