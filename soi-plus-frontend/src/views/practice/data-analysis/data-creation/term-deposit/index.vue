<template>
  <div v-loading.fullscreen.lock="loading" class="data-visualization-data-creation-container">
    <customize-card :title="$t('soi.router.termDepositDataCreation')">
      <!--Term Deposit-->
      <el-form
        ref="termDepositForm"
        :rules="termDepositFormRules"
        :model="termDepositForm"
        label-position="left"
        label-width="160px"
      >
        <el-form-item :label="$t('soi.termDepositCreation.dataSource')" hidden="hidden">
          <el-select
            v-model="termDepositForm.dataSource"
            style="width:100%;"
            :placeholder="$t('soi.termDepositCreation.dataSourcePlaceholder')"
          >
            <el-option label="10W" value="2" />
            <el-option label="Migration" value="1" />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('soi.termDepositCreation.accountNumber')">
          <el-radio-group v-model="termDepositFormSelectType">
            <el-radio-button label="1">{{ $t('soi.termDepositCreation.accountNumber') }}</el-radio-button>
            <el-radio-button label="2">{{ $t('soi.termDepositCreation.linkToDatatable') }}</el-radio-button>
          </el-radio-group>
        </el-form-item>
        <el-form-item
          v-if="termDepositFormSelectType === '1'"
          prop="branchFr"
        >
          <el-input
            v-model="termDepositForm.branchFr"
            :placeholder="$t('soi.termDepositCreation.transactionAccountPlaceholder')"
          />
        </el-form-item>
        <el-form-item v-if="termDepositFormSelectType === '2'">
          <el-upload
            :action="dataHost+'/file/upload'"
            :before-remove="beforeRemove"
            :limit="1"
            :on-change="exportData"
            :on-exceed="handleExceed"
            :on-success="termDepositFormFileListSuccess"
            :file-list="termDepositFormFileList"
            class="upload-demo"
            accept=".csv,.xls,.xlsx"
          >
            <el-button size="small" type="primary" @click="downloadTemplate('TermDepositAccountNumber.xlsx')">
              {{ $t('soi.termDepositCreation.downloadAccountTemplate') }}
            </el-button>
            <el-button size="small" type="primary">{{ $t('soi.termDepositCreation.upload') }}</el-button>
            <span slot="tip" class="el-upload__tip">{{ $t('soi.termDepositCreation.uploadFileRule') }}</span>
          </el-upload>
        </el-form-item>
        <el-form-item :label="$t('soi.termDepositCreation.channel')" prop="channelID">
          <el-select v-model="termDepositForm.channelID" style="width:100%;">
            <el-option v-for="item in channelList" :key="item.id" :value="item.id" :label="item.name" />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('soi.termDepositCreation.transactionDetails')">
          <el-row
            v-for="(data,index) in termDepositForm.transToList"
            :key="index"
            :gutter="20"
            :style="{ margin: index > 0 ? '20px 0 0 0' : '0' }"
          >
            <el-col :span="7">
              <el-form-item
                :label="$t('soi.termDepositCreation.period')+(index+1)"
                :prop="'transToList.' + index + '.termPeriod'"
                :rules="{required: true, message: 'Please select a period', trigger: 'change'}"
                label-width="170px"
              >
                <el-select v-model="data.termPeriod" style="width:100%;">
                  <el-option v-for="item in termDepositPeriodList" :key="item" :value="item" :label="item" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="9">
              <el-form-item
                :prop="'transToList.' + index + '.amountFr'"
                :rules="{required: true, validator: validateLimitValue, trigger: 'blur'}"
                :label="$t('soi.termDepositCreation.amountFrom')"
                label-width="205px"
              >
                <el-input-number
                  v-model="data.amountFr"
                  style="margin:0; width: 100%"
                  :min="0"
                  :step="1000"
                  step-strictly
                />
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item
                :prop="'transToList.' + index + '.amountTo'"
                :rules="[
                  {required: true, validator: validateLimitValue, trigger: 'blur'},
                  {required: true, validator: (rule, value, callback) => validateAmount(rule, value, callback, data.amountFr,'transactionDateFrom','amountTo'), trigger: 'blur'}
                ]"
                :label="$t('soi.termDepositCreation.amountTo')"
                label-width="40px"
              >
                <el-input-number
                  v-model="data.amountTo"
                  style="margin:0; width: 100%"
                  :min="1000"
                  :step="1000"
                  step-strictly
                />
              </el-form-item>
            </el-col>
            <el-col :span="2">
              <el-button
                v-if="index === 0"
                type="primary"
                @click="addTermDepositFormCondition()"
              >{{
                $t('soi.common.add')
              }}
              </el-button>
              <el-button
                v-if="index > 0"
                type="primary"
                @click="handleDeleteTermDepositForm(index)"
              >{{ $t('soi.common.delete') }}
              </el-button>
            </el-col>
          </el-row>
        </el-form-item>
        <el-form-item :label="$t('soi.termDepositCreation.transactionDate')">
          <el-col :span="11">
            <el-form-item
              :label="$t('soi.termDepositCreation.transactionDateFrom')"
              prop="transactionFromDate"
              label-width="60px"
            >
              <el-date-picker
                v-model="termDepositForm.transactionFromDate"
                type="datetime"
                value-format="timestamp"
                style="width: 60%;"
                :placeholder="$t('soi.termDepositCreation.transactionDateFromPlaceholder')"
                :picker-options="pickerOptionsStart"
              />
            </el-form-item>
          </el-col>
          <el-col :span="2" class="line" style="text-align: center;">-</el-col>
          <el-col :span="11">
            <el-form-item
              :label="$t('soi.termDepositCreation.transactionDateTo')"
              prop="transactionToDate"
              label-width="60px"
            >
              <el-date-picker
                v-model="termDepositForm.transactionToDate"
                type="datetime"
                value-format="timestamp"
                style="width: 60%;"
                :placeholder="$t('soi.termDepositCreation.transactionDateToPlaceholder')"
                :picker-options="pickerOptionsEnd"
              />
            </el-form-item>
          </el-col>
        </el-form-item>
        <el-form-item :label="$t('soi.termDepositCreation.frequency')" prop="frequency">
          <el-select
            v-model="termDepositForm.frequency"
            style="width:100%;"
            :placeholder="$t('soi.termDepositCreation.frequencyPlaceholder')"
          >
            <el-option v-for="item in frequencyList" :key="item" :value="item" :label="item" />
          </el-select>
        </el-form-item>
        <el-form-item :label="$t('soi.termDepositCreation.frequencyRate')" prop="frequencyRate">
          <el-input
            v-model="termDepositForm.frequencyRate"
            :placeholder="$t('soi.termDepositCreation.frequencyRatePlaceholder')"
          />
        </el-form-item>
        <el-form-item :label="$t('soi.termDepositCreation.totalTransaction')" prop="total">
          <el-input-number
            v-model="termDepositForm.total"
            :max="1000"
            :min="1"
            style="width: 100%;"
            :placeholder="$t('soi.termDepositCreation.totalTransactionPlaceholder')"
          />
        </el-form-item>
        <el-form-item label="Term Table: " prop="termTable" hidden="hidden">
          <el-select
            v-model="termDepositForm.termTable"
            filterable
            allow-create
            style="width:100%;"
            default-first-option
            placeholder=""
          >
            <el-option
              v-for="item in termTableNameList"
              :key="item"
              :label="item"
              :value="item"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="Transfer Table: " prop="transferTable" hidden="hidden">
          <el-select
            v-model="termDepositForm.transferTable"
            filterable
            allow-create
            style="width:100%;"
            default-first-option
            placeholder=""
          >
            <el-option
              v-for="item in transferTableNameList"
              :key="item"
              :label="item"
              :value="item"
            />
          </el-select>
        </el-form-item>
        <div style="text-align:center;">
          <el-button type="primary" @click="submitForm()">{{ $t('soi.common.submit') }}</el-button>
          <el-button @click="resetForm()">{{ $t('soi.common.reset') }}</el-button>
        </div>
      </el-form>
    </customize-card>
  </div>
</template>
<script>
import CustomizeCard from '@/components/CustomizeCard/index.vue'
import { DATA_CREATION_API_URL } from '@/contains'
import {
  getAllFrequencyList,
  getChannelList,
  getTableList,
  getTermDepositPeriodPeriod,
  termDepositGenerate
} from '@/api/practice/data-creation'
import { downloadTemplate } from '@/api/practice/document'
import { mapGetters } from 'vuex'

export default {
  name: 'TermDepositDataCreation',
  components: { CustomizeCard },
  data() {
    const validateLimitValue = (rule, value, callback) => {
      let a
      a = a = /^[0-9]\d*$/g

      if (!a.test(value)) {
        callback(new Error('Invalid number'))
      } else {
        callback()
      }
    }
    const startDate = new Date()
    startDate.setHours(0, 0, 0, 0) // 设置时、分、秒、毫秒为 0
    const endDate = new Date()
    endDate.setHours(23, 59, 59, 0) // 设置时、分、秒、毫秒为 0
    return {
      validateLimitValue: validateLimitValue,
      loading: true,
      dataHost: DATA_CREATION_API_URL,
      termDepositFormSelectType: '1',
      termDepositFormFileList: [],
      termDepositPeriodList: [],
      frequencyList: [],
      channelList: [],
      transferTableNameList: [],
      termTableNameList: [],
      termDepositForm: {
        dataSource: '1',
        branchFr: '',
        branchFrLink: '',
        transToList: [
          {
            termPeriod: '1week',
            settleAccType: 'Saving',
            amountFr: '1000',
            amountTo: '2000'
          }
        ],
        transactionFromDate: startDate.getTime(),
        transactionToDate: endDate.getTime(),
        total: '1000',
        channelID: 1,
        frequency: 'Weekly',
        frequencyRate: '2',
        termTable: 'termdeposit_transaction',
        transferTable: 'transfer_transaction',
        userId: ''
      },
      termDepositFormRules: {
        branchFr: [{
          required: true,
          message: this.$t('soi.termDepositCreation.transactionAccountPlaceholder'),
          trigger: 'blur'
        }],
        transactionFromDate: [{
          required: true,
          message: this.$t('soi.termDepositCreation.transactionDateFromPlaceholder'),
          trigger: 'change'
        }],
        transactionToDate: [{
          required: true,
          message: this.$t('soi.termDepositCreation.transactionDateToPlaceholder'),
          trigger: 'change'
        }],
        frequency: [{
          required: true,
          message: this.$t('soi.termDepositCreation.frequencyPlaceholder'),
          trigger: 'change'
        }],
        frequencyRate: [{
          required: true,
          message: this.$t('soi.termDepositCreation.frequencyRatePlaceholder'),
          trigger: 'blur'
        }],
        total: [{
          required: true,
          message: this.$t('soi.termDepositCreation.totalTransactionPlaceholder'),
          trigger: 'blur'
        }],
        termTable: [{ required: true, message: 'Please input the term table name.', trigger: 'change' }],
        transferTable: [{ required: true, message: 'Please input the tansfer table name.', trigger: 'change' }]
      },
      pickerOptionsStart: {
        disabledDate: time => {
          const beginDateVal = this.termDepositForm.transactionToDate
          if (beginDateVal) {
            return (
              time.getTime() > new Date(beginDateVal).getTime() - 24 * 60 * 60 * 1000
            )
          }
        }
      },
      pickerOptionsEnd: {
        disabledDate: time => {
          const beginDateVal = this.termDepositForm.transactionFromDate
          if (beginDateVal) {
            return (
              time.getTime() < new Date(beginDateVal).getTime() + 24 * 60 * 60 * 1000
            )
          }
        }
      }
    }
  },
  computed: {
    ...mapGetters(['userDetails'])
  },
  watch: {
    'termDepositFormSelectType'(newValue) {
      if (newValue === '2') {
        this.$refs['termDepositForm'].clearValidate(['branchFr'])
      }
    }
  },
  mounted() {
    this.initTableName()
    this.initSelectOptions()
    this.loading = false
  },
  methods: {
    initSelectOptions() {
      const _this = this
      getAllFrequencyList()
        .then(function(res) {
          _this.frequencyList = res.data
        })
        .catch(() => {
          _this.frequencyList = []
        })
      getChannelList()
        .then(function(res) {
          _this.channelList = res.data
        })
        .catch(() => {
          _this.channelList = []
        })
      getTermDepositPeriodPeriod()
        .then(function(res) {
          _this.termDepositPeriodList = res.data
        })
        .catch(() => {
          _this.termDepositPeriodList = []
        })
    },
    initTableName() {
      const _this = this
      getTableList({ type: 'TermDeposit' })
        .then(function(res) {
          _this.termTableNameList = res.data
        })
        .catch(() => {
          _this.termTableNameList = []
        })
      getTableList({ type: 'Transfer' })
        .then(function(res) {
          _this.transferTableNameList = res.data
        })
        .catch(() => {
          _this.transferTableNameList = []
        })
    },
    handleExceed(files, fileList) {
      this.$message.warning(this.$t('soi.termDepositCreation.handleExceedMessage', {
        thisTimeChoose: files.length,
        totallyChoose: files.length + fileList.length
      }))
    },
    beforeRemove(file, fileList) {
      return this.$confirm(this.$t('soi.termDepositCreation.beforeRemoveMessage', { fileName: file.name }))
    },
    exportData(file, fileList) {
      if (!file) {
        return
      }
      const isLt2M = file.size / 1024 / 1024 < 5
      if (!isLt2M) {
        this.$message.error(this.$t('soi.termDepositCreation.uploadFileRule'))
        fileList = []
        return isLt2M
      }
    },
    termDepositFormFileListSuccess(res, file, fileList) {
      if (res.code !== 20000) {
        this.$alert(res.message)
        this.termDepositFormFileList = []
        return
      }
      this.termDepositForm.branchFrLink = res.data
    },
    addTermDepositFormCondition() {
      const _this = this
      _this.termDepositForm.transToList.push(
        {
          termPeriod: '1week',
          settleAccType: 'Saving',
          amountFr: '100',
          amountTo: '1000'
        }
      )
    },
    handleDeleteTermDepositForm(index) {
      this.termDepositForm.transToList.splice(index, 1)
    },
    submitForm() {
      this.$refs['termDepositForm'].validate((valid) => {
        const _this = this
        if (valid) {
          _this.loading = true
          _this.termDepositFormSelectType === '1' ? _this.termDepositForm.branchFrLink = '' : _this.termDepositForm.branchFr = ''
          _this.termDepositForm.userId = _this.userDetails.id
          termDepositGenerate(_this.termDepositForm)
            .then(function(res) {
              _this.$alert(res.message)
            })
            .finally(() => {
              _this.loading = false
            })
        } else {
          return false
        }
      })
    },
    resetForm() {
      const startDate = new Date()
      startDate.setHours(0, 0, 0, 0) // 设置时、分、秒、毫秒为 0
      const endDate = new Date()
      endDate.setHours(23, 59, 59, 0) // 设置时、分、秒、毫秒为 0
      this.termDepositForm = {
        dataSource: '1',
        branchFr: '',
        branchFrLink: '',
        transToList: [
          {
            termPeriod: '1week',
            settleAccType: 'Saving',
            amountFr: '',
            amountTo: ''
          }
        ],
        transactionFromDate: startDate.getTime(),
        transactionToDate: endDate.getTime(),
        total: '1000',
        channelID: 1,
        frequency: 'Weekly',
        frequencyRate: '',
        termTable: 'termdeposit_transaction',
        transferTable: 'transfer_transaction',
        userId: ''
      }
    },
    downloadTemplate,
    validateAmount(rule, value, callback, amountFrValue, amountFr, amountTo) {
      if (value === '' || value === null) {
        callback(new Error(this.$t('soi.common.invalidNumber')))
      } else if (Number(value) < Number(amountFrValue)) {
        callback(new Error(
          this.$t(
            'soi.termDepositCreation.numberGreaterThanPlaceholder',
            {
              form: this.$t(`soi.termDepositCreation.${amountFr}`),
              to: this.$t(`soi.termDepositCreation.${amountTo}`)
            }
          )
        ))
      } else {
        callback()
      }
    }
  }
}
</script>
<style scoped>
.maintenance-page {
  text-align: center;
  padding: 20px;
  color: #333;
}
</style>
