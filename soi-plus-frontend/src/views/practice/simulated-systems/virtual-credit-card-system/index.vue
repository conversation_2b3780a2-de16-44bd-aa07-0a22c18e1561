<template>
  <div v-loading.fullscreen.lock="loading" class="simulated-systems-virtual-credit-card-system-container">
    <customize-card :title="$t('soi.router.virtualBankSystem')">
      <h4 style="margin-bottom:10px;">{{ $t("soi.simulatedSystems.selectVcsCustomerTip") }}</h4>

      <el-row :gutter="40">
        <el-col :span="24">
          <p class="subtitle">{{ $t("soi.simulatedSystems.hkVcsAccount") }}</p>
          <el-collapse v-model="activeNames">
            <el-collapse-item :title="$t('soi.simulatedSystems.staffList')" name="1">
              <el-table :data="staffTokenOptions" :header-cell-style="{'background': '#109eae42', 'color': '#333'}">
                <el-table-column type="index" width="50" label="#" />
                <el-table-column prop="customerNumber" :label="$t('soi.simulatedSystems.loginName')" />
                <el-table-column prop="loginName" :label="$t('soi.simulatedSystems.username')" />
                <el-table-column prop="role" :label="$t('soi.simulatedSystems.role')" />
                <el-table-column prop="loginPwd" :label="$t('soi.simulatedSystems.loginPassword')" />
                <el-table-column :label="$t('soi.common.operate')" width="100px">
                  <template slot-scope="scope">
                    <el-button type="text" @click="saveCustomerProfile(scope.row)">{{ $t('soi.simulatedSystems.login') }}</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-collapse-item>
            <el-collapse-item name="2">
              <el-table :data="tokenOptions" :header-cell-style="{'background': '#109eae42', 'color': '#333'}">
                <el-table-column type="index" width="50" label="#" />
                <el-table-column prop="customerNumber" :label="$t('soi.simulatedSystems.loginName')" />
                <el-table-column prop="loginName" :label="$t('soi.simulatedSystems.username')" />
                <el-table-column prop="loginPwd" :label="$t('soi.simulatedSystems.loginPassword')" />
                <el-table-column :label="$t('soi.common.operate')" width="100px">
                  <template slot-scope="scope">
                    <el-button type="text" @click="saveCustomerProfile(scope.row)">{{ $t('soi.simulatedSystems.login') }}</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-collapse-item>
          </el-collapse>
        </el-col>
      </el-row>
    </customize-card>
  </div>
</template>
<script>
import CustomizeCard from '@/components/CustomizeCard/index.vue'
import { getLbsStaff, getTokenInfo } from '@/api/practice/sandbox'
import { mapGetters } from 'vuex'
import { VIRTUAL_CREDIT_CARD_SYSTEM_URL } from '@/contains'

export default {
  name: 'SimulatedSystemsVirtualBankSystem',
  components: { CustomizeCard },
  data() {
    return {
      loading: false,
      temporaryPermission: process.env.VUE_APP_TEMPORARY_PERMISSION,
      activeNames: ['1', '2'],
      staffTokenOptions: [],
      tokenOptions: []
    }
  },
  computed: {
    ...mapGetters([
      'userDetails',
      'roles',
      'language'
    ])
  },
  mounted() {
    this.initData()
  },
  methods: {
    initData() {
      const vue = this
      vue.loading = true

      getTokenInfo({ userId: this.userDetails.id })
        .then((res) => {
          if (res.data.length > 0) {
            vue.tokenOptions = res.data
          }
          return getLbsStaff({ userId: this.userDetails.id })
        })
        .then((res) => {
          vue.staffTokenOptions = []
          if (res.data.length > 0) {
            res.data.filter((item) => {
              return ['CreditOfficer', 'Manager', 'OperatingOfficer', 'MarketingOfficer'].includes(item.role)
            })
              .forEach(item => {
                vue.staffTokenOptions.push({
                  customerNumber: item.staffNumber,
                  loginName: item.userName,
                  loginPwd: item.pwd,
                  role: item.role
                })
              })
          }
        })
        .finally(() => {
          vue.loading = false
        })
    },
    saveCustomerProfile(row) {
      window.open(VIRTUAL_CREDIT_CARD_SYSTEM_URL +
        '?customerNumber=' + row.customerNumber +
        '&loginPassword=' + row.loginPwd +
        '&loginName=' + row.loginName,
      '_blank',
      ''
      )
    }
  }
}
</script>

<style lang="scss" scoped>
.simulated-systems-virtual-credit-card-system-container {
  .subtitle {
    font-size: 14px;
  }

  .doc {
    color: #109eae;
    border-color: #109eae;
    margin-left: 10px;
  }
}

</style>
