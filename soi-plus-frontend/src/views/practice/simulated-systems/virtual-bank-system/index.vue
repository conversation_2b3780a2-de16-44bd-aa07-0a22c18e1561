<template>
  <div v-loading.fullscreen.lock="loading" class="simulated-systems-virtual-bank-system-container">
    <customize-card :title="$t('soi.router.virtualBankSystem')">
      <h4 style="margin-bottom:10px;">{{ $t("soi.simulatedSystems.selectVbsCustomerTip") }}</h4>

      <el-row v-if="!roles.includes('administrator')" :gutter="40">
        <el-col :span="temporaryPermission !== 'cuhk' && temporaryPermission !== 'hsu' ? 12 : 24">
          <p class="subtitle">{{ $t("soi.simulatedSystems.hkVbsAccount") }}</p>
          <el-collapse v-model="activeNames">
            <el-collapse-item :title="$t('soi.simulatedSystems.staffList')" name="1">
              <el-table :data="staffTokenOptions" :style="{width: temporaryPermission !== 'cuhk' && temporaryPermission !== 'hsu' ? '700px' : '100%'}" :header-cell-style="{'background': '#109eae42', 'color': '#333'}">
                <el-table-column type="index" width="50" label="#" />
                <el-table-column prop="customerNumber" :label="$t('soi.simulatedSystems.loginName')" />
                <el-table-column prop="loginName" :label="$t('soi.simulatedSystems.username')" />
                <el-table-column prop="role" :label="$t('soi.simulatedSystems.role')" />
                <el-table-column prop="loginPwd" :label="$t('soi.simulatedSystems.loginPassword')" />
                <el-table-column :label="$t('soi.common.operate')" width="100px">
                  <template slot-scope="scope">
                    <el-button type="text" @click="saveCustomerProfile(scope.row)">{{ $t('soi.simulatedSystems.login') }}</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-collapse-item>
            <el-collapse-item name="2">
              <template slot="title">
                <span>{{ $t('soi.simulatedSystems.customerList') }}</span>
                <el-button v-if="$route.meta.customerRoleDocument" size="mini" plain class="doc" @click.prevent.stop="openVBSCustomerDocument($route.meta.customerRoleDocument)">{{ $t('soi.common.customerRoleHelpDocument') }}</el-button>
              </template>
              <el-table :data="tokenOptions" :style="{width: temporaryPermission !== 'cuhk' && temporaryPermission !== 'hsu' ? '700px' : '100%'}" :header-cell-style="{'background': '#109eae42', 'color': '#333'}">
                <el-table-column type="index" width="50" label="#" />
                <el-table-column prop="customerNumber" :label="$t('soi.simulatedSystems.loginName')" />
                <el-table-column prop="loginName" :label="$t('soi.simulatedSystems.username')" />
                <el-table-column prop="loginPwd" :label="$t('soi.simulatedSystems.loginPassword')" />
                <el-table-column :label="$t('soi.common.operate')" width="100px">
                  <template slot-scope="scope">
                    <el-button type="text" @click="saveCustomerProfile(scope.row)">{{ $t('soi.simulatedSystems.login') }}</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-collapse-item>
          </el-collapse>
        </el-col>
        <el-col v-if="temporaryPermission !== 'cuhk' && temporaryPermission !== 'hsu'" :span="12">
          <p class="subtitle">{{ $t("soi.simulatedSystems.cnVbsAccount") }}</p>
          <el-collapse v-model="activeNamesCN">
            <el-collapse-item :title="$t('soi.simulatedSystems.customerList')" name="1">
              <el-table :data="tokenOptionsCN" style="width:700px" :header-cell-style="{'background': '#109eae42', 'color': '#333'}">
                <el-table-column type="index" width="50" label="#" />
                <el-table-column prop="customerNumber" :label="$t('soi.simulatedSystems.loginName')" />
                <el-table-column prop="loginPwd" :label="$t('soi.simulatedSystems.loginPassword')" />
                <el-table-column prop="loginName" :label="$t('soi.simulatedSystems.username')" />
                <el-table-column :label="$t('soi.common.operate')" width="100px">
                  <template slot-scope="scope">
                    <el-button type="text" @click="saveCustomerProfileCN(scope.row)">{{ $t('soi.simulatedSystems.login') }}</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-collapse-item>
          </el-collapse>
        </el-col>
      </el-row>

      <el-row v-else :gutter="40">
        <el-col :span="12">
          <p class="subtitle">{{ $t("soi.simulatedSystems.hkVbsAccount") }}</p>
          <el-collapse v-model="activeNames">
            <el-collapse-item :title="$t('soi.simulatedSystems.systemOperatorList')" name="1">
              <el-table :data="systemOperatorOptions" style="width:700px" :header-cell-style="{'background': '#109eae42', 'color': '#333'}">
                <el-table-column type="index" width="50" label="#" />
                <el-table-column prop="customerNumber" :label="$t('soi.simulatedSystems.loginName')" />
                <el-table-column prop="loginPwd" :label="$t('soi.simulatedSystems.loginPassword')" />
                <el-table-column prop="loginName" :label="$t('soi.simulatedSystems.username')" />
                <el-table-column :label="$t('soi.common.operate')" width="100px">
                  <template slot-scope="scope">
                    <el-button type="text" @click="saveCustomerProfile(scope.row)">{{ $t('soi.simulatedSystems.login') }}</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-collapse-item>
            <el-collapse-item :title="$t('soi.simulatedSystems.systemManagerList')" name="2">
              <el-table :data="systemManagerOptions" style="width:700px" :header-cell-style="{'background': '#109eae42', 'color': '#333'}">
                <el-table-column type="index" width="50" label="#" />
                <el-table-column prop="customerNumber" :label="$t('soi.simulatedSystems.loginName')" />
                <el-table-column prop="loginPwd" :label="$t('soi.simulatedSystems.loginPassword')" />
                <el-table-column prop="loginName" :label="$t('soi.simulatedSystems.username')" />
                <el-table-column :label="$t('soi.common.operate')" width="100px">
                  <template slot-scope="scope">
                    <el-button type="text" @click="saveCustomerProfile(scope.row)">{{ $t('soi.simulatedSystems.login') }}</el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-collapse-item>
          </el-collapse>
        </el-col>
      </el-row>
    </customize-card>
  </div>
</template>
<script>
import CustomizeCard from '@/components/CustomizeCard/index.vue'
import { getLbsStaff, getTokenInfo } from '@/api/practice/sandbox'
import { mapGetters } from 'vuex'
import { CN_VBS_URL, HK_VBS_URL } from '@/contains'

export default {
  name: 'SimulatedSystemsVirtualBankSystem',
  components: { CustomizeCard },
  data() {
    return {
      loading: false,
      temporaryPermission: process.env.VUE_APP_TEMPORARY_PERMISSION,
      activeNames: ['1', '2'],
      activeNamesCN: ['1'],
      staffTokenOptions: [],
      tokenOptions: [],
      systemOperatorOptions: [],
      systemManagerOptions: [],
      tokenOptionsCN: [
        { customerNumber: '************', loginPwd: '123456', loginName: '320103198408196460' },
        { customerNumber: '************', loginPwd: '123456', loginName: '150423198501231088' },
        { customerNumber: '************', loginPwd: '123456', loginName: '632623198205078595' },
        { customerNumber: '************', loginPwd: '123456', loginName: '310113199403255871' },
        { customerNumber: '************', loginPwd: '123456', loginName: '230705197302104594' }
      ]
    }
  },
  computed: {
    ...mapGetters([
      'userDetails',
      'roles',
      'language'
    ])
  },
  mounted() {
    this.initData()
  },
  methods: {
    initData() {
      const vue = this
      vue.loading = true

      getTokenInfo({ userId: this.userDetails.id })
        .then((res) => {
          if (res.data.length > 0) {
            vue.tokenOptions = res.data
          }
          return getLbsStaff({ userId: this.userDetails.id })
        })
        .then((res) => {
          vue.staffTokenOptions = []
          if (res.data.length > 0) {
            res.data.forEach(item => {
              vue.staffTokenOptions.push({
                customerNumber: item.staffNumber,
                loginName: item.userName,
                loginPwd: item.pwd,
                role: item.role
              })
            })
            res.data.filter(item => item.role === 'System Operator').forEach(item => {
              vue.systemOperatorOptions.push({
                customerNumber: item.staffNumber,
                loginName: item.userName,
                loginPwd: item.pwd
              })
            })
            res.data.filter(item => item.role === 'System Manager').forEach(item => {
              vue.systemManagerOptions.push({
                customerNumber: item.staffNumber,
                loginName: item.userName,
                loginPwd: item.pwd
              })
            })
          }
        })
        .finally(() => {
          vue.loading = false
        })
    },
    saveCustomerProfile(row) {
      window.open(HK_VBS_URL +
        '?customerNumber=' + row.customerNumber +
        '&loginPassword=' + row.loginPwd +
        '&loginName=' + row.loginName,
      '_blank',
      ''
      )
    },
    saveCustomerProfileCN(row) {
      window.open(CN_VBS_URL +
        '?customerNumber=' + row.customerNumber +
        '&loginPassword=' + row.loginPwd +
        '&loginName=' + row.loginName,
      '_blank',
      ''
      )
    },
    openVBSCustomerDocument(document) {
      window.open(document[this.language], '_blank')
    }
  }
}
</script>

<style lang="scss" scoped>
.simulated-systems-virtual-bank-system-container {
  .subtitle {
    font-size: 14px;
  }

  .doc {
    color: #109eae;
    border-color: #109eae;
    margin-left: 10px;
  }
}

</style>
