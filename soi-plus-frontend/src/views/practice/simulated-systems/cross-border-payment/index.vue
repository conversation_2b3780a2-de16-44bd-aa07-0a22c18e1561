<template>
  <div class="simulated-systems-cross-border-payment-container">
    <customize-card :title="$t('soi.router.crossBorderPayment')">
      <h4 style="margin-bottom:10px;">{{ $t("soi.simulatedSystems.selectVbsCustomerTip") }}</h4>

      <p class="subtitle">{{ $t("soi.simulatedSystems.cnVbsAccount") }}</p>
      <el-collapse v-model="activeNamesCN">
        <el-collapse-item :title="$t('soi.simulatedSystems.customerList')" name="1">
          <el-table :data="tokenOptionsCN" :header-cell-style="{'background': '#109eae42', 'color': '#333'}">
            <el-table-column type="index" width="50" label="#" />
            <el-table-column prop="customerNumber" :label="$t('soi.simulatedSystems.loginName')" />
            <el-table-column prop="loginPwd" :label="$t('soi.simulatedSystems.loginPassword')" />
            <el-table-column prop="loginName" :label="$t('soi.simulatedSystems.username')" />
            <el-table-column :label="$t('soi.common.operate')" width="100px">
              <template slot-scope="scope">
                <el-button type="text" @click="saveCustomerProfileCN(scope.row)">{{ $t('soi.simulatedSystems.login') }}</el-button>
              </template>
            </el-table-column>
          </el-table>
        </el-collapse-item>
      </el-collapse>
    </customize-card>
  </div>
</template>
<script>
import CustomizeCard from '@/components/CustomizeCard/index.vue'
import { CN_VBS_URL } from '@/contains'

export default {
  name: 'SimulatedSystemsCrossBorderPayment',
  components: { CustomizeCard },
  data() {
    return {
      loading: false,
      tokenOptionsCN: [
        { customerNumber: '101000531507', loginPwd: '123456', loginName: '320103198408196460' },
        { customerNumber: '101000531508', loginPwd: '123456', loginName: '150423198501231088' },
        { customerNumber: '101000531509', loginPwd: '123456', loginName: '632623198205078595' },
        { customerNumber: '102000531510', loginPwd: '123456', loginName: '310113199403255871' },
        { customerNumber: '103000531513', loginPwd: '123456', loginName: '230705197302104594' }
      ],
      activeNamesCN: ['1']
    }
  },
  methods: {
    saveCustomerProfileCN(row) {
      window.open(CN_VBS_URL +
        '?customerNumber=' + row.customerNumber +
        '&loginPassword=' + row.loginPwd +
        '&loginName=' + row.loginName,
      '_blank',
      ''
      )
    }
  }
}
</script>
