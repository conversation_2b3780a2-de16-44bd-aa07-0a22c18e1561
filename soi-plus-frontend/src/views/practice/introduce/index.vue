<template>
  <div class="app-container customize-card-container platform-introduction-container">
    <div class="header-actions">
      <el-button size="mini" @click="$router.go(-1)">{{ $t('soi.common.back') }}</el-button>
    </div>

    <el-row type="flex" justify="center">
      <el-col :span="20">
        <h1 class="api-title">{{ $t('soi.practicalPlatformIntroduction.title') }}</h1>
        <h4 class="tip-item">{{ $t('soi.practicalPlatformIntroduction.description1') }}</h4>
        <h4 class="tip-item">{{ $t('soi.practicalPlatformIntroduction.description2') }}</h4>
        <h2 class="subtitle">{{ $t('soi.practicalPlatformIntroduction.subtitle1') }}</h2>
        <img width="100%" :src="require(`@/assets/images/api-market-place-${language}.png`)" alt="">
        <h4 class="tip-item">{{ $t('soi.practicalPlatformIntroduction.description3') }}</h4>
        <h2 class="subtitle">{{ $t('soi.practicalPlatformIntroduction.subtitle2') }}</h2>
        <img width="100%" :src="require(`@/assets/images/synthetic-financial-data-sandbox-${language}.png`)" alt="">
        <h4 class="tip-item">{{ $t('soi.practicalPlatformIntroduction.description4') }}</h4>
        <h2 class="subtitle">{{ $t('soi.practicalPlatformIntroduction.subtitle3') }}</h2>
        <img width="100%" :src="require(`@/assets/images/data-analysis-${language}.png`)" alt="">
        <h4 class="tip-item">{{ $t('soi.practicalPlatformIntroduction.description5') }}</h4>
        <h2 class="subtitle">{{ $t('soi.practicalPlatformIntroduction.subtitle4') }}</h2>
        <img width="100%" :src="require(`@/assets/images/simulated-systems-${language}.png`)" alt="">
        <h4 class="tip-item">{{ $t('soi.practicalPlatformIntroduction.description6') }}</h4>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { mapGetters } from 'vuex'

export default {
  name: 'PracticePlatformIntroduce',
  data() {
    return {}
  },
  computed: {
    ...mapGetters([
      'language'
    ])
  },
  methods: {
  }
}
</script>

<style lang="scss" scoped>
.platform-introduction-container {
  .header-actions {
    text-align: right;
  }

  .api-title {
    margin: 30px 0;
    text-align: center;
  }

  .subtitle {
    margin: 60px 0;
    text-align: center;
  }

  .tip-item {
    margin: 10px 0 ;
    line-height: 2;
    font-size: 16px;
    font-weight: 400;
  }

  .image-item {
    margin: 60px auto;
    text-align: center;
  }
}

</style>
