<template>
  <div v-loading.fullscreen.lock="loading" class="technological-development-coding-practice-category-container">
    <customize-card :title="$t('soi.router.codingPractice')">
      <el-row :gutter="40" class="panel-group">
        <el-col v-for="item in primaryCategory" :key="item.id" :span="8" :xs="24">
          <div class="card-panel subject-list" @click="handlerClickCategory(item.id)">
            <div class="card-panel-icon-wrapper icon-people">
              <svg-icon icon-class="document" class-name="card-panel-icon" />
            </div>
            <div class="card-panel-description">
              <div class="card-panel-text">{{ ['zh', 'cht'].includes(language) ? item.name : item.nameEn }}</div>
            </div>
          </div>
        </el-col>
      </el-row>
    </customize-card>
  </div>
</template>

<script>
import CustomizeCard from '@/components/CustomizeCard/index.vue'
import { mapGetters } from 'vuex'
import { getCategory } from '@/api/practice/coding-practice'

export default {
  name: 'CodingPracticeCategory',
  components: { CustomizeCard },
  data() {
    return {
      loading: false,
      primaryCategory: []
    }
  },
  computed: {
    ...mapGetters([
      'language'
    ])
  },
  mounted() {
    this.initPrimaryCategory()
  },
  methods: {
    handlerClickCategory(categoryId) {
      this.$router.push({ name: 'technological-development-coding-practice-subcategory', params: { categoryId }})
    },
    initPrimaryCategory() {
      this.loading = true
      getCategory()
        .then(response => {
          this.primaryCategory = response.data
        })
        .finally(() => {
          this.loading = false
        })
    }
  }
}
</script>

<style lang="scss" scoped>
.panel-group {
  .card-panel-col {
    margin-bottom: 32px;
  }

  .card-panel {
    height: 108px;
    cursor: pointer;
    font-size: 12px;
    position: relative;
    color: #666;
    background: #fff;
    box-shadow: 4px 4px 40px rgba(0, 0, 0, 0.05);
    border-color: rgba(0, 0, 0, 0.05);

    &:hover {
      .card-panel-icon-wrapper {
        color: #fff;
      }

      .icon-people {
        background: #40c9c6;
      }

      .icon-message {
        background: #36a3f7;
      }

      .icon-money {
        background: #f4516c;
      }

      .icon-shopping {
        background: #34bfa3;
      }
    }

    .icon-people {
      color: #40c9c6;
    }

    .icon-message {
      color: #36a3f7;
    }

    .icon-money {
      color: #f4516c;
    }

    .icon-shopping {
      color: #34bfa3;
    }

    .card-panel-icon-wrapper {
      float: left;
      padding: 16px;
      transition: all 0.38s ease-out;
      border-radius: 6px;
    }

    .card-panel-icon {
      float: left;
      font-size: 48px;
    }

    .card-panel-description {
      font-weight: bold;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;

      .card-panel-text {
        line-height: 108px;
        color: rgba(0, 0, 0, 0.45);
        font-size: 16px;
        margin-left: 20px;
        word-break: break-all;
      }

      .card-panel-num {
        font-size: 20px;
      }
    }
  }
}
.subject-list {
  display: flex;
  align-items: center;
  margin: 10px 0;
  .card-panel-icon-wrapper {
    margin: 14px;
  }

  .card-panel-text {
    line-height: inherit;
    white-space: break-spaces;
  }
}
</style>
