<template>
  <div v-loading.fullscreen.lock="loading" class="technological-development-api-apply-customer-data-token-container">
    <customize-card :title="$t('soi.router.applyCustomerDataToken')">
      <template #header-actions>
        <el-button v-if="maxSandboxNumber > 0 && !processing" size="mini" @click="dialogFormVisible = true">
          {{ $t('soi.applyCustomerDataToken.applyNewToken') }}
        </el-button>
        <el-button v-if="processing" size="mini">
          {{ $t('soi.applyCustomerDataToken.checkStatus') }}
        </el-button>
      </template>

      <el-alert v-if="processing" :title="$t('soi.applyCustomerDataToken.waitingCreationCompleteTip')" type="success" effect="dark" />

      <el-table
        :data="sandboxTableData"
        tooltip-effect="dark"
        stripe
        highlight-current-row
        :header-cell-style="{'background': '#109eae42', 'color': '#333'}"
      >
        <el-table-column type="index" width="50" label="#" />
        <el-table-column prop="customerNumber" :label="$t('soi.applyCustomerDataToken.customerNumber')" width="200" />
        <el-table-column prop="loginName" :label="$t('soi.applyCustomerDataToken.loginName')" width="200" />
        <el-table-column prop="loginPwd" :label="$t('soi.applyCustomerDataToken.loginPassword')" width="200" />
        <el-table-column prop="userToken" :label="$t('soi.common.token')" />
      </el-table>
    </customize-card>

    <el-dialog
      width="600px"
      :title="$t('soi.applyCustomerDataToken.applyNewToken')"
      :visible.sync="dialogFormVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <el-form ref="applyTokenForm" :model="applyTokenForm" :rules="applyTokenFormRules" label-width="150px">
        <el-form-item :label="$t('soi.applyCustomerDataToken.number')" prop="number">
          <el-input-number
            v-model="applyTokenForm.number"
            type="number"
            :min="1"
            :max="maxSandboxNumber"
            step-strictly
            :step="1"
            :autofocus="true"
            autocomplete="off"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item>{{ $t('soi.applyCustomerDataToken.maxSandboxNumber', { max: maxSandboxNumber }) }}</el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button size="small" @click="dialogFormVisible = false">{{ $t('soi.common.cancel') }}</el-button>
        <el-button size="small" type="primary" @click="applyToken()">{{ $t('soi.common.confirm') }}</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import CustomizeCard from '@/components/CustomizeCard/index.vue'
import { createTokenInfo, getTaskStatus, getTokenInfo } from '@/api/practice/sandbox'
import { mapGetters } from 'vuex'

export default {
  name: 'ApiApplyCustomerDataToken',
  components: { CustomizeCard },
  data() {
    const validateNumber = (rule, value, callback) => {
      if (value.length <= 0 || value > this.maxSandboxNumber) {
        callback(
          new Error(this.$t('soi.applyCustomerDataToken.correctNumberRange', { max: this.maxSandboxNumber }))
        )
      } else {
        callback()
      }
    }

    return {
      loading: false,
      maxSandboxNumber: 10,
      processing: false,
      dialogFormVisible: false,
      sandboxTableData: [],
      applyTokenForm: {
        number: ''
      },
      applyTokenFormRules: {
        number: { required: true, message: this.$t('soi.applyCustomerDataToken.correctNumberApplications'), trigger: 'blur', validator: validateNumber }
      }
    }
  },
  computed: {
    ...mapGetters([
      'userDetails'
    ])
  },
  mounted() {
    this.sandboxId = this.$route.query.sandboxId
    this.initData()
  },
  methods: {
    initData() {
      const vue = this
      vue.loading = true
      getTokenInfo({ userId: this.userDetails.id })
        .then(res => {
          if (res.data.length > 0) {
            vue.sandboxTableData = res.data
            if (res.data[0].sandBoxId) {
              vue.sandboxId = res.data[0].sandBoxId
            }
            vue.maxSandboxNumber = 10 - res.data.length
            vue.applyTokenForm.number = ''
          }
        })
        .catch((error) => {
          if (error.code) {
            getTaskStatus({ userId: this.userDetails.id })
              .then(res => {
                if (res.data === 'processing') {
                  // 分配Sandbox Data未完成
                  vue.processing = true
                  vue.$alert(vue.$t('soi.applyCustomerDataToken.waitingCreationCompleteTip'), vue.$t('soi.common.tip'))
                } else if (res.data === 'error') {
                  // 分配Sandbox Data失败
                  vue.processing = false
                  vue.$alert(vue.$t('soi.applyCustomerDataToken.applySandboxTokenFailedTip'), vue.$t('soi.common.tip'))
                } else if (res.data === 'completed') {
                  // 分配Sandbox Data已完成
                  vue.processing = false
                } else {
                  // 没有等待分配的Sandbox Data
                  vue.processing = false
                }
              })
              .finally(() => {
                vue.loading = false
              })
          }
        })
        .finally(() => {
          vue.loading = false
        })
    },
    applyToken() {
      const vue = this
      this.$refs.applyTokenForm.validate((valid) => {
        if (valid) {
          vue.loading = true
          const requestData = {
            userId: vue.userDetails.id,
            email: vue.userDetails.email,
            developername: vue.userDetails.email,
            createSize: vue.applyTokenForm.number,
            sandboxid: vue.sandboxId
          }
          createTokenInfo(requestData)
            .then(res => {
              if (res.data.taskStatus === 'error') {
                vue.$alert(res.data.taskLog, vue.$t('soi.common.tip'))
              } else if (res.data.taskStatus === 'completed') {
                vue.$alert(vue.$t('soi.applyCustomerDataToken.applySuccessTip'), vue.$t('soi.common.tip'))
                vue.dialogFormVisible = false
                vue.initData()
              } else if (res.data.taskStatus === 'processing') {
                vue.processing = true
                vue.dialogFormVisible = false
                vue.$alert(vue.$t('soi.applyCustomerDataToken.applySubmitSuccessTip'), vue.$t('soi.common.tip'))
              } else {
                vue.$alert(vue.$t('soi.applyCustomerDataToken.applyFailedTip'), vue.$t('soi.common.tip'))
              }
            })
            .finally(() => {
              vue.loading = false
            })
        }
      })
    }
  }
}
</script>
