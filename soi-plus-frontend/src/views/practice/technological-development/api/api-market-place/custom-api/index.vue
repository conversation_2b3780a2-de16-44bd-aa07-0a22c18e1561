<template>
  <div v-loading.fullscreen.lock="loading" class="technological-development-api-api-market-place-custom-api-container">
    <customize-card :title="$t('soi.router.customApi')">
      <div v-if="isStudent">
        <h3>{{ $t("soi.apiMarketPlace.customApiSelf") }}</h3>
        <el-table
          :data="tableData.slice((currentPage - 1) * pageSize, currentPage * pageSize)"
          style="width: 100%"
          stripe
          highlight-current-row
          :header-cell-style="{'background': '#109eae42', 'color': '#333'}"
        >
          <el-table-column type="index" label="#" width="50" />
          <el-table-column prop="apiName" :label="$t('soi.apiMarketPlace.apiName')" min-width="200" />
          <el-table-column prop="description" :label="$t('soi.common.description')" min-width="200" />
          <el-table-column :label="$t('soi.common.operate')" width="200">
            <template slot-scope="scope">
              <el-button size="mini" @click="handleDeleteApi(scope.row)">{{ $t("soi.common.delete") }}</el-button>
              <el-button size="mini" @click="handleTryoutApi(scope.row)">{{ $t("soi.apiMarketPlace.tryoutApi") }}</el-button>
            </template>
          </el-table-column>
        </el-table>
        <div class="table-footer">
          <el-pagination
            :current-page="currentPage"
            :page-sizes="[5, 10, 20, 40]"
            :page-size="pageSize"
            :total="tableData.length"
            layout="total, prev, pager, next"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>

        <h3>{{ $t("soi.apiMarketPlace.customApiFromOthers") }}</h3>
      </div>
      <el-table
        :data="tableData2.slice((currentPage2 - 1) * pageSize2, currentPage2 * pageSize2)"
        style="width: 100%"
        stripe
        highlight-current-row
        :header-cell-style="{'background': '#109eae42', 'color': '#333'}"
      >
        <el-table-column type="index" label="#" width="50" />
        <el-table-column prop="apiName" :label="$t('soi.apiMarketPlace.apiName')" min-width="200" />
        <el-table-column prop="description" :label="$t('soi.common.description')" min-width="200" />
        <el-table-column :label="$t('soi.common.operate')" width="120">
          <template slot-scope="scope">
            <el-button size="mini" @click="handleTryoutApi(scope.row)">{{ $t("soi.apiMarketPlace.tryoutApi") }}</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="table-footer">
        <el-pagination
          :current-page="currentPage2"
          :page-sizes="[5, 10, 20, 40]"
          :page-size="pageSize2"
          :total="tableData2.length"
          layout="total, prev, pager, next"
          @size-change="handleSizeChange2"
          @current-change="handleCurrentChange2"
        />
      </div>
    </customize-card>
  </div>
</template>
<script>
import CustomizeCard from '@/components/CustomizeCard/index.vue'
import { mapGetters } from 'vuex'
import { deleteApi, getStudent } from '@/api/practice/api-market'

export default {
  name: '',
  components: { CustomizeCard },
  data() {
    return {
      loading: false,
      tableData: [],
      tableData2: [],
      currentPage: 1,
      currentPage2: 1,
      pageSize: 10,
      pageSize2: 10
    }
  },
  computed: {
    ...mapGetters([
      'userDetails'
    ]),
    isStudent() {
      return this.userDetails.roles.includes('Student')
    }
  },
  mounted() {
    this.getStudentApi()
  },
  methods: {
    getStudentApi() {
      const vue = this
      vue.loading = true
      const classId = '10000'

      getStudent({ apiClassId: classId, userId: this.userDetails.id })
        .then((res) => {
          vue.tableData = []
          vue.tableData2 = []
          if (res.data) {
            const list = res.data.reverse(); var obj = {}
            for (let i = 0; i < list.length; i++) {
              if (list[i].apiInfo.classifyName === '10000') {
                if (vue.userDetails.id === list[i].apiInfo.userId) {
                  obj = {}
                  obj.apiName = list[i].apiInfo.apiName
                  obj.apiNameEn = list[i].apiInfo.apiNameEn
                  obj.apiId = list[i].apiInfo.apiId
                  obj.targetUrl = list[i].apiInfo.targetUrl
                  obj.description = list[i].apiInfo.description
                  obj.createDate = list[i].apiInfo.createDate
                  obj.id = list[i].apiInfo.id
                  obj.courseID = list[i].apiInfo.courseID
                  obj.classifyName = list[i].apiInfo.classifyName
                  obj.policyId = list[i].apiInfo.policyId
                  obj.fiName = list[i].apiInfo.fiName
                  vue.tableData.push(obj)
                } else {
                  obj = {}
                  obj.apiName = list[i].apiInfo.apiName
                  obj.apiId = list[i].apiInfo.apiId
                  obj.apiNameEn = list[i].apiInfo.apiNameEn
                  obj.targetUrl = list[i].apiInfo.targetUrl
                  obj.description = list[i].apiInfo.description
                  obj.createDate = list[i].apiInfo.createDate
                  obj.id = list[i].apiInfo.id
                  obj.courseID = list[i].apiInfo.courseID
                  obj.classifyName = list[i].apiInfo.classifyName
                  obj.policyId = list[i].apiInfo.policyId
                  obj.fiName = list[i].apiInfo.fiName
                  vue.tableData2.push(obj)
                }
              }
            }
            if (vue.tableData.length <= vue.pageSize) {
              vue.currentPage = 1
            }
          }
        }).finally(() => {
          vue.loading = false
        })
    },
    handleSizeChange(pageSize) {
      this.pageSize = pageSize
    },
    handleCurrentChange(currentPage) {
      this.currentPage = currentPage
    },
    handleSizeChange2(pageSize) {
      this.pageSize2 = pageSize
    },
    handleCurrentChange2(currentPage) {
      this.currentPage2 = currentPage
    },
    handleDeleteApi(row) {
      const vue = this
      vue.$confirm(vue.$t('soi.apiMarketPlace.deleteApiTip'), vue.$t('soi.common.tip'), {
        confirmButtonText: vue.$t('soi.common.confirm'),
        cancelButtonText: vue.$t('soi.common.cancel'),
        type: 'warning'
      }).then(() => {
        vue.loading = true
        deleteApi({ apiIds: [row.apiId] })
          .then(() => {
            vue.getStudentApi()

            this.$message.success(vue.$t('soi.apiMarketPlace.deleteSuccess'))
          }).finally(() => {
            vue.loading = false
          })
      }).catch(() => {})
    },
    handleTryoutApi(row) {
      console.log(row)
      localStorage.setItem('api-info', JSON.stringify(row))
      this.$router.push({
        name: 'technological-development-api-api-market-place-tryout-api',
        params: { apiId: row.apiId }
      })
    }
  }
}
</script>
