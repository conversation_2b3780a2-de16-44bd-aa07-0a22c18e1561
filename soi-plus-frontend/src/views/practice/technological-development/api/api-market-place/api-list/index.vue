<template>
  <div v-loading.fullscreen.lock="loading" class="technological-development-api-api-market-place-api-list-container">
    <customize-card :title="`${$t('soi.router.apiList')} - ${['zh', 'cht'].includes(language) ? apiCatalogue.className : apiCatalogue.classNameEn}`">
      <el-row :gutter="40">
        <el-col :span="8">
          <el-row class="panel-group">
            <el-col v-for="(item, index) in listBusiness" :key="index" :span="24">
              <el-tooltip :content="['zh', 'cht'].includes(language) ? item.details : item.detailsEn" class="item" effect="dark" placement="top-start">
                <div style="cursor: pointer;" @click="handleSetLineChartData(item)">
                  <el-card :style="`border: ${item.id === selected ? '1px solid #000' : '1px solid #ccc'}`" shadow="never" class="card-list">
                    <el-row type="flex" align="middle">
                      <el-col :span="6" class="card-img">
                        <img :src="item.image" alt="">
                      </el-col>
                      <el-col :span="16" :offset="2">
                        <h2>{{ ['zh', 'cht'].includes(language) ? item.classSubName : item.classSubNameEn }}</h2>
                        <p class="api-desc">{{ ['zh', 'cht'].includes(language) ? item.details : item.detailsEn }}</p>
                      </el-col>
                    </el-row>
                  </el-card>
                </div>
              </el-tooltip>
            </el-col>
          </el-row>
        </el-col>
        <el-col :span="16">
          <el-card shadow="never">
            <el-table
              :data="list"
              :show-header="false"
              tooltip-effect="dark"
            >
              <el-table-column :label="$t('soi.apiMarketPlace.apiName')" prop="apiName" align="left">
                <template slot-scope="scope">
                  <div style="display: flex; align-items: center">
                    <img
                      :src="scope.row.imageUrl"
                      style="width: 77px; height: 86px; margin-right: 20px"
                      alt=""
                    >
                    <div>
                      <h4>{{ ['zh', 'cht'].includes(language) ? scope.row.apiName : scope.row.apiNameEn }}</h4>
                      <p class="api-desc">{{ ['zh', 'cht'].includes(language) ? scope.row.description : scope.row.descriptionEn }}</p>
                    </div>
                  </div>
                </template>
              </el-table-column>
              <el-table-column>
                <template slot-scope="scope">
                  <p v-for="(item, index) in scope.row.endpoints" :key="index">
                    <el-link type="primary" @click="viewSwaggerPage(item)">{{ item }}</el-link>
                  </p>
                </template>
              </el-table-column>
              <el-table-column :label="$t('soi.common.operate')" align="center" width="150">
                <template slot-scope="scope">
                  <el-button
                    size="mini"
                    type="primary"
                    @click="handleClick(scope.row)"
                  >{{ $t('soi.apiMarketPlace.tryoutApi') }}</el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-card>
        </el-col>
      </el-row>
    </customize-card>
  </div>
</template>

<script>
import CustomizeCard from '@/components/CustomizeCard/index.vue'
import { mapGetters } from 'vuex'
import { SOI_PLUS_BUSINESS_API_URL } from '@/contains'
import { getApiList, getSubclassList, getSwagger } from '@/api/practice/api-market'

export default {
  name: 'ApiMarketPlaceApiList',
  components: { CustomizeCard },
  data() {
    return {
      loading: false,
      originalSwagger: '',
      originalSwaggerCn: '',
      originalSwaggerCht: '',
      apiCatalogue: {},
      listBusiness: [],
      selected: '',
      list: [],
      requestCount: 0
    }
  },
  computed: {
    ...mapGetters([
      'userDetails',
      'language'
    ])
  },
  created() {
    this.apiCatalogue = JSON.parse(localStorage.getItem('api-catalogue-info'))

    // 查询子分类列表
    this.handlerQuerySubcategory()
    // 获取对应的模块的原始swagger文件
    this.queryOriginalSwagger()
  },
  methods: {
    startLoading() {
      this.requestCount++
      this.loading = true
    },
    endLoading() {
      this.requestCount--
      if (this.requestCount === 0) {
        setTimeout(() => {
          this.loading = false
        }, 100)
      }
    },
    viewSwaggerPage(url) {
      console.log(url)
      let apiInfo = this.originalSwagger.paths[url].post ? this.originalSwagger.paths[url].post : this.originalSwagger.paths[url].get
      if (['zh'].includes(this.language)) apiInfo = this.originalSwaggerCn.paths[url].post ? this.originalSwaggerCn.paths[url].post : this.originalSwaggerCn.paths[url].get
      if (['cht'].includes(this.language)) apiInfo = this.originalSwaggerCht.paths[url].post ? this.originalSwaggerCht.paths[url].post : this.originalSwaggerCn.paths[url].get
      window.open(`/swagger-ui/index.html?name=${this.name}&lang=${this.language}#/${this.name}/${apiInfo.tags[0]}/${apiInfo.operationId}`)
    },
    queryOriginalSwagger() {
      let className = ''
      let fileName = ''
      switch (this.apiCatalogue.classNameEn) {
        case 'Deposit':
          fileName = 'Deposit Service.json'
          className = 'Deposit-Service'
          break
        case 'CreditCard':
          fileName = 'Credit Card Service.json'
          className = 'Creditcard-Service'
          break
        case 'Fund':
          fileName = 'Fund Service.json'
          className = 'Fund-Service'
          break
        case 'Foreign Exchange':
          fileName = 'Foreign Exchange Service.json'
          className = 'Foreign-Exchange-Service'
          break
        case 'Payment':
          fileName = 'Payment Service.json'
          className = 'Payment-Service'
          break
        case 'Bank Side Insurance':
          fileName = 'Travel Insurance Service.json'
          className = 'Bank-Side-Insurance-Service'
          break
        case 'E-wallet':
          fileName = 'E-wallet Service.json'
          className = 'E-Wallet-Service'
          break
        case 'Mortgage Loan':
          fileName = 'Mortgage Loan Service.json'
          className = 'Mortgage-Loan-Service'
          break
        case 'System Service':
          fileName = 'Sysadmin Service.json'
          className = 'Sysadmin-Service'
          break
        case 'Stock':
          fileName = 'Stock service.json'
          className = 'Stock-Service'
          break
        case 'Term Deposit':
          fileName = 'Term Deposit Service.json'
          className = 'Term-Deposit-Service'
          break
        case 'Personal Loans':
          fileName = 'Personal Loan service.json'
          className = 'Personal-Loans-Service'
          break
        case 'Bonds':
          fileName = 'Bonds Service.json'
          className = 'Bonds-Service'
          break
        case 'Domestic insurance business':
          fileName = 'Domestic_Insurance.json'
          className = 'Domestic-Insurance'
          break
        case 'E-wallet Service (Bank Side)':
          fileName = 'e-wallet Bank Side.json'
          className = 'E-wallet-Service (Bank Side)'
      }
      this.name = className
      // 获取原始的Swagger文件内容
      fetch(`/swagger-ui/src/v2/${fileName}`, { method: 'GET' })
        .then(response => {
          response.text().then(data => {
            this.originalSwagger = JSON.parse(data)
          })
        })
        .catch(e => console.log(e))
      fetch(`/swagger-ui/src/v2_cn/${fileName}`, { method: 'GET' })
        .then(response => {
          response.text().then(data => {
            this.originalSwaggerCn = JSON.parse(data)
          })
        })
        .catch(e => console.log(e))
      fetch(`/swagger-ui/src/v2_cht/${fileName}`, { method: 'GET' })
        .then(response => {
          response.text().then(data => {
            this.originalSwaggerCht = JSON.parse(data)
          })
        })
        .catch(e => console.log(e))
    },
    handleClick(row) {
      localStorage.setItem('api-info', JSON.stringify(row))
      this.$router.push({
        name: 'technological-development-api-api-market-place-tryout-api',
        params: { apiId: row.apiId }
      })
    },
    handlerQuerySubcategory() {
      const _this = this
      _this.startLoading()
      const time = new Date()

      getSubclassList({ apiClassId: _this.apiCatalogue.id })
        .then(response => {
          const data = response.data
          data.map(item => {
            item.image = `${SOI_PLUS_BUSINESS_API_URL}/v1/practical/api-market/subclass/image?apiSubClassId=${item.id}&v=${time.getTime()}`
          })
          _this.listBusiness = data
          this.handleSetLineChartData(_this.listBusiness[0])
        }).finally(() => {
          _this.endLoading()
        })
    },
    handleSetLineChartData(row) {
      if (this.selected === row.id) return

      this.selected = row.id
      const vue = this
      vue.startLoading()
      getApiList({ apiClassId: this.apiCatalogue.id, userId: this.userDetails.id, apiSubClassId: row.id, self: 0 })
        .then(async(res) => {
          const data = []
          const list = res.data
          let obj = {}
          for (let i = 0; i < list.length; i++) {
            obj = {}
            obj.apiName = list[i].apiInfo.apiName
            obj.apiNameEn = list[i].apiInfo.apiNameEn
            obj.listenPath = list[i].apiInfo.listenPath
            obj.access = list[i].access
            obj.apiId = list[i].apiInfo.apiId
            obj.policyID = list[i].apiInfo.policyId
            obj.description = list[i].apiInfo.description
            obj.descriptionEn = list[i].apiInfo.descriptionEn
            obj.classifyName = list[i].apiInfo.classifyName
            obj.fiName = list[i].apiInfo.fiName
            obj.imageUrl = list[i].apiInfo.imageUrl
            // obj.endpoints = list[i].apiInfo.endpoints
            const { data: apiInfo } = await getSwagger({ apiId: obj.apiId })
            obj.endpoints = Object.keys(JSON.parse(apiInfo.en).paths)
            data.push(obj)
          }
          vue.list = data
        })
        .finally(() => {
          vue.endLoading()
        })
    }
  }
}
</script>

<style lang="scss" scoped>
.technological-development-api-api-market-place-api-list-container {
  .header-action {
    float: right;
    .header-btn {
      float: right;
      margin-right: 10px;
    }
  }

  .panel-group {
    .card-panel-col {
      margin-bottom: 32px;
    }
    .card-panel {
      height: 108px;
      cursor: pointer;
      font-size: 12px;
      position: relative;
      overflow: hidden;
      color: #666;
      background: #fff;
      box-shadow: 4px 4px 40px rgba(0, 0, 0, 0.05);
      border-color: rgba(0, 0, 0, 0.05);
      &:hover {
        .card-panel-icon-wrapper {
          color: #fff;
        }
        .icon-people {
          background: #40c9c6;
        }
        .icon-message {
          background: #36a3f7;
        }
        .icon-money {
          background: #f4516c;
        }
        .icon-shopping {
          background: #34bfa3;
        }
      }
      .icon-people {
        color: #40c9c6;
      }
      .icon-message {
        color: #36a3f7;
      }
      .icon-money {
        color: #f4516c;
      }
      .icon-shopping {
        color: #34bfa3;
      }
      .card-panel-icon-wrapper {
        float: left;
        margin: 14px 0 0 14px;
        padding: 16px;
        transition: all 0.38s ease-out;
        border-radius: 6px;
      }
      .card-panel-icon {
        float: left;
        font-size: 48px;
      }
      .card-panel-description {
        float: left;
        font-weight: bold;
        .card-panel-text {
          line-height: 108px;
          color: rgba(0, 0, 0, 0.45);
          font-size: 16px;
          margin-left: 20px;
          word-break: break-all;
        }
        .card-panel-num {
          font-size: 20px;
        }
      }
    }
  }
  .card-list {
    position: relative;
    margin-bottom: 20px;
    .card-img {
      text-align: center;
      img {
        width: 80%;
        height: auto;
        vertical-align: middle;
      }
    }
    h2 {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      line-height: 1.5;
      margin-top: 0;
    }
    p {
      font-size: 14px;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      line-clamp: 2;
      -webkit-box-orient: vertical;
    }
    .card-icon {
      position: absolute;
      top: 10px;
      right: 20px;
      cursor: pointer;
      i {
        margin-left: 10px;
      }
    }
  }
  .card-add {
    cursor: pointer;
    text-align: center;
    line-height: 170px;
    font-size: 60px;
    vertical-align: middle;
    color: #999;
  }

  .api-desc {
    word-break: normal;
    overflow-wrap: break-word;
  }
}
</style>
