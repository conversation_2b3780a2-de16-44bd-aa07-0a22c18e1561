<template>
  <div class="technological-development-api-sandbox-usage-guide-container">
    <customize-card :title="$t('soi.router.sandboxUsageGuide')">
      <el-row type="flex" justify="center">
        <el-col>
          <h1 class="api-title">{{ $t('soi.router.sandboxUsageGuide') }}</h1>
          <h4 class="tip-item">{{ $t('soi.sandboxUsageGuide.description1') }}</h4>
          <h4 class="tip-item">{{ $t('soi.sandboxUsageGuide.description2') }}</h4>
          <h4 class="tip-item">{{ $t('soi.sandboxUsageGuide.description3') }}</h4>
          <img :src="require(`@/assets/images/sandbox-${language}.png`)" width="100%" alt="connectivity">
        </el-col>
      </el-row>
    </customize-card>
  </div>
</template>

<script>
import CustomizeCard from '@/components/CustomizeCard/index.vue'
import { mapGetters } from 'vuex'

export default {
  name: 'ApiSandboxUsageGuide',
  components: { CustomizeCard },
  data() {
    return {
    }
  },
  computed: {
    ...mapGetters([
      'language'
    ])
  }
}
</script>

<style lang="scss" scoped>
.technological-development-api-sandbox-usage-guide-container {
  .api-item {
    height: 160px;
    display: flex;
    font-size: 20px;
    justify-content: center;
    align-items: center;
  }

  .api-title {
    margin: 30px 0;
  }

  .tip-item {
    margin: 20px 0 ;
    line-height: 2;
    font-weight: 400;
  }
  .image-item {
    display: flex;
    align-items: center;
  }
}
</style>
