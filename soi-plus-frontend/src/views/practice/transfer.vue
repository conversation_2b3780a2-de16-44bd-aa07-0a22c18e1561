<template>
  <div class="practice-transfer-container">
    <h2 class="title">
      {{
        title.reduce((previousValue, currentValue) => {
          return previousValue + ' - ' + this.$t(`soi.router.${currentValue}`)
        }, this.$t('soi.common.practicePlatform'))
      }}
    </h2>

    <div class="back-container">
      <el-button size="small" @click="back()">{{ $t('soi.common.back') }}</el-button>
    </div>
    <transition name="fade-transform" mode="out-in">
      <card-menu :key="stepRouter.length" :routes="!stepRouter.length ? routes : stepRouter[stepRouter.length - 1].children" @click-link="clickLinkHandler($event)" @click-group="clickGroupHandler($event)" />
    </transition>
  </div>
</template>

<script>
import CardMenu from '@/views/practice/components/CardMenu.vue'
import { isExternal } from '@/utils/validate'
import { mapGetters } from 'vuex'
import { getAccessToken } from '@/utils/auth'

export default {
  name: 'PracticeTransfer',
  components: { CardMenu },
  data() {
    const cacheJSonStr = sessionStorage.getItem('practice-cache')
    let cache = {}
    if (cacheJSonStr) {
      cache = JSON.parse(cacheJSonStr)
    }
    this.onlyOneChild = null
    return Object.assign({
      stepRouter: []
    }, cache)
  },
  computed: {
    ...mapGetters([
      'permissions'
    ]),
    routes() {
      const _this = this
      const systemRouters = this.$router.options.routes.filter(route => route.meta && route.meta.module === 'practice')
      const routers = JSON.parse(JSON.stringify(systemRouters))
      function checkAndHideRouters(routers) {
        routers.forEach(router => {
          if (router.meta && router.meta.source && !_this.permissions.includes(router.meta.source)) {
            router.hidden = true
          }

          if (router.children && Array.isArray(router.children)) {
            checkAndHideRouters(router.children)
          }
        })
      }

      checkAndHideRouters(routers)
      return routers
    },
    title() {
      return this.stepRouter.map(item => item.meta.title)
    }
  },
  watch: {
    '$data': {
      handler(newValue) {
        sessionStorage.setItem('practice-cache', JSON.stringify(newValue))
      },
      deep: true
    }
  },
  created() {
    const target = this.$route.query.target
    if (target && target.length) {
      this.stepRouter = []
      const nodes = target.split(',').map(item => item.trim())
      const cache = []
      let patentRoute = this.routes
      nodes.forEach(item => {
        const r = patentRoute.find(route => route.path === item)
        if (r && r.children && r.children.length && !(this.hasOneShowingChild(r.children, r) && (!this.onlyOneChild.children || this.onlyOneChild.noShowingChildren) && !r.alwaysShow)) {
          cache.push(r)
          patentRoute = r.children
        } else {
          this.$router.replace('/404')
        }
      })
      this.stepRouter = [...cache]
      this.$router.replace('/practice/transfer')
    }
  },
  methods: {
    hasOneShowingChild(children = [], parent) {
      const showingChildren = children.filter(item => {
        if (item.hidden) {
          return false
        } else {
          // Temp set(will be used if only has one showing child)
          this.onlyOneChild = item
          return true
        }
      })

      // When there is only one child router, the child router is displayed by default
      if (showingChildren.length === 1) {
        return true
      }

      // Show parent if there are no child router to display
      if (showingChildren.length === 0) {
        this.onlyOneChild = { ...parent, path: '', noShowingChildren: true }
        return true
      }

      return false
    },
    clickLinkHandler(value) {
      if (isExternal(value)) {
        window.open(value.replace('%token%', encodeURIComponent(getAccessToken())), '_blank')
      } else {
        this.$router.push({ name: value })
      }
    },
    clickGroupHandler(route) {
      this.stepRouter.push(route)
    },
    back() {
      if (!this.stepRouter.length) {
        this.$router.go(-1)
      } else {
        this.stepRouter.pop()
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.title {
  text-align: center;
  padding-top: 30px;
  font-size: 30px;
  font-weight: 500;
  margin-bottom: 60px;
}

.back-container {
  text-align: right;
  margin-bottom: 10px;
}
</style>
