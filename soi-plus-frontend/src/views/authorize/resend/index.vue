<template>
  <div class="resend-email-container">
    <el-form ref="resendEmailForm" :model="resendEmailForm" :rules="resendEmailRules" class="resend-email-form" auto-complete="on" label-position="left">
      <div class="title-container">
        <h3 class="title">{{ $t('soi.authorize.login') }}</h3>
      </div>

      <div class="language-select">
        <language />
      </div>

      <el-form-item prop="email" class="form-item">
        <span class="svg-container">
          <svg-icon icon-class="user" />
        </span>
        <el-input
          ref="email"
          v-model="resendEmailForm.email"
          :placeholder="$t('soi.authorize.email')"
          name="email"
          type="text"
          tabindex="1"
          auto-complete="off"
        />
      </el-form-item>

      <el-button :loading="loading" type="primary" class="send-email-button" @click.native.prevent="handleResendEmail">
        {{ $t('soi.authorize.sendEmail') }}
      </el-button>

      <el-row>
        <el-col :span="12">
          <el-link :underline="false" type="primary " class="privacy-policy-link" @click="goLink('/authorize/privacy-policy')">{{ $t('soi.authorize.newAccountPleaseFirstRegister') }}</el-link>
        </el-col>
        <el-col :span="12" class="login-now">
          <el-link :underline="false" type="primary" class="login-now-link" @click="goLink('/authorize/login')">{{ $t('soi.authorize.loginNow') }}</el-link>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script>
import Language from '@/components/Language/index.vue'
import { emailValidator } from '@/utils/validate'
import { resendEmail } from '@/api/system/authorize'

export default {
  name: 'ResendEmail',
  components: { Language },
  data() {
    const validateEmail = (rule, value, callback) => {
      if (!emailValidator(value)) {
        callback(new Error(this.$t('soi.authorize.emailFormatError')))
      } else if (!value) {
        callback(new Error(this.$t('soi.authorize.invalidEmailFormat')))
      } else {
        callback()
      }
    }
    return {
      loading: false,
      resendEmailForm: {
        email: ''
      },
      resendEmailRules: {
        email: [{ required: true, trigger: 'blur', validator: validateEmail }]
      }
    }
  },
  mounted() {
    this.resendEmailForm.email = this.$route.query.email
  },
  methods: {
    goLink(path) {
      this.$router.push({ path: path })
    },
    handleResendEmail() {
      this.$refs.resendEmailForm.validate(valid => {
        if (valid) {
          this.loading = true
          resendEmail(this.resendEmailForm)
            .then(() => {
              this.$message.success(this.$t('soi.authorize.emailSendSuccess'))
              this.$router.push({ path: '/authorize/send-active-email-success' })
            })
            .finally(() => {
              this.loading = false
            })
        }
      })
    }
  }
}
</script>

<style lang="scss">
$bg: #fff;
$light_gray: #707070;
$cursor: #707070;

@supports (-webkit-mask: none) and (not (cater-color: $cursor)) {
  .resend-email-container .el-input input {
    color: $cursor;
  }
}

/* reset element-ui css */
.resend-email-container {
  .el-input {
    display: inline-block;
    height: 47px;
    width: 85%;

    input {
      background: transparent;
      border: 0;
      -webkit-appearance: none;
      border-radius: 0;
      padding: 12px 5px 12px 15px;
      color: $light_gray;
      height: 47px;
      caret-color: $cursor;

      &:-webkit-autofill {
        box-shadow: 0 0 0 1000px $bg inset !important;
        -webkit-text-fill-color: $cursor !important;
      }
    }
  }

  .el-form-item {
    border: 1px solid rgba(0, 0, 0, 0.3);
    background: $bg;
    border-radius: 5px;
    color: #707070;
  }
}
</style>

<style lang="scss" scoped>
$bg: #fff;
$dark_gray: #889aa4;
$light_gray: #333;

.resend-email-container {
  min-height: 100%;
  width: 100%;
  overflow: hidden;

  .resend-email-form {
    position: relative;
    width: 520px;
    max-width: 100%;
    padding: 0 35px 0;
    margin: 0 auto;
    overflow: hidden;
  }

  .form-item {
    margin-bottom: 30px;
  }

  .send-email-button {
    width: 100%;
    margin-bottom: 30px;
    height: 40px;
    margin-top: 10px;
  }
  .title-container {
    position: relative;
    .title {
      font-size: 26px;
      color: $light_gray;
      margin: 0 auto 10px auto;
      text-align: center;
      font-weight: bold;

      &::before,
      &::after {
        position: absolute;
        top: 46%;
        content: "";
        display: block;
        width: 100px;
        height: 1px;
        background: #333;
      }
      &::before {
        left: 0;
      }
      &::after {
        right: 0;
      }
    }
  }

  .language-select {
    margin-bottom: 10px;
    text-align: right;
  }

  .svg-container {
    padding: 3px 5px 6px 15px;
    color: #555;
    vertical-align: middle;
    width: 30px;
    display: inline-block;
  }

  .login-now {
    text-align: right;
  }

  .privacy-policy-link, .login-now-link {
    font-size: 16px;
  }
}
</style>
