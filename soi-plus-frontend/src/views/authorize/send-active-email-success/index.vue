<template>
  <div class="send-email-success-container">
    <div class="language-select">
      <language />
    </div>

    <el-result icon="success" :title="$t('soi.authorize.emailSendSuccess')" :sub-title="$t('soi.authorize.activateTip')">
      <template slot="extra">
        <el-button type="primary" size="mini" @click="$router.push({ path: '/authorize/login' })">{{ $t('soi.authorize.loginNow') }}</el-button>
      </template>
    </el-result>
  </div>
</template>

<script>
import Language from '@/components/Language/index.vue'

export default {
  name: 'SendActiveEmailSuccess',
  components: { Language },
  data() {
    return {}
  }
}
</script>

<style lang="scss" scoped>
.language-select {
  margin-bottom: 10px;
  text-align: right;
}
</style>
