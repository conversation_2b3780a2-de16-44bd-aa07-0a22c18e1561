<template>
  <div class="authorize-register-container">
    <el-form ref="userForm" :model="userForm" :rules="userRules" size="" label-position="top">
      <div class="title-container">
        <h3 class="title">{{ $t('soi.authorize.register') }}</h3>
      </div>

      <el-row :gutter="30">
        <el-col :span="12">
          <el-form-item :label="$t('soi.authorize.email')" prop="email">
            <el-input v-model="userForm.email" :placeholder="$t('soi.authorize.emailPlaceholder')" auto-complete="off" maxlength="128" autofocus clearable />
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item :label="$t('soi.authorize.nickname')" prop="nickname">
            <el-input v-model="userForm.nickname" :placeholder="$t('soi.authorize.nicknamePlaceholder')" auto-complete="off" maxlength="64" clearable />
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item :label="$t('soi.authorize.username')" prop="username">
            <el-input v-model="userForm.username" :placeholder="$t('soi.authorize.usernamePlaceholder')" auto-complete="off" maxlength="64" clearable />
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item :label="$t('soi.authorize.phoneNumber')" prop="phoneNumber">
            <el-input v-model="userForm.phoneNumber" :placeholder="$t('soi.authorize.phoneNumberPlaceholder')" auto-complete="off" maxlength="64" clearable />
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item :label="$t('soi.authorize.cardType')" prop="cardType">
            <el-select v-model="userForm.cardType" :placeholder="$t('soi.authorize.cardTypePlaceholder')" class="select-item">
              <el-option v-for="item in cardTypeOptions" :key="item.value" :label="$t(item.label)" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item :label="$t('soi.authorize.cardId')" prop="cardId">
            <el-input v-model="userForm.cardId" :placeholder="$t('soi.authorize.cardIdPlaceholder')" auto-complete="off" maxlength="64" clearable />
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item :label="$t('soi.authorize.password')" prop="password">
            <el-input v-model="userForm.password" :placeholder="$t('soi.authorize.passwordPlaceholder')" show-password auto-complete="off" maxlength="64" clearable />
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item :label="$t('soi.authorize.confirmPassword')" prop="confirmPassword">
            <el-input v-model="userForm.confirmPassword" :placeholder="$t('soi.authorize.confirmPasswordPlaceholder')" show-password auto-complete="off" maxlength="64" clearable />
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item :label="$t('soi.authorize.birthday')" prop="birthday">
            <el-date-picker
              v-model="userForm.birthday"
              :placeholder="$t('soi.authorize.birthdayPlaceholder')"
              type="date"
              value-format="yyyy-MM-dd"
              class="select-item"
            />
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item :label="$t('soi.authorize.sex')" prop="sex">
            <el-select v-model="userForm.sex" :placeholder="$t('soi.authorize.sexPlaceholder')" class="select-item">
              <el-option v-for="item in genderOptions" :key="item.value" :label="$t(item.label)" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item :label="$t('soi.authorize.graduateSchool')" prop="graduateSchool">
            <el-input v-model="userForm.graduateSchool" :placeholder="$t('soi.authorize.graduateSchoolPlaceholder')" auto-complete="off" maxlength="64" clearable />
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item :label="$t('soi.authorize.organization')" prop="organizationId">
            <el-select v-model="userForm.organizationId" :placeholder="$t('soi.authorize.organizationPlaceholder')" auto-complete="off" clearable class="select-item">
              <el-option v-for="item in organizationOptions" :key="item.organizationId" :label="item.organizationName" :value="item.organizationId" />
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item :label="$t('soi.authorize.studentOrStaffNumber')" prop="studentOrStaffNumber">
            <el-input v-model="userForm.studentOrStaffNumber" :placeholder="$t('soi.authorize.studentOrStaffNumberPlaceholder')" auto-complete="off" maxlength="64" clearable />
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item :label="$t('soi.authorize.department')" prop="department">
            <el-input v-model="userForm.department" :placeholder="$t('soi.authorize.departmentPlaceholder')" auto-complete="off" maxlength="64" clearable />
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item :label="$t('soi.authorize.jobTitle')" prop="jobTitle">
            <el-input v-model="userForm.jobTitle" :placeholder="$t('soi.authorize.jobTitlePlaceholder')" auto-complete="off" maxlength="64" clearable />
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item :label="$t('soi.authorize.experienceYear')" prop="experienceYear">
            <el-input-number
              v-model="userForm.experienceYear"
              :placeholder="$t('soi.authorize.experienceYearPlaceholder')"
              :controls="false"
              :min="1"
              :max="99"
              :step="1"
              step-strictly
              auto-complete="on"
              class="experience-year-input select-item"
            />
          </el-form-item>
        </el-col>

        <el-col :span="12">
          <el-form-item :label="$t('soi.authorize.level')" prop="level">
            <el-select v-model="userForm.level" :placeholder="$t('soi.authorize.levelPlaceholder')" maxlength="64" clearable class="select-item">
              <el-option v-for="item in userLevelOptions" :key="item.value" :label="$t(item.label)" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row style="text-align: center;">
        <el-button
          type="primary"
          style="width: 200px;"
          :loading="loading"
          @click.native.prevent="handleRegister"
        >
          {{ $t('soi.authorize.register') }}
        </el-button>
      </el-row>
    </el-form>
  </div>
</template>

<script>
import { register } from '@/api/system/authorize'
import { emailValidator, phoneNumberValidator, nicknameValidator, usernameValidator, passwordValidator, cardIdValidator } from '@/utils/validate'
import { cardTypeOptions, genderOptions, organizationOptions, userLevelOptions } from '@/data'

export default {
  name: 'Register',
  data() {
    const validateEmail = (rule, value, callback) => {
      if (!value) {
        callback()
      } else if (!emailValidator(value)) {
        callback(new Error(this.$t('soi.authorize.invalidEmailFormat')))
      } else {
        callback()
      }
    }

    const validatePhoneNumber = (rule, value, callback) => {
      if (!value) {
        callback()
      } else if (!phoneNumberValidator(value)) {
        callback(new Error(this.$t('soi.authorize.invalidPhoneNumberFormat')))
      } else {
        callback()
      }
    }

    const validateNickname = (rule, value, callback) => {
      if (!value) {
        callback()
      } else if (!nicknameValidator(value)) {
        callback(new Error(this.$t('soi.authorize.invalidNicknameFormat')))
      } else {
        callback()
      }
    }

    const validateUsername = (rule, value, callback) => {
      if (!value) {
        callback()
      } else if (!usernameValidator(value)) {
        callback(new Error(this.$t('soi.authorize.invalidUsernameFormat')))
      } else {
        callback()
      }
    }

    const validatePassword = (rule, value, callback) => {
      if (!value) {
        callback()
      } else if (!passwordValidator(value)) {
        callback(new Error(this.$t('soi.authorize.invalidPasswordFormat')))
      } else {
        callback()
      }
    }

    const validateConfirmPassword = (rule, value, callback) => {
      if (this.userForm.password !== this.userForm.confirmPassword) {
        callback(new Error(this.$t('soi.authorize.invalidConfirmPasswordFormat')))
      } else {
        callback()
      }
    }
    const validateCardId = (rule, value, callback) => {
      if (!value) {
        callback()
      } else if (!cardIdValidator(this.userForm.cardType, value)) {
        callback(new Error(this.$t('soi.authorize.invalidCardIdFormat')))
      } else {
        callback()
      }
    }
    return {
      loading: false,
      genderOptions,
      userLevelOptions,
      cardTypeOptions,
      organizationOptions: [],
      userForm: {
        email: '',
        username: '',
        organizationId: '',
        nickname: '',
        phoneNumber: '',
        studentOrStaffNumber: '',
        cardType: null,
        cardId: '',
        department: '',
        jobTitle: '',
        birthday: '',
        experienceYear: undefined,
        sex: null,
        level: '',
        graduateSchool: '',
        password: '',
        confirmPassword: ''
      },
      userRules: {
        email: [
          { required: true, message: this.$t('soi.authorize.emailIsRequired'), trigger: 'blur' },
          { validator: validateEmail, trigger: 'blur' }
        ],
        nickname: [
          { required: true, message: this.$t('soi.authorize.nicknameIsRequired'), trigger: 'blur' },
          { validator: validateNickname, trigger: 'blur' }
        ],
        username: [
          { required: true, message: this.$t('soi.authorize.usernameIsRequired'), trigger: 'blur' },
          { validator: validateUsername, trigger: 'blur' }
        ],
        phoneNumber: [
          { required: true, message: this.$t('soi.authorize.phoneNumberIsRequired'), trigger: 'blur' },
          { validator: validatePhoneNumber, trigger: 'blur' }
        ],
        cardType: [
          { required: true, message: this.$t('soi.authorize.cardTypeIsRequired'), trigger: 'change' }
        ],
        cardId: [
          { required: true, message: this.$t('soi.authorize.cardIdIsRequired'), trigger: 'blur' },
          { validator: validateCardId, trigger: 'blur' }
        ],
        level: [
          { required: true, message: this.$t('soi.user.levelIsRequired'), trigger: 'change' }
        ],
        password: [
          { required: true, message: this.$t('soi.authorize.passwordIsRequired'), trigger: 'blur' },
          { validator: validatePassword, trigger: 'blur' }
        ],
        confirmPassword: [
          { required: true, message: this.$t('soi.authorize.confirmPasswordIsRequired'), trigger: 'blur' },
          { validator: validateConfirmPassword, trigger: 'blur' }
        ]
      }
    }
  },
  async mounted() {
    window.localStorage.setItem('platform', this.$route.query.platform || '')

    this.organizationOptions = await organizationOptions()
  },
  methods: {
    handleRegister() {
      this.$refs.userForm.validate(valid => {
        if (valid) {
          this.loading = true
          register(this.userForm).then((res) => {
            this.$message({
              showClose: true,
              message: this.$t('soi.authorize.registerSuccess'),
              type: 'success'
            })
            this.$router.push({ path: '/authorize/send-active-email-success' })
          }).finally(() => {
            this.loading = false
          })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
$light_gray: #707070;
.authorize-register-container {
  .select-item {
    width: 100%;
  }
  .title-container {
    position: relative;
    width: 400px;
    margin: 0 auto 20px;
    .title {
      font-size: 26px;
      color: $light_gray;
      margin: 0 auto 10px auto;
      text-align: center;
      font-weight: bold;

      &::before,
      &::after {
        position: absolute;
        top: 46%;
        content: "";
        display: block;
        width: 100px;
        height: 1px;
        background: #333;
      }
      &::before {
        left: 0;
      }
      &::after {
        right: 0;
      }
    }
  }

  .language-select {
    margin-bottom: 10px;
    text-align: right;
  }
}
</style>

<style lang="scss">
.authorize-register-container {
  .experience-year-input {
    input {
      text-align: left;
    }
  }
}
</style>
