import Vue from 'vue'

import 'normalize.css/normalize.css' // A modern alternative to CSS resets
import ElementUI from 'element-ui'

import '@/styles/index.scss' // global css
import App from '@/App'
import store from '@/store'
import router from '@/router'
import i18n from '@/lang'

import Vue2OrgTree from 'vue2-org-tree'
import 'vue2-org-tree/dist/style.css'
import Vue2PerfectScrollbar from 'vue2-perfect-scrollbar'
import 'vue2-perfect-scrollbar/dist/vue2-perfect-scrollbar.css'
import echarts from 'echarts'
import moment from 'moment'
import '@/icons' // icon
import '@/permission' // permission control
import VMdPreview from '@kangc/v-md-editor/lib/preview'
import '@kangc/v-md-editor/lib/style/preview.css'
import githubTheme from '@kangc/v-md-editor/lib/theme/github.js'
import '@kangc/v-md-editor/lib/theme/style/github.css'

// highlightjs
import hljs from 'highlight.js'

Vue.use(Vue2OrgTree)

Vue.use(Vue2PerfectScrollbar)

VMdPreview.use(githubTheme, {
  Hljs: hljs
})

Vue.use(VMdPreview)

Vue.prototype.$echarts = echarts

Vue.prototype.$moment = moment
moment.locale('zh-cn')

Vue.use(ElementUI, {
  size: 'medium', // set element-ui default size
  i18n: (key, value) => i18n.t(key, value)
})

Vue.config.productionTip = false

new Vue({
  el: '#app',
  router,
  store,
  i18n,
  render: h => h(App)
})
