server:
  port: 8300
  deploy:
    url: https://simnectz.hsu.edu.hk/soi-plus-business/

spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ***************************************************************************************
    username: root
    password: SimnectzRDS@2020
    hikari:
      minimum-idle: 5
      maximum-pool-size: 15
      auto-commit: true
      idle-timeout: 30000
      max-lifetime: 1800000
      connection-timeout: 30000
      connection-test-query: SELECT 1
  redis:
    password: root
    database: 0
    port: 6379
    host: 127.0.0.1
  mail:
    host: smtp.exmail.qq.com
    protocol: smtp
    port: 465
    username: <EMAIL>
    password: Guangzhou2019
    properties:
      mail:
        smtp:
          socketFactory:
            port: 465
          ssl:
            enable: true
            socketFactory:
              fallback: false
              class: com.sun.mail.util.MailSSLSocketFactory
          auth: true
          starttls:
            enable: true
            required: true
  freemarker:
    cache: true

mybatis-flex:
  type-aliases-package: com.simnectz.**.entity
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  datasource:
    default:
      url: ***************************************************************************************
      username: root
      password: SimnectzRDS@2020
    deposit:
      url: ************************************************************************************************************************************************************************************************
      username: root
      password: SimnectzRDS@2020
    creditcard:
      url: ***************************************************************************************************************************************************************************************************
      username: root
      password: SimnectzRDS@2020
    foreign_exchange:
      url: *********************************************************************************************************************************************************************************************************
      username: root
      password: SimnectzRDS@2020
    fund:
      url: *********************************************************************************************************************************************************************************************
      username: root
      password: SimnectzRDS@2020
    insurance:
      url: **************************************************************************************************************************************************************************************************
      username: root
      password: SimnectzRDS@2020
    payment:
      url: ************************************************************************************************************************************************************************************************
      username: root
      password: SimnectzRDS@2020
    stock:
      url: **********************************************************************************************************************************************************************************************
      username: root
      password: SimnectzRDS@2020
    term_deposit:
      url: *****************************************************************************************************************************************************************************************************
      username: root
      password: SimnectzRDS@2020
    loan:
      url: *********************************************************************************************************************************************************************************************
      username: root
      password: SimnectzRDS@2020
    sysadmin:
      url: *************************************************************************************************************************************************************************************************
      username: root
      password: SimnectzRDS@2020

system:
  authorize:
    header: 'Authorization'
    prefix: 'Bearer '
    access-token-secret-key: a83f0e5e-55f5-c3f2-2bf4-c9eb442d2cec
    access-token-expire-time: 1440 # 1 day
    refresh-token-secret-key: 87a2e6c6-a656-9180-8799-73a133fa4609
    refresh-token-expire-time: 10080 # 7 days
    password-encode-secret-key: 8b70e850-7420-58b1-7a4d-ec1399a992ad
  email:
    from: ${spring.mail.username}
  upload:
    request-url: https://simnectz.hsu.edu.hk/document/soi-plus/
    storage-path: /home/<USER>/document/soi-plus
    sql-file-path: uploadSqlFile/
    table-info-file-path: uploadTableFile/

lbs:
  hackathon:
    sql:
      path: sql/

importdb:
  dbUrl: ****************************************************************************************************************

send:
  email:
    jump:
      url: http://localhost:9000/login?redirect=/WorkSheetManagement

#编码运行相关系统配置
compiler:
  java:
    path: /temp/java/
    file-name: Main
    jar-package-name: api-tools.jar
  python:
    path: /temp/python/
    file-name: main
  c:
    path: /temp/c/
    file-name: main
  cpp:
    path: /temp/cpp/
    file-name: main

tyk:
  policy:
    path: /opt/tyk-gateway/policies/policies.json
  reload:
    url: https://simnectz.hsu.edu.hk/gateway/tyk/reload
  host: https://simnectz.hsu.edu.hk/gateway/
  authorization: simnectz
  proxyHost: simnectz.hsu.edu.hk/gateway/

oss:
  accessKeyId: LTAI4G1KR2a93kf12q3G1upF
  accessKeySecret: ******************************
  endpoint: oss-cn-shenzhen.aliyuncs.com
  bucketName: simnectzplatform
  objectName: trainingPlatform/
license:
  enabled: true # Whether to enable license verification
  issued-time: 2025-03-14 00:00:00 # Authorization start time(yyyy-MM-dd HH:mm:ss)
  expiry-time: 2026-03-14 23:59:59 # Authorization end time(yyyy-MM-dd HH:mm:ss)
  ip-address: *********** # Authorization server ip address list (Leave it blank to not verify IP)
  mac-address: # Authorization server mac address list
    - 06:3f:bb:71:15:6c
    - 02:42:91:e7:c2:fa
    - 02:42:79:37:26:f7
    - 92:d2:8b:a0:7e:ec
    - ca:77:9b:7a:84:bc
    - 26:aa:c0:14:6e:15
    - 96:7d:d9:e6:2d:40
    - 4e:38:54:ca:34:6b
    - b6:15:62:45:12:75
    - f2:d1:28:37:e4:79
    - da:a8:c4:3b:7e:03
    - aa:3b:a1:a2:4c:36
    - 5e:bf:14:e7:da:69
    - ee:ee:ee:ee:ee:ee
    - 02:42:07:32:14:3e
  cpu-serial: 54 06 05 00 FF FB EB BF # Authorization server cpu serial
  main-board-serial: ec20e947-7b66-3d2d-5e48-89b88a10351e # Authorization server main board serial
  rule: strict # strict or lenient
