/*
 Navicat Premium Data Transfer

 Source Server         : 新测试环境
 Source Server Type    : MySQL
 Source Server Version : 50732
 Source Host           : **************:3306
 Source Schema         : dev_foreign_exchange

 Target Server Type    : MySQL
 Target Server Version : 50732
 File Encoding         : 65001

 Date: 10/02/2022 17:22:28
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for foreign_exchange
-- ----------------------------
DROP TABLE IF EXISTS `foreign_exchange`;
CREATE TABLE `foreign_exchange`  (
  `CountryCode` varchar(2) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `ClearingCode` varchar(4) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `BranchCode` varchar(3) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `SandBoxId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `AccountNumber` varchar(34) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `TransactionType` varchar(4) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `LocalCurrency` varchar(3) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `ForeignCurrency` varchar(3) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `ExchangeRate` decimal(18, 4) NULL DEFAULT NULL,
  `ExchangeAmoutInLocalCurrency` decimal(18, 2) NULL DEFAULT NULL,
  `ExchangeAmoutInForeignCurrency` decimal(18, 2) NULL DEFAULT NULL,
  `PrevBalInForeignCurrencyAccount` decimal(18, 2) NULL DEFAULT NULL,
  `PostBalInForeignCurrencyAccount` decimal(18, 2) NULL DEFAULT NULL,
  `TransactionTime` decimal(14, 0) NOT NULL,
  PRIMARY KEY (`TransactionTime`) USING BTREE,
  INDEX `AccountNumber`(`AccountNumber`) USING BTREE,
  INDEX `ForeignCurrency`(`ForeignCurrency`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;
