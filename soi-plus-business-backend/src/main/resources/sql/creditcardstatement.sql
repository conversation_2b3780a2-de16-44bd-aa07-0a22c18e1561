/*
 Navicat Premium Data Transfer

 Source Server         : 新测试环境
 Source Server Type    : MySQL
 Source Server Version : 50732
 Source Host           : **************:3306
 Source Schema         : dev_creditcard

 Target Server Type    : MySQL
 Target Server Version : 50732
 File Encoding         : 65001

 Date: 10/02/2022 17:25:32
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for creditcardstatement
-- ----------------------------
DROP TABLE IF EXISTS `creditcardstatement`;
CREATE TABLE `creditcardstatement`  (
  `ID` int(11) NOT NULL AUTO_INCREMENT,
  `CountryCode` varchar(10) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `ClearingCode` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `BranchCode` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `SandBoxId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `CreditCardNumber` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `HolderName` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `CustomerNumber` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `StatementDate` decimal(20, 0) NULL DEFAULT NULL,
  `RepaymentDueDate` decimal(20, 0) NULL DEFAULT NULL,
  `RepaymentAmount` decimal(18, 5) NULL DEFAULT NULL,
  `MinimumPayment` decimal(18, 5) NULL DEFAULT NULL,
  `NeedInterest` varchar(100) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `StatementPath` varchar(255) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `CreateDate` decimal(20, 0) NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 98026 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;
