/*
 Navicat Premium Data Transfer

 Source Server         : 新测试环境
 Source Server Type    : MySQL
 Source Server Version : 50732
 Source Host           : **************:3306
 Source Schema         : dev_deposit

 Target Server Type    : MySQL
 Target Server Version : 50732
 File Encoding         : 65001

 Date: 10/02/2022 17:18:35
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for currentaccountmaster
-- ----------------------------
DROP TABLE IF EXISTS `currentaccountmaster`;
CREATE TABLE `currentaccountmaster`  (
  `ID` int(20) NOT NULL AUTO_INCREMENT,
  `CountryCode` varchar(2) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `ClearingCode` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `BranchCode` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `SandBoxId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `CustomerNumber` varchar(25) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `AccountNumber` varchar(34) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `AccountStatus` varchar(4) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `CurrencyCode` varchar(3) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `LedgeBalance` decimal(18, 5) NULL DEFAULT NULL,
  `HoldingBalance` decimal(18, 5) NULL DEFAULT NULL,
  `AvailableBalance` decimal(18, 5) NULL DEFAULT NULL,
  `LastUpdatedDate` decimal(20, 0) NULL DEFAULT NULL,
  `ChequeBookType` varchar(1) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `ChequeBookSize` decimal(3, 0) NULL DEFAULT NULL,
  `CreateDate` decimal(20, 0) NULL DEFAULT NULL,
  `SubType` varchar(5) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `AccountName` varchar(70) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `AccountType` varchar(4) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `MonthlyPaymentValue` decimal(18, 5) NULL DEFAULT NULL,
  `MonthlyReceivedValue` decimal(18, 5) NULL DEFAULT NULL,
  `MonthlyTransactionNumber` decimal(5, 0) NULL DEFAULT NULL,
  `AverageBalance` decimal(18, 5) NULL DEFAULT NULL,
  `AccountPurpose` varchar(140) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `FloorNotificationAmount` decimal(18, 5) NULL DEFAULT NULL,
  `CeilingNotificationAmount` decimal(18, 5) NULL DEFAULT NULL,
  `StatementFrequency` varchar(4) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `CommunicationMethod` varchar(4) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `DeliveryAddress` varchar(350) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `StatementFormat` varchar(4) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `ClosingDate` date NULL DEFAULT NULL,
  `RestrictionType` varchar(4) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `ValidFrom` decimal(20, 0) NULL DEFAULT NULL,
  `ValidUntil` decimal(20, 0) NULL DEFAULT NULL,
  `LastChequeBookPage` decimal(5, 0) NULL DEFAULT NULL,
  `CustomerIndustryCode` decimal(5, 0) NULL DEFAULT NULL,
  `DateFirstContact` decimal(20, 0) NULL DEFAULT NULL,
  `CustomerOutstandingPosition` decimal(18, 5) NULL DEFAULT NULL,
  `CustomerOffsetLowPosition` decimal(18, 5) NULL DEFAULT NULL,
  `CustomerOffsetHighPosition` decimal(18, 5) NULL DEFAULT NULL,
  `CustomerOutstandAggregatePosition` decimal(18, 5) NULL DEFAULT NULL,
  `CustomerAggregateDayNumber` decimal(3, 0) NULL DEFAULT NULL,
  `CustomerOutstandingAggregatePostionLastPeriod` decimal(18, 5) NULL DEFAULT NULL,
  `CustomerAggregateDayLastPeriod` decimal(3, 0) NULL DEFAULT NULL,
  `CustomerStatementLowBalance` decimal(18, 5) NULL DEFAULT NULL,
  `CustomerStatementHighBalance` decimal(18, 5) NULL DEFAULT NULL,
  `CustomerStatementAggregateDebitBalance` decimal(18, 5) NULL DEFAULT NULL,
  `CustomerStatementAggregateCreditBalance` decimal(18, 5) NULL DEFAULT NULL,
  `CustomerStatementAggregateDebitDay` decimal(3, 0) NULL DEFAULT NULL,
  `CustomerStatementAggregateCreditDay` decimal(3, 0) NULL DEFAULT NULL,
  `CustomerStatementReturnChequeCount` decimal(3, 0) NULL DEFAULT NULL,
  PRIMARY KEY (`ID`, `CustomerNumber`) USING BTREE,
  UNIQUE INDEX `ID`(`ID`) USING BTREE,
  UNIQUE INDEX `AccountNumber`(`AccountNumber`) USING BTREE,
  INDEX `CustomerNumber`(`CustomerNumber`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 112060 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;
