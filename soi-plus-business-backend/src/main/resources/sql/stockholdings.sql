/*
 Navicat Premium Data Transfer

 Source Server         : 新测试环境
 Source Server Type    : MySQL
 Source Server Version : 50732
 Source Host           : **************:3306
 Source Schema         : dev_stock

 Target Server Type    : MySQL
 Target Server Version : 50732
 File Encoding         : 65001

 Date: 10/02/2022 17:24:39
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for stockholdings
-- ----------------------------
DROP TABLE IF EXISTS `stockholdings`;
CREATE TABLE `stockholdings`  (
  `ID` int(20) NOT NULL AUTO_INCREMENT,
  `AccountNumber` varchar(34) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `StockCode` varchar(10) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `SharesHoldingNo` decimal(18, 5) NULL DEFAULT NULL,
  `AvailableShare` decimal(18, 5) NULL DEFAULT NULL,
  `AveragePrice` decimal(18, 5) NULL DEFAULT NULL,
  `Currency` varchar(3) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `LastUpdateDate` decimal(20, 0) NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `AccountNumber`(`AccountNumber`) USING BTREE,
  INDEX `StockCode`(`StockCode`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 53107 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;
