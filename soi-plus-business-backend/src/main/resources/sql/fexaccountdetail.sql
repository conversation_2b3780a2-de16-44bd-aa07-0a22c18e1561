/*
 Navicat Premium Data Transfer

 Source Server         : 新测试环境
 Source Server Type    : MySQL
 Source Server Version : 50732
 Source Host           : **************:3306
 Source Schema         : dev_foreign_exchange

 Target Server Type    : MySQL
 Target Server Version : 50732
 File Encoding         : 65001

 Date: 10/02/2022 17:22:38
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for fexaccountdetail
-- ----------------------------
DROP TABLE IF EXISTS `fexaccountdetail`;
CREATE TABLE `fexaccountdetail`  (
  `ID` int(20) NOT NULL AUTO_INCREMENT,
  `AccountNumber` varchar(34) CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL,
  `CurrencyCode` varchar(3) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `Balance` decimal(18, 5) NULL DEFAULT NULL,
  `LastUpdatedDate` decimal(20, 0) NULL DEFAULT NULL,
  `CreateDate` decimal(20, 0) NULL DEFAULT NULL,
  PRIMARY KEY (`ID`, `AccountNumber`) USING BTREE,
  UNIQUE INDEX `ID`(`ID`) USING BTREE,
  INDEX `AccountNumber`(`AccountNumber`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 416234 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;
