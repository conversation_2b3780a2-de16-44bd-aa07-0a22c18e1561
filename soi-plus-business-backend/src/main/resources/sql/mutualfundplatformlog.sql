/*
 Navicat Premium Data Transfer

 Source Server         : 新测试环境
 Source Server Type    : MySQL
 Source Server Version : 50732
 Source Host           : **************:3306
 Source Schema         : dev_fund

 Target Server Type    : MySQL
 Target Server Version : 50732
 File Encoding         : 65001

 Date: 10/02/2022 17:23:21
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for mutualfundplatformlog
-- ----------------------------
DROP TABLE IF EXISTS `mutualfundplatformlog`;
CREATE TABLE `mutualfundplatformlog`  (
  `ID` int(20) NOT NULL AUTO_INCREMENT,
  `CountryCode` varchar(2) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `ClearingCode` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `BranchCode` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `FundCcy` varchar(3) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `AccountNumber` varchar(34) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `TradingOption` varchar(10) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `FundCode` varchar(35) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `SharingNo` decimal(18, 5) NULL DEFAULT NULL,
  `TradingAmount` decimal(18, 5) NULL DEFAULT NULL,
  `TrdingCommission` decimal(18, 5) NULL DEFAULT NULL,
  `TransactionAmount` decimal(18, 5) NULL DEFAULT NULL,
  `TransactionDate` decimal(20, 0) NULL DEFAULT NULL,
  `SandBoxId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `RiskRating` varchar(10) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `FundPrice` decimal(18, 2) NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 134385 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;
