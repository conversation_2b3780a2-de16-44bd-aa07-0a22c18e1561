/*
 Navicat Premium Data Transfer

 Source Server         : 新测试环境
 Source Server Type    : MySQL
 Source Server Version : 50732
 Source Host           : **************:3306
 Source Schema         : dev_loan

 Target Server Type    : MySQL
 Target Server Version : 50732
 File Encoding         : 65001

 Date: 10/02/2022 17:32:53
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for mortgageloancontractdetail
-- ----------------------------
DROP TABLE IF EXISTS `mortgageloancontractdetail`;
CREATE TABLE `mortgageloancontractdetail`  (
  `ID` int(20) NOT NULL AUTO_INCREMENT,
  `AccountNumber` varchar(34) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `ContractNumber` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `RepaymentCycle` varchar(1) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `PhaseNo` varchar(10) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `LoanAmountPerPhase` decimal(18, 5) NULL DEFAULT NULL,
  `LoanInterestPerPhase` decimal(18, 5) NULL DEFAULT NULL,
  `TotalPayment` decimal(18, 5) NULL DEFAULT NULL,
  `CurrencyCode` varchar(3) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `RepaymentDueDate` decimal(20, 0) NULL DEFAULT NULL,
  `RepaymentStatus` varchar(1) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `PenaltyAmount` decimal(18, 5) NULL DEFAULT NULL,
  `OutstandingBalance` decimal(18, 5) NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE,
  UNIQUE INDEX `Unique`(`ContractNumber`, `PhaseNo`) USING BTREE,
  INDEX `ContractNumber`(`ContractNumber`) USING BTREE,
  INDEX `PhaseNo`(`PhaseNo`) USING BTREE,
  INDEX `AccountNumber`(`AccountNumber`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 5489634 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Compact;

SET FOREIGN_KEY_CHECKS = 1;
