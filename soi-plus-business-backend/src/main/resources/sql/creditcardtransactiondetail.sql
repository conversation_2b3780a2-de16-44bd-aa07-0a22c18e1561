/*
 Navicat Premium Data Transfer

 Source Server         : 新测试环境
 Source Server Type    : MySQL
 Source Server Version : 50732
 Source Host           : **************:3306
 Source Schema         : dev_creditcard

 Target Server Type    : MySQL
 Target Server Version : 50732
 File Encoding         : 65001

 Date: 10/02/2022 17:25:42
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for creditcardtransactiondetail
-- ----------------------------
DROP TABLE IF EXISTS `creditcardtransactiondetail`;
CREATE TABLE `creditcardtransactiondetail`  (
  `ID` int(20) NOT NULL AUTO_INCREMENT,
  `CountryCode` varchar(2) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `ClearingCode` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `BranchCode` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `SandBoxId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `CreditCardNumber` varchar(34) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `CreditCardType` varchar(1) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `DealNumber` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `TransactionTime` decimal(20, 0) NULL DEFAULT NULL,
  `MerchantName` varchar(140) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `MerchantNumber` varchar(35) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `DisplayName` varchar(140) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `TransactionCcy` varchar(3) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `TransactionAmount` decimal(18, 5) NULL DEFAULT NULL,
  `BookingCcy` varchar(3) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `BookingAmount` decimal(18, 5) NULL DEFAULT NULL,
  `AuthorizationNumber` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `MerchantBalance` decimal(18, 5) NULL DEFAULT NULL,
  `TransactionType` int(11) NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE,
  INDEX `CreditCardNumber`(`CreditCardNumber`) USING BTREE,
  INDEX `TransactionAmount`(`TransactionAmount`) USING BTREE,
  INDEX `TransactionTime`(`TransactionTime`) USING BTREE,
  INDEX `MerchantNumber`(`MerchantNumber`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 772714 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;
