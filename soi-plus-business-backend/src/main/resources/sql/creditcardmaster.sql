/*
 Navicat Premium Data Transfer

 Source Server         : 新测试环境
 Source Server Type    : MySQL
 Source Server Version : 50732
 Source Host           : **************:3306
 Source Schema         : dev_creditcard

 Target Server Type    : MySQL
 Target Server Version : 50732
 File Encoding         : 65001

 Date: 10/02/2022 17:25:10
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for creditcardmaster
-- ----------------------------
DROP TABLE IF EXISTS `creditcardmaster`;
CREATE TABLE `creditcardmaster`  (
  `ID` int(20) NOT NULL AUTO_INCREMENT,
  `CountryCode` varchar(2) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `ClearingCode` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `BranchCode` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `SandBoxId` varchar(50) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `CreditCardNumber` varchar(34) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `HolderName` varchar(140) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `CreditCardType` varchar(1) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `IssuanceDate` decimal(20, 0) NULL DEFAULT NULL,
  `ExpiryDate` decimal(20, 0) NULL DEFAULT NULL,
  `VerificationCode` varchar(3) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `CreditCardStatus` varchar(20) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `ReportLossDate` decimal(20, 0) NULL DEFAULT NULL,
  `ReportCancelDate` decimal(20, 0) NULL DEFAULT NULL,
  `CreateDate` decimal(20, 0) NULL DEFAULT NULL,
  `ApprovedLimit` decimal(18, 5) NULL DEFAULT NULL,
  `CashAdvanceLimit` decimal(18, 5) NULL DEFAULT NULL,
  `UsedLimit` decimal(18, 5) NULL DEFAULT NULL,
  `AvailableLimit` decimal(18, 5) NULL DEFAULT NULL,
  `AssociatedCusNum` varchar(34) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `RepaymentCycle` varchar(5) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `OutstandingBalance` decimal(18, 5) NULL DEFAULT NULL,
  `StatementDate` decimal(20, 0) NULL DEFAULT NULL,
  `RepaymentDueDate` decimal(20, 0) NULL DEFAULT NULL,
  `RepaymentAccountNum` varchar(34) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  `PainAmount` decimal(18, 5) NULL DEFAULT NULL,
  `RepaymentAmount` decimal(18, 5) NULL DEFAULT NULL,
  `MinimumPayment` decimal(18, 5) NULL DEFAULT NULL,
  `NeedInterest` varchar(1) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL,
  PRIMARY KEY (`ID`) USING BTREE,
  UNIQUE INDEX `ID`(`ID`) USING BTREE,
  UNIQUE INDEX `CreditCardNumber`(`CreditCardNumber`) USING BTREE,
  INDEX `AssociatedCusNum`(`AssociatedCusNum`) USING BTREE
) ENGINE = InnoDB AUTO_INCREMENT = 147285 CHARACTER SET = utf8 COLLATE = utf8_general_ci ROW_FORMAT = Dynamic;

SET FOREIGN_KEY_CHECKS = 1;
