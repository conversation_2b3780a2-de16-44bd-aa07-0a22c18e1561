server:
  port: 8300
  deploy:
    url: https://simnectzplatform.com/soi-plus-business/
#  ssl:
#    key-store: classpath:ssl/uat/demo.simnectzplatform.com.pfx
#    key-store-password: hfcdogxh
#    keyStoreType: PKCS12

spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ********************************************************************************************
    username: chinasoft
    password: Aa123456
    hikari:
      minimum-idle: 5
      maximum-pool-size: 15
      auto-commit: true
      idle-timeout: 30000
      max-lifetime: 1800000
      connection-timeout: 30000
      connection-test-query: SELECT 1
  redis:
    password:
    database: 0
    port: 6379
    host: 127.0.0.1
  mail:
    host: smtp.exmail.qq.com
    protocol: smtp
    port: 465
    username: <EMAIL>
    password: Guangzhou2019
    properties:
      mail:
        smtp:
          socketFactory:
            port: 465
          ssl:
            enable: true
            socketFactory:
              fallback: false
              class: com.sun.mail.util.MailSSLSocketFactory
          auth: true
          starttls:
            enable: true
            required: true
  freemarker:
    cache: true

mybatis-flex:
  type-aliases-package: com.simnectz.**.entity
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  datasource:
    default:
      url: ********************************************************************************************
      username: root
      password: SimnectzRDS@2020
    deposit:
      url: *****************************************************************************************************************************************************************************************************
      username: root
      password: SimnectzRDS@2020
    creditcard:
      url: ********************************************************************************************************************************************************************************************************
      username: root
      password: SimnectzRDS@2020
    foreign_exchange:
      url: **************************************************************************************************************************************************************************************************************
      username: root
      password: SimnectzRDS@2020
    fund:
      url: **************************************************************************************************************************************************************************************************
      username: root
      password: SimnectzRDS@2020
    insurance:
      url: *******************************************************************************************************************************************************************************************************
      username: root
      password: SimnectzRDS@2020
    payment:
      url: *****************************************************************************************************************************************************************************************************
      username: root
      password: SimnectzRDS@2020
    stock:
      url: ***************************************************************************************************************************************************************************************************
      username: root
      password: SimnectzRDS@2020
    term_deposit:
      url: **********************************************************************************************************************************************************************************************************
      username: root
      password: SimnectzRDS@2020
    loan:
      url: **************************************************************************************************************************************************************************************************
      username: root
      password: SimnectzRDS@2020
    sysadmin:
      url: ******************************************************************************************************************************************************************************************************
      username: root
      password: SimnectzRDS@2020

system:
  authorize:
    header: 'Authorization'
    prefix: 'Bearer '
    access-token-secret-key: a83f0e5e-55f5-c3f2-2bf4-c9eb442d2cec
    access-token-expire-time: 1440 # 1 day
    refresh-token-secret-key: 87a2e6c6-a656-9180-8799-73a133fa4609
    refresh-token-expire-time: 10080 # 7 days
    password-encode-secret-key: 8b70e850-7420-58b1-7a4d-ec1399a992ad
  email:
    from: ${spring.mail.username}
  upload:
    request-url: https://demo.simnectzplatform.com/file/
    storage-path: /home/<USER>/file
    sql-file-path: uploadSqlFile/
    table-info-file-path: uploadTableFile/

lbs:
  hackathon:
    sql:
      path: sql/

importdb:
  dbUrl: *********************************************************************************************************************

send:
  email:
    jump:
      url: http://localhost:9000/login?redirect=/WorkSheetManagement

#编码运行相关系统配置
compiler:
  java:
    path: /temp/java/
    file-name: Main
    jar-package-name: aip-tools-1.0.0.jar
  python:
    path: /temp/python/
    file-name: main
  c:
    path: /temp/c/
    file-name: main
  cpp:
    path: /temp/cpp/
    file-name: main

tyk:
  policy:
    path: /opt/tyk-gateway/policies/policies.json
  reload:
    url: https://simnectzplatform.com/gateway/tyk/reload
  host: https://simnectzplatform.com/gateway/
  authorization: simnectz
  proxyHost: simnectzplatform.com/gateway/

oss:
  accessKeyId: LTAI4G1KR2a93kf12q3G1upF
  accessKeySecret: ******************************
  endpoint: oss-cn-shenzhen.aliyuncs.com
  bucketName: simnectzplatform
  objectName: trainingPlatform/
