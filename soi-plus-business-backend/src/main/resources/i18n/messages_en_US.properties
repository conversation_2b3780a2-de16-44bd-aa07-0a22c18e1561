OPERATION_SUCCESS=Operation Successfully
UNAUTHORIZED=Unauthorized
FORBIDDEN=Forbidden
METHOD_NOT_ALLOWED=Request method '{method}' not supported
SERVER_ERROR=Server Error
COURSE_CATEGORY_ID_INDEX_IS_REQUIRED=Course category id index is required
COURSE_CATEGORY_IS_REQUIRED=Course category id is required
COURSE_CATEGORY_PICTURE_IS_REQUIRED=Course category picture is required
COURSE_NAME_IS_REQUIRED=Course name is required
COURSE_CODE_IS_REQUIRED=Course code is required
COURSE_DESCRIPTION_IS_REQUIRED=Course description is required
COURSE_PICTURE_IS_REQUIRED=Course picture is required
COURSE_ID_INDEX_IS_REQUIRED=Course id index is required
COURSE_CATEGORY_NOT_FOUND=Course category not found
COURSE_NOT_FOUND=Course not found
COURSE_CATEGORY_ALREADY_EXISTS=Course category already exists
EXERCISE_ANSWER_ALREADY_EXISTS=The exercise answer already exists
COURSE_ALREADY_EXISTS=Course already exists
THE_GROUP_NOT_EXIST=The group not exist
UNABLE_DELETE_COURSE_CATEGORY=Please input valid course category ids
UNABLE_DELETE_COURSE=Please input valid course ids
UNABLE_DELETE_QUESTION_BANK=Please input valid question bank ids
STU_PATH_NOT_FOUND=Study path not found
STU_PATH_ALREADY_EXISTS=Study path already exists
UNABLE_DELETE_STU_PATH=Please input valid study path ids
REQUEST_PARAMETER_ERROR=Request parameter error
SUBJECT_CODE_IS_REQUIRED=Subject code is required
SUBSCRIPTION_STATUS_IS_REQUIRED=Subscription status is required
USER_NOT_FOUND=User not found
THE_DB_INFO_NOT_EXIST=The db info not exist
THE_CONSUMER_NOT_EXIST=The consumer not exist
THE_DATABASE_NO_PERMISSION=The database no permission
THE_CONSUMER_HAVE_NOT_SANDBOX_DATA=The consumer have not sandbox data
THE_USER_HAS_NOT_BEEN_ASSIGNED_SANDBOX_DATA=The user has not been assigned sandbox data
CHART_NOT_FOUND=Chart not found
VIEW_NOT_FOUND=viewEntity not found
THE_CHART_NAME_ALREADY_EXISTS=The chartEntity name already exists
QUESTION_ALREADY_EXISTS=Question already exists
QUESTION_NOT_FOUND=Question not found
ANSWERS_NOT_FOUND=Answers not found
SUBJECT_ID_INDEX_IS_REQUIRED=Subject id index is required
COURSE_ID_IS_REQUIRED=Course id is required
SUBJECT_NAME_IS_REQUIRED=Subject name is required
SUBJECT_CATEGORY_IS_REQUIRED=Subject category is required
SUBJECT_OBJECTIVE_IS_REQUIRED=Subject objective is required
SUBJECT_DESCRIPTION_IS_REQUIRED=Subject description is required
SUBJECT_PICTURE_URL_IS_REQUIRED=Subject picture url is required
SUBJECT_HOURS_IS_REQUIRED=Subject hours is required
SUBJECT_LEVEL_IS_REQUIRED=Subject level is required
INVALID_SUBJECT_CATEGORY=Invalid subject category
INVALID_SUBJECT_LEVEL=Invalid subject level
SUBJECT_NOT_FOUND=Subject not found
SUBJECT_ALREADY_EXISTS=Subject already exists
UNABLE_DELETE_SUBJECT=Please input valid subject ids
UNABLE_DELETE_WORK_SHEET=Please input valid worksheet ids
QUESTION_ID_IS_REQUIRED=Question id is required
COURSE_CATEGORY_ID_IS_REQUIRED=Course category id is required
QUESTION_STUANSWER_IS_REQUIRED=Question stuanswer is required
QUESTION_BANK_NOT_FOUND=Question bank not found
FILE_IS_EMPTY=File is empty
INVALID_FILE_SIZE=File size can not grate than 50MB
SAVE_FILE_FAILED=Save file failed
CREATED_DATABASE_FAILED=has not created the database yet
INVALID_UPLOAD_TABLES_TYPE=Upload tables only support Excel file
WORKBOOK_IS_NULL_AND_EXCEL_READ_ERROR=Workbook is null,excel read error
IMPORT_DATA_ERROR_AND_ILLEGAL_DATA=Import data error,There is an illegal data in Excel
ILLEGAL_TABLE_NAME=sheet name contains illegal characters and cannot be used as a table name
ILLEGAL_TABLE_FIELD=sheet table field can only be letters, combination of letters、numbers or underscores
TABLE_FIELD_IS_BLANK=sheet table field is blank;
DATABASE_NOT_FOUND=Can not find database by userId
THE_CLASS_ASSOCIATED_DOCUMENT_DATA=The class associated document data
INVALID_SUBSCRIPTION_STATUS=Invalid subscription status
INVALID_UPLOAD_FILE_SIZE=Upload file cannot be greater than 500kb
INVALID_ORDER_STATUS=You can only leave a message when the order status is not Close
WITHOUT_CREATE_WORKSPACE=The user haven't created workspace
QUESTION_ID_CAN_NOT_BE_EMPTY=Question ID cannot be empty
COMPILATION_FAILED=Compilation failed
RUN_FAILED=Run failed
RUN_SUCCESSFUL=Run Successful
RUNNING_PROGRAMMING_LANGUAGE_CANNOT_BE_EMPTY=The running programming language cannot be empty
RUNNING_CODE_CANNOT_BE_EMPTY=The running code cannot be empty
UNSUPPORTED_PROGRAMMING_LANGUAGE=Unsupported programming language
CANNOT_FIND_TASK=can not find task by the userId
NO_INDUSTRY_DATA=No industry data yet
API_NOT_EXIST=api is not exist
QUESTION_CREATOR_CANNOT_BE_EMPTY=Question creator cannot be empty
INVALID_QUESTION_TITLE_ZH=Question title(zh) cannot be empty and cannot exceed 600 characters
INVALID_QUESTION_TITLE_EN=Question title(en) cannot be empty and cannot exceed 300 characters
INVALID_QUESTION_CATEGORY_ZH=Question category(zh) cannot be empty and cannot exceed 100 characters
INVALID_QUESTION_CATEGORY_EN=Question category(en) cannot be empty and cannot exceed 200 characters
INVALID_QUESTION_SUBCATEGORY_ZH=Question subcategory(zh) cannot be empty and cannot exceed 100 characters
INVALID_QUESTION_SUBCATEGORY_EN=Question subcategory(en) cannot be empty and cannot exceed 200 characters
INVALID_QUESTION_DESCRIPTION_ZH=Question description(zh) cannot be empty
INVALID_QUESTION_DESCRIPTION_EN=Question description(en) cannot be empty
INVALID_QUESTION_INITIAL_CODE=Question initial code cannot be empty
REPEAT_QUESTION_TITLE_ZH=Question title(zh) repeat
REPEAT_QUESTION_TITLE_EN=Question title(en) repeat
DELETE_FAILED=Deletion failed
FILE_SIZE_CANNOT_GRATE_THAN_5GB=File size can not grate than 5GB
UPLOAD_FILE_FAILED=Upload file failed
CREATE_FAILED=Create failed
UPLOAD_FAILED=Update failed
CLASS_NAME_ERROR=Error className exists
FINAME_NOT_EXIST=FiName not exist
FI_INFO_NOT_EXIST=Fi info not exist
GRANT_TYPE_IS_REQUIRED=grant_type(s) is required
CLIENT_SECRET_IS_REQUIRED=client_secret is required
CLIENT_ID_IS_REQUIRED=client_id is required
INVALID_GRANT_TYPE=grant_type(s) can not just be [refresh_token]
INVALID_CLIENT_SECRET=client_secret Contains at least 8 characters
INVALID_CLIENT_ID=client_id Contains at least five characters
REPEAT_CLIENT_ID=client_id [ {clientId} ] exists
CLIENT_ID_NOT_EXIST=client id not exist
DATA_NOT_EXIST=Data not exist
MARKDOWN_DOCUMENT_NOT_FOUND=Markdown document not found
# License
INVALID_SERVER_HARDWARE_INFORMATION=Abnormal acquisition of server hardware information
INVALID_AUTHORIZATION_TIME=Abnormal authorization time
INVALID_CURRENT_TIME=Current time is not within the authorized range
INVALID_CURRENT_IP=The current server IP is not within the authorized range
INVALID_CURRENT_MAC_ADDRESS=The current server Mac address is not within the authorized range
INVALID_CURRENT_MOTHERBOARD_SERIAL_NUMBER=The current server motherboard serial number is not within the authorized range
INVALID_CURRENT_CPU_SERIAL_NUMBER=The current server CPU serial number is not within the authorized range
IMPORT_FAILED=Data import failed: Error in row {0}: {1}. Please check and re-upload.
FILED_NAME_IS_REQUIRED=Filed name is required.
FILED_TYPE_IS_REQUIRED=Filed type is required.
INVALID_FIELD_TYPE=Invalid field type. Allowed values: \"tinyint\", \"int\", \"bigint\", \"decimal\", \"varchar\", \"date\", \"datetime\".
MAX_LENGTH_IS_REQUIRED=Max length is required.
INVALID_MAX_LENGTH_SIZE=Invalid max length size. Range: {min}-{max}.
INVALID_MAX_LENGTH_SIZE_TINYINT=Invalid max length size. Allowed value: 1.
SCALE_IS_REQUIRED=Scale is required.
INVALID_SCALE_SIZE=Invalid scale size. Range: {min}-{max}.
DATA_TABLE_FIELD_IS_REQUIRED=Data table field list is required.
DATA_FIELD_NOT_MATCH=The data does not match the table fields: {0}. Please check the data and re-upload.
DATA_TABLE_NOT_FOUND=Please upload the data table description first and try again
DATA_TABLE_NOT_FOUND_PLEASE_TRY_AGAIN=Data table not found. Please upload the data table description first and try again