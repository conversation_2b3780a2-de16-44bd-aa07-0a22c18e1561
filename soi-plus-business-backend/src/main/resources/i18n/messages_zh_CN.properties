OPERATION_SUCCESS=操作成功
UNAUTHORIZED=未授权
FORBIDDEN=拒绝访问
METHOD_NOT_ALLOWED=不支持请求方法'{method}'
SERVER_ERROR=服务器错误
COURSE_CATEGORY_ID_INDEX_IS_REQUIRED=课程大分类信息表中的id索引是必填的
COURSE_CATEGORY_IS_REQUIRED=课程大分类是必填的
COURSE_CATEGORY_PICTURE_IS_REQUIRED=课程大分类图片URL是必填的
COURSE_NAME_IS_REQUIRED=课程名称为必填项
COURSE_CODE_IS_REQUIRED=课程代码为必填项
COURSE_DESCRIPTION_IS_REQUIRED=课程描述为必填项
COURSE_PICTURE_IS_REQUIRED=课程图片为必填项
COURSE_ID_INDEX_IS_REQUIRED=课程id索引为必填项
COURSE_CATEGORY_NOT_FOUND=未找到该课程大分类记录
COURSE_NOT_FOUND=未找到该课程记录
COURSE_CATEGORY_ALREADY_EXISTS=课程大分类已经存在
EXERCISE_ANSWER_ALREADY_EXISTS=练习答案已经存在
COURSE_ALREADY_EXISTS=课程已经存在
THE_GROUP_NOT_EXIST=分组不存在
UNABLE_DELETE_COURSE_CATEGORY=请输入有效的课程大分类ids
UNABLE_DELETE_COURSE=请输入有效的课程ids
UNABLE_DELETE_QUESTION_BANK=请输入有效的试题练习ids
STU_PATH_NOT_FOUND=学习路径信息未找到记录
STU_PATH_ALREADY_EXISTS=学习路径信息已经存在
UNABLE_DELETE_STU_PATH=请输入有效的学习路径信息ids
REQUEST_PARAMETER_ERROR=请求参数有误
SUBJECT_CODE_IS_REQUIRED=课题代码是必填的
SUBSCRIPTION_STATUS_IS_REQUIRED=订阅状态是必填的
USER_NOT_FOUND=未找到该用户
THE_CONSUMER_NOT_EXIST=用户不存在
THE_DB_INFO_NOT_EXIST=数据库信息不存在
THE_DATABASE_NO_PERMISSION=数据库无权限
THE_CONSUMER_HAVE_NOT_SANDBOX_DATA=该用户没有沙盒数据
THE_USER_HAS_NOT_BEEN_ASSIGNED_SANDBOX_DATA=该用户未被分配沙盒数据
CHART_NOT_FOUND=未找到图表
VIEW_NOT_FOUND=未找到视图
THE_CHART_NAME_ALREADY_EXISTS=图标名称已存在
QUESTION_CONTENT_IS_REQUIRED=试题内容是必填的
QUESTION_MODEL_ANSWER_IS_REQUIRED=试题标准答案是必填的
QUESTION_ALREADY_EXISTS=试题已经存在
QUESTION_NOT_FOUND=未找到试题信息
ANSWERS_NOT_FOUND=未找到答案信息
SUBJECT_ID_INDEX_IS_REQUIRED=课题信息表中的id索引不能为空
COURSE_ID_IS_REQUIRED=关联课程信息id不能为空
SUBJECT_NAME_IS_REQUIRED=课题名称不能为空
SUBJECT_CATEGORY_IS_REQUIRED=课题类型不能为空
SUBJECT_OBJECTIVE_IS_REQUIRED=课题目标不能为空
SUBJECT_DESCRIPTION_IS_REQUIRED=课题内容描述不能为空
SUBJECT_PICTURE_URL_IS_REQUIRED=课题图片URL地址不能为空
SUBJECT_HOURS_IS_REQUIRED=课题时长不能为空
SUBJECT_LEVEL_IS_REQUIRED=课题级别不能为空
INVALID_SUBJECT_CATEGORY=无效的课题类型
INVALID_SUBJECT_LEVEL=无效的课题级别
COURSE_ID_NOT_FOUND=未找到课题名称
COURSE_ID_ALREADY_EXISTS=课题名称已经存在
SUBJECT_CODE_NOT_FOUND=未找到课题代码
SUBJECT_CODE_ALREADY_EXISTS=课题代码已经存在
SUBJECT_NOT_FOUND=未找到课题
SUBJECT_ALREADY_EXISTS=课题已经存在
UNABLE_DELETE_SUBJECT=请输入有效的课题信息ids
UNABLE_DELETE_WORK_SHEET=请输入有效的工单信息ids
QUESTION_ID_IS_REQUIRED=题干id不能为空
COURSE_CATEGORY_ID_IS_REQUIRED=课程大分类id不能为空
QUESTION_STUANSWER_IS_REQUIRED=学生给出的答案不能为空
INVALID_FILE_SIZE=文件大小不能超过50MB
SAVE_FILE_FAILED=保存文件失败
CREATED_DATABASE_FAILED=用户还没有创建数据库
INVALID_UPLOAD_TABLES_TYPE=上传表格只支持Excel文件
WORKBOOK_IS_NULL_AND_EXCEL_READ_ERROR=工作簿为空，excel读取错误
IMPORT_DATA_ERROR_AND_ILLEGAL_DATA=导入数据错误，Excel中存在非法数据
ILLEGAL_TABLE_NAME=工作表名称包含非法字符，不能用作表名
ILLEGAL_TABLE_FIELD=工作表表字段只能是字母、字母、数字或下划线的组合
TABLE_FIELD_IS_BLANK=工作表字段为空白；
DATABASE_NOT_FOUND=无法通过userId找到数据库
THE_CLASS_ASSOCIATED_DOCUMENT_DATA=该类已关联文档数据
QUESTION_BANK_NOT_FOUND=该试题练习信息未找到
INVALID_SUBSCRIPTION_STATUS=不合法的订阅状态
INVALID_UPLOAD_FILE_SIZE=上传的文件不能大于500kb
INVALID_ORDER_STATUS=您只能在订单状态不是Close的情况下留言
WITHOUT_CREATE_WORKSPACE=用户没有创建工作空间
QUESTION_ID_CAN_NOT_BE_EMPTY=试题ID不能为空
COMPILATION_FAILED=编译失败
RUN_FAILED=运行失败
RUN_SUCCESSFUL=运行成功
RUNNING_PROGRAMMING_LANGUAGE_CANNOT_BE_EMPTY=正在运行的编程语言不能为空
RUNNING_CODE_CANNOT_BE_EMPTY=正在运行的代码不能为空
UNSUPPORTED_PROGRAMMING_LANGUAGE=不支持的编程语言
CANNOT_FIND_TASK=无法通过userId找到任务
NO_INDUSTRY_DATA=目前还没有行业数据
API_NOT_EXIST=API不存在
QUESTION_CREATOR_CANNOT_BE_EMPTY=题目创建人不能为空
INVALID_QUESTION_TITLE_ZH=题目(中文)不能为空，且不能超过600个字符
INVALID_QUESTION_TITLE_EN=题目(英文)不能为空，且不能超过300个字符
INVALID_QUESTION_CATEGORY_ZH=问题类别(中文)不能为空，且不能超过100个字符
INVALID_QUESTION_CATEGORY_EN=问题类别(英文)不能为空，且不能超过200个字符
INVALID_QUESTION_SUBCATEGORY_ZH=问题子类别(中文)不能为空，且不能超过100个字符
INVALID_QUESTION_SUBCATEGORY_EN=问题子类别(英文)不能为空，不能超过200个字符
INVALID_QUESTION_DESCRIPTION_ZH=问题描述(中文)不能为空
INVALID_QUESTION_DESCRIPTION_EN=问题描述(英文)不能为空
INVALID_QUESTION_INITIAL_CODE=问题初始码不能为空
REPEAT_QUESTION_TITLE_ZH=问题题目(中文)重复
REPEAT_QUESTION_TITLE_EN=问题题目(英文)重复
DELETE_FAILED=删除失败
FILE_SIZE_CANNOT_GRATE_THAN_5GB=文件大小不能超过5GB
UPLOAD_FILE_FAILED=上传文件失败
CREATE_FAILED=创建失败
UPLOAD_FAILED=更新失败
CLASS_NAME_ERROR=分类名称存在错误
FINAME_NOT_EXIST=Fi名称不存在
FI_INFO_NOT_EXIST=Fi信息不存在
GRANT_TYPE_IS_REQUIRED=grant_type(s)是必需的
CLIENT_SECRET_IS_REQUIRED=client_secret是必需的
CLIENT_ID_IS_REQUIRED=client_id是必需的
INVALID_GRANT_TYPE=grant_type(s) 不能只是[refresh_token]
INVALID_CLIENT_SECRET=client_secret 长度至少8位
INVALID_CLIENT_ID=client_id 长度至少5位
REPEAT_CLIENT_ID=client_id [ {clientId} ] 已存在
CLIENT_ID_NOT_EXIST=client_id 不存在
DATA_NOT_EXIST=数据不存在
DOCUMENT_ID_REQUIRED=文档ID不能为空
DOCUMENT_NAME_REQUIRED=文档名称不能为空
DOCUMENT_NOT_FOUND=文档不存在
DOCUMENT_NAME_ALREADY_EXISTS=文档名称已存在
DOCUMENT_HAS_CHILDREN=该文档下存在子文档，无法删除
PARENT_DOCUMENT_ID_REQUIRED=父文档ID不能为空
# License
INVALID_SERVER_HARDWARE_INFORMATION=获取服务器硬件信息异常
INVALID_AUTHORIZATION_TIME=授权时间异常
INVALID_CURRENT_TIME=当前时间不在授权范围内
INVALID_CURRENT_IP=当前服务器的IP没在授权范围内
INVALID_CURRENT_MAC_ADDRESS=当前服务器的Mac地址没在授权范围内
INVALID_CURRENT_MOTHERBOARD_SERIAL_NUMBER=当前服务器的主板序列号没在授权范围内
INVALID_CURRENT_CPU_SERIAL_NUMBER=当前服务器的CPU序列号没在授权范围内
IMPORT_FAILED=导入数据失败：第{0}行存在错误：{1}，请检查后重新上传
FILED_NAME_IS_REQUIRED=字段名称不能为空
FILED_TYPE_IS_REQUIRED=字段类型不能为空
INVALID_FIELD_TYPE=无效的字段类型，允许的值: \"tinyint\", \"int\", \"bigint\", \"decimal\", \"varchar\", \"date\", \"datetime\"
MAX_LENGTH_IS_REQUIRED=总长度不能为空
INVALID_MAX_LENGTH_SIZE=总长度值无效，有效范围: {min}-{max}
INVALID_MAX_LENGTH_SIZE_TINYINT=总长度值无效，有效值: {min}
SCALE_IS_REQUIRED=标度不能为空
INVALID_SCALE_SIZE=标度值无效，有效范围: {min}-{max}
DATA_TABLE_FIELD_IS_REQUIRED=数据表字段列表不能为空
DATA_FIELD_NOT_MATCH=数据与表字段不匹配: {0}，请检查数据信息后重新上传
DATA_TABLE_NOT_FOUND=数据表不存在
DATA_TABLE_NOT_FOUND_PLEASE_TRY_AGAIN=数据表不存在，请先上传数据表描述后再试