server:
  port: 8300
  deploy:
    url: https://simnectz-portal.cefar.cuhk.edu.hk/soi-plus-business/

spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ***************************************************************************************
    username: root
    password: CUHK@MySQL
    hikari:
      minimum-idle: 5
      maximum-pool-size: 15
      auto-commit: true
      idle-timeout: 30000
      max-lifetime: 1800000
      connection-timeout: 30000
      connection-test-query: SELECT 1
  redis:
    password: root
    database: 0
    port: 6379
    host: 127.0.0.1
  mail:
    host: smtp.exmail.qq.com
    protocol: smtp
    port: 465
    username: <EMAIL>
    password: Guangzhou2019
    properties:
      mail:
        smtp:
          socketFactory:
            port: 465
          ssl:
            enable: true
            socketFactory:
              fallback: false
              class: com.sun.mail.util.MailSSLSocketFactory
          auth: true
          starttls:
            enable: true
            required: true
  freemarker:
    cache: true

mybatis-flex:
  type-aliases-package: com.simnectz.**.entity
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  datasource:
    default:
      url: ***************************************************************************************
      username: root
      password: CUHK@MySQL
    deposit:
      url: ************************************************************************************************************************************************************************************************
      username: root
      password: CUHK@MySQL
    creditcard:
      url: ***************************************************************************************************************************************************************************************************
      username: root
      password: CUHK@MySQL
    foreign_exchange:
      url: *********************************************************************************************************************************************************************************************************
      username: root
      password: CUHK@MySQL
    fund:
      url: *********************************************************************************************************************************************************************************************
      username: root
      password: CUHK@MySQL
    insurance:
      url: **************************************************************************************************************************************************************************************************
      username: root
      password: CUHK@MySQL
    payment:
      url: ************************************************************************************************************************************************************************************************
      username: root
      password: CUHK@MySQL
    stock:
      url: **********************************************************************************************************************************************************************************************
      username: root
      password: CUHK@MySQL
    term_deposit:
      url: *****************************************************************************************************************************************************************************************************
      username: root
      password: CUHK@MySQL
    loan:
      url: *********************************************************************************************************************************************************************************************
      username: root
      password: CUHK@MySQL
    sysadmin:
      url: *************************************************************************************************************************************************************************************************
      username: root
      password: CUHK@MySQL

system:
  authorize:
    header: 'Authorization'
    prefix: 'Bearer '
    access-token-secret-key: a83f0e5e-55f5-c3f2-2bf4-c9eb442d2cec
    access-token-expire-time: 1440 # 1 day
    refresh-token-secret-key: 87a2e6c6-a656-9180-8799-73a133fa4609
    refresh-token-expire-time: 10080 # 7 days
    password-encode-secret-key: 8b70e850-7420-58b1-7a4d-ec1399a992ad
  email:
    from: ${spring.mail.username}
  upload:
    request-url: https://simnectz-portal.cefar.cuhk.edu.hk/document/soi-plus/
    storage-path: /home/<USER>/document/soi-plus
    sql-file-path: uploadSqlFile/
    table-info-file-path: uploadTableFile/

lbs:
  hackathon:
    sql:
      path: sql/

importdb:
  dbUrl: ****************************************************************************************************************

send:
  email:
    jump:
      url: http://localhost:9000/login?redirect=/WorkSheetManagement

#编码运行相关系统配置
compiler:
  java:
    path: /temp/java/
    file-name: Main
    jar-package-name: api-tools.jar
  python:
    path: /temp/python/
    file-name: main
  c:
    path: /temp/c/
    file-name: main
  cpp:
    path: /temp/cpp/
    file-name: main

tyk:
  policy:
    path: /opt/tyk-gateway/policies/policies.json
  reload:
    url: https://simnectz-portal.cefar.cuhk.edu.hk/gateway/tyk/reload
  host: https://simnectz-portal.cefar.cuhk.edu.hk/gateway/
  authorization: simnectz
  proxyHost: simnectz-portal.cefar.cuhk.edu.hk/gateway/

oss:
  accessKeyId: LTAI4G1KR2a93kf12q3G1upF
  accessKeySecret: ******************************
  endpoint: oss-cn-shenzhen.aliyuncs.com
  bucketName: simnectzplatform
  objectName: trainingPlatform/
license:
  enabled: true # Whether to enable license verification
  issued-time: 2024-10-15 00:00:00 # Authorization start time(yyyy-MM-dd HH:mm:ss)
  expiry-time: 2025-07-31 23:59:59 # Authorization end time(yyyy-MM-dd HH:mm:ss)
  ip-address: ************* # Authorization server ip address list (Leave it blank to not verify IP)
  mac-address: # Authorization server mac address list
    - 00:50:56:b1:2a:45
    - 02:42:48:60:58:3d
    - 02:42:75:84:2d:ee
    - 32:d9:ab:0b:2c:40
    - be:c8:7d:e0:b5:8b
    - ee:c5:b2:3f:44:f9
    - b2:8a:85:5d:24:87
  cpu-serial: 54 06 05 00 FF FB 8B 1F # Authorization server cpu serial
  main-board-serial: VMware-42 31 25 cb b7 29 55 1c-f2 46 e9 70 d1 3c c6 e9 # Authorization server main board serial
  rule: strict # strict or lenient
