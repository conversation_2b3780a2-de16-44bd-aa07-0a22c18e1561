package com.simnectz.authorize.system.domain.bo;

import cn.hutool.core.collection.CollUtil;
import com.mybatisflex.annotation.RelationManyToMany;
import com.simnectz.soiplus.business.learningspace.users.domain.entity.UserEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.springframework.security.core.userdetails.UserDetails;

import java.io.Serializable;
import java.util.Collection;
import java.util.HashSet;
import java.util.Set;

/**
 * System user entity
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class User extends UserEntity implements Serializable, UserDetails {

    private static final long serialVersionUID = 2595958345804355588L;

    private boolean enabled;
    private boolean accountNonExpired;
    private boolean accountNonLocked;
    private boolean credentialsNonExpired;
    @RelationManyToMany(
            selfField = "id",
            targetTable = "system_role",
            targetField = "id",
            valueField = "roleName",
            joinTable = "system_user_role",
            joinSelfColumn = "user_id",
            joinTargetColumn = "role_id"
    )
    private Set<String> roles;
    private Set<Authority> authorities;

    public void addAuthorities(Collection<Authority> authorities) {
        if (CollUtil.isEmpty(this.authorities)) {
            this.authorities = new HashSet<>();
        }
        this.authorities.addAll(authorities);
    }

}
