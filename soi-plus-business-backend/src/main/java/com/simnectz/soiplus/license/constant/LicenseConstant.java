package com.simnectz.soiplus.license.constant;

public class LicenseConstant {
	private LicenseConstant() {

	}

	public static class Code {
		public static final Integer INVALID_SERVER_AUTHORIZATION = 40301;
	}

	public static class Message {
		public static final String INVALID_SERVER_HARDWARE_INFORMATION = "INVALID_SERVER_HARDWARE_INFORMATION";
		public static final String INVALID_AUTHORIZATION_TIME = "INVALID_AUTHORIZATION_TIME";
		public static final String INVALID_CURRENT_TIME = "INVALID_CURRENT_TIME";
		public static final String INVALID_CURRENT_IP = "INVALID_CURRENT_IP";
		public static final String INVALID_CURRENT_MAC_ADDRESS = "INVALID_CURRENT_MAC_ADDRESS";
		public static final String INVALID_CURRENT_MOTHERBOARD_SERIAL_NUMBER = "INVALID_CURRENT_MOTHERBOARD_SERIAL_NUMBER";
		public static final String INVALID_CURRENT_CPU_SERIAL_NUMBER = "INVALID_CURRENT_CPU_SERIAL_NUMBER";
	}
}
