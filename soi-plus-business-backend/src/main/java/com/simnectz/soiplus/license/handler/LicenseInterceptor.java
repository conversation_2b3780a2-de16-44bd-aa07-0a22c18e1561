package com.simnectz.soiplus.license.handler;

import com.alibaba.fastjson2.JSON;
import com.simnectz.soiplus.business.system.domain.vo.Response;
import com.simnectz.soiplus.license.constant.LicenseConstant;
import com.simnectz.soiplus.license.verify.LicenseVerify;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

@Component
public class LicenseInterceptor implements HandlerInterceptor {
	@Override
	public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
		boolean verify = LicenseVerify.verify();
		if (verify) {
			return true;
		}
		String result = JSON.toJSONString(Response.failed(LicenseConstant.Code.INVALID_SERVER_AUTHORIZATION, LicenseConstant.Message.INVALID_SERVER_HARDWARE_INFORMATION));
		this.renderString(response, result);
		return false;
	}

	/**
	 * 将字符串渲染到客户端
	 *
	 * @param response 渲染对象
	 * @param string   待渲染的字符串
	 */
	private void renderString(HttpServletResponse response, String string) {
		try {
			response.setStatus(200);
			response.setContentType("application/json");
			response.setCharacterEncoding("utf-8");
			response.getWriter().print(string);
		} catch (IOException e) {
			throw new RuntimeException(e);
		}
	}
}
