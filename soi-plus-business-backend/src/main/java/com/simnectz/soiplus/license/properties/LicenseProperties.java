package com.simnectz.soiplus.license.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;

@Data
@Configuration
@ConfigurationProperties(prefix = "license")
public class LicenseProperties {
	private boolean enabled = false;
	private String issuedTime;
	private String expiryTime;
	private List<String> ipAddress;
	private List<String> macAddress;
	private String cpuSerial;
	private String mainBoardSerial;
	private String rule;
}
