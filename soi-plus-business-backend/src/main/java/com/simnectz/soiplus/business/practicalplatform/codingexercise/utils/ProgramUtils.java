package com.simnectz.soiplus.business.practicalplatform.codingexercise.utils;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

public class ProgramUtils {

    public static String compiler(String cmd) throws IOException, InterruptedException {
        StringBuilder compilerError = new StringBuilder();
        String line = null;
        Process process = Runtime.getRuntime().exec(cmd);
        BufferedReader compilerErrorReader = new BufferedReader(new InputStreamReader(process.getErrorStream(), StandardCharsets.UTF_8));
        while ((line = compilerErrorReader.readLine()) != null) {
            compilerError.append(line).append("\r\n");
        }
        compilerErrorReader.close();
        process.waitFor();
        return compilerError.toString();
    }

    public static Map<String, String> run(String cmd) throws IOException, InterruptedException {
        Map<String, String> result = new HashMap<>();
        StringBuilder runOutput = new StringBuilder();
        StringBuilder runError = new StringBuilder();
        String line = null;
        Process process = Runtime.getRuntime().exec(cmd);

        BufferedReader runOutputReader = new BufferedReader(new InputStreamReader(process.getInputStream(), StandardCharsets.UTF_8));
        while ((line = runOutputReader.readLine()) != null) {
            runOutput.append(line).append("\r\n");
        }
        result.put("runOutput", runOutput.toString());
        runOutputReader.close();

        BufferedReader runErrorReader = new BufferedReader(new InputStreamReader(process.getErrorStream(), StandardCharsets.UTF_8));
        while ((line = runErrorReader.readLine()) != null) {
            runError.append(line).append("\r\n");
        }
        result.put("runError", runError.toString());
        runErrorReader.close();

        process.waitFor();

        return result;
    }

}
