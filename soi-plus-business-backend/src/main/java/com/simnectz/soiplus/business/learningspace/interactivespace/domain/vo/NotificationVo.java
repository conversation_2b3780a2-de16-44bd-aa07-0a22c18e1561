package com.simnectz.soiplus.business.learningspace.interactivespace.domain.vo;

import com.simnectz.soiplus.business.learningspace.interactivespace.domain.enitity.LearnNotificationEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class NotificationVo extends LearnNotificationEntity implements Serializable {

    private static final long serialVersionUID = -2718363211453055577L;

    private String courseName;
    private String tutorName;
}
