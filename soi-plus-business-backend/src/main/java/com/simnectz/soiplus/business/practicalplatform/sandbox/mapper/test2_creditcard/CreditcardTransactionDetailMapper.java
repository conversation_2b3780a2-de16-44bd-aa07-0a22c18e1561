package com.simnectz.soiplus.business.practicalplatform.sandbox.mapper.test2_creditcard;

import com.mybatisflex.annotation.UseDataSource;
import com.simnectz.soiplus.business.practicalplatform.sandbox.domain.entity.test2_creditcard.CreditcardTransactionDetailEntity;
import com.simnectz.soiplus.business.system.mapper.BasicMapper;
import org.apache.ibatis.annotations.Mapper;

/**
 * CreditcardTransactionDetail data access layer interface
 */
@Mapper
@UseDataSource("creditcard")
public interface CreditcardTransactionDetailMapper extends BasicMapper<CreditcardTransactionDetailEntity> {
}
