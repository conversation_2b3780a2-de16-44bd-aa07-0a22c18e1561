package com.simnectz.soiplus.business.learningspace.subjectdefinition.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.If;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import com.simnectz.soiplus.business.learningspace.course.service.CourseService;
import com.simnectz.soiplus.business.learningspace.subjectdefinition.domain.dto.CreateSubjectDefinitionDto;
import com.simnectz.soiplus.business.learningspace.subjectdefinition.domain.dto.UpdateSubjectDefinitionDto;
import com.simnectz.soiplus.business.learningspace.subjectdefinition.domain.entity.SubjectDefinitionEntity;
import com.simnectz.soiplus.business.learningspace.subjectdefinition.domain.vo.SubjectDefinitionVo;
import com.simnectz.soiplus.business.learningspace.subjectdefinition.mapper.SubjectDefinitionMapper;
import com.simnectz.soiplus.business.learningspace.subjectdefinition.service.SubjectDefinitionService;
import com.simnectz.soiplus.business.learningspace.users.service.UserService;
import com.simnectz.soiplus.business.system.constant.ResponseConstant;
import com.simnectz.soiplus.business.system.constant.SystemConstant;
import com.simnectz.soiplus.business.system.domain.vo.Paginate;
import com.simnectz.soiplus.business.system.domain.vo.Response;
import com.simnectz.soiplus.business.system.exception.SystemException;
import com.simnectz.soiplus.business.system.utils.MessageUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import java.util.Set;

import static com.simnectz.soiplus.business.learningspace.course.domain.entity.table.CourseTableDef.course;
import static com.simnectz.soiplus.business.learningspace.subjectdefinition.domain.entity.table.SubjectDefinitionTableDef.subject_definition;

@Service
@RequiredArgsConstructor
public class SubjectDefinitionServiceImpl extends ServiceImpl<SubjectDefinitionMapper, SubjectDefinitionEntity> implements SubjectDefinitionService {

    private final CourseService courseService;
    private final UserService userService;

    @Transactional
    public Response<Paginate<SubjectDefinitionVo>> paginateEnquirySubjectDefinition(String keywords,
                                                                                    Integer currentPage,
                                                                                    Integer pageSize, String orderBy,
                                                                                    Boolean isAsc, Date startTime,
                                                                                    Date endTime, String courseId) {
          Page<SubjectDefinitionVo> subjectPage = mapper.paginateAs(
                currentPage,
                pageSize,
                this.queryChain().
                        select(subject_definition.all_columns,
                               course.course_name
                        )
                        .from(subject_definition)
                        .leftJoin(course)
                        .on(course.id.eq(subject_definition.course_id))
                        .where(subject_definition.subject_name.like(keywords, If::hasText))
                        .and(subject_definition.course_id.eq(courseId, If::hasText))
                        .and(subject_definition.create_time.gt(startTime, If::notNull))
                        .and(subject_definition.create_time.lt(endTime, If::notNull))
                        .orderBy(StrUtil.toUnderlineCase(orderBy), StrUtil.isNotBlank(orderBy) ? isAsc : null),
                SubjectDefinitionVo.class
        );

        for (SubjectDefinitionVo subjectDefinitionVo : subjectPage.getRecords()) {
            String creator = userService.getById(subjectDefinitionVo.getCreator()).getUsername();
            String updatedBy = userService.getById(subjectDefinitionVo.getLastUpdateCreator()).getUsername();
            subjectDefinitionVo.setCreator(creator);
            subjectDefinitionVo.setLastUpdateCreator(updatedBy);
        }

        return Response.success(new Paginate<>(subjectPage.getTotalRow(), subjectPage.getRecords()));

    }

    @Override
    @Transactional
    public Response<?> createSubjectDefinition(CreateSubjectDefinitionDto createSubjectDefinitionDto) {
        this.validationCourseExist(createSubjectDefinitionDto.getCourseId());
        this.validationSubjectDefinitionUnique(createSubjectDefinitionDto.getCourseId(),
                createSubjectDefinitionDto.getSubjectCode());
        SubjectDefinitionEntity subjectDefinitionEntity = BeanUtil.copyProperties(createSubjectDefinitionDto, SubjectDefinitionEntity.class);
        this.save(subjectDefinitionEntity);
        return Response.success();
    }

    @Transactional(readOnly = true)
    public void validationCourseExist(String courseId) {
        boolean exists = courseService.exists(this.queryChain()
                .select()
                .from(course)
                .where(course.id.eq(courseId))
        );
        if (!exists) {
            throw new SystemException(
                    ResponseConstant.Code.NOT_FOUND,
                    MessageUtils.message(ResponseConstant.Message.COURSE_NOT_FOUND)
            );
        }
    }

    @Transactional(readOnly = true)
    public void validationSubjectDefinitionUnique(String courseId, String subjectCode ) {
         boolean exists = this.exists(this.queryChain()
                .select()
                .from(subject_definition)
                .where(subject_definition.course_id.eq(courseId))
                .and(subject_definition.subject_code.eq(subjectCode))
        );
        if (exists) {
            throw new SystemException(
                    ResponseConstant.Code.BAD_REQUEST,
                    MessageUtils.message(ResponseConstant.Message.SUBJECT_ALREADY_EXISTS)
            );
        }
    }

    @Override
    @Transactional
    public Response<?> updateSubjectDefinition(UpdateSubjectDefinitionDto updateSubjectDefinitionDto) {
        this.validationCourseExist(updateSubjectDefinitionDto.getCourseId());
        if (ObjUtil.isNotEmpty(updateSubjectDefinitionDto.getId())) {
            Boolean exists = this.validationSubjectDefinitionExists(updateSubjectDefinitionDto.getId());
            if (!exists) {
                throw new SystemException(
                        ResponseConstant.Code.NOT_FOUND,
                        MessageUtils.message(ResponseConstant.Message.SUBJECT_NOT_FOUND)
                );
            }
        }
        this.updateChain()
                .set(subject_definition.course_id, updateSubjectDefinitionDto.getCourseId())
                .set(subject_definition.subject_name, updateSubjectDefinitionDto.getSubjectName())
                .set(subject_definition.subject_code, updateSubjectDefinitionDto.getSubjectCode())
                .set(subject_definition.subject_category, updateSubjectDefinitionDto.getSubjectCategory())
                .set(subject_definition.subject_objective, updateSubjectDefinitionDto.getSubjectObjective())
                .set(subject_definition.subject_description, updateSubjectDefinitionDto.getSubjectDescription())
                .set(subject_definition.subject_picture_url, updateSubjectDefinitionDto.getSubjectPictureUrl())
                .set(subject_definition.subject_hours, updateSubjectDefinitionDto.getSubjectHours())
                .set(subject_definition.subject_level, updateSubjectDefinitionDto.getSubjectLevel())
                .set(subject_definition.materials_url, updateSubjectDefinitionDto.getMaterialsUrl())
                .set(subject_definition.video_url, updateSubjectDefinitionDto.getVideoUrl())
                .set(subject_definition.assignment_url, updateSubjectDefinitionDto.getAssignmentUrl())
                .where(subject_definition.id.eq(updateSubjectDefinitionDto.getId()))
                .update();

        return Response.success();
    }

    @Transactional(readOnly = true)
    public Boolean validationSubjectDefinitionExists(String id) {
        return this.exists(this.queryChain()
                .select()
                .from(subject_definition)
                .where(subject_definition.id.eq(id))
        );
    }

    @Override
    @Transactional
    public Response<?> batchDeleteSubjectDefinition(Set<String> subjectDefinitionIds) {
        if (subjectDefinitionIds.isEmpty() || subjectDefinitionIds.size() == SystemConstant.ZERO) {
            throw new SystemException(
                    ResponseConstant.Code.BAD_REQUEST,
                    MessageUtils.message(ResponseConstant.Message.UNABLE_DELETE_SUBJECT)
            );
        }
        this.removeByIds(subjectDefinitionIds);
        return Response.success();
    }

    @Override
    public Response<List<SubjectDefinitionVo>> enquiryAllSubjects() {
        List<SubjectDefinitionVo> results = this.queryChain()
                .select(subject_definition.all_columns,
                        course.course_name
                        )
                .from(subject_definition)
                .leftJoin(course)
                .on(course.id.eq(subject_definition.course_id))
                .listAs(SubjectDefinitionVo.class);
        return Response.success(results);
    }

    @Transactional(readOnly = true)
    public Boolean validationSubjectExistsBySubjectCode(String subjectCode) {
        return this.exists(this.queryChain()
                .select(subject_definition.all_columns,
                        course.course_name
                )
                .from(subject_definition)
                .leftJoin(course)
                .on(course.id.eq(subject_definition.course_id))
                .where(subject_definition.subject_code.eq(subjectCode))
        );
    }

    @Transactional(readOnly = true)
    public Boolean validationSubjectExistsByCourseId(String courseId) {
        return this.exists(this.queryChain()
                .select(subject_definition.all_columns,
                        course.course_name
                )
                .from(subject_definition)
                .leftJoin(course)
                .on(course.id.eq(subject_definition.course_id))
                .where(subject_definition.course_id.eq(courseId))
        );
    }

    @Override
    public Response<SubjectDefinitionVo> findSubjectBySubjectCode(String subjectCode) {
        Boolean exists = this.validationSubjectExistsBySubjectCode(subjectCode);
        if (!exists) {
            throw new SystemException(
                    ResponseConstant.Code.NOT_FOUND,
                    MessageUtils.message(ResponseConstant.Message.SUBJECT_NOT_FOUND)
            );
        }
        SubjectDefinitionVo results = mapper.selectOneByQueryAs(
                this.queryChain()
                .select(subject_definition.all_columns,
                        course.course_name,
                        course.course_code,
                        course.id.as("course_id")
                )
                .from(subject_definition)
                .leftJoin(course)
                .on(course.id.eq(subject_definition.course_id))
                .where(subject_definition.subject_code.eq(subjectCode)),
                SubjectDefinitionVo.class);

            String creator = userService.getById(results.getCreator()).getUsername();
            String updatedBy = userService.getById(results.getLastUpdateCreator()).getUsername();
            results.setCreator(creator);
            results.setLastUpdateCreator(updatedBy);
        return Response.success(results);
    }

    @Override
    public Response<List<SubjectDefinitionVo>> findSubjectByCourseId(String courseId) {
        Boolean exists = this.validationSubjectExistsByCourseId(courseId);
        if (!exists) {
            throw new SystemException(
                    ResponseConstant.Code.NOT_FOUND,
                    MessageUtils.message(ResponseConstant.Message.SUBJECT_NOT_FOUND)
            );
        }
        List<SubjectDefinitionVo> results = this.queryChain()
                .select(subject_definition.all_columns,
                        course.course_name
                )
                .from(subject_definition)
                .leftJoin(course)
                .on(course.id.eq(subject_definition.course_id))
                .where(subject_definition.course_id.eq(courseId))
                .listAs(SubjectDefinitionVo.class);
        return Response.success(results);
    }

    @Override
    public Response<?> enquirySubjectByCourseIds(List<String> courseIds) {
        List<SubjectDefinitionVo> subjectVoList = mapper.selectListWithRelationsByQueryAs(
                this.queryChain()
                        .select(
                                subject_definition.all_columns,
                                course.course_name
                        )
                        .from(subject_definition)
                        .innerJoin(course)
                        .on(subject_definition.course_id.eq(course.id))
                        .where(subject_definition.course_id.in(courseIds)),
                SubjectDefinitionVo.class
        );
        return Response.success(subjectVoList);
    }
}

