package com.simnectz.soiplus.business.system.annotation;

import com.simnectz.soiplus.business.system.domain.Dictionary;
import com.simnectz.soiplus.business.system.enums.ColumnType;
import com.simnectz.soiplus.business.system.enums.Direction;
import com.simnectz.soiplus.business.system.enums.TransferType;
import org.apache.poi.ss.usermodel.HorizontalAlignment;
import org.apache.poi.ss.usermodel.IndexedColors;

import java.lang.annotation.*;
import java.math.RoundingMode;

/**
 * Annotation to configure properties for Excel export.
 */
@Documented
@Target({ElementType.FIELD})
@Retention(RetentionPolicy.RUNTIME)
public @interface ExcelColumn {

    /**
     * Column name in the Excel sheet.
     */
    String name();

    /**
     * Column order, default is 0.
     */
    int sort() default 0;

    /**
     * Date format for date fields.
     * Example: yyyy-MM-dd HH:mm:ss
     */
    String dateFormat() default "";

    /**
     * Precision for numeric values.
     * -1 means no specific scale.
     */
    int scale() default -1;

    /**
     * Specifies whether to use a thousand separator in numeric values. default is false.
     */
    boolean useThousandSeparator() default false;

    /**
     * Rounding mode, default is {@link RoundingMode#HALF_UP}.
     */
    RoundingMode roundingMode() default RoundingMode.HALF_UP;

    /**
     * Cell height, default is 0.
     */
    double height() default 0;

    /**
     * Cell width, default is 16.
     */
    double width() default 16;

    /**
     * Default value to use if the field is empty.
     */
    String defaultValue() default "";

    /**
     * Tooltip text for the cell.
     */
    String prompt() default "";

    /**
     * Whether the cell content should wrap.
     */
    boolean wrapText() default false;

    /**
     * Dropdown options source, default is none.
     */
    Class<? extends Dictionary> options() default Dictionary.class;

    /**
     * Whether to export the data for this field, default is true.
     */
    boolean isExportData() default true;

    /**
     * Whether to include this column in statistics, default is false.
     */
    boolean isStatistics() default false;

    /**
     * Type of the cell, default is {@link ColumnType#STRING}.
     */
    ColumnType columnType() default ColumnType.STRING;

    /**
     * Header background color, default is {@link IndexedColors#WHITE}.
     */
    IndexedColors headerBackgroundColor() default IndexedColors.WHITE;

    /**
     * Header font color, default is {@link IndexedColors#GREY_50_PERCENT}.
     */
    IndexedColors headerColor() default IndexedColors.GREY_50_PERCENT;

    /**
     * Cell background color, default is {@link IndexedColors#WHITE}.
     */
    IndexedColors backgroundColor() default IndexedColors.WHITE;

    /**
     * Cell font color, default is {@link IndexedColors#BLACK}.
     */
    IndexedColors color() default IndexedColors.BLACK;

    /**
     * Horizontal alignment for the cell, default is {@link HorizontalAlignment#CENTER}.
     */
    HorizontalAlignment align() default HorizontalAlignment.CENTER;

    /**
     * Type of data transfer, default is {@link TransferType#ALL}.
     */
    TransferType transferType() default TransferType.ALL;

    /**
     * The direction of the base collection, specifying whether the base data collection.
     * default is horizontal {@link Direction#HORIZONTAL}.
     */
    Direction sampleCollectionDirection() default Direction.HORIZONTAL;

    /**
     * The column size of the base collection, specifying the width of each column. default is 3.
     */
    int sampleCollectionSize() default 3;

    /**
     * Whether to split the base collection, specifying whether to divide the base data collection.
     */
    boolean splitSampleCollections() default false;

}
