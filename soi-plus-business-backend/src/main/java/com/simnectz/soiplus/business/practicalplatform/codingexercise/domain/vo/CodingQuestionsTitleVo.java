package com.simnectz.soiplus.business.practicalplatform.codingexercise.domain.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
public class CodingQuestionsTitleVo implements Serializable {

    private static final long serialVersionUID = 326130419918503433L;

    /**
     * Coding question id
     */
    private String id;

    /**
     * Coding question title
     */
    private String questionTitle;

    /**
     * Coding question title(en)
     */
    private String questionTitleEn;

}
