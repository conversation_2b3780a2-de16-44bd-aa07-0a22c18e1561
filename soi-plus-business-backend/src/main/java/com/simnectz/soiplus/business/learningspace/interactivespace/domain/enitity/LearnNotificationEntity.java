package com.simnectz.soiplus.business.learningspace.interactivespace.domain.enitity;

import com.mybatisflex.annotation.Table;
import com.simnectz.soiplus.business.system.domain.entity.BasicEntity;
import lombok.*;

import java.io.Serializable;

/**
 * 通知 实体类
 */
@Data
@With
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(value = "learn_notification")
@EqualsAndHashCode(callSuper = true)
public class LearnNotificationEntity extends BasicEntity implements Serializable {

    private static final long serialVersionUID = 8924475007134028901L;

    private String courseId;

    private String subjectId;

    private String title;

    private String content;

    private String userId;

    private Boolean isGrouped;

    private String groupList;

}
