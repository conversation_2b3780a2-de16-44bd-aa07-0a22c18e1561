package com.simnectz.soiplus.business.system.constant;

public class ResponseConstant {

    public static class Code {
        public static final Integer OPERATION_SUCCESS = 20000;
        public static final Integer BAD_REQUEST = 40000;
        public static final Integer UNAUTHORIZED = 40100;
        public static final Integer FORBIDDEN = 40300;
        public static final Integer NOT_FOUND = 40400;
        public static final Integer METHOD_NOT_ALLOWED = 40500;
        public static final Integer SERVER_ERROR = 50000;
        public static final Integer USER_NOT_ACTIVATED = 40001;
        public static final Integer USER_HAS_BEEN_BLACKLISTED = 40002;
        public static final Integer USER_ACTIVATED = 40003;
    }

    public static class Message {
        public static final String OPERATION_SUCCESS = "OPERATION_SUCCESS";
        public static final String UNAUTHORIZED = "UNAUTHORIZED";
        public static final String FORBIDDEN = "FORBIDDEN";
        public static final String METHOD_NOT_ALLOWED = "METHOD_NOT_ALLOWED";
        public static final String SERVER_ERROR = "SERVER_ERROR";
        public static final String DEFAULT_ROLE_NOT_FOUND = "DEFAULT_ROLE_NOT_FOUND";
        public static final String EMAIL_ALREADY_REGISTER = "EMAIL_ALREADY_REGISTER";
        public static final String TOKEN_REFRESH_FAILED = "TOKEN_REFRESH_FAILED";
        public static final String INVALID_ROLE_INFORMATION = "INVALID_ROLE_INFORMATION";
        public static final String USER_NOT_FOUND = "USER_NOT_FOUND";
        public static final String THE_DB_INFO_NOT_EXIST = "THE_DB_INFO_NOT_EXIST";
        public static final String THE_CONSUMER_NOT_EXIST = "THE_CONSUMER_NOT_EXIST";
        public static final String THE_DATABASE_NO_PERMISSION = "THE_DATABASE_NO_PERMISSION";
        public static final String THE_CONSUMER_HAVE_NOT_SANDBOX_DATA = "THE_CONSUMER_HAVE_NOT_SANDBOX_DATA";
        public static final String THE_USER_HAS_NOT_BEEN_ASSIGNED_SANDBOX_DATA = "THE_USER_HAS_NOT_BEEN_ASSIGNED_SANDBOX_DATA";
        public static final String ROLE_NOT_FOUND = "ROLE_NOT_FOUND";
        public static final String CHART_NOT_FOUND = "CHART_NOT_FOUND";
        public static final String VIEW_NOT_FOUND = "VIEW_NOT_FOUND";
        public static final String THE_CHART_NAME_ALREADY_EXISTS = "THE_CHART_NAME_ALREADY_EXISTS";
        public static final String ROLE_NAME_ALREADY_EXISTS = "ROLE_NAME_ALREADY_EXISTS";
        public static final String UNABLE_DELETE_ROLE = "UNABLE_DELETE_ROLE";
        public static final String USER_NOT_ACTIVATED = "USER_NOT_ACTIVATED";
        public static final String CAPTCHA_HAS_EXPIRED = "CAPTCHA_HAS_EXPIRED";
        public static final String CAPTCHA_ERROR = "CAPTCHA_ERROR";
        public static final String USER_ACTIVATION_FAILED = "USER_ACTIVATION_FAILED";
        public static final String GET_CAPTCHA_FAILED = "GET_CAPTCHA_FAILED";
        public static final String USER_ACTIVATED = "USER_ACTIVATED";
        public static final String USER_HAS_BEEN_BLACKLISTED = "USER_HAS_BEEN_BLACKLISTED";
        public static final String SEND_EMAIL_FAILED = "SEND_EMAIL_FAILED";
        public static final String RESET_PASSWORD_FAILED = "RESET_PASSWORD_FAILED";
        public static final String COURSE_CATEGORY_NOT_FOUND = "COURSE_CATEGORY_NOT_FOUND";
        public static final String COURSE_NOT_FOUND = "COURSE_NOT_FOUND";
        public static final String QUESTION_BANK_NOT_FOUND = "QUESTION_BANK_NOT_FOUND";
        public static final String COURSE_CATEGORY_ALREADY_EXISTS = "COURSE_CATEGORY_ALREADY_EXISTS";
        public static final String EXERCISE_ANSWER_ALREADY_EXISTS = "EXERCISE_ANSWER_ALREADY_EXISTS";
        public static final String COURSE_ALREADY_EXISTS = "COURSE_ALREADY_EXISTS";
        public static final String THE_GROUP_NOT_EXIST = "THE_GROUP_NOT_EXIST";
        public static final String UNABLE_DELETE_COURSE_CATEGORY = "UNABLE_DELETE_COURSE_CATEGORY";
        public static final String UNABLE_DELETE_COURSE = "UNABLE_DELETE_COURSE";
        public static final String UNABLE_DELETE_QUESTION_BANK = "UNABLE_DELETE_QUESTION_BANK";
        public static final String STU_PATH_NOT_FOUND = "STU_PATH_NOT_FOUND";
        public static final String STU_PATH_ALREADY_EXISTS = "STU_PATH_ALREADY_EXISTS";
        public static final String UNABLE_DELETE_STU_PATH = "UNABLE_DELETE_STU_PATH";
        public static final String REQUEST_PARAMETER_ERROR = "REQUEST_PARAMETER_ERROR";
        public static final String QUESTION_ALREADY_EXISTS = "QUESTION_ALREADY_EXISTS";
        public static final String QUESTION_NOT_FOUND = "QUESTION_NOT_FOUND";
        public static final String ANSWERS_NOT_FOUND = "ANSWERS_NOT_FOUND";
		public static final String SUBJECT_NOT_FOUND = "SUBJECT_NOT_FOUND";
        public static final String SUBJECT_ALREADY_EXISTS = "SUBJECT_ALREADY_EXISTS";
        public static final String UNABLE_DELETE_SUBJECT = "UNABLE_DELETE_SUBJECT";
        public static final String UNABLE_DELETE_WORK_SHEET = "UNABLE_DELETE_WORK_SHEET";
        public static final String FILE_IS_EMPTY = "FILE_IS_EMPTY";
        public static final String INVALID_FILE_SIZE  = "INVALID_FILE_SIZE";
        public static final String SAVE_FILE_FAILED  = "SAVE_FILE_FAILED";
        public static final String CREATED_DATABASE_FAILED  = "CREATED_DATABASE_FAILED";
        public static final String INVALID_UPLOAD_TABLES_TYPE = "INVALID_UPLOAD_TABLES_TYPE";
        public static final String WORKBOOK_IS_NULL_AND_EXCEL_READ_ERROR = "WORKBOOK_IS_NULL_AND_EXCEL_READ_ERROR";
        public static final String IMPORT_DATA_ERROR_AND_ILLEGAL_DATA = "IMPORT_DATA_ERROR_AND_ILLEGAL_DATA";
        public static final String ILLEGAL_TABLE_NAME = "ILLEGAL_TABLE_NAME";
        public static final String ILLEGAL_TABLE_FIELD = "ILLEGAL_TABLE_FIELD";
        public static final String TABLE_FIELD_IS_BLANK = "TABLE_FIELD_IS_BLANK";
        public static final String DATABASE_NOT_FOUND = "DATABASE_NOT_FOUND";
        public static final String THE_CLASS_ASSOCIATED_DOCUMENT_DATA = "THE_CLASS_ASSOCIATED_DOCUMENT_DATA";
        public static final String INVALID_SUBSCRIPTION_STATUS = "INVALID_SUBSCRIPTION_STATUS";
        public static final String INVALID_UPLOAD_FILE_SIZE = "INVALID_UPLOAD_FILE_SIZE";
        public static final String INVALID_ORDER_STATUS = "INVALID_ORDER_STATUS";
        public static final String WITHOUT_CREATE_WORKSPACE = "WITHOUT_CREATE_WORKSPACE";
        public static final String QUESTION_ID_CAN_NOT_BE_EMPTY = "QUESTION_ID_CAN_NOT_BE_EMPTY";
        public static final String COMPILATION_FAILED = "COMPILATION_FAILED";
        public static final String RUN_FAILED = "RUN_FAILED";
        public static final String RUN_SUCCESSFUL = "RUN_SUCCESSFUL";
        public static final String RUNNING_PROGRAMMING_LANGUAGE_CANNOT_BE_EMPTY = "RUNNING_PROGRAMMING_LANGUAGE_CANNOT_BE_EMPTY";
        public static final String RUNNING_CODE_CANNOT_BE_EMPTY = "RUNNING_CODE_CANNOT_BE_EMPTY";
        public static final String UNSUPPORTED_PROGRAMMING_LANGUAGE = "UNSUPPORTED_PROGRAMMING_LANGUAGE";
        public static final String CANNOT_FIND_TASK = "CANNOT_FIND_TASK";
        public static final String NO_INDUSTRY_DATA = "NO_INDUSTRY_DATA";
        public static final String API_NOT_EXIST = "API_NOT_EXIST";
        public static final String QUESTION_CREATOR_CANNOT_BE_EMPTY = "QUESTION_CREATOR_CANNOT_BE_EMPTY";
        public static final String INVALID_QUESTION_TITLE_EN = "INVALID_QUESTION_TITLE_EN";
        public static final String INVALID_QUESTION_TITLE_ZH = "INVALID_QUESTION_TITLE_ZH";
        public static final String INVALID_QUESTION_CATEGORY_ZH = "INVALID_QUESTION_CATEGORY_ZH";
        public static final String INVALID_QUESTION_CATEGORY_EN = "INVALID_QUESTION_CATEGORY_EN";
        public static final String INVALID_QUESTION_SUBCATEGORY_ZH = "INVALID_QUESTION_SUBCATEGORY_ZH";
        public static final String INVALID_QUESTION_SUBCATEGORY_EN = "INVALID_QUESTION_SUBCATEGORY_EN";
        public static final String INVALID_QUESTION_DESCRIPTION_ZH = "INVALID_QUESTION_DESCRIPTION_ZH";
        public static final String INVALID_QUESTION_DESCRIPTION_EN = "INVALID_QUESTION_DESCRIPTION_EN";
        public static final String INVALID_QUESTION_INITIAL_CODE = "INVALID_QUESTION_INITIAL_CODE";
        public static final String REPEAT_QUESTION_TITLE_ZH= "REPEAT_QUESTION_TITLE_ZH";
        public static final String REPEAT_QUESTION_TITLE_EN= "REPEAT_QUESTION_TITLE_EN";
        public static final String DELETE_FAILED= "DELETE_FAILED";
        public static final String FILE_SIZE_CANNOT_GRATE_THAN_5GB= "FILE_SIZE_CANNOT_GRATE_THAN_5GB";
        public static final String UPLOAD_FILE_FAILED= "UPLOAD_FILE_FAILED";
        public static final String CREATE_FAILED= "CREATE_FAILED";
        public static final String UPLOAD_FAILED= "UPLOAD_FAILED";
        public static final String CLASS_NAME_ERROR= "CLASS_NAME_ERROR";
        public static final String FINAME_NOT_EXIST= "FINAME_NOT_EXIST";
        public static final String FI_INFO_NOT_EXIST= "FI_INFO_NOT_EXIST";
        public static final String GRANT_TYPE_IS_REQUIRED= "GRANT_TYPE_IS_REQUIRED";
        public static final String CLIENT_SECRET_IS_REQUIRED= "CLIENT_SECRET_IS_REQUIRED";
        public static final String CLIENT_ID_IS_REQUIRED= "CLIENT_ID_IS_REQUIRED";
        public static final String INVALID_GRANT_TYPE = "INVALID_GRANT_TYPE";
        public static final String INVALID_CLIENT_SECRET= "INVALID_CLIENT_SECRET";
        public static final String INVALID_CLIENT_ID= "INVALID_CLIENT_ID";
        public static final String REPEAT_CLIENT_ID= "REPEAT_CLIENT_ID";
        public static final String CLIENT_ID_NOT_EXIST= "CLIENT_ID_NOT_EXIST";
        public static final String DATA_NOT_EXIST= "DATA_NOT_EXIST";
        public static final String MARKDOWN_DOCUMENT_NOT_FOUND= "MARKDOWN_DOCUMENT_NOT_FOUND";
        public static final String IMPORT_FAILED= "IMPORT_FAILED";
        public static final String DATA_TABLE_FIELD_IS_REQUIRED= "DATA_TABLE_FIELD_IS_REQUIRED";
        public static final String DATA_FIELD_NOT_MATCH= "DATA_FIELD_NOT_MATCH";
        public static final String DATA_TABLE_NOT_FOUND= "DATA_TABLE_NOT_FOUND";
        public static final String DATA_TABLE_NOT_FOUND_PLEASE_TRY_AGAIN= "DATA_TABLE_NOT_FOUND_PLEASE_TRY_AGAIN";

    }

    private ResponseConstant() {
    }

}
