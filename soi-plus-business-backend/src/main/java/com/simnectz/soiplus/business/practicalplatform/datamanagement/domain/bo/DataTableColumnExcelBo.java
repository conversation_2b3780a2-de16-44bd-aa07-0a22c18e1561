package com.simnectz.soiplus.business.practicalplatform.datamanagement.domain.bo;

import com.simnectz.soiplus.business.learningspace.subjectdefinition.enums.EnumValidate;
import com.simnectz.soiplus.business.learningspace.subjectdefinition.enums.ValidationEnum;
import com.simnectz.soiplus.business.system.annotation.Excel;
import com.simnectz.soiplus.business.system.annotation.ExcelColumn;
import com.simnectz.soiplus.business.system.enums.ColumnType;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.Getter;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.hibernate.validator.constraints.Range;
import org.hibernate.validator.group.GroupSequenceProvider;
import org.hibernate.validator.spi.group.DefaultGroupSequenceProvider;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Data
@NoArgsConstructor
@AllArgsConstructor
@Excel(fileName = "data-table-desc")
@GroupSequenceProvider(DataTableColumnExcelBo.DataTableColumnExcelSequence.class)
public class DataTableColumnExcelBo implements Serializable {

    private static final long serialVersionUID = 8388209209726589714L;

    @NotBlank(message = "{FILED_NAME_IS_REQUIRED}")
    @ExcelColumn(name = "Field Name")
    private String fieldName;

    @NotBlank(message = "{FILED_TYPE_IS_REQUIRED}")
    @ExcelColumn(name = "Field Type")
    @ValidationEnum(enumClass = FieldType.class, message = "{INVALID_FIELD_TYPE}")
    private String fieldType;

    @ExcelColumn(name = "Max Length", columnType = ColumnType.NUMERIC)
    @NotNull(message = "{MAX_LENGTH_IS_REQUIRED}", groups = {TinyintGroup.class, IntGroup.class, BigintGroup.class, DecimalGroup.class, VarcharGroup.class})
    @Range(min = 1, max = 1, message = "{INVALID_MAX_LENGTH_SIZE_TINYINT}", groups = {TinyintGroup.class})
    @Range(min = 1, max = 11, message = "{INVALID_MAX_LENGTH_SIZE}", groups = {IntGroup.class})
    @Range(min = 1, max = 20, message = "{INVALID_MAX_LENGTH_SIZE}", groups = {BigintGroup.class})
    @Range(min = 1, max = 65, message = "{INVALID_MAX_LENGTH_SIZE}", groups = {DecimalGroup.class})
    @Range(min = 1, max = 65535, message = "{INVALID_MAX_LENGTH_SIZE}", groups = {VarcharGroup.class})
    private Integer maxLength;

    @ExcelColumn(name = "Scale", columnType = ColumnType.NUMERIC)
    @NotNull(message = "{SCALE_IS_REQUIRED}", groups = {DecimalGroup.class})
    @Range(min = 0, max = 30, message = "{INVALID_SCALE_SIZE}", groups = {DecimalGroup.class})
    private Integer scale;

    @ExcelColumn(name = "Example")
    private String example;

    @ExcelColumn(name = "Description")
    private String description;

    @Getter
    @AllArgsConstructor
    private enum FieldType implements EnumValidate {

        TINYINT("tinyint"),
        INT("int"),
        BIGINT("bigint"),
        DECIMAL("decimal"),
        VARCHAR("varchar"),
        DATE("date"),
        DATETIME("datetime"),
        ;

        private final String value;
    }

    public static class DataTableColumnExcelSequence implements DefaultGroupSequenceProvider<DataTableColumnExcelBo> {
        @Override
        public List<Class<?>> getValidationGroups(DataTableColumnExcelBo bean) {
            List<Class<?>> groups = new ArrayList<>();

            groups.add(DataTableColumnExcelBo.class);

            addValidateGroup(bean, groups);

            return groups;
        }
    }

    private static void addValidateGroup(DataTableColumnExcelBo bean, List<Class<?>> groups) {
        if (Objects.nonNull(bean) && StringUtils.isNotBlank(bean.getFieldType())) {
            if (StringUtils.equalsAnyIgnoreCase("tinyint", bean.getFieldType())) {
                groups.add(TinyintGroup.class);
            }
            if (StringUtils.equalsAnyIgnoreCase("int", bean.getFieldType())) {
                groups.add(IntGroup.class);
            }
            if (StringUtils.equalsAnyIgnoreCase("bigint", bean.getFieldType())) {
                groups.add(BigintGroup.class);
            }
            if (StringUtils.equalsAnyIgnoreCase("varchar", bean.getFieldType())) {
                groups.add(VarcharGroup.class);
            }
            if (StringUtils.equalsAnyIgnoreCase("decimal", bean.getFieldType())) {
                groups.add(DecimalGroup.class);
            }
        }
    }

    public String getFieldName() {
        return fieldName.trim();
    }

    private interface TinyintGroup {
    }

    private interface IntGroup {
    }

    private interface BigintGroup {
    }

    private interface DecimalGroup {
    }

    private interface VarcharGroup {
    }

}


