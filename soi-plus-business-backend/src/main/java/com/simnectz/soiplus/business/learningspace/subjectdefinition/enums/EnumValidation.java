package com.simnectz.soiplus.business.learningspace.subjectdefinition.enums;

import javax.validation.ConstraintValidator;
import javax.validation.ConstraintValidatorContext;

public class EnumValidation implements ConstraintValidator<ValidationEnum, String> {

    private Class<? extends EnumValidate> enumClass;

    @Override
    public void initialize(ValidationEnum constraintAnnotation) {
        enumClass = constraintAnnotation.enumClass();
    }

    @Override
    public boolean isValid(String value, ConstraintValidatorContext context) {
        if (value == null) {
            return true;
        }

        EnumValidate[] enumValues = enumClass.getEnumConstants();
        for (EnumValidate enumValue : enumValues) {
            if (enumValue.getValue().equals(value)) {
                return true;
            }
        }

        return false;
    }

}
