package com.simnectz.soiplus.business.practicalplatform.documentlibrary.domain.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import lombok.*;

import java.io.Serializable;
import java.util.Date;

@Data
@With
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
@Table(value = "document_file_upload")
public class DocumentFileUploadEntity implements Serializable {

    private static final long serialVersionUID = -5280278397929686850L;

    @Id(keyType = KeyType.Auto)
    private Integer id;

    private String ossKey;

    private String originalName;

    private String size;

    private String uploadId;

    private String description;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date uploadDate;

    private String userId;

    private String playUrl;
}
