package com.simnectz.soiplus.business.practicalplatform.sandbox.domain.entity.test2_deposit;


import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.Table;
import lombok.*;

@Data
@With
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(value = "currentaccountmaster", dataSource = "deposit")
@EqualsAndHashCode()
public class CurrentAccountMasterEntity {
    @Id
    @Column(value = "ID")
    private String id;
    @Column(value = "CountryCode")
    private String countryCode;
    @Column(value = "ClearingCode")
    private String clearingCode;
    @Column(value = "BranchCode")
    private String branchCode;
    @Column(value = "SandBoxId")
    private String sandBoxId;
    @Column(value = "CustomerNumber")
    private String customerNumber;
    @Column(value = "AccountNumber")
    private String accountNumber;
    @Column(value = "AccountStatus")
    private String accountStatus;
    @Column(value = "CurrencyCode")
    private String currencyCode;
    @Column(value = "LedgeBalance")
    private String ledgeBalance;
    @Column(value = "HoldingBalance")
    private String holdingBalance;
    @Column(value = "AvailableBalance")
    private String availableBalance;
    @Column(value = "LastUpdatedDate")
    private String lastUpdatedDate;
    @Column(value = "ChequeBookType")
    private String chequeBookType;
    @Column(value = "ChequeBookSize")
    private String chequeBookSize;
    @Column(value = "CreateDate")
    private String createDate;

}
