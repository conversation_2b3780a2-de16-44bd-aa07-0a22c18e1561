package com.simnectz.soiplus.business.learningspace.pathmaintenance.resource;

import com.simnectz.soiplus.business.learningspace.pathmaintenance.domain.dto.EnquiryStuPathDto;
import com.simnectz.soiplus.business.learningspace.pathmaintenance.domain.dto.StuPathDto;
import com.simnectz.soiplus.business.learningspace.pathmaintenance.domain.dto.StuStudyInfoDto;
import com.simnectz.soiplus.business.learningspace.pathmaintenance.domain.vo.StuPathVo;
import com.simnectz.soiplus.business.learningspace.pathmaintenance.service.StuPathService;
import com.simnectz.soiplus.business.system.domain.vo.Paginate;
import com.simnectz.soiplus.business.system.domain.vo.Response;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Set;

@RestController
@RequestMapping("v1/learn/path")
@RequiredArgsConstructor
public class StuPathResource {

    private final StuPathService stuPathService;

    @GetMapping("subject-recommend")
    public ResponseEntity<Response<?>> getSubjectRecommend() {
        return ResponseEntity.ok(stuPathService.getSubjectRecommend());
    }

    @GetMapping
    public ResponseEntity<Response<Paginate<StuPathVo>>> paginateEnquiryStuPath(EnquiryStuPathDto stuPathPaginate) {
        return ResponseEntity.ok(stuPathService.paginateEnquiryStuPath(
                stuPathPaginate.getKeywords(),
                stuPathPaginate.getCurrentPage(),
                stuPathPaginate.getPageSize(),
                stuPathPaginate.getOrderBy(),
                stuPathPaginate.getIsAsc(),
                stuPathPaginate.getStartTime(),
                stuPathPaginate.getEndTime(),
                stuPathPaginate.getCourseCode(),
                stuPathPaginate.getCourseCategoryId()
        ));
    }

    @PostMapping("subscription")
    public ResponseEntity<Response<?>> createStuPath(@RequestBody @Validated List<StuPathDto> stuPathDtoList) {
        return ResponseEntity.ok(stuPathService.createStuPath(stuPathDtoList));
    }

    @PutMapping("subscription-status")
    public ResponseEntity<Response<?>> updateStuPathSubscriptionStatus(@RequestBody @Validated StuPathDto stuPathDto) {
        return ResponseEntity.ok(stuPathService.updateStuPathSubscriptionStatus(stuPathDto));
    }

    @PutMapping("first-study")
    public ResponseEntity<Response<?>> putFirstStudy(@RequestBody @Validated StuStudyInfoDto stuStudyInfoDto) {
        return ResponseEntity.ok(stuPathService.putFirstStudy(stuStudyInfoDto));
    }

    @PutMapping("finished-study")
    public ResponseEntity<Response<?>> putFinishedStudy(@RequestBody @Validated StuStudyInfoDto stuStudyInfoDto) {
        return ResponseEntity.ok(stuPathService.putFinishedStudy(stuStudyInfoDto));
    }

    @DeleteMapping
    public ResponseEntity<Response<?>> batchDeleteStuPath(@RequestParam("ids") Set<String> stuPathIds) {
        return ResponseEntity.ok(stuPathService.batchDeleteStuPath(stuPathIds));
    }

}
