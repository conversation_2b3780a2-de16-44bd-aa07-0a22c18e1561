package com.simnectz.soiplus.business.practicalplatform.datamanagement.service.impl;

import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.mybatisflex.core.query.QueryWrapper;
import com.simnectz.soiplus.business.learningspace.users.domain.entity.UserEntity;
import com.simnectz.soiplus.business.learningspace.users.service.UserService;
import com.simnectz.soiplus.business.practicalplatform.apimarket.domain.model.MakeSwagView;
import com.simnectz.soiplus.business.practicalplatform.apimarket.service.impl.APIMarketServiceImpl;
import com.simnectz.soiplus.business.practicalplatform.dataanalysis.datavisualization.domain.entity.SourceEntity;
import com.simnectz.soiplus.business.practicalplatform.dataanalysis.datavisualization.domain.entity.table.SourceTableDef;
import com.simnectz.soiplus.business.practicalplatform.dataanalysis.datavisualization.mapper.SourceMapper;
import com.simnectz.soiplus.business.practicalplatform.datamanagement.domain.bo.DataTableColumnExcelBo;
import com.simnectz.soiplus.business.practicalplatform.datamanagement.domain.entity.DbInfoEntity;
import com.simnectz.soiplus.business.practicalplatform.datamanagement.domain.entity.TableInfoEntity;
import com.simnectz.soiplus.business.practicalplatform.datamanagement.mapper.DbInfoMapper;
import com.simnectz.soiplus.business.practicalplatform.datamanagement.mapper.TableInfoMapper;
import com.simnectz.soiplus.business.practicalplatform.datamanagement.service.DataManagementService;
import com.simnectz.soiplus.business.practicalplatform.sandbox.model.TableModel;
import com.simnectz.soiplus.business.system.constant.ResponseConstant;
import com.simnectz.soiplus.business.system.domain.bo.ExcelHeaderBo;
import com.simnectz.soiplus.business.system.domain.vo.Response;
import com.simnectz.soiplus.business.system.enums.ContentType;
import com.simnectz.soiplus.business.system.exception.SystemException;
import com.simnectz.soiplus.business.system.utils.*;
import io.swagger.models.Swagger;
import io.swagger.util.Json;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.IOUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.BufferedOutputStream;
import java.io.File;
import java.io.IOException;
import java.net.MalformedURLException;
import java.net.URLEncoder;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.simnectz.soiplus.business.learningspace.users.domain.entity.table.UserTableDef.user;
import static com.simnectz.soiplus.business.practicalplatform.datamanagement.domain.entity.table.DbInfoTableDef.db_info;
import static com.simnectz.soiplus.business.practicalplatform.datamanagement.domain.entity.table.TableInfoTableDef.table_info;

@Slf4j
@Service
@RequiredArgsConstructor
public class DataManagementServiceImpl implements DataManagementService {
    private static final Logger logger = LoggerFactory.getLogger(DataManagementServiceImpl.class);

    private final DbInfoMapper dbInfoMapper;
    private final SourceMapper sourceMapper;
    private final TableInfoMapper tableInfoMapper;
    private final UserService userService;
    private final APIMarketServiceImpl apiMarketServiceImpl;
    @Value("${system.upload.sql-file-path}")
    private String filePath;
    @Value("${system.upload.table-info-file-path}")
    private String tableInfoPath;
    @Value("${importdb.dbUrl}")
    private String dbUrl;
    @Value("${mybatis-flex.datasource.default.username}")
    private String dbuserName;
    @Value("${mybatis-flex.datasource.default.password}")
    private String dbPassword;

    @Override
    public Response<?> getUserDb(String userID) {
        DbInfoEntity dbInfo = dbInfoMapper.selectOneWithRelationsByQueryAs(
                QueryWrapper.create()
                        .select(db_info.all_columns)
                        .from(db_info)
                        .where(db_info.user_id.eq(userID)),
                DbInfoEntity.class
        );
        if (ObjUtil.isNull(dbInfo)) {
            throw new SystemException(ResponseConstant.Code.NOT_FOUND, MessageUtils.message(ResponseConstant.Message.THE_DB_INFO_NOT_EXIST));
        }
        return Response.success(true);
    }

    @Override
    @Transactional
    public Response<?> uploadExcelFileAndCreateTable(MultipartFile file, String userId, String type) {
        // 校验文件
        validateExcel(file);
        // 保存文件
        saveExcel(file, this.filePath);
        // 读取表头
        Map<String, Integer> headerMap = getExcelHeader(file);
        // 获取数据对应的表名称
        // !!! 如果是从机器学习上传的，数据表名称为 "prediction_result"
        String tableName;
        if ("aml".equals(type)) {
            tableName = "prediction_result";
        } else {
            try {
                tableName = ExcelUtils.of(DataTableColumnExcelBo.class).getSheetName(file.getInputStream(), 0);
            } catch (IOException e) {
                log.error("Failed get sheet name: {}", e.getMessage(), e);
                throw new SystemException(ResponseConstant.Code.SERVER_ERROR, e.getMessage());
            }
        }
        // 获取数据表字段详情信息
        List<DataTableColumnExcelBo> dataTableColumnInfo = getDataTableInfo(userId, tableName);
        // 获取 excel 的表头
        List<String> dataFields = new ArrayList<>(headerMap.keySet());
        if (CollectionUtils.isEmpty(dataTableColumnInfo)) {
//            dataTableColumnInfo = dataFields.stream().map(dataField -> {
//                DataTableColumnExcelBo columnExcelBo = new DataTableColumnExcelBo();
//                columnExcelBo.setFieldName(dataField);
//                columnExcelBo.setFieldType("text");
//                return columnExcelBo;
//            }).collect(Collectors.toList());
//            createDataTable(userId, tableName, dataTableColumnInfo);
            throw new SystemException(ResponseConstant.Code.NOT_FOUND, MessageUtils.message(ResponseConstant.Message.DATA_TABLE_NOT_FOUND_PLEASE_TRY_AGAIN));
        }

        // 获取数据表的所有字段
        List<String> tableColumns = dataTableColumnInfo.stream().map(DataTableColumnExcelBo::getFieldName).collect(Collectors.toList());
        // excel 的表头和数据表的字段不匹配
        if (!CollectionUtils.isEqualCollection(tableColumns, dataFields)) {
            Collection<String> nonIntersection = CollectionUtils.disjunction(tableColumns, dataFields);
            throw new SystemException(ResponseConstant.Code.BAD_REQUEST, MessageUtils.message(ResponseConstant.Message.DATA_FIELD_NOT_MATCH, String.join(", ", nonIntersection)));
        }
        // 获取 excel 中的所有数据
        List<ExcelHeaderBo> excelHeaders = new ArrayList<>();
        for (DataTableColumnExcelBo columnExcelBo : dataTableColumnInfo) {
            String fieldName = columnExcelBo.getFieldName();
            String fieldType = columnExcelBo.getFieldType();
            Integer colIndex = headerMap.get(fieldName);
            excelHeaders.add(
                    ExcelHeaderBo.builder()
                            .fieldName(fieldName)
                            .fieldType(fieldType)
                            .colIndex(colIndex)
                            .build()
            );
        }
        List<Map<String, String>> data = getData(file, excelHeaders);

        DbInfoEntity dbInfo = getDatabaseInfo(userId);
        TableModel tableModel = new TableModel();
        tableModel.setTableName(tableName);
        tableModel.setColumns(tableColumns);
        tableModel.setLineData(data);

        try {
            DataSourceUtils.generateInsertSql(dbInfo.getDbName(), tableModel);
        } catch (Exception e) {
            log.error(e.getMessage(), e);
            throw new SystemException(ResponseConstant.Code.SERVER_ERROR, e.getMessage());
        }

        return Response.success();
    }

    private List<DataTableColumnExcelBo> getDataTableInfo(String userId, String tableName) {
        TableInfoEntity tableInfo = tableInfoMapper.selectOneByCondition(
                table_info.user_id.eq(userId).and(table_info.table_name.eq(tableName))
        );
        if (Objects.isNull(tableInfo)) {
            return new ArrayList<>();
        }
        String description = tableInfo.getDescription();
        return JSONArray.parseArray(description, DataTableColumnExcelBo.class);
    }

    private static List<Map<String, String>> getData(MultipartFile file, List<ExcelHeaderBo> excelHeaders) {
        List<Map<String, String>> data;
        try {
            data = ExcelUtils.of().getSampleData(file.getInputStream(), excelHeaders);
        } catch (IOException exception) {
            log.error(exception.getMessage(), exception);
            throw new SystemException(ResponseConstant.Code.BAD_REQUEST, exception.getMessage());
        }
        return data;
    }

    private static Map<String, Integer> getExcelHeader(MultipartFile file) {
        Map<String, Integer> excelHeader;
        try {
            excelHeader = ExcelUtils.of().getExcelHeader(file.getInputStream());
        } catch (IOException exception) {
            log.error(exception.getMessage(), exception);
            throw new SystemException(ResponseConstant.Code.BAD_REQUEST, exception.getMessage());
        }
        return excelHeader;
    }

    private static List<DataTableColumnExcelBo> getDataTableColumns(MultipartFile file) {
        List<DataTableColumnExcelBo> dataTable;
        try {
            dataTable = ExcelUtils.of(DataTableColumnExcelBo.class).importData(file.getInputStream());
        } catch (IOException e) {
            log.error("Read file input stream error: {}", e.getMessage(), e);
            throw new SystemException(ResponseConstant.Code.BAD_REQUEST, ResponseConstant.Message.SERVER_ERROR);
        }
        return dataTable;
    }

    private DbInfoEntity getDatabaseInfo(String userId) {
        DbInfoEntity dbInfo = dbInfoMapper.selectOneWithRelationsByQueryAs(
                QueryWrapper.create()
                        .select(db_info.all_columns)
                        .from(db_info)
                        .where(db_info.user_id.eq(userId)),
                DbInfoEntity.class
        );
        if (ObjUtil.isNull(dbInfo)) {
            throw new SystemException(ResponseConstant.Code.BAD_REQUEST, userId + MessageUtils.message(ResponseConstant.Message.CREATED_DATABASE_FAILED));
        }
        return dbInfo;
    }

    private void saveExcel(MultipartFile file, String filePath) {
        String fileName = file.getOriginalFilename();
        File filePaths = new File(filePath);
        File dest = new File(filePaths.getAbsolutePath() + File.separator + fileName);
        // 创建父目录
        if (!dest.getParentFile().exists()) {
            boolean mkdir = dest.getParentFile().mkdir();
            if (mkdir) {
                logger.warn("Create dir: '{}'", dest.getParentFile().getAbsolutePath());
            }
        }

        // 删除旧文件
        if (!dest.delete()) {
            logger.warn("Failed delete file: '{}'", dest.getAbsolutePath());
        }

        // 保存新文件
        try {
            file.transferTo(dest);
        } catch (Exception e) {
            log.error("Failed save file '{}' to '{}'", fileName, dest.getAbsolutePath());
            throw new SystemException(ResponseConstant.Code.SERVER_ERROR, e.getMessage());
        }
    }

    private static void validateExcel(MultipartFile file) {
        // 校验文件是否为空
        if (Objects.isNull(file) || file.isEmpty()) {
            throw new SystemException(ResponseConstant.Code.BAD_REQUEST, MessageUtils.message(ResponseConstant.Message.FILE_IS_EMPTY));
        }
        String fileName = file.getOriginalFilename();
        // 校验文件类型
        try {
            String mimeType = FileUtils.getFileMimeType(file.getBytes(), fileName);
            if (!ContentType.XLS.getValue().equals(mimeType) && !ContentType.XLSX.getValue().equals(mimeType)) {
                throw new SystemException(ResponseConstant.Code.BAD_REQUEST, MessageUtils.message(ResponseConstant.Message.INVALID_UPLOAD_TABLES_TYPE));
            }
        } catch (IOException e) {
            log.error("Get file bytes error: '{}'", fileName, e);
            throw new SystemException(ResponseConstant.Code.SERVER_ERROR, e.getMessage());
        }

        // 校验文件大小
        int size = (int) file.getSize();
        if (size > 52428800) {
            throw new SystemException(ResponseConstant.Code.BAD_REQUEST, MessageUtils.message(ResponseConstant.Message.INVALID_FILE_SIZE));
        }
    }

    @Override
    public Response<?> getTableListByUserId(String userId) {
        DbInfoEntity dbInfoPublic = dbInfoMapper.selectOneWithRelationsByQueryAs(
                QueryWrapper.create()
                        .select(db_info.all_columns)
                        .from(db_info)
                        .where(db_info.public_source.eq(1)),
                DbInfoEntity.class
        );

        DbInfoEntity dbInfo = dbInfoMapper.selectOneWithRelationsByQueryAs(
                QueryWrapper.create()
                        .select(db_info.all_columns)
                        .from(db_info)
                        .where(db_info.user_id.eq(userId)),
                DbInfoEntity.class
        );
        JSONObject res = new JSONObject();
        if (ObjUtil.isNotNull(dbInfo)) {
            String tableList = dbInfo.getTableList();
            JSONArray array = JSONArray.parseArray(tableList);
            res.put("self", array);
        } else {
            res.put("self", new JSONObject());
        }
        if (ObjUtil.isNotNull(dbInfoPublic)) {
            String tableList2 = dbInfoPublic.getTableList();
            JSONArray array2 = JSONArray.parseArray(tableList2);
            res.put("public", array2);
        } else {
            res.put("public", new JSONObject());
        }
        return Response.success(res);
    }

    @Override
    @Transactional
    public Response<?> uploadTableInfoFileAndSave(MultipartFile file, String userId, String type) {
        // 校验文件
        validateExcel(file);
        // 保存文件
        saveExcel(file, tableInfoPath);
        // 读取数据表信息
        List<DataTableColumnExcelBo> dataTable = getDataTableColumns(file);
        if (CollectionUtils.isEmpty(dataTable)) {
            throw new SystemException(ResponseConstant.Code.BAD_REQUEST, ResponseConstant.Message.DATA_TABLE_FIELD_IS_REQUIRED);
        }
        String tableName;
        // !!! 如果是从机器学习上传的，数据表名称为 "prediction_result"
        if ("aml".equals(type)) {
            tableName = "prediction_result";
        } else {
            try {
                tableName = ExcelUtils.of(DataTableColumnExcelBo.class).getSheetName(file.getInputStream(), 0);
            } catch (IOException e) {
                log.error("Failed get sheet name: {}", e.getMessage(), e);
                throw new SystemException(ResponseConstant.Code.SERVER_ERROR, e.getMessage());
            }
        }

        createDataTable(userId, tableName, dataTable);

        return Response.success();
    }

    private void createDataTable(String userId, String tableName, List<DataTableColumnExcelBo> dataTable) {
        boolean b = regTableName(tableName);
        if (!b) {
            throw new SystemException(ResponseConstant.Code.BAD_REQUEST, tableName + MessageUtils.message(ResponseConstant.Message.ILLEGAL_TABLE_NAME));
        }

        TableInfoEntity tableInfo = new TableInfoEntity();
        tableInfo.setUserId(userId);
        tableInfo.setTableName(tableName);
        tableInfo.setCreateDate(new Date());
        tableInfo.setDescription(JSONArray.toJSONString(dataTable));
        tableInfo.setPublicSource(0);
        tableInfoMapper.deleteByCondition(
                table_info.user_id.eq(userId).and(table_info.table_name.eq(tableName))
        );
        tableInfoMapper.insert(tableInfo);

        // 获取数据库信息
        DbInfoEntity dbInfo = getDatabaseInfo(userId);

        try {
            DataSourceUtils.createTableSql(dbInfo.getDbName(), tableName, dataTable);
        } catch (SQLException exception) {
            log.error("Create data table failed: {}", exception.getMessage(), exception);
            throw new SystemException(ResponseConstant.Code.SERVER_ERROR, exception.getMessage());
        }

        String tableLists = dbInfo.getTableList();
        JSONArray jsonArray = new JSONArray();
        if (tableLists != null) {
            jsonArray = JSON.parseArray(tableLists);
        }
        if (!jsonArray.contains(tableName)) {
            jsonArray.add(tableName);
        }
        dbInfo.setTableList(jsonArray.toString());
        dbInfoMapper.insertOrUpdateSelective(dbInfo);

        JSONObject obj = new JSONObject();
        obj.put("url", String.format(dbUrl, dbInfo.getDbName()));
        obj.put("password", dbPassword);
        obj.put("username", dbuserName);
        SourceEntity source = new SourceEntity();
        source.setUserId(dbInfo.getUserId());
        source.setName(dbInfo.getDbName());
        source.setPublicSource(false);
        source.setType(false);
        source.setCreateTime(new Date());
        source.setUpdateTime(new Date());
        source.setConfig(obj.toJSONString());

        SourceEntity sourceRes = sourceMapper.selectOneWithRelationsByQueryAs(
                QueryWrapper.create()
                        .select(SourceTableDef.source.all_columns)
                        .from(SourceTableDef.source)
                        .where(SourceTableDef.source.user_id.eq(dbInfo.getUserId()))
                        .and(SourceTableDef.source.name.eq(dbInfo.getDbName())),
                SourceEntity.class
        );
        if (ObjUtil.isNull(sourceRes)) {
            sourceMapper.insertOrUpdate(source);
        }
    }

    @Override
    public Response<?> getDataByUserIdAndTableName(String userId, String tableName, String publicSource) {
        DbInfoEntity dbInfo;
        if (publicSource.equals("1")) {
            dbInfo = dbInfoMapper.selectOneWithRelationsByQueryAs(
                    QueryWrapper.create()
                            .select(db_info.all_columns)
                            .from(db_info)
                            .where(db_info.public_source.eq(1)),
                    DbInfoEntity.class
            );
        } else {
            dbInfo = dbInfoMapper.selectOneWithRelationsByQueryAs(
                    QueryWrapper.create()
                            .select(db_info.all_columns)
                            .from(db_info)
                            .where(db_info.user_id.eq(userId)),
                    DbInfoEntity.class
            );
        }
        if (ObjUtil.isNotNull(dbInfo)) {
            String dbName = dbInfo.getDbName();
            String sql = "select * from " + tableName + " limit 50";
            List<Map<String, Object>> mapList = null;
            try {
                mapList = DataSourceUtils.findAllByTableName(dbName, sql);
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
            return Response.success(mapList);
        }
        throw new SystemException(ResponseConstant.Code.NOT_FOUND, MessageUtils.message(ResponseConstant.Message.DATABASE_NOT_FOUND));
    }

    @Override
    public Response<?> deleteTblByTableName(String userId, String tableName) {
        //除了删除DB里面的table之外，FI的DB表和table的介绍表都要删除
        DbInfoEntity dbInfo = dbInfoMapper.selectOneWithRelationsByQueryAs(
                QueryWrapper.create()
                        .select(db_info.all_columns)
                        .from(db_info)
                        .where(db_info.user_id.eq(userId)),
                DbInfoEntity.class
        );
        if (ObjUtil.isNull(dbInfo)) {
            throw new SystemException(ResponseConstant.Code.BAD_REQUEST, userId + MessageUtils.message(ResponseConstant.Message.CREATED_DATABASE_FAILED));
        }
        tableInfoMapper.deleteByCondition(
                table_info.user_id.eq(userId).and(table_info.table_name.eq(tableName))
        );
        //删除db表里面的tablelist里面的里table
        String tableListStr = dbInfo.getTableList();
        JSONArray tableList = JSONArray.parseArray(tableListStr);
        tableList.remove(tableName);
        dbInfo.setTableList(tableList.toJSONString());
        dbInfoMapper.insertOrUpdateSelective(dbInfo);
        Connection conn = DataSourceUtils.getConnection(dbInfo.getDbName());
        Statement stat = null;
        try {
            stat = conn.createStatement();
            //删除数据库下的table
            stat.executeUpdate("DROP TABLE IF EXISTS " + tableName);

        } catch (SQLException e) {
            log.error(e.getMessage(), e);
            throw new SystemException(ResponseConstant.Code.SERVER_ERROR, e.getMessage());
        } finally {
            //打开创建的数据库
            try {
                if (stat != null) {
                    stat.close();
                }
                DataSourceUtils.close(conn);
            } catch (SQLException e) {
                log.error(e.getMessage(), e);
            }
        }
        return Response.success();
    }

    @Override
    public Response<?> export(HttpServletResponse response, String userID, String tableName) {
        String sql = String.format("select * from %s", tableName);
        UserEntity consumer = userService.getMapper().selectOneWithRelationsByQueryAs(
                userService.queryChain()
                        .select(user.all_columns)
                        .from(user)
                        .where(user.email.eq(userID)),
                UserEntity.class
        );
        DbInfoEntity dbInfo = dbInfoMapper.selectOneWithRelationsByQueryAs(
                QueryWrapper.create()
                        .select(db_info.all_columns)
                        .from(db_info)
                        .where(db_info.user_id.eq(consumer.getId())),
                DbInfoEntity.class
        );
        if (ObjUtil.isNotNull(dbInfo)) {
            String dbName = dbInfo.getDbName();
            String columnSql = String.format("select column_name from information_schema.columns where table_schema='%s' and table_name='%s' ORDER BY ordinal_position;", dbName, tableName);
            List<Map<String, Object>> columns = DataSourceUtils.findAllByTableName(dbName, columnSql);
            List<Map<String, Object>> mapList = DataSourceUtils.findAllByTableName(dbName, sql);
            Workbook wk = ExcelExportUtils.exportExcel(tableName, columns, mapList);
            String filename = tableName + ".xlsx";
            try (BufferedOutputStream bos = new BufferedOutputStream(response.getOutputStream());
            ) {
                response.addHeader(HttpHeaders.CONTENT_DISPOSITION, "attachment;filename=" + URLEncoder.encode(filename, "UTF-8"));
                response.addHeader(HttpHeaders.CONTENT_TYPE, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
                wk.write(bos);
                bos.flush();
            } catch (IOException e) {
                log.error(e.getMessage(), e);
            } finally {
                IOUtils.closeQuietly(wk);
            }
        }
        return Response.success();
    }

    @Override
    public Response<?> createdb(String userId, String dbName) {
        boolean b = existDb(dbName);
        if (b) {
            throw new SystemException(ResponseConstant.Code.BAD_REQUEST, dbName + " already existed");
        }
        DbInfoEntity dbInfoEntity = dbInfoMapper.selectOneByCondition(db_info.user_id.eq(userId));
        if (ObjUtil.isNotNull(dbInfoEntity)) {
            dbInfoMapper.deleteByCondition(db_info.user_id.eq(userId));
        }
        Connection conn = DataSourceUtils.getConnection("mysql");
        Statement stat = null;
        try {
            stat = conn.createStatement();
            if (ObjUtil.isNotNull(dbInfoEntity)) {
                stat.executeUpdate("DROP DATABASE IF EXISTS " + dbInfoEntity.getDbName());
            }
            //创建数据库hello
            String sql = "create database " + dbName;
            stat.executeUpdate(sql);
            DbInfoEntity dbInfo = new DbInfoEntity();
            dbInfo.setDbName(dbName);
            dbInfo.setPublicSource(0);
            dbInfo.setCreateDate(new Date());
            dbInfo.setUserId(userId);
            dbInfoMapper.insertOrUpdate(dbInfo);
            JSONObject obj = new JSONObject();
            obj.put("url", String.format(dbUrl, dbName));
            obj.put("password", dbPassword);
            obj.put("username", dbuserName);
            SourceEntity source = new SourceEntity();
            source.setName(dbName);
            source.setCreateTime(new Date());
            source.setUpdateTime(new Date());
            source.setPublicSource(false);
            source.setUserId(userId);
            source.setConfig(obj.toJSONString());
            source.setType(false);
            sourceMapper.insertOrUpdate(source);
            return Response.success(dbInfo);
        } catch (SQLException e) {
            log.error(e.getMessage(), e);
            throw new SystemException(ResponseConstant.Code.BAD_REQUEST, "Illegal dbName");
        } finally {
            //打开创建的数据库
            try {
                if (stat != null) {
                    stat.close();
                }
                DataSourceUtils.close(conn);
            } catch (SQLException e) {
                log.error(e.getMessage(), e);
            }
        }
    }

    @Override
    public Response<?> getParamsListByUserId(String userId, String tableName) {
        TableInfoEntity tableInfo = tableInfoMapper.selectOneByCondition(
                (
                        table_info.user_id.eq(userId)
                                .or(table_info.public_source.eq(1))
                )
                        .and(table_info.table_name.eq(tableName))

        );
        if (ObjUtil.isNull(tableInfo)) {
            throw new SystemException(ResponseConstant.Code.NOT_FOUND, MessageUtils.message(ResponseConstant.Message.DATA_NOT_EXIST));
        }
        JSONObject obj = new JSONObject();
        obj.put("tableName", tableInfo.getTableName());
        JSONArray jsonArray = JSONArray.parseArray(tableInfo.getDescription());
        obj.put("parameters", jsonArray);
        return Response.success(Lists.newArrayList(obj));
    }

    @Override
    public Response<?> getUserDbName(String userId) {
        DbInfoEntity dbInfo = dbInfoMapper.selectOneByCondition(db_info.user_id.eq(userId));
        if (ObjUtil.isNotNull(dbInfo)) {
            return Response.success(dbInfo.getDbName());
        }
        return Response.success();
    }

    @Override
    public Response<?> getDataExecuteByParams(Map<String, String> genericApiModel, String db_name, String table_name) throws SQLException {
        List<Map<String, String>> genericApiList = new ArrayList<>();
        Map<String, String> queryModel1 = new HashMap<>();
        Map<String, String> queryModel2 = new HashMap<>();
        for (String rParam : genericApiModel.keySet()) {
            if (rParam.startsWith("2_")) {
                String value = genericApiModel.get(rParam);
                String key = rParam.substring(rParam.indexOf("2_") + 2);
                queryModel2.put(key, value);
            } else {
                queryModel1.put(rParam, genericApiModel.get(rParam));
            }
        }
        genericApiList.add(queryModel1);
        if (!queryModel2.isEmpty()) {
            genericApiList.add(queryModel2);
        }
        Map<String, String> allRequestParams = genericApiList.get(0);
        if (genericApiList.size() > 1) {
            Set<String> firstResult = new HashSet<>();
            Map<String, String> secondMap = genericApiList.get(1);
            if (secondMap.containsKey("secondQueryParam")) {
                String secondQueryParam = secondMap.get("secondQueryParam");
                allRequestParams.put("result", secondQueryParam);
                Response<?> response = firstQuery(db_name, table_name, allRequestParams);
                allRequestParams.remove("flag");
                if (ObjUtil.isNotNull(response.getData())) {
                    JSONArray array = (JSONArray) (response.getData());
                    for (int i = 0; i < array.size(); i++) {
                        JSONObject jsonObject = array.getJSONObject(i);
                        if (jsonObject.containsKey(secondQueryParam)) {
                            //第一次查询的结果集
                            firstResult.add(jsonObject.getString(secondQueryParam));
                        }
                    }

                }//组装第二次查询的IN条件
                String join = StrUtil.join(",", firstResult);
                int index = secondMap.size() - 2;
                secondMap.put(secondQueryParam + "_logic_" + index, "AND");
                secondMap.put(secondQueryParam + "_condition_" + index, "IN");
                secondMap.put(secondQueryParam + "_param_" + index, join);
            }
            return firstQuery(db_name, table_name, secondMap);

        } else {
            return firstQuery(db_name, table_name, allRequestParams);
        }
    }

    @Override
    public Response<?> makeSwagger(MakeSwagView swagView) {
        String url = swagView.getUrl();
        String flag = getParamByUrl(url, "flag");
        Swagger swag;
        try {
            swag = SwaggerBuilderUtils.makeSwagger(swagView);
            String pretty = Json.pretty(swag);
            JSONObject jsonObject = JSONObject.parseObject(pretty);
            JSONObject paths = jsonObject.getJSONObject("paths");
            Map<String, Object> innerMap = paths.getInnerMap();
            for (Map.Entry<String, Object> entry : innerMap.entrySet()) {
                Map<String, Object> value = (Map) entry.getValue();
                for (Map.Entry<String, Object> entry1 : value.entrySet()) {
                    Map map = (Map) entry1.getValue();
                    if (map.containsKey("responses")) {
                        Map responses = (Map) map.get("responses");
                        Map inter = (Map) responses.get("200");
                        Map schema = new HashMap();
                        schema.put("type", "object");
                        schema.put("example", swagView.getResponse());
                        inter.put("schema", schema);
                    }
                }
            }
            apiMarketServiceImpl.autoGen(swagView.getFiname(), SecurityUtils.getUserId(),
                    jsonObject.toJSONString(), swagView.getApiClassId(), swagView.getCourseID(), flag, url, swagView.getApiClassId());
        } catch (MalformedURLException e) {
            log.error(e.getMessage(), e);
        }
        return Response.success();
    }

    @Override
    public void getTableTemplate(HttpServletResponse response, String userId, String tableName) {
        List<DataTableColumnExcelBo> dataTableInfo = getDataTableInfo(userId, tableName);

        if (CollectionUtils.isEmpty(dataTableInfo)) {
            throw new SystemException(ResponseConstant.Code.NOT_FOUND, MessageUtils.message(ResponseConstant.Message.DATA_TABLE_NOT_FOUND));
        }

        Set<String> headerFields = dataTableInfo.stream().map(DataTableColumnExcelBo::getFieldName).collect(Collectors.toSet());
        ExcelUtils.of().exportTemplateFromHeader(response, tableName, headerFields);
    }

    public static String getParamByUrl(String url, String name) {
        url += "&";
        String pattern = "(\\?|&){1}#{0,1}" + name + "=[a-zA-Z0-9]*(&{1})";
        Pattern r = Pattern.compile(pattern);
        Matcher m = r.matcher(url);
        if (m.find()) {
            return m.group(0).split("=")[1].replace("&", "");
        } else {
            return null;
        }

    }

    private Response<?> firstQuery(String db_name, String table_name, Map<String, String> allRequestParams) throws SQLException {
        String columns = allRequestParams.remove("result");
        String flag = allRequestParams.get("flag");
        String sqlQuery;
        List<String> valueList = new ArrayList<>();
        if (allRequestParams.isEmpty()) {
            //没有where语句
            sqlQuery = QueryBuilderUtils.buildSelectSqlQuery(db_name, table_name, columns, allRequestParams);
        } else {
            //有where语句
            Map<String, Object> objectMap = QueryBuilderUtils.buildSelectSqlWhereQuery(db_name, table_name, columns, allRequestParams);
            sqlQuery = objectMap.get("sqlQuery").toString();
            valueList = (List<String>) objectMap.get("valueList");
        }
        List<Map<String, Object>> mapList = null;
        if ("1".equals(flag)) {
            List<Object> value = DataSourceUtils.findAllPrepareStatementHaveFlag(db_name, sqlQuery, valueList);
            return Response.success(value);
        } else {
            mapList = DataSourceUtils.findAllPrepareStatement(db_name, sqlQuery, valueList);
            return Response.success(mapList);
        }
    }

    private boolean existDb(String dbName) {
        Connection conn = DataSourceUtils.getConnection("mysql");
        //判断要创建的数据库是否存在
        String sql = "select * from information_schema.SCHEMATA where SCHEMA_NAME = '" + dbName + "'";
        try (Statement stat = conn.createStatement();
             ResultSet resultSet = stat.executeQuery(sql);) {
            //select * from information_schema.SCHEMATA where SCHEMA_NAME = 'cifore'
            resultSet.last();
            int row = resultSet.getRow();
            return row != 0;
        } catch (SQLException e) {
            log.error(e.getMessage(), e);
            return false;
        } finally {
            DataSourceUtils.close(conn);
        }
    }

    private boolean regTableName(String tableName) {
        Pattern p = Pattern.compile("^(?!\\d+$)(?!_+$)(?![0-9_]+$)[a-zA-Z0-9_]+$");
        Matcher m = p.matcher(tableName);
        return m.matches();
    }

}
