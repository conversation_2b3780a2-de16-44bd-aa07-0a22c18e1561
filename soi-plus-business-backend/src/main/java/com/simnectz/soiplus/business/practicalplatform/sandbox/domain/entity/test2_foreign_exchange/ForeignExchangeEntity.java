package com.simnectz.soiplus.business.practicalplatform.sandbox.domain.entity.test2_foreign_exchange;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.Table;
import lombok.*;

@Data
@With
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(value = "foreign_exchange",dataSource = "foreign_exchange")
@EqualsAndHashCode()
public class ForeignExchangeEntity {

    @Column(value = "CountryCode")
    private String countryCode;

    @Column(value = "ClearingCode")
    private String clearingCode;

    @Column(value = "BranchCode")
    private String branchCode;

    @Column(value = "SandBoxId")
    private String sandBoxId;

    @Column(value = "AccountNumber")
    private String accountNumber;

    @Column(value = "TransactionType")
    private String transactionType;

    @Column(value = "LocalCurrency")
    private String localCurrency;

    @Column(value = "ForeignCurrency")
    private String foreignCurrency;

    @Column(value = "ExchangeRate")
    private String exchangeRate;

    @Column(value = "ExchangeAmoutInLocalCurrency")
    private String exchangeAmoutInLocalCurrency;

    @Column(value = "ExchangeAmoutInForeignCurrency")
    private String exchangeAmoutInForeignCurrency;

    @Column(value = "PrevBalInForeignCurrencyAccount")
    private String prevBalInForeignCurrencyAccount;

    @Column(value = "PostBalInForeignCurrencyAccount")
    private String postBalInForeignCurrencyAccount;

    @Id
    @Column(value = "TransactionTime")
    private String transactionTime;

}
