package com.simnectz.soiplus.business.practicalplatform.datamanagement.service;

import com.simnectz.soiplus.business.practicalplatform.apimarket.domain.model.MakeSwagView;
import com.simnectz.soiplus.business.system.domain.vo.Response;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.sql.SQLException;
import java.util.Map;

public interface DataManagementService {
    Response<?> getUserDb(String userID);

    Response<?> uploadExcelFileAndCreateTable(MultipartFile file, String userId, String type);

    Response<?> getTableListByUserId(String userId);

    Response<?> uploadTableInfoFileAndSave(MultipartFile file, String userId, String type);

    Response<?> getDataByUserIdAndTableName(String userId, String tableName, String publicSource);

    Response<?> deleteTblByTableName(String userId, String tableName);

    Response<?> export(HttpServletResponse response, String userID, String tableName);

    Response<?> createdb(String userId, String dbName);

    Response<?> getParamsListByUserId(String userId, String tableName);

    Response<?> getUserDbName(String userId);

    Response<?> getDataExecuteByParams(Map<String, String> genericApiModel, String db_name, String table_name) throws SQLException;

    Response<?> makeSwagger(MakeSwagView swagView);

    void getTableTemplate(HttpServletResponse response, String userId, String tableName);

}
