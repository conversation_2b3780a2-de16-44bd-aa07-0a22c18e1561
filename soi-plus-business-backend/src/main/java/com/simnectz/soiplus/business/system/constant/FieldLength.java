package com.simnectz.soiplus.business.system.constant;

public class FieldLength {

    /**
     * nickname min length
     */
    public static final int NICKNAME_MIN = 2;

    /**
     * nickname max length
     */
    public static final int NICKNAME_MAX = 64;

    /**
     * username min length
     */
    public static final int USERNAME_MIN = 2;

    /**
     * username max length
     */
    public static final int USERNAME_MAX = 64;

    /**
     * email max length
     */
    public static final int EMAIL_MAX = 128;

    /**
     * password min length
     */
    public static final int PASSWORD_MIN = 8;

    /**
     * password max length
     */
    public static final int PASSWORD_MAX = 16;

    /**
     * remark max length
     */
    public static final int REMARK_MAX = 300;

    /**
     * role name max length
     */
    public static final int ROLE_NAME_MAX = 128;

}
