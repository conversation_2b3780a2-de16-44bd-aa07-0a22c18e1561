package com.simnectz.soiplus.business.practicalplatform.codingexercise.utils;

import cn.hutool.extra.spring.SpringUtil;
import com.simnectz.soiplus.business.system.config.CompilerProperties;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.IOException;
import java.util.Map;

@Data
@EqualsAndHashCode(callSuper = true)
public class PythonCompiler extends Compiler {

    private final CompilerProperties compilerProperties = SpringUtil.getBean(CompilerProperties.class);

    public final String PYTHON_CODE_PATH = System.getProperty("user.dir") + compilerProperties.getPython().getPath();

    public PythonCompiler(String code, String userId) {
        this.folderName = userId;
        FileUtils.createFile(PYTHON_CODE_PATH + folderName, PYTHON_CODE_PATH + folderName + "/" + compilerProperties.getPython().getFileName() + ".py", code);
    }

    @Override
    public void compilerRun() throws IOException, InterruptedException {
        // Run
        Map<String, String> runResult = ProgramUtils.run("python " + PYTHON_CODE_PATH + folderName + "/" + compilerProperties.getPython().getFileName() + ".py");
        runErrorMessage = runResult.get("runError");
        runOutputMessage = runResult.get("runOutput");
        FileUtils.removeFile(PYTHON_CODE_PATH + folderName + "/" + compilerProperties.getPython().getFileName() + ".py");
    }
}
