package com.simnectz.soiplus.business.practicalplatform.sandbox.mapper.test2_loan;

import com.mybatisflex.annotation.UseDataSource;
import com.simnectz.soiplus.business.practicalplatform.sandbox.domain.entity.test2_loan.MortgageLoanContractDetailEntity;
import com.simnectz.soiplus.business.system.mapper.BasicMapper;
import org.apache.ibatis.annotations.Mapper;

/**
 * MortgageLoanContractDetail data access layer interface
 */
@Mapper
@UseDataSource("loan")
public interface MortgageLoanContractDetailMapper extends BasicMapper<MortgageLoanContractDetailEntity> {
}
