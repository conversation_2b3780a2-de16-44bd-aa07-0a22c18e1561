package com.simnectz.soiplus.business.learningspace.course.domain.dto;

import com.simnectz.soiplus.business.system.domain.dto.PaginateEnquiry;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * enquiry program Definition request parameters
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class EnquiryCourseDto extends PaginateEnquiry implements Serializable {

    private static final long serialVersionUID = -7181585090697781299L;

    private String keywords;

    private String courseCategoryId;

}
