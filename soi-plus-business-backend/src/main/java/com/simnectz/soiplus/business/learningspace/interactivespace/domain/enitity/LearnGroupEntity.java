package com.simnectz.soiplus.business.learningspace.interactivespace.domain.enitity;

import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import com.mybatisflex.core.keygen.KeyGenerators;
import com.simnectz.soiplus.business.system.domain.entity.BasicEntity;
import lombok.*;

import java.io.Serializable;

@Data
@With
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(value = "learn_group")
@EqualsAndHashCode(callSuper = true)
public class LearnGroupEntity extends BasicEntity implements Serializable {

    private static final long serialVersionUID = -2758371492792509240L;

    @Id(keyType = KeyType.Generator, value = KeyGenerators.uuid)
    private String id;

    private String number;

    private String name;

    private String groupProjectTitle;

    private String member;

    private String courseId;

    private String subjectId;

    private String assignmentId;

}
