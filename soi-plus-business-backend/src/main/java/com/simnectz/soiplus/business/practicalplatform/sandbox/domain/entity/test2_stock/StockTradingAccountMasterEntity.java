package com.simnectz.soiplus.business.practicalplatform.sandbox.domain.entity.test2_stock;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.Table;
import lombok.*;

@Data
@With
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(value = "stocktradingaccountmaster",dataSource = "stock")
public class StockTradingAccountMasterEntity {

    @Id
    @Column(value = "ID")
    private String id;

    @Column(value = "CountryCode")
    private String countryCode;

    @Column(value = "ClearingCode")
    private String clearingCode;

    @Column(value = "BranchCode")
    private String branchCode;

    @Column(value = "SandBoxId")
    private String sandBoxId;

    @Column(value = "CustomerNumber")
    private String customerNumber;

    @Column(value = "AccountNumber")
    private String accountNumber;

    @Column(value = "SettlementAccount")
    private String settlementAccount;

    @Column(value = "AccountStatus")
    private String accountStatus;

    @Column(value = "LastUpdateDate")
    private String lastUpdateDate;

    @Column(value = "CreateDate")
    private String createDate;

}
