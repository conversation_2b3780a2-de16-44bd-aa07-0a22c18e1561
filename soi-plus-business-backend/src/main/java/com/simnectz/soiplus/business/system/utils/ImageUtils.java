package com.simnectz.soiplus.business.system.utils;

import com.simnectz.soiplus.business.system.enums.ContentType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.io.InputStream;
import java.util.Objects;

@Slf4j
public class ImageUtils {

    public static byte[] getImageByUrl(String imageUrl) {
        if (StringUtils.isEmpty(imageUrl)) {
            return new byte[0];
        }
        try (InputStream is = FileUtils.getFileStreamByUrl(imageUrl)) {
            if (Objects.nonNull(is)) {
                return IOUtils.toByteArray(is);
            }
        } catch (IOException exception) {
            log.error("Error while loading image from url [{}]: {}", imageUrl, exception.getMessage(), exception);
        }
        return new byte[0];
    }

    public static String writeImageBytes(byte[] imageBytes) throws IOException {
        String imageExtension = getImageExtendName(imageBytes);
        if (StringUtils.isEmpty(imageExtension)) {
            throw new IllegalArgumentException("Unsupported image type");
        }
        return FileUtils.writeBytesFile(
                imageBytes, IdUtils.generateUUID() + "." + imageExtension.toLowerCase()
        );
    }

    public static String getImageExtendName(byte[] imageBytes) {
        String mimeType = FileUtils.getFileMimeType(imageBytes);

        if (StringUtils.equals(ContentType.JPG.getValue(), mimeType)) {
            return ContentType.JPG.getLabel();
        }
        if (StringUtils.equals(ContentType.PNG.getValue(), mimeType)) {
            return ContentType.PNG.getLabel();
        }
        return null;
    }

    private ImageUtils() {
    }

}