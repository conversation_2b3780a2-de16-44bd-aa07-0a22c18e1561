package com.simnectz.soiplus.business.practicalplatform.datamanagement.domain.entity;

import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import com.mybatisflex.core.keygen.KeyGenerators;
import lombok.*;

import java.io.Serializable;
import java.util.Date;

@Data
@With
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(value = "data_db_info")
@EqualsAndHashCode()
public class DbInfoEntity implements Serializable {

    private static final long serialVersionUID = 3498818630001350573L;

    @Id(keyType = KeyType.Generator, value = KeyGenerators.uuid)
    private String id;
    private String dbName;
    private String tableList;
    private String userId;
    private Integer publicSource;
    private Date createDate;

}
