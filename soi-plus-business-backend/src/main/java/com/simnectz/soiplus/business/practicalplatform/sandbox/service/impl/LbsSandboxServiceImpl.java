package com.simnectz.soiplus.business.practicalplatform.sandbox.service.impl;

import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONArray;
import com.alibaba.fastjson2.JSONObject;
import com.google.common.collect.Lists;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.If;
import com.mybatisflex.core.query.QueryWrapper;
import com.simnectz.soiplus.business.learningspace.interactivespace.domain.enitity.LearnGroupEntity;
import com.simnectz.soiplus.business.learningspace.interactivespace.service.GroupService;
import com.simnectz.soiplus.business.learningspace.organizations.mapper.OrganizationMapper;
import com.simnectz.soiplus.business.learningspace.users.domain.entity.UserEntity;
import com.simnectz.soiplus.business.learningspace.users.service.UserService;
import com.simnectz.soiplus.business.practicalplatform.datamanagement.domain.entity.DbInfoEntity;
import com.simnectz.soiplus.business.practicalplatform.datamanagement.mapper.DbInfoMapper;
import com.simnectz.soiplus.business.practicalplatform.sandbox.domain.dto.DelApiTokenDto;
import com.simnectz.soiplus.business.practicalplatform.sandbox.domain.entity.ApiTokenEntity;
import com.simnectz.soiplus.business.practicalplatform.sandbox.domain.entity.InsuranceUserEntity;
import com.simnectz.soiplus.business.practicalplatform.sandbox.domain.entity.LbsStaffEntity;
import com.simnectz.soiplus.business.practicalplatform.sandbox.domain.entity.TaskQueueEntity;
import com.simnectz.soiplus.business.practicalplatform.sandbox.domain.entity.test2_creditcard.CreditcardMasterEntity;
import com.simnectz.soiplus.business.practicalplatform.sandbox.domain.entity.test2_creditcard.CreditcardPointReferenceEntity;
import com.simnectz.soiplus.business.practicalplatform.sandbox.domain.entity.test2_creditcard.CreditcardStatementEntity;
import com.simnectz.soiplus.business.practicalplatform.sandbox.domain.entity.test2_creditcard.CreditcardTransactionDetailEntity;
import com.simnectz.soiplus.business.practicalplatform.sandbox.domain.entity.test2_deposit.CurrentAccountMasterEntity;
import com.simnectz.soiplus.business.practicalplatform.sandbox.domain.entity.test2_deposit.CustomerMasterEntity;
import com.simnectz.soiplus.business.practicalplatform.sandbox.domain.entity.test2_deposit.SavingAccountMasterEntity;
import com.simnectz.soiplus.business.practicalplatform.sandbox.domain.entity.test2_deposit.TransactionLogEntity;
import com.simnectz.soiplus.business.practicalplatform.sandbox.domain.entity.test2_foreign_exchange.FexAccountDetailEntity;
import com.simnectz.soiplus.business.practicalplatform.sandbox.domain.entity.test2_foreign_exchange.FexAccountMasterEntity;
import com.simnectz.soiplus.business.practicalplatform.sandbox.domain.entity.test2_foreign_exchange.ForeignExchangeEntity;
import com.simnectz.soiplus.business.practicalplatform.sandbox.domain.entity.test2_fund.MutualFundAccountMasterEntity;
import com.simnectz.soiplus.business.practicalplatform.sandbox.domain.entity.test2_fund.MutualFundHoldingsEntity;
import com.simnectz.soiplus.business.practicalplatform.sandbox.domain.entity.test2_fund.MutualFundPlatformLogEntity;
import com.simnectz.soiplus.business.practicalplatform.sandbox.domain.entity.test2_insurance.InsuranceProductDetailEntity;
import com.simnectz.soiplus.business.practicalplatform.sandbox.domain.entity.test2_insurance.InsuranceQuoteDetailEntity;
import com.simnectz.soiplus.business.practicalplatform.sandbox.domain.entity.test2_insurance.InsuranceQuoteMasterEntity;
import com.simnectz.soiplus.business.practicalplatform.sandbox.domain.entity.test2_loan.LoanAccountMasterEntity;
import com.simnectz.soiplus.business.practicalplatform.sandbox.domain.entity.test2_loan.MortgageLoanContractDetailEntity;
import com.simnectz.soiplus.business.practicalplatform.sandbox.domain.entity.test2_loan.MortgageLoanContractMasterEntity;
import com.simnectz.soiplus.business.practicalplatform.sandbox.domain.entity.test2_loan.MortgageLoanTransactionLogEntity;
import com.simnectz.soiplus.business.practicalplatform.sandbox.domain.entity.test2_payment.PaymentTransactionEntity;
import com.simnectz.soiplus.business.practicalplatform.sandbox.domain.entity.test2_stock.StockHoldingsEntity;
import com.simnectz.soiplus.business.practicalplatform.sandbox.domain.entity.test2_stock.StockPlatformLogEntity;
import com.simnectz.soiplus.business.practicalplatform.sandbox.domain.entity.test2_stock.StockTradingAccountMasterEntity;
import com.simnectz.soiplus.business.practicalplatform.sandbox.domain.entity.test2_sysadmin.AdminEntity;
import com.simnectz.soiplus.business.practicalplatform.sandbox.domain.entity.test2_sysadmin.RoleEntity;
import com.simnectz.soiplus.business.practicalplatform.sandbox.domain.entity.test2_term_deposit.TermDepositDetailEntity;
import com.simnectz.soiplus.business.practicalplatform.sandbox.domain.entity.test2_term_deposit.TermDepositMasterEntity;
import com.simnectz.soiplus.business.practicalplatform.sandbox.mapper.ApiTokenMapper;
import com.simnectz.soiplus.business.practicalplatform.sandbox.mapper.InsuranceUserMapper;
import com.simnectz.soiplus.business.practicalplatform.sandbox.mapper.LbsStaffMapper;
import com.simnectz.soiplus.business.practicalplatform.sandbox.mapper.TaskQueueMapper;
import com.simnectz.soiplus.business.practicalplatform.sandbox.mapper.test2_creditcard.CreditcardMasterMapper;
import com.simnectz.soiplus.business.practicalplatform.sandbox.mapper.test2_creditcard.CreditcardPointReferenceMapper;
import com.simnectz.soiplus.business.practicalplatform.sandbox.mapper.test2_creditcard.CreditcardStatementMapper;
import com.simnectz.soiplus.business.practicalplatform.sandbox.mapper.test2_creditcard.CreditcardTransactionDetailMapper;
import com.simnectz.soiplus.business.practicalplatform.sandbox.mapper.test2_deposit.CurrentAccountMasterMapper;
import com.simnectz.soiplus.business.practicalplatform.sandbox.mapper.test2_deposit.CustomerMasterMapper;
import com.simnectz.soiplus.business.practicalplatform.sandbox.mapper.test2_deposit.SavingAccountMasterMapper;
import com.simnectz.soiplus.business.practicalplatform.sandbox.mapper.test2_deposit.TransactionLogMapper;
import com.simnectz.soiplus.business.practicalplatform.sandbox.mapper.test2_foreign_exchange.FexAccountDetailMapper;
import com.simnectz.soiplus.business.practicalplatform.sandbox.mapper.test2_foreign_exchange.FexAccountMasterMapper;
import com.simnectz.soiplus.business.practicalplatform.sandbox.mapper.test2_foreign_exchange.ForeignExchangeMapper;
import com.simnectz.soiplus.business.practicalplatform.sandbox.mapper.test2_fund.MutualFundAccountMasterMapper;
import com.simnectz.soiplus.business.practicalplatform.sandbox.mapper.test2_fund.MutualFundHoldingsMapper;
import com.simnectz.soiplus.business.practicalplatform.sandbox.mapper.test2_fund.MutualFundPlatformLogMapper;
import com.simnectz.soiplus.business.practicalplatform.sandbox.mapper.test2_insurance.InsuranceProductDetailMapper;
import com.simnectz.soiplus.business.practicalplatform.sandbox.mapper.test2_insurance.InsuranceQuoteDetailMapper;
import com.simnectz.soiplus.business.practicalplatform.sandbox.mapper.test2_insurance.InsuranceQuoteMasterMapper;
import com.simnectz.soiplus.business.practicalplatform.sandbox.mapper.test2_insurance.InsuranceTransactionMasterMapper;
import com.simnectz.soiplus.business.practicalplatform.sandbox.mapper.test2_loan.LoanAccountMasterMapper;
import com.simnectz.soiplus.business.practicalplatform.sandbox.mapper.test2_loan.MortgageLoanContractDetailMapper;
import com.simnectz.soiplus.business.practicalplatform.sandbox.mapper.test2_loan.MortgageLoanContractMasterMapper;
import com.simnectz.soiplus.business.practicalplatform.sandbox.mapper.test2_loan.MortgageLoanTransactionLogMapper;
import com.simnectz.soiplus.business.practicalplatform.sandbox.mapper.test2_payment.PaymentTransactionMapper;
import com.simnectz.soiplus.business.practicalplatform.sandbox.mapper.test2_stock.StockHoldingsMapper;
import com.simnectz.soiplus.business.practicalplatform.sandbox.mapper.test2_stock.StockPlatformLogMapper;
import com.simnectz.soiplus.business.practicalplatform.sandbox.mapper.test2_stock.StockTradingAccountMasterMapper;
import com.simnectz.soiplus.business.practicalplatform.sandbox.mapper.test2_sysadmin.AdminMapper;
import com.simnectz.soiplus.business.practicalplatform.sandbox.mapper.test2_sysadmin.CmbLoginMapper;
import com.simnectz.soiplus.business.practicalplatform.sandbox.mapper.test2_sysadmin.LbsRoleMapper;
import com.simnectz.soiplus.business.practicalplatform.sandbox.mapper.test2_term_deposit.TermDepositDetailMapper;
import com.simnectz.soiplus.business.practicalplatform.sandbox.mapper.test2_term_deposit.TermDepositMasterMapper;
import com.simnectz.soiplus.business.practicalplatform.sandbox.model.TableModel;
import com.simnectz.soiplus.business.practicalplatform.sandbox.service.ApiTokenService;
import com.simnectz.soiplus.business.practicalplatform.sandbox.service.LbsSandboxService;
import com.simnectz.soiplus.business.system.constant.ResponseConstant;
import com.simnectz.soiplus.business.system.domain.vo.Paginate;
import com.simnectz.soiplus.business.system.domain.vo.Response;
import com.simnectz.soiplus.business.system.utils.DataSourceUtils;
import com.simnectz.soiplus.business.system.utils.GetColumnsByReflectUtils;
import com.simnectz.soiplus.business.system.utils.MessageUtils;
import com.simnectz.soiplus.business.system.utils.RandomGeneratorUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.AsyncResult;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.InputStream;
import java.util.*;
import java.util.concurrent.Future;
import java.util.stream.Collectors;

import static com.mybatisflex.core.query.QueryMethods.distinct;
import static com.simnectz.soiplus.business.learningspace.organizations.domain.entity.table.OrganizationTableDef.organization;
import static com.simnectz.soiplus.business.learningspace.roles.domain.table.RoleTableDef.role;
import static com.simnectz.soiplus.business.learningspace.users.domain.entity.table.UserRoleTableDef.user_role;
import static com.simnectz.soiplus.business.learningspace.users.domain.entity.table.UserTableDef.user;
import static com.simnectz.soiplus.business.practicalplatform.datamanagement.domain.entity.table.DbInfoTableDef.db_info;
import static com.simnectz.soiplus.business.practicalplatform.sandbox.domain.entity.table.ApiTokenTableDef.api_token;
import static com.simnectz.soiplus.business.practicalplatform.sandbox.domain.entity.table.LbsStaffTableDef.lbs_staff;
import static com.simnectz.soiplus.business.practicalplatform.sandbox.domain.entity.table.TaskQueueTableDef.task_queue;
import static com.simnectz.soiplus.business.practicalplatform.sandbox.domain.entity.test2_creditcard.table.CreditcardMasterTableDef.creditcard_master;
import static com.simnectz.soiplus.business.practicalplatform.sandbox.domain.entity.test2_creditcard.table.CreditcardPointReferenceTableDef.creditcard_point_reference;
import static com.simnectz.soiplus.business.practicalplatform.sandbox.domain.entity.test2_creditcard.table.CreditcardStatementTableDef.creditcard_statement;
import static com.simnectz.soiplus.business.practicalplatform.sandbox.domain.entity.test2_creditcard.table.CreditcardTransactionDetailTableDef.creditcard_transaction_detail;
import static com.simnectz.soiplus.business.practicalplatform.sandbox.domain.entity.test2_deposit.table.CurrentAccountMasterTableDef.current_account_master;
import static com.simnectz.soiplus.business.practicalplatform.sandbox.domain.entity.test2_deposit.table.CustomerMasterTableDef.customer_master;
import static com.simnectz.soiplus.business.practicalplatform.sandbox.domain.entity.test2_deposit.table.SavingAccountMasterTableDef.saving_account_master;
import static com.simnectz.soiplus.business.practicalplatform.sandbox.domain.entity.test2_deposit.table.TransactionLogTableDef.transaction_log;
import static com.simnectz.soiplus.business.practicalplatform.sandbox.domain.entity.test2_foreign_exchange.table.FexAccountDetailTableDef.fex_account_detail;
import static com.simnectz.soiplus.business.practicalplatform.sandbox.domain.entity.test2_foreign_exchange.table.FexAccountMasterTableDef.fex_account_master;
import static com.simnectz.soiplus.business.practicalplatform.sandbox.domain.entity.test2_foreign_exchange.table.ForeignExchangeTableDef.foreign_exchange;
import static com.simnectz.soiplus.business.practicalplatform.sandbox.domain.entity.test2_fund.table.MutualFundAccountMasterTableDef.mutual_fund_account_master;
import static com.simnectz.soiplus.business.practicalplatform.sandbox.domain.entity.test2_fund.table.MutualFundHoldingsTableDef.mutual_fund_holdings;
import static com.simnectz.soiplus.business.practicalplatform.sandbox.domain.entity.test2_fund.table.MutualFundPlatformLogTableDef.mutual_fund_platform_log;
import static com.simnectz.soiplus.business.practicalplatform.sandbox.domain.entity.test2_insurance.table.InsuranceProductDetailTableDef.insurance_product_detail;
import static com.simnectz.soiplus.business.practicalplatform.sandbox.domain.entity.test2_insurance.table.InsuranceQuoteDetailTableDef.insurance_quote_detail;
import static com.simnectz.soiplus.business.practicalplatform.sandbox.domain.entity.test2_insurance.table.InsuranceQuoteMasterTableDef.insurance_quote_master;
import static com.simnectz.soiplus.business.practicalplatform.sandbox.domain.entity.test2_loan.table.LoanAccountMasterTableDef.loan_account_master;
import static com.simnectz.soiplus.business.practicalplatform.sandbox.domain.entity.test2_loan.table.MortgageLoanContractDetailTableDef.mortgage_loan_contract_detail;
import static com.simnectz.soiplus.business.practicalplatform.sandbox.domain.entity.test2_loan.table.MortgageLoanContractMasterTableDef.mortgage_loan_contract_master;
import static com.simnectz.soiplus.business.practicalplatform.sandbox.domain.entity.test2_loan.table.MortgageLoanTransactionLogTableDef.mortgage_loan_transaction_log;
import static com.simnectz.soiplus.business.practicalplatform.sandbox.domain.entity.test2_payment.table.PaymentTransactionTableDef.payment_transaction;
import static com.simnectz.soiplus.business.practicalplatform.sandbox.domain.entity.test2_stock.table.StockHoldingsTableDef.stock_holdings;
import static com.simnectz.soiplus.business.practicalplatform.sandbox.domain.entity.test2_stock.table.StockPlatformLogTableDef.stock_platform_log;
import static com.simnectz.soiplus.business.practicalplatform.sandbox.domain.entity.test2_stock.table.StockTradingAccountMasterTableDef.stock_trading_account_master;
import static com.simnectz.soiplus.business.practicalplatform.sandbox.domain.entity.test2_sysadmin.table.AdminTableDef.admin;
import static com.simnectz.soiplus.business.practicalplatform.sandbox.domain.entity.test2_term_deposit.table.TermDepositDetailTableDef.term_deposit_detail;
import static com.simnectz.soiplus.business.practicalplatform.sandbox.domain.entity.test2_term_deposit.table.TermDepositMasterTableDef.term_deposit_master;

@Service
@RequiredArgsConstructor
public class LbsSandboxServiceImpl implements LbsSandboxService {

    private final UserService userService;
    private final DbInfoMapper dbInfoMapper;
    private final ApiTokenMapper apiTokenMapper;
    private final ApiTokenService apiTokenService;
    private final CustomerMasterMapper customerMasterMapper;
    private final SavingAccountMasterMapper savingAccountMasterMapper;
    private final CurrentAccountMasterMapper currentAccountMasterMapper;
    private final TransactionLogMapper transactionLogMapper;
    private final FexAccountDetailMapper fexAccountDetailMapper;
    private final FexAccountMasterMapper fexAccountMasterMapper;
    private final ForeignExchangeMapper foreignExchangeMapper;
    private final MutualFundAccountMasterMapper mutualFundAccountMasterMapper;
    private final MutualFundHoldingsMapper mutualFundHoldingsMapper;
    private final MutualFundPlatformLogMapper mutualFundPlatformLogMapper;
    private final StockHoldingsMapper stockHoldingsMapper;
    private final StockPlatformLogMapper stockPlatformLogMapper;
    private final StockTradingAccountMasterMapper stockTradingAccountMasterMapper;
    private final CreditcardMasterMapper creditcardMasterMapper;
    private final CreditcardPointReferenceMapper creditcardPointReferenceMapper;
    private final CreditcardStatementMapper creditcardStatementMapper;
    private final CreditcardTransactionDetailMapper creditcardTransactionDetailMapper;
    private final InsuranceProductDetailMapper insuranceProductDetailMapper;
    private final InsuranceQuoteDetailMapper insuranceQuoteDetailMapper;
    private final InsuranceQuoteMasterMapper insuranceQuoteMasterMapper;
    private final InsuranceTransactionMasterMapper insuranceTransactionMasterMapper;
    private final LoanAccountMasterMapper loanAccountMasterMapper;
    private final MortgageLoanContractDetailMapper mortgageLoanContractDetailMapper;
    private final MortgageLoanContractMasterMapper mortgageLoanContractMasterMapper;
    private final MortgageLoanTransactionLogMapper mortgageLoanTransactionLogMapper;
    private final PaymentTransactionMapper paymentTransactionMapper;
    private final TermDepositDetailMapper termDepositDetailMapper;
    private final TermDepositMasterMapper termDepositMasterMapper;
    private final AdminMapper adminMapper;
    private final CmbLoginMapper cmbLoginMapper;
    private final LbsRoleMapper lbsRoleMapper;
    private final LbsStaffMapper lbsStaffMapper;
    private final OrganizationMapper organizationMapper;
    private final InsuranceUserMapper insuranceUserMapper;
    private final GroupService groupService;
    private final TaskQueueMapper taskQueueMapper;

    @Value("${lbs.hackathon.sql.path}")
    private String hackathonSqlPath;

    @Override
    public Response<?> dataSync(String userID, String dbName) {
        UserEntity user = userService.getById(userID);
        if (ObjUtil.isNull(user)) {
            return Response.failed(-1, MessageUtils.message(ResponseConstant.Message.THE_CONSUMER_NOT_EXIST));
        }

        DbInfoEntity dbInfo = dbInfoMapper.selectOneWithRelationsByQueryAs(
                QueryWrapper.create()
                        .select(db_info.all_columns)
                        .from(db_info)
                        .where(db_info.user_id.eq(user.getId()))
                        .and(db_info.db_name.eq(dbName)),
                DbInfoEntity.class
        );

        if (ObjUtil.isNull(dbInfo)) {
            return Response.failed(ResponseConstant.Code.NOT_FOUND, MessageUtils.message(ResponseConstant.Message.THE_DB_INFO_NOT_EXIST));
        }

        List<ApiTokenEntity> tokenList = apiTokenMapper.selectListWithRelationsByQueryAs(
                QueryWrapper.create()
                        .select(api_token.all_columns)
                        .from(api_token)
                        .where(api_token.user_id.eq(user.getId())),
                ApiTokenEntity.class
        );
        if (tokenList.size() == 0) {
            return Response.failed(-1, MessageUtils.message(ResponseConstant.Message.THE_CONSUMER_HAVE_NOT_SANDBOX_DATA));
        }

        // customerNumber List
        Set<String> customerNumberList = tokenList.stream().map(ApiTokenEntity::getCustomerNumber).collect(Collectors.toSet());

        // 在新数据库建分析数据库初始表
        List<InputStream> filesPath = GetColumnsByReflectUtils.getFilesPath(hackathonSqlPath);
        for (InputStream inputStream : filesPath) {
            try {
                DataSourceUtils.executeSqlFileInputStream(inputStream, dbName);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

        if (customerNumberList.size() == 0) {
            return Response.failed(-1, MessageUtils.message(ResponseConstant.Message.THE_USER_HAS_NOT_BEEN_ASSIGNED_SANDBOX_DATA));
        }

        // Deposit
        List<CustomerMasterEntity> customerMasters = customerMasterMapper.selectListWithRelationsByQueryAs(
                QueryWrapper.create()
                        .select(customer_master.all_columns)
                        .from(customer_master)
                        .where(customer_master.customer_number.in(customerNumberList)),
                CustomerMasterEntity.class
        );
        excuteInsert(dbName, customerMasters, "customermaster", new CustomerMasterEntity());
        // account_posting
        List<String> accounts = new ArrayList<>();
        Map<String, String> accountCustomerMap = new HashMap<>();
        List<SavingAccountMasterEntity> savingList = savingAccountMasterMapper.selectListWithRelationsByQueryAs(
                QueryWrapper.create()
                        .select(saving_account_master.all_columns)
                        .from(saving_account_master)
                        .where(saving_account_master.customer_number.in(customerNumberList)),
                SavingAccountMasterEntity.class
        );
        List<CurrentAccountMasterEntity> currentList = currentAccountMasterMapper.selectListWithRelationsByQueryAs(
                QueryWrapper.create()
                        .select(current_account_master.all_columns)
                        .from(current_account_master)
                        .where(current_account_master.customer_number.in(customerNumberList)),
                CurrentAccountMasterEntity.class
        );
        excuteInsert(dbName, savingList, "savingaccountmaster", new SavingAccountMasterEntity());
        excuteInsert(dbName, currentList, "currentaccountmaster", new CurrentAccountMasterEntity());

        // 补充分析数据库没有的字段CustomerNumber用的
        accountCustomerMap.putAll(savingList.stream().collect(Collectors.toMap(SavingAccountMasterEntity::getAccountNumber, SavingAccountMasterEntity::getCustomerNumber)));
        accountCustomerMap.putAll(currentList.stream().collect(Collectors.toMap(CurrentAccountMasterEntity::getAccountNumber, CurrentAccountMasterEntity::getCustomerNumber)));
        accounts.addAll(savingList.stream().map(SavingAccountMasterEntity::getAccountNumber).collect(Collectors.toList()));
        accounts.addAll(currentList.stream().map(CurrentAccountMasterEntity::getAccountNumber).collect(Collectors.toList()));
        if (accounts.size() > 0) {
            List<TransactionLogEntity> accountPostLogs = transactionLogMapper.selectListWithRelationsByQueryAs(
                    QueryWrapper.create()
                            .select(transaction_log.all_columns)
                            .from(transaction_log)
                            .where(transaction_log.account_number.in(accounts)),
                    TransactionLogEntity.class
            );
            excuteInsert(dbName, accountPostLogs, "transactionlog", new TransactionLogEntity());
        }

        // Foreign Exchange
        List<FexAccountMasterEntity> fexAccountMasters = fexAccountMasterMapper.selectListWithRelationsByQueryAs(
                QueryWrapper.create()
                        .select(fex_account_master.all_columns)
                        .from(fex_account_master)
                        .where(fex_account_master.account_number.in(customerNumberList)),
                FexAccountMasterEntity.class
        );
        excuteInsert(dbName, fexAccountMasters, "fexaccountmaster", new FexAccountMasterEntity());
        List<String> fexAccountMasterNums = fexAccountMasters.stream().map(FexAccountMasterEntity::getAccountNumber).collect(Collectors.toList());
        if (fexAccountMasterNums.size() > 0) {
            List<ForeignExchangeEntity> foreignExchangeList = foreignExchangeMapper.selectListWithRelationsByQueryAs(
                    QueryWrapper.create()
                            .select(foreign_exchange.all_columns)
                            .from(foreign_exchange)
                            .where(foreign_exchange.account_number.in(fexAccountMasterNums)),
                    ForeignExchangeEntity.class
            );
            excuteInsert(dbName, foreignExchangeList, "foreign_exchange", new ForeignExchangeEntity());
            List<FexAccountDetailEntity> fexAccountDetails = fexAccountDetailMapper.selectListWithRelationsByQueryAs(
                    QueryWrapper.create()
                            .select(fex_account_detail.all_columns)
                            .from(fex_account_detail)
                            .where(fex_account_detail.account_number.in(fexAccountMasterNums)),
                    FexAccountDetailEntity.class
            );
            excuteInsert(dbName, fexAccountDetails, "fexaccountdetail", new FexAccountDetailEntity());
        }

        // Fund
        List<MutualFundAccountMasterEntity> fundAccounts = mutualFundAccountMasterMapper.selectListWithRelationsByQueryAs(
                QueryWrapper.create()
                        .select(mutual_fund_account_master.all_columns)
                        .from(mutual_fund_account_master)
                        .where(mutual_fund_account_master.customer_number.in(customerNumberList)),
                MutualFundAccountMasterEntity.class
        );
        excuteInsert(dbName, fundAccounts, "mutualfundaccountmaster", new MutualFundAccountMasterEntity());
        List<String> fundAccountNums = fundAccounts.stream().map(MutualFundAccountMasterEntity::getAccountNumber).collect(Collectors.toList());
        if (fundAccountNums.size() > 0) {
            List<MutualFundPlatformLogEntity> fundLogs = mutualFundPlatformLogMapper.selectListWithRelationsByQueryAs(
                    QueryWrapper.create()
                            .select(mutual_fund_platform_log.all_columns)
                            .from(mutual_fund_platform_log)
                            .where(mutual_fund_platform_log.account_number.in(fundAccountNums)),
                    MutualFundPlatformLogEntity.class
            );
            excuteInsert(dbName, fundLogs, "mutualfundplatformlog", new MutualFundPlatformLogEntity());
            List<MutualFundHoldingsEntity> fundHoldings = mutualFundHoldingsMapper.selectListWithRelationsByQueryAs(
                    QueryWrapper.create()
                            .select(mutual_fund_holdings.all_columns)
                            .from(mutual_fund_holdings)
                            .where(mutual_fund_holdings.account_number.in(fundAccountNums)),
                    MutualFundHoldingsEntity.class
            );
            excuteInsert(dbName, fundHoldings, "mutualfundholdings", new MutualFundHoldingsEntity());
        }

        // Stock
        List<StockTradingAccountMasterEntity> stockAccounts = stockTradingAccountMasterMapper.selectListWithRelationsByQueryAs(
                QueryWrapper.create()
                        .select(stock_trading_account_master.all_columns)
                        .from(stock_trading_account_master)
                        .where(stock_trading_account_master.customer_number.in(customerNumberList)),
                StockTradingAccountMasterEntity.class
        );
        excuteInsert(dbName, stockAccounts, "stocktradingaccountmaster", new StockTradingAccountMasterEntity());
        List<String> stockAccountNums = stockAccounts.stream().map(StockTradingAccountMasterEntity::getAccountNumber).collect(Collectors.toList());
        if (stockAccountNums.size() > 0) {
            List<StockPlatformLogEntity> stockLogs = stockPlatformLogMapper.selectListWithRelationsByQueryAs(
                    QueryWrapper.create()
                            .select(stock_platform_log.all_columns)
                            .from(stock_platform_log)
                            .where(stock_platform_log.account_number.in(stockAccountNums)),
                    StockPlatformLogEntity.class
            );
            excuteInsert(dbName, stockLogs, "stockplatformlog", new StockPlatformLogEntity());
            List<StockHoldingsEntity> stockHoldings = stockHoldingsMapper.selectListWithRelationsByQueryAs(
                    QueryWrapper.create()
                            .select(stock_holdings.all_columns)
                            .from(stock_holdings)
                            .where(stock_holdings.account_number.in(stockAccountNums)),
                    StockHoldingsEntity.class
            );
            excuteInsert(dbName, stockHoldings, "stockholdings", new StockHoldingsEntity());
        }

        // CreditCard
        List<CreditcardMasterEntity> creditcardMasterList = creditcardMasterMapper.selectListWithRelationsByQueryAs(
                QueryWrapper.create()
                        .select(creditcard_master.all_columns)
                        .from(creditcard_master)
                        .where(creditcard_master.associated_cus_num.in(customerNumberList)),
                CreditcardMasterEntity.class
        );
        excuteInsert(dbName, creditcardMasterList, "creditcardmaster", new CreditcardMasterEntity());
        List<String> creditcardMasterNums = creditcardMasterList.stream().map(CreditcardMasterEntity::getCreditCardNumber).collect(Collectors.toList());
        if (creditcardMasterNums.size() > 0) {
            List<CreditcardTransactionDetailEntity> creditcardTransactionDetails = creditcardTransactionDetailMapper.selectListWithRelationsByQueryAs(
                    QueryWrapper.create()
                            .select(creditcard_transaction_detail.all_columns)
                            .from(creditcard_transaction_detail)
                            .where(creditcard_transaction_detail.credit_card_number.in(creditcardMasterNums)),
                    CreditcardTransactionDetailEntity.class
            );
            excuteInsert(dbName, creditcardTransactionDetails, "creditcardtransactiondetail", new CreditcardTransactionDetailEntity());
            List<CreditcardPointReferenceEntity> pointReferences = creditcardPointReferenceMapper.selectListWithRelationsByQueryAs(
                    QueryWrapper.create()
                            .select(creditcard_point_reference.all_columns)
                            .from(creditcard_point_reference)
                            .where(creditcard_point_reference.credit_card_number.in(creditcardMasterNums)),
                    CreditcardPointReferenceEntity.class
            );
            excuteInsert(dbName, pointReferences, "creditcardpointreference", new CreditcardPointReferenceEntity());
            List<CreditcardStatementEntity> statementList = creditcardStatementMapper.selectListWithRelationsByQueryAs(
                    QueryWrapper.create()
                            .select(creditcard_statement.all_columns)
                            .from(creditcard_statement)
                            .where(creditcard_statement.credit_card_number.in(creditcardMasterNums)),
                    CreditcardStatementEntity.class
            );
            excuteInsert(dbName, statementList, "creditcardstatement", new CreditcardStatementEntity());
        }

        // Term Deposit
        List<TermDepositMasterEntity> termAccounts = termDepositMasterMapper.selectListWithRelationsByQueryAs(
                QueryWrapper.create()
                        .select(term_deposit_master.all_columns)
                        .from(term_deposit_master)
                        .where(term_deposit_master.customer_number.in(customerNumberList)),
                TermDepositMasterEntity.class
        );
        excuteInsert(dbName, termAccounts, "termdepositmaster", new TermDepositMasterEntity());
        List<String> termAccountNums = termAccounts.stream().map(TermDepositMasterEntity::getAccountNumber).collect(Collectors.toList());
        if (termAccountNums.size() > 0) {
            List<TermDepositDetailEntity> depositDetailList = termDepositDetailMapper.selectListWithRelationsByQueryAs(
                    QueryWrapper.create()
                            .select(term_deposit_detail.all_columns)
                            .from(term_deposit_detail)
                            .where(term_deposit_detail.account_number.in(termAccountNums)),
                    TermDepositDetailEntity.class
            );
            excuteInsert(dbName, depositDetailList, "termdepositdetail", new TermDepositDetailEntity());
        }

        // Mortgage Loan
        List<LoanAccountMasterEntity> loanAccounts = loanAccountMasterMapper.selectListWithRelationsByQueryAs(
                QueryWrapper.create()
                        .select(loan_account_master.all_columns)
                        .from(loan_account_master)
                        .where(loan_account_master.customer_number.in(customerNumberList)),
                LoanAccountMasterEntity.class
        );
        excuteInsert(dbName, loanAccounts, "loanaccountmaster", new LoanAccountMasterEntity());
        List<String> loanAccountNums = loanAccounts.stream().map(LoanAccountMasterEntity::getAccountNumber).collect(Collectors.toList());
        if (loanAccountNums.size() > 0) {
            List<MortgageLoanTransactionLogEntity> mortgageloantransactionlogs = mortgageLoanTransactionLogMapper.selectListWithRelationsByQueryAs(
                    QueryWrapper.create()
                            .select(mortgage_loan_transaction_log.all_columns)
                            .from(mortgage_loan_transaction_log)
                            .where(mortgage_loan_transaction_log.account_number.in(loanAccountNums)),
                    MortgageLoanTransactionLogEntity.class
            );
            List<MortgageLoanContractDetailEntity> loanContractDetails = mortgageLoanContractDetailMapper.selectListWithRelationsByQueryAs(
                    QueryWrapper.create()
                            .select(mortgage_loan_contract_detail.all_columns)
                            .from(mortgage_loan_contract_detail)
                            .where(mortgage_loan_contract_detail.account_number.in(loanAccountNums)),
                    MortgageLoanContractDetailEntity.class
            );

            excuteInsert(dbName, mortgageloantransactionlogs, "mortgageloantransactionlog", new MortgageLoanContractDetailEntity());
            // loancontractdetail
            excuteInsert(dbName, loanContractDetails, "mortgageloancontractdetail", new MortgageLoanContractDetailEntity());
            List<MortgageLoanContractMasterEntity> contractMasterList = mortgageLoanContractMasterMapper.selectListWithRelationsByQueryAs(
                    QueryWrapper.create()
                            .select(mortgage_loan_contract_master.all_columns)
                            .from(mortgage_loan_contract_master)
                            .where(mortgage_loan_contract_master.account_number.in(loanAccountNums)),
                    MortgageLoanContractMasterEntity.class
            );
            excuteInsert(dbName, contractMasterList, "mortgageloancontractmaster", new MortgageLoanContractMasterEntity());
        }

        // payment
        List<PaymentTransactionEntity> paymentTransactions = paymentTransactionMapper.selectListWithRelationsByQueryAs(
                QueryWrapper.create()
                        .select(payment_transaction.all_columns)
                        .from(payment_transaction)
                        .where(payment_transaction.customer_number.in(customerNumberList)),
                PaymentTransactionEntity.class
        );
        excuteInsert(dbName, paymentTransactions, "paymenttransaction", new PaymentTransactionEntity());

        // Travel Insurance
        Set<String> customerIdList = customerMasters.stream().map(CustomerMasterEntity::getCustomerId).collect(Collectors.toSet());
        List<InsuranceQuoteMasterEntity> quoteMasterList = new ArrayList<>();
        if (customerIdList.size() > 0) {
            quoteMasterList = insuranceQuoteMasterMapper.selectListWithRelationsByQueryAs(
                    QueryWrapper.create()
                            .select(insurance_quote_master.all_columns)
                            .from(insurance_quote_master)
                            .where(insurance_quote_master.customer_id.in(customerIdList)),
                    InsuranceQuoteMasterEntity.class
            );
            excuteInsert(dbName, quoteMasterList, "insurancequotemaster", new InsuranceQuoteMasterEntity());
        }
        Set<String> quoteIdSet = quoteMasterList.stream().map(InsuranceQuoteMasterEntity::getQuoteId).collect(Collectors.toSet());
        if (quoteIdSet.size() > 0) {
            List<InsuranceQuoteDetailEntity> quoteDetailList = insuranceQuoteDetailMapper.selectListWithRelationsByQueryAs(
                    QueryWrapper.create()
                            .select(insurance_quote_detail.all_columns)
                            .from(insurance_quote_detail)
                            .where(insurance_quote_detail.quote_id.in(quoteIdSet)),
                    InsuranceQuoteDetailEntity.class
            );
            excuteInsert(dbName, quoteDetailList, "insurancequotedetail", new InsuranceQuoteDetailEntity());
        }
        Set<String> productCodeSet = quoteMasterList.stream().map(InsuranceQuoteMasterEntity::getProductCode).collect(Collectors.toSet());
        if (productCodeSet.size() > 0) {
            List<InsuranceProductDetailEntity> productDetailList = insuranceProductDetailMapper.selectListWithRelationsByQueryAs(
                    QueryWrapper.create()
                            .select(insurance_product_detail.all_columns)
                            .from(insurance_product_detail)
                            .where(insurance_product_detail.product_code.in(productCodeSet)),
                    InsuranceProductDetailEntity.class
            );
            excuteInsert(dbName, productDetailList, "insuranceproductdetail", new InsuranceProductDetailEntity());
        }

        return Response.success();
    }

    @Override
    public Response<?> findStaffByUserId(String userId) {
        userId = getParentUserId(userId);
        List<LbsStaffEntity> result = lbsStaffMapper.selectListByQueryAs(
                QueryWrapper.create()
                        .select(lbs_staff.all_columns)
                        .from(lbs_staff)
                        .where(lbs_staff.user_id.eq(userId)),
                LbsStaffEntity.class
        );
        return Response.success(result);
    }

    @Override
    public Response<?> findTokenByUserId(String userId) {
        userId = getParentUserId(userId);
        List<ApiTokenEntity> result = apiTokenMapper.selectListByQueryAs(
                QueryWrapper.create()
                        .select(api_token.all_columns)
                        .from(api_token)
                        .where(api_token.user_id.eq(userId)),
                ApiTokenEntity.class
        );
        return Response.success(result);
    }

    @Override
    public Response<?> findInsuranceUser(String userId) {
        userId = getParentUserId(userId);
        List<InsuranceUserEntity> insuranceUserList = insuranceUserMapper.selectAll();
        return Response.success(insuranceUserList);
    }

    @Override
    public Response<?> groupGetTokenTable(String groupID) {
        LearnGroupEntity group = groupService.getById(groupID);
        JSONArray array = new JSONArray();
        if (ObjUtil.isNotNull(group)) {
            String member = group.getMember();
            if (member != null) {
                List<String> students = JSON.parseArray(member, String.class);
                students.forEach(studentID -> {
                    JSONObject response = new JSONObject();
                    UserEntity consumer = userService.getMapper().selectOneByCondition(user.email.eq(studentID));
                    if (consumer != null) {
                        response.put("membername", consumer.getEmail());
                        List<ApiTokenEntity> userTokens = apiTokenMapper.selectListByCondition(api_token.user_id.eq(consumer.getId()));
                        response.put("memberTokenList", userTokens);
                    }
                    array.add(response);
                });
            }
        }
        return Response.success(array);
    }

    @Override
    public Response<?> getTaskStatus(String userId) {
        userId = getParentUserId(userId);
        List<TaskQueueEntity> taskQueues = taskQueueMapper.selectListByQuery(
                QueryWrapper.create()
                        .select(task_queue.all_columns)
                        .from(task_queue)
                        .where(task_queue.user_id.eq(userId))
                        .orderBy(task_queue.create_date, false)
        );
        if (taskQueues.size() == 0) {
            return Response.failed(0, MessageUtils.message(ResponseConstant.Message.CANNOT_FIND_TASK));
        }
        return Response.success(taskQueues.get(0).getTaskStatus());
    }

    @Override
    public Response<?> findByUserId(String userId) {
        userId = getParentUserId(userId);
        List<ApiTokenEntity> list = apiTokenMapper.selectListByCondition(api_token.user_id.eq(userId));
        Map<String, String> map = new HashMap<>();
        if (list.size() > 0) {
            map.put("sandboxId", list.get(0).getSandboxId());
        }
        return Response.success(ResponseConstant.Code.OPERATION_SUCCESS, map);
    }

    @Override
    public Response<?> findUser(String roleName, String email, String company, Integer pager,
                                Integer pageSize, Long startRegisterTime, Long endRegisterTime) {
        Page<UserEntity> page = userService.getMapper().paginateWithRelations(
                pager,
                pageSize,
                userService.queryChain()
                        .select(distinct(user.all_columns))
                        .from(user)
                        .innerJoin(user_role)
                        .on(user.id.eq(user_role.user_id))
                        .innerJoin(role)
                        .on(user_role.role_id.eq(role.id))
                        .leftJoin(organization)
                        .on(user.organization_id.eq(organization.organization_id))
                        .where(role.role_name.eq(roleName, If::hasText))
                        .and(user.email.like(email, If::hasText))
                        .and(organization.organization_name.like(company, If::hasText))
                        .and(user.create_time.ge(startRegisterTime, If::notNull))
                        .and(user.create_time.le(endRegisterTime, If::notNull))

        );
        return Response.success(new Paginate<>(page.getTotalRow(), page.getRecords()));
    }

    @Override
    public Response<?> getSandboxlist(String userId, String sandboxId) {
        List<ApiTokenEntity> apiUserTokens;
        if (StrUtil.isBlank(userId) && StrUtil.isBlank(sandboxId)) {
            apiUserTokens = apiTokenMapper.selectListByCondition(api_token.user_id.isNotNull());
        } else {
            apiUserTokens = apiTokenMapper.selectListByCondition(api_token.user_id.eq(userId, If::hasText).and(api_token.sandbox_id.eq(sandboxId, If::hasText)));
        }
        Map<String, List<ApiTokenEntity>> map = new HashMap<>();
        if (apiUserTokens.size() > 0) {
            apiUserTokens.forEach(data -> {
                if (map.containsKey(data.getUserId())) {
                    map.get(data.getUserId()).add(data);
                } else {
                    List<ApiTokenEntity> list = new ArrayList<>();
                    list.add(data);
                    map.put(data.getUserId(), list);
                }
            });
            List<JSONObject> responseObj = new ArrayList<>();
            for (Map.Entry<String, List<ApiTokenEntity>> entry : map.entrySet()) {
                String userIdkey = entry.getKey();
                JSONObject obj = new JSONObject();
                obj.put("userId", userIdkey);
                List<ApiTokenEntity> tokens = entry.getValue();
                obj.put("sandboxId", tokens.get(0).getSandboxId());
                List<JSONObject> list = new ArrayList<>();
                tokens.forEach(data -> {
                    JSONObject tokenObj = new JSONObject();
                    tokenObj.put("id", data.getId());
                    tokenObj.put("DeveloperId", data.getDeveloperId());
                    tokenObj.put("Loginname", data.getLoginName());
                    tokenObj.put("LoginPwd", data.getLoginPwd());
                    tokenObj.put("Token", data.getUserToken());
                    tokenObj.put("CustomerNumber", data.getCustomerNumber());
                    list.add(tokenObj);
                });
                obj.put("tokenList", list);
                responseObj.add(obj);
            }
            return Response.success(responseObj);
        }
        return Response.success(Lists.newArrayList());
    }

    @Override
    @Transactional
    public Response<?> emptyApiTokenByUserId(List<DelApiTokenDto> delApiTokens) {
        if (delApiTokens.size() <= 0) {
            return Response.failed(-1, MessageUtils.message(ResponseConstant.Message.DELETE_FAILED));
        }
        for (DelApiTokenDto delApiToken : delApiTokens) {
            apiTokenService.updateChain()
                    .set(api_token.user_id, null)
                    .set(api_token.sandbox_id, null)
                    .where(api_token.user_id.eq(delApiToken.getUserId()))
                    .and(api_token.id.eq(delApiToken.getId()))
                    .update();
        }
        return Response.success();
    }

    @Override
    public Response<?> createTokenInfo(JSONObject jsonObject) {
        String userId = jsonObject.getString("userId");
        userId = getParentUserId(userId);
        jsonObject.put("userId", userId);
        if (!jsonObject.containsKey("sandboxid")) {
            jsonObject.put("sandboxid", UUID.randomUUID().toString().replace("-", ""));
        } else if (StrUtil.isBlank(jsonObject.getString("sandboxid"))) {
            jsonObject.put("sandboxid", UUID.randomUUID().toString().replace("-", ""));
        }
        TaskQueueEntity taskQueue = new TaskQueueEntity();
        taskQueue.setTaskId(RandomGeneratorUtil.generateApiIdRandom(10));
        taskQueue.setUserId(jsonObject.getString("userId"));
        taskQueue.setTaskStatus("processing");
        taskQueue.setTaskLog("Task is working");
        taskQueue.setCreateDate(new Date());
        taskQueueMapper.insertOrUpdate(taskQueue);
        createSandbox(jsonObject, taskQueue);
        return Response.success(ResponseConstant.Code.OPERATION_SUCCESS, taskQueue, "processing");
    }

    @Override
    public Response<?> createStaffInfo(JSONObject jsonObject) {
        String userId = jsonObject.getString("userId");
        int createSize = Integer.parseInt(jsonObject.getString("createSize"));
        List<LbsStaffEntity> lbsStaffEntityList = lbsStaffMapper.selectListByQuery(
                QueryWrapper.create().select(lbs_staff.all_columns)
                        .from(lbs_staff)
                        .where(lbs_staff.user_id.isNull())
                        .limit(createSize)
        );
        lbsStaffEntityList.forEach(apiToken -> {
            apiToken.setUserId(userId);
        });
        for (LbsStaffEntity lbsStaffEntity : lbsStaffEntityList) {
            lbsStaffMapper.insertOrUpdate(lbsStaffEntity);
        }
        return Response.success();
    }

    @Override
    public Response<?> transferStaffData() {
        List<LbsStaffEntity> lbsStaffEntityList = lbsStaffMapper.selectAll();
        List<String> staffNumberList = lbsStaffEntityList.stream().map(LbsStaffEntity::getStaffNumber).collect(Collectors.toList());

        List<AdminEntity> adminEntityList = adminMapper.selectListByCondition(
                admin.staff_number.notIn(staffNumberList)
        );
        List<RoleEntity> roleEntityList = lbsRoleMapper.selectAll();

        List<LbsStaffEntity> lbsStaffList = new ArrayList<>();
        adminEntityList.forEach(admin -> {
            LbsStaffEntity lbsStaffEntity = new LbsStaffEntity();
            lbsStaffEntity.setBranchCode(admin.getBranchCode());
            lbsStaffEntity.setUserName(admin.getUserName());
            lbsStaffEntity.setPwd(admin.getPwd());
            lbsStaffEntity.setStaffNumber(admin.getStaffNumber());
            lbsStaffEntity.setRole(roleEntityList.stream().filter(r -> r.getId().equals(admin.getRoleId())).findAny().orElseThrow(() -> new RuntimeException("Role not found")).getRole());
            lbsStaffEntity.setCreateDate(new Date());
            lbsStaffList.add(lbsStaffEntity);
        });

        lbsStaffMapper.insertBatch(lbsStaffList);
        return Response.success();
    }

    private String getParentUserId(String userId) {
//        UserEntity consumer = userService.getById(userId);
//        if (ObjUtil.isNotNull(consumer)) {
//            String contactEmail = organizationMapper.selectOneByQueryAs(
//                    QueryWrapper.create()
//                            .select(organization.contact_email)
//                            .from(organization)
//                            .where(organization.organization_id.eq(consumer.getOrganizationId())),
//                    String.class
//            );
//            if (StrUtil.isNotBlank(contactEmail)) {
//                UserEntity parentUser = userService.getOneAs(
//                        QueryWrapper.create()
//                                .select(user.all_columns)
//                                .from(user)
//                                .where(user.email.eq(contactEmail)),
//                        UserEntity.class
//                );
//                if (parentUser != null) {
//                    userId = parentUser.getId();
//                }
//            }
//        }
        return userId;
    }

    /**
     * 将数据插入数据库
     *
     * @param dbName
     * @param transactions
     * @param tableName
     * @param entity
     */
    private void excuteInsert(String dbName, Object transactions, String tableName, Object entity) {
        TableModel tableModel = new TableModel();
        // 获取表字段
        tableModel.setColumns(GetColumnsByReflectUtils.getColumns(entity));
        // 获取表数据
        tableModel.setLineData(GetColumnsByReflectUtils.getColumnsValue(transactions));
        tableModel.setTableName(tableName);
        boolean bool = false;
        try {
            bool = DataSourceUtils.generateInsertSql(dbName, tableModel);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public Future<String> createSandbox(JSONObject jsonObject, TaskQueueEntity task) {
        try {
            String userID = jsonObject.getString("userId");
            int createSize = Integer.parseInt(jsonObject.getString("createSize"));
            String sandboxId = jsonObject.getString("sandboxid");
            List<ApiTokenEntity> apiUserTokenList = apiTokenMapper.selectListByQuery(
                    QueryWrapper.create().select(api_token.all_columns)
                            .from(api_token)
                            .where(api_token.user_id.isNull())
                            .limit(createSize)
            );
            apiUserTokenList.forEach(apiToken -> {
                apiToken.setUserId(userID);
                apiToken.setSandboxId(sandboxId);
            });
            for (ApiTokenEntity apiTokenEntity : apiUserTokenList) {
                apiTokenMapper.insertOrUpdate(apiTokenEntity);
            }
            task.setTaskLog("Success");
            task.setTaskStatus("completed");
            taskQueueMapper.insertOrUpdate(task);
            return new AsyncResult<>("Completed");
        } catch (Exception e) {
            e.printStackTrace();
            task.setTaskLog(e.getMessage());
            task.setTaskStatus("error");
            taskQueueMapper.insertOrUpdate(task);
            return new AsyncResult<>(e.getMessage());
        }
    }
}
