package com.simnectz.soiplus.business.practicalplatform.sandbox.domain.entity.test2_payment;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.Table;
import lombok.*;

@Data
@With
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(value = "paymenttransaction",dataSource = "payment")
public class PaymentTransactionEntity {

    @Id
    @Column(value = "ID")
    private String id;
    @Column(value = "PayeeCategoryID")
    private String payeeCategoryID;
    @Column(value = "PayeeCategory")
    private String payeeCategory;
    @Column(value = "PayeeID")
    private String payeeID;
    @Column(value = "PayeeNumber")
    private String payeeNumber;
    @Column(value = "CustomerNumber")
    private String customerNumber;
    @Column(value = "SandBoxId")
    private String sandBoxId;
    @Column(value = "TransactionDealNumber")
    private String transactionDealNumber;
    @Column(value = "CustomerAccountType")
    private String customerAccountType;
    @Column(value = "CustomerAccountNumber")
    private String customerAccountNumber;
    @Column(value = "TransactionTime")
    private String transactionTime;
    @Column(value = "TransactionAmount")
    private String transactionAmount;
    @Column(value = "TransactionCurrency")
    private String transactionCurrency;
    @Column(value = "PaymentEffectiveDay")
    private String paymentEffectiveDay;
    @Column(value = "Remarks")
    private String remarks;
    @Column(value = "LastUpdateDate")
    private String lastUpdateDate;
    @Column(value = "CreateDate")
    private String createDate;
    @Column(value = "Status")
    private String status;

}
