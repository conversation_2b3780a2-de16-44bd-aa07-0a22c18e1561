package com.simnectz.soiplus.business.practicalplatform.sandbox.domain.entity.test2_creditcard;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.Table;
import lombok.*;

@Data
@With
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(value = "creditcardstatement", dataSource = "creditcard")
@EqualsAndHashCode()
public class CreditcardStatementEntity {

    @Id
    @Column(value = "ID")
    private String id;

    @Column(value = "CountryCode")
    private String countryCode;

    @Column(value = "ClearingCode")
    private String clearingCode;

    @Column(value = "BranchCode")
    private String branchCode;

    @Column(value = "SandBoxId")
    private String sandBoxId;

    @Column(value = "CreditCardNumber")
    private String creditCardNumber;

    @Column(value = "HolderName")
    private String holderName;

    @Column(value = "CustomerNumber")
    private String customerNumber;

    @Column(value = "StatementDate")
    private String statementDate;

    @Column(value = "RepaymentDueDate")
    private String repaymentDueDate;

    @Column(value = "RepaymentAmount")
    private String repaymentAmount;

    @Column(value = "MinimumPayment")
    private String minimumPayment;

    @Column(value = "NeedInterest")
    private String needInterest;

    @Column(value = "StatementPath")
    private String statementPath;

    @Column(value = "CreateDate")
    private String createDate;

}
