package com.simnectz.soiplus.business.practicalplatform.apimarket.domain.entity;

import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import com.mybatisflex.core.keygen.KeyGenerators;
import lombok.*;

import java.io.Serializable;
import java.util.Date;

@Data
@With
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
@Table(value = "api_class_sub")
public class ApiSubClassEntity implements Serializable {

    private static final long serialVersionUID = -1419850038252507689L;

    @Id(keyType = KeyType.Generator, value = KeyGenerators.uuid)
    private String id;

    private String apiClassId;

    private String classSubName;

    private String classSubNameEn;

    private String details;

    private String detailsEn;

    private byte[] image;

    private Long createTime =new Date().getTime();
}
