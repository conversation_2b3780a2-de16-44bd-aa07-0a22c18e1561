package com.simnectz.soiplus.business.practicalplatform.dataanalysis.datavisualization.domain.entity;

import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import com.mybatisflex.core.keygen.KeyGenerators;
import lombok.*;

import java.io.Serializable;
import java.util.Date;

@Data
@With
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(value = "data_analysis_view")
@EqualsAndHashCode()
public class ViewEntity implements Serializable {

    private static final long serialVersionUID = 6660541786269230222L;

    @Id(keyType = KeyType.Generator, value = KeyGenerators.uuid)
    private String id;

    private String name;

    private String sourceId;

    private String userId;

    /**
     * 表连接,字段修理信息
     */
    private String config;

    /**
     * dimension&measure自定义
     */
    private String model;

    /**
     * sql语句
     */
    private String sql;

    private Date createTime;

    private Date updateTime;
}

