package com.simnectz.soiplus.business.practicalplatform.codingexercise.utils;

import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.simnectz.soiplus.business.system.config.CompilerProperties;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.IOException;
import java.util.Map;

@Data
@EqualsAndHashCode(callSuper = true)
public class CppCompiler extends Compiler {

    private final CompilerProperties compilerProperties = SpringUtil.getBean(CompilerProperties.class);

    public final String CPP_CODE_PATH = System.getProperty("user.dir") + compilerProperties.getCpp().getPath();

    public CppCompiler(String code, String userid) {
        this.folderName = userid;
        FileUtils.createFile(CPP_CODE_PATH + folderName, CPP_CODE_PATH + folderName + "/" + compilerProperties.getCpp().getFileName() + ".cpp", code);
    }

    @Override
    public void compilerRun() throws IOException, InterruptedException {
        compileErrorMessage = ProgramUtils.compiler("g++ " + CPP_CODE_PATH + folderName + "/" + compilerProperties.getCpp().getFileName() + ".cpp -o " + CPP_CODE_PATH + folderName + "/" + compilerProperties.getCpp().getFileName());
        FileUtils.removeFile(CPP_CODE_PATH + folderName + "/" + compilerProperties.getCpp().getFileName() + ".cpp");

        if (StrUtil.isBlank(compileErrorMessage)) {
            // Run
            Map<String, String> runResult = ProgramUtils.run(CPP_CODE_PATH + folderName + "/" + compilerProperties.getCpp().getFileName());
            runErrorMessage = runResult.get("runError");
            runOutputMessage = runResult.get("runOutput");
            FileUtils.removeFile(CPP_CODE_PATH + folderName + "/" + compilerProperties.getCpp().getFileName());
        }
    }
}
