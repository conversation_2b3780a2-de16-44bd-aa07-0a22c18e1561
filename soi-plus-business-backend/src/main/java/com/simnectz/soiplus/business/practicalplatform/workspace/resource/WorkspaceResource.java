package com.simnectz.soiplus.business.practicalplatform.workspace.resource;

import com.simnectz.soiplus.business.practicalplatform.workspace.service.WorkspaceService;
import com.simnectz.soiplus.business.system.domain.vo.Response;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 工作区管理
 */
@RestController
@RequestMapping("v1/practical/workspace")
@RequiredArgsConstructor
public class WorkspaceResource {

    private final WorkspaceService workSpaceService;

    /**
     * 技术开发 - 工作区 - 查询工作区列表信息
     * @param userID
     * @return
     */
    @GetMapping("/getworkspace")
    public ResponseEntity<Response<?>> getWorkspace(String userID){
        return ResponseEntity.ok(workSpaceService.getWorkspace(userID));
    }
}
