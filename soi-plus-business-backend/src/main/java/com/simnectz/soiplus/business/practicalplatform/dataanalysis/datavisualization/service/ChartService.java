package com.simnectz.soiplus.business.practicalplatform.dataanalysis.datavisualization.service;


import com.mybatisflex.core.service.IService;
import com.simnectz.soiplus.business.practicalplatform.dataanalysis.datavisualization.domain.dto.ChartInfoDto;
import com.simnectz.soiplus.business.practicalplatform.dataanalysis.datavisualization.domain.dto.ChartUpdateDto;
import com.simnectz.soiplus.business.practicalplatform.dataanalysis.datavisualization.domain.entity.ChartEntity;
import com.simnectz.soiplus.business.system.domain.vo.Response;

/**
 * Chart service
 */
public interface ChartService extends IService<ChartEntity> {

    Response<?> getChartByUser(String userId, int page, int pageSize);

    Response<?> deleteChart(String chartId);

    Response<?> saveChart(ChartInfoDto chartInfoDto);

    Response<?> getChartInfo(String chartId);

    Response<?> updateChart(String chartId, String userId, ChartUpdateDto chartUpdateDto);

}
