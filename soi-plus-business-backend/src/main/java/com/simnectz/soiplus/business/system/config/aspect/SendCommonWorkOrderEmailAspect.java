package com.simnectz.soiplus.business.system.config.aspect;

import com.simnectz.authorize.system.domain.bo.User;
import com.simnectz.soiplus.business.learningspace.course.domain.entity.CourseEntity;
import com.simnectz.soiplus.business.learningspace.course.service.CourseService;
import com.simnectz.soiplus.business.learningspace.interactivespace.domain.enitity.CommonWorkOrderEntity;
import com.simnectz.soiplus.business.learningspace.subjectdefinition.domain.entity.SubjectDefinitionEntity;
import com.simnectz.soiplus.business.learningspace.subjectdefinition.service.SubjectDefinitionService;
import com.simnectz.soiplus.business.system.config.SystemProperties;
import com.simnectz.soiplus.business.system.constant.OrderStatusConstant;
import com.simnectz.soiplus.business.system.constant.OrderTypeConstant;
import com.simnectz.soiplus.business.system.domain.vo.Response;
import com.simnectz.soiplus.business.system.utils.DateUtils;
import com.simnectz.soiplus.business.system.utils.MailUtils;
import com.simnectz.soiplus.business.system.utils.SecurityUtils;
import lombok.RequiredArgsConstructor;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Aspect
@RequiredArgsConstructor
@Component
public class SendCommonWorkOrderEmailAspect {

    private final SubjectDefinitionService subjectDefinitionService;
    private final CourseService courseService;
    private final SystemProperties systemProperties;

    @Value("${send.email.jump.url}")
    private String sendEmailJumpUrl;

    @Pointcut("execution(public * com.simnectz.soiplus.business.learningspace.interactivespace.service.impl.CommonWorkOrderServiceImpl.creatCommonWorkOrder(..))")
    public void sendCommonWorkOrderEmailAspect() {
    }

    @Around("sendCommonWorkOrderEmailAspect()")
    public Response<?> sendCommonWorkOrderEmailAspect(ProceedingJoinPoint proceedingJoinPoint) throws Throwable {

        Object[] args = proceedingJoinPoint.getArgs();
        List<Object> argList = Arrays.asList(args);

        CommonWorkOrderEntity commonWorkOrder = null;
        try {
            Response<?> responseMsg = (Response<?>) proceedingJoinPoint.proceed();
            commonWorkOrder = (CommonWorkOrderEntity) responseMsg.getData();
        } catch (Throwable throwable) {
            throwable.printStackTrace();
        }
        String courseId = (String) argList.get(3);
        String subjectId = (String) argList.get(4);
        CourseEntity course = courseService.getById(courseId);
        SubjectDefinitionEntity subject = subjectDefinitionService.getById(subjectId);

        User consumer = SecurityUtils.getLoginUser();
        String userEmail = "";
        if (consumer != null) {
            userEmail = consumer.getEmail();
        }

        String opEmail = systemProperties.getEmail().getFrom();
        String orderStatus = OrderStatusConstant.Ready;
        String orderId = null;
        String createTime = null;
        String content = null;
        try {
            if (commonWorkOrder != null) {
                orderId = commonWorkOrder.getId();
                createTime = DateUtils.getDateTimeFormat(commonWorkOrder.getApplicationDate());
                content = commonWorkOrder.getContent();
            }
        } catch (Exception e) {
            e.printStackTrace();
            return Response.failed(-500, "order not generated ");
        }

        sendOrderEmail(opEmail, orderId, course.getCourseName(),
                subject.getSubjectName(), content != null ? content : "NO_CONTENT!", orderStatus, createTime
        );

        return Response.success();
    }

    private void sendOrderEmail(String opEmail, String id,
                                String courseName, String subjectName, String content, String state, String createTime) {
        Map<String, Object> model = new HashMap<>();
        model.put("host", sendEmailJumpUrl);
        model.put("url", "CommonWorkSheetDetail");
        model.put("id", id);
        model.put("type", OrderTypeConstant.CREAT_COMMON_WORK_SHEET);
        model.put("courseName", courseName);
        model.put("subjectName", subjectName);
        model.put("content", content);
        model.put("state", state);
        model.put("createTime", createTime);
        model.put("handlerName", "---");

        new Thread(() -> {
            MailUtils.sendTemplateMail(
                    opEmail,
                    OrderTypeConstant.CREATE_ORDER,
                    "order-html.ftlh",
                    model
                    );
        }).start();
    }

}
