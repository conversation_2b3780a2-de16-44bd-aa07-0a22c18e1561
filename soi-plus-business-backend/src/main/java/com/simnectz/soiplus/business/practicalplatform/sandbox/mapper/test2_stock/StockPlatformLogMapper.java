package com.simnectz.soiplus.business.practicalplatform.sandbox.mapper.test2_stock;

import com.mybatisflex.annotation.UseDataSource;
import com.simnectz.soiplus.business.practicalplatform.sandbox.domain.entity.test2_stock.StockPlatformLogEntity;
import com.simnectz.soiplus.business.system.mapper.BasicMapper;
import org.apache.ibatis.annotations.Mapper;

/**
 * StockPlatformLog data access layer interface
 */
@Mapper
@UseDataSource("stock")
public interface StockPlatformLogMapper extends BasicMapper<StockPlatformLogEntity> {
}
