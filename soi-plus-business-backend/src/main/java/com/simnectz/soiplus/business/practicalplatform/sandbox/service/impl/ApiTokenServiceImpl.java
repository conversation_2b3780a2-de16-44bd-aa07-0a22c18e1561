package com.simnectz.soiplus.business.practicalplatform.sandbox.service.impl;

import com.mybatisflex.spring.service.impl.ServiceImpl;
import com.simnectz.soiplus.business.practicalplatform.sandbox.domain.entity.ApiTokenEntity;
import com.simnectz.soiplus.business.practicalplatform.sandbox.mapper.ApiTokenMapper;
import com.simnectz.soiplus.business.practicalplatform.sandbox.service.ApiTokenService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

/**
 * Api token service implements
 */
@Service
@RequiredArgsConstructor
public class ApiTokenServiceImpl extends ServiceImpl<ApiTokenMapper, ApiTokenEntity> implements ApiTokenService {
}
