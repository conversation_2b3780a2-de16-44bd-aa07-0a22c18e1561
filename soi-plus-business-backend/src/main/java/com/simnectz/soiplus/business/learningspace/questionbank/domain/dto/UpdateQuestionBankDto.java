package com.simnectz.soiplus.business.learningspace.questionbank.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class UpdateQuestionBankDto implements Serializable {
    private static final long serialVersionUID = 4925277663999784825L;

    @NotBlank(message = "{QUESTION_BANK_ID_INDEX_IS_REQUIRED}")
    private String id;
    @NotBlank(message = "{COURSE_CATEGORY_IS_REQUIRED}")
    private String courseCategoryId;
    @NotBlank(message = "{COURSE_CODE_IS_REQUIRED}")
    private String courseCode;
    @NotBlank(message = "{SUBJECT_CODE_IS_REQUIRED}")
    private String subjectCode;
    @NotBlank(message = "{QUESTION_CONTENT_IS_REQUIRED}")
    private String questionContent;
    @NotBlank(message = "{QUESTION_MODEL_ANSWER_IS_REQUIRED}")
    private String questionModelanswer;
}
