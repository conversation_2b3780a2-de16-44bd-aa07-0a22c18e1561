package com.simnectz.soiplus.business.learningspace.subjectdefinition.domain.vo;

import com.simnectz.soiplus.business.system.domain.entity.BasicEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * Subject details response entity
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class SubjectDefinitionVo extends BasicEntity implements Serializable {


    private static final long serialVersionUID = -1341548047330882691L;

    private String id;
    private String courseName;
    private String courseCode;
    private String courseId;
    private String subjectName;
    private String subjectCode;
    private String subjectCategory;
    private String subjectObjective;
    private String subjectDescription;
    private String subjectPictureUrl;
    private BigDecimal subjectHours;
    private String subjectLevel;
    private String materialsUrl;
    private String videoUrl;
    private String assignmentUrl;

}
