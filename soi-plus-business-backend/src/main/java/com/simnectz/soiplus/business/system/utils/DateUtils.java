package com.simnectz.soiplus.business.system.utils;


import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

public class DateUtils {

    private static Logger logger = LoggerFactory.getLogger(DateUtils.class);

    /**
     * 默认日期格式
     */
    public static final String DATE_DEFAULT_FORMAT = "yyyy-MM-dd";

    /**
     * 默认时间格式
     */
    public static final String DATETIME_DEFAULT_FORMAT = "yyyy-MM-dd HH:mm:ss";

    public static final String TIME_DEFAULT_FORMAT = "HH:mm:ss";

    /**
     * 日期格式化
     */
    private static DateFormat dateFormat = null;

    /**
     * 时间格式化
     */
    private static DateFormat dateTimeFormat = null;

    private static DateFormat timeFormat = null;

    static {
        dateFormat = new SimpleDateFormat(DATE_DEFAULT_FORMAT);
        dateTimeFormat = new SimpleDateFormat(DATETIME_DEFAULT_FORMAT);
        timeFormat = new SimpleDateFormat(TIME_DEFAULT_FORMAT);
    }

    /**
     * 格式化日期函数 内部使用
     * <p>
     * 根据指定格式对当前日期进行格式化
     *
     * @param date   当前日期
     * @param format 需要转化的格式
     * @return String 转换后的字符串格式日期
     */
    public static String parseDate(Date date, String format) {
        SimpleDateFormat dateformat = new SimpleDateFormat(format);
        return dateformat.format(date);
    }

    /**
     * 时间格式化
     *
     * @param date yyyy-MM-dd HH:mm:ss
     * @return
     */
    public static Date formatDateTime(String date) {
        try {
            return dateTimeFormat.parse(date);
        } catch (ParseException e) {
            logger.error("getDateTimeFormat error,parm:{} parse error", date, e);
        }
        return null;
    }

    /**
     * 日期相加
     *
     * @param date 日期
     * @param day  天数
     * @return 返回相加后的日期
     */
    public static Date addDate(Date date, int day) {
        Calendar c = Calendar.getInstance();
        c.setTimeInMillis(getMillis(date) + ((long) day) * 24 * 3600 * 1000);
        return c.getTime();
    }

    /**
     * 返回毫秒
     *
     * @param date 日期
     * @return 返回毫秒
     */
    public static long getMillis(Date date) {
        Calendar c = Calendar.getInstance();
        c.setTime(date);
        return c.getTimeInMillis();
    }

    /**
     * 日期格式化 yyyy-MM-dd HH:mm:ss
     *
     * @param date
     * @return
     */
    public static String getDateTimeFormat(Date date) {
        return dateTimeFormat.format(date);
    }

    /**
     * 获取当前时间 + 中国时区
     *
     * @return
     */
    public static String getDate() {
        SimpleDateFormat sformart = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        long localTimes = new Date().getTime();
        return sformart.format(new Date(localTimes));
    }

}
