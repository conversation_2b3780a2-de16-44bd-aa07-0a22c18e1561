package com.simnectz.soiplus.business.learningspace.interactivespace.resource;

import com.simnectz.soiplus.business.learningspace.interactivespace.domain.enitity.LearnNotificationEntity;
import com.simnectz.soiplus.business.learningspace.interactivespace.service.NotificationService;
import com.simnectz.soiplus.business.system.domain.vo.Response;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("v1/learn/notification")
@RequiredArgsConstructor
public class NotificationResource {

    private final NotificationService notificationService;

    /**
     * 新增通知
     *
     * @param notification
     * @return
     */
    // TODO LearnNotificationEntity中多加了一个参数 subjectId(非必填项)
    @PostMapping("/addnotification")
    public ResponseEntity<Response<?>> addNotification(LearnNotificationEntity notification) {
        return ResponseEntity.ok(notificationService.addNotification(notification));
    }

    /**
     * 批量删除通知
     *
     * @param ids
     * @return
     */
    @PostMapping("/delnotification")
    public ResponseEntity<Response<?>> delNotification(@RequestBody List<String> ids) {
        return ResponseEntity.ok(notificationService.delNotification(ids));
    }

    /**
     * 查询通知列表 - 包括tutor和student角色
     * @param courseID
     * @param userID
     * @param role
     * @return
     */
    @GetMapping("/getnotification")
    public ResponseEntity<Response<?>> getNotification(String courseID, String userID, String role) {
        return ResponseEntity.ok(notificationService.getNotification(courseID, userID, role));
    }

    /**
     * 通知以及分组中选择课程 - 查询该用户以及该角色下的已经订阅的课程列表信息
     *
     * @param userID
     * @param role
     * @return
     */
    @GetMapping("/getcourse")
    public ResponseEntity<Response<?>> getCourse(String userID, String role) {
        return ResponseEntity.ok(notificationService.getCourseList(userID, role));
    }

}
