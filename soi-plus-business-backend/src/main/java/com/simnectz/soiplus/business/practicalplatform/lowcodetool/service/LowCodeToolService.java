package com.simnectz.soiplus.business.practicalplatform.lowcodetool.service;

import com.simnectz.soiplus.business.practicalplatform.lowcodetool.domain.entity.ApiUiEntity;
import com.simnectz.soiplus.business.system.domain.vo.Response;

public interface LowCodeToolService {

    Response<?> getUiIndustry();

    Response<?> queryUi(String classId, String fiName, String componentName);

    Response<?> listUiClassByFiName(String fiName);

    Response<?> saveUi(ApiUiEntity apiUiDTO);

    Response<?> deleteUi(String uiId);

    Response<?> editUi(ApiUiEntity apiUiDTO);

    Response<?> getSimulatorUiList();
}
