package com.simnectz.soiplus.business.practicalplatform.sandbox.mapper.test2_insurance;

import com.mybatisflex.annotation.UseDataSource;
import com.simnectz.soiplus.business.practicalplatform.sandbox.domain.entity.test2_insurance.InsuranceTransactionMasterEntity;
import com.simnectz.soiplus.business.system.mapper.BasicMapper;
import org.apache.ibatis.annotations.Mapper;

/**
 * InsuranceTransactionMaster data access layer interface
 */
@Mapper
@UseDataSource("insurance")
public interface InsuranceTransactionMasterMapper extends BasicMapper<InsuranceTransactionMasterEntity> {
}
