package com.simnectz.soiplus.business.system.utils;

import cn.hutool.core.util.ObjUtil;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @Date 2019年4月16日
 * @Description 一段对类的描述
 */

public class ExcelExportUtils {

    public static Workbook exportExcel(String tableName, List<Map<String, Object>> columns, List<Map<String, Object>> valueMapList) {
        // 创建excel对象
        Workbook wk = new SXSSFWorkbook();
        // 创建一张食物信息表
        Sheet sheet = wk.createSheet(tableName);
        // 创建标题行
        Row smallTitle = sheet.createRow(0);
        // 表字段
        for (int i = 0; i < columns.size(); i++) {
            Map<String, Object> column = columns.get(i);
            if (column.containsKey("COLUMN_NAME")) {
                String fieldName = column.get("COLUMN_NAME").toString();
                smallTitle.createCell(i).setCellValue(fieldName);
            }
        }
        // 表数据
        int count = 1;
        for (Map<String, Object> map : valueMapList) {
            Row row = sheet.createRow(count++);
            for (int j = 0; j < columns.size(); j++) {
                Map<String, Object> column = columns.get(j);
                if (column.containsKey("COLUMN_NAME")) {
                    String fieldName = column.get("COLUMN_NAME").toString();
                    row.createCell(j).setCellValue(ObjUtil.isNull(map.get(fieldName)) ? null : map.get(fieldName).toString());
                    System.out.println(row.getCell(j));
                }
            }
        }
        return wk;
    }


}
