package com.simnectz.soiplus.business.learningspace.studentexercise.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * Create student exercise request parameters
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class StuExerciseDto implements Serializable {

    private static final long serialVersionUID = 3936521635658020566L;

    @NotBlank(message = "{QUESTION_CONTENT_IS_REQUIRED}")
    private String questionContent;

    @NotBlank(message = "{COURSE_CATEGORY_ID_IS_REQUIRED}")
    private String courseCategoryId;

    @NotBlank(message = "{COURSE_CODE_IS_REQUIRED}")
    private String courseCode;

    @NotBlank(message = "{SUBJECT_CODE_IS_REQUIRED}")
    private String subjectCode;

    @NotBlank(message = "{QUESTION_STUANSWER_IS_REQUIRED}")
    private String studentAnswer;

}
