package com.simnectz.soiplus.business.practicalplatform.sandbox.mapper.test2_creditcard;

import com.mybatisflex.annotation.UseDataSource;
import com.simnectz.soiplus.business.practicalplatform.sandbox.domain.entity.test2_creditcard.CreditcardPointReferenceEntity;
import com.simnectz.soiplus.business.system.mapper.BasicMapper;
import org.apache.ibatis.annotations.Mapper;

/**
 * CreditcardPointReference data access layer interface
 */
@Mapper
@UseDataSource("creditcard")
public interface CreditcardPointReferenceMapper extends BasicMapper<CreditcardPointReferenceEntity> {
}
