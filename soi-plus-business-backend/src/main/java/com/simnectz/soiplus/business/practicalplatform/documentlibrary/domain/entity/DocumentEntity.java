package com.simnectz.soiplus.business.practicalplatform.documentlibrary.domain.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import lombok.*;

import java.io.Serializable;
import java.util.Date;

@Data
@With
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(value = "data_document")
@EqualsAndHashCode()
public class DocumentEntity implements Serializable {

    private static final long serialVersionUID = 4317514882842990427L;

    @Id(keyType = KeyType.Auto)
    private Integer id;

    private String enDocUrl;

    private String cnDocUrl;

    private String enVideoUrl;

    private String cnVideoUrl;

    private String userId;

    private String classId;

    private String docName;

    private String docNameEn;

    private String topic;

    private String courseId;

    private boolean isTimeLimit;

    private String expiryDate;

    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date createDate;

}
