package com.simnectz.soiplus.business.practicalplatform.sandbox.domain.entity.test2_deposit;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.Table;
import lombok.*;


@Data
@With
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(value = "customermaster",dataSource = "deposit")
@EqualsAndHashCode()
public class CustomerMasterEntity {

    @Id
    @Column(value = "ID")
    private String id;
    @Column(value = "CountryCode")
    private String countryCode;
    @Column(value = "ClearingCode")
    private String clearingCode;
    @Column(value = "BranchCode")
    private String branchCode;
    @Column(value = "SandBoxId")
    private String sandBoxId;
    @Column(value = "CustomerNumber")
    private String customerNumber;
    @Column(value = "MobilePhoneNumber")
    private String mobilePhoneNumber;
    @Column(value = "CustomerID")
    private String customerId;
    @Column(value = "IssueCountry")
    private String issueCountry;
    @Column(value = "DateOfBirth")
    private String dateOfBirth;
    @Column(value = "ChineseName")
    private String chineseName;
    @Column(value = "Gender")
    private String gender;
    @Column(value = "Nationality")
    private String nationality;
    @Column(value = "PermanentResidenceStatus")
    private String permanentResidenceStatus;
}
