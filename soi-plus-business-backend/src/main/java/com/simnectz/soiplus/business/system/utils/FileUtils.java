package com.simnectz.soiplus.business.system.utils;

import com.simnectz.soiplus.business.system.config.SystemProperties;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.tika.Tika;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.net.URLConnection;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Objects;

@Slf4j
public class FileUtils {

    private static final Tika TIKA_INSTANCE = new Tika();
    private static final int CONNECT_TIMEOUT = 30 * 1000;
    private static final int READ_TIMEOUT = 60 * 1000;
    private static final SystemProperties systemProperties = SpringUtils.getBean(SystemProperties.class);

    private static InputStream openConnection(String url) throws IOException {
        URL urlObj = new URL(url);
        URLConnection urlConnection = urlObj.openConnection();
        urlConnection.setConnectTimeout(CONNECT_TIMEOUT);
        urlConnection.setReadTimeout(READ_TIMEOUT);
        urlConnection.setDoInput(true);
        return urlConnection.getInputStream();
    }

    public static InputStream getFileStreamByUrl(String url) {
        try {
            byte[] fileContent = readFileBytesByUrl(url);
            return new ByteArrayInputStream(fileContent);
        } catch (Exception exception) {
            log.error("Error while retrieving file from path [{}]: {}", url, exception.getMessage(), exception);
        }
        return null;
    }

    public static byte[] readFileBytesByUrl(String url) {
        if (!url.startsWith("http")) {
            url = systemProperties.getUpload().getRequestUrl() + url;
        }
        try (InputStream in = openConnection(url)) {
            if (Objects.nonNull(in)) {
                return IOUtils.toByteArray(in);
            }
        } catch (IOException exception) {
            log.error("Error while reading file from URL [{}]: {}", url, exception.getMessage(), exception);
        }
        return new byte[0];
    }

    public static String writeBytesFile(byte[] data, String fileName) throws IOException {
        Path path = Paths.get(
                systemProperties.getUpload().getStoragePath(),
                fileName
        );
        Path parentDir = path.getParent();

        createParentDir(parentDir);

        Files.write(path, data);
        String absolutePath = path.toAbsolutePath().toString();

        log.info("File written to: {}", absolutePath);

        return absolutePath;
    }

    public static String getFileMimeType(byte[] fileBytes) {
        return getFileMimeType(fileBytes, null);
    }

    public static String getFileMimeType(byte[] fileBytes, String fileName) {
        if (StringUtils.isEmpty(fileName)) {
            return TIKA_INSTANCE.detect(fileBytes);
        }
        return TIKA_INSTANCE.detect(fileBytes, fileName);
    }

    private static void createParentDir(Path parentDir) throws IOException {
        if (Objects.nonNull(parentDir) && Files.notExists(parentDir)) {
            Files.createDirectories(parentDir);
            log.info("Created directory: {}", parentDir.toAbsolutePath());
        }
    }

    private FileUtils() {
    }

}