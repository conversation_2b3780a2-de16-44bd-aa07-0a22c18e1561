package com.simnectz.soiplus.business.practicalplatform.apimarket.domain.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import com.simnectz.soiplus.business.system.utils.DateUtils;
import lombok.*;

import java.util.Date;

@Data
@With
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
@Table(value = "api_swagger")
public class ApiSwaggerEntity {

    @Id(keyType = KeyType.Auto)
    private Integer id;

    private String apiName;

    private String apiNameEn;

    private String content;

    private String contentEn;

    private Long createTime = new Date().getTime() / 1000;

    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private String createDate = DateUtils.parseDate(new Date(), DateUtils.DATETIME_DEFAULT_FORMAT);

    private String apiId;
}
