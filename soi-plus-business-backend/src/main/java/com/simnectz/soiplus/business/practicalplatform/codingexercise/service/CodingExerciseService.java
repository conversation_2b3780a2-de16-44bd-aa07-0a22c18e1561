package com.simnectz.soiplus.business.practicalplatform.codingexercise.service;


import com.simnectz.soiplus.business.practicalplatform.codingexercise.domain.dto.CompilerCodeDto;
import com.simnectz.soiplus.business.practicalplatform.codingexercise.domain.dto.CreateCodingQuestionDto;
import com.simnectz.soiplus.business.practicalplatform.codingexercise.domain.dto.SaveAnswerInfoDto;
import com.simnectz.soiplus.business.practicalplatform.codingexercise.domain.dto.UpdateCodingQuestionDto;
import com.simnectz.soiplus.business.system.domain.vo.Response;

import java.util.List;

public interface CodingExerciseService {
    Response<?> queryCodingQuestionCategory();

    Response<?> queryCodingQuestionSubcategory(String categoryId);

    Response<?> queryCodingQuestionsTitleByCategory(String category);

    Response<?> queryCodingQuestionById(String questionId);

    Response<?> queryCodingQuestionAnswer(String userId, String questionId, String language);

    Response<?> queryCodingQuestionAnswers(String userId);

    Response<?> saveCodingQuestionAnswer(SaveAnswerInfoDto saveAnswerInfo);

    Response<?> compiler(CompilerCodeDto compiler);

    Response<?> queryCodingQuestionsByPage(String key, String category, Integer status, Integer page,
                                      Integer rows, String sortBy, Boolean desc);

    Response<?> createCodingQuestion(CreateCodingQuestionDto createCodingQuestionInfo);

    Response<?> updateCodingQuestionById(UpdateCodingQuestionDto updateCodingQuestionInfo);

    Response<?> deleteCodingQuestionByIds(List<String> ids);
}
