package com.simnectz.soiplus.business.practicalplatform.codingexercise.utils;

import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.simnectz.soiplus.business.system.config.CompilerProperties;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.IOException;
import java.util.Map;

@Data
@EqualsAndHashCode(callSuper = true)
public class JavaCompiler extends Compiler {

    private final CompilerProperties compilerProperties = SpringUtil.getBean(CompilerProperties.class);

    public final String JAVA_CODE_PATH = System.getProperty("user.dir") + compilerProperties.getJava().getPath();

    public JavaCompiler(String code, String userid) {
        this.folderName = userid;
        FileUtils.createFile(JAVA_CODE_PATH + folderName, JAVA_CODE_PATH + folderName + "/" + compilerProperties.getJava().getFileName() + ".java", code);
    }

    @Override
    public void compilerRun() throws IOException, InterruptedException {
        compileErrorMessage = ProgramUtils.compiler("javac -encoding UTF-8 -classpath " + System.getProperty("user.dir") + "/" + compilerProperties.getJava().getJarPackageName() + " " + JAVA_CODE_PATH + folderName + "/" + compilerProperties.getJava().getFileName() + ".java");
        FileUtils.removeFile(JAVA_CODE_PATH + folderName + "/" + compilerProperties.getJava().getFileName() + ".java");

        if (StrUtil.isBlank(compileErrorMessage)) {
            // Run
            Map<String, String> runResult = ProgramUtils.run("java -cp " + System.getProperty("user.dir") + "/" + compilerProperties.getJava().getJarPackageName() + ":" + JAVA_CODE_PATH + folderName + " " + compilerProperties.getJava().getFileName());
            runErrorMessage = runResult.get("runError");
            runOutputMessage = runResult.get("runOutput");
            FileUtils.removeFile(JAVA_CODE_PATH + folderName + "/" + compilerProperties.getJava().getFileName() + ".class");
        }
    }
}
