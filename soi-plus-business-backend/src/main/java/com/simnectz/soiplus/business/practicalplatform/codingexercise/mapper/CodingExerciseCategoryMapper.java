package com.simnectz.soiplus.business.practicalplatform.codingexercise.mapper;

import com.simnectz.soiplus.business.practicalplatform.codingexercise.domain.entity.CodingExerciseCategoryEntity;
import com.simnectz.soiplus.business.system.mapper.BasicMapper;
import org.apache.ibatis.annotations.Mapper;

/**
 * CodingExerciseCategory data access layer interface
 */
@Mapper
public interface CodingExerciseCategoryMapper extends BasicMapper<CodingExerciseCategoryEntity> {
}
