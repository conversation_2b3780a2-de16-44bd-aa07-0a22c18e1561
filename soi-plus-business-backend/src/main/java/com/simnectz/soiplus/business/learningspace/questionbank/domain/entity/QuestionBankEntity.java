package com.simnectz.soiplus.business.learningspace.questionbank.domain.entity;

import com.mybatisflex.annotation.Table;
import com.simnectz.soiplus.business.system.domain.entity.BasicEntity;
import lombok.*;

import java.io.Serializable;

@Data
@With
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(value = "learn_question_bank")
@EqualsAndHashCode(callSuper = true)
public class QuestionBankEntity extends BasicEntity implements Serializable {
    private static final long serialVersionUID = -731660950440834485L;

    private String courseCategoryId;
    private String courseCode;
    private String subjectCode;
    private String questionContent;
    private String questionModelanswer;
}
