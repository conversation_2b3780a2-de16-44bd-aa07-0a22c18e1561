package com.simnectz.soiplus.business.practicalplatform.documentlibrary.domain.entity;

import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import lombok.*;

import java.io.Serializable;

@Data
@With
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(value = "document_course_student")
@EqualsAndHashCode()
public class DocumentCourseStudentEntity implements Serializable {

    private static final long serialVersionUID = 4344421945327469968L;

    @Id(keyType = KeyType.Auto)
    private Integer id;

    private Integer courseId;

    private String studentId;

    private String email;

}
