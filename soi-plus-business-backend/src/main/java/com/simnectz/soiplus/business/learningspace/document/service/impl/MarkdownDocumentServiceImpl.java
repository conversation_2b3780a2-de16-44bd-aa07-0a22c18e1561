package com.simnectz.soiplus.business.learningspace.document.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.mybatisflex.core.query.If;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.core.relation.RelationManager;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import com.simnectz.soiplus.business.learningspace.document.domain.dto.CreateMarkdownDocumentDto;
import com.simnectz.soiplus.business.learningspace.document.domain.dto.UpdateMarkdownDocumentDto;
import com.simnectz.soiplus.business.learningspace.document.domain.entity.MarkdownDocumentEntity;
import com.simnectz.soiplus.business.learningspace.document.domain.vo.MarkdownDocumentVo;
import com.simnectz.soiplus.business.learningspace.document.mapper.MarkdownDocumentMapper;
import com.simnectz.soiplus.business.learningspace.document.service.MarkdownDocumentService;
import com.simnectz.soiplus.business.system.constant.ResponseConstant;
import com.simnectz.soiplus.business.system.domain.vo.Response;
import com.simnectz.soiplus.business.system.exception.SystemException;
import com.simnectz.soiplus.business.system.utils.MessageUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

import static com.simnectz.soiplus.business.learningspace.document.domain.entity.table.MarkdownDocumentTableDef.markdown_document;

@Slf4j
@Service
public class MarkdownDocumentServiceImpl extends ServiceImpl<MarkdownDocumentMapper, MarkdownDocumentEntity> implements MarkdownDocumentService {

    /**
     * 验证文档名称是否唯一
     *
     * @param documentId   文档ID
     * @param documentName 文档名称
     * @return true: 重复, false: 不重复
     */
    @Override
    @Transactional(readOnly = true)
    public Response<Boolean> validationDocumentNameUnique(String documentId, String documentName) {
        if (StrUtil.isBlank(documentName)) {
            throw new SystemException(
                    ResponseConstant.Code.BAD_REQUEST,
                    MessageUtils.message("DOCUMENT_NAME_REQUIRED")
            );
        }

        Boolean exists = this.exists(this.queryChain()
                .select()
                .from(markdown_document)
                .where(markdown_document.id.ne(documentId, If::notNull))
                .and(markdown_document.name.eq(documentName))
        );

        return Response.success(exists);
    }

    @Override
    @Transactional
    public Response<?> createDocument(CreateMarkdownDocumentDto createMarkdownDocumentDto) {
        if (createMarkdownDocumentDto == null || StrUtil.isBlank(createMarkdownDocumentDto.getName())) {
            throw new SystemException(
                    ResponseConstant.Code.BAD_REQUEST,
                    MessageUtils.message("DOCUMENT_NAME_REQUIRED")
            );
        }

        // 验证文档名称是否唯一
//        Response<Boolean> isDocumentNameUniqueResult = this.validationDocumentNameUnique(null, createMarkdownDocumentDto.getName());
//        if (isDocumentNameUniqueResult.getData()) {
//            throw new SystemException(
//                    ResponseConstant.Code.BAD_REQUEST,
//                    MessageUtils.message("DOCUMENT_NAME_ALREADY_EXISTS")
//            );
//        }

        MarkdownDocumentEntity markdownDocumentEntity = new MarkdownDocumentEntity();
        BeanUtil.copyProperties(createMarkdownDocumentDto, markdownDocumentEntity);
        this.save(markdownDocumentEntity);
        return Response.success();
    }

    @Override
    @Transactional
    public Response<?> deleteDocument(String id) {
        if (StrUtil.isBlank(id)) {
            throw new SystemException(
                    ResponseConstant.Code.BAD_REQUEST,
                    MessageUtils.message("DOCUMENT_ID_REQUIRED")
            );
        }

        // 检查是否存在子文档
        List<MarkdownDocumentEntity> children = this.list(QueryWrapper.create()
                .where(markdown_document.parent_id.eq(id)));

        if (CollUtil.isNotEmpty(children)) {
            throw new SystemException(
                    ResponseConstant.Code.BAD_REQUEST,
                    MessageUtils.message("DOCUMENT_HAS_CHILDREN")
            );
        }

        this.removeById(id);
        return Response.success();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Response<?> updateDocument(UpdateMarkdownDocumentDto updateMarkdownDocumentDto) {
        if (updateMarkdownDocumentDto == null || StrUtil.isBlank(updateMarkdownDocumentDto.getId())) {
            throw new SystemException(
                    ResponseConstant.Code.BAD_REQUEST,
                    MessageUtils.message("DOCUMENT_ID_REQUIRED")
            );
        }

        if (StrUtil.isBlank(updateMarkdownDocumentDto.getName())) {
            throw new SystemException(
                    ResponseConstant.Code.BAD_REQUEST,
                    MessageUtils.message("DOCUMENT_NAME_REQUIRED")
            );
        }

        MarkdownDocumentEntity markdownDocumentEntity = this.getById(updateMarkdownDocumentDto.getId());
        if (markdownDocumentEntity == null) {
            throw new SystemException(
                    ResponseConstant.Code.NOT_FOUND,
                    MessageUtils.message("DOCUMENT_NOT_FOUND")
            );
        }

        // 验证文档名称是否唯一
//        Response<Boolean> isDocumentNameUniqueResult = this.validationDocumentNameUnique(
//                updateMarkdownDocumentDto.getId(), updateMarkdownDocumentDto.getName());
//        if (isDocumentNameUniqueResult.getData()) {
//            throw new SystemException(
//                    ResponseConstant.Code.BAD_REQUEST,
//                    MessageUtils.message("DOCUMENT_NAME_ALREADY_EXISTS")
//            );
//        }

        BeanUtil.copyProperties(updateMarkdownDocumentDto, markdownDocumentEntity);
        this.updateById(markdownDocumentEntity);
        return Response.success();
    }

    @Override
    public Response<?> getDocumentChildrenTree(String parentId) {
        RelationManager.setMaxDepth(10);
        List<MarkdownDocumentVo> markdownDocumentVos = mapper.selectListWithRelationsByQueryAs(
                queryChain()
                        .select(markdown_document.all_columns)
                        .from(markdown_document)
                        .where(markdown_document.id.eq(parentId))
                        .orderBy(markdown_document.sort_number, true),
                MarkdownDocumentVo.class
        );

        return Response.success(markdownDocumentVos);
    }

    @Override
    public Response<?> getDocumentContent(String id) {
        if (StrUtil.isBlank(id)) {
            throw new SystemException(
                    ResponseConstant.Code.BAD_REQUEST,
                    MessageUtils.message("DOCUMENT_ID_REQUIRED")
            );
        }

        MarkdownDocumentEntity markdownDocumentEntity = this.getById(id);
        if (markdownDocumentEntity == null) {
            throw new SystemException(
                    ResponseConstant.Code.NOT_FOUND,
                    MessageUtils.message("DOCUMENT_NOT_FOUND")
            );
        }

        MarkdownDocumentVo markdownDocumentVo = new MarkdownDocumentVo();
        BeanUtil.copyProperties(markdownDocumentEntity, markdownDocumentVo);
        return Response.success(markdownDocumentVo);
    }

    @Override
    public Response<?> getDocumentChildren(String parentId) {
        if (StrUtil.isBlank(parentId)) {
            throw new SystemException(
                    ResponseConstant.Code.BAD_REQUEST,
                    MessageUtils.message("PARENT_DOCUMENT_ID_REQUIRED")
            );
        }

        List<MarkdownDocumentVo> documentEntities = this.listAs(
                QueryWrapper.create()
                        .where(markdown_document.parent_id.eq(parentId))
                        .and(markdown_document.status.eq(1))
                        .orderBy(markdown_document.sort_number, true),
                MarkdownDocumentVo.class
        );

        return Response.success(documentEntities);
    }

}
