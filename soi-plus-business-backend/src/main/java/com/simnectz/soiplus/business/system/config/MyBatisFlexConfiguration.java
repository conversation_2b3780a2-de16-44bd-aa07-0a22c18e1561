package com.simnectz.soiplus.business.system.config;

import cn.hutool.core.date.DateUtil;
import com.mybatisflex.annotation.InsertListener;
import com.mybatisflex.annotation.UpdateListener;
import com.mybatisflex.core.FlexGlobalConfig;
import com.mybatisflex.core.audit.AuditManager;
import com.mybatisflex.spring.boot.MyBatisFlexCustomizer;
import com.simnectz.soiplus.business.system.domain.entity.BasicEntity;
import com.simnectz.soiplus.business.system.utils.SecurityUtils;
import org.springframework.context.annotation.Configuration;

@Configuration
public class MyBatisFlexConfiguration
        implements MyBatisFlexCustomizer, InsertListener, UpdateListener {

    @Override
    public void customize(FlexGlobalConfig globalConfig) {
        // SQL Audit
        AuditManager.setAuditEnable(false);

        globalConfig.registerInsertListener(
                this,
                BasicEntity.class
        );

        globalConfig.registerUpdateListener(
                this,
                BasicEntity.class
        );
    }

    @Override
    public void onInsert(Object entity) {
        BasicEntity basicEntity = (BasicEntity) entity;

        String current = DateUtil.now();

        basicEntity.setCreator(SecurityUtils.getUserId());
        basicEntity.setCreateTime(current);
        basicEntity.setLastUpdateCreator(SecurityUtils.getUserId());
        basicEntity.setLastUpdateTime(current);
    }

    @Override
    public void onUpdate(Object entity) {
        BasicEntity basicEntity = (BasicEntity) entity;

        String current = DateUtil.now();

        basicEntity.setLastUpdateCreator(SecurityUtils.getUserId());
        basicEntity.setLastUpdateTime(current);
    }

}
