package com.simnectz.soiplus.business.practicalplatform.sandbox.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class DelApiTokenDto implements Serializable {

    private static final long serialVersionUID = 2928493621412994764L;

    private String id;

    private String userId;
}
