package com.simnectz.soiplus.business.system.utils;

import com.simnectz.soiplus.business.practicalplatform.documentlibrary.domain.entity.DocumentFileUploadEntity;
import com.simnectz.soiplus.business.practicalplatform.documentlibrary.mapper.DocumentFileUploadMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.FileCopyUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.nio.file.Files;
import java.util.Date;

@Component
@RequiredArgsConstructor
public class OssOperationUtils {

    @Value("${oss.accessKeyId}")
    private String accessKeyId;

    @Value("${oss.accessKeySecret}")
    private String accessKeySecret;

    @Value("${oss.endpoint}")
    private String endpoint;

    @Value("${oss.bucketName}")
    private String bucketName;

    @Value("${oss.objectName}")
    private String objectName;

    @Value("${system.upload.storage-path}")
    private String filePath;

    @Value("${system.upload.storage-path}")
    private String pdfPath;

    @Value("${system.upload.request-url}")
    private String nginxEndPoint;

    private final DocumentFileUploadMapper documentFileUploadMapper;

    private String ossEndpoint() {
        return "https://" + bucketName + "." + endpoint + "/";
    }

    public String uploadMultiFileToNginx(String key, MultipartFile file, DocumentFileUploadEntity fileModel) {
        File dest = new File(pdfPath + key);
        if (!dest.getParentFile().exists()) { //判断文件父目录是否存在
            dest.getParentFile().mkdirs();
        }
        try {
//                file.transferTo(dest); //保存文件
            FileCopyUtils.copy(file.getInputStream(), Files.newOutputStream(dest.toPath()));
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
        documentFileUploadMapper.insertOrUpdate(fileModel);
        return fileModel.getPlayUrl();
    }

    public String docToPdfAndUpload(MultipartFile file, String userId, DocumentFileUploadEntity fileModel){
        String fileOriginalName = file.getOriginalFilename();
        String extend = fileOriginalName.substring(fileOriginalName.lastIndexOf(".") + 1);
        File filePaths = new File(filePath);
        long time = new Date().getTime();
        File pdfPaths = new File(pdfPath + userId + File.separator + time + ".pdf");
        //        String relativeFilePath = filedir + File.separator + filename;
        //相对路径
        //文件保存在绝对路径
        File dest = new File(filePaths.getAbsolutePath() + File.separator + System.currentTimeMillis() + "." + extend);
        try {
            if (!dest.getParentFile().exists()) { //判断文件父目录是否存在
                dest.getParentFile().mkdirs();
            }
            if (!pdfPaths.getParentFile().exists()) { //判断文件父目录是否存在
                pdfPaths.getParentFile().mkdirs();
            }
            try {
//                file.transferTo(dest); //保存文件
                FileCopyUtils.copy(file.getInputStream(), Files.newOutputStream(dest.toPath()));
            } catch (Exception e) {
                e.printStackTrace();
                return null;
            }
            if ("doc".equalsIgnoreCase(extend) || "docx".equalsIgnoreCase(extend) || "txt".equalsIgnoreCase(extend) || "csv".equalsIgnoreCase(extend)) {
                AsposeUtils.trans(dest.getAbsolutePath(), pdfPaths.getAbsolutePath(), "word");
            }
            if ("ppt".equalsIgnoreCase(extend) || "pptx".equalsIgnoreCase(extend)) {
                AsposeUtils.trans(dest.getAbsolutePath(), pdfPaths.getAbsolutePath(), "ppt");
            }
            if ("xlsx".equalsIgnoreCase(extend) || "xls".equalsIgnoreCase(extend)) {
                AsposeUtils.trans(dest.getAbsolutePath(), pdfPaths.getAbsolutePath(), "excel");
            }
            String key = userId + "/" + time + ".pdf";
            String pdfUrl = nginxEndPoint + key;
            fileModel.setOssKey(key);
            fileModel.setPlayUrl(pdfUrl);
            documentFileUploadMapper.insertOrUpdate(fileModel);
            return pdfUrl;
        }catch (Exception e) {
            e.printStackTrace();
            return null;
        }finally {
            if (dest.exists()){
                if(!dest.delete()) {
                    System.out.println(dest + "删除失败");
                }
            }
        }
    }

}
