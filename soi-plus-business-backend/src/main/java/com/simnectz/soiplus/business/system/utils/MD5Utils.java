package com.simnectz.soiplus.business.system.utils;

import java.io.*;
import java.util.List;
import java.util.UUID;

public class MD5Utils {

    private MD5Utils() {
    }

    /**
     * 建议使用MD5Util。md5Hex() md5加密
     *
     * @param data 要加密的字符串
     * @return 摘要
     */
    public static String md5(final String data) {
        return new String(org.apache.commons.codec.digest.DigestUtils.md5(data));
    }

    /**
     * 建议使用MD5Util。md5Hex() md5加密
     *
     * @param file 要加密的文件
     * @return 摘要
     */
    public static String md5(final File file) throws Exception {
        return new String(org.apache.commons.codec.digest.DigestUtils
                .md5(org.apache.commons.io.FileUtils.readFileToByteArray(file)));
    }

    /**
     * md5加密
     *
     * @param data 要加密的字符串
     * @return 摘要
     */
    public static String md5Hex(final String data) {
        return org.apache.commons.codec.digest.DigestUtils.md5Hex(data);
    }


    /**
     * md5加密
     *
     * @param bytes 要加密的字符串
     * @return 摘要
     */
    public static String md5Hex(final byte[] bytes) {
        return org.apache.commons.codec.digest.DigestUtils.md5Hex(bytes);
    }


    /**
     * md5加密
     *
     * @param file 要加密的文件
     * @return 摘要
     */
    public static String md5Hex(final File file) throws Exception {
        return org.apache.commons.codec.digest.DigestUtils
                .md5Hex(org.apache.commons.io.FileUtils.readFileToByteArray(file));
    }


    /**
     * 生成md5随机字符串，可以作为outh2中的authorizationCode,token,refreshToken等场景使用(如果是服务端保存状态的话)，如果是客户端保存状态，建议使用jwt
     *
     * @return
     * @throws Exception
     */
    public static String generateValue() {
        return md5Hex(UUID.randomUUID().toString());
    }

    /**
     * 深复制
     * @param src
     * @param <T>
     * @return
     * @throws IOException
     * @throws ClassNotFoundException
     */
    public static <T> List<T> deepCopy(List<T> src) throws IOException, ClassNotFoundException {
        ByteArrayOutputStream byteOut = new ByteArrayOutputStream();
        ObjectOutputStream out = new ObjectOutputStream(byteOut);
        out.writeObject(src);

        ByteArrayInputStream byteIn = new ByteArrayInputStream(byteOut.toByteArray());
        ObjectInputStream in = new ObjectInputStream(byteIn);
        @SuppressWarnings("unchecked")
        List<T> dest = (List<T>) in.readObject();
        return dest;
    }
}
