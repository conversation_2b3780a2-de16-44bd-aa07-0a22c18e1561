package com.simnectz.soiplus.business.practicalplatform.sandbox.domain.entity.test2_loan;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.Table;
import lombok.*;

@Data
@With
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(value = "loanaccountmaster",dataSource = "loan")
public class LoanAccountMasterEntity {

    @Id
    @Column(value = "ID")
    private String id;
    @Column(value = "CountryCode")
    private String countryCode;
    @Column(value = "ClearingCode")
    private String clearingCode;
    @Column(value = "BranchCode")
    private String branchCode;
    @Column(value = "SandBoxId")
    private String sandBoxId;
    @Column(value = "CustomerNumber")
    private String customerNumber;
    @Column(value = "AccountNumber")
    private String accountNumber;
    @Column(value = "CurrencyCode")
    private String currencyCode;
    @Column(value = "RelAccountNumber")
    private String relAccountNumber;
    @Column(value = "AccountStatus")
    private String accountStatus;
    @Column(value = "ReportCancelDate")
    private String reportCancelDate;
    @Column(value = "CreateDate")
    private String createDate;
    @Column(value = "LastUpdatedDate")
    private String lastUpdatedDate;


}
