package com.simnectz.soiplus.business.system.domain.dto;

import com.simnectz.soiplus.business.system.constant.SystemConstant;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.util.Date;

/**
 * Paginate enquiry entity
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class PaginateEnquiry {

    private Integer currentPage = SystemConstant.DEFAULT_CURRENT_PAGE;
    private Integer pageSize = SystemConstant.DEFAULT_PAGE_SIZE;
    private String orderBy = "lastUpdateTime";
    private Boolean isAsc = Boolean.FALSE;
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date startTime;
    @DateTimeFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date endTime;

}
