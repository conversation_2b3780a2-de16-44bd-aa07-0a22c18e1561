package com.simnectz.soiplus.business.learningspace.course.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import java.io.Serializable;

/**
 * create program Definition request parameters
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class CreateCourseDto implements Serializable {

    private static final long serialVersionUID = 3285852756315386201L;

    @NotBlank(message = "{COURSE_CATEGORY_ID_IS_REQUIRED}")
    private String courseCategoryId;

    @NotBlank(message = "{COURSE_NAME_IS_REQUIRED}")
    private String courseName;

    @NotBlank(message = "{COURSE_CODE_IS_REQUIRED}")
    private String courseCode;

    @NotBlank(message = "{COURSE_DESCRIPTION_IS_REQUIRED}")
    private String courseDescription;

    @NotBlank(message = "{COURSE_PICTURE_IS_REQUIRED}")
    private String coursePicture;

}
