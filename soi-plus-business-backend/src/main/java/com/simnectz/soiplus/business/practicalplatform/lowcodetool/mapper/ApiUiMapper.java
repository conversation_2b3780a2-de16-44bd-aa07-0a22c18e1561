package com.simnectz.soiplus.business.practicalplatform.lowcodetool.mapper;

import com.simnectz.soiplus.business.practicalplatform.lowcodetool.domain.entity.ApiUiEntity;
import com.simnectz.soiplus.business.system.mapper.BasicMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 *  ApiUi data access layer interface
 */
@Mapper
public interface ApiUiMapper extends BasicMapper<ApiUiEntity> {
    @Select(" SELECT IA.UiName AS NAME\n" +
            "        FROM (\n" +
            "        SELECT IF ( ui_class_id = '10000', 'Student', NULL ) AS UiName\n" +
            "        FROM low_code_api_ui\n" +
            "        ) IA\n" +
            "        WHERE IA.UiName IS NOT NULL\n" +
            "        UNION\n" +
            "        SELECT fi_name AS NAME\n" +
            "        FROM low_code_api_ui\n" +
            "        WHERE ui_class_id != '10000'\n" +
            "        GROUP BY fi_name;")
    List<String> getUiIndustry();
}
