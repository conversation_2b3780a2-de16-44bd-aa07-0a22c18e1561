package com.simnectz.soiplus.business.practicalplatform.datamanagement.domain.entity;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import com.mybatisflex.core.keygen.KeyGenerators;
import lombok.*;

import java.io.Serializable;

@Data
@With
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
@Table(value = "data_fi")
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class FiEntity implements Serializable {

    private static final long serialVersionUID = -7299574703328301071L;

    @Id(keyType = KeyType.Generator, value = KeyGenerators.uuid)
    private String id;

    private String finame;

    private String company;

    private String userEmail;
}
