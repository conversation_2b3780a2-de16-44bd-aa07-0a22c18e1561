package com.simnectz.soiplus.business.system.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum ContentType {

    JPG("JPG", "image/jpeg"),
    PNG("PNG", "image/png"),
    WEBP("WEBP", "image/webp"),
    SVG("SVG", "image/svg+xml"),
    WMF("WMF", "image/wmf"),
    EMF("EMF", "image/emf"),
    PICT("PICT", "image/x-pict"),
    DIB("DIB", "image/dib"),

    DOC("DOC", "application/msword"),
    DOCX("DOCX", "application/vnd.openxmlformats-officedocument.wordprocessingml.document"),
    XLS("XLS", "application/vnd.ms-excel"),
    XLSX("XLSX", "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"),
    PPT("PPT", "application/vnd.ms-powerpoint"),
    PPTX("PPTX", "application/vnd.openxmlformats-officedocument.presentationml.presentation"),
    PDF("PDF", "application/pdf"),

    TXT("TXT", "text/plain"),
    MD("MD", "text/x-web-markdown"),
    JSON("JSON", "application/json"),

    MP4("MP4", "video/mp4"),

    MP3("MP3", "audio/mpeg"),

    ZIP("ZIP", "application/zip"),
    ;

    private final String label;
    private final String value;

}