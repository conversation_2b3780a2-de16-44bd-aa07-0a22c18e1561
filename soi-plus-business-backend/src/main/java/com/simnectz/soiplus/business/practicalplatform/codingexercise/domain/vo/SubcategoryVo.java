package com.simnectz.soiplus.business.practicalplatform.codingexercise.domain.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class SubcategoryVo implements Serializable {

    private static final long serialVersionUID = 1555263826829273349L;

    private String name;
    private String nameEn;

}
