package com.simnectz.soiplus.business.system.domain.bo;

import com.simnectz.soiplus.business.system.annotation.ExcelColumn;
import com.simnectz.soiplus.business.system.enums.Direction;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;

import java.lang.reflect.Field;
import java.util.Collection;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ExcelFieldNodeBo {

    private Field field;
    private ExcelColumn excelColumn;
    private int column;
    private List<ExcelFieldNodeBo> children;

    public boolean isSampleCollection() {
        return isCollection() && CollectionUtils.isEmpty(this.children);
    }

    public boolean isComplexCollection() {
        return isCollection() && CollectionUtils.isNotEmpty(this.children);
    }

    public boolean isCollection() {
        return Collection.class.isAssignableFrom(this.field.getType());
    }

    public int countChildrenNodes() {
        return countChildrenNodes(this);
    }

    private int countChildrenNodes(ExcelFieldNodeBo fieldNode) {
        int count = 1;
        ExcelColumn nodeExcelColumn = fieldNode.getExcelColumn();
        List<ExcelFieldNodeBo> childrenList = fieldNode.getChildren();
        if (CollectionUtils.isNotEmpty(childrenList)) {
            count -= 1;
            for (ExcelFieldNodeBo child : childrenList) {
                count += countChildrenNodes(child);
            }
        }

        if (fieldNode.isSampleCollection()
                && nodeExcelColumn.splitSampleCollections()
                && nodeExcelColumn.sampleCollectionDirection() == Direction.HORIZONTAL) {
            count += nodeExcelColumn.sampleCollectionSize() - 1;
        }
        return count;
    }

}