package com.simnectz.soiplus.business.practicalplatform.documentlibrary.resource;

import com.simnectz.soiplus.business.practicalplatform.documentlibrary.domain.entity.DocumentEntity;
import com.simnectz.soiplus.business.practicalplatform.documentlibrary.service.DocumentLibraryService;
import com.simnectz.soiplus.business.system.domain.vo.Response;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

/**
 * 文档管理
 */
@RestController
@RequestMapping("v1/practical/document")
@RequiredArgsConstructor
public class DocumentLibraryResource {

    private final DocumentLibraryService documentLibraryService;

    /**
     * 查询文档列表
     * @param classId
     * @param userId
     * @return
     */
    @RequestMapping("/list")
    public ResponseEntity<Response<?>> getDocumentList(Integer classId, String userId){
        return ResponseEntity.ok(documentLibraryService.getDocumentList(classId, userId));
    }

    /**
     * 上传文件
     * @param file
     * @param userID
     * @return
     */
    @RequestMapping("/upload")
    public ResponseEntity<Response<?>> uploadSubFiles(@RequestParam(value = "file", required = false) MultipartFile file, String userID) {
        return ResponseEntity.ok(documentLibraryService.uploadSubFiles(file, userID));
    }

    /**
     * 新增文档
     * @param documentModelDTO
     * @return
     */
    @PostMapping("/create")
    public ResponseEntity<Response<?>> createDocument(DocumentEntity documentModelDTO){
        return ResponseEntity.ok(documentLibraryService.createDocument(documentModelDTO));
    }

    /**
     * 删除文档
     * @param json
     * @return
     */
    @PostMapping("/delrecord")
    public ResponseEntity<Response<?>> delDocument(@RequestBody String json){
        return ResponseEntity.ok(documentLibraryService.delDocument(json));
    }

    /**
     * 上传文件后删除文档
     * @param json
     * @return
     */
    @PostMapping("/del")
    public ResponseEntity<Response<?>> deleteOssFile(@RequestBody String json){
        return ResponseEntity.ok(documentLibraryService.deleteOssFile(json));
    }
}
