package com.simnectz.soiplus.business.practicalplatform.sandbox.domain.entity;

import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import com.mybatisflex.core.keygen.KeyGenerators;
import lombok.*;

import java.io.Serializable;
import java.util.Date;

@Data
@With
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(value = "sandbox_task_queue")
@EqualsAndHashCode()
public class TaskQueueEntity implements Serializable {

    private static final long serialVersionUID = -7626692848266586252L;

    @Id(keyType = KeyType.Generator, value = KeyGenerators.uuid)
    private String id;

    private String taskId;

    private String taskStatus;

    private String taskLog;

    private String userId;

    private Date createDate;
}
