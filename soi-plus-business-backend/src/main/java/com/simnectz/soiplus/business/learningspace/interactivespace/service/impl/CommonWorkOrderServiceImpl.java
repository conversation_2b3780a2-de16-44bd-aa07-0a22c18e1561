package com.simnectz.soiplus.business.learningspace.interactivespace.service.impl;

import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.mybatisflex.core.query.If;
import com.mybatisflex.core.query.QueryWrapper;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import com.simnectz.authorize.system.domain.bo.User;
import com.simnectz.soiplus.business.learningspace.interactivespace.domain.enitity.CommonWorkOrderEntity;
import com.simnectz.soiplus.business.learningspace.interactivespace.domain.enitity.CommonWorkOrderMessageEntity;
import com.simnectz.soiplus.business.learningspace.interactivespace.mapper.CommonWorkOrderMapper;
import com.simnectz.soiplus.business.learningspace.interactivespace.mapper.CommonWorkOrderMessageMapper;
import com.simnectz.soiplus.business.learningspace.interactivespace.service.CommonWorkOrderService;
import com.simnectz.soiplus.business.learningspace.users.domain.entity.UserEntity;
import com.simnectz.soiplus.business.learningspace.users.service.UserService;
import com.simnectz.soiplus.business.system.constant.OrderStatusConstant;
import com.simnectz.soiplus.business.system.constant.OrderTypeConstant;
import com.simnectz.soiplus.business.system.constant.ResponseConstant;
import com.simnectz.soiplus.business.system.constant.SystemConstant;
import com.simnectz.soiplus.business.system.domain.vo.Response;
import com.simnectz.soiplus.business.system.exception.SystemException;
import com.simnectz.soiplus.business.system.utils.DateUtils;
import com.simnectz.soiplus.business.system.utils.MessageUtils;
import com.simnectz.soiplus.business.system.utils.SecurityUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Set;

import static com.simnectz.soiplus.business.learningspace.course.domain.entity.table.CourseTableDef.course;
import static com.simnectz.soiplus.business.learningspace.interactivespace.domain.enitity.table.CommonWorkOrderMessageTableDef.common_work_order_message;
import static com.simnectz.soiplus.business.learningspace.interactivespace.domain.enitity.table.CommonWorkOrderTableDef.common_work_order;
import static com.simnectz.soiplus.business.learningspace.subjectdefinition.domain.entity.table.SubjectDefinitionTableDef.subject_definition;
import static com.simnectz.soiplus.business.learningspace.users.domain.entity.table.UserTableDef.user;

/**
 * CommonWorkOrder service implements
 */
@Service
@RequiredArgsConstructor
public class CommonWorkOrderServiceImpl extends ServiceImpl<CommonWorkOrderMapper, CommonWorkOrderEntity>
        implements CommonWorkOrderService {

    private final UserService userService;
    private final CommonWorkOrderMessageMapper commonWorkOrderMessageMapper;

    public static final Integer MAX_SIZE = 500 * 1024;

    @Override
    public Response<?> creatCommonWorkOrder(String developerId, String company, String workOrderType, String courseId, String subjectId, String content, MultipartFile file) {
        User loginUser = SecurityUtils.getLoginUser();
        UserEntity user = userService.getById(loginUser.getId());
        if (ObjUtil.isNull(user)) {
            //TODO 原来返回code为1，现在改为40400
            return Response.failed(ResponseConstant.Code.NOT_FOUND, MessageUtils.message(ResponseConstant.Message.USER_NOT_FOUND));
        }

        CommonWorkOrderEntity commonWorkOrder = new CommonWorkOrderEntity();
        commonWorkOrder.setDevelopId(developerId);
        commonWorkOrder.setCompany(company);
        commonWorkOrder.setWorkOrderType(workOrderType);
        commonWorkOrder.setCourseId(courseId);
        commonWorkOrder.setSubjectId(subjectId);
        commonWorkOrder.setContent(content);
        commonWorkOrder.setStatus("Ready");
        commonWorkOrder.setApplicationDate(new Date());
        commonWorkOrder.setUsername(loginUser.getUsername());
        commonWorkOrder.setEmail(loginUser.getEmail());
        String extensionName = "";
        if (file != null) {
            try {
                if (file.getBytes().length > MAX_SIZE) {
                    return Response.failed(-1, MessageUtils.message(ResponseConstant.Message.INVALID_UPLOAD_FILE_SIZE));
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
            String filename = file.getOriginalFilename();
            if ((filename != null) && (filename.length() > 0)) {
                int dot = filename.lastIndexOf('.');
                if ((dot > -1) && (dot < (filename.length() - 1))) {
                    extensionName = filename.substring(dot + 1);

                }
            }
        }
        if (StrUtil.isNotBlank(extensionName)) {
            commonWorkOrder.setFileExtensionName(extensionName);
        }
        try {
            if (file != null) commonWorkOrder.setFile(file.getBytes());
        } catch (IOException e) {
            e.printStackTrace();
        }

        this.saveOrUpdate(commonWorkOrder);
        return Response.success(commonWorkOrder);
    }

    @Override
    public Response<?> saveSuOrderMessage(String originWorkSheetId, String type, String messageContent, MultipartFile file, String isOperator) {
        if (StrUtil.isBlank(originWorkSheetId)) {
            //父级订单号不能为空 -500代表参数传递有误
            return Response.failed(-500, "order id cant be empty!");
        }
        if (StrUtil.isBlank(type)) {
            //父级订单类型不能为空 -500代表参数传递有误
            return Response.failed(-500, "type cant be empty!");
        }
        String extensionName = "";
        if (file != null) {
            try {
                if (file.getBytes().length>MAX_SIZE){
                    return Response.failed(-500, MessageUtils.message(ResponseConstant.Message.INVALID_UPLOAD_FILE_SIZE));
                }
            } catch (IOException e) {
                e.printStackTrace();
                return Response.failed(-500, "Upload file error");
            }
            String filename = file.getOriginalFilename();
            if ((filename != null) && (filename.length() > 0)) {
                int dot = filename.lastIndexOf('.');
                if ((dot > -1) && (dot < (filename.length() - 1))) {
                    extensionName = filename.substring(dot + 1);

                }
            }
        }
        String senderEmail = SecurityUtils.getLoginUser().getEmail();
        if (OrderTypeConstant.CREAT_COMMON_WORK_SHEET.equals(type)) {
            CommonWorkOrderEntity commonWorkOrder = this.getById(originWorkSheetId);
            if (OrderStatusConstant.Close.equals(commonWorkOrder.getStatus())) {
                return Response.failed(-400, MessageUtils.message(ResponseConstant.Message.INVALID_ORDER_STATUS));
            }
            if (StrUtil.isBlank(commonWorkOrder.getId())) {
                return Response.failed(0, "order does not exist");
            }

            CommonWorkOrderMessageEntity commonWorkOrderMessage = new CommonWorkOrderMessageEntity();
            commonWorkOrderMessage.setSendTime(new Date());
            commonWorkOrderMessage.setDeleteState(1);
            commonWorkOrderMessage.setOrderId(originWorkSheetId);
            commonWorkOrderMessage.setContent(messageContent);
            commonWorkOrderMessage.setSenderEmail(senderEmail);
            commonWorkOrderMessage.setIsOperator("NO");
            if ("YES".equals(isOperator)) {
                commonWorkOrderMessage.setSenderEmail("<EMAIL>");
                commonWorkOrderMessage.setIsOperator("YES");
            }
            if (StrUtil.isNotBlank(extensionName)) {
                commonWorkOrderMessage.setFileExtensionName(extensionName);
                try {
                    if (file != null) {
                        commonWorkOrderMessage.setFileURL(file.getBytes());
                    }
                } catch (IOException e) {
                    System.out.println(e.getCause());
                }
            }
            return Response.success(commonWorkOrderMessage);
        }
        return Response.failed(0, "submit error!");
    }

    @Override
    public Response<?> searchCommonWorkOrder(String company, String status, String applicationDate,
                                             String sortByApplicationDate, String content) {

        UserEntity user = userService.getById(SecurityUtils.getUserId());
        if (ObjUtil.isNull(user)) {
            //TODO 原来返回code为1，现在改为40400
            return Response.failed(ResponseConstant.Code.NOT_FOUND, MessageUtils.message(ResponseConstant.Message.USER_NOT_FOUND));
        }

        Date startDate = null;
        if (StrUtil.isNotBlank(applicationDate)) {
            applicationDate = applicationDate + " 00:00:00";
            try {
                startDate = DateUtils.formatDateTime(applicationDate);
            } catch (Exception e) {
                return Response.failed(-500, "Date format error,Example:2019-06-06");
            }
        }
        Date endDate = DateUtils.addDate(startDate, 1);

        List<CommonWorkOrderEntity> commonWorkOrderList;
        if ("ASC".equals(sortByApplicationDate)) {
            commonWorkOrderList = this.getMapper().selectListWithRelationsByQuery((
                    this.queryChain().select(common_work_order.all_columns)
                            .from(common_work_order)
                            .innerJoin(course)
                            .on(common_work_order.course_id.eq(course.id))
                            .innerJoin(subject_definition)
                            .on(common_work_order.subject_id.eq(subject_definition.id))
                            .where(common_work_order.status.eq(status, If::hasText))
                            .and(common_work_order.company.like(company, If::hasText))
                            .and(subject_definition.subject_name.like(content, If::hasText)
                                    .or(course.course_name.like(content, If::hasText))
                                    .or(common_work_order.content.like(content, If::hasText)))
                            .and(common_work_order.application_date.ge(startDate, If::notNull))
                            .and(common_work_order.application_date.lt(endDate, If::notNull))
                            .orderBy(common_work_order.application_date, Boolean.TRUE)

            ));
        } else {
            commonWorkOrderList = this.getMapper().selectListByQuery(
                    this.queryChain().select(common_work_order.all_columns)
                            .from(common_work_order)
                            .innerJoin(course)
                            .on(common_work_order.course_id.eq(course.id))
                            .innerJoin(subject_definition)
                            .on(common_work_order.subject_id.eq(subject_definition.id))
                            .where(common_work_order.status.eq(status, If::hasText))
                            .and(common_work_order.company.like(company, If::hasText))
                            .and(subject_definition.subject_name.like(content, If::hasText)
                                    .or(course.course_name.like(content, If::hasText))
                                    .or(common_work_order.content.like(content, If::hasText)))
                            .and(common_work_order.application_date.ge(startDate, If::notNull))
                            .and(common_work_order.application_date.lt(endDate, If::notNull))
                            .orderBy(common_work_order.application_date, Boolean.FALSE)

            );
        }
        return Response.success(commonWorkOrderList);
    }

    @Override
    public Response<?> getWorkSheetDetail(String type, String id) {
        if (StrUtil.isBlank(type)) {
            //-500代表参数异常
            return Response.failed(-500, "type can not be empty");
        }
        if (StrUtil.isBlank(id)) {
            //-500代表参数异常
            return Response.failed(-500, "id can not be empty");
        }

        if (OrderTypeConstant.CREAT_COMMON_WORK_SHEET.equals(type)) {
            CommonWorkOrderEntity commonWorkOrder = this.getById(id);
            if (commonWorkOrder == null) {
                //TODO 原来返回code为0，现在改为40400
                return Response.failed(ResponseConstant.Code.NOT_FOUND, "can not find order");
            }
            List<CommonWorkOrderMessageEntity> commonWorkOrderMessageList = commonWorkOrderMessageMapper.selectListByCondition(
                    common_work_order_message.order_id.eq(id)
                    .and(common_work_order_message.delete_state.eq(1)));
            for (CommonWorkOrderMessageEntity commonWorkOrderMessage : commonWorkOrderMessageList) {
                if (!"YES".equals(commonWorkOrderMessage.getIsOperator())) {
                    commonWorkOrderMessage.setSenderEmail(formatUserEmail(commonWorkOrderMessage.getSenderEmail()));
                }
            }
            commonWorkOrder.setSubWorkSheet(commonWorkOrderMessageList);

            return Response.success(commonWorkOrder);

        }
        //TODO 原来返回code为0，现在改为40400
        return Response.failed(ResponseConstant.Code.NOT_FOUND, "can not find order");
    }

    @Override
    public Response<?> editWorkSheet(Map<String, Object> map) {
        String id = (String) map.get("id");
        String status = (String) map.get("status");
        String comment = (String) map.get("comment");
        String type = (String) map.get("type");
        if (StrUtil.isBlank(id)) {
            return Response.failed(-500, "id cant be empty");
        }
        if (StrUtil.isBlank(status)) {
            return Response.failed(-500, "status cant be empty");
        }
        if (StrUtil.isBlank(type)) {
            return Response.failed(-500, "type cant be empty");
        }
        if (OrderTypeConstant.CREAT_COMMON_WORK_SHEET.equals(type)) {
            CommonWorkOrderEntity commonWorkOrder = this.getById(id);
            commonWorkOrder.setStatus(status);
            if (StrUtil.isNotBlank(comment)) {
                commonWorkOrder.setComment(comment);
            }
            switch (status) {
                case OrderStatusConstant.Processing:
                    commonWorkOrder.setProcessingDate(new Date());
                    break;
                case OrderStatusConstant.Done:
                    commonWorkOrder.setCompleteDate(new Date());
                    break;
                case OrderStatusConstant.Reload:
                    commonWorkOrder.setReloadDate(new Date());
                    break;
                case OrderStatusConstant.Error:
                    commonWorkOrder.setErrorDate(new Date());
                    break;
                case OrderStatusConstant.Close:
                    commonWorkOrder.setCloseDate(new Date());
                    break;
            }
            this.saveOrUpdate(commonWorkOrder);
            return Response.success();
        }
        return Response.failed(-500, "type error!");
    }

    @Override
    @Transactional
    public Response<?> deleteWorkSheet(Set<String> ids) {
        if (ids.isEmpty() || ids.size() == SystemConstant.ZERO) {
            throw new SystemException(
                    ResponseConstant.Code.BAD_REQUEST,
                    MessageUtils.message(ResponseConstant.Message.UNABLE_DELETE_WORK_SHEET)
            );
        }
        this.removeByIds(ids);
        List<String> commonWorkOrderMessageIds = commonWorkOrderMessageMapper.selectListByQueryAs(
                QueryWrapper.create().select(common_work_order_message.id)
                        .from(common_work_order_message)
                        .where(common_work_order_message.order_id.eq(ids)),
                String.class
        );
        commonWorkOrderMessageMapper.deleteBatchByIds(commonWorkOrderMessageIds);
        return Response.success();
    }

    //格式化用户邮箱,<EMAIL> -> 1**@qq.com
    public String formatUserEmail(String userID) {
        UserEntity consumer = userService.getMapper().selectOneByCondition(user.email.eq(userID));
        if (ObjUtil.isNotNull(consumer)) {
            String userEmail = consumer.getEmail();
            //存放邮箱首字符
            String userEmailFirstChar = userEmail.substring(0, 1);
            //存放邮箱@前面的字符串
            String userEmailBefore = userEmail.substring(0, userEmail.indexOf("@"));
            //存放邮箱@后面的字符串
            String userEmailAfter = userEmail.substring(userEmail.indexOf("@"), userEmail.length());

            if (userEmailBefore.length() > 1) {
                return userEmailFirstChar + "**" + userEmailAfter;
            } else {
                return "**" + userEmailAfter;
            }
        }
        return null;
    }

}
