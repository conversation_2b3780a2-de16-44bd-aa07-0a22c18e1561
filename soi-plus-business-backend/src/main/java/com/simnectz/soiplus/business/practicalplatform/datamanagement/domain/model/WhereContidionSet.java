package com.simnectz.soiplus.business.practicalplatform.datamanagement.domain.model;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;

public class WhereContidionSet {

    private static String[] strArr = {"IS NULL", "IS NOT NULL", "IN", "NOT IN", "LIKE", "NOT LIKE"};
    private static String[] strInArr = {"IN", "NOT IN", "LIKE", "NOT LIKE"};

    private String name, value, condition, logic;

    public WhereContidionSet(String name, String value, String condition, String logic) {
        this.name = name;
        this.value = value;
        this.condition = condition;
        this.logic = logic;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getCondition() {
        return condition;
    }

    public void setCondition(String condition) {
        this.condition = condition;
    }

    public String getLogic() {
        return logic;
    }

    public void setLogic(String logic) {
        this.logic = logic;
    }


    public String toStringFormat(List<String> list) {
        StringBuffer buff = new StringBuffer();
        if (logic != null && !logic.isEmpty()) buff.append(" " + logic + " ");
        buff.append(name + " ");
        buff.append(condition);

        List<String> keyword1 = Arrays.asList(strArr);
        if (!keyword1.contains(condition.toUpperCase())) {
            if (value !=null && !value.trim().isEmpty()) {
                buff.append("?");
                list.add(value);
            }
        } else {
            List<String> keyword2 = Arrays.asList(strInArr);
            if (keyword2.contains(condition.toUpperCase())) {
                if (value !=null && !value.trim().isEmpty()) {
                    if(condition.toUpperCase().equals("IN") || condition.toUpperCase().equals("NOT IN")){
                        String[] split = value.split(",");
                        if (split.length > 0) {
                            buff.append(" (");
                            for (int i = 0; i < split.length; i++) {
                                buff.append("?");
                                list.add(split[i]);
                                if (i != split.length -1) {
                                    buff.append(",");
                                }
                            }
                            buff.append(" )");
                        }
                    } else {
                        buff.append(" \"%" + value.trim() + "%\"");
                    }
                }
            }
        }
        return buff.toString();
    }


    @Override
    public String toString() {
        return "WhereContidionSet{" +
                "name='" + name + '\'' +
                ", value='" + value + '\'' +
                ", condition='" + condition + '\'' +
                ", logic='" + logic + '\'' +
                '}';
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        WhereContidionSet that = (WhereContidionSet) o;
        return Objects.equals(name, that.name) &&
                Objects.equals(value, that.value) &&
                Objects.equals(condition, that.condition) &&
                Objects.equals(logic, that.logic);
    }

    @Override
    public int hashCode() {
        return Objects.hash(name, value, condition, logic);
    }
}
