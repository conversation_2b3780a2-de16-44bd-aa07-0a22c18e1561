package com.simnectz.soiplus.business.practicalplatform.usercaseclass.service.impl;

import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson2.JSONObject;
import com.mybatisflex.core.query.QueryWrapper;
import com.simnectz.soiplus.business.practicalplatform.documentlibrary.domain.entity.DocumentEntity;
import com.simnectz.soiplus.business.practicalplatform.documentlibrary.mapper.DocumentMapper;
import com.simnectz.soiplus.business.practicalplatform.usercaseclass.domain.entity.UserCaseClassEntity;
import com.simnectz.soiplus.business.practicalplatform.usercaseclass.mapper.UserCaseClassMapper;
import com.simnectz.soiplus.business.practicalplatform.usercaseclass.service.UserCaseClassService;
import com.simnectz.soiplus.business.system.constant.ResponseConstant;
import com.simnectz.soiplus.business.system.domain.vo.Response;
import com.simnectz.soiplus.business.system.utils.MessageUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

import static com.simnectz.soiplus.business.practicalplatform.documentlibrary.domain.entity.table.DocumentTableDef.document;
import static com.simnectz.soiplus.business.practicalplatform.usercaseclass.domain.entity.table.UserCaseClassTableDef.user_case_class;

@Service
@RequiredArgsConstructor
public class UserCaseClassServiceImpl implements UserCaseClassService {

    private final UserCaseClassMapper userCaseClassMapper;
    private final DocumentMapper documentMapper;

    @Override
    public Response<?> listByGroup(String group) {
        List<UserCaseClassEntity> classList = userCaseClassMapper.selectListWithRelationsByQueryAs(
                QueryWrapper.create()
                        .select(user_case_class.all_columns)
                        .from(user_case_class)
                        .where(user_case_class.group_name_en.eq(group)),
                UserCaseClassEntity.class
        );
        return Response.success(classList);
    }

    @Override
    public Response<?> save(UserCaseClassEntity userCaseClass) {
        if (StrUtil.isBlank(userCaseClass.getGroupName())) {
            userCaseClass.setGroupName("默认");
        }
        if (StrUtil.isBlank(userCaseClass.getGroupNameEn())) {
            userCaseClass.setGroupName("Default");
        }
        userCaseClassMapper.insertOrUpdateSelective(userCaseClass);
        return Response.success();
    }

    @Override
    public Response<?> getAll() {
        List<String> byGroups = userCaseClassMapper.selectListWithRelationsByQueryAs(
                QueryWrapper.create()
                        .select(user_case_class.group_name)
                        .from(user_case_class)
                        .groupBy(user_case_class.group_name),
                String.class
        );
        JSONObject response = new JSONObject();
        for (String group : byGroups) {
            List<UserCaseClassEntity> classList = userCaseClassMapper.selectListWithRelationsByQueryAs(
                    QueryWrapper.create()
                            .select(user_case_class.all_columns)
                            .from(user_case_class)
                            .where(user_case_class.group_name_en.eq(group)),
                    UserCaseClassEntity.class
            );
            response.put(group, classList);
        }
        return Response.success(response);
    }

    @Override
    public Response<?> deleteClass(String id) {
        List<DocumentEntity> documentList = documentMapper.selectListWithRelationsByQueryAs(
                QueryWrapper.create()
                        .select(document.all_columns)
                        .from(document)
                        .where(document.class_id.eq(id)),
                DocumentEntity.class
        );
        if (documentList.size() > 0) {
            return Response.failed(-1, MessageUtils.message(ResponseConstant.Message.THE_CLASS_ASSOCIATED_DOCUMENT_DATA));
        }

        userCaseClassMapper.deleteById(Integer.valueOf(id));
        return Response.success();
    }

}
