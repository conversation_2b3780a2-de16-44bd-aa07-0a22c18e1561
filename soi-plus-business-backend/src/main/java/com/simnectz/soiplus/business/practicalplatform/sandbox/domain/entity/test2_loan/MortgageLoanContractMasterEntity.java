package com.simnectz.soiplus.business.practicalplatform.sandbox.domain.entity.test2_loan;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.Table;
import lombok.*;

@Data
@With
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(value = "mortgageloancontractmaster",dataSource = "loan")
public class MortgageLoanContractMasterEntity {

    @Id
    @Column(value = "ID")
    private String id;
    @Column(value = "AccountNumber")
    private String accountNumber;
    @Column(value = "ContractNumber")
    private String contractnumber;
    @Column(value = "LoanAmount")
    private String loanAmount;
    @Column(value = "LoanAnnualRate")
    private String loanAnnualRate;
    @Column(value = "InterestAmount")
    private String interestAmount;
    @Column(value = "CurrencyCode")
    private String currencyCode;
    @Column(value = "RepaymentPeriod")
    private String repaymentPeriod;
    @Column(value = "TotalPhase")
    private String totalPhase;
    @Column(value = "ContractTime")
    private String contractTime;
    @Column(value = "RepaymentAccountNumber")
    private String repaymentAccountNumber;
    @Column(value = "RepaymentCycle")
    private String repaymentCycle;
    @Column(value = "ContractStatus")
    private String contractStatus;
    @Column(value = "RateFloatPercentage")
    private String rateFloatPercentage;
    @Column(value = "OverdueInterestUplift")
    private String overdueInterestUplift;
    @Column(value = "RepaymentPlan")
    private String repaymentPlan;
    @Column(value = "DrawdownDate")
    private String drawdownDate;
    @Column(value = "OutstandingAmount")
    private String outstandingAmount;
    @Column(value = "PenaltyPercentage")
    private String penaltyPercentage;
    @Column(value = "EarlyRedemptionPenalty")
    private String earlyRedemptionPenalty;
    @Column(value = "PropertyAddressFormat")
    private String propertyAddressFormat;
    @Column(value = "PropertyAreaCode")
    private String propertyAreaCode;
    @Column(value = "PropertyFlat")
    private String propertyFlat;
    @Column(value = "PropertyRoom")
    private String propertyRoom;
    @Column(value = "PropertyFloor")
    private String propertyFloor;
    @Column(value = "PropertyBlock")
    private String propertyBlock;
    @Column(value = "PropertyBuilding")
    private String propertyBuilding;
    @Column(value = "PropertyEstate")
    private String propertyEstate;
    @Column(value = "PropertyStreetNumber")
    private String propertyStreetNumber;
    @Column(value = "PropertyStreetName")
    private String propertyStreetName;
    @Column(value = "PropertyPostCode")
    private String propertyPostCode;
    @Column(value = "PropertyCountry")
    private String propertyCountry;
    @Column(value = "PropertyAddressLine1")
    private String propertyAddressLine1;
    @Column(value = "PropertyAddressLine2")
    private String propertyAddressLine2;
    @Column(value = "PropertyAddressLine3")
    private String propertyAddressLine3;
    @Column(value = "PropertyAddressLine4")
    private String propertyAddressLine4;
    @Column(value = "PropertyWithRoof")
    private String propertyWithRoof;
    @Column(value = "PropertyWithGarden")
    private String propertyWithGarden;
    @Column(value = "PropertyWithCarpark")
    private String propertyWithCarpark;
    @Column(value = "PropertyCarparkNumber")
    private String propertyCarparkNumber;
    @Column(value = "PropertyType")
    private String propertyType;
    @Column(value = "PropertyClassification")
    private String propertyClassification;
    @Column(value = "PropertyTransactionStatus")
    private String propertyTransactionStatus;
    @Column(value = "PropertyMonthlyRental")
    private String propertyMonthlyRental;
    @Column(value = "PurchasePrice")
    private String purchasePrice;
    @Column(value = "LoanScheme")
    private String loanScheme;
    @Column(value = "SolicitorsFirm")
    private String solicitorsFirm;
    @Column(value = "SolicitorsContactPerson")
    private String solicitorsContactPerson;
    @Column(value = "SolicitorsPhoneNumber")
    private String solicitorsPhoneNumber;
    @Column(value = "SolicitorsFaxNumber")
    private String solicitorsFaxNumber;

}
