package com.simnectz.soiplus.business.practicalplatform.sandbox.mapper.test2_deposit;

import com.mybatisflex.annotation.UseDataSource;
import com.simnectz.soiplus.business.practicalplatform.sandbox.domain.entity.test2_deposit.CurrentAccountMasterEntity;
import com.simnectz.soiplus.business.system.mapper.BasicMapper;
import org.apache.ibatis.annotations.Mapper;

/**
 * CurrentAccountMaster data access layer interface
 */
@Mapper
@UseDataSource("deposit")
public interface CurrentAccountMasterMapper extends BasicMapper<CurrentAccountMasterEntity> {
}
