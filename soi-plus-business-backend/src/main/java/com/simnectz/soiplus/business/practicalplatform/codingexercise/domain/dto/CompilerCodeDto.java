package com.simnectz.soiplus.business.practicalplatform.codingexercise.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;


@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
public class CompilerCodeDto implements Serializable {

    private static final long serialVersionUID = 5997295956137727339L;

    /**
     * User ID
     */
    private String userId;
    /**
     * The code to be compiled and run
     */
    private String code;
    /**
     * Programming language name
     */
    private String language;

}
