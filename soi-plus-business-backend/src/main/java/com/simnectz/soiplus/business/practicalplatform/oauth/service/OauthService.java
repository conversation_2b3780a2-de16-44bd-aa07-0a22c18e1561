package com.simnectz.soiplus.business.practicalplatform.oauth.service;

import com.simnectz.soiplus.business.practicalplatform.oauth.domain.entity.OauthClientDetailsEntity;
import com.simnectz.soiplus.business.system.domain.vo.Response;

public interface OauthService {

    Response<?> getOauthClientToken(String userId, String courseID);

    Response<?> registerClientDetails(OauthClientDetailsEntity oauthClientDetails);

    Response<?> archiveClient(String clientId);

    Response<?> clientCredentials(String client_id, String client_secret, String grant_type, String scope);

    Response<?> authorization_code(String client_id, String client_secret, String grant_type, String code, String redirect_uri);

    Response<?> password(String client_id, String client_secret, String grant_type, String scope, String username, String password);

    Response<?> refresh_token(String client_id, String client_secret, String grant_type, String refresh_token);

    Response<?> verify_token(String token, String client_id);

    OauthClientDetailsEntity loadOauthClientDetails(String clientId);
}
