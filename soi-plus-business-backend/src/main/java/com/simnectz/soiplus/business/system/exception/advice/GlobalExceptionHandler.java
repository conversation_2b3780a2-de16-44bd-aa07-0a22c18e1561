package com.simnectz.soiplus.business.system.exception.advice;

import com.simnectz.soiplus.business.system.constant.ResponseConstant;
import com.simnectz.soiplus.business.system.constant.SystemConstant;
import com.simnectz.soiplus.business.system.domain.vo.Response;
import com.simnectz.soiplus.business.system.exception.SystemException;
import com.simnectz.soiplus.business.system.utils.MessageUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.util.HashMap;

/**
 * Global exception handler
 */
@Slf4j
@RestControllerAdvice
public class GlobalExceptionHandler {

    @ExceptionHandler(SystemException.class)
    public ResponseEntity<Response<?>> handlerGlobalException(SystemException exception) {
        return ResponseEntity.ok(Response.failed(exception.getCode(), exception.getMessage()));
    }

    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<Response<?>> validExceptionHandler(MethodArgumentNotValidException exception) {
        return ResponseEntity.ok(Response.failed(
                ResponseConstant.Code.BAD_REQUEST,
                exception.getBindingResult().getAllErrors().get(SystemConstant.ZERO).getDefaultMessage()
        ));
    }

    @ExceptionHandler(HttpRequestMethodNotSupportedException.class)
    public ResponseEntity<Response<?>> handlerHttpRequestMethodNotSupportedException(HttpRequestMethodNotSupportedException exception) {

        HashMap<String, String> keywords = new HashMap<>();
        keywords.put("{method}", exception.getMethod());
        return ResponseEntity.ok(Response.failed(
                ResponseConstant.Code.METHOD_NOT_ALLOWED,
                MessageUtils.replacePlaceholders(
                        ResponseConstant.Message.METHOD_NOT_ALLOWED,
                        keywords
                )
        ));
    }

    @ExceptionHandler(RuntimeException.class)
    public ResponseEntity<Response<?>> handlerRuntimeException(RuntimeException exception) {
        log.error(exception.getMessage(), exception);
        return ResponseEntity.ok(Response.failed(
                ResponseConstant.Code.SERVER_ERROR,
                MessageUtils.message(ResponseConstant.Message.SERVER_ERROR)
        ));
    }

    @ExceptionHandler(Exception.class)
    public ResponseEntity<Response<?>> handlerException(Exception exception) {
        log.error(exception.getMessage(), exception);
        return ResponseEntity.ok(Response.failed(
                ResponseConstant.Code.SERVER_ERROR,
                MessageUtils.message(ResponseConstant.Message.SERVER_ERROR)
        ));
    }

}
