package com.simnectz.soiplus.business.practicalplatform.codingexercise.resource;

import com.simnectz.soiplus.business.practicalplatform.codingexercise.domain.dto.*;
import com.simnectz.soiplus.business.practicalplatform.codingexercise.service.CodingExerciseService;
import com.simnectz.soiplus.business.system.domain.vo.Response;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

/**
 * 编码练习模块
 */
@CrossOrigin
@RestController
@RequestMapping("v1/practical/coding-exercise")
@RequiredArgsConstructor
public class CodingExerciseResource {

    private final CodingExerciseService codingExerciseService;

    /**
     * 创建试题
     * @param createCodingQuestionInfo
     * @return
     */
    @PostMapping(value = "/create")
    public ResponseEntity<Response<?>> createCodingQuestion(@RequestBody CreateCodingQuestionDto createCodingQuestionInfo) {
        return ResponseEntity.ok(codingExerciseService.createCodingQuestion(createCodingQuestionInfo));
    }

    /**
     * 更新试题
     * @param updateCodingQuestionInfo
     * @return
     */
    @PostMapping(value = "/update")
    public ResponseEntity<Response<?>> updateCodingQuestionById(@RequestBody UpdateCodingQuestionDto updateCodingQuestionInfo) {
        return ResponseEntity.ok(codingExerciseService.updateCodingQuestionById(updateCodingQuestionInfo));
    }

    /**
     * 批量删除试题
     * @param ids
     * @return
     */
    @PostMapping(value = "/delete")
    public ResponseEntity<Response<?>> deleteCodingQuestionByIds(@RequestBody IdsDto<String> ids) {
        return ResponseEntity.ok(codingExerciseService.deleteCodingQuestionByIds(ids.getIds()));
    }

    /**
     * 查询练习题分类
     * @return
     */
    @GetMapping(value = "/category")
    public ResponseEntity<Response<?>> queryCodingQuestionCategory() {
        return ResponseEntity.ok(codingExerciseService.queryCodingQuestionCategory());
    }

    /**
     * 按类别id查询习题子分类
     * @param categoryId
     * @return
     */
    @GetMapping(value = "/subcategory")
    public ResponseEntity<Response<?>> queryCodingQuestionSubcategory(@RequestParam("categoryId") String categoryId) {
        return ResponseEntity.ok(codingExerciseService.queryCodingQuestionSubcategory(categoryId));
    }

    /**
     * 按类别查询所有编码题目
     * @param category
     * @return
     */
    @GetMapping(value = "/titles")
    public ResponseEntity<Response<?>> queryCodingQuestionsTitle(@RequestParam(name = "category") String category) {
        return ResponseEntity.ok(codingExerciseService.queryCodingQuestionsTitleByCategory(category));
    }

    /**
     * 通过编码题id查询编码题详细信息
     * @param questionId
     * @return
     */
    @GetMapping(value = "/item")
    public ResponseEntity<Response<?>> queryCodingQuestionById(@RequestParam("questionId") String questionId) {
        return ResponseEntity.ok(codingExerciseService.queryCodingQuestionById(questionId));
    }

    /**
     * 查询某道题的练习答案
     * @param userId
     * @param questionId
     * @param language
     * @return
     */
    @GetMapping(value = "/answer")
    public ResponseEntity<Response<?>> queryCodingQuestionAnswer(
            @RequestParam(name = "userId") String userId,
            @RequestParam(name = "questionId") String questionId,
            @RequestParam(name = "language") String language
    ) {
        return ResponseEntity.ok(codingExerciseService.queryCodingQuestionAnswer(userId, questionId, language));
    }

    /**
     * 通过用户id查询实践答题的历史记录
     * @param userId
     * @return
     */
    @GetMapping(value = "/answers")
    public ResponseEntity<Response<?>> queryCodingQuestionAnswers(@RequestParam(name = "userId") String userId) {
        return ResponseEntity.ok(codingExerciseService.queryCodingQuestionAnswers(userId));
    }

    /**
     * 保存编码问题练习答案
     * @param saveAnswerInfo
     * @return
     */
    @PostMapping(value = "/save-answer")
    public ResponseEntity<Response<?>> saveCodingQuestionAnswer(@RequestBody SaveAnswerInfoDto saveAnswerInfo) {
        return ResponseEntity.ok(codingExerciseService.saveCodingQuestionAnswer(saveAnswerInfo));
    }

    /**
     * 运行编码题
     * @param compiler
     * @return
     */
    @RequestMapping(value = "/compiler")
    public ResponseEntity<Response<?>> compiler(@RequestBody CompilerCodeDto compiler) {
        return ResponseEntity.ok(codingExerciseService.compiler(compiler));
    }

    /**
     * 按页面查询编码问题信息
     * @param key
     * @param category
     * @param status
     * @param page
     * @param rows
     * @param sortBy
     * @param desc
     * @return
     */
    @GetMapping(value = "/page")
    public ResponseEntity<Response<?>> queryCodingQuestionsByPage(
            @RequestParam(name = "key", required = false) String key,
            @RequestParam(name = "category", required = false) String category,
            @RequestParam(name = "status", required = false) Integer status,
            @RequestParam(name = "page", defaultValue = "1") Integer page,
            @RequestParam(name = "rows", defaultValue = "10") Integer rows,
            @RequestParam(name = "sortBy", required = false) String sortBy,
            @RequestParam(value = "desc", defaultValue = "true") Boolean desc
    ) {
        return ResponseEntity.ok(codingExerciseService.queryCodingQuestionsByPage(key, category, status, page, rows, sortBy, desc));
    }

}
