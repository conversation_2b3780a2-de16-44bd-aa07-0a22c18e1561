package com.simnectz.soiplus.business.learningspace.interactivespace.resource;

import com.simnectz.soiplus.business.learningspace.interactivespace.domain.dto.GroupModelDto;
import com.simnectz.soiplus.business.learningspace.interactivespace.domain.vo.DelGroupDataVo;
import com.simnectz.soiplus.business.learningspace.interactivespace.service.GroupService;
import com.simnectz.soiplus.business.system.domain.vo.Response;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("v1/learn/group")
@RequiredArgsConstructor
public class GroupResource {

    private final GroupService groupService;

    /**
     * 分组中新增和编辑分组
     *
     * @param json
     * @return
     */
    //TODO json入参中多加一个subjectId参数(非必填项)
    @PostMapping("/groupingstudent")
    public ResponseEntity<Response<?>> groupingStudent(@RequestBody String json) {
        return ResponseEntity.ok(groupService.groupingStudent(json));
    }

    /**
     * 批量删除分组
     *
     * @param delGroupDataList
     * @return
     */
    //TODO DelGroupDataVo多加了一个参数subjectId(非必填项)
    @PostMapping("/delgroupingstudent")
    public ResponseEntity<Response<?>> delGroupingStudent(@RequestBody List<DelGroupDataVo> delGroupDataList) {
        return ResponseEntity.ok(groupService.delGroup(delGroupDataList));
    }

    /**
     * 邀请学生列表加入分组
     *
     * @param groupModelDto
     * @return
     */
    @PostMapping("/invitejoingroup")
    public ResponseEntity<Response<?>> inviteJoinGroup(@RequestBody GroupModelDto groupModelDto) {
        return ResponseEntity.ok(groupService.inviteJoinGroup(groupModelDto));
    }

    /**
     * 退出分组
     *
     * @param userID
     * @param groupID
     * @return
     */
    @PostMapping("/exitgroup")
    public ResponseEntity<Response<?>> exitGroup(String userID, String groupID) {
        return ResponseEntity.ok(groupService.exitGroup(userID, groupID));
    }

    /**
     * 查看邀请列表
     *
     * @return
     */
    @GetMapping("/invitedlist")
    public ResponseEntity<Response<?>> invitedList() {
        return ResponseEntity.ok(groupService.invitedList());
    }

    /**
     * 通知中的新增和编辑、分组中列表 - 获取该学生下的分组信息
     *
     * @param courseID
     * @param studentID
     * @return
     */
    @GetMapping("/getgroupingstudent")
    public ResponseEntity<Response<?>> groupList(String courseID, String studentID) {
        return ResponseEntity.ok(groupService.getGroupList(courseID, studentID));
    }

    /**
     * 通知中查询未分组的学生信息
     *
     * @param courseID
     * @return
     */
    @GetMapping("/getungroupedstudents")
    public ResponseEntity<Response<?>> getUngroupedStudents(@RequestParam String courseID) {
        return ResponseEntity.ok(groupService.getUngroupedStudents(courseID));
    }

    /**
     * 通知中查询关联课程学生信息
     *
     * @param courseID
     * @return
     */
    @GetMapping("/getconnectcoursestudent")
    public ResponseEntity<Response<?>> getConnectCourseStudent(String courseID) {
        return ResponseEntity.ok(groupService.getConnectCourseStudent(courseID));
    }

}
