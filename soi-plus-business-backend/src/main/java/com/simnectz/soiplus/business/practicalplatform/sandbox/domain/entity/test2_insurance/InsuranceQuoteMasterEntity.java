package com.simnectz.soiplus.business.practicalplatform.sandbox.domain.entity.test2_insurance;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.Table;
import lombok.*;

@Data
@With
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(value = "insurancequotemaster",dataSource = "insurance")
public class InsuranceQuoteMasterEntity {

    @Id
    @Column(value = "ID")
    private String id;
    @Column(value = "QuoteId")
    private String quoteId;
    @Column(value = "ProductCode")
    private String productCode;
    @Column(value = "CoverageType")
    private String coverageType;
    @Column(value = "StartDate")
    private String startDate;
    @Column(value = "EndDate")
    private String endDate;
    @Column(value = "InsuredPerson")
    private String insuredPerson;
    @Column(value = "PersonQuantity")
    private String personQuantity;
    @Column(value = "Destination")
    private String destination;
    @Column(value = "Status")
    private String status;
    @Column(value = "CcyCode")
    private String ccyCode;
    @Column(value = "CreationTime")
    private String creationTime;
    @Column(value = "LastUpdateTime")
    private String lastUpdateTime;
    @Column(value = "CustomerName")
    private String customerName;
    @Column(value = "CustomerID")
    private String customerId;

}
