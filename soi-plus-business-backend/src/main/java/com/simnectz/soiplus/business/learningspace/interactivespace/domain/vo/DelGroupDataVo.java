package com.simnectz.soiplus.business.learningspace.interactivespace.domain.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class DelGroupDataVo implements Serializable {

    private static final long serialVersionUID = -3640129896373187669L;

    private String groupId;

    private String courseId;

    private String subjectId;
}
