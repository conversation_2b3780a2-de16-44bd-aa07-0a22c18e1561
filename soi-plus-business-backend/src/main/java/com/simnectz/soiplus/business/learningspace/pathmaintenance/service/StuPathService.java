package com.simnectz.soiplus.business.learningspace.pathmaintenance.service;

import com.mybatisflex.core.service.IService;
import com.simnectz.soiplus.business.learningspace.pathmaintenance.domain.dto.StuPathDto;
import com.simnectz.soiplus.business.learningspace.pathmaintenance.domain.dto.StuStudyInfoDto;
import com.simnectz.soiplus.business.learningspace.pathmaintenance.domain.enitity.StuPathEntity;
import com.simnectz.soiplus.business.learningspace.pathmaintenance.domain.vo.StuPathVo;
import com.simnectz.soiplus.business.system.domain.vo.Paginate;
import com.simnectz.soiplus.business.system.domain.vo.Response;

import java.util.Date;
import java.util.List;
import java.util.Set;

public interface StuPathService extends IService<StuPathEntity> {

    /**
     * Paginate enquiry StuPath
     */
    Response<Paginate<StuPathVo>> paginateEnquiryStuPath(
            String keywords,
            Integer currentPage,
            Integer pageSize,
            String orderBy,
            Boolean isAsc,
            Date startTime,
            Date endTime,
            String courseCode,
            String courseCategoryId
    );

    Response<?> createStuPath(List<StuPathDto> stuPathDtoList);

    Response<?> batchDeleteStuPath(Set<String> stuPathIds);

    Response<?> updateStuPathSubscriptionStatus(StuPathDto stuPathDto);

    Response<?> putFirstStudy(StuStudyInfoDto stuStudyInfoDto);

    Response<?> putFinishedStudy(StuStudyInfoDto stuStudyInfoDto);

    Response<?> getSubjectRecommend();
}
