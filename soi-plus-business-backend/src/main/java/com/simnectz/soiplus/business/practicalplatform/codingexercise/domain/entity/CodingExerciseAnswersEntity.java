package com.simnectz.soiplus.business.practicalplatform.codingexercise.domain.entity;

import com.mybatisflex.annotation.Table;
import com.simnectz.soiplus.business.system.domain.entity.BasicEntity;
import lombok.*;

import java.io.Serializable;

@Data
@With
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(value = "coding_exercise_answers")
@EqualsAndHashCode(callSuper = true)
public class CodingExerciseAnswersEntity extends BasicEntity implements Serializable {

    private static final long serialVersionUID = -5272109896793478406L;

    private String userId;

    private String questionId;

    private String language;

    private String code;

}
