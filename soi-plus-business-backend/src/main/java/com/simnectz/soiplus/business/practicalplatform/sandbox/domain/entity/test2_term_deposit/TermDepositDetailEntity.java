package com.simnectz.soiplus.business.practicalplatform.sandbox.domain.entity.test2_term_deposit;


import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.Table;
import lombok.*;

@Data
@With
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(value = "termdepositdetail",dataSource = "term_deposit")
public class TermDepositDetailEntity {

    @Id
    @Column(value = "ID")
    private String id;

    @Column(value = "AccountNumber")
    private String accountNumber;

    @Column(value = "SandBoxId")
    private String sandBoxId;

    @Column(value = "DepositNumber")
    private String depositNumber;

    @Column(value = "DepositAmount")
    private String depositAmount;

    @Column(value = "TermPeriod")
    private String termPeriod;

    @Column(value = "TermInterestRate")
    private String termInterestRate;

    @Column(value = "MaturityDate")
    private String maturityDate;

    @Column(value = "MaturityInterest")
    private String maturityInterest;

    @Column(value = "MaturityAmount")
    private String maturityAmount;

    @Column(value = "MaturityStatus")
    private String maturityStatus;

    @Column(value = "CreateDate")
    private String createDate;

    @Column(value = "LastUpdatedDate")
    private String lastUpdatedDate;

    @Column(value = "SystemDate")
    private String systemDate;
}
