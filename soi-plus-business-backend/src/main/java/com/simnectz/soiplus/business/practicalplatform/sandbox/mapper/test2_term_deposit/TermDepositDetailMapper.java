package com.simnectz.soiplus.business.practicalplatform.sandbox.mapper.test2_term_deposit;

import com.mybatisflex.annotation.UseDataSource;
import com.simnectz.soiplus.business.practicalplatform.sandbox.domain.entity.test2_term_deposit.TermDepositDetailEntity;
import com.simnectz.soiplus.business.system.mapper.BasicMapper;
import org.apache.ibatis.annotations.Mapper;

/**
 * TermDepositDetail data access layer interface
 */
@Mapper
@UseDataSource("term_deposit")
public interface TermDepositDetailMapper extends BasicMapper<TermDepositDetailEntity> {
}
