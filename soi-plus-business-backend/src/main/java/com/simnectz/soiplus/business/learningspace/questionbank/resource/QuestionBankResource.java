package com.simnectz.soiplus.business.learningspace.questionbank.resource;

import com.simnectz.soiplus.business.learningspace.questionbank.domain.dto.CreateQuestionBankDto;
import com.simnectz.soiplus.business.learningspace.questionbank.domain.dto.EnquiryQuestionBankDto;
import com.simnectz.soiplus.business.learningspace.questionbank.domain.dto.UpdateQuestionBankDto;
import com.simnectz.soiplus.business.learningspace.questionbank.domain.vo.QuestionBankVo;
import com.simnectz.soiplus.business.learningspace.questionbank.service.QuestionBankService;
import com.simnectz.soiplus.business.system.domain.vo.Paginate;
import com.simnectz.soiplus.business.system.domain.vo.Response;
import lombok.RequiredArgsConstructor;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.Set;

@RestController
@RequestMapping("v1/learn/question-bank")
@RequiredArgsConstructor
public class QuestionBankResource {

    private final QuestionBankService questionBankService;

    @GetMapping
    public ResponseEntity<Response<Paginate<QuestionBankVo>>> paginateEnquiryQuestions(EnquiryQuestionBankDto questionBankPaginate) {
        return ResponseEntity.ok(questionBankService.paginateEnquiryQuestions(
                questionBankPaginate.getKeywords(),
                questionBankPaginate.getCurrentPage(),
                questionBankPaginate.getPageSize(),
                questionBankPaginate.getOrderBy(),
                questionBankPaginate.getIsAsc(),
                questionBankPaginate.getStartTime(),
                questionBankPaginate.getEndTime(),
                questionBankPaginate.getCourseCategoryId(),
                questionBankPaginate.getCourseCode(),
                questionBankPaginate.getSubjectCode()
        ));
    }

    @PostMapping
        public ResponseEntity<Response<?>> createQuestion(@RequestBody @Validated CreateQuestionBankDto createQuestionBankDto) {
        return ResponseEntity.ok(questionBankService.createQuestion(createQuestionBankDto));
    }

    @PutMapping
    public ResponseEntity<Response<?>> updateQuestion(@RequestBody @Validated UpdateQuestionBankDto updateQuestionBankDto) {
        return ResponseEntity.ok(questionBankService.updateQuestion(updateQuestionBankDto));
    }

    @DeleteMapping
    public ResponseEntity<Response<?>> batchDeleteQuestions(@RequestParam("ids") Set<String> questionIds) {
        return ResponseEntity.ok(questionBankService.batchDeleteQuestions(questionIds));
    }

    @GetMapping("options")
    public ResponseEntity<Response<?>> getAll() {
        return ResponseEntity.ok(Response.success(questionBankService.enquiryAllQuestions()));
    }

    @GetMapping("/subject-code/{subjectCode}")
    public ResponseEntity<Response<?>> getBySubjectCode(@PathVariable String subjectCode) {
        return ResponseEntity.ok(Response.success(questionBankService.findQuestionBySubjectCode(subjectCode)));
    }

    @GetMapping("/question-id/{id}")
    public ResponseEntity<Response<?>> getByQuestionId(@PathVariable String id) {
        return ResponseEntity.ok(Response.success(questionBankService.findQuestionById(id)));
    }
}
