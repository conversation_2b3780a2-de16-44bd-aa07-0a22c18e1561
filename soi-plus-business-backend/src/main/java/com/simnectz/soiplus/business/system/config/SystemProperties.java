package com.simnectz.soiplus.business.system.config;

import lombok.Data;
import lombok.Getter;
import lombok.Setter;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * System properties entity
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "system")
public class SystemProperties {

    private Authorize authorize;
    private Email email;
    private Upload upload;

    @Getter
    @Setter
    public static class Authorize {
        private String header;
        private String prefix;
        private String accessTokenSecretKey;
        private Integer accessTokenExpireTime;
        private String refreshTokenSecretKey;
        private Integer refreshTokenExpireTime;
        private String passwordEncodeSecretKey;
    }

    @Getter
    @Setter
    public static class Email {
        private String from;
    }

    @Getter
    @Setter
    public static class Upload {
        private String requestUrl;
        private String storagePath;
        private String sqlFilePath;
        private String tableInfoFilePath;
    }

}
