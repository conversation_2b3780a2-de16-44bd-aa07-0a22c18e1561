package com.simnectz.soiplus.business.practicalplatform.workspace.domain.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import com.mybatisflex.core.keygen.KeyGenerators;
import lombok.*;

import java.io.Serializable;
import java.util.Date;

@Data
@With
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(value = "workspace_web_ide")
@EqualsAndHashCode()
public class WorkspaceWebIdeEntity implements Serializable {

    private static final long serialVersionUID = 1804380440529447713L;

    @Id(keyType = KeyType.Generator, value = KeyGenerators.uuid)
    private String id;

    private String userId;

    private String name;

    private String accessUrl;

    private String containerId;

    private String port;

    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date lastLoginTime;

    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss")
    private Date shutDownTime;

    private String status;
}
