package com.simnectz.soiplus.business.practicalplatform.lowcodetool.domain.entity;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import com.mybatisflex.core.keygen.KeyGenerators;
import com.simnectz.soiplus.business.system.utils.DateUtils;
import lombok.*;

import java.io.Serializable;
import java.util.Date;

@Data
@With
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
@Table(value = "low_code_api_ui")
public class ApiUiEntity implements Serializable {

    private static final long serialVersionUID = -4277883478989781799L;

    @Id(keyType = KeyType.Generator, value = KeyGenerators.uuid)
    private String id;

    private String uiId;

    private String fiName;

    private Integer paramType;

    private String paramName;

    private String paramDataLink;

    private String componentName;

    private String componentDescription;

    private String createDate = DateUtils.parseDate(new Date(),DateUtils.DATETIME_DEFAULT_FORMAT);

    private String uiClassId;

    private String userId;

    private String userName;


    @Column(isLarge = true)
    private String code;

    private String isChecked;

}
