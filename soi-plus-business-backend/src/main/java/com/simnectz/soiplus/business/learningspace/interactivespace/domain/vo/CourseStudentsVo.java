package com.simnectz.soiplus.business.learningspace.interactivespace.domain.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class CourseStudentsVo implements Serializable {

    private static final long serialVersionUID = -1375839892568059385L;

    private String email;
    private String userName;
}
