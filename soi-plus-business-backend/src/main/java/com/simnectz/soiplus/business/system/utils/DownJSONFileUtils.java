package com.simnectz.soiplus.business.system.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Date;

public class DownJSONFileUtils {

    public void downLoad(HttpServletResponse response, String content, String fileName) {
        FileInputStream inputStream;
        OutputStream out;
        JSONObject object = JSONObject.parseObject(content.replaceAll("\\$ref", "ref"));
        String jsonString = JSON.toJSONString(object, SerializerFeature.PrettyFormat, SerializerFeature.WriteMapNullValue,
                SerializerFeature.WriteDateUseDateFormat).replaceAll("ref", "\\$ref");
        try {
            String suffix = ".json";
            response.reset();
            response.setContentType("application/json");
            response.setHeader("Content-Disposition", "attachment;filename=" + fileName + suffix);
            File file = File.createTempFile(fileName, suffix);
            FileOutputStream fos = new FileOutputStream(file);
            fos.write(jsonString.getBytes());
            inputStream = new FileInputStream(file);
            out = response.getOutputStream();
            int length;
            byte[] buffer = new byte[1024];
            while ((length = inputStream.read(buffer)) != -1) {
                out.write(buffer, 0, length);
            }
            inputStream.close();
            file.deleteOnExit();
            fos.close();
            out.flush();
            out.close();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public String downLoadZip(HttpServletResponse response,String content,String contentEn,String fileName) {
        long time = new Date().getTime();
        String zipPath = "/home/<USER>/file/" + fileName + ".zip";
        String src = "/home/<USER>/file/" + time + "/";
//        String zipPath = "D:\\home\\document\\file\\" + fileName + ".zip";
//        String src = "D:\\home\\document\\file\\" + time + "/";
        File file = new File(src);
        if (!file.exists()) { //判断文件父目录是否存在
            file.mkdirs();
        }
        File fileZip = new File(zipPath);
        if (!fileZip.getParentFile().exists()) { //判断文件父目录是否存在
            fileZip.getParentFile().mkdirs();
        }
        try {
            String suffix = ".json";
            if (content != null) {
                String jsonString = getString(content);
                Files.write(Paths.get(src+fileName+"_zh"+suffix), jsonString.getBytes(StandardCharsets.UTF_8));
            }
            if (contentEn != null){
                String jsonStringEn = getString(contentEn);
                Files.write(Paths.get(src+fileName+"_en"+suffix), jsonStringEn.getBytes(StandardCharsets.UTF_8));
            }
            FileOutputStream fileOutputStream = new FileOutputStream(new File(zipPath));
            ZipUtils.toZip(src, fileOutputStream, true);
            return ZipUtils.downloadZip(response, zipPath);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    private String getString(String content) {
        JSONObject object = JSONObject.parseObject(content.replaceAll("\\$ref", "ref"));
        String jsonString = JSON.toJSONString(object, SerializerFeature.PrettyFormat, SerializerFeature.WriteMapNullValue,
                SerializerFeature.WriteDateUseDateFormat).replaceAll("ref", "\\$ref");
        return jsonString;
    }

    public static void main(String[] args) throws IOException {
        Files.write(Paths.get("D:\\home\\document\\file\\a.txt"), "hello".getBytes(StandardCharsets.UTF_8));
    }
}
