package com.simnectz.soiplus.business.practicalplatform.codingexercise.utils;

import lombok.extern.slf4j.Slf4j;

import java.io.File;
import java.io.FileWriter;
import java.io.Writer;

@Slf4j
public class FileUtils {

    public static void createFile(String folderName, String path, String content) {
        try {
            File folder = new File(folderName);
            File file = new File(path);
            if (!folder.exists()) {
                folder.mkdir();
            }
            if (!file.exists()) {
                boolean b = file.createNewFile();
                if (b) {
                    Writer out = new FileWriter(file);
                    out.write(content);
                    out.close();
                }
            }
        } catch (Exception e) {
            log.error(e.getMessage());
        }
    }

    public static void removeFile(String path) {
        try {
            File file = new File(path);
            if (file.exists()) {
                file.delete();
            }
        } catch (Exception e) {
            log.error(e.getMessage());
        }

    }

}
