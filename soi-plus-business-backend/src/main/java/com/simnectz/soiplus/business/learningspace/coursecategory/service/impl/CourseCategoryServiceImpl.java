package com.simnectz.soiplus.business.learningspace.coursecategory.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.mybatisflex.core.paginate.Page;
import com.mybatisflex.core.query.If;
import com.mybatisflex.spring.service.impl.ServiceImpl;
import com.simnectz.soiplus.business.learningspace.coursecategory.domain.dto.CreateCourseCategoryDto;
import com.simnectz.soiplus.business.learningspace.coursecategory.domain.dto.UpdateCourseCategoryDto;
import com.simnectz.soiplus.business.learningspace.coursecategory.domain.enitity.CourseCategoryEntity;
import com.simnectz.soiplus.business.learningspace.coursecategory.domain.vo.CourseCategoryDetailsVo;
import com.simnectz.soiplus.business.learningspace.coursecategory.domain.vo.CourseCategoryVo;
import com.simnectz.soiplus.business.learningspace.coursecategory.mapper.CourseCategoryMapper;
import com.simnectz.soiplus.business.learningspace.coursecategory.service.CourseCategoryService;
import com.simnectz.soiplus.business.system.constant.ResponseConstant;
import com.simnectz.soiplus.business.system.constant.SystemConstant;
import com.simnectz.soiplus.business.system.domain.vo.Paginate;
import com.simnectz.soiplus.business.system.domain.vo.Response;
import com.simnectz.soiplus.business.system.exception.SystemException;
import com.simnectz.soiplus.business.system.utils.MessageUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.Set;

import static com.simnectz.soiplus.business.learningspace.coursecategory.domain.enitity.table.CourseCategoryTableDef.course_category;

@Service
public class CourseCategoryServiceImpl extends ServiceImpl<CourseCategoryMapper, CourseCategoryEntity> implements CourseCategoryService {
    @Override
    @Transactional(readOnly = true)
    public Response<Paginate<CourseCategoryVo>> paginateEnquiryCourseCategory(String keywords, Integer currentPage, Integer pageSize, String orderBy, Boolean isAsc, Date startTime, Date endTime) {
        Page<CourseCategoryVo> courseCategoryPage = mapper.paginateAs(
                currentPage,
                pageSize,
                this.queryChain().select()
                        .from(course_category)
                        .where(course_category.course_category_.like(keywords, If::hasText))
                        .and(course_category.create_time.gt(startTime, If::notNull))
                        .and(course_category.create_time.lt(endTime, If::notNull))
                        .orderBy(StrUtil.toUnderlineCase(orderBy), StrUtil.isNotBlank(orderBy) ? isAsc : null),
                CourseCategoryVo.class
        );

        return Response.success(new Paginate<>(courseCategoryPage.getTotalRow(), courseCategoryPage.getRecords()));

    }

    @Override
    @Transactional
    public Response<?> createCourseCategory(CreateCourseCategoryDto createCourseCategoryDto) {
        this.validationCourseCategoryNameUnique(createCourseCategoryDto.getCourseCategory());
        CourseCategoryEntity courseCategoryEntity = BeanUtil.copyProperties(createCourseCategoryDto, CourseCategoryEntity.class);
        this.save(courseCategoryEntity);
        return Response.success();
    }

    @Transactional(readOnly = true)
    public void validationCourseCategoryNameUnique(String CourseCategory) {
         boolean exists = this.exists(this.queryChain()
                .select()
                .from(course_category)
                .where(course_category.course_category_.eq(CourseCategory))
        );
        if (exists) {
            throw new SystemException(
                    ResponseConstant.Code.BAD_REQUEST,
                    MessageUtils.message(ResponseConstant.Message.COURSE_CATEGORY_ALREADY_EXISTS)
            );
        }
    }

    @Override
    @Transactional
    public Response<?> updateCourseCategory(UpdateCourseCategoryDto updateCourseCategoryDto) {
        if (ObjUtil.isNotEmpty(updateCourseCategoryDto.getId())) {
            Boolean exists = this.validationCourseCategoryExists(updateCourseCategoryDto.getId());
            if (!exists) {
                throw new SystemException(
                        ResponseConstant.Code.NOT_FOUND,
                        MessageUtils.message(ResponseConstant.Message.COURSE_CATEGORY_NOT_FOUND)
                );
            }
        }
        this.validationCourseCategoryNameUnique(updateCourseCategoryDto.getCourseCategory());
        this.updateChain()
                .set(course_category.course_category_, updateCourseCategoryDto.getCourseCategory())
                .set(course_category.course_category_picture, updateCourseCategoryDto.getCourseCategoryPicture())
                .where(course_category.id.eq(updateCourseCategoryDto.getId()))
                .update();

        return Response.success();
    }

    @Transactional(readOnly = true)
    public Boolean validationCourseCategoryExists(String id) {
        return this.exists(this.queryChain()
                .select()
                .from(course_category)
                .where(course_category.id.eq(id))
        );
    }

    @Override
    @Transactional
    public Response<?> batchDeleteCourseCategory(Set<String> courseCategoryIds) {
        if (courseCategoryIds.isEmpty() || courseCategoryIds.size() == SystemConstant.ZERO) {
            throw new SystemException(
                    ResponseConstant.Code.BAD_REQUEST,
                    MessageUtils.message(ResponseConstant.Message.UNABLE_DELETE_COURSE_CATEGORY)
            );
        }
        this.removeByIds(courseCategoryIds);
        return Response.success();
    }

    @Override
    public Response<?> enquiryCourseCategoryDetails(String courseCategoryId) {
        // Enquiry user details
        CourseCategoryDetailsVo courseCategoryDetails = mapper.selectOneByQueryAs(
                this.queryChain()
                        .select(
                                course_category.id,
                                course_category.course_category_,
                                course_category.course_category_picture
                        )
                        .from(course_category)
                        .where(course_category.id.eq(courseCategoryId)),
                CourseCategoryDetailsVo.class
        );

        return Response.success(courseCategoryDetails);
    }

}
