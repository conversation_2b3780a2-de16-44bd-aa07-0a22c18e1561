package com.simnectz.soiplus.business.practicalplatform.sandbox.mapper.test2_payment;

import com.mybatisflex.annotation.UseDataSource;
import com.simnectz.soiplus.business.practicalplatform.sandbox.domain.entity.test2_payment.PaymentTransactionEntity;
import com.simnectz.soiplus.business.system.mapper.BasicMapper;
import org.apache.ibatis.annotations.Mapper;

/**
 * PaymentTransaction data access layer interface
 */
@Mapper
@UseDataSource("payment")
public interface PaymentTransactionMapper extends BasicMapper<PaymentTransactionEntity> {
}
