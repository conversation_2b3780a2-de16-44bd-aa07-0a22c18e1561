package com.simnectz.soiplus.business.practicalplatform.lowcodetool.service.impl;

import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.mybatisflex.core.query.If;
import com.mybatisflex.core.query.QueryWrapper;
import com.simnectz.soiplus.business.practicalplatform.lowcodetool.domain.entity.ApiUiClassEntity;
import com.simnectz.soiplus.business.practicalplatform.lowcodetool.domain.entity.ApiUiEntity;
import com.simnectz.soiplus.business.practicalplatform.lowcodetool.mapper.ApiUiClassMapper;
import com.simnectz.soiplus.business.practicalplatform.lowcodetool.mapper.ApiUiMapper;
import com.simnectz.soiplus.business.practicalplatform.lowcodetool.service.LowCodeToolService;
import com.simnectz.soiplus.business.system.domain.vo.Response;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.*;

import static com.simnectz.soiplus.business.practicalplatform.lowcodetool.domain.entity.table.ApiUiClassTableDef.api_ui_class;
import static com.simnectz.soiplus.business.practicalplatform.lowcodetool.domain.entity.table.ApiUiTableDef.api_ui;

@Service
@RequiredArgsConstructor
public class LowCodeToolServiceImpl implements LowCodeToolService {

    private final ApiUiMapper apiUiMapper;
    private final ApiUiClassMapper apiUiClassMapper;

    @Override
    public Response<?> getUiIndustry() {
        List<String> uiIndustry = apiUiMapper.getUiIndustry();
        return Response.success(uiIndustry);
    }

    @Override
    public Response<?> queryUi(String classId, String fiName, String componentName) {
        List<Map<String, Object>> maps = new ArrayList<>();
        String className = "";
        if (StrUtil.isNotBlank(classId)) {
            ApiUiClassEntity apiUiClassEntity = apiUiClassMapper.selectOneById(classId);
            if (ObjUtil.isNotNull(apiUiClassEntity)) {
                className = apiUiClassEntity.getClassName();
            }
        }

        List<ApiUiEntity> apiUiEntities = apiUiMapper.selectListByCondition(api_ui.ui_class_id.eq(classId, If::hasText)
                .and(api_ui.fi_name.eq(fiName, If::hasText))
                .and(api_ui.component_name.eq(componentName, If::hasText)));
        String finalClassName = className;
        apiUiEntities.forEach(apiUi -> {
            Map<String, Object> map = new HashMap<>();
            map.put("UiInfo", apiUi);
            map.put("className", finalClassName);
            maps.add(map);
        });
        return Response.success(maps);
    }

    @Override
    public Response<?> listUiClassByFiName(String fiName) {
        List<ApiUiClassEntity> apiUiClassList = apiUiClassMapper.selectListByCondition(api_ui_class.fi_name.eq(fiName, If::hasText));
        return Response.success(apiUiClassList);
    }

    @Override
    public Response<?> saveUi(ApiUiEntity apiUiDTO) {
        apiUiDTO.setUiId(UUID.randomUUID().toString());
        apiUiMapper.insertOrUpdate(apiUiDTO);
        return Response.success();
    }

    @Override
    public Response<?> deleteUi(String uiId) {
        apiUiMapper.deleteByCondition(api_ui.ui_id.eq(uiId));
        return Response.success();
    }

    @Override
    public Response<?> editUi(ApiUiEntity apiUi) {
        ApiUiEntity ui = apiUiMapper.selectOneByCondition(api_ui.ui_id.eq(apiUi.getUiId()));
        if (ObjUtil.isNotNull(ui)) {
            ui.setComponentName(apiUi.getComponentName());
            ui.setParamType(apiUi.getParamType());
            ui.setParamName(apiUi.getParamName());
            ui.setParamDataLink(apiUi.getParamDataLink());
            ui.setComponentDescription(apiUi.getComponentDescription());
            ui.setUiClassId(apiUi.getUiClassId());
            ui.setCode(apiUi.getCode());
            ui.setIsChecked(apiUi.getIsChecked());
            apiUiMapper.insertOrUpdate(ui);
            return Response.success();
        } else {
            return Response.failed(-99, "failure");
        }
    }

    @Override
    public Response<?> getSimulatorUiList() {
        //查询fiName和className分组数据
        List<Map> mapList = apiUiMapper.selectListByQueryAs(
                QueryWrapper.create()
                        .select(api_ui.ui_class_id, api_ui.fi_name)
                        .from(api_ui)
                        .groupBy(api_ui.fi_name, api_ui.ui_class_id),
                Map.class
        );
        List<Map<String, Object>> maps = new ArrayList<>();
        for (Map map : mapList) {
            Map<String,Object> strMap = new HashMap<>();
            String fiName = (String) map.get("fi_name");
            String uiClassId = (String) map.get("ui_class_id");
            List<ApiUiEntity> list = apiUiMapper.selectListByCondition(api_ui.fi_name.eq(fiName).and(api_ui.ui_class_id.eq(uiClassId)));
            String className = apiUiClassMapper.selectOneById(uiClassId).getClassName();
            strMap.put("fiName",fiName);
            strMap.put("className",className);
            strMap.put("uiList",list);
            maps.add(strMap);
        }
        return Response.success(maps);
    }
}
