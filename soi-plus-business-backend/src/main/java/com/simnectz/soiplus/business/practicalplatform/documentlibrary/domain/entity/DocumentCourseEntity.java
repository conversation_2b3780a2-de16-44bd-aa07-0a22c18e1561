package com.simnectz.soiplus.business.practicalplatform.documentlibrary.domain.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import lombok.*;

import java.io.Serializable;
import java.util.Date;

@Data
@With
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(value = "document_course")
@EqualsAndHashCode()
public class DocumentCourseEntity implements Serializable {

    private static final long serialVersionUID = -5785903866205001453L;

    @Id(keyType = KeyType.Auto)
    private Integer id;

    private String name;

    private String courseCode;

    private String platformCode;

    private String term;

    private String userId;

    private String year;

    private String semester;

    private String professorName;

    private String tutorName;

    private String professorList;

    private String tutorList;

    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date updateTime;

}
