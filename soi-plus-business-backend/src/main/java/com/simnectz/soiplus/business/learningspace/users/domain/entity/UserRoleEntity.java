package com.simnectz.soiplus.business.learningspace.users.domain.entity;

import com.mybatisflex.annotation.Table;
import lombok.*;

import java.io.Serializable;

/**
 * System user role entity
 */
@Data
@With
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(value = "system_user_role")
@EqualsAndHashCode
public class UserRoleEntity implements Serializable {

    private static final long serialVersionUID = -959394886045722557L;

    private String userId;
    private String roleId;

}
