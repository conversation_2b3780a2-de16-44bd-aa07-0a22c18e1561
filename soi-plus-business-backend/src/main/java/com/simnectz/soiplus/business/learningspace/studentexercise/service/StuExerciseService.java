package com.simnectz.soiplus.business.learningspace.studentexercise.service;

import com.mybatisflex.core.service.IService;
import com.simnectz.soiplus.business.learningspace.studentexercise.domain.dto.StuExerciseDto;
import com.simnectz.soiplus.business.learningspace.studentexercise.domain.entity.StuExerciseEntity;
import com.simnectz.soiplus.business.learningspace.studentexercise.domain.vo.StuExerciseVo;
import com.simnectz.soiplus.business.system.domain.vo.Paginate;
import com.simnectz.soiplus.business.system.domain.vo.Response;

import java.util.Date;
import java.util.List;

public interface StuExerciseService extends IService<StuExerciseEntity> {

    /**
     * Paginate enquiry Student exercise
     */
    Response<Paginate<StuExerciseVo>> paginateEnquiryStuExercise(
            String keywords,
            Integer currentPage,
            Integer pageSize,
            String orderBy,
            Boolean isAsc,
            Date startTime,
            Date endTime,
            String courseCategoryId,
            String courseCode,
			String subjectCode

    );

    Response<?> createStuExercise(StuExerciseDto stuExerciseDto);

   Response<List<StuExerciseVo>> enquiryAllStuExercises();

}
