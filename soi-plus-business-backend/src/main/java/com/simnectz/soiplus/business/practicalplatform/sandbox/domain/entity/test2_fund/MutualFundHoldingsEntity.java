package com.simnectz.soiplus.business.practicalplatform.sandbox.domain.entity.test2_fund;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.Table;
import lombok.*;

@Data
@With
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(value = "mutualfundholdings",dataSource = "fund")
@EqualsAndHashCode()
public class MutualFundHoldingsEntity {

    @Id
    @Column(value = "ID")
    private String id;
    @Column(value = "AccountNumber")
    private String accountNumber;
    @Column(value = "CurrencyCode")
    private String currencyCode;
    @Column(value = "FundCode")
    private String fundCode;
    @Column(value = "SharesHolding")
    private String sharesHolding;
    @Column(value = "AvaliableHoldingNo")
    private String avaliableHoldingNo;
    @Column(value = "AveragePrice")
    private String averagePrice;
    @Column(value = "LastUpdateDate")
    private String lastUpdateDate;

}
