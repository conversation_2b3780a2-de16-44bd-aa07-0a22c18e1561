package com.simnectz.soiplus.business.practicalplatform.sandbox.domain.entity.test2_creditcard;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.Table;
import lombok.*;

@Data
@With
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(value = "creditcardpointreference", dataSource = "creditcard")
@EqualsAndHashCode()
public class CreditcardPointReferenceEntity {

    @Id
    @Column(value = "ID")
    private String id;

    @Column(value = "CreditCardNumber")
    private String creditCardNumber;

    @Column(value = "ExpireDate")
    private String expireDate;

    @Column(value = "Points")
    private String points;

}
