package com.simnectz.soiplus.business.practicalplatform.sandbox.domain.entity.test2_insurance;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.Table;
import lombok.*;

@Data
@With
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(value = "insuranceproductdetail",dataSource = "insurance")
public class InsuranceProductDetailEntity {

    @Id
    @Column(value = "ID")
    private String id;
    @Column(value = "ProductCode")
    private String productCode;
    @Column(value = "ProductName")
    private String productName;
    @Column(value = "CoverageType")
    private String coverageType;
    @Column(value = "Destination")
    private String destination;
    @Column(value = "MediRelatedExpenses")
    private String mediRelatedExpenses;
    @Column(value = "MediEvacuaRepatriation")
    private String mediEvacuaRepatriation;
    @Column(value = "MediEvacuaRepatCoverage")
    private String mediEvacuaRepatCoverage;
    @Column(value = "PersonalAccident")
    private String personalAccident;
    @Column(value = "LossCashDocuments")
    private String lossCashDocuments;
    @Column(value = "BaggagePerBelongs")
    private String baggagePerBelongs;
    @Column(value = "DelayedBaggage")
    private String delayedBaggage;
    @Column(value = "TravelDelay")
    private String travelDelay;
    @Column(value = "TripCancellation")
    private String tripCancellation;
    @Column(value = "PersonalLiability")
    private String personalLiability;
    @Column(value = "OtherAssisService")
    private String otherAssisService;
    @Column(value = "WinterSportsCover")
    private String winterSportsCover;
    @Column(value = "CcyCode")
    private String ccycode;
    @Column(value = "CreationTime")
    private String creationTime;
    @Column(value = "LastUpdateTime")
    private String lastUpdateTime;

}
