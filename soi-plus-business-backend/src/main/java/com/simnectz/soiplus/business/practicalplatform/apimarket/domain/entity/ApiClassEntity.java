package com.simnectz.soiplus.business.practicalplatform.apimarket.domain.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.KeyType;
import com.mybatisflex.annotation.Table;
import com.mybatisflex.core.keygen.KeyGenerators;
import lombok.*;

import java.util.Date;

@Data
@With
@Builder
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
@Table(value = "api_class")
public class ApiClassEntity {

	@Id(keyType = KeyType.Generator, value = KeyGenerators.uuid)
	private String id;

	private String className;

	private String classNameEn;

	private String company;

	private String finame;

	private String fiEmail;

	private String imageName;

	private String level;

	private String details;

	private String detailsEn;

	private byte[] image;

	@JsonFormat(pattern="yyyy-MM-dd HH:mm:ss")
	private Long createTime = new Date().getTime();
}
