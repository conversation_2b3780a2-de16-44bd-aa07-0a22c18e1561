package com.simnectz.soiplus.business.system.utils;

import com.alibaba.fastjson.JSON;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 发送请求时拼接的字符串
 */
public class HttpSendTextUtils {

    /**
     * 添加api 需要的json 字符串
     */
    public static final String ADD_API_TEXT = "{\"api_definition\":{\"api_id\":\"\",\"upstream_certificates\":{},\"use_keyless\":false,\"enable_coprocess_auth\":false,\"disable_quota\":false,\"custom_middleware_bundle\":\"\",\"cache_options\":{\"enable_cache\":false,\"enable_upstream_cache_control\":false,\"cache_timeout\":60,\"cache_response_codes\":[],\"cache_all_safe_requests\":false},\"enable_ip_blacklisting\":false,\"tag_headers\":[],\"pinned_public_keys\":{},\"domain\":\"\",\"openid_options\":{\"segregate_by_client\":false,\"providers\":[]},\"active\":true,\"config_data\":{},\"notifications\":{\"oauth_on_keychange_url\":\"\",\"shared_secret\":\"\"},\"auth\":{\"use_certificate\":false,\"auth_header_name\":\"Authorization\",\"use_param\":false,\"param_name\":\"\",\"use_cookie\":false,\"cookie_name\":\"\"},\"check_host_against_uptime_tests\":false,\"blacklisted_ips\":[],\"hmac_allowed_clock_skew\":-1,\"uptime_tests\":{\"check_list\":[],\"config\":{\"service_discovery\":{\"use_discovery_service\":false,\"query_endpoint\":\"\",\"use_nested_query\":false,\"parent_data_path\":\"\",\"data_path\":\"\",\"cache_timeout\":60}}},\"enable_jwt\":false,\"name\":\"test\",\"slug\":\"test\",\"oauth_meta\":{\"auth_login_redirect\":\"\",\"allowed_access_types\":[],\"allowed_authorize_types\":[]},\"CORS\":{\"enable\":false,\"allow_credentials\":false,\"options_passthrough\":true,\"max_age\":24,\"allowed_origins\":[],\"allowed_methods\":[],\"allowed_headers\":[],\"exposed_headers\":[]},\"proxy\":{\"enable_load_balancing\":false,\"listen_path\":\"/test/\",\"strip_listen_path\":true,\"preserve_host_header\":false,\"target_list\":[],\"target_url\":\"http://httpbin.org/test\",\"service_discovery\":{\"endpoint_returns_list\":false,\"cache_timeout\":0,\"parent_data_path\":\"\",\"query_endpoint\":\"\",\"use_discovery_service\":false,\"_sd_show_port_path\":false,\"use_target_list\":false,\"use_nested_query\":false,\"data_path\":\"\",\"port_data_path\":\"\"},\"check_host_against_uptime_tests\":false},\"client_certificates\":[],\"use_basic_auth\":false,\"version_data\":{\"not_versioned\":true,\"versions\":{\"Default\":{\"name\":\"Default\",\"expires\":\"\",\"paths\":{\"ignored\":[],\"white_list\":[],\"black_list\":[]},\"use_extended_paths\":true,\"global_headers\":null,\"global_headers_remove\":null,\"global_size_limit\":0,\"override_target\":\"\",\"extended_paths\":{\"white_list\":[]}}},\"default_version\":\"\"},\"use_standard_auth\":true,\"disable_rate_limit\":false,\"definition\":{\"key\":\"x-api-version\",\"location\":\"header\"},\"use_oauth2\":false,\"allowed_ips\":[],\"org_id\":\"\",\"enable_ip_whitelisting\":false,\"global_rate_limit\":{\"rate\":0,\"per\":0},\"enable_context_vars\":false,\"tags\":[],\"strip_auth_data\":false,\"certificates\":[],\"enable_signature_checking\":false,\"use_openid\":false,\"internal\":false,\"enable_batch_request_support\":false,\"response_processors\":[],\"use_mutual_tls_auth\":false},\"hook_references\":[]}";
    public static final String ADD_API_TEXT_TYK_COMMUNITY = "{\"api_id\":\"\",\"upstream_certificates\":{},\"use_keyless\":false,\"enable_coprocess_auth\":false,\"disable_quota\":false,\"custom_middleware_bundle\":\"\",\"cache_options\":{\"enable_cache\":false,\"enable_upstream_cache_control\":false,\"cache_timeout\":60,\"cache_response_codes\":[],\"cache_all_safe_requests\":false},\"enable_ip_blacklisting\":false,\"tag_headers\":[],\"pinned_public_keys\":{},\"domain\":\"\",\"openid_options\":{\"segregate_by_client\":false,\"providers\":[]},\"active\":true,\"config_data\":{},\"notifications\":{\"oauth_on_keychange_url\":\"\",\"shared_secret\":\"\"},\"auth\":{\"use_certificate\":false,\"auth_header_name\":\"Authorization\",\"use_param\":false,\"param_name\":\"\",\"use_cookie\":false,\"cookie_name\":\"\"},\"check_host_against_uptime_tests\":false,\"blacklisted_ips\":[],\"hmac_allowed_clock_skew\":-1,\"uptime_tests\":{\"check_list\":[],\"config\":{\"service_discovery\":{\"use_discovery_service\":false,\"query_endpoint\":\"\",\"use_nested_query\":false,\"parent_data_path\":\"\",\"data_path\":\"\",\"cache_timeout\":60}}},\"enable_jwt\":false,\"name\":\"test\",\"slug\":\"test\",\"oauth_meta\":{\"auth_login_redirect\":\"\",\"allowed_access_types\":[],\"allowed_authorize_types\":[]},\"CORS\":{\"enable\":false,\"allow_credentials\":false,\"options_passthrough\":true,\"max_age\":24,\"allowed_origins\":[],\"allowed_methods\":[],\"allowed_headers\":[],\"exposed_headers\":[]},\"proxy\":{\"enable_load_balancing\":false,\"listen_path\":\"/test/\",\"strip_listen_path\":true,\"preserve_host_header\":false,\"target_list\":[],\"target_url\":\"http://httpbin.org/test\",\"service_discovery\":{\"endpoint_returns_list\":false,\"cache_timeout\":0,\"parent_data_path\":\"\",\"query_endpoint\":\"\",\"use_discovery_service\":false,\"_sd_show_port_path\":false,\"use_target_list\":false,\"use_nested_query\":false,\"data_path\":\"\",\"port_data_path\":\"\"},\"check_host_against_uptime_tests\":false},\"client_certificates\":[],\"use_basic_auth\":false,\"version_data\":{\"not_versioned\":true,\"versions\":{\"Default\":{\"name\":\"Default\",\"expires\":\"\",\"paths\":{\"ignored\":[],\"white_list\":[],\"black_list\":[]},\"use_extended_paths\":true,\"global_headers\":null,\"global_headers_remove\":null,\"global_size_limit\":0,\"override_target\":\"\",\"extended_paths\":{\"white_list\":[]}}},\"default_version\":\"\"},\"use_standard_auth\":true,\"disable_rate_limit\":false,\"definition\":{\"key\":\"x-api-version\",\"location\":\"header\"},\"use_oauth2\":false,\"allowed_ips\":[],\"org_id\":\"5e46871f34a6e5748c2c4171\",\"enable_ip_whitelisting\":false,\"global_rate_limit\":{\"rate\":0,\"per\":0},\"enable_context_vars\":false,\"tags\":[],\"strip_auth_data\":false,\"certificates\":[],\"enable_signature_checking\":false,\"use_openid\":false,\"internal\":false,\"enable_batch_request_support\":false,\"response_processors\":[],\"use_mutual_tls_auth\":false,\"hook_references\":[]}";

    /***
     *
     */
    private static final Map<String, Object> ADD_API_MAP = JSON.parseObject(ADD_API_TEXT_TYK_COMMUNITY);

    /**
     * 获取申请api的JSON字符串
     *
     * @param apiName    api名称
     * @param targetUrl  源url
     * @param listenPath 监听路径头
     * @param paths      路径
     * @return
     * @Date 2019年7月3日
     * @version 1.0
     */
    @SuppressWarnings("unchecked")
    public static String getSendAddApiJSON(String apiName, String targetUrl, String listenPath, List<Map> paths, String api_id) {
        Map<String, Object> reqMap = new HashMap<>();
        reqMap.putAll(ADD_API_MAP);

//		Map<String,Object>  addApiMap =(Map)reqMap.get("api_definition");
        reqMap.put("name", apiName);
        reqMap.put("api_id", api_id);
        reqMap.put("slug", apiName.replaceAll(" ", "-"));
        Map<String, Object> proxyMap = (Map<String, Object>) reqMap.get("proxy");
        proxyMap.put("target_url", targetUrl);
        proxyMap.put("listen_path", listenPath);
        Map<String, Object> extendedPathsMap = (Map<String, Object>) ((Map<String, Object>) ((Map<String, Object>) ((Map<String, Object>) reqMap.get("version_data")).get("versions")).get("Default")).get("extended_paths");
        //Map<String,Object> whiteListMap = (Map<String, Object>) extendedPathsMap.get("white_list");
        String methodStr = "{ \"action\":\"no_action\", \"code\":200, \"headers\":{} }";
        paths.forEach(item -> {
            if (item.containsKey("method")) {
                String method = item.get("method").toString().toUpperCase();
                item.remove("method");
                item.put("method_actions", JSON.parseObject("{\"" + method + "\":" + methodStr + "}", Map.class));
            }
        });
        extendedPathsMap.remove("white_list");
        extendedPathsMap.put("ignored", paths);
        return JSON.toJSONString(reqMap);
    }

    public static String getSendAddApiJSON2(String apiName, String targetUrl, String listenPath, List<Map> paths, String apiId, String flag) {
//		String addApiStr="{\"api_definition\":{\"api_id\":\"\",\"upstream_certificates\":{},\"use_keyless\":false,\"enable_coprocess_auth\":false,\"disable_quota\":false,\"custom_middleware_bundle\":\"\",\"cache_options\":{\"enable_cache\":false,\"enable_upstream_cache_control\":false,\"cache_timeout\":60,\"cache_response_codes\":[],\"cache_all_safe_requests\":false},\"enable_ip_blacklisting\":false,\"tag_headers\":[],\"pinned_public_keys\":{},\"domain\":\"\",\"openid_options\":{\"segregate_by_client\":false,\"providers\":[]},\"active\":true,\"config_data\":{},\"notifications\":{\"oauth_on_keychange_url\":\"\",\"shared_secret\":\"\"},\"auth\":{\"use_certificate\":false,\"auth_header_name\":\"Authorization\",\"use_param\":false,\"param_name\":\"\",\"use_cookie\":false,\"cookie_name\":\"\"},\"check_host_against_uptime_tests\":false,\"blacklisted_ips\":[],\"hmac_allowed_clock_skew\":-1,\"uptime_tests\":{\"check_list\":[],\"config\":{\"service_discovery\":{\"use_discovery_service\":false,\"query_endpoint\":\"\",\"use_nested_query\":false,\"parent_data_path\":\"\",\"data_path\":\"\",\"cache_timeout\":60}}},\"enable_jwt\":false,\"name\":\"test\",\"slug\":\"test\",\"oauth_meta\":{\"auth_login_redirect\":\"\",\"allowed_access_types\":[],\"allowed_authorize_types\":[]},\"CORS\":{\"enable\":false,\"allow_credentials\":false,\"options_passthrough\":true,\"max_age\":24,\"allowed_origins\":[],\"allowed_methods\":[],\"allowed_headers\":[],\"exposed_headers\":[]},\"proxy\":{\"enable_load_balancing\":false,\"listen_path\":\"/test/\",\"strip_listen_path\":true,\"preserve_host_header\":false,\"target_list\":[],\"target_url\":\"http://httpbin.org/\",\"service_discovery\":{\"endpoint_returns_list\":false,\"cache_timeout\":0,\"parent_data_path\":\"\",\"query_endpoint\":\"\",\"use_discovery_service\":false,\"_sd_show_port_path\":false,\"use_target_list\":false,\"use_nested_query\":false,\"data_path\":\"\",\"port_data_path\":\"\"},\"check_host_against_uptime_tests\":false},\"client_certificates\":[],\"use_basic_auth\":false,\"version_data\":{\"not_versioned\":true,\"versions\":{\"Default\":{\"name\":\"Default\",\"expires\":\"\",\"paths\":{\"ignored\":[],\"white_list\":[],\"black_list\":[]},\"use_extended_paths\":true,\"global_headers\":null,\"global_headers_remove\":null,\"global_size_limit\":0,\"override_target\":\"\",\"extended_paths\":{\"ignored\":[{\"path\":\"/333\",\"method_actions\":{\"POST\":{\"action\":\"no_action\",\"code\":200,\"headers\":{}}}},{\"path\":\"/dd\",\"method_actions\":{\"GET\":{\"action\":\"no_action\",\"code\":200,\"headers\":{}}}}]}}},\"default_version\":\"\"},\"use_standard_auth\":true,\"disable_rate_limit\":false,\"definition\":{\"key\":\"x-api-version\",\"location\":\"header\"},\"use_oauth2\":false,\"allowed_ips\":[],\"org_id\":\"\",\"enable_ip_whitelisting\":false,\"global_rate_limit\":{\"rate\":0,\"per\":0},\"enable_context_vars\":false,\"tags\":[],\"strip_auth_data\":false,\"certificates\":[],\"enable_signature_checking\":false,\"use_openid\":false,\"internal\":false,\"enable_batch_request_support\":false,\"response_processors\":[],\"use_mutual_tls_auth\":false},\"hook_references\":[]}";
        String addApiStr = null;
        if ("1".equals(flag)) {
            addApiStr = "{\"api_id\":\"\",\"upstream_certificates\":{},\"use_keyless\":true,\"enable_coprocess_auth\":false,\"disable_quota\":false,\"custom_middleware_bundle\":\"\",\"cache_options\":{\"enable_cache\":false,\"enable_upstream_cache_control\":false,\"cache_timeout\":60,\"cache_response_codes\":[],\"cache_all_safe_requests\":false},\"enable_ip_blacklisting\":false,\"tag_headers\":[],\"pinned_public_keys\":{},\"domain\":\"\",\"openid_options\":{\"segregate_by_client\":false,\"providers\":[]},\"active\":true,\"config_data\":{},\"notifications\":{\"oauth_on_keychange_url\":\"\",\"shared_secret\":\"\"},\"auth\":{\"use_certificate\":false,\"auth_header_name\":\"Authorization\",\"use_param\":false,\"param_name\":\"\",\"use_cookie\":false,\"cookie_name\":\"\"},\"check_host_against_uptime_tests\":false,\"blacklisted_ips\":[],\"hmac_allowed_clock_skew\":-1,\"uptime_tests\":{\"check_list\":[],\"config\":{\"service_discovery\":{\"use_discovery_service\":false,\"query_endpoint\":\"\",\"use_nested_query\":false,\"parent_data_path\":\"\",\"data_path\":\"\",\"cache_timeout\":60}}},\"enable_jwt\":false,\"name\":\"test\",\"slug\":\"test\",\"oauth_meta\":{\"auth_login_redirect\":\"\",\"allowed_access_types\":[],\"allowed_authorize_types\":[]},\"CORS\":{\"enable\":false,\"allow_credentials\":false,\"options_passthrough\":true,\"max_age\":24,\"allowed_origins\":[],\"allowed_methods\":[],\"allowed_headers\":[],\"exposed_headers\":[]},\"proxy\":{\"enable_load_balancing\":false,\"listen_path\":\"/test/\",\"strip_listen_path\":true,\"preserve_host_header\":false,\"target_list\":[],\"target_url\":\"http://httpbin.org/\",\"service_discovery\":{\"endpoint_returns_list\":false,\"cache_timeout\":0,\"parent_data_path\":\"\",\"query_endpoint\":\"\",\"use_discovery_service\":false,\"_sd_show_port_path\":false,\"use_target_list\":false,\"use_nested_query\":false,\"data_path\":\"\",\"port_data_path\":\"\"},\"check_host_against_uptime_tests\":false},\"client_certificates\":[],\"use_basic_auth\":false,\"version_data\":{\"not_versioned\":true,\"versions\":{\"Default\":{\"name\":\"Default\",\"expires\":\"\",\"paths\":{\"ignored\":[],\"white_list\":[],\"black_list\":[]},\"use_extended_paths\":true,\"global_headers\":null,\"global_headers_remove\":null,\"global_size_limit\":0,\"override_target\":\"\",\"extended_paths\":{\"ignored\":[{\"path\":\"/333\",\"method_actions\":{\"POST\":{\"action\":\"no_action\",\"code\":200,\"headers\":{}}}},{\"path\":\"/dd\",\"method_actions\":{\"GET\":{\"action\":\"no_action\",\"code\":200,\"headers\":{}}}}]}}},\"default_version\":\"\"},\"use_standard_auth\":false,\"disable_rate_limit\":false,\"definition\":{\"key\":\"x-api-version\",\"location\":\"header\"},\"use_oauth2\":false,\"allowed_ips\":[],\"org_id\":\"5e46871f34a6e5748c2c4171\",\"enable_ip_whitelisting\":false,\"global_rate_limit\":{\"rate\":0,\"per\":0},\"enable_context_vars\":false,\"tags\":[],\"strip_auth_data\":false,\"certificates\":[],\"enable_signature_checking\":false,\"use_openid\":false,\"internal\":false,\"enable_batch_request_support\":false,\"response_processors\":[],\"use_mutual_tls_auth\":false,\"hook_references\":[]}";
        } else {
            addApiStr = "{\"api_id\":\"\",\"upstream_certificates\":{},\"use_keyless\":false,\"enable_coprocess_auth\":false,\"disable_quota\":false,\"custom_middleware_bundle\":\"\",\"cache_options\":{\"enable_cache\":false,\"enable_upstream_cache_control\":false,\"cache_timeout\":60,\"cache_response_codes\":[],\"cache_all_safe_requests\":false},\"enable_ip_blacklisting\":false,\"tag_headers\":[],\"pinned_public_keys\":{},\"domain\":\"\",\"openid_options\":{\"segregate_by_client\":false,\"providers\":[]},\"active\":true,\"config_data\":{},\"notifications\":{\"oauth_on_keychange_url\":\"\",\"shared_secret\":\"\"},\"auth\":{\"use_certificate\":false,\"auth_header_name\":\"Authorization\",\"use_param\":false,\"param_name\":\"\",\"use_cookie\":false,\"cookie_name\":\"\"},\"check_host_against_uptime_tests\":false,\"blacklisted_ips\":[],\"hmac_allowed_clock_skew\":-1,\"uptime_tests\":{\"check_list\":[],\"config\":{\"service_discovery\":{\"use_discovery_service\":false,\"query_endpoint\":\"\",\"use_nested_query\":false,\"parent_data_path\":\"\",\"data_path\":\"\",\"cache_timeout\":60}}},\"enable_jwt\":false,\"name\":\"test\",\"slug\":\"test\",\"oauth_meta\":{\"auth_login_redirect\":\"\",\"allowed_access_types\":[],\"allowed_authorize_types\":[]},\"CORS\":{\"enable\":false,\"allow_credentials\":false,\"options_passthrough\":true,\"max_age\":24,\"allowed_origins\":[],\"allowed_methods\":[],\"allowed_headers\":[],\"exposed_headers\":[]},\"proxy\":{\"enable_load_balancing\":false,\"listen_path\":\"/test/\",\"strip_listen_path\":true,\"preserve_host_header\":false,\"target_list\":[],\"target_url\":\"http://httpbin.org/\",\"service_discovery\":{\"endpoint_returns_list\":false,\"cache_timeout\":0,\"parent_data_path\":\"\",\"query_endpoint\":\"\",\"use_discovery_service\":false,\"_sd_show_port_path\":false,\"use_target_list\":false,\"use_nested_query\":false,\"data_path\":\"\",\"port_data_path\":\"\"},\"check_host_against_uptime_tests\":false},\"client_certificates\":[],\"use_basic_auth\":false,\"version_data\":{\"not_versioned\":true,\"versions\":{\"Default\":{\"name\":\"Default\",\"expires\":\"\",\"paths\":{\"ignored\":[],\"white_list\":[],\"black_list\":[]},\"use_extended_paths\":true,\"global_headers\":null,\"global_headers_remove\":null,\"global_size_limit\":0,\"override_target\":\"\",\"extended_paths\":{\"ignored\":[{\"path\":\"/333\",\"method_actions\":{\"POST\":{\"action\":\"no_action\",\"code\":200,\"headers\":{}}}},{\"path\":\"/dd\",\"method_actions\":{\"GET\":{\"action\":\"no_action\",\"code\":200,\"headers\":{}}}}]}}},\"default_version\":\"\"},\"use_standard_auth\":true,\"disable_rate_limit\":false,\"definition\":{\"key\":\"x-api-version\",\"location\":\"header\"},\"use_oauth2\":false,\"allowed_ips\":[],\"org_id\":\"5e46871f34a6e5748c2c4171\",\"enable_ip_whitelisting\":false,\"global_rate_limit\":{\"rate\":0,\"per\":0},\"enable_context_vars\":false,\"tags\":[],\"strip_auth_data\":false,\"certificates\":[],\"enable_signature_checking\":false,\"use_openid\":false,\"internal\":false,\"enable_batch_request_support\":false,\"response_processors\":[],\"use_mutual_tls_auth\":false,\"hook_references\":[]}";
        }
        Map map = JSON.parseObject(addApiStr, Map.class);
        map.put("name", apiName);
        map.put("slug", apiName);
        map.put("api_id", apiId);
        Map proxy = (Map) map.get("proxy");
        proxy.put("listen_path", listenPath);
        proxy.put("target_url", targetUrl);
        map.put("proxy", proxy);
        Map versionData = (Map) map.get("version_data");
        Map version = (Map) versionData.get("versions");
        Map default2 = (Map) version.get("Default");
        Map extendedPahts = (Map) default2.get("extended_paths");
        List ignoreList = (List) extendedPahts.get("ignored");
        ignoreList.clear();
        HashMap<String, Object> stringStringHashMap = new HashMap<>();
        stringStringHashMap.put("action", "no_action");
        stringStringHashMap.put("code", 200);
        stringStringHashMap.put("headers", new HashMap<>());
        for (Map path : paths) {
            String method = path.get("method").toString();
            String path1 = path.get("path").toString();
            HashMap<String, Object> pathMap = new HashMap<>();
            pathMap.put("path", path1);
            HashMap<String, Object> methodMap = new HashMap<>();
            methodMap.put(method, stringStringHashMap);
            pathMap.put("method_actions", methodMap);
            ignoreList.add(pathMap);
        }
        extendedPahts.put("ignored", ignoreList);
        default2.put("extended_paths", extendedPahts);
        version.put("Default", default2);
        versionData.put("versions", version);
        map.put("version_data", versionData);
//		map.put("api_definition",apiDefinition);
        return JSON.toJSONString(map);
    }


    /**
     * 创建tyk的 POLICY
     */
    public static final String CREATE_POLICY_TXT = "{\"last_updated\":\"0\",\"last_check\":0,\"allowance\":0,\"rate\":1000,\"per\":60,\"expires\":0,\"quota_max\":-1,\"quota_renews\":0,\"quota_remaining\":0,\"quota_renewal_rate\":3600,\"org_id\":\"5e46871f34a6e5748c2c4171\",\"access_rights\":{\"\":{\"api_id\":\"\",\"api_name\":\"\",\"versions\":[\"Default\"],\"allowed_urls\":[],\"limit\":null}},\"name\":\"\",\"is_inactive\":false,\"active\":true,\"hmac_enabled\":false,\"tags\":[],\"key_expires_in\":0,\"throttle_interval\":-1,\"throttle_retry_limit\":-1,\"partitions\":{\"quota\":false,\"rate_limit\":false,\"acl\":false}}";


    /**
     * 创建tyk的 Map
     */
    private static final Map<String, Object> CREATE_POLICY_MAP = JSON.parseObject(CREATE_POLICY_TXT);

    private static final List<String> CREATE_POLICY_PARM = JSON.parseArray("['Default']", String.class);

    public static String getSendPolicyJSON(String apiName, String apiId, String policyName, long time) {
        Map<String, Object> policyMap = new HashMap<String, Object>();
        policyMap.putAll(CREATE_POLICY_MAP);
        policyMap.put("last_updated", Long.valueOf(time).toString());
        policyMap.put("quota_renews", Long.valueOf(time).toString());
        Map<String, Object> apiMap = new HashMap<>();
        apiMap.put("api_id", apiId);
        apiMap.put("api_name", apiName);
        apiMap.put("versions", CREATE_POLICY_PARM);
        apiMap.put("allowed_urls", new ArrayList<>());
        apiMap.put("limit", null);
        Map<String, Object> accessRightsMap = new HashMap<>();
        accessRightsMap.put(apiId, apiMap);
        policyMap.put("access_rights", accessRightsMap);
        policyMap.put("name", policyName);
        return JSON.toJSONString(policyMap);
    }

    /**
     * 创建 catalogue使用的字符串
     * 修改参数  #catalogueName#   #policyID#
     */
    public static final String CREATE_CATALOGUE_TEXT = "{\"name\":\"#catalogueName# \",\"short_description\":\"\",\"long_description\":\"\",\"documentation\":\"\",\"show\":true,\"policy_id\":\"#policyID#\",\"version\":\"v2\",\"config\":{\"key_request_fields\":[],\"require_key_approval\":false,\"redirect_on_key_request\":false,\"override\":false}}";

    public static String getSendCatalogueJSON(String catalogueName, String policyID) {
        return CREATE_CATALOGUE_TEXT.replaceFirst("#catalogueName#", catalogueName).replaceFirst("#policyID#", policyID);
    }


}
