package com.simnectz.soiplus.business.learningspace.questionbank.domain.dto;

import com.simnectz.soiplus.business.system.domain.dto.PaginateEnquiry;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class EnquiryQuestionBankDto extends PaginateEnquiry implements Serializable {
    private static final long serialVersionUID = 5016255709488333864L;

    private String keywords;
    private String courseCategoryId;
    private String courseCode;
    private String subjectCode;
}
