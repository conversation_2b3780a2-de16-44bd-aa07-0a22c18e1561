package com.simnectz.soiplus.business.learningspace.coursecategory.domain.vo;

import com.simnectz.soiplus.business.system.domain.entity.BasicEntity;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * Role details response entity
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode(callSuper = true)
public class CourseCategoryVo extends BasicEntity implements Serializable {

    private static final long serialVersionUID = 5050072179049801383L;

    private String courseCategory;
    private String courseCategoryPicture;

}
