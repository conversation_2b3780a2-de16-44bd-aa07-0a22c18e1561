package com.simnectz.soiplus.business.practicalplatform.sandbox.domain.entity.test2_stock;

import com.mybatisflex.annotation.Column;
import com.mybatisflex.annotation.Id;
import com.mybatisflex.annotation.Table;
import lombok.*;

@Data
@With
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(value = "stockplatformlog",dataSource = "stock")
public class StockPlatformLogEntity {

    @Id
    @Column(value = "ID")
    private String id;

    @Column(value = "CountryCode")
    private String countryCode;

    @Column(value = "ClearingCode")
    private String clearingCode;

    @Column(value = "BranchCode")
    private String branchCode;

    @Column(value = "SandBoxId")
    private String sandBoxId;

    @Column(value = "AccountNumber")
    private String accountNumber;

    @Column(value = "TradingOption")
    private String tradingOption;

    @Column(value = "StockNumber")
    private String stockNumber;

    @Column(value = "SharingNo")
    private String sharingNo;

    @Column(value = "StockTrdingAmount")
    private String stockTrdingAmount;

    @Column(value = "StockTrdingCommission")
    private String stockTrdingCommission;

    @Column(value = "CustodyCharges")
    private String custodyCharges;

    @Column(value = "TransactionAmount")
    private String transactionAmount;

    @Column(value = "TransactionDate")
    private String transactionDate;

    @Column(value = "RiskRating")
    private String riskRating;

    @Column(value = "StockPrice")
    private String stockPrice;

    @Column(value = "LotSize")
    private String lotSize;

    @Column(value = "TransactionDesc")
    private String transactionDesc;
}
