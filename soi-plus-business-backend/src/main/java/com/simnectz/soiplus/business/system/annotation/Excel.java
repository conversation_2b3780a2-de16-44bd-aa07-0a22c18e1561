package com.simnectz.soiplus.business.system.annotation;

import org.apache.poi.ss.usermodel.BorderStyle;
import org.apache.poi.ss.usermodel.IndexedColors;

import java.lang.annotation.*;

@Documented
@Target({ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
public @interface Excel {

    String fileName();

    String sheetName() default "Sheet";

    int sheetSize() default 65536;

    String title() default "";

    short titleHeight() default 30;

    short headerHeight() default 24;

    double cellMaxHeight() default 14;

    String fontName() default "Arial";

    BorderStyle borderStyle() default BorderStyle.THIN;

    IndexedColors borderColor() default IndexedColors.GREY_50_PERCENT;

}
