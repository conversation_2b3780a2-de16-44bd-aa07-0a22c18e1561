package com.simnectz.soiplus.business.practicalplatform.apimarket.domain.model;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.Data;

@Data
@JsonSerialize(include = JsonSerialize.Inclusion.NON_NULL)
public class ConsumerKeysInfo {

    private String apiId;
    private String apiKey;
    private String apiName;
    private String apiProvide;
    private Integer madeCall;
    private Integer maxCallQuota;

    private Integer errorCall;

    private Integer successCall;

    private String catalogueId;
    private String className;

    private String policyId;


}
