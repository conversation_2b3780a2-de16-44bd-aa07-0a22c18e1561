package com.simnectz.soiplus.business.learningspace.subjectdefinition.service;

import com.mybatisflex.core.service.IService;
import com.simnectz.soiplus.business.learningspace.subjectdefinition.domain.dto.CreateSubjectDefinitionDto;
import com.simnectz.soiplus.business.learningspace.subjectdefinition.domain.dto.UpdateSubjectDefinitionDto;
import com.simnectz.soiplus.business.learningspace.subjectdefinition.domain.entity.SubjectDefinitionEntity;
import com.simnectz.soiplus.business.learningspace.subjectdefinition.domain.vo.SubjectDefinitionVo;
import com.simnectz.soiplus.business.system.domain.vo.Paginate;
import com.simnectz.soiplus.business.system.domain.vo.Response;

import java.util.Date;
import java.util.List;
import java.util.Set;

public interface SubjectDefinitionService extends IService<SubjectDefinitionEntity> {

    /**
     * Paginate enquiry Course
     */
    Response<Paginate<SubjectDefinitionVo>> paginateEnquirySubjectDefinition(
            String keywords,
            Integer currentPage,
            Integer pageSize,
            String orderBy,
            Boolean isAsc,
            Date startTime,
            Date endTime,
            String courseId
    );

    Response<?> createSubjectDefinition(CreateSubjectDefinitionDto createSubjectDefinitionDto);

    Response<?> updateSubjectDefinition(UpdateSubjectDefinitionDto updateSubjectDefinitionDto);

    Response<?> batchDeleteSubjectDefinition(Set<String> subjectDefinitionIds);

    Response<List<SubjectDefinitionVo>> enquiryAllSubjects();

    Response<SubjectDefinitionVo> findSubjectBySubjectCode(String subjectCode);

    Response<List<SubjectDefinitionVo>> findSubjectByCourseId(String courseId);

    Response<?> enquirySubjectByCourseIds(List<String> courseIds);
}
