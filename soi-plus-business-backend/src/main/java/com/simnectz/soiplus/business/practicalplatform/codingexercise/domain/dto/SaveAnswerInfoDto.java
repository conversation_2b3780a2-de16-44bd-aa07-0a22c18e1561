package com.simnectz.soiplus.business.practicalplatform.codingexercise.domain.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
public class SaveAnswerInfoDto implements Serializable {

    private static final long serialVersionUID = -4548968380320837652L;

    /**
     * ID
     */
    private String id;
    /**
     * User id(email)
     */
    private String userId;
    /**
     * 题类型加题ID
     */
    private String questionId;
    /**
     * 编程语言
     */
    private String language;
    /**
     * 答案代码
     */
    private String code;

}
