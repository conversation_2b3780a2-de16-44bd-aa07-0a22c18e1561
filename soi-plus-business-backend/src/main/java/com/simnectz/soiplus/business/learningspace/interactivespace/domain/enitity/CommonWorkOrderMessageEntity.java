package com.simnectz.soiplus.business.learningspace.interactivespace.domain.enitity;


import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.mybatisflex.annotation.Table;
import com.simnectz.soiplus.business.system.domain.entity.BasicEntity;
import lombok.*;

import java.io.Serializable;
import java.util.Date;

@Data
@With
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(value = "learn_common_work_order_message")
@EqualsAndHashCode(callSuper = true)
public class CommonWorkOrderMessageEntity extends BasicEntity implements Serializable {

    private static final long serialVersionUID = -4616963429567438866L;

    private String orderId;

    private String content;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss",timezone="GMT+8")
    private Date sendTime;

    private Integer deleteState;

    @JsonIgnore
    private byte[] fileURL;

    private String fileExtensionName;

    private String senderEmail;

    private String isOperator;

}
