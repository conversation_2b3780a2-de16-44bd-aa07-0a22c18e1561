package com.simnectz.soiplus.business.system.filter;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.simnectz.authorize.system.domain.bo.User;
import com.simnectz.soiplus.business.system.config.SystemProperties;
import com.simnectz.soiplus.business.system.constant.SystemConstant;
import com.simnectz.soiplus.business.system.domain.bo.TokenPayload;
import com.simnectz.soiplus.business.system.utils.JwtUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Optional;
import java.util.concurrent.TimeUnit;

/**
 * Jwt authentication filter
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class JwtAuthenticationTokenFilter extends OncePerRequestFilter {

    private final SystemProperties systemProperties;
    private final RedisTemplate<String, User> redisTemplate;

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain) throws ServletException, IOException {
        String authenticationHeader = request.getHeader(systemProperties.getAuthorize().getHeader());
        if (verifyAuthenticationHeader(authenticationHeader)) {
            String accessToken = authenticationHeader.replace(systemProperties.getAuthorize().getPrefix(), SystemConstant.EMPTY_STRING);
            Optional<TokenPayload> tokenPayloadOptional = JwtUtils.parseAccessToken(accessToken, Boolean.FALSE);
            if (tokenPayloadOptional.isPresent()) {
                this.setupAuthentication(tokenPayloadOptional.get().getUserId());
            } else {
                SecurityContextHolder.clearContext();
            }
        }
        filterChain.doFilter(request, response);
    }

    private void setupAuthentication(String userId) {
        User loginUser = redisTemplate.opsForValue().get(SystemConstant.REDIS_PREFIX_LOGIN + userId);
        if (ObjectUtil.isNull(loginUser)) {
            SecurityContextHolder.clearContext();
        } else {
            UsernamePasswordAuthenticationToken authentication = new UsernamePasswordAuthenticationToken(loginUser, null, loginUser.getAuthorities());
            SecurityContextHolder.getContext().setAuthentication(authentication);

            redisTemplate
                    .expire(
                            // key: user id
                            SystemConstant.REDIS_PREFIX_LOGIN + userId,
                            // expire time: refresh token expire time
                            systemProperties.getAuthorize().getAccessTokenExpireTime(),
                            // unit: minutes
                            TimeUnit.MINUTES
                    );
        }
    }

    private Boolean verifyAuthenticationHeader(String authenticationHeader) {
        return StrUtil.isNotBlank(authenticationHeader) &&
                authenticationHeader.startsWith(systemProperties.getAuthorize().getPrefix());
    }

}
