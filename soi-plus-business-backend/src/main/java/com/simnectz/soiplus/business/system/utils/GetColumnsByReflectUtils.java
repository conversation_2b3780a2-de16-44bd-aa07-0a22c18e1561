package com.simnectz.soiplus.business.system.utils;

import org.springframework.core.io.ClassPathResource;

import java.io.IOException;
import java.io.InputStream;
import java.io.Serializable;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.*;

public class GetColumnsByReflectUtils implements Serializable {

    private static final long serialVersionUID = -2862585049955236662L;

    private static final List<String> sqlList = new ArrayList<>(Arrays.asList("creditcardmaster.sql","creditcardpointreference.sql",
            "creditcardstatement.sql","creditcardtransactiondetail.sql","currentaccountmaster.sql",
            "customermaster.sql","fexaccountdetail.sql","fexaccountmaster.sql",
            "insurancequotedetail.sql","insurancequotemaster.sql","loanaccountmaster.sql",
            "mortgageloancontractdetail.sql","mortgageloancontractmaster.sql","mortgageloantransactionlog.sql",
            "mutualfundaccountmaster.sql","mutualfundholdings.sql","mutualfundplatformlog.sql",
            "paymenttransaction.sql","stockplatformlog.sql","stocktradingaccountmaster.sql",
            "termdepositdetail.sql","savingaccountmaster.sql","stockholdings.sql",
            "termdepositmaster.sql","transactionlog.sql",
            "foreign_exchange.sql","insuranceproductdetail.sql"));

    public static List<String> getColumns(Object entity) {
        Class<?> clazz = entity.getClass();
        List<String> columns = new ArrayList<>();
        // 取得本类的全部属性
        Field[] field = clazz.getDeclaredFields();
        for (int i = 0; i < field.length; i++) {
            // 权限修饰符
            columns.add(field[i].getName());
//            System.out.println(field[i].getName() + getFieldValue(entity,field[i].getName()));
        }
        return columns;
    }

    public static List<Map<String,String>> getColumnsValue(Object object) {
        List<Map<String,String>> lineData = new ArrayList<>();
        List list = (List) object;
        for (int i = 0; i < list.size(); i++) {
            Object entity = list.get(i);
            Class<?> clazz = entity.getClass();
            // 取得本类的全部属性
            Field[] field = clazz.getDeclaredFields();
            Map<String,String> line = new HashMap<>();
            for (int j = 0; j < field.length; j++) {
                line.put(field[j].getName(),getFieldValue(entity,field[j].getName()));
            }
            lineData.add(line);
        }
        return lineData;
    }

    private static String getFieldValue(Object owner, String fieldName)
    {
        Object o = invokeMethod(owner, fieldName, null);
        if (o != null) {
            return o.toString();
        }
        return null;
    }


    /**
     *
     * 执行某个Field的getField方法
     *
     * @param owner 类
     * @param fieldName 类的属性名称
     * @param args 参数，默认为null
     * @return
     */
    private static Object invokeMethod(Object owner, String fieldName, Object[] args)
    {
        Class<? extends Object> ownerClass = owner.getClass();

        //fieldName -> FieldName
        String methodName = fieldName.substring(0, 1).toUpperCase()+ fieldName.substring(1);

        Method method = null;
        try
        {
            method = ownerClass.getMethod("get" + methodName);
        }
        catch (SecurityException e)
        {
            //e.printStackTrace();
        }
        catch (NoSuchMethodException e)
        {
            // e.printStackTrace();
            return "";
        }

        //invoke getMethod
        try
        {
            return method.invoke(owner);
        }
        catch (Exception e)
        {
            return "";
        }
    }

    public static List<InputStream> getFilesPath(String path)  {
        List<InputStream> inputStreamList = new ArrayList<>();
        for (int i = 0; i < sqlList.size(); i++) {
            String sqlpath = sqlList.get(i);
            ClassPathResource resource = new ClassPathResource(path + sqlpath);
            InputStream inputStream = null;
            try {
                inputStream = resource.getInputStream();
                inputStreamList.add(inputStream);
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return inputStreamList;
    }
}
