package com.simnectz.soiplus.business.system.config.security;

import cn.hutool.extra.servlet.ServletUtil;
import cn.hutool.http.ContentType;
import cn.hutool.json.JSONConfig;
import cn.hutool.json.JSONUtil;
import com.simnectz.soiplus.business.system.constant.ResponseConstant;
import com.simnectz.soiplus.business.system.domain.vo.Response;
import com.simnectz.soiplus.business.system.utils.MessageUtils;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.AuthenticationEntryPoint;
import org.springframework.stereotype.Component;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.nio.charset.StandardCharsets;

/**
 * Authentication entry point
 */
@Component
public class RestAuthenticationEntryPoint implements AuthenticationEntryPoint {

    @Override
    public void commence(HttpServletRequest request, HttpServletResponse response, AuthenticationException exception) {
        String result = JSONUtil.toJsonStr(
                Response.failed(
                        ResponseConstant.Code.UNAUTHORIZED,
                        MessageUtils.message(ResponseConstant.Message.UNAUTHORIZED)
                ),
                JSONConfig.create().setIgnoreNullValue(false)
        );
        ServletUtil.write(response, result, ContentType.build(ContentType.JSON, StandardCharsets.UTF_8));
    }

}
