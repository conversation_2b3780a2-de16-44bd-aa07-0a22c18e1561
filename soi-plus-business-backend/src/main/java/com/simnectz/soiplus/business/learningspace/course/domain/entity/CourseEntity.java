package com.simnectz.soiplus.business.learningspace.course.domain.entity;

import com.mybatisflex.annotation.Table;
import com.simnectz.soiplus.business.system.domain.entity.BasicEntity;
import lombok.*;

import java.io.Serializable;

@Data
@With
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(value = "learn_course")
@EqualsAndHashCode(callSuper = true)

public class CourseEntity extends BasicEntity implements Serializable {

    private static final long serialVersionUID = 2426881425867600116L;

    private String courseCategoryId;

    private String courseName;

    private String courseCode;

    private String courseDescription;

    private String coursePicture;

}
