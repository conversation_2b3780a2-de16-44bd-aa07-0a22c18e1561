package com.simnectz.soiplus.business.system.utils;

import com.google.common.collect.Lists;
import com.simnectz.soiplus.business.practicalplatform.apimarket.domain.model.MakeSwagView;
import com.simnectz.soiplus.business.practicalplatform.apimarket.domain.model.ReqParam;
import io.swagger.models.*;
import io.swagger.models.parameters.Parameter;
import io.swagger.models.parameters.QueryParameter;

import java.net.MalformedURLException;
import java.net.URL;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class SwaggerBuilderUtils {

    static public final String SWAGGER_VERSION = "2.0";
    static public final String REQUEST_QUERY = "query";
    static public final String API_VERSION = "1.0";
    static public boolean setTags = false;
    static public boolean setContactInfo = false;

    static public Swagger makeSwagger(MakeSwagView msw) throws MalformedURLException {

        Swagger swagger = new Swagger();
        URL url = new URL(msw.getUrl());
        setBaseInfo(swagger, url, msw);
        setParameters(swagger, msw.getParams(), url);
        return swagger;
    }


    static public void setBaseInfo(Swagger swagger, URL url, MakeSwagView msw ){

        //set up base information
        swagger.setSwagger(SWAGGER_VERSION);
        swagger.setSchemes(Lists.newArrayList(Scheme.HTTPS));


        Info info = new Info();
        if(setContactInfo) {
            Contact contact = new Contact();
            contact.setEmail("contact email");
            contact.setName("contact's name");
            contact.setUrl("contact url");
            info.setContact(contact);
        }

        info.setDescription("Data Retrieval API");
        info.setVersion(API_VERSION);
        info.setTitle(msw.getTitle());
        swagger.setInfo(info);

        //set tags
//        if (setTags) {
        List<Tag> tags = new ArrayList<>();
        Tag tag = new Tag();
        tag.setName(msw.getTitle());
        tag.setDescription(msw.getTitle());
        tags.add(tag);
        swagger.setTags(tags);
//        }
        swagger.setHost(url.getAuthority());
        swagger.setBasePath("/");

        //PATH
        Path path = new Path();
        Operation op = new Operation();
        op.setTags(Lists.newArrayList(msw.getTitle()));
        op.setSummary(msw.getSummary());
        op.addProduces("application/json");
        Map<String, Path> pathMap = new HashMap<>();
        pathMap.put(url.getPath(), path);

        //set response
        Response response = new Response();
        response.setDescription("A JSON array of database row data");
        op.addResponse("200", response );

        //only support "GET" requests
        path.setGet(op);

        swagger.setPaths(pathMap);
    }


    static public void setParameters(Swagger swagger, List<ReqParam> params, URL url) {

        List<Parameter> swaggerParameters = new ArrayList();
        for( ReqParam param : params) {
            QueryParameter qp = new QueryParameter();
            qp.setName(param.getName());
            qp.setDescription(param.getDescription());
            qp.setRequired(Boolean.valueOf(param.getRequired()));
            qp.setDefault(param.getExample());
            qp.setType(param.getType());
            qp.setIn(REQUEST_QUERY);

            swaggerParameters.add(qp);
        }
        swagger.getPath(url.getPath()).getGet().setParameters(swaggerParameters);

    }



}
