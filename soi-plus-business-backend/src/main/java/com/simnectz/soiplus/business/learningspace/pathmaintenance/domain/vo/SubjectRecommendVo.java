package com.simnectz.soiplus.business.learningspace.pathmaintenance.domain.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * Role details response entity
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class SubjectRecommendVo implements Serializable {

    private static final long serialVersionUID = 5050072179049801383L;

    private String id;
    private String courseName;
    private String courseCode;
    private String subjectName;
    private String subjectCode;
    private String subjectCategory;
    private String subjectObjective;
    private String subjectDescription;
    private String subjectPictureUrl;
    private BigDecimal subjectHours;
    private String subjectLevel;
    private String materialsUrl;
    private String videoUrl;
    private String assignmentUrl;
    private String createTime;
    private String lastUpdateTime;
    private String creator;
    private String lastUpdateCreator;
}
