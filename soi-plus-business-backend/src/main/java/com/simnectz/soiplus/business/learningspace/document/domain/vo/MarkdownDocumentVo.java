package com.simnectz.soiplus.business.learningspace.document.domain.vo;

import com.mybatisflex.annotation.RelationOneToMany;
import com.simnectz.soiplus.business.system.domain.bo.Tree;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
@NoArgsConstructor
@AllArgsConstructor
@EqualsAndHashCode
public class MarkdownDocumentVo implements Tree<MarkdownDocumentVo>, Serializable {

    private static final long serialVersionUID = -3967595173267516129L;

    private String id;

    private String parentId;

    private String name;

    private String image;

    private String type;

    private String content;

    private Integer status;

    private String createTime;

    private Integer sortNumber;

    @RelationOneToMany(selfField = "id", targetField = "parentId", targetTable = "learn_document", orderBy = "sort_number ASC")
    private List<MarkdownDocumentVo> children = new ArrayList<>();

}
